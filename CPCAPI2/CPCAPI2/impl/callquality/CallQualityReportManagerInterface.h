#pragma once

#if !defined(CPCAPI2_CALL_QUALITY_REPORT_MANAGER_INTERFACE_H)
#define CPCAPI2_CALL_QUALITY_REPORT_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "callquality/CallQualityReportManager.h"
#include "callquality/CallQualityReportHandler.h"
#include "phone/Cpcapi2EventSource.h"
#include "phone/PhoneModule.h"

namespace CPCAPI2
{
class PhoneInterface;
namespace CallQuality
{
class CallQualityReporterImpl;
class CallQualityReporterSyncHandler {};
class CallQualityReportManagerInterface : public CPCAPI2::EventSource<CallQualityReporterHandle, CallQualityReportHandler, CallQualityReporterSyncHandler>,
                                     public PhoneModule,
                                     public CallQualityReportManager
{
public:
   CallQualityReportManagerInterface(Phone* phone);
   virtual ~CallQualityReportManagerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // CallQualityReportManager
   virtual int process(unsigned int timeout) OVERRIDE {
      return CPCAPI2::EventSource<CallQualityReporterHandle, CallQualityReportHandler, CallQualityReporterSyncHandler>::process(timeout);
   }
   virtual void interruptProcess() OVERRIDE {
      CPCAPI2::EventSource<CallQualityReporterHandle, CallQualityReportHandler, CallQualityReporterSyncHandler>::interruptProcess();
   }
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE {
      CPCAPI2::EventSource<CallQualityReporterHandle, CallQualityReportHandler, CallQualityReporterSyncHandler>::setCallbackHook(cbHook, context);
   }

   virtual CallQualityReporterHandle createCallQualityReporter() OVERRIDE;
   virtual int setHandler(CallQualityReporterHandle cqr, CallQualityReportHandler* handler) OVERRIDE;
   virtual int configureCallQualityReporter(CallQualityReporterHandle cqr, const CallQualityReporterConfig& config) OVERRIDE;
   virtual int startCallQualityReporter(CallQualityReporterHandle cqr) OVERRIDE;
   virtual int stopCallQualityReporter(CallQualityReporterHandle cqr) OVERRIDE;

   PhoneInterface* getPhone() const {
      return mPhone;
   }

   template<typename TFn, typename TEvt> void fireCallQualityEvent(const char* funcName, TFn func, CallQualityReporterHandle h, const TEvt& args)
   {
      fireEvent(funcName, func, h, args);
   }

   // EventSource
   void logEvent(CallQualityReporterHandle handle, const char* funcName, const char* eventName) OVERRIDE;

private:
   void createCallQualityReporterImpl(CallQualityReporterHandle cqr);
   void configureCallQualityReporterImpl(CallQualityReporterHandle cqr, const CallQualityReporterConfig& config);
   void startCallQualityReporterImpl(CallQualityReporterHandle cqr);
   void stopCallQualityReporterImpl(CallQualityReporterHandle cqr);

private:
   PhoneInterface* mPhone;
   typedef std::map<CallQualityReporterHandle, std::shared_ptr<CallQualityReporterImpl> > InstanceMap;
   InstanceMap mInstMap;
};

class CallQualityReporterHandleFactory
{
public:
   static CallQualityReporterHandle getNext() { return sNextHandle++; }
private:
   static CallQualityReporterHandle sNextHandle;
};

}
}

#endif // CPCAPI2_CALL_QUALITY_REPORT_MANAGER_INTERFACE_H

