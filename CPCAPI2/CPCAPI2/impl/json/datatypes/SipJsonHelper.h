// Not a generated files
// TODO: Use JsonDataTypes to generate this file as well
#pragma once

#ifndef SIP_JSON_HELPER_H
#define SIP_JSON_HELPER_H

#include "cpcapi2types.h"

#include "../SerializationHelperMacros.h"

#include "account/SipAccountSettings.h"
#include "call/SipConversationTypes.h"

namespace CPCAPI2
{
namespace Json
{

  template<class Writer>
  inline void Serialize(Writer& w, const CPCAPI2::NameAddress& v)
  {
    w.StartObject();
    Write(w, "displayName", v.displayName);
    Write(w, "address", v.address);
    w.EndObject();
  }
  inline void Deserialize(const rapidjson::Value& json, CPCAPI2::NameAddress& v)
  {
    Read(json, "displayName", v.displayName);
    Read(json, "address", v.address);
  }

  template<class Writer>
  inline void Serialize(Writer& w, const CPCAPI2::MimeType& v)
  {
    w.StartObject();
    Write(w, "mimeType", v.mimeType);
    Write(w, "mimeSubType", v.mimeSubType);
    w.EndObject();
  }
  inline void Deserialize(const rapidjson::Value& json, CPCAPI2::MimeType& v)
  {
    Read(json, "mimeType", v.mimeType);
    Read(json, "mimeSubType", v.mimeSubType);
  }

   template<class Writer>
   inline void Serialize(Writer& w, const CPCAPI2::Parameter& v)
   {
     w.StartObject();
     Write(w, "name", v.name);
     Write(w, "value", v.value);
     w.EndObject();
   }
   inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Parameter& v)
   {
     Read(json, "name", v.name);
     Read(json, "value", v.value);
   }

   template<class Writer>
   inline void Serialize(Writer& w, const CPCAPI2::SipHeader& v)
   {
     w.StartObject();
     Write(w, "header", v.header);
     Write(w, "value", v.value);
     w.EndObject();
   }
   inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipHeader& v)
   {
     Read(json, "header", v.header);
     Read(json, "value", v.value);
   }

   template<class Writer>
   inline void Serialize(Writer& w, const CPCAPI2::DialogId& v)
   {
     w.StartObject();
     Write(w, "callId", v.callId);
     Write(w, "localTag", v.localTag);
     Write(w, "remoteTag", v.remoteTag);
     w.EndObject();
   }
   inline void Deserialize(const rapidjson::Value& json, CPCAPI2::DialogId& v)
   {
     Read(json, "callId", v.callId);
     Read(json, "localTag", v.localTag);
     Read(json, "remoteTag", v.remoteTag);
   }


   // These should be autogenerated into other files, but their namespaces are incorrect
   template<class Writer>
   inline void Serialize(Writer& w, const CPCAPI2::SipParameterType& v)
   {
     w.StartObject();
     Write(w, "name", v.name);
     Write(w, "value", v.value);
     w.EndObject();
   }
   inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipParameterType& v)
   {
     Read(json, "name", v.name);
     Read(json, "value", v.value);
   }

   template<class Writer>
   inline void Serialize(Writer& w, const CPCAPI2::SipResponseType& v)
   {
     w.StartObject();
     Write(w, "method", v.method);
     Write(w, "responseCode", v.responseCode);
     w.EndObject();
   }
   inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipResponseType& v)
   {
     Read(json, "method", v.method);
     Read(json, "responseCode", v.responseCode);
   }

   template<class Writer>
   inline void Serialize(Writer& w, const CPCAPI2::HistoryInfo& v)
   {
      w.StartObject();
      Write(w, "remoteAddress", v.remoteAddress);
      Write(w, "remoteDisplayName", v.remoteDisplayName);
      Write(w, "reason", v.reason);
      w.EndObject();
   }
   inline void Deserialize(const rapidjson::Value& json, CPCAPI2::HistoryInfo& v)
   {
      Read(json, "remoteAddress", v.remoteAddress);
      Read(json, "remoteDisplayName", v.remoteDisplayName);
      Read(json, "reason", v.reason);
   }
} // namespace Json
} // namespace CPCAPI2

#endif // CPCAPI2_SIP_CONVERSATION_TYPES_H
