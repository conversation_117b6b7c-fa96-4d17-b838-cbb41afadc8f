#include "brand_branded.h"

#include "interface/public/call/SipConversationManager.h"
#include "interface/public/call/SipConversationState.h"

#if (CPCAPI2_BRAND_CALL_MODULE == 1)
#include "SipAVConversationManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#if (CPCAPI2_BRAND_CALL_STATE_MODULE == 1)
#include "SipConversationStateImpl.h"
#endif
#endif

namespace CPCAPI2
{
namespace SipConversation
{

SipConversationManager* SipConversationManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_CALL_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<SipAVConversationManagerInterface>(phone, "SipAVConversationManagerInterface");
#else
   return NULL;
#endif
}

SipConversationStateManager* SipConversationStateManager::getInterface(SipConversationManager* cpcConvMan)
{
#if (CPCAPI2_BRAND_CALL_MODULE == 1) && (CPCAPI2_BRAND_CALL_STATE_MODULE == 1)
   SipAVConversationManagerInterface* parent = dynamic_cast<SipAVConversationManagerInterface*>(cpcConvMan);
   if (parent == NULL) return NULL;
   PhoneInterface* phone = parent->phoneInterface();
   return _GetInterfaceEx<SipConversationStateImpl>(phone, "SipConversationStateManager", parent);
#else
   return NULL;
#endif
}

}
}
