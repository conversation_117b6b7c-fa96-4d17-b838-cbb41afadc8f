#include "INetworkChangeManager.h"
#include "impl/jsonrpc/IJsonRpcServerModuleProcessing.h"

#ifndef JSONRPC_CPCAPI2_NETWORKCHANGEMANAGER_NETWORKCHANGEMANAGERSERVER_H
#define JSONRPC_CPCAPI2_NETWORKCHANGEMANAGER_NETWORKCHANGEMANAGERSERVER_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace NetworkChangeManager {
class NetworkChangeManagerServer
{
public:
    NetworkChangeManagerServer(::CPCAPI2::IJsonRpcServerModuleProcessing& transport, INetworkChangeManager& impl) :
        mTransport(transport),
        mImpl(impl)
    {
        mTransport.registerFunction("NetworkChangeManager::networkTransport", jsonrpccxx::methodHandle(&INetworkChangeManager::networkTransport, mImpl), {{ "handle" }});
    }

private:
    ::CPCAPI2::IJsonRpcServerModuleProcessing& mTransport;
    INetworkChangeManager& mImpl;
}; // class NetworkChangeManagerServer
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace NetworkChangeManager
#endif // JSONRPC_CPCAPI2_NETWORKCHANGEMANAGER_NETWORKCHANGEMANAGERSERVER_H
