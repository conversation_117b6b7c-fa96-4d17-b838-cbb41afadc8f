#include <nlohmann/json.hpp>
#include "MediaType.h"

#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSETMEDIAENABLED_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSETMEDIAENABLED_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationSetMediaEnabled
  {
  public:
    int32_t phoneHandle;
    int32_t conversationHandle;
    MediaType mediaType;
    bool enabled;

    SipConversationSetMediaEnabled() = default;

    SipConversationSetMediaEnabled(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationSetMediaEnabled& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["conversationHandle"] = o.conversationHandle;
     j["mediaType"] = o.mediaType;
     j["enabled"] = o.enabled;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationSetMediaEnabled& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.conversationHandle = j.at("conversationHandle");
     o.mediaType = j.at("mediaType");
     o.enabled = j.at("enabled");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSETMEDIAENABLED_H
