#include <libxml/xmlwriter.h>

#include <sstream>

#include "LicenseAuxiliaryDocCreater.h"
#include "../../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::LICENSING

#define XMLCHAR_CAST(x) reinterpret_cast<const xmlChar*>(x)

namespace CPCAPI2
{
namespace Licensing
{
   LicenseAuxiliaryDocCreater::LicenseAuxiliaryDocCreater(time_t accumulatedRunTime, time_t checkTime)
   {
      m_Xml = Create(accumulatedRunTime, checkTime);
   }

   LicenseAuxiliaryDocCreater::LicenseAuxiliaryDocCreater(std::string accumulatedRunTime, std::string checkTime)
   {
      m_Xml = Create(atol(accumulatedRunTime.c_str()), atol(checkTime.c_str()));
   }

   std::string 
   LicenseAuxiliaryDocCreater::getXml() const
   {
      return m_Xml;
   }

   static std::string 
   timeToString(unsigned long in)
   {
      char temp[32];
      snprintf(temp, 32, "%d", (int)in);
      std::string result(temp);

      return result;
   }

   std::string
   LicenseAuxiliaryDocCreater::Create(time_t accumulatedRunTime, time_t checkTime)
   {
      xmlBufferPtr buffer = xmlBufferCreate();

      try
      {
         xmlTextWriterPtr writer = xmlNewTextWriterMemory(buffer, 0);
         if (!writer)
         {
            ErrLog(<< "CLicensing::CreateArtAndCheckTimeXML() - XML buffer alloction fails!");
            assert(false);
            return std::string();
         }

         std::string artText = timeToString(static_cast<unsigned long>(accumulatedRunTime));
         std::string checkTimeText = timeToString(static_cast<unsigned long>(checkTime));

         xmlTextWriterStartDocument  (writer, 0, "utf8", 0);
         
            xmlTextWriterStartElement   (writer, XMLCHAR_CAST("timeInfo"));

               xmlTextWriterWriteElement (writer, XMLCHAR_CAST("art"),           XMLCHAR_CAST(artText.c_str()));
               xmlTextWriterWriteElement (writer, XMLCHAR_CAST("checkTime"),     XMLCHAR_CAST(checkTimeText.c_str()));
            
            xmlTextWriterEndElement     (writer);

         xmlTextWriterEndDocument    (writer);

         xmlFreeTextWriter(writer);
      }
      catch (const std::exception&)
      {
         xmlBufferFree(buffer);

         ErrLog(<< "LicenseAuxiliaryDocCreater::Create() - XML writer fails!");
         assert(false);
         return std::string();
      }

      std::string contents(reinterpret_cast<const char*>(buffer->content));
      xmlBufferFree(buffer);

      return contents;
   }


}
}
