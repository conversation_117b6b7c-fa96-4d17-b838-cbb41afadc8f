#include <nlohmann/json.hpp>
#include "NetworkTransport.h"

#ifndef JSONRPC_CPCAPI2_NETWORKCHANGEMANAGER_NETWORKCHANGEMANAGERNETWORKCHANGEEVENT_H
#define JSONRPC_CPCAPI2_NETWORKCHANGEMANAGER_NETWORKCHANGEMANAGERNETWORKCHANGEEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace NetworkChangeManager {
  class NetworkChangeManagerNetworkChangeEvent
  {
  public:
    int32_t handle;
    NetworkTransport networkTransport;

    NetworkChangeManagerNetworkChangeEvent() = default;

    NetworkChangeManagerNetworkChangeEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const NetworkChangeManagerNetworkChangeEvent& o)
    {
      j = nlohmann::json::object();
       j["handle"] = o.handle;
     j["networkTransport"] = o.networkTransport;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, NetworkChangeManagerNetworkChangeEvent& o)
    {
     o.handle = j.at("handle");
     o.networkTransport = j.at("networkTransport");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace NetworkChangeManager
#endif // JSONRPC_CPCAPI2_NETWORKCHANGEMANAGER_NETWORKCHANGEMANAGERNETWORKCHANGEEVENT_H
