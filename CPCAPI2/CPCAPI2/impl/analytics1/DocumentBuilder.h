#pragma once
#ifndef __CPCAPI2_DOCUMENTBUILDER_H__
#define __CPCAPI2_DOCUMENTBUILDER_H__

#include <string>

struct msrp_tree;

namespace CPCAPI2
{
   namespace Analytics
   {
      /**
       * The purpose of the Document Builder class is to take the data in the
       * "registry" and convert it into an XML document suitable for transmission
       * to the stretto server (note that this class does not actually handle the
       * transmission itself)
       */
      class DocumentBuilder
      {
      public:

         DocumentBuilder( msrp_tree* pRegistry );
         virtual ~DocumentBuilder();

         /**
          * Converts the registry to a string, which is returned as an out param.
          * @return false if the document was not converted properly
          */
         bool toDocument( std::string& outDocument );

      private:

         msrp_tree *mRegistry;
      };
   }
}

#endif // __CPCAPI2_DOCUMENTBUILDER_H__