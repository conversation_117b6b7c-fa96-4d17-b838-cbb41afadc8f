#include "jsonrpc/OptionalSerialization.h"
#include <any>
#include "jsonrpc/VectorSerialization.h"
#include "jsonrpc/StringSerialization.h"
#include <nlohmann/json.hpp>
#include "MediaInfo.h"

#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONCONVERSATIONMEDIACHANGEREQUESTEVENT_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONCONVERSATIONMEDIACHANGEREQUESTEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationConversationMediaChangeRequestEvent
  {
  public:
    std::optional<int32_t> conversationHandle;
    std::optional<cpc::vector<MediaInfo>> remoteMediaInfo;

    SipConversationConversationMediaChangeRequestEvent() = default;

    SipConversationConversationMediaChangeRequestEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationConversationMediaChangeRequestEvent& o)
    {
      j = nlohmann::json::object();
       if (o.conversationHandle) { j["conversationHandle"] = *(o.conversationHandle); }
     if (o.remoteMediaInfo) { j["remoteMediaInfo"] = *(o.remoteMediaInfo); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationConversationMediaChangeRequestEvent& o)
    {
     if (j.contains("conversationHandle")) { o.conversationHandle = j.at("conversationHandle"); }
     if (j.contains("remoteMediaInfo")) { o.remoteMediaInfo = j.at("remoteMediaInfo"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONCONVERSATIONMEDIACHANGEREQUESTEVENT_H
