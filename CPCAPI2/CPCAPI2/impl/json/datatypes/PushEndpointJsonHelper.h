// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef PushEndpoint_JSON_HELPER_H
#define PushEndpoint_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/experimental/push_endpoint/PushNotificationCommonTypes.h>
#include <interface/experimental/push_endpoint/PushNotificationEndpointHandler.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo& v)
    {
      w.StartObject();
      Write(w, "pushEndpointId", v.pushEndpointId);
      Write(w, "pushNetworkType", v.pushNetworkType);
      Write(w, "deviceToken", v.deviceToken);
      Write(w, "apnInfo", v.apnInfo);
      Write(w, "fcmInfo", v.fcmInfo);
      Write(w, "websocketInfo", v.websocketInfo);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo& v)
    {
      Read(json, "pushEndpointId", v.pushEndpointId);
      Read(json, "pushNetworkType", v.pushNetworkType);
      Read(json, "deviceToken", v.deviceToken);
      Read(json, "apnInfo", v.apnInfo);
      Read(json, "fcmInfo", v.fcmInfo);
      Read(json, "websocketInfo", v.websocketInfo);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo::APNInfo& v)
    {
      w.StartObject();
      Write(w, "apnsTopic", v.apnsTopic);
      Write(w, "apnsTopicPushKit", v.apnsTopicPushKit);
      Write(w, "pushKitToken", v.pushKitToken);
      Write(w, "useSandbox", v.useSandbox);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo::APNInfo& v)
    {
      Read(json, "apnsTopic", v.apnsTopic);
      Read(json, "apnsTopicPushKit", v.apnsTopicPushKit);
      Read(json, "pushKitToken", v.pushKitToken);
      Read(json, "useSandbox", v.useSandbox);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo::FCMInfo& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo::FCMInfo& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo::WSInfo& v)
    {
      w.StartObject();
      Write(w, "jsonUserHandle", v.jsonUserHandle);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo::WSInfo& v)
    {
      Read(json, "jsonUserHandle", v.jsonUserHandle);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushEndpoint::PushDatabaseSettings& v)
    {
      w.StartObject();
      Write(w, "redisIp", v.redisIp);
      Write(w, "redisPort", v.redisPort);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushEndpoint::PushDatabaseSettings& v)
    {
      Read(json, "redisIp", v.redisIp);
      Read(json, "redisPort", v.redisPort);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushEndpoint::PushNotificationEvent& v)
    {
      w.StartObject();
      Write(w, "jsonDocument", v.jsonDocument);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushEndpoint::PushNotificationEvent& v)
    {
      Read(json, "jsonDocument", v.jsonDocument);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushEndpoint::PushRegistrationSuccessEvent& v)
    {
      w.StartObject();
      Write(w, "endpointId", v.endpointId);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushEndpoint::PushRegistrationSuccessEvent& v)
    {
      Read(json, "endpointId", v.endpointId);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushEndpoint::PushRegistrationFailureEvent& v)
    {
      w.StartObject();
      Write(w, "errorText", v.errorText);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushEndpoint::PushRegistrationFailureEvent& v)
    {
      Read(json, "errorText", v.errorText);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushEndpoint::PushRegistrationQueryListResult& v)
    {
      w.StartObject();
      Write(w, "registrationList", v.registrationList);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushEndpoint::PushRegistrationQueryListResult& v)
    {
      Read(json, "registrationList", v.registrationList);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushEndpoint::PushRegistrationQueryInfoResult& v)
    {
      w.StartObject();
      Write(w, "pushEndpointHandle", v.pushEndpointHandle);
      Write(w, "jsonUserHandle", v.jsonUserHandle);
      Write(w, "pushEndpointId", v.pushEndpointId);
      Write(w, "pushNetworkType", v.pushNetworkType);
      Write(w, "apnsTopic", v.apnsTopic);
      Write(w, "deviceToken", v.deviceToken);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushEndpoint::PushRegistrationQueryInfoResult& v)
    {
      Read(json, "pushEndpointHandle", v.pushEndpointHandle);
      Read(json, "jsonUserHandle", v.jsonUserHandle);
      Read(json, "pushEndpointId", v.pushEndpointId);
      Read(json, "pushNetworkType", v.pushNetworkType);
      Read(json, "apnsTopic", v.apnsTopic);
      Read(json, "deviceToken", v.deviceToken);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushEndpoint::PushNotificationEndpointHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushEndpoint::PushNotificationEndpointHandler& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // PushEndpoint_JSON_HELPER_H