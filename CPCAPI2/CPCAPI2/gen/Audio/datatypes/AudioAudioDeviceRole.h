#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIODEVICEROLE_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIODEVICEROLE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
  enum class AudioAudioDeviceRole
  {
    None,
    Headset,
    SpeakerPhone,
    Ringing,
    Bluetooth
  };

  inline std::string to_string(AudioAudioDeviceRole value)
  {
    switch (value)
    {
      case AudioAudioDeviceRole::None:
        return "None";
      case AudioAudioDeviceRole::Headset:
        return "Headset";
      case AudioAudioDeviceRole::SpeakerPhone:
        return "SpeakerPhone";
      case AudioAudioDeviceRole::Ringing:
        return "Ringing";
      case AudioAudioDeviceRole::Bluetooth:
        return "Bluetooth";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, AudioAudioDeviceRole value)
  {
    switch (value)
    {
      case AudioAudioDeviceRole::None:
        os << "None";
        break;
      case AudioAudioDeviceRole::Headset:
        os << "Headset";
        break;
      case AudioAudioDeviceRole::SpeakerPhone:
        os << "SpeakerPhone";
        break;
      case AudioAudioDeviceRole::Ringing:
        os << "Ringing";
        break;
      case AudioAudioDeviceRole::Bluetooth:
        os << "Bluetooth";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const AudioAudioDeviceRole& e)
  {
    switch (e)
    {
      case AudioAudioDeviceRole::None:
        j = "None";
        break;
      case AudioAudioDeviceRole::Headset:
        j = "Headset";
        break;
      case AudioAudioDeviceRole::SpeakerPhone:
        j = "SpeakerPhone";
        break;
      case AudioAudioDeviceRole::Ringing:
        j = "Ringing";
        break;
      case AudioAudioDeviceRole::Bluetooth:
        j = "Bluetooth";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, AudioAudioDeviceRole& e)
  {
      if (0 == j.get<std::string>().compare("None"))
    {
      e = AudioAudioDeviceRole::None;
    }
    else if (0 == j.get<std::string>().compare("Headset"))
    {
      e = AudioAudioDeviceRole::Headset;
    }
    else if (0 == j.get<std::string>().compare("SpeakerPhone"))
    {
      e = AudioAudioDeviceRole::SpeakerPhone;
    }
    else if (0 == j.get<std::string>().compare("Ringing"))
    {
      e = AudioAudioDeviceRole::Ringing;
    }
    else if (0 == j.get<std::string>().compare("Bluetooth"))
    {
      e = AudioAudioDeviceRole::Bluetooth;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum AudioAudioDeviceRole"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIODEVICEROLE_H
