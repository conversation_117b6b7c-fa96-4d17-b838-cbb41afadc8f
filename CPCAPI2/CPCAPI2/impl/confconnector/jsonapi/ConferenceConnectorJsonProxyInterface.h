#pragma once

#if !defined(CPCAPI2_CONFERENCE_CONNECTOR_JSON_PROXY_INTERFACE_H)
#define CPCAPI2_CONFERENCE_CONNECTOR_JSON_PROXY_INTERFACE_H

#include "confconnector/ConferenceConnectorJsonProxy.h"
#include "jsonapi/JsonApiClientModule.h"
#include "phone/Cpcapi2EventSource.h"
#include "impl/phone/PhoneModule.h"
#include "util/AutoTestProcessor.h"

#include <rutil/Reactor.hxx>
#include <rutil/Fifo.hxx>

#include <future>

namespace CPCAPI2
{
class PhoneInterface;
namespace ConferenceConnector
{
class ConferenceConnectorJsonSyncHandler {};
class ConferenceConnectorJsonProxyInterface : public CPCAPI2::EventSource<ConferenceConnectorHandle, ConferenceConnectorHandler, ConferenceConnectorJsonSyncHandler>,
                                           public CPCAPI2::ConferenceConnector::ConferenceConnectorManagerJsonProxy,
                                           public CPCAPI2::JsonApi::JsonApiClientModule,
                                           public CPCAPI2::PhoneModule
{

public:

   ConferenceConnectorJsonProxyInterface(CPCAPI2::Phone* phone);
   virtual ~ConferenceConnectorJsonProxyInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   virtual int process(unsigned int timeout) OVERRIDE {
      return CPCAPI2::EventSource<ConferenceConnectorHandle, ConferenceConnectorHandler, ConferenceConnectorJsonSyncHandler>::process(timeout);
   }
   virtual void interruptProcess() OVERRIDE {
      CPCAPI2::EventSource<ConferenceConnectorHandle, ConferenceConnectorHandler, ConferenceConnectorJsonSyncHandler>::interruptProcess();
   }
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE {
      CPCAPI2::EventSource<ConferenceConnectorHandle, ConferenceConnectorHandler, ConferenceConnectorJsonSyncHandler>::setCallbackHook(cbHook, context);
   }

   // JsonApiClientModule
   virtual void setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport) OVERRIDE;
   virtual int processIncoming(const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // ConferenceConnectorManagerJsonProxy

   // Inherited via ConferenceConnectorManager
   virtual ConferenceConnectorHandle createConferenceConnector() OVERRIDE;
   virtual int setHandler(ConferenceConnectorHandle conn, ConferenceConnectorHandler* handler) OVERRIDE;
   virtual int destroyConferenceConnector(ConferenceConnectorHandle conn) OVERRIDE;
   virtual int setConnectionSettings(ConferenceConnectorHandle conn, const ConferenceConnectorSettings& settings) OVERRIDE;
   virtual int connectToConferenceService(ConferenceConnectorHandle conn) OVERRIDE;
   virtual int disconnectFromConferenceService(ConferenceConnectorHandle conn) OVERRIDE;
   virtual int queryConferenceList(ConferenceConnectorHandle conn) OVERRIDE;
   virtual int createNewConference(ConferenceConnectorHandle conn, const CloudConferenceSettings& settings) OVERRIDE;
   virtual int destroyConference(CloudConferenceHandle conference) OVERRIDE;
   virtual int queryParticipantList(CloudConferenceHandle conference) OVERRIDE;
   virtual CloudConferenceSessionHandle createConferenceSession(CloudConferenceHandle conference) OVERRIDE;
   virtual CloudConferenceSessionHandle createConferenceSession(CloudConferenceHandle conference, const CloudConferenceSessionSettings& settings) OVERRIDE;
   virtual int setSessionSettings(CloudConferenceSessionHandle session, const CloudConferenceSessionSettings& settings) OVERRIDE;
   virtual int setSessionMediaSettings(CloudConferenceSessionHandle session, const CloudConferenceSessionMediaSettings& settings) OVERRIDE;
   virtual int startSession(CloudConferenceSessionHandle session) OVERRIDE;
   virtual int endSession(CloudConferenceSessionHandle session) OVERRIDE;
   virtual int updateSessionMedia(CloudConferenceSessionHandle session) OVERRIDE;
   virtual int setParticipantPermissions(CloudConferenceSessionHandle session, CloudConferenceParticipantHandle participant, const ConferenceSessionPermissions& permissions) OVERRIDE;
   virtual int pauseSessionPlayout(CloudConferenceSessionHandle session) OVERRIDE;
   virtual int resumeSessionPlayout(CloudConferenceSessionHandle session) OVERRIDE;

private:
   void createConferenceConnectorImpl();
   void destroyConferenceConnectorImpl(ConferenceConnectorHandle conn);
   void setConnectionSettingsImpl(ConferenceConnectorHandle conn, const ConferenceConnectorSettings& settings);
   void connectToConferenceServiceImpl(ConferenceConnectorHandle conn);
   void disconnectFromConferenceServiceImpl(ConferenceConnectorHandle conn);
   void queryConferenceListImpl(ConferenceConnectorHandle conn);
   void createNewConferenceImpl(ConferenceConnectorHandle conn, const CloudConferenceSettings& settings);
   void destroyConferenceImpl(CloudConferenceHandle conference);
   void queryParticipantListImpl(CloudConferenceHandle conference);
   void createConferenceSessionImpl(CloudConferenceHandle conference, const CloudConferenceSessionSettings& settings);
   void setSessionSettingsImpl(CloudConferenceSessionHandle session, const CloudConferenceSessionSettings& settings);
   void setSessionMediaSettingsImpl(CloudConferenceSessionHandle session, const CloudConferenceSessionMediaSettings& settings);
   void startSessionImpl(CloudConferenceSessionHandle session);
   void endSessionImpl(CloudConferenceSessionHandle session);
   void updateSessionMediaImpl(CloudConferenceSessionHandle session);
   void setParticipantPermissionsImpl(CloudConferenceSessionHandle session, CloudConferenceParticipantHandle participant, const ConferenceSessionPermissions& permissions);
   void pauseSessionPlayoutImpl(CloudConferenceSessionHandle session);
   void resumeSessionPlayoutImpl(CloudConferenceSessionHandle session);

   void processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request);
   int handleCreateConferenceConnector(const rapidjson::Value& functionObjectVal);
   int handleCreateConferenceSession(const rapidjson::Value& functionObjectVal);
   int handleServiceConnectionStatusChanged(const rapidjson::Value& functionObjectVal);
   int handleConferenceCreated(const rapidjson::Value& functionObjectVal);
   int handleConferenceEnded(const rapidjson::Value& functionObjectVal);
   int handleConfereceListUpdated(const rapidjson::Value& functionObjectVal);
   int handleConfereceParticipantListUpdated(const rapidjson::Value& functionObjectVal);
   int handleConferenceSessionStatusChanged(const rapidjson::Value& functionObjectVal);
   int handleConferenceSessionMediaStatusChanged(const rapidjson::Value& functionObjectVal);

private:
   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiTransport* mTransport;
   std::promise<ConferenceConnectorHandle> mServerCreatedConfConnectorHandle;
   std::promise<CloudConferenceSessionHandle> mServerCreatedConfSessionHandle;
};

}

}

#endif // CPCAPI2_CONFERENCE_CONNECTOR_JSON_PROXY_INTERFACE_H
