#pragma once

#if !defined(CPCAPI2_INSTANT_MESSAGE_IMPL_H)
#define CPCAPI2_INSTANT_MESSAGE_IMPL_H

#include "cpcapi2defs.h"
#include "im/SipInstantMessageInterface.h"

#include "../account/SipAccountImpl.h"
#include "../account/SipAccountAwareFeature.h"
#include "../util/DumFpCommand.h"
#include "../iscomposing/IsComposingManager.h"
#include "../iscomposing/IsComposingInfo.h"

#include <resip/dum/PagerMessageHandler.hxx>
#include <resip/dum/MasterProfile.hxx>
#include <resip/dum/DialogUsageManager.hxx>

#include <map>

namespace CPCAPI2
{
namespace SipInstantMessage
{
struct InstantMessageIsComposingInfo : public CPCAPI2::IsComposing::IsComposingInfo
{
   resip::NameAddr remoteNameAddr;
   CPCAPI2::IsComposing::IsComposingMessageState state;
};

struct InstantMessageMimeType
{
  cpc::string type;
  std::map<cpc::string, cpc::string> parameters;
};

class SipInstantMessageHandler;

class SipInstantMessageImpl : public CPCAPI2::SipAccount::SipAccountAwareFeature,
                              public CPCAPI2::SipAccount::CPPagerMessageHandlerDelegate,
                              public CPCAPI2::IsComposing::IsComposingManager
{
public:
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, SipInstantMessageImpl*> InstantMessageMap;
   SipInstantMessageImpl(SipInstantMessageInterface* iff, std::shared_ptr<InstantMessageMap> parentMap, PhoneInterface* cpcPhone, CPCAPI2::SipAccount::SipAccountImpl& account);
   virtual ~SipInstantMessageImpl();

   int initialize();
   int acceptMimeType(const cpc::string& mimeType);
   int acceptMimeTypeEnum(const SipInstantMessageManager::MimeType mimeType);
   int acceptIncoming(SipInstantMessageHandle im);
   int rejectIncoming(SipInstantMessageHandle im, unsigned int responseCode);
   int rejectIncomingMimeType(SipInstantMessageHandle im, const cpc::vector<cpc::string>& acceptMimeTypes);
   int sendOutgoingMessage(
         SipInstantMessageHandle h,
         cpc::string targetAddress,
         const resip::Data& content,
         SipInstantMessageManager::MimeType mimeType,
         const cpc::string& messageType);
   int sendOutgoingMessage(
         SipInstantMessageHandle h,
         cpc::string targetAddress,
         const resip::Data& content,
         const cpc::string& customMimeType,
         const cpc::string& messageType);
   int setIsComposingMessage(
         const cpc::string& targetAddress,
         SipInstantMessageManager::MimeType mimeType,
         struct std::tm datetime,
         int refreshInterval,
         int idleInterval);
   int setIsComposingMessage(
         const cpc::string& targetAddress,
         const cpc::string& customMimeType,
         struct std::tm datetime,
         int refreshInterval,
         int idleInterval);

   bool hasIncomingInstantMessage(SipInstantMessageHandle im) const;

private:
   SipInstantMessageInterface* mInterface;
   CPCAPI2::SipAccount::SipAccountImpl& mAccount;
   std::map<SipInstantMessageHandle, resip::ServerPagerMessageHandle> mServerHandleMap;
   std::map<SipInstantMessageHandle, resip::ClientPagerMessageHandle> mClientHandleMap;
   SipInstantMessageHandle mNextHandle;
   cpc::vector<cpc::string> mAcceptableMimeTypesStrings;
   cpc::vector<InstantMessageMimeType> mAcceptableMimeTypes;
   std::map<resip::ClientPagerMessageHandle, IsComposing::IsComposingMessageState> mClientHandleIsComposingStateMap;
   std::map<resip::Uri, InstantMessageIsComposingInfo*> mRemoteUriIsComposingInfoMap;

   std::weak_ptr<InstantMessageMap> mParentMap;

   // IAccountAware
   virtual int registerSdkPagerMessageHandler(CPCAPI2::SipAccount::CPPagerMessageHandler& pagerMessageHandler) OVERRIDE;
   virtual int adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile) OVERRIDE;
   virtual int addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig) OVERRIDE { return kSuccess; }
   virtual int onDumBeingDestroyed() OVERRIDE;
   virtual void release() OVERRIDE;
   virtual int registerSdkDialogSetFactory(CPCAPI2::SipAccount::AppDialogSetFactory& factory) OVERRIDE;

   // ServerPagerMessageHandler
   virtual void onMessageArrived(resip::ServerPagerMessageHandle h, const resip::SipMessage& message) OVERRIDE;

   // ClientPagerMessageHandler
   virtual void onSuccess(resip::ClientPagerMessageHandle h, const resip::SipMessage& status) OVERRIDE;
   virtual void onFailure(resip::ClientPagerMessageHandle h, const resip::SipMessage& status, std::unique_ptr<resip::Contents> contents) OVERRIDE;

   // CPPagerMessageHandlerDelegate
   virtual bool isMyMessage(const resip::SipMessage& msg) OVERRIDE;
   virtual bool isMyResponse(resip::ClientPagerMessageHandle h, const resip::SipMessage& msg) OVERRIDE;

   // IsComposingManager
   virtual void sendIsComposingMessageNotification(CPCAPI2::IsComposing::IsComposingInfo* info, CPCAPI2::IsComposing::IsComposingMessageState state, const resip::Mime& contentType, int refreshInterval, const tm& lastActive) OVERRIDE;
   virtual void onIsComposingMessage(CPCAPI2::IsComposing::IsComposingInfo* info, CPCAPI2::IsComposing::IsComposingMessageState state, const resip::Mime& contentType, const tm& lastActive) OVERRIDE;
   virtual void backToComposerIdleState(CPCAPI2::IsComposing::IsComposingInfo* info) OVERRIDE;
   virtual void backToReceiverIdleState(CPCAPI2::IsComposing::IsComposingInfo* info) OVERRIDE;

   InstantMessageIsComposingInfo* getIsComposingInfo(const resip::NameAddr& remoteNameAddr, bool createInfo);
   void removeIsComposingInfo(CPCAPI2::IsComposing::IsComposingInfo* info);
   SipInstantMessageHandle getSdkHandleFrom(resip::ClientPagerMessageHandle h);
   int setIsComposingMessage(const cpc::string& targetAddress, const resip::Mime& contentType, struct std::tm datetime, int refreshInterval, int idleInterval);
   InstantMessageMimeType parseMimeType(cpc::string mimeType);
   bool isMimeTypeAcceptable(cpc::string mimeType);
   void fireError(const SipInstantMessageHandle& h, const cpc::string& errorText);
   bool isIsComposingNotificationSent(resip::ClientPagerMessageHandle h);
   bool isIsComposingMessageReceived(const resip::SipMessage& message);
   void onIsComposingMessageArrived(resip::ServerPagerMessageHandle h, const resip::SipMessage& message);
   void onInstantMessageArrived(resip::ServerPagerMessageHandle h, const resip::SipMessage& message);
   void onSetIsComposingSuccess(resip::ClientPagerMessageHandle h, const resip::SipMessage& status);
   void onSendInstantMessageSuccess(resip::ClientPagerMessageHandle h, const resip::SipMessage& status);
   void onSetIsComposingFailure(resip::ClientPagerMessageHandle h, const resip::SipMessage& status);
   void onSendInstantMessageFailure(resip::ClientPagerMessageHandle h, const resip::SipMessage& status);
};

}
}
#endif // CPCAPI2_INSTANT_MESSAGE_IMPL_H
