#pragma once

#if !defined(__CPCAPI2_SIP_CHAT_HELPER_H__)
#define __CPCAPI2_SIP_CHAT_HELPER_H__

#include "../cpm/CpmHelper.h"
#include "chat/SipChatHandler.h"
#include "../iscomposing/IsComposingHelper.h"

#include <resip/dum/InviteSessionHandler.hxx>

using namespace CPCAPI2::CPM;

namespace CPCAPI2
{
namespace SipChat
{

class SipChatHelper : public IsComposing::IsComposingHelper
{
public:
   static ChatEndReason getChatEndReason(resip::InviteSessionHandler::TerminatedReason reason);
   static CpimMessage createCpimMessage(const cpc::string& messageId, const resip::Uri& fromUri, const resip::Uri& toUri, const IsComposingDocument& isComposingDocument);
   static void extractCpimMessage(CpimMessage cpimMessage, cpc::string& messageId, IsComposingDocument& isComposingDocument);
   static bool isIsComposingMessageNotification(CpimMessage cpimMessage);
   static resip::Data buildSdp(msrp_uri_t* localUri, UInt32 localMediaPort, const resip::Mime& cpimContentType, bool isActive);
};

}
}

#endif // __CPCAPI2_SIP_CHAT_HELPER_H__