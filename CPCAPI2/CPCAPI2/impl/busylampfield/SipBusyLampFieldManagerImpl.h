#pragma once

#if !defined(__CPCAPI2_SIP_BUSY_LAMP_FIELD_MANAGER_IMPL_H__)
#define __CPCAPI2_SIP_BUSY_LAMP_FIELD_MANAGER_IMPL_H__

#include "cpcapi2defs.h"
#include "SipBusyLampFieldManagerInterface.h"
#include "SipBusyLampFieldRemoteLineSetInfo.h"
#include "SipBusyLampFieldStateImpl.h"
#include "busylampfield/SipBusyLampFieldHandler.h"
#include "dialogevent/SipDialogEventSubscriptionManagerInterface.h"
#include "call/SipAVConversationManagerInterface.h"
#include "account/SipAccountImpl.h"

namespace CPCAPI2
{
namespace SipBusyLampField
{

struct RemoteLinePair
{
   SipBusyLampFieldRemoteLineSetHandle remoteLineSet;
   SipBusyLampFieldRemoteLineHandle remoteLine;

   RemoteLinePair()
   {
      remoteLineSet = 0;
      remoteLine = "";
   }
};

struct BusyLampFieldResubscribe
{
   SipAccount::SipAccountHandle                                   account;
   std::shared_ptr<resip::DeadlineTimer<resip::MultiReactor> >    m_Timer;
   cpc::string                                                    buddyAddress;
   SipEvent::SipEventSubscriptionHandle                           subscription;
   int                                                            statusCode;

   BusyLampFieldResubscribe(resip::MultiReactor& service, cpc::string address, SipEvent::SipEventSubscriptionHandle handle)
   {
      m_Timer.reset(new resip::DeadlineTimer<resip::MultiReactor>(service));
      buddyAddress = address;
      account = 0xffffffff;
      subscription = handle;
      statusCode = 0;
   }
};

typedef cpc::vector<RemoteLinePair> RemoteLinePairVector;
typedef std::map<SipEvent::SipEventSubscriptionHandle, BusyLampFieldResubscribe*> ResubscribeMap;

class SipBusyLampFieldManagerImpl : public CPCAPI2::EventSyncHandler<CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandler>,
                                    public resip::DeadlineTimerHandler
{
public:
   SipBusyLampFieldManagerImpl(CPCAPI2::PhoneInterface* phone, SipBusyLampFieldManagerInterface* iff, CPCAPI2::SipAccount::SipAccountHandle account, SipBusyLampFieldStateManager* stateManager, CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionManagerInterface* sipDialogEventIf, CPCAPI2::SipConversation::SipAVConversationManagerInterface* sipConversationIf);
   virtual ~SipBusyLampFieldManagerImpl();

   // Invoked by the interface
   SipBusyLampFieldRemoteLineSetInfo* createRemoteLineSetInfo(SipBusyLampFieldRemoteLineSetHandle handle);
   SipBusyLampFieldRemoteLineSetInfo* getRemoteLineSetInfo(SipBusyLampFieldRemoteLineSetHandle handle) const;
   void startSubscriptions(SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo);
   void endSubscriptions(SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo);
   void fireError(SipBusyLampFieldRemoteLineSetHandle remoteLineSetHandle, const cpc::string& msg);
   CPCAPI2::SipConversation::SipConversationHandle answerCall(SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo, const SipBusyLampFieldRemoteLineHandle& remoteLine, const cpc::string& dialogId, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor);
   CPCAPI2::SipConversation::SipConversationHandle joinCall(SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo, const SipBusyLampFieldRemoteLineHandle& remoteLine, const cpc::string& dialogId, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor, BargeInMode bargeInMode, AlertMode alertMode);

private:
   PhoneInterface* mPhone;
   SipBusyLampFieldManagerInterface* mInterface;
   CPCAPI2::SipAccount::SipAccountHandle mAccount;
   SipBusyLampFieldStateManager* mStateManager;
   CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionManagerInterface* mSipDialogEventSubscriptionIf;
   CPCAPI2::SipConversation::SipAVConversationManagerInterface* mSipConversationIf;
   std::set<SipBusyLampFieldHandler*> mSdkObservers;
   SipBusyLampFieldHandler* mHandler;
   SipBusyLampFieldRemoteLineSetMap mRemoteLineSetInfoMap;
   ResubscribeMap mResubscribeMap;

   // SipDialogEventSubscriptionHandler interface
   virtual int onNewSubscription(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::NewDialogEventSubscriptionEvent& args);
   virtual int onSubscriptionEnded(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::DialogEventSubscriptionEndedEvent& args);
   virtual int onIncomingDialogInfo(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::IncomingDialogInfoEvent& args);
   virtual int onDialogResourceListUpdated(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::DialogResourceListEvent& args);
   virtual int onSubscriptionStateChanged(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::DialogEventSubscriptionStateChangedEvent& args);
   virtual int onNotifyDialogInfoFailure(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::NotifyDialogInfoFailureEvent& args);
   virtual int onError(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::ErrorEvent& args);

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   cpc::vector<SipBusyLampFieldRemoteLineCallInfo> createCalls(SipBusyLampFieldRemoteLineHandle sca, const CPCAPI2::SipDialogEvent::DialogInfoDocument& dialogInfoDoc);
   cpc::string getRemoteLineName(const CPCAPI2::SipDialogEvent::DialogInfoDocument& dialogInfoDoc) const;
   RemoteLinePairVector getRemoteLinesForSubscription(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, bool& isRlsSubscription) const;
   SipBusyLampFieldSubscriptionState getLineSubscriptionState(SipBusyLampFieldRemoteLineHandle remoteLine) const;
   SipBusyLampFieldSubscriptionState getSubscriptionState(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription) const;
   void setSubscriptionState(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, SipBusyLampFieldSubscriptionState state);
   CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionSettings dialogEventSubSettings(const SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo) const;
   CPCAPI2::SipDialogEvent::DialogInfo getDialogForRemoteLine(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, const cpc::string& dialogId);
   bool isAllowedBargeInMode(SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo, BargeInMode bargeInMode);
   void fireRemoteLineNewSubscription(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const RemoteLineNewSubscriptionEvent& event);
   void fireRemoteLineSubscriptionStateChanged(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const RemoteLineSubscriptionStateChangedEvent& event);
   void fireRemoteLineSubscriptionEnded(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const RemoteLineSubscriptionEndedEvent& event);
   void fireRemoteLineStateChanged(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const RemoteLineStateChangedEvent& event);
   void fireRemoteLineEmptySubscriptions(const RemoteLineEmptySubscriptionsEvent& event);
   void fireError(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const ErrorEvent& event);
   bool isRemoteLinePairValid(RemoteLinePair remoteLine) const;
};

}
}

#endif // __CPCAPI2_SIP_BUSY_LAMP_FIELD_MANAGER_IMPL_H__
