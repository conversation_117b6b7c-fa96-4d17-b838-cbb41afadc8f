#include "IPhone.h"
#include "impl/jsonrpc/IJsonRpcServerModuleProcessing.h"

#ifndef JSONRPC_CPCAPI2_PHONE_PHONESERVER_H
#define JSONRPC_CPCAPI2_PHONE_PHONESERVER_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
class PhoneServer
{
public:
    PhoneServer(::CPCAPI2::IJsonRpcServerModuleProcessing& transport, IPhone& impl) :
        mTransport(transport),
        mImpl(impl)
    {
        mTransport.registerFunction("Phone::create", jsonrpccxx::methodHandle(&IPhone::create, mImpl), {});
	mTransport.registerFunction("Phone::release", jsonrpccxx::methodHandle(&IPhone::release, mImpl), {{ "handle" }});
	mTransport.registerFunction("Phone::getVersion", jsonrpccxx::methodHandle(&IPhone::getVersion, mImpl), {});
	mTransport.registerFunction("Phone::initialize", jsonrpccxx::methodHandle(&IPhone::initialize, mImpl), {{ "handle" }, { "licenseInfo" }, { "useNetworkChangeManager" }, { "ciphers", nullptr }});
	mTransport.registerFunction("Phone::setLoggingEnabled", jsonrpccxx::methodHandle(&IPhone::setLoggingEnabled, mImpl), {{ "handle" }, { "enabled" }});
	mTransport.registerFunction("Phone::setFileLoggingEnabled", jsonrpccxx::methodHandle(&IPhone::setFileLoggingEnabled, mImpl), {{ "handle" }, { "enabled" }, { "id", nullptr }});
	mTransport.registerFunction("Phone::setLogDirectory", jsonrpccxx::methodHandle(&IPhone::setLogDirectory, mImpl), {{ "handle" }, { "directory" }});
	mTransport.registerFunction("Phone::setLogLevel", jsonrpccxx::methodHandle(&IPhone::setLogLevel, mImpl), {{ "handle" }, { "level" }});
	mTransport.registerFunction("Phone::getInstanceId", jsonrpccxx::methodHandle(&IPhone::getInstanceId, mImpl), {{ "handle" }});
	mTransport.registerFunction("Phone::createEventQueue", jsonrpccxx::methodHandle(&IPhone::createEventQueue, mImpl), {{ "handle" }, { "events", nullptr }});
	mTransport.registerFunction("Phone::destroyEventQueue", jsonrpccxx::methodHandle(&IPhone::destroyEventQueue, mImpl), {{ "handle" }, { "eventQueueHandle" }});
	mTransport.registerFunction("Phone::getEvent", jsonrpccxx::methodHandle(&IPhone::getEvent, mImpl), {{ "handle" }, { "eventQueueHandle" }, { "timeout" }});
    }

private:
    ::CPCAPI2::IJsonRpcServerModuleProcessing& mTransport;
    IPhone& mImpl;
}; // class PhoneServer
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_PHONESERVER_H
