#include <brand_branded.h>

#include <analytics1/AnalyticsManager.h>
#include <phone/PhoneInterface.h>

#if (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)
#include "AnalyticsManagerInterface.h"
#endif

using namespace CPCAPI2::Analytics;

AnalyticsManager* AnalyticsManager::getInterface(CPCAPI2::Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)
   CPCAPI2::PhoneInterface* phone = dynamic_cast< CPCAPI2::PhoneInterface* >(cpcPhone);
   return _GetInterface<AnalyticsManagerInterface>(phone, "AnalyticsManager");
#else
   return NULL;
#endif
}
