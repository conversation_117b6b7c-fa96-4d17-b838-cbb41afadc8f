#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_BUSY_LAMP_FIELD_MODULE == 1)

#include "SipBusyLampFieldStateImpl.h"
#include "SipBusyLampFieldHelper.h"

namespace CPCAPI2
{
namespace SipBusyLampField
{

SipBusyLampFieldRemoteLineState SipBusyLampFieldStateImpl::REMOTE_LINE_STATE_NOT_FOUND;
SipBusyLampFieldRemoteLineCallInfo SipBusyLampFieldStateImpl::REMOTE_LINE_CALL_NOT_FOUND;

SipBusyLampFieldStateImpl::SipBusyLampFieldStateImpl(SipBusyLampFieldManagerInterface* blfManagerIf)
   : mBlfManagerIf(blfManagerIf)
{
}

SipBusyLampFieldStateImpl::~SipBusyLampFieldStateImpl()
{
}

void SipBusyLampFieldStateImpl::Release()
{
   delete this;
}

int SipBusyLampFieldStateImpl::getState(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, SipBusyLampFieldRemoteLineSetState& remoteLineSetState)
{
   std::map<SipBusyLampFieldRemoteLineSetHandle, SipBusyLampFieldRemoteLineSetState>::const_iterator it = mStateMap.find(remoteLineSet);
   if (it != mStateMap.end())
   {
      remoteLineSetState = it->second;
      return kSuccess;
   }

   return kError;
}

int SipBusyLampFieldStateImpl::getState(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, SipBusyLampFieldRemoteLineHandle remoteLine, SipBusyLampFieldRemoteLineState& remoteLineState)
{
   SipBusyLampFieldRemoteLineSetState remoteLineSetState;
   if (getState(remoteLineSet, remoteLineSetState) == kSuccess)
   {
      remoteLineState = getRemoteLineState(remoteLineSetState, remoteLine, false);
      return !remoteLineState.remoteLine.empty() ? kSuccess : kError;
   }

   return kError;
}

int SipBusyLampFieldStateImpl::getCall(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, SipBusyLampFieldRemoteLineHandle remoteLine, const cpc::string& dialogId, SipBusyLampFieldRemoteLineCallInfo& call)
{
   SipBusyLampFieldRemoteLineState remoteLineState;
   if (getState(remoteLineSet, remoteLine, remoteLineState) == kSuccess)
   {
      call = getRemoteLineCall(remoteLineState, dialogId);
      return !call.dialog.id.empty() ? kSuccess : kError;
   }

   return kError;
}

int SipBusyLampFieldStateImpl::onRemoteLineNewSubscription(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const RemoteLineNewSubscriptionEvent& args)
{
   SipBusyLampFieldRemoteLineState& remoteLineState = getRemoteLineState(args.account, remoteLineSet, args.remoteLine);
   remoteLineState.subscriptionStarted = true;
   remoteLineState.subscriptionState = CPCAPI2::SipEvent::SipSubscriptionState_Pending;
   return kSuccess;
}

int SipBusyLampFieldStateImpl::onRemoteLineSubscriptionStateChanged(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const RemoteLineSubscriptionStateChangedEvent& args)
{
   SipBusyLampFieldRemoteLineState& remoteLineState = getRemoteLineState(args.account, remoteLineSet, args.remoteLine);
   remoteLineState.subscriptionState = args.subscriptionState;
   return kSuccess;
}

int SipBusyLampFieldStateImpl::onRemoteLineSubscriptionEnded(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const RemoteLineSubscriptionEndedEvent& args)
{
   SipBusyLampFieldRemoteLineState& remoteLineState = getRemoteLineState(args.account, remoteLineSet, args.remoteLine);
   remoteLineState.subscriptionState = CPCAPI2::SipEvent::SipSubscriptionState_Terminated;
   remoteLineState.subscriptionStarted = false;
   return kSuccess;
}

int SipBusyLampFieldStateImpl::onRemoteLineStateChanged(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const RemoteLineStateChangedEvent& args)
{
   SipBusyLampFieldRemoteLineState& remoteLineState = getRemoteLineState(args.account, remoteLineSet, args.remoteLine);
   remoteLineState.calls = args.calls;
   return kSuccess;
}

int SipBusyLampFieldStateImpl::onRemoteLineEmptySubscriptions(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const RemoteLineEmptySubscriptionsEvent& args)
{
   return kSuccess;
}

int SipBusyLampFieldStateImpl::onError(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const ErrorEvent& args)
{
   return kSuccess;
}

SipBusyLampFieldRemoteLineState& SipBusyLampFieldStateImpl::getRemoteLineState(CPCAPI2::SipAccount::SipAccountHandle account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, SipBusyLampFieldRemoteLineHandle remoteLine)
{  
   std::map<SipBusyLampFieldRemoteLineSetHandle, SipBusyLampFieldRemoteLineSetState>::iterator it = mStateMap.find(remoteLineSet);
   if (it == mStateMap.end())
   {
      // Create a new remote line set state entry in the map
      SipBusyLampFieldRemoteLineSetState remoteLineSetState;
      remoteLineSetState.account = account;
      remoteLineSetState.remoteLineSet = remoteLineSet;
      mStateMap[remoteLineSet] = remoteLineSetState;
   }

   return getRemoteLineState(mStateMap[remoteLineSet], remoteLine, true);
}

SipBusyLampFieldRemoteLineState& SipBusyLampFieldStateImpl::getRemoteLineState(SipBusyLampFieldRemoteLineSetState& remoteLineSetState, SipBusyLampFieldRemoteLineHandle remoteLine, bool create)
{
   // Cycle through the remote lines states to find the remote line specified and update its state
   for (unsigned int i = 0; i <  remoteLineSetState.remoteLineStates.size(); i++)
   {
      SipBusyLampFieldRemoteLineState& remoteLineState = remoteLineSetState.remoteLineStates[i];
      if (remoteLineState.remoteLine == remoteLine)
      {
         // Remote line found
         return remoteLineState;
      }
   }

   if (create)
   {
      // Remote line not found
      // Add a new entry to the remote line states
      SipBusyLampFieldRemoteLineState remoteLineState;
      remoteLineState.remoteLine = remoteLine;
      remoteLineSetState.remoteLineStates.push_back(remoteLineState);
      return remoteLineSetState.remoteLineStates.back();
   }
   else
   {   
      return REMOTE_LINE_STATE_NOT_FOUND;
   }
}

SipBusyLampFieldRemoteLineCallInfo& SipBusyLampFieldStateImpl::getRemoteLineCall(SipBusyLampFieldRemoteLineState& remoteLineState, const cpc::string& dialogId)
{
   // Cycle through all the calls on the remote line and find the one with the dialog id specified
   for (unsigned int i = 0; i <  remoteLineState.calls.size(); i++)
   {
      SipBusyLampFieldRemoteLineCallInfo& call = remoteLineState.calls[i];
      if (call.dialog.id == dialogId)
      {
         return call;
      }
   }

   return REMOTE_LINE_CALL_NOT_FOUND;
}

}
}

#endif // CPCAPI2_BRAND_SIP_BUSY_LAM_FIELD_MODULE