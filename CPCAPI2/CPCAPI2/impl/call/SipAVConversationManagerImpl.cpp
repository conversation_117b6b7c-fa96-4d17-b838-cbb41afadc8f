#include "brand_branded.h"

#if (CPCAPI2_BRAND_CALL_MODULE == 1)
#include "resip/recon/DefaultDialogSet.hxx"
#include "resip/recon/RemoteParticipantDialogSet.hxx"
#include "SipAVConversationManagerImpl.h"
#include "ReconUserAgentImpl.h"
#include "ReconConversationManagerImpl.h"
#include "SipConversationHandlerInternal.h"
#include "SipCallEventMacros.h"
#include "../util/cpc_logger.h"
#include "../phone/NetworkChangeManagerImpl.h"
#include "../log/LocalLogger.h"

#include "gen/SipConversation/datatypes/SipConversationEvents.h"
#include "gen/SipConversation/datatypes/SipConversationConversationStateChangedEvent.h"
#include "gen/SipConversation/datatypes/SipConversationNewConversationEvent.h"
#include "gen/SipConversation/datatypes/SipConversationConversationEndedEvent.h"
#include "gen/SipConversation/datatypes/SipConversationTransferRequestEvent.h"
#include "gen/SipConversation/datatypes/SipConversationConversationMediaChangedEvent.h"
#include "gen/SipConversation/datatypes/SipConversationConversationMediaChangeRequestEvent.h"
#include "gen/SipConversation/datatypes/SipConversationTransferProgressEvent.h"
#include "gen/SipConversation/datatypes/SipConversationTransferResponseEvent.h"
#include "gen/SipConversation/datatypes/SipConversationTransferRequestEvent.h"
#include "gen/SipConversation/datatypes/SipConversationConversationStateChangeRequestEvent.h"
#include "gen/SipConversation/datatypes/SipConversationErrorEvent.h"


// #include "gen/SipConversation/datatypes/SipConversationConversationTransferResponseEvent.h"

#include "phone/EventQueue.h"



using namespace CPCAPI2::SipAccount;
using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_CALL

#define ENUM_SWITCH_CASE_LONG(_enum_name, _enum_item_from, _enum_item_to) {               \
                        case _enum_name::_enum_name ## _ ## _enum_item_to: \
                           return jsonrpc::CPCAPI2::SipConversation::_enum_name::_enum_item_from; \
                   }
#define ENUM_SWITCH_CASE(_enum_name, _enum_item) ENUM_SWITCH_CASE_LONG(_enum_name, _enum_item, _enum_item)

namespace CPCAPI2
{

namespace SipConversation
{

bool MyAppDialogFactoryDelegate::isMyMessage(const resip::SipMessage& msg)
{
   if (msg.method() == resip::SUBSCRIBE)
   {
      return false;
   }
   return true;
}

resip::AppDialogSet* MyAppDialogFactoryDelegate::createAppDialogSet(resip::DialogUsageManager& dum, const resip::SipMessage& msg)
{
   switch(msg.method())
   {
   case resip::INVITE:
      return new recon::RemoteParticipantDialogSet(*(mImpl->mReconConvMan));
      break;
   default:
      return new recon::DefaultDialogSet(*(mImpl->mReconConvMan));
      break;
   }
}

SipAVConversationManagerImpl::SipAVConversationManagerImpl(SipAVConversationManagerInterface* iff,
                                                           std::shared_ptr<AccountMap> parentMap,
                                                           SipAccountImpl& account,
                                                           std::shared_ptr<webrtc_recon::MediaStackImpl> ms,
                                                           LocalLogger* localLogger)
   : mInterface(iff),
     mParentMap(parentMap),
     mReconUA(new ReconUserAgent(ms)),
     mReconConvMan(NULL),
     mAccount(account),
     mDtmfModePreferentialOrder(( int ) DtmfMode_Everything ),
     mAdornmentHandler(NULL),
     mCallsInProgress(0),
     mLocalLogger(localLogger),
     mDtlsSupported(true),
     mIgnoreBindingFilter(false)
{
   mAccount.registerAccountAwareFeature(this);
   mDtmfModePreferentialOrder[ 0 ] = DtmfMode_RFC2833_InBand;
}

SipAVConversationManagerImpl::~SipAVConversationManagerImpl()
{
   if (mCallsInProgress != 0)
   {
      ErrLog(<< "SipAVConversationManagerImpl::~SipAVConversationManagerImpl ending with non-zero call count, mCallsInProgress=" << mCallsInProgress);
   }
   assert(mCallsInProgress == 0);

   delete mReconUA;

   if (mReconConvMan != NULL)
   {
      mReconConvMan->shutdown();
      delete mReconConvMan;
   }

   mAccount.unregisterAccountAwareFeature(this);
}

void SipAVConversationManagerImpl::release()
{
   if (std::shared_ptr<AccountMap> parentMap = mParentMap.lock())
   {
      parentMap->erase(mAccount.getHandle());
   }
   delete this;
}

void SipAVConversationManagerImpl::setDefaultSettings(const SipConversationSettings& settings, CPCAPI2::NetworkTransport transport)
{
   InfoLog(<< "SipAVConversationManagerImpl::setDefaultSettings (account handle=" << mAccount.getHandle() << "): " << std::endl
      << "                      holdMode: " << settings.holdMode << std::endl
      << "    includeAttribsForStaticPLs: " << settings.includeAttribsForStaticPLs << std::endl
      << "      includePAssertedIdentity: " << settings.includePAssertedIdentity << std::endl
      << "     includePPreferredIdentity: " << settings.includePPreferredIdentity << std::endl
      << "                    maxRtpPort: " << settings.maxRtpPort << std::endl
      << "                    minRtpPort: " << settings.minRtpPort << std::endl   
      << "               maxRtpPortAudio: " << settings.maxRtpPortAudio << std::endl
      << "               minRtpPortAudio: " << settings.minRtpPortAudio << std::endl
      << "               maxRtpPortVideo: " << settings.maxRtpPortVideo << std::endl
      << "               minRtpPortVideo: " << settings.minRtpPortVideo << std::endl
      << "              natTraversalMode: " << settings.natTraversalMode << std::endl
      << "            natTraversalServer: " << settings.natTraversalServer << std::endl   
      << "      natTraversalServerSource: " << settings.natTraversalServerSource << std::endl
      << "        natTraversalServerType: " << settings.natTraversalServerType << std::endl
      << "                     prackMode: " << settings.prackMode << std::endl
      << "               answerMode.mode: " << settings.answerMode.mode << std::endl
      << "           answerMode.required: " << settings.answerMode.required << std::endl
      << "         answerMode.privileged: " << settings.answerMode.privileged << std::endl
      << "          answerMode.challenge: " << settings.answerMode.challenge << std::endl
      << "          answerMode.allowAuto: " << settings.answerMode.allowAuto << std::endl
      << "        answerMode.allowManual: " << settings.answerMode.allowManual << std::endl
      << "    answerMode.allowprivileged: " << settings.answerMode.allowPrivileged << std::endl
      << "                   sessionName: " << settings.sessionName << std::endl
      << "                  turnPassword: " << settings.turnPassword << std::endl
      << "                  turnUsername: " << settings.turnUsername << std::endl
      << "     networkChangeHandoverMode: " << settings.networkChangeHandoverMode << std::endl
      << " networkChangeHandoverStarcode: " << settings.networkChangeHandoverStarcode << std::endl
      << "              NetworkTransport: " << transport << std::endl
      << "                 mReconConvMan: " << mReconConvMan << std::endl
      << "                      mReconUA: " << mReconUA);

   mConvSettings[transport] = settings;
   if (mReconConvMan)
   {
      mReconConvMan->setReconConvManSettings(settings, transport);
      mReconConvMan->updateDefaultConversationProfile();
   }

   mReconUA->setPort(settings.minRtpPort, settings.maxRtpPort, sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_NONE);
   mReconUA->setPort(settings.minRtpPort, settings.maxRtpPort, sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_UNKNOWN);
   mReconUA->setPort(settings.minRtpPortAudio, settings.maxRtpPortAudio, sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_AUDIO);
   mReconUA->setPort(settings.minRtpPortVideo, settings.maxRtpPortVideo, sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_VIDEO);
   mReconUA->setPort(settings.minRtpPort, settings.maxRtpPort, sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_TEXT);
   mReconUA->setPort(settings.minRtpPort, settings.maxRtpPort, sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_APPLICATION);
   mReconUA->setPort(settings.minRtpPort, settings.maxRtpPort, sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_MESSAGE);
}

bool SipAVConversationManagerImpl::getDefaultSettings(CPCAPI2::NetworkTransport transport, SipConversationSettings& settings)
{
   std::map<NetworkTransport, SipConversationSettings>::iterator i = mConvSettings.find(transport);
   if (i != mConvSettings.end())
   {
      settings = i->second;
      return true;
   }
   return false;
}

//
// IAccountAware
//
int SipAVConversationManagerImpl::adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile)
{
   if (!profile->isMethodSupported(resip::INVITE)) profile->addSupportedMethod(resip::INVITE);
   if (!profile->isMethodSupported(resip::ACK)) profile->addSupportedMethod(resip::ACK);
   if (!profile->isMethodSupported(resip::CANCEL)) profile->addSupportedMethod(resip::CANCEL);
   if (!profile->isMethodSupported(resip::BYE)) profile->addSupportedMethod(resip::BYE);
   if (!profile->isMethodSupported(resip::REFER)) profile->addSupportedMethod(resip::REFER);
   if (!profile->isMethodSupported(resip::INFO)) profile->addSupportedMethod(resip::INFO);
   if (!profile->isMethodSupported(resip::NOTIFY)) profile->addSupportedMethod(resip::NOTIFY);
   if (!profile->isMethodSupported(resip::OPTIONS)) profile->addSupportedMethod(resip::OPTIONS);
   
   resip::Mime optionsMime("application", "sdp");
   if (!profile->isMimeTypeSupported(resip::OPTIONS, optionsMime)) profile->addSupportedMimeType(resip::OPTIONS, optionsMime);

   const SipConversationSettings& convSettings = mConvSettings[TransportNone];
   assert(convSettings.prackMode >= 0 && convSettings.prackMode <= 3);

   if (convSettings.prackMode > 0)
   {
      if (!profile->isMethodSupported(resip::UPDATE))
      {
         profile->addSupportedMethod(resip::UPDATE); // only if UAC PRACK mode enabled
      }
      if (!profile->isMethodSupported(resip::PRACK))
      {
         profile->addSupportedMethod(resip::PRACK);
      }
   }

   switch (convSettings.prackMode)
   {
   case PrackMode_Disabled:
      {
         profile->setUacReliableProvisionalMode(resip::MasterProfile::Never);
         profile->setUasReliableProvisionalMode(resip::MasterProfile::Never);
      }
      break;
   case PrackMode_Required:
      {
         profile->setUacReliableProvisionalMode(resip::MasterProfile::Required);
         profile->setUasReliableProvisionalMode(resip::MasterProfile::Never);
      }
      break;
   case PrackMode_Supported:
      {
         profile->setUacReliableProvisionalMode(resip::MasterProfile::Supported);
         profile->setUasReliableProvisionalMode(resip::MasterProfile::Never);
      }
      break;
   case PrackMode_SupportUasAndUac:
      {
         profile->setUacReliableProvisionalMode(resip::MasterProfile::Supported);
         profile->setUasReliableProvisionalMode(resip::MasterProfile::Supported);
      }
      break;
   default:
      break;
   }

   return kSuccess;
}
  
int SipAVConversationManagerImpl::registerSdkDialogSetFactory( CPCAPI2::SipAccount::AppDialogSetFactory& factory )
{
   // this part runs before addHandlers
   std::shared_ptr<MyAppDialogFactoryDelegate> pTemp(new MyAppDialogFactoryDelegate(this));
   factory.addDelegate(pTemp, true);
   return kSuccess;
}

int SipAVConversationManagerImpl::addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig)
{
   // the recon ConversationManager will get its copy of DUM from the UserAgent
   mReconUA->setDUM(dum);

   // this installs all of the handlers that recon needs
   mReconConvMan = new ReconConversationManagerImpl(*mReconUA, mAccount, *this, mConvSettings, overrideSourceIpSignalling.c_str(), overrideSourceIpTransport.c_str(), tscconfig, mDtlsSupported);
   mReconConvMan->addAdornmentHandler(mAdornmentHandler);

   // Set the default invite handler
   mAccount.setDefaultInviteHandler(mReconConvMan);

   return kSuccess;
}

// this is called BEFORE dum is shutdown/deleted
int SipAVConversationManagerImpl::onDumBeingDestroyed()
{
   if (mReconConvMan != NULL)
   {
      mReconConvMan->addAdornmentHandler(NULL);
      mReconConvMan->destroyAllConversations();
   }

   // important: we must not hold any references to DUM, since the SipStack
   // is about to get deleted
   mReconUA->setDUM(resip::SharedPtr<resip::DialogUsageManager>());

   return 0;
}

int SipAVConversationManagerImpl::afterDumDestroyed()
{
   if (mReconConvMan != NULL)
   {
      mReconConvMan->shutdown();
      delete mReconConvMan;
      mReconConvMan = NULL;
   }
   return 0;
}

int SipAVConversationManagerImpl::onRegistrationSuccess(const SipRegistrationSuccessEvent& args)
{
   DebugLog(<< "SipAVConversationManagerImpl::onRegistrationSuccess - updateConversationProfileLocalIp for best route to: " << args.server);
   if (mReconConvMan != NULL)
   {
      bool bindingUpdated = mReconConvMan->hasLocalBindingUpdated(args.localContact);
      bool bindingPopulated = (args.localContact.uri().getAor().empty() == false);
      bool starcodeEnabled = (mReconConvMan->getConvSettings().networkChangeHandoverMode == SipConversation::NetworkChangeHandoverMode_Starcode);

      InfoLog(<< "onRegistrationSuccess - updateConversationProfileLocalIp for best route to server: " << args.server
         << " localContact: " << args.localContact << " overrideSourceIpForNAT64: " << args.overrideSourceIpSignalling
         << " binding-populated: " << bindingPopulated << " binding-updated: " << bindingUpdated << " ignore-binding: " << mIgnoreBindingFilter);

      // Ensure that the local interface override is reset as the registration success may have been triggered due to a network change
      mReconConvMan->resetLocalInterfaceOverride();
      mReconConvMan->overrideSourceIpSignalling() = args.overrideSourceIpSignalling;
      mReconConvMan->updateDefaultConversationProfile(&args.server);

      // Contact host-port maybe empty when no registrar is involved, will get populated in transport layer
      mReconConvMan->updateLocalInterfaceOverride(&args.server);
      mReconConvMan->updateLocalContact(args.localContact);

      // If the contact-binding is empty as would be the case in no-registrar or tunnel configurations, then starcode handling is not required.
      // See header comment of SipConversationManagerInternal::ignoreBindingFilterForStarcodeHandover(..) for usage of mIgnoreBindingFilter
      if (starcodeEnabled && ((bindingPopulated && bindingUpdated) || mIgnoreBindingFilter))
      {
         mReconConvMan->startStarcodeHandovers();
      }
      else
      {
         mReconConvMan->updateMediaConnections();
      }
   }

   return kSuccess;
}

void SipAVConversationManagerImpl::ignoreBindingFilterForStarcodeHandover()
{
   mIgnoreBindingFilter = true;
}

bool SipAVConversationManagerImpl::canDisable(const SipAccount::AccountDisableCondition& cond)
{
   if (mCallsInProgress)
   {
      DebugLog(<< "SipAVConversationManagerImpl::canDisable requesting deferred disable, mCallsInProgress=" << mCallsInProgress);
      mAccountDisableCondition = cond;
      return false;
   }
   return true;
}

int SipAVConversationManagerImpl::onRedirectReceived(const resip::SipMessage& originalRequest, const resip::SipMessage& response, resip::AppDialogSetHandle h)
{
   InfoLog(<< "onRedirectReceived(AppDialogSetHandle): " << response.brief());
   recon::RemoteParticipantDialogSet *remoteParticipantDialogSet = dynamic_cast<recon::RemoteParticipantDialogSet *>(h.get());
   if (remoteParticipantDialogSet)
   {
      if (remoteParticipantDialogSet->getNumDialogs() == 0)
      {
         remoteParticipantDialogSet->onRedirectReceived(h, response);
      }
   }

   return kSuccess;
}

bool SipAVConversationManagerImpl::onRedirectTryingNextTarget(const resip::Uri& target, resip::SipMessage& request, resip::AppDialogSetHandle h)
{
   InfoLog(<< "onTryingNextTarget(AppDialogSetHandle): " << request.brief());
   // Always allow redirection for now
   return true;
}

void SipAVConversationManagerImpl::countNewCall()
{
   mCallsInProgress++;
   mAccount.newCallActive();
}

void SipAVConversationManagerImpl::countEndCall()
{
   mCallsInProgress--;

   if (mCallsInProgress < 1)
   {
      mAccountDisableCondition.reset();

      if (mCallsInProgress < 0) // Sanity check, should never happen
      {
         ErrLog(<< "SipAVConversationManagerImpl::countEndCall counting below zero");
         mCallsInProgress = 0;
         assert(0);
      }
      
      mAccount.allCallsEnded();
   }
}

int SipAVConversationManagerImpl::getCallCount()
{
   return mCallsInProgress;
}

SipCallCreationInfo* SipAVConversationManagerImpl::getCreationInfo(const SipConversationHandle& h) const
{
   return (mReconConvMan != NULL ? mReconConvMan->getCreationInfo(h) : NULL);
}

void SipAVConversationManagerImpl::addCreationInfo(const SipConversationHandle& h, SipCallCreationInfo* ci)
{
   if (mReconConvMan != NULL)
   {
      mReconConvMan->addCreationInfo(h, ci);
   }
}

void SipAVConversationManagerImpl::removeCreationInfo(const SipConversationHandle& h)
{
   if (mReconConvMan != NULL)
   {
      mReconConvMan->removeCreationInfo(h);
   }
}

void SipAVConversationManagerImpl::fireError(const SipConversationHandle& h, const cpc::string& errorText)
{
   ErrorEvent event;
   event.errorText = errorText;
   mInterface->fireEvent(cpcEvent(SipConversationHandler, onError), mAccount.getHandle(), h, event);
}

void SipAVConversationManagerImpl::fireCallInitiated(const SipConversationHandle& h, const ConversationInitiatedEvent& args)
{
   mInterface->fireEvent(cpcEvent(SipConversationHandlerInternal, onConversationInitiated), mAccount.getHandle(), h, args);
}

void SipAVConversationManagerImpl::fireCallsEndedEventFromStarcodeNetworkChange(const SipConversationHandle& h, const ConversationEndedEventFromStarcodeNetworkChange& args)
{
   mInterface->fireEvent(cpcEvent(SipConversationHandlerInternal, onConversationEndedFromStarcodeNetworkChange), mAccount.getHandle(), h, args);
}

static jsonrpc::CPCAPI2::SipConversation::ConversationState convert(ConversationState convState)
{
   switch (convState)
   {
      case SipConversation::ConversationState_None:
         return jsonrpc::CPCAPI2::SipConversation::ConversationState::None;
      case ConversationState_LocalOriginated:
         return jsonrpc::CPCAPI2::SipConversation::ConversationState::LocalOriginated;
      case ConversationState_RemoteOriginated:
         return jsonrpc::CPCAPI2::SipConversation::ConversationState::RemoteOriginated;
      case ConversationState_RemoteRinging:
         return jsonrpc::CPCAPI2::SipConversation::ConversationState::RemoteRinging;
      case ConversationState_LocalRinging:
         return jsonrpc::CPCAPI2::SipConversation::ConversationState::LocalRinging;
      case ConversationState_Connected:
         return jsonrpc::CPCAPI2::SipConversation::ConversationState::Connected;
      case ConversationState_Early:
         return jsonrpc::CPCAPI2::SipConversation::ConversationState::Early;
      case ConversationState_Ended:
         return jsonrpc::CPCAPI2::SipConversation::ConversationState::Ended;
      default:
         ErrLog(<< "Enum mismatch");
         return jsonrpc::CPCAPI2::SipConversation::ConversationState::None;
   }
}

static jsonrpc::CPCAPI2::SipConversation::ConversationType convert(ConversationType convState)
{
   switch (convState)
   {
      case SipConversation::ConversationType_Incoming:
         return jsonrpc::CPCAPI2::SipConversation::ConversationType::Incoming;
      case ConversationType_Outgoing:
         return jsonrpc::CPCAPI2::SipConversation::ConversationType::Outgoing;
      case ConversationType_IncomingJoinRequest:
         return jsonrpc::CPCAPI2::SipConversation::ConversationType::IncomingJoinRequest;
      case ConversationType_IncomingTransferRequest:
         return jsonrpc::CPCAPI2::SipConversation::ConversationType::IncomingTransferRequest;
      case ConversationType_OutgoingNetworkChangeHandover:
         return jsonrpc::CPCAPI2::SipConversation::ConversationType::OutgoingNetworkChangeHandover;
      
      default:
         ErrLog(<< "Enum mismatch");
         return jsonrpc::CPCAPI2::SipConversation::ConversationType::Incoming;
   }
}

static jsonrpc::CPCAPI2::SipConversation::ConversationEndReason convert(ConversationEndReason endReason)
{
   switch (endReason)
   {
      case SipConversation::ConversationEndReason_Unknown:
         return jsonrpc::CPCAPI2::SipConversation::ConversationEndReason::Unknown;
      case ConversationEndReason_UserTerminatedLocally:
         return jsonrpc::CPCAPI2::SipConversation::ConversationEndReason::UserTerminatedLocally;
      case ConversationEndReason_UserTerminatedRemotely:
         return jsonrpc::CPCAPI2::SipConversation::ConversationEndReason::UserTerminatedRemotely;
      case ConversationEndReason_ServerError:
         return jsonrpc::CPCAPI2::SipConversation::ConversationEndReason::ServerError;
      case ConversationEndReason_ServerRejected:
         return jsonrpc::CPCAPI2::SipConversation::ConversationEndReason::ServerRejected;
      case ConversationEndReason_Redirected:
         return jsonrpc::CPCAPI2::SipConversation::ConversationEndReason::Redirected;
      case ConversationEndReason_CallAnsweredElsewhere:
         return jsonrpc::CPCAPI2::SipConversation::ConversationEndReason::CallAnsweredElsewhere;
      
      default:
         ErrLog(<< "Enum mismatch");
         return jsonrpc::CPCAPI2::SipConversation::ConversationEndReason::Unknown;
   }
}



void SipAVConversationManagerImpl::fireConvStateChangedEvent(SipConversationHandle convHandle, const ConversationStateChangedEvent& args)
{
   fireEvent(cpcEvent(SipConversationHandler, onConversationStateChanged), convHandle, args);

   jsonrpc::CPCAPI2::SipConversation::SipConversationConversationStateChangedEvent jrpcEvt;
   jrpcEvt.conversationHandle = convHandle;
   jrpcEvt.conversationState = convert(args.conversationState);
   jrpcEvt.remoteAddress = args.remoteAddress;
   jrpcEvt.remoteDisplayName = args.remoteDisplayName;
   jrpcEvt.statusText = args.statusText;

   mAccount.getPhone()->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::SipConversation::SipConversationEvents::SipConversationDotOnConversationStateChanged), 
                                                  std::move(jrpcEvt.marshal())));
}

void SipAVConversationManagerImpl::fireNewConvEvent(SipConversationHandle convHandle, const NewConversationEvent& args)
{
   fireEvent(cpcEvent(SipConversationHandler, onNewConversation), convHandle, args);

   jsonrpc::CPCAPI2::SipConversation::SipConversationNewConversationEvent jrpcEvt;
   jrpcEvt.conversationHandle = convHandle;
   jrpcEvt.conversationState = convert(args.conversationState);
   jrpcEvt.conversationType = convert(args.conversationType);
   jrpcEvt.localAddress = args.localAddress;
   jrpcEvt.localDisplayName = args.localDisplayName;
   jrpcEvt.remoteAddress = args.remoteAddress;
   jrpcEvt.remoteDisplayName = args.remoteDisplayName;
   jrpcEvt.relatedConversation = args.relatedConversation;
   jrpcEvt.conversationToReplace = args.conversationToReplace;
   jrpcEvt.conversationToJoin = args.conversationToJoin;
   jrpcEvt.accountHandle = args.account;
   jrpcEvt.isAudioCodecsMismatched = args.isAudioCodecsMismatched;
   jrpcEvt.isVideoCodecsMismatched = args.isVideoCodecsMismatched;

   mAccount.getPhone()->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::SipConversation::SipConversationEvents::SipConversationDotOnNewConversation), 
                                                  std::move(jrpcEvt.marshal())));
}

void SipAVConversationManagerImpl::fireConvEndedEvent(SipConversationHandle convHandle, const ConversationEndedEvent& args)
{
   fireEvent(cpcEvent(SipConversationHandler, onConversationEnded), convHandle, args);

   jsonrpc::CPCAPI2::SipConversation::SipConversationConversationEndedEvent jrpcEvt;
   jrpcEvt.conversationHandle = convHandle;
   jrpcEvt.conversationState = convert(args.conversationState);
   jrpcEvt.endReason = convert(args.endReason);
   jrpcEvt.sipResponseCode = args.sipResponseCode;

   mAccount.getPhone()->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::SipConversation::SipConversationEvents::SipConversationDotOnConversationEnded), 
                                                  std::move(jrpcEvt.marshal())));
}


void SipAVConversationManagerImpl::fireIncomingTransferRequest(SipConversationHandle convHandle, const TransferRequestEvent& args)
{
   fireEvent(cpcEvent(SipConversationHandler, onIncomingTransferRequest), convHandle, args);

   jsonrpc::CPCAPI2::SipConversation::SipConversationTransferRequestEvent jrpcEvt;

   jrpcEvt.conversationHandle = convHandle;
   jrpcEvt.transferTargetAddress = args.transferTargetAddress;
   jrpcEvt.transferTargetDisplayName = args.transferTargetDisplayName;
   jrpcEvt.transferTargetConversation = args.transferTargetConversation;

   mAccount.getPhone()->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::SipConversation::SipConversationEvents::SipConversationDotOnIncomingTransferRequest), 
                                                std::move(jrpcEvt.marshal())));
}


jsonrpc::CPCAPI2::SipConversation::TransferProgressEventType convert(const TransferProgressEventType& item) {
   switch (item)
    {
      ENUM_SWITCH_CASE(TransferProgressEventType, Trying);
      ENUM_SWITCH_CASE(TransferProgressEventType, Ringing);
      ENUM_SWITCH_CASE(TransferProgressEventType, Connected);
      ENUM_SWITCH_CASE(TransferProgressEventType, Redirected);
      ENUM_SWITCH_CASE(TransferProgressEventType, Failed);
    }
}

void SipAVConversationManagerImpl::fireTransferProgress(SipConversationHandle convHandle, const TransferProgressEvent& args)
{
   fireEvent(cpcEvent(SipConversationHandler, onTransferProgress), convHandle, args);

   jsonrpc::CPCAPI2::SipConversation::SipConversationTransferProgressEvent jrpcEvt;

   jrpcEvt.conversationHandle = convHandle;
   jrpcEvt.progressEventType = convert(args.progressEventType);
   jrpcEvt.sipResponseCode = args.sipResponseCode;

   mAccount.getPhone()->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::SipConversation::SipConversationEvents::SipConversationDotOnTransferProgress), 
                                                  std::move(jrpcEvt.marshal())));
}

void SipAVConversationManagerImpl::fireTransferResponse(SipConversationHandle convHandle, const TransferResponseEvent& args)
{
   fireEvent(cpcEvent(SipConversationHandler, onTransferResponse), convHandle, args);

   jsonrpc::CPCAPI2::SipConversation::SipConversationTransferResponseEvent jrpcEvt;

   jrpcEvt.conversationHandle = convHandle;
   jrpcEvt.sipResponseCode = args.sipResponseCode;
   jrpcEvt.warningHeader = args.warningHeader;

   mAccount.getPhone()->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::SipConversation::SipConversationEvents::SipConversationDotOnTransferResponse), 
                                                std::move(jrpcEvt.marshal())));
}

void SipAVConversationManagerImpl::fireConversationStateChangeRequest(SipConversationHandle convHandle, const ConversationStateChangeRequestEvent& args)
{
   fireEvent(cpcEvent(SipConversationHandler, onConversationStateChangeRequest), convHandle, args);

   jsonrpc::CPCAPI2::SipConversation::SipConversationConversationStateChangeRequestEvent jrpcEvt;

   jrpcEvt.conversationHandle = convHandle;

   mAccount.getPhone()->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::SipConversation::SipConversationEvents::SipConversationDotOnConversationStateChangeRequest), 
                                                std::move(jrpcEvt.marshal())));
}


jsonrpc::CPCAPI2::SipConversation::MediaType convert(MediaType v)
{
    switch (v)
    {
        ENUM_SWITCH_CASE(MediaType, Audio);
        ENUM_SWITCH_CASE(MediaType, Video);
    }
}

jsonrpc::CPCAPI2::SipConversation::MediaEncryptionMode convert(const MediaEncryptionMode v)
{
   switch (v)
    {
      ENUM_SWITCH_CASE(MediaEncryptionMode, Unencrypted);
      ENUM_SWITCH_CASE_LONG(MediaEncryptionMode, SrtpSdesEncrypted, SRTP_SDES_Encrypted);
      ENUM_SWITCH_CASE_LONG(MediaEncryptionMode, SrtpDtlsEncrypted, SRTP_DTLS_Encrypted);
    }
}

jsonrpc::CPCAPI2::SipConversation::MediaCryptoSuite convert(const MediaCryptoSuite v)
{
   switch (v)
    {
      ENUM_SWITCH_CASE(MediaCryptoSuite, None);
      ENUM_SWITCH_CASE_LONG(MediaCryptoSuite, AesCm_128HmacSha1_32, AES_CM_128_HMAC_SHA1_32);
      ENUM_SWITCH_CASE_LONG(MediaCryptoSuite, AesCm_128HmacSha1_80, AES_CM_128_HMAC_SHA1_80);
      ENUM_SWITCH_CASE_LONG(MediaCryptoSuite, AesCm_256HmacSha1_32, AES_CM_256_HMAC_SHA1_32);
      ENUM_SWITCH_CASE_LONG(MediaCryptoSuite, AesCm_256HmacSha1_80, AES_CM_256_HMAC_SHA1_80);
      ENUM_SWITCH_CASE_LONG(MediaCryptoSuite, AesCm_192HmacSha1_32, AES_CM_192_HMAC_SHA1_32);
      ENUM_SWITCH_CASE_LONG(MediaCryptoSuite, AesCm_192HmacSha1_80, AES_CM_192_HMAC_SHA1_80);
      ENUM_SWITCH_CASE_LONG(MediaCryptoSuite, AeadAes_128Gcm, AEAD_AES_128_GCM);
      ENUM_SWITCH_CASE_LONG(MediaCryptoSuite, AeadAes_256Gcm, AEAD_AES_256_GCM);
    }
}

jsonrpc::CPCAPI2::SipConversation::MediaDirection convert(const MediaDirection v)
{
    switch (v)
    {
        ENUM_SWITCH_CASE(MediaDirection, None);
        ENUM_SWITCH_CASE(MediaDirection, SendReceive);
        ENUM_SWITCH_CASE(MediaDirection, SendOnly);
        ENUM_SWITCH_CASE(MediaDirection, ReceiveOnly);
        ENUM_SWITCH_CASE(MediaDirection, Inactive);
    }
}

cpc::vector<jsonrpc::CPCAPI2::SipConversation::MediaCryptoSuite> convert(const cpc::vector<MediaCryptoSuite>& item)
{
   cpc::vector<jsonrpc::CPCAPI2::SipConversation::MediaCryptoSuite> result(item.size());
   for (size_t ind = 0; ind < item.size(); ++ind) {
      result[ind] = convert(item[ind]);
   }
   return result;
}

jsonrpc::CPCAPI2::SipConversation::MediaEncryptionOptions convert(const MediaEncryptionOptions item)
{
   jsonrpc::CPCAPI2::SipConversation::MediaEncryptionOptions result;
   result.mediaEncryptionMode = convert(item.mediaEncryptionMode);
   result.secureMediaRequired = item.secureMediaRequired;
   result.mediaCryptoSuites = convert(item.mediaCryptoSuites);
   return result;
}

jsonrpc::CPCAPI2::SipConversation::AudioCodec convert(const AudioCodec& item) {
   jsonrpc::CPCAPI2::SipConversation::AudioCodec result;
   result.pltype = item.pltype;
   result.plname = item.plname;
   result.plfreq = item.plfreq;
   result.pacsize = item.pacsize;
   result.channels = item.channels;
   result.rate = item.rate;
   result.priority = item.priority;
   result.displayName = item.displayName;
   return result;
}

jsonrpc::CPCAPI2::SipConversation::VideoCodec convert(const VideoCodec& item) {
   jsonrpc::CPCAPI2::SipConversation::VideoCodec result;
   result.plName = item.plName;
   result.plType = item.plType;
   result.width = item.width;
   result.height = item.height;
   result.startBitrate = item.startBitrate;
   result.maxBitrate = item.maxBitrate;
   result.minBitrate = item.minBitrate;
   result.maxFramerate = item.maxFramerate;
   result.hadwareAccelerated = item.hadwareAccelerated;
   result.priority = item.priority;
   result.displayName = item.displayName;
   return result;
}

jsonrpc::CPCAPI2::SipConversation::MediaInfo convert(const MediaInfo& item) {
   jsonrpc::CPCAPI2::SipConversation::MediaInfo result;
   result.mediaType = convert(item.mediaType);
   result.mediaDirection = convert(item.mediaDirection);
   result.mediaCrypto = convert(item.mediaCrypto);
   result.mediaEncryptionOptions = convert(item.mediaEncryptionOptions);
   result.audioCodec = convert(item.audioCodec);
   result.videoCodec = convert(item.videoCodec);
   result.conferenceMixContribution = item.conferenceMixContribution;
   result.isLocallyDisabled = item.isLocallyDisabled;
   result.conferenceMixId = item.conferenceMixId;
   result.mediaStreamId = item.mediaStreamId;
   result.videoCaptureDeviceId = item.videoCaptureDeviceId;
   return result;
}

cpc::vector<jsonrpc::CPCAPI2::SipConversation::MediaInfo> convert(const cpc::vector<MediaInfo>& vec) {
   cpc::vector<jsonrpc::CPCAPI2::SipConversation::MediaInfo> result(vec.size());
   for (size_t ind = 0; ind < vec.size(); ++ind) {
      result[ind] = convert(vec[ind]);
   }
   return result;
}

void SipAVConversationManagerImpl::fireConversationMediaChangeRequest(SipConversationHandle convHandle, const ConversationMediaChangeRequestEvent& args)
{
   fireEvent(cpcEvent(SipConversationHandler, onConversationMediaChangeRequest), convHandle, args);

   jsonrpc::CPCAPI2::SipConversation::SipConversationConversationMediaChangeRequestEvent jrpcEvt;
   jrpcEvt.conversationHandle = convHandle;
   jrpcEvt.remoteMediaInfo = convert(args.remoteMediaInfo);

   mAccount.getPhone()->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::SipConversation::SipConversationEvents::SipConversationDotOnConversationMediaChangeRequest), 
                                                  std::move(jrpcEvt.marshal())));
}

void SipAVConversationManagerImpl::fireConversationMediaChanged(SipConversationHandle convHandle, const ConversationMediaChangedEvent& args)
{
   fireEvent(cpcEvent(SipConversationHandler, onConversationMediaChanged), convHandle, args);

   jsonrpc::CPCAPI2::SipConversation::SipConversationConversationMediaChangedEvent jrpcEvt;
   jrpcEvt.conversationHandle = convHandle;
   jrpcEvt.localMediaInfo = convert(args.localMediaInfo);
   jrpcEvt.remoteMediaInfo = convert(args.remoteMediaInfo);
   jrpcEvt.localHold = args.localHold;
   jrpcEvt.remoteHold = args.remoteHold;

   mAccount.getPhone()->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::SipConversation::SipConversationEvents::SipConversationDotOnConversationMediaChanged), 
                                                  std::move(jrpcEvt.marshal())));
}

void SipAVConversationManagerImpl::fireError(SipConversationHandle convHandle, const ErrorEvent& args)
{
   // fireEvent(cpcEvent(SipConversationHandler, onConversationMediaChangeRequest), convHandle, args);
   fireEvent(cpcEvent(SipConversationHandler, onError), convHandle, args);

   jsonrpc::CPCAPI2::SipConversation::SipConversationErrorEvent jrpcEvt;
   jrpcEvt.conversationHandle = convHandle;
   jrpcEvt.errorText = args.errorText;

   mAccount.getPhone()->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::SipConversation::SipConversationEvents::SipConversationDotOnError), 
                                                  std::move(jrpcEvt.marshal())));
}

void SipAVConversationManagerImpl::setAdornmentHandler(SipConversationAdornmentInternalHandler* handler)
{
   mAdornmentHandler = handler;
}

void SipAVConversationManagerImpl::setDtlsSupported(bool dtlsSupported)
{
   mDtlsSupported = dtlsSupported;
}

void SipAVConversationManagerImpl::setAnswerMode(CPCAPI2::SipConversation::AnswerModeSettings answerMode, CPCAPI2::NetworkTransport transport)
{
   std::map<NetworkTransport, SipConversationSettings>::iterator i = mConvSettings.find(transport);
   if (i == mConvSettings.end())
   {
      DebugLog(<< "SipAVConversationManagerImpl::setAnswerMode(): no conversation settings found for transport: " << transport);
      return;
   }

   SipConversationSettings& convSettings = mConvSettings[transport];
   convSettings.answerMode = answerMode;
   setDefaultSettings(convSettings, transport);
}

void SipAVConversationManagerImpl::logEvent(const char* funcName, SipConversationHandle h, const std::string& argsStr)
{
   InfoLog(<< "SipConversationHandler event callback: " << funcName << "(" << h << ", args: " << argsStr << ")");
   mLocalLogger->logDebugMessage("SipConversationHandler event callback: {} ({}), args: {}", funcName, h, argsStr);
}

void SipAVConversationManagerImpl::setReconConvMan(ReconConversationManagerImpl* rcm)
{
   mReconConvMan = rcm;
}

webrtc_recon::MediaStackImpl* SipAVConversationManagerImpl::media_stack() const
{
   return dynamic_cast<webrtc_recon::MediaStackImpl*>(mReconUA->getMediaStack());
}

bool SipAVConversationManagerImpl::hasCallWithCallId(const cpc::string& callId) const
{
   return mReconConvMan->hasCallWithCallId(callId);
}
}

}

#endif // CPCAPI2_BRAND_CALL_MODULE
