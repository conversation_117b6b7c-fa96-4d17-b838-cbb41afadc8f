// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef Licensing_JSON_HELPER_H
#define Licensing_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/experimental/licensing/LicensingClientTypes.h>
#include <interface/experimental/licensing/LicensingClientHandler.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Licensing::LicenseInfo& v)
    {
      w.StartObject();
      Write(w, "key", v.key);
      Write(w, "type", v.type);
      Write(w, "features", v.features);
      Write(w, "expiry", v.expiry);
      Write(w, "gracePeriod", v.gracePeriod);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Licensing::LicenseInfo& v)
    {
      Read(json, "key", v.key);
      Read(json, "type", v.type);
      Read(json, "features", v.features);
      Read(json, "expiry", v.expiry);
      Read(json, "gracePeriod", v.gracePeriod);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Licensing::InvalidLicenseInfo& v)
    {
      w.StartObject();
      Write(w, "id", v.id);
      Write(w, "code", v.code);
      Write(w, "message", v.message);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Licensing::InvalidLicenseInfo& v)
    {
      Read(json, "id", v.id);
      Read(json, "code", v.code);
      Read(json, "message", v.message);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& v)
    {
      w.StartObject();
      Write(w, "timeLimitLicense", v.timeLimitLicense);
      Write(w, "timeLimitRemaining", v.timeLimitRemaining);
      Write(w, "licenseUrl", v.licenseUrl);
      Write(w, "hardwareId", v.hardwareId);
      Write(w, "licenses", v.licenses);
      Write(w, "invalidLicenses", v.invalidLicenses);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Licensing::ValidateLicensesSuccessEvent& v)
    {
      Read(json, "timeLimitLicense", v.timeLimitLicense);
      Read(json, "timeLimitRemaining", v.timeLimitRemaining);
      Read(json, "licenseUrl", v.licenseUrl);
      Read(json, "hardwareId", v.hardwareId);
      Read(json, "licenses", v.licenses);
      Read(json, "invalidLicenses", v.invalidLicenses);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& v)
    {
      w.StartObject();
      Write(w, "errorCode", v.errorCode);
      Write(w, "status", v.status);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Licensing::ValidateLicensesFailureEvent& v)
    {
      Read(json, "errorCode", v.errorCode);
      Read(json, "status", v.status);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Licensing::ErrorEvent& v)
    {
      w.StartObject();
      Write(w, "errorText", v.errorText);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Licensing::ErrorEvent& v)
    {
      Read(json, "errorText", v.errorText);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Licensing::LicensingClientHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Licensing::LicensingClientHandler& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // Licensing_JSON_HELPER_H