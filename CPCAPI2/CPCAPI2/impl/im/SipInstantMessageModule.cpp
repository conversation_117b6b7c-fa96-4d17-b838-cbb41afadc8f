#include "brand_branded.h"

#include "interface/public/im/SipInstantMessage.h"

#if (CPCAPI2_BRAND_IM_MODULE == 1)
#include "SipInstantMessageInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace SipInstantMessage
{
SipInstantMessageManager* SipInstantMessageManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_IM_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<SipInstantMessageInterface>(phone, "SipInstantMessageInterface");
#else
   return NULL;
#endif
}
}
}
