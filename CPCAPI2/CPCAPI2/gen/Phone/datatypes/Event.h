#include "jsonrpc/StringSerialization.h"
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_PHONE_EVENT_H
#define JSONRPC_CPCAPI2_PHONE_EVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  class Event
  {
  public:
    cpc::string eventType;
    cpc::string eventPayload;

    Event() = default;

    Event(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const Event& o)
    {
      j = nlohmann::json::object();
       j["eventType"] = o.eventType;
     j["eventPayload"] = o.eventPayload;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, Event& o)
    {
     o.eventType = j.at("eventType");
     o.eventPayload = j.at("eventPayload");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_EVENT_H
