#include "brand_branded.h"

#include "interface/experimental/licensing/LicensingClientManager.h"

#if (CPCAPI2_BRAND_LICENSING_MODULE == 1)
#include "LicensingClientManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace Licensing
{

LicensingClientManager* LicensingClientManager::getInterface(CPCAPI2::Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_LICENSING_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<LicensingClientManagerInterface>(phone, "LicensingClientManagerInterface");
#else
   return NULL;
#endif
}

}
}
