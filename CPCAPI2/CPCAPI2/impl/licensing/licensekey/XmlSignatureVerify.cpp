#include "XmlSignatureVerify.h"
#include "../../util/cpc_logger.h"
#include "../../util/LibxmlSharedUsage.h"

#include <memory>
#include <sstream>
#include <string>

#include <libxml/xmlmemory.h>
#include <libxml/parser.h>

#include <xmlsec/xmlsec.h>
#include <xmlsec/xmltree.h>
#include <xmlsec/xmldsig.h>
#include <xmlsec/crypto.h>
#include <xmlsec/errors.h>


#ifdef _WIN32
//for CryptoAPI
#include <windows.h>
#include <wincrypt.h>
#endif

#if defined(MAC) || defined(__linux__)
#include <stdexcept>
#endif

#if defined(__linux__)
#define stricmp strcasecmp
#endif

#ifdef _WIN32
#undef X509_NAME
#endif
#include <openssl/x509v3.h>

///
#include <openssl/aes.h>
#include <openssl/evp.h>
#include <stdio.h>
#include <openssl/sha.h>
#include <openssl/rand.h>
#include <string>
#include <assert.h>
#include <openssl/err.h>
///
#include <fstream>
#include <boost/scoped_array.hpp>

#define INITIALIZATION_VECTOR "ExternalBodyContents.hxx"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::LICENSING

namespace CPCAPI2
{
namespace Licensing
{
   /*
    * The other half of the encrypted root certificate
    */
   static unsigned char OtherHalf[] = {
      0x1f, 0x41, 0x35, 0x97, 0x68, 0xf2, 0x5b, 0x44, 0xb6, 0xe5, 0xe9, 0x23, 0x86, 0x68, 0x53, 0x58,
      0xb3, 0xac, 0xd0, 0x48, 0x75, 0x71, 0x2d, 0xe7, 0x85, 0xd2, 0x48, 0xc9, 0x4c, 0x0c, 0x42, 0x2d,
      0x7e, 0xc0, 0x1e, 0xbf, 0xf3, 0xe2, 0x42, 0x45, 0x44, 0x83, 0x5a, 0xde, 0xd2, 0x1b, 0x63, 0x25,
      0x4c, 0x33, 0xfc, 0x33, 0xf3, 0x07, 0x79, 0x93, 0x5d, 0xd6, 0x78, 0x59, 0xc9, 0xee, 0x0a, 0xb2,
      0xeb, 0xd7, 0xa5, 0x9c, 0x49, 0x56, 0x63, 0x32, 0x6c, 0x5a, 0x64, 0xfc, 0xd0, 0xe3, 0xbe, 0x3b,
      0x5f, 0xf1, 0x83, 0x00, 0x58, 0x3a, 0x4e, 0x1a, 0x53, 0x0b, 0x17, 0xd9, 0x10, 0x47, 0x7c, 0x1a,
      0x94, 0x5b, 0xc5, 0x8d, 0x60, 0xa1, 0x40, 0xb5, 0xbf, 0x7d, 0x1e, 0x21, 0x35, 0xbf, 0x1c, 0x8f,
      0x90, 0xeb, 0xde, 0xfc, 0x6c, 0xe9, 0x49, 0x87, 0xe9, 0xfa, 0x91, 0xbf, 0x66, 0x7d, 0x19, 0x71,
      0xb5, 0xff, 0xbc, 0x15, 0x8c, 0xf1, 0x81, 0x2b, 0xa3, 0xc2, 0x49, 0x35, 0xd5, 0xc7, 0x5c, 0x66,
      0x11, 0x6e, 0xb5, 0x79, 0x43, 0x06, 0x0b, 0xab, 0x1a, 0x97, 0x7a, 0xb3, 0x9d, 0x05, 0x28, 0xe3,
      0x50, 0xf7, 0x31, 0xfd, 0x18, 0x86, 0xd9, 0x81, 0x80, 0x11, 0x40, 0x08, 0xdd, 0xb3, 0x36, 0x21,
      0x9a, 0x68, 0x74, 0x98, 0x6a, 0x32, 0xca, 0x46, 0x93, 0xa1, 0x2f, 0xf0, 0xfd, 0xda, 0x5a, 0x6a,
      0x7a, 0x7e, 0xd6, 0x6f, 0xd6, 0x39, 0x5f, 0xbb, 0xae, 0x7d, 0x05, 0x4e, 0x68, 0xf7, 0x12, 0x70,
      0xfd, 0x70, 0xf1, 0xbb, 0xf2, 0x6d, 0xbc, 0xd6, 0x56, 0x8f, 0x2b, 0x68, 0x49, 0xd1, 0x43, 0xa1,
      0xcd, 0xdf, 0x6a, 0x5b, 0x8a, 0xec, 0xca, 0x93, 0xf5, 0x04, 0xae, 0x99, 0x32, 0x3d, 0x43, 0xa7,
      0xd4, 0xf8, 0x07, 0x08, 0x5d, 0x3b, 0x8d, 0x1d, 0x50, 0x33, 0x8f, 0x8d, 0x9f, 0x9b, 0xf5, 0xf3,
      0x36, 0x89, 0xc9, 0x79, 0xbc, 0x0f, 0x7b, 0x05, 0xee, 0x9f, 0xa4, 0x00, 0x95, 0xe6, 0x19, 0x14,
      0x63, 0x0b, 0x51, 0xad, 0xbb, 0x02, 0x40, 0xc8, 0x22, 0xdc, 0xe3, 0x25, 0x65, 0xeb, 0x89, 0x92,
      0xc9, 0x55, 0x3c, 0xe3, 0x9c, 0x17, 0x14, 0xe6, 0x5b, 0x71, 0x67, 0x08, 0x40, 0x55, 0x7e, 0x18,
      0xef, 0xb9, 0x42, 0x86, 0x46, 0xc5, 0x8a, 0xaa, 0x57, 0xa1, 0xbb, 0x3d, 0x28, 0xf9, 0x23, 0xe6,
      0x4d, 0x45, 0x43, 0xce, 0xb6, 0xbf, 0x3b, 0x0b, 0x6b, 0xe9, 0xb7, 0x07, 0x86, 0xaf, 0xe6, 0x75,
      0xad, 0x28, 0xad, 0x72, 0x43, 0x99, 0x69, 0xd5, 0xf1, 0x51, 0xc8, 0xaa, 0xce, 0xc8, 0x02, 0xe2,
      0x66, 0xd3, 0xa2, 0x5a, 0xc0, 0x3e, 0xdd, 0x6d, 0x64, 0xa4, 0x6b, 0x6b, 0x4b, 0xc0, 0xf5, 0xcb,
      0xc1, 0x47, 0x19, 0x9f, 0xd8, 0x27, 0xa8, 0x33, 0xdd, 0xce, 0x65, 0xf2, 0xcd, 0x82, 0x4a, 0xc7,
      0xb3, 0xaa, 0x20, 0xdc, 0x8d, 0x1d, 0xb9, 0x32, 0xf2, 0xcb, 0x4e, 0xc1, 0x28, 0x53, 0x5a, 0x6b,
      0xc9, 0xb1, 0x5e, 0xee, 0xd2, 0xf3, 0x21, 0x76, 0x39, 0xdb, 0xf1, 0x6b, 0x67, 0xf3, 0xdd, 0x09,
      0xf3, 0xbc, 0x7e, 0x99, 0x86, 0xfe, 0xf3, 0xc0, 0x24, 0xbc, 0xea, 0x79, 0x68, 0x21, 0x5a, 0xc7,
      0x05, 0x24, 0x27, 0x37, 0xc4, 0xa1, 0x90, 0xa9, 0xba, 0x85, 0x0a, 0x21, 0xc0, 0x37, 0x65, 0x85,
      0x8d, 0x47, 0xa7, 0x12, 0xc0, 0xcd, 0xb6, 0xc4, 0xc3, 0x99, 0xbe, 0x87, 0x36, 0x2e, 0x96, 0x02,
      0xc5, 0x6e, 0x44, 0x18, 0x7a, 0x8d, 0x92, 0x20, 0xa8, 0x41, 0xe0, 0x11, 0x1a, 0x19, 0xd5, 0xed,
      0xc5, 0x42, 0x2b, 0xbc, 0x03, 0x41, 0x80, 0x02, 0xfd, 0x40, 0x54, 0xc4, 0x68, 0x6d, 0x8c, 0x40,
      0xa9, 0x50, 0x47, 0x72, 0xbf, 0xfc, 0xb4, 0x09, 0x0f, 0x5f, 0x23, 0xcd, 0x38, 0x42, 0x43, 0xa5,
      0x3c, 0x0c, 0x74, 0x56, 0xc0, 0x0b, 0x93, 0x08, 0xf6, 0x68, 0xbe, 0x47, 0x55, 0xc4, 0xc7, 0x5a,
      0x75, 0xfd, 0x54, 0x8b, 0x18, 0xaa, 0x44, 0xe2, 0xcf, 0xcb, 0xb8, 0x87, 0xee, 0x3c, 0xd8, 0x5d,
      0xb5, 0x96, 0xba, 0x93, 0xf2, 0xd4, 0x42, 0xdf, 0x85, 0xcd, 0xd8, 0xa1, 0x57, 0x6d, 0x1b, 0x31,
      0x0d, 0x97, 0x7b, 0xff, 0xb2, 0xcb, 0x96, 0x36, 0x5e, 0x8f, 0x81, 0x73, 0xfa, 0xca, 0x79, 0x65,
      0xaa, 0x33, 0x34, 0xa6, 0x23, 0x10, 0x91, 0x00, 0x93, 0x22, 0x6e, 0xee, 0xd8, 0xc1, 0x02, 0xf1,
      0xd6, 0xfd, 0x58, 0x6e, 0x80, 0xb8, 0x30, 0x81, 0x5d, 0x70, 0xa9, 0xcc, 0x39, 0x46, 0x32, 0x81,
      0xbe, 0x59, 0x53, 0xb4, 0xee, 0xe9, 0x4d, 0xf6, 0xd1, 0x64, 0xae, 0xd9, 0x3f, 0x87, 0x03, 0xf7,
      0xfc, 0x49, 0x20, 0x4a, 0xae, 0x8a, 0x47, 0xfb, 0x1b, 0xf0, 0x8e, 0xee, 0x86, 0x28, 0xbb, 0xf6,
      0x2e, 0xa5, 0x1d, 0xfc, 0xe0, 0xd6, 0xff, 0xcf, 0xa9, 0x90, 0x26, 0x46, 0xb3, 0x71, 0xe0, 0x87,
      0xd1, 0xf2, 0xaf, 0xe8, 0xea, 0x05, 0x69, 0x00, 0x8c, 0xa4, 0x23, 0xbc, 0x75, 0x90, 0x5c, 0xbd,
      0x21, 0xf3, 0x4c, 0x45, 0xf5, 0x8c, 0x5f, 0xff, 0x57, 0x2c, 0x70, 0xfd, 0xae, 0x4f, 0xc6, 0xfd,
      0x40, 0x85, 0xb3, 0x19, 0x64, 0x87, 0x9b, 0xc6, 0x05, 0xb7, 0x62, 0x0e, 0x24, 0xb9, 0xbf, 0xcf,
      0x72, 0x5d, 0x4c, 0xa4, 0x62, 0x58, 0xac, 0xf8, 0x05, 0xb7, 0xa8, 0xf0, 0x10, 0x29, 0x9f, 0x3f,
      0x87, 0xb7, 0x4c, 0xff, 0x5b, 0x82, 0x86, 0x62, 0xff, 0xa4, 0x38, 0x79, 0xeb, 0x14, 0xbd, 0x16,
      0x1e, 0x03, 0x3f, 0xbe, 0x96, 0x89, 0x5f, 0xf0, 0x5a, 0x8d, 0x73, 0x60, 0x8d, 0xa4, 0xcb, 0x72,
      0x32, 0xf4, 0x2f, 0xe9, 0x10, 0x3d, 0x6d, 0xf2, 0xd4, 0xe5, 0x72, 0x7f, 0x41, 0x34, 0xab, 0x95,
      0xb9, 0x77, 0x59, 0xff, 0xa1, 0x09, 0x1e, 0x05, 0x8f, 0xe6, 0x46, 0xf6, 0xa6, 0xda, 0x5e, 0x74,
      0x46, 0x53, 0x5e, 0xb2, 0xa7, 0x72, 0x61, 0xf3, 0x95, 0xfc, 0x61, 0x34, 0xfe, 0xcd, 0xac, 0xb8,
      0x6c, 0x16, 0xec, 0x08, 0x96, 0x1d, 0xd0, 0x53, 0x40, 0x5c, 0x68, 0x41, 0x3b, 0x12, 0x6b, 0x1d,
      0xc5, 0x2a, 0x0b, 0xc6, 0x62, 0x11, 0xdc, 0x03, 0x05, 0xf8, 0x0b, 0x9b, 0x5d, 0xc9, 0xb0, 0x35,
      0x1b, 0x2d, 0xf7, 0xfb, 0x27, 0x5a, 0x72, 0xaf, 0xb5, 0xf8, 0x5f, 0xa8, 0x84, 0xe2, 0xf3, 0x0a,
      0x19, 0xb0, 0x53, 0xe7, 0x7e, 0x12, 0xaa, 0xaa, 0xa9, 0x72, 0x8b, 0x1c, 0x96, 0xca, 0xdf, 0xab,
      0xf3, 0x15, 0x2f, 0xc8, 0x48, 0x01, 0x5a, 0xa5, 0xd6, 0x54, 0x26, 0xb9, 0x06, 0x95, 0x53, 0xd8,
      0xcb, 0x15, 0x62, 0x8b, 0xdd, 0xbf, 0x5e, 0x64, 0xa7, 0xbc, 0x8c, 0x04, 0x31, 0x01, 0x4e, 0x05,
      0xe0, 0x9f, 0xa4, 0x6f, 0x8e, 0xfc, 0x9d, 0x71, 0xb3, 0xeb, 0x5e, 0xd5, 0x34, 0xf8, 0xfb, 0xd3,
      0xef, 0x01, 0xb3, 0xc1, 0x87, 0xc4, 0x12, 0xe7 };


//==============================================================================

std::mutex XmlSignatureVerify::m_mutex;
// reverse a byte
unsigned char
XmlSignatureVerify::rev(unsigned char v)
{
   char r = v; // r will be reversed bits of v; first get LSB of v
   int s = sizeof(v) * 8 - 1; // extra shift needed at end

   for (v >>= 1; v; v >>= 1)
   {
      r <<= 1;
      r |= v & 1;
      s--;
   }

   r <<= s; // shift when v's highest bits are zero

   return r;
}


// fibonacci number
int
XmlSignatureVerify::cci(int n)
{
   if (n < 2)
   {
      return n;
   }
   else
   {
      return cci(n-1) + cci(n-2);
   }
}

// fibonnaci walk and flip those bytes
void
XmlSignatureVerify::walk(unsigned char* array, int size)
{
   int index;
   for (int i = 0;; ++i)
   {
      index = cci(i);

      if (index >= size)
         break;
      else
      {
         unsigned char b = array[index];
         array[index] = rev(b);
      }
   }
}


static void hexdump(FILE *f, const char *title, const unsigned char *s, int l)
{
   int n=0;

   fprintf(f,"%s",title);
   for( ; n < l ; ++n)
   {
      if((n%16) == 0)
         fprintf(f,"\n%04x",n);
      fprintf(f," %02x",s[n]);
   }
   fprintf(f,"\n");
}



XmlSignatureVerify::XmlSignatureVerify()
{
}

XmlSignatureVerify::~XmlSignatureVerify()
{
   m_RootCertificates.clear();
}

/**
 * Extract the signature from the XML document, turn it into an X509 certificate
 * block, and then verify the entire signed XML contents using that.  <cbond>
 **/
bool
XmlSignatureVerify::verifySignature(const std::string& s, unsigned char** psig, int& siglen)
{
   ScopedXmlSecSharedUsage xmlSecUsage;

   std::unique_lock<std::mutex> lock(m_mutex);

   xmlDocPtr document = xmlParseMemory(s.c_str(), (unsigned long)s.size());
   if (!document)
   {
      ErrLog(<< "XmlSignatureVerify::verifySignature() Bad XML document - Can't verify!");
      return false;
   }

   xmlNodePtr root = xmlDocGetRootElement(document);

   // Get the contents of the X509Certificate node.
   xmlNodePtr sig = xmlSecFindNode(root, xmlSecNodeSignature, xmlSecDSigNs);
   if (sig == 0)
   {
      ErrLog(<<"XmlSignatureVerify::verifySignature() Failed finding X509Certificate node - Can't verify!");
      xmlFreeDoc(document);
      return false;
   }

   xmlSecDSigCtxPtr sigContext = 0;
   xmlSecKeyDataPtr keyData = 0;

   std::string cert;
   try
   {
      cert = readCertificate(sig);

      if (cert.empty())
      {
         throw std::runtime_error("empty certificate contents");
      }

      // Ensure the X509 certificate data is properly formatted.
      cert = extractCertificate(cert);

      // NEW NEW NEW Extract the Authority Key ID from the signing cert
      BIO *b=BIO_new_mem_buf((void*)cert.data(), -1);
      X509 *xCert = PEM_read_bio_X509(b,0,0,0);
      BIO_free(b);
      if (NULL == xCert)
      {
         if (ERR_peek_error())
         {
            ErrLog(<< "Dumping OpenSSL error stack:");
            while (ERR_peek_error())
            {
               char errBuf[120];
               ERR_error_string(ERR_get_error(), errBuf);
               ErrLog(<< "OpenSSL error stack: " << errBuf);
            }
         }
         throw std::runtime_error("cannot load document certificate");
      }
      AUTHORITY_KEYID *akid = static_cast<AUTHORITY_KEYID *>(X509_get_ext_d2i(xCert, NID_authority_key_identifier, NULL, NULL));
      if (NULL == akid)
      {
         if (ERR_peek_error())
         {
            ErrLog(<< "Dumping OpenSSL error stack:");
            while (ERR_peek_error())
            {
               char errBuf[120];
               ERR_error_string(ERR_get_error(), errBuf);
               ErrLog(<< "OpenSSL error stack: " << errBuf);
            }
         }
         throw std::runtime_error("cannot extract keyid");
      }

      siglen = akid->keyid->length;
      unsigned char *bytes;
      bytes = new unsigned char[siglen+1];
      memcpy(bytes, akid->keyid->data, siglen);

      // Pass back the bytes and length in some form??
      *psig = bytes;

      fillTrustedRootCertificates(resip::Data(*psig, siglen));

      //free(bytes);
      AUTHORITY_KEYID_free(akid);
      X509_free(xCert);
      // NEW NEW NEW

      //load all the trusted root certs and then the signing cert (as untrusted)?
      if (loadTrustedRootCertificates(keyData) == 0)
      {
         throw std::runtime_error("cannot load trusted root certificates");
      }

      sigContext = xmlSecDSigCtxCreate(NULL);
      /*dragos: this is bizarre: this sigContext object is always NULL regardless of what I try;*/
      if (sigContext == NULL)
      {
         throw std::bad_alloc();
      }
   }
   catch (const std::exception& e)
   {
      ErrLog(<< "XmlSignatureVerify::verifySignature() Cannot load license X509 certficiate contents: " + std::string(e.what()));
      if (sigContext) xmlSecDSigCtxDestroy(sigContext);
      xmlFreeDoc(document);
      return false;
   }

   //destroyed by xmlSecDSigCtxDestroy:
   xmlSecKeyPtr key = xmlSecKeyCreate();
   // make sure the desired public key is used for signature verification:
   if (NULL != key)
   {
      key->value = keyData;
      key->usage = xmlSecKeyUsageVerify;

      sigContext->signKey = key;
   }
   bool verify = (NULL != key) && (xmlSecDSigCtxVerify(sigContext, sig) >= 0) && (sigContext->status == xmlSecDSigStatusSucceeded);

   xmlSecDSigCtxDestroy(sigContext);
   xmlFreeDoc(document);

   clearTrustedRootCertificates();

   return verify;
}

int
XmlSignatureVerify::loadCertificate(xmlSecKeysMngrPtr mgr, const std::string& contents)
{
   int ret = 0;
   if (mgr == 0)
   {
      throw std::runtime_error("invalid xmlsec key manager");
   }

   if (xmlSecCryptoAppKeysMngrCertLoadMemory(mgr, reinterpret_cast<const xmlSecByte*>(contents.c_str()), contents.size(), xmlSecKeyDataFormatPem, xmlSecKeyDataTypeTrusted) < 0)
   {
      throw std::runtime_error("cannot load certificate");
   }
   ret = 1;

   return ret;
}

void
XmlSignatureVerify::clearTrustedRootCertificates()
{
   m_RootCertificates.clear();
}

void
XmlSignatureVerify::fillTrustedRootCertificates(const resip::Data& psig)
{
   if (m_RootCertificates.size() == 0)
   {
     /*
      * Uses XXX_encobf and xten::OtherHalf as one long string,
      * fibonacci walks and reverses those select bytes, decrypts
      * using AES, and then loads it
      *
      * These two arrays can be obtained by compiling (outside the sdk) and runing
      * embedc.cpp located at \util\newlicensing
      */

      const resip::Data hexSig = psig.hex();
      if (hexSig.size() == 0 || hexSig.size() < 32)
      {
         /*
#ifdef _DEBUG
         SUA_TRACE_ERROR("Authority id not correct");
#endif
         */
         return;
      }

      /*
#ifdef _DEBUG
      // DEBUGGING ONLY -- dumps signature for embedc utility
      std::ofstream of("c:\\temp\\sig.txt");
      of << psig.hex() << std::endl;
#endif
      */


      unsigned char XXX_encobf[] = {
         0x21, 0xc1, 0x39, 0x60, 0x2c, 0x60, 0x26, 0xc2, 0xd9, 0x7a, 0xf8, 0xc5, 0x16, 0x05, 0xf2, 0x1a,
         0x34, 0x25, 0x97, 0xec, 0x3d, 0xf5, 0x1a, 0x5e, 0x9e, 0x3b, 0xd5, 0xa3, 0xaf, 0xbb, 0x98, 0x77,
         0x48, 0x2c, 0x1a, 0x7b, 0x05, 0x4e, 0x9e, 0x3c, 0x85, 0x9b, 0x8c, 0xdb, 0x6c, 0xf6, 0x16, 0x24,
         0x3a, 0x8b, 0x9a, 0x9d, 0x36, 0x72, 0xb2, 0x7b, 0x95, 0x64, 0xbe, 0xc3, 0x77, 0xe3, 0x5b, 0xa1,
         0x2b, 0xe8, 0x65, 0xb8, 0x30, 0x9d, 0x17, 0x39, 0xed, 0x2d, 0x34, 0x13, 0xbc, 0xd2, 0xeb, 0xc8,
         0xe3, 0x3a, 0xbe, 0x16, 0xda, 0x85, 0xd8, 0x24, 0x24, 0xb2, 0x08, 0xc7, 0x03, 0xbf, 0xdd, 0xd9,
         0xfb, 0x2a, 0x5a, 0xf4, 0x61, 0x52, 0xac, 0x61, 0x9f, 0x5e, 0x74, 0x78, 0xe3, 0xe8, 0x25, 0xce,
         0x9c, 0x2d, 0x3c, 0x8b, 0x93, 0xd4, 0x8f, 0xc2, 0x7d, 0xf3, 0xba, 0x8b, 0xc6, 0x52, 0xdc, 0x8b,
         0x67, 0xb3, 0x7f, 0x71, 0xa9, 0xe5, 0x0a, 0x4d, 0x5d, 0xf1, 0xbb, 0x24, 0x6d, 0x07, 0xaa, 0x9a,
         0x4e, 0xdc, 0x9e, 0x7b, 0xd3, 0xa7, 0x51, 0x97, 0x09, 0x93, 0x7b, 0x45, 0xed, 0xc2, 0x03, 0xbf,
         0x74, 0x04, 0xa7, 0xdd, 0x82, 0xc6, 0x37, 0xea, 0xc0, 0xcd, 0x21, 0xf7, 0x32, 0xba, 0x01, 0x63,
         0xd3, 0x69, 0xb3, 0xe4, 0x04, 0xdd, 0x59, 0x80, 0x63, 0x42, 0x29, 0xba, 0x76, 0xcf, 0xd0, 0xea,
         0x2a, 0x29, 0x6b, 0x6d, 0xa4, 0x7b, 0x7b, 0x90, 0x3e, 0x25, 0x6f, 0x51, 0x9f, 0xab, 0x42, 0xe5,
         0x76, 0xc8, 0xbc, 0x41, 0x1b, 0x38, 0x6b, 0xb4, 0x49, 0xb4, 0x87, 0xba, 0xe2, 0xb5, 0xef, 0xd6,
         0xe6, 0x94, 0x05, 0x74, 0xee, 0xc9, 0x4f, 0x85, 0x85, 0x55, 0xa0, 0x9c, 0x7c, 0xce, 0x5a, 0xbd,
         0xf9, 0xa1, 0x61, 0x40, 0xe9, 0x6e, 0xe0, 0xd4, 0x47, 0xc8, 0x63, 0xcb, 0x40, 0x96, 0x60, 0x1e,
         0xc9, 0x06, 0x2a, 0x9a, 0x99, 0x61, 0x5b, 0x2e, 0x82, 0xb4, 0x66, 0x49, 0x1a, 0x41, 0x36, 0x55,
         0xf3, 0x53, 0x25, 0x54, 0x4c, 0x32, 0x3f, 0xc8, 0xfe, 0x9f, 0xe3, 0x6c, 0x8a, 0x82, 0x5e, 0xce,
         0xea, 0xb6, 0x04, 0x22, 0xd1, 0x0a, 0x75, 0xa5, 0x5f, 0x68, 0xe7, 0x56, 0x38, 0xe4, 0x28, 0xe6,
         0x94, 0x9e, 0xd2, 0x48, 0xcb, 0x67, 0xc5, 0xb3, 0x30, 0xc5, 0x30, 0x4c, 0x0c, 0xa6, 0x1b, 0xd4,
         0xaf, 0x5b, 0x91, 0x3c, 0x4a, 0xed, 0x97, 0x74, 0x4e, 0x13, 0xd9, 0x8f, 0xa1, 0x1d, 0x6b, 0x3a,
         0x5e, 0x0f, 0xd5, 0x51, 0xd7, 0x5d, 0xec, 0xd7, 0xa6, 0x3a, 0xbf, 0x89, 0xb3, 0x63, 0x7d, 0x15,
         0x8c, 0xed, 0xc7, 0x8b, 0x9c, 0x7a, 0x12, 0xa0, 0x20, 0xbe, 0xdd, 0xb4, 0x9c, 0x9f, 0x07, 0x73,
         0x5f, 0xd6, 0x33, 0x1a, 0xcd, 0x4a, 0xec, 0xeb, 0x5f, 0xc9, 0x31, 0xba, 0x69, 0x61, 0xcf, 0x3f,
         0x12, 0x5a, 0x5c, 0x69, 0xc2, 0xee, 0x31, 0x57, 0xd3, 0x15, 0x78, 0x17, 0x72, 0x49, 0x91, 0x2c,
         0x9d, 0x54, 0x8a, 0x8e, 0x0f, 0xfe, 0xd8, 0xa2, 0x6e, 0xca, 0x9c, 0x31, 0xba, 0xa1, 0x77, 0x8c,
         0x3f, 0x4c, 0xa7, 0xd6, 0x25, 0x9c, 0x3a, 0x6e, 0x5e, 0xdf, 0xae, 0x6d, 0xef, 0xaa, 0xb7, 0x9d,
         0xf5, 0x1b, 0x64, 0xe3, 0x14, 0xc4, 0x2a, 0x1b, 0x5f, 0x18, 0x5b, 0x1c, 0xfe, 0xfc, 0xdc, 0x4a,
         0x3c, 0x4c, 0xd2, 0xab, 0xbb, 0xf8, 0x6c, 0xec, 0xc3, 0x09, 0xcb, 0x2e, 0xbc, 0x62, 0x37, 0xff,
         0x93, 0x88, 0xbd, 0x83, 0x69, 0xb1, 0x52, 0x5f, 0x09, 0x6e, 0x59, 0xa0, 0x1f, 0x2a, 0x67, 0x57,
         0xa7, 0xd5, 0x7e, 0x2a, 0x73, 0xe4, 0x7e, 0x56, 0x62, 0x03, 0x84, 0xac, 0x95, 0x0b, 0x83, 0xcb,
         0xca, 0xc7, 0x60, 0xed, 0x75, 0x6e, 0xda, 0x99, 0xce, 0x84, 0xed, 0x74, 0xc0, 0xc4, 0xbd, 0x0b,
         0x1c, 0x27, 0xb7, 0xd8, 0x7b, 0x6a, 0x17, 0x89, 0x41, 0xb2, 0xe3, 0x18, 0x08, 0xa5, 0x37, 0x59,
         0x2e, 0xae, 0x06, 0xb1, 0x12, 0xd2, 0x9c, 0x73, 0x43, 0x5f, 0x9f, 0x5b, 0xd5, 0x5c, 0xc0, 0x29,
         0x5f, 0xb0, 0xa8, 0x7b, 0x74, 0x0a, 0xed, 0x9b, 0xf6, 0x08, 0xa4, 0xe4, 0x62, 0x5d, 0x7e, 0xf6,
         0x38, 0xa6, 0xf5, 0x03, 0x73, 0x96, 0x03, 0x54, 0x88, 0x7f, 0x56, 0xe4, 0x2d, 0xb2, 0xab, 0xe3,
         0x73, 0x47, 0x46, 0xff, 0xed, 0xee, 0xe3, 0x5d, 0xb9, 0x88, 0x4e, 0x18, 0xb8, 0x13, 0x61, 0x38,
         0xe7, 0x6b, 0x39, 0x8b, 0x28, 0xbb, 0xef, 0xe3, 0x23, 0xdd, 0x1d, 0xda, 0x1d, 0x62, 0xf0, 0x4c,
         0x46, 0x37, 0xd4, 0x4a, 0x32, 0xe6, 0x72, 0x93, 0x4e, 0x15, 0xf6, 0x33, 0xae, 0xf1, 0x30, 0x81,
         0x99, 0x94, 0x93, 0x70, 0x2d, 0xb8, 0x76, 0x64, 0x98, 0x3d, 0xf6, 0xbd, 0x3d, 0x8b, 0x5a, 0x17,
         0x45, 0xc9, 0xfc, 0x7e, 0xbf, 0x6a, 0xfd, 0x1f, 0xc3, 0x0e, 0x69, 0xb3, 0x84, 0x74, 0x20, 0xbf,
         0x3f, 0xaa, 0x85, 0x20, 0x4c, 0x85, 0x1a, 0x4c, 0xef, 0x88, 0xd7, 0xe8, 0x94, 0x42, 0x26, 0x84,
         0xc4, 0x8d, 0xc7, 0x95, 0x4a, 0x88, 0xac, 0x77, 0xed, 0x50, 0x2a, 0x63, 0x2d, 0x4b, 0x0c, 0xc0,
         0x66, 0xd8, 0xc8, 0x96, 0x52, 0xa1, 0x19, 0x55, 0x71, 0xf0, 0x2b, 0x8a, 0x82, 0xa6, 0x77, 0xb8,
         0x02, 0x0d, 0x59, 0x28, 0x64, 0xa9, 0xc8, 0x11, 0xb8, 0x75, 0x5f, 0x89, 0xf5, 0x8c, 0x8c, 0x45,
         0x69, 0x5b, 0x91, 0x07, 0xf1, 0x9c, 0xf0, 0x2a, 0x32, 0x96, 0xf7, 0xae, 0x18, 0x94, 0x2b, 0x46,
         0x0d, 0x7b, 0x44, 0xb7, 0xcb, 0x1c, 0xd5, 0xf3, 0xd5, 0xbc, 0x80, 0x8c, 0xe5, 0x6e, 0x23, 0xd7,
         0x23, 0xae, 0xa7, 0xeb, 0x8c, 0x8d, 0xbc, 0x89, 0x1b, 0x78, 0x4e, 0xe7, 0x23, 0x18, 0x56, 0xf2,
         0x09, 0x69, 0x3a, 0x68, 0x99, 0x03, 0xe4, 0xea, 0x89, 0x86, 0xdc, 0x1d, 0x66, 0xdd, 0x7b, 0x0a,
         0xb1, 0x85, 0xd1, 0xa4, 0xb3, 0x57, 0x37, 0x28, 0x1e, 0xe0, 0xf7, 0x29, 0x46, 0x66, 0xc2, 0xaf,
         0x0d, 0x6c, 0x7b, 0x59, 0x23, 0x7d, 0x5a, 0x7e, 0x83, 0xdc, 0x32, 0x6f, 0x32, 0x5e, 0xf1, 0xb2,
         0xc1, 0xb9, 0xc8, 0x99, 0x74, 0x19, 0x8d, 0x0c, 0x46, 0x73, 0xe1, 0xec, 0xad, 0x84, 0x19, 0x83,
         0xa9, 0xbc, 0x15, 0x90, 0x99, 0x21, 0xb3, 0x65, 0x81, 0x89, 0xa4, 0x6e, 0x42, 0x60, 0xea, 0x58,
         0x7a, 0x62, 0xc2, 0x15, 0x5a, 0x46, 0xd5, 0xac, 0x2b, 0x00, 0x20, 0x73, 0x5c, 0x69, 0xbc, 0x0c,
         0x9b, 0x7c, 0xcc, 0x80, 0xe4, 0xde, 0x46, 0x3f, 0x5f, 0x1f, 0x7a, 0x32, 0xba, 0x07, 0x53, 0xc4,
         0x8e, 0xb4, 0xb0, 0xe4, 0xf5, 0x2c, 0x7c, 0xdf, 0xcd, 0x22, 0x46, 0x98, 0xaa, 0xae, 0x51, 0xa7,
         0xb5, 0x4a, 0x23, 0x85, 0x1e, 0xd2, 0x96, 0xd2, 0xbb, 0x48, 0x1b, 0x61, 0xdb, 0x3e, 0xe9, 0x04,
         0x76, 0xdc, 0x9f, 0xfa, 0x6a, 0xe3, 0xb4, 0xec };


      const int entireLength = sizeof(XXX_encobf) + sizeof(OtherHalf);
      boost::scoped_array<unsigned char> entire(new unsigned char[entireLength]);

      memcpy(entire.get(), XXX_encobf, sizeof(XXX_encobf));
      memcpy(entire.get() + sizeof(XXX_encobf), OtherHalf, sizeof(OtherHalf));

      walk(entire.get(), entireLength);

      int tmplen = 0;
      int outlen = 0;

      // max size in1 + ciper_block_size - 1
      boost::scoped_array<unsigned char> outbuf(new unsigned char[entireLength + EVP_CIPHER_block_size(EVP_aes_256_cbc()) + 1]);

      EVP_CIPHER_CTX* x;
      x = EVP_CIPHER_CTX_new();

      bool decryptOk = true;

      // key length for aes-256 is 32 bytes
      EVP_DecryptInit_ex(x, EVP_aes_256_cbc(), NULL, (unsigned char*)hexSig.substr(0, 32).c_str(),
                         (unsigned char*)INITIALIZATION_VECTOR);

      if (!EVP_DecryptUpdate(x, outbuf.get(), &outlen,(const unsigned char*) entire.get(), entireLength))
      {
   #ifdef _DEBUG
         ErrLog(<< " Error decrypting root ca ");
   #endif

         decryptOk = false;
      }

      if (!EVP_DecryptFinal_ex(x, outbuf.get() + outlen, &tmplen))
      {
   #ifdef _DEBUG
         ErrLog(<< " Error decrypting ");
         unsigned long error, reason;
         char buf[128];
         error = ERR_peek_error();
         reason = ERR_GET_REASON(error);
         ERR_error_string_n(reason,buf,128);
   #endif // _DEBUG

         decryptOk = false;
      }

      outlen += tmplen;

      EVP_CIPHER_CTX_free(x);

      if (decryptOk)
      {
         m_RootCertificates.push_back(std::string((const char*)outbuf.get(), outlen));
      }
   }
}


int
XmlSignatureVerify::loadTrustedRootCertificates(xmlSecKeyDataPtr &ptr)
{
   std::vector<std::string>::iterator it_root_cert = m_RootCertificates.begin();
   while (it_root_cert != m_RootCertificates.end())
   {
      if (ptr != 0)
      {
         throw std::runtime_error("invalid xmlsec data");
      }
      const unsigned char *data = reinterpret_cast<const unsigned char *>((*it_root_cert).c_str());
      X509 *cert = d2i_X509(NULL, &data, (*it_root_cert).size());
      if (NULL != cert)
      {
         ptr = xmlSecOpenSSLX509CertGetKey(cert);
         X509_free(cert);
      }

      it_root_cert++;
   }

   return (ptr != 0 ? 1 : 0);
}


const std::string
XmlSignatureVerify::readCertificate(xmlNodePtr node)
{
   xmlNodePtr child = xmlSecFindChild(node, BAD_CAST "KeyInfo", BAD_CAST "http://www.w3.org/2000/09/xmldsig#");
   if (child == 0)
   {
      throw std::runtime_error("missing <Signature/KeyInfo> node");
   }

   xmlNodePtr x509data = xmlSecFindChild(child, BAD_CAST "X509Data", BAD_CAST "http://www.w3.org/2000/09/xmldsig#");
   if (x509data == 0)
   {
      throw std::runtime_error("missing <Signature/KeyInfo/X509Data> node");
   }

   xmlNodePtr x509cert = xmlSecFindChild(x509data, BAD_CAST "X509Certificate", BAD_CAST "http://www.w3.org/2000/09/xmldsig#");
   if (x509cert == 0)
   {
      throw std::runtime_error("missing <Signature/KeyInfo/X509Data/X509Certificate> node");
   }

   xmlNodePtr contents = x509cert->children;
   if (contents
#ifdef __APPLE__
      && strcasecmp(reinterpret_cast<const char*>(contents->name), "text") == 0
#else
      && stricmp(reinterpret_cast<const char*>(contents->name), "text") == 0
#endif
      && contents->content)
   {
      std::string x(reinterpret_cast<const char*>(contents->content), xmlStrlen(contents->content));

      const std::string::size_type n = x.find("</X509Certificate></X509Data></KeyInfo>");
      if (n != std::string::npos)
      {
         x.erase(n);
      }

      return x;
   }

   throw std::runtime_error("missing <Signature/KeyInfo/X509Data/X509Certificate> contents");
}

const std::string
XmlSignatureVerify::extractCertificate(const std::string& block)
{
   std::stringstream ss;

   ss << "-----BEGIN CERTIFICATE-----" << std::endl;

   for (std::string::size_type i = 0; i < block.size(); /**/)
   {
      const std::string::size_type length = std::min<std::string::size_type>(64, block.size() - i);
      const std::string s = block.substr(i, length);

      ss << s;

      if (s[s.size() - 1] != '\r' && s[s.size() - 1] != '\n')
      {
         ss << std::endl;
      }

      i += length;
   }

   ss << "-----END CERTIFICATE-----" << std::endl;

   return ss.str();
}

}
}
