#include <nlohmann/json.hpp>
#include "AudioAudioDeviceRole.h"

#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOSETRENDERDEVICE_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOSETRENDERDEVICE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
  class AudioSetRenderDevice
  {
  public:
    int32_t phoneHandle;
    int32_t deviceId;
    AudioAudioDeviceRole role;

    AudioSetRenderDevice() = default;

    AudioSetRenderDevice(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const AudioSetRenderDevice& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["deviceId"] = o.deviceId;
     j["role"] = o.role;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, AudioSetRenderDevice& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.deviceId = j.at("deviceId");
     o.role = j.at("role");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOSETRENDERDEVICE_H
