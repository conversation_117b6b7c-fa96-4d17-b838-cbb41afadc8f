#include "brand_branded.h"

#include <interface/experimental/jsonapi/JsonApiClient.h>
#include <interface/experimental/jsonapi/JsonApiServer.h>
#include <interface/experimental/jsonapi/JsonApiServerSendTransport.h>

#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "phone/PhoneInterface.h"
#include "JsonApiClientInterface.h"
#endif
#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "phone/PhoneInterface.h"
#include "JsonApiServerInterface.h"
#include "JsonApiServerSendTransportInterface.h"
#endif

namespace CPCAPI2
{
   namespace JsonApi
   {
      JsonApiClient* JsonApiClient::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<JsonApiClientInterface>(phone, "JsonApiClient");
#else
         return NULL;
#endif
      }

      JsonApiServer* JsonApiServer::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<JsonApiServerInterface>(phone, "JsonApiServer");
#else
         return NULL;
#endif
      }

      JsonApiServerSendTransport* JsonApiServerSendTransport::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<JsonApiServerSendTransportInterface>(phone, "JsonApiServerSendTransport");
#else
         return NULL;
#endif
      }
   }
}
