#include "BIEventResponse.h"
#include "BIDruidCheckResponse.h"

using namespace CPCAPI2::BIEvents;

BIDruidResponse_t BIDruidCheckResponse::getResponseStatus( void )
{
   BIDruidResponse_t result( BIDruidResponse_Invalid );

   if( m_Params.find( "status" ) != m_Params.end() )
   {
      const RPCParam& statusValue( m_Params[ "status" ] );
      if( statusValue.m_Value.m_Type == RPCValueType_string )
      {
         if( statusValue.m_Value.m_StringVal == "new" )
            result = BIDruidResponse_New;
         else if( statusValue.m_Value.m_StringVal == "reissued" )
            result = BIDruidResponse_Reissued;
         else if( statusValue.m_Value.m_StringVal == "valid" )
            result = BIDruidResponse_Valid;
         else if( statusValue.m_Value.m_StringVal == "updated" )
            result = BIDruidResponse_Updated;
         else if( statusValue.m_Value.m_StringVal == "conflict" )
            result = BIDruidResponse_Conflict;
         else if( statusValue.m_Value.m_StringVal == "fraud" )
            result = BIDruidResponse_Fraud;
         else if( statusValue.m_Value.m_StringVal == "invalid" )
            result = BIDruidResponse_Invalid;
         else
            assert( 0 && "unknown status value" );
      }
   }

   return result;
}

std::string BIDruidCheckResponse::getResponseDruid( void )
{
   std::string result;

   if( m_Params.find( "druid" ) != m_Params.end() )
   {
      const RPCParam& druidValue( m_Params[ "druid" ] );
      if( druidValue.m_Value.m_Type == RPCValueType_string )
         result = druidValue.m_Value.m_StringVal;
   }
   return result;
}
