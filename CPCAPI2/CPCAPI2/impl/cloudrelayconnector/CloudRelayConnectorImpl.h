#pragma once

#if !defined(CPCAPI2_CLOUD_RELAY_CONNECTOR_IMPL_H)
#define CPCAPI2_CLOUD_RELAY_CONNECTOR_IMPL_H

#include "cpcapi2defs.h"
#include "cloudrelayconnector/CloudRelayConnector.h"
#include "jsonapi/JsonApiClient.h"
#include "cloudconnector/CloudServerConnectionObserver.h"
#include "cloudrelay/CloudRelayManager.h"
#include "cloudrelay/CloudRelayHandler.h"
#include "cloudrelay/CloudRelaySyncHandler.h"
#include "cloudrelay/CloudRelayJsonProxy.h"
#include "peerconnection/PeerConnectionManager.h"
#include "peerconnection/PeerConnectionHandler.h"
#include "peerconnection/PeerConnectionSyncHandler.h"
#include "videostreaming/VideoStreaming.h"
#include "media/video/Video.h"
#include "../experimental/video_ext/VideoExt.h"

#include <rutil/Data.hxx>
#include <map>
#include <memory>

#if !defined(_WIN32) && !defined(ANDROID)
#include <mm_malloc.h>
#endif

namespace CPCAPI2
{
class PhoneInterface;
namespace CloudConnector
{
class CloudServerConnection;
}
namespace CloudRelayConnector
{
class CloudRelayConnectorInterface;
struct CloudServerConnStatusEvent;
struct ConferenceSessionStatusChangedEvent;

class CloudRelayConnectorImpl : public CPCAPI2::CloudConnector::CloudServerConnectionObserver,
                                public CPCAPI2::CloudRelay::CloudRelayHandler,
                                public CPCAPI2::CloudRelay::CloudRelaySyncHandler,
                                public std::enable_shared_from_this<CloudRelayConnectorImpl>
{
public:
   CloudRelayConnectorImpl(CloudRelayConnectorInterface* ccif, CloudRelayConnectorHandle h);
   virtual ~CloudRelayConnectorImpl();

   void* operator new(size_t i)
   {
#if defined(ANDROID)
      return memalign(folly::hardware_destructive_interference_size, i);
#else
      return _mm_malloc(i, folly::hardware_destructive_interference_size);
#endif
   }

   void operator delete(void* p)
   {
#if defined(ANDROID)
      free(p);
#else
      _mm_free(p);
#endif
   }

   int destroy();
   int setConnectionSettings(const CloudRelayConnectorSettings& settings);
   int requestService();
   int connectToServices();
   int disconnectService();
   int broadcast(const cpc::string& msg);
   int sendTo(CloudRelayEndpointId destinationId, const cpc::string& msg);

   void release();

   // CloudServerConnectionObserver
   virtual void handleCloudServerConnectionStatusChanged(CPCAPI2::CloudConnector::CloudServerConnection* conn, const CPCAPI2::CloudConnector::CloudServerConnStatusEvent& args);

   // CloudRelayHandler
   virtual int onBroadcast(CPCAPI2::CloudRelay::CloudRelayHandle relay, const CPCAPI2::CloudRelay::BroadcastEvent& args);
   virtual int onMessage(CPCAPI2::CloudRelay::CloudRelayHandle relay, const CPCAPI2::CloudRelay::MessageEvent& args);

private:
   //CloudConferenceHandle createConferenceHandle(const CPCAPI2::CloudRelay::ConferenceSettings& settings);
   int doFirstTimeConnect(const resip::Data& authToken, const CPCAPI2::CloudConnector::ServiceDesc& cloudrelayServiceDesc);
   static void setIncomingVideoRenderTargetWrap(CPCAPI2::Media::Video* videoIf, int mediaStreamId, void* surface, Media::VideoSurfaceType type = Media::VideoSurfaceType_Default);

private:
   CloudRelayConnectorInterface* mInterface;
   CloudRelayConnectorHandle mHandle;
   CloudRelayConnectorSettings mSettings;

   resip::Data mConfServiceUri;
   std::shared_ptr<CPCAPI2::CloudConnector::CloudServerConnection> mConfServiceConn;
   resip::MultiReactor mWebRequestThread;
   
   CPCAPI2::Phone* mConfPhone;

   // JSON proxy for CloudRelayManager
   CPCAPI2::CloudRelay::CloudRelayManager* mConfBridgeMgr;

};

}

}

#endif // CPCAPI2_CLOUD_RELAY_CONNECTOR_IMPL_H

