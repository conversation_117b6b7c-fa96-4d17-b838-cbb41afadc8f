#include <cstdint>

#include "interface/public/phone/Phone.h"

#pragma once

#if !defined(CPCAPI2_PHONE_INSTANCES_H)
#define CPCAPI2_PHONE_INSTANCES_H

typedef uint64_t PhoneHandle;

namespace CPCAPI2
{
    class PhoneInstances
    {
    public:
        PhoneInstances() = default;
        virtual ~PhoneInstances() = default;

        PhoneHandle add(Phone *instance);

        // Remove the Phone instance. The instance must also be release by calling Phone::release.
        void remove(PhoneHandle handle);

        // Returns the Phone instance. Throws a JSON-RPC execption if handle is not found
        Phone* get(PhoneHandle handle);

        // Returns the Phone instance. Returns null if handle is not found
        Phone* getNoThrow(PhoneHandle handle);

    protected:
        std::map<PhoneHandle, Phone*> mInstances;
        PhoneHandle mNextPhoneHandle = 1;
    }; // class PhoneInstances
} // namespace CPCAPI2

#endif // CPCAPI2_PHONE_INSTANCES_H
