#pragma once

#if !defined(CPCAPI2_SIP_EVENT_PUBLICATION_MANAGER_INTERFACE_H)
#define CPCAPI2_SIP_EVENT_PUBLICATION_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "event/SipPublicationManager.h"
#include "event/SipEventPublicationHandler.h"
#include "../phone/PhoneModule.h"
#include "../phone/PhoneInterface.h"

#include <map>

namespace CPCAPI2
{

namespace SipAccount
{
class SipAccountInterface;
}

namespace SipEvent
{
class SipEventPublicationManagerImpl;
class SipEventPublicationCreationInfo;

class SipEventPublicationManagerInterface : public CPCAPI2::EventSource2< CPCAPI2::EventHandler<SipEventPublicationHandler, CPCAPI2::SipAccount::SipAccountHandle, std::string> >,
                                            public SipEventPublicationManager,
                                            public PhoneModule
{
public:
   
   SipEventPublicationManagerInterface(Phone* phone);
   virtual ~SipEventPublicationManagerInterface();

   virtual void Release() OVERRIDE;

   /**
   * Set the handler.
   */   
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const cpc::string& eventType,
         SipEventPublicationHandler * handler) OVERRIDE;


   virtual SipEventPublicationHandle createPublication(CPCAPI2::SipAccount::SipAccountHandle account, const SipEventPublicationSettings& settings) OVERRIDE;
   virtual int setTarget(SipEventPublicationHandle publication, const cpc::string& targetAddress) OVERRIDE;
   virtual int publish(SipEventPublicationHandle publication, const SipEventState& eventState) OVERRIDE;
   virtual int end(SipEventPublicationHandle publication) OVERRIDE;
   virtual int refresh(SipEventPublicationHandle publication) OVERRIDE;

   /**
    * Function will destroy the previous SipEventPublicationCreationInfo associated with the handle,
    * and map the same handle to a new SipEventPublicationCreationInfo.
    *
    * Do not hold onto the previous SipEventPublicationCreationInfo pointer after calling this function.
   */
   bool recreatePublication(CPCAPI2::SipAccount::SipAccountHandle account, SipEventPublicationHandle publication);

   SipEventPublicationCreationInfo * getCreationInfo(SipEventPublicationHandle h);

   int setHandlerImpl(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const cpc::string& eventType,
         SipEventPublicationHandler* handler);

   SipEventPublicationHandle createPublicationSync(CPCAPI2::SipAccount::SipAccountHandle account, const SipEventPublicationSettings& settings);

   // EventSource2
   void addAppHandler(SipEventPublicationHandler* handler, CPCAPI2::SipAccount::SipAccountHandle handle, std::string filter);
   void addSdkObserver(SipEventPublicationHandler* handler, CPCAPI2::SipAccount::SipAccountHandle handle, std::string filter);
   void addSdkObserverSafe(const std::shared_ptr<SipEventPublicationHandler>& handler, CPCAPI2::SipAccount::SipAccountHandle handle, std::string filter);

private:
   void addEventType(const CPCAPI2::SipAccount::SipAccountHandle& account, const std::string& eventType);

   int createPublicationImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipEventPublicationHandle h, const SipEventPublicationSettings& settings);
   int setTargetImpl(SipEventPublicationHandle publication, const cpc::string& targetAddress);
   int publishImpl(SipEventPublicationHandle publication, const SipEventState& eventState);
   int endImpl(SipEventPublicationHandle publication);
   int refreshImpl(SipEventPublicationHandle publication);

private:
   
   CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, SipEventPublicationManagerImpl*> AccountMap;
   std::shared_ptr<AccountMap> mAccountMapPtr;
   AccountMap& mAccountMap;
   std::map<std::tuple<CPCAPI2::SipAccount::SipAccountHandle, std::string>, SipEventPublicationHandler*> mHandlers;
   SipEventPublicationHandle mNextPublicationHandle;
   CPCAPI2::PhoneInterface* mPhone;
};

std::ostream& operator<<(std::ostream& os, const PublicationSuccessEvent& evt);
std::ostream& operator<<(std::ostream& os, const PublicationFailureEvent& evt);
std::ostream& operator<<(std::ostream& os, const PublicationRemoveEvent& evt);
std::ostream& operator<<(std::ostream& os, const PublicationErrorEvent& evt);
std::ostream& operator<<(std::ostream& os, const PublicationManagerReadyEvent& evt);

}
   
}

#endif // CPCAPI2_SIP_EVENT_PUBLICATION_MANAGER_INTERFACE_H
