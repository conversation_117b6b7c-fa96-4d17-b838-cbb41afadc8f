#include "brand_branded.h"

#if (CPCAPI2_BRAND_CLOUD_SERVICE_CONFIG_MODULE == 1)
#include "cpcapi2utils.h"
#include "CloudServiceConfigInterface.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "../orchestration_server/OrchestrationServerInterface.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

#include <sstream>

using namespace resip;
using namespace CPCAPI2::JsonA<PERSON>;
using namespace CPCAPI2::OrchestrationServer;

using resip::ReadCallbackBase;

#define CLOUD_SERVICE_CONFIG_REREG_TIMER_ID 1
#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

namespace CPCAPI2
{
namespace CloudServiceConfig
{
CloudServiceConfigInterface::CloudServiceConfigInterface(Phone* phone) :
EventSource<CloudServiceConfigHandle, CloudServiceConfigHandler, CloudServiceConfigSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
mPhone(dynamic_cast<PhoneInterface*>(phone)),
mOrchServer(NULL),
mTimer(mPhone->getSdkModuleThread())
{
   StackLog(<< "CloudServiceConfigInterface::CloudServiceConfigInterface(): " << this << " phone: " << phone);
   mPhone->addRefImpl();

   mOrchServer = CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(phone);
}

CloudServiceConfigInterface::~CloudServiceConfigInterface()
{
   StackLog(<< "CloudServiceConfigInterface::~CloudServiceConfigInterface(): " << this << " phone: " << mPhone);
   mTimer.cancel();
   mPhone->releaseImpl();
}

void CloudServiceConfigInterface::Release()
{
   StackLog(<< "CloudServiceConfigInterface::Release(): " << this << " phone: " << mPhone);
   mTimer.cancel();
   mPhone->getSdkModuleThread().safeDelete(this);
}

void CloudServiceConfigInterface::post(ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

int CloudServiceConfigInterface::setServerInfo(const ServiceConfigSettings& serviceConfigSettings, const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo)
{
   post(resip::resip_bind(&CloudServiceConfigInterface::setServerInfoImpl, this, serviceConfigSettings, serverInfo));
   return kSuccess;
}

int CloudServiceConfigInterface::setServerInfoImpl(const ServiceConfigSettings& serviceConfigSettings, const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo)
{
   StackLog(<< "CloudServiceConfigInterface::setServerInfoImpl(): serviceConfigSettings: " << CPCAPI2::CloudServiceConfig::get_debug_string(serviceConfigSettings).c_str() << " serverInfo: " << CPCAPI2::OrchestrationServer::get_debug_string(serverInfo).c_str());
   mTimer.expires_from_now(60000);
   mTimer.async_wait(this, CLOUD_SERVICE_CONFIG_REREG_TIMER_ID, NULL);

   ServerInfo localServerInfo = serverInfo;
   ServerRegInfo regInfo;
   regInfo.serverInfo = localServerInfo;
   regInfo.settings = serviceConfigSettings;
   bool regInfoExists = false;
   std::vector<ServerRegInfo>::iterator itRegInfo = mServerRegistrations.begin();
   for (; itRegInfo != mServerRegistrations.end(); ++itRegInfo)
   {
      if (itRegInfo->serverInfo.region == serverInfo.region &&
         itRegInfo->serverInfo.uri == serverInfo.uri)
      {
         itRegInfo->serverInfo.services = serverInfo.services;
         regInfoExists = true;
         break;
      }
   }
   if (!regInfoExists)
   {
      mServerRegistrations.push_back(regInfo);
   }

   assert(mOrchServer != NULL);
   int retVal = doOrchSetServerInfo_Local(localServerInfo);
   setServerTtlImpl(regInfo.settings, regInfo.serverInfo);
   return retVal;
}

int CloudServiceConfigInterface::setServerTtl(const ServiceConfigSettings& serviceConfigSettings, const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo)
{
   post(resip::resip_bind(&CloudServiceConfigInterface::setServerTtlImpl, this, serviceConfigSettings, serverInfo));
   return kSuccess;
}

int CloudServiceConfigInterface::setServerTtlImpl(const ServiceConfigSettings& serviceConfigSettings, const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo)
{
   // StackLog(<< "CloudServiceConfigInterface::setServerTtlImpl(): authServerUrl: " << serviceConfigSettings.authServerUrl << ", orchServerUrl: " << serviceConfigSettings.orchestrationServerUrl << ", region: " << serverInfo.region << ", uri: " << serverInfo.uri << ", watchdog: " << (serverInfo.watchdog ? "true" : "false") << ", ttlSeconds: " << serverInfo.ttlSeconds);
   assert(mOrchServer != NULL);
   /*int retVal = */doOrchSetServerTtl_Local(serviceConfigSettings, serverInfo);
   return kSuccess;
}
 
int CloudServiceConfigInterface::setHandler(CloudServiceConfigHandler* handler)
{
   setAppHandler(0, handler);
   return kSuccess;
}
   
#ifdef CPCAPI2_AUTO_TEST
   
int CloudServiceConfigInterface::stopTtlService()
{
   post(resip::resip_bind(&CloudServiceConfigInterface::stopTtlServiceImpl, this));
   return kSuccess;
}
   
int CloudServiceConfigInterface::stopTtlServiceImpl()
{
   mTimer.cancel();
   return kSuccess;
}
   
int CloudServiceConfigInterface::removeServerInfo(const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo)
{
   post(resip::resip_bind(&CloudServiceConfigInterface::removeServerInfoImpl, this, serverInfo));
   return kSuccess;
}
   
int CloudServiceConfigInterface::removeServerInfoImpl(const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo)
{
   std::vector<ServerRegInfo>::const_iterator it = mServerRegistrations.begin();
   for (; it != mServerRegistrations.end(); ++it)
   {
      if (it->serverInfo.region == serverInfo.region && it->serverInfo.uri == serverInfo.uri)
      {
         mServerRegistrations.erase(it);
         break;
      }
   }
   return kSuccess;
}

#endif
   
int CloudServiceConfigInterface::doOrchSetServerInfo_Local(const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo)
{
   mOrchServer->setServerInfo(serverInfo);

   SetServerInfoResult args;
   args.success = true;
   fireEvent(cpcFunc(CloudServiceConfigHandler::onSetServerInfoSuccess), 0, args);
   return kSuccess;
}

int CloudServiceConfigInterface::doOrchSetServerTtl_Local(const ServiceConfigSettings& serviceConfigSettings, const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo)
{
   // StackLog(<< "CloudServiceConfigInterface::doOrchSetServerTtl_Local(): authServerUrl: " << serviceConfigSettings.authServerUrl << ", orchServerUrl: " << serviceConfigSettings.orchestrationServerUrl << ", region: " << serverInfo.region << ", uri: " << serverInfo.uri << ", watchdog: " << (serverInfo.watchdog ? "true" : "false") << ", ttlSeconds: " << serverInfo.ttlSeconds);
   const uint64_t ttlSec = 90;
   mOrchServer->setServerTtl(serverInfo.uri, ttlSec);
   return kSuccess;
}
   
void CloudServiceConfigInterface::onTimer(unsigned short timerId, void* appState)
{
   // StackLog(<< "CloudServiceConfigInterface::onTimer()");
   std::vector<ServerRegInfo>::const_iterator it = mServerRegistrations.begin();
   for (; it != mServerRegistrations.end(); ++it)
   {
      setServerTtlImpl(it->settings, it->serverInfo);
   }

   mTimer.async_wait(this, CLOUD_SERVICE_CONFIG_REREG_TIMER_ID, NULL);
}

cpc::string get_debug_string(const CPCAPI2::CloudServiceConfig::ServiceConfigSettings& settings)
{
   std::stringstream ss;
   ss << "authServerUrl: " << settings.authServerUrl.c_str() << " orchestrationServerUrl: " << settings.orchestrationServerUrl.c_str() << " username: " << settings.username.c_str() << " password: " << settings.password.c_str() << " authToken: " << settings.authToken.c_str();
   return ss.str().c_str();
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::CloudServiceConfig::ServiceConfigSettings& settings)
{
   os << CPCAPI2::CloudServiceConfig::get_debug_string(settings);
   return os;
}

}

}

#endif
