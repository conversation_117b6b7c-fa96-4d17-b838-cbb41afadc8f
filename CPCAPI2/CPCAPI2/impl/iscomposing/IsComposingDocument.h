#pragma once

#if !defined(__CPCAPI2_ISCOMPOSING_DOCUMENT_H__)
#define __CPCAPI2_ISCOMPOSING_DOCUMENT_H__

#include "iscomposing/IsComposingTypes.h"

#include <cpcstl/string.h>
#include <resip/stack/Mime.hxx>

namespace CPCAPI2
{
namespace IsComposing
{

/**
 * XML parser and generator for the IsComposing format (RFC 3994)
 */
class IsComposingDocument
{
public:
   IsComposingDocument();
   ~IsComposingDocument();

   IsComposingMessageState getState() const { return state; }
   tm getLastActive() const { return lastActive; }
   resip::Mime getContentType() const { return contentType; }
   int getRefresh() const { return refresh; }

   void setState(IsComposingMessageState state) { this->state = state; }
   void setLastActive(const tm& lastActive) { this->lastActive = lastActive; }
   void setContentType(const resip::Mime& contentType) { this->contentType = contentType; }
   void setRefresh(int refresh) { this->refresh = refresh; }

   cpc::string toString() const;

   static IsComposingDocument* parse(const cpc::string& xml);

private:
   static cpc::string stateToString(IsComposingMessageState state);
   static cpc::string refreshToString(int refresh);
   static IsComposingMessageState stringToState(const cpc::string& stateStr);
   static int stringToRefresh(const cpc::string& refreshStr);

   IsComposingMessageState state;
   tm lastActive;
   resip::Mime contentType;
   int refresh;
};

}
}

#endif // __CPCAPI2_ISCOMPOSING_DOCUMENT_H__
