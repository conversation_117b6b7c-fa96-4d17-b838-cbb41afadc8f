#include "brand_branded.h"

#include "interface/experimental/confbridge/ConferenceRegistrar.h"

#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_REGISTRAR_MODULE == 1)
#include "../phone/PhoneInterface.h"
#include "ConferenceRegistrarManagerInterface.h"
#endif

namespace CPCAPI2
{
   namespace ConferenceBridge
   {
      ConferenceRegistrar* ConferenceRegistrar::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_REGISTRAR_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<ConferenceRegistrarManagerInterface>(phone, "ConferenceRegistrarManagerInterface");
#else
         return NULL;
#endif
      }

   }
}
