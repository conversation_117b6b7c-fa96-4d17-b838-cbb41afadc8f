#pragma once

#ifndef CPCAPI2_CONFERENCE_BRIDGE_HANDLER_INTERNAL_H
#define CPCAPI2_CONFERENCE_BRIDGE_HANDLER_INTERNAL_H

#include <confbridge/ConferenceBridgeTypes.h>
#include <confbridge/ConferenceBridgeHandler.h>

namespace CPCAPI2
{

namespace ConferenceBridge
{
   struct ConferenceParticipantMediaStatistics
   {
      CPCAPI2::PeerConnection::MediaStreamHandle mediaStream;
      int rtpPacketCount = 0;
      int rtcpPacketCount = 0;
   };

   struct ConferenceBridgeMediaStatistics
   {
      ConferenceHandle conference;
      ConferenceParticipantHandle participant = 0;
      ConferenceParticipantHandle owner = 0;
      CPCAPI2::PeerConnection::PeerConnectionHandle peerConnection = 0;
      cpc::string label;
      cpc::string address;
      cpc::string displayName;
      bool hasFloor = false;
      cpc::vector<ConferenceParticipantMediaStatistics> streamStats;
   };

   struct ConferenceBridgeMediaStatisticsEvent
   {
      cpc::vector<ConferenceBridgeMediaStatistics> bridgeMediaStats;
   };

   /**
    * Private interface for internal methods on the SDK Observers.
   */
   class ConferenceBridgeHandlerInternal : public ConferenceBridgeHandler
   {

   public:

#ifdef CPCAPI2_AUTO_TEST
      virtual int onConferenceBridgeMediaStatistics(ConferenceHandle conference, const ConferenceBridgeMediaStatisticsEvent& args) { return kSuccess; }
#endif

   };
}

}

#endif /* CPCAPI2_CONFERENCE_BRIDGE_HANDLER_INTERNAL_H */
