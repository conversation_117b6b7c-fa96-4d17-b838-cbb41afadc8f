#include "IMediaManager.h"
#include "impl/jsonrpc/IJsonRpcServerModuleProcessing.h"

#ifndef JSONRPC_CPCAPI2_MEDIAMANAGER_MEDIAMANAGERSERVER_H
#define JSONRPC_CPCAPI2_MEDIAMANAGER_MEDIAMANAGERSERVER_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace MediaManager {
class MediaManagerServer
{
public:
    MediaManagerServer(::CPCAPI2::IJsonRpcServerModuleProcessing& transport, IMediaManager& impl) :
        mTransport(transport),
        mImpl(impl)
    {
        mTransport.registerFunction("MediaManager::initializeMediaStack", jsonrpccxx::methodHandle(&IMediaManager::initializeMediaStack, mImpl), {{ "phoneHandle" }});
    }

private:
    ::CPCAPI2::IJsonRpcServerModuleProcessing& mTransport;
    IMediaManager& mImpl;
}; // class MediaManagerServer
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace MediaManager
#endif // JSONRPC_CPCAPI2_MEDIAMANAGER_MEDIAMANAGERSERVER_H
