#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>
#include "AnswerMode.h"

#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_ANSWERMODESETTINGS_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_ANSWERMODESETTINGS_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class AnswerModeSettings
  {
  public:
    std::optional<AnswerMode> mode;
    std::optional<bool> privileged;
    std::optional<bool> required;
    std::optional<bool> challenge;
    std::optional<bool> allowManual;
    std::optional<bool> allowAuto;
    std::optional<bool> allowPrivileged;

    AnswerModeSettings() = default;

    AnswerModeSettings(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const AnswerModeSettings& o)
    {
      j = nlohmann::json::object();
       if (o.mode) { j["mode"] = *(o.mode); }
     if (o.privileged) { j["privileged"] = *(o.privileged); }
     if (o.required) { j["required"] = *(o.required); }
     if (o.challenge) { j["challenge"] = *(o.challenge); }
     if (o.allowManual) { j["allowManual"] = *(o.allowManual); }
     if (o.allowAuto) { j["allowAuto"] = *(o.allowAuto); }
     if (o.allowPrivileged) { j["allowPrivileged"] = *(o.allowPrivileged); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, AnswerModeSettings& o)
    {
     if (j.contains("mode")) { o.mode = j.at("mode"); }
     if (j.contains("privileged")) { o.privileged = j.at("privileged"); }
     if (j.contains("required")) { o.required = j.at("required"); }
     if (j.contains("challenge")) { o.challenge = j.at("challenge"); }
     if (j.contains("allowManual")) { o.allowManual = j.at("allowManual"); }
     if (j.contains("allowAuto")) { o.allowAuto = j.at("allowAuto"); }
     if (j.contains("allowPrivileged")) { o.allowPrivileged = j.at("allowPrivileged"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_ANSWERMODESETTINGS_H
