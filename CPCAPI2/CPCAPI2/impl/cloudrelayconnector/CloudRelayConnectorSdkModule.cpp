#include "brand_branded.h"

#include "interface/experimental/cloudrelayconnector/CloudRelayConnector.h"

#if (CPCAPI2_BRAND_CLOUD_RELAY_CONNECTOR_MODULE == 1)
#include "CloudRelayConnectorInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace CloudRelayConnector
{
   CloudRelayConnector* CloudRelayConnector::getInterface(Phone* cpcPhone)
   {
#if (CPCAPI2_BRAND_CLOUD_RELAY_CONNECTOR_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<CloudRelayConnectorInterface>(phone, "CloudRelayConnector");
#else
      return NULL;
#endif
   }

}
}
