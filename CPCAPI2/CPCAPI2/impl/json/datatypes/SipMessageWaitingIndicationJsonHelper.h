// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef SipMessageWaitingIndication_JSON_HELPER_H
#define SipMessageWaitingIndication_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/public/mwi/SipMessageWaitingIndicationManager.h>
#include <interface/public/mwi/SipMessageWaitingIndicationHandler.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipMessageWaitingIndication::SipMWISubscriptionSettings& v)
    {
      w.StartObject();
      Write(w, "expiresSeconds", v.expiresSeconds);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipMessageWaitingIndication::SipMWISubscriptionSettings& v)
    {
      Read(json, "expiresSeconds", v.expiresSeconds);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipMessageWaitingIndication::SipMessageWaitingIndicationManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipMessageWaitingIndication::SipMessageWaitingIndicationManager& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipMessageWaitingIndication::NewMWISubscriptionEvent& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipMessageWaitingIndication::NewMWISubscriptionEvent& v)
    {
      Read(json, "account", v.account);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipMessageWaitingIndication::MWISubscriptionEndedEvent& v)
    {
      w.StartObject();
      Write(w, "endReason", v.endReason);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipMessageWaitingIndication::MWISubscriptionEndedEvent& v)
    {
      Read(json, "endReason", v.endReason);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipMessageWaitingIndication::MessageWaitingItem& v)
    {
      w.StartObject();
      Write(w, "type", v.type);
      Write(w, "newMessageCount", v.newMessageCount);
      Write(w, "oldMessageCount", v.oldMessageCount);
      Write(w, "newUrgentMessageCount", v.newUrgentMessageCount);
      Write(w, "oldUrgentMessageCount", v.oldUrgentMessageCount);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipMessageWaitingIndication::MessageWaitingItem& v)
    {
      Read(json, "type", v.type);
      Read(json, "newMessageCount", v.newMessageCount);
      Read(json, "oldMessageCount", v.oldMessageCount);
      Read(json, "newUrgentMessageCount", v.newUrgentMessageCount);
      Read(json, "oldUrgentMessageCount", v.oldUrgentMessageCount);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipMessageWaitingIndication::IncomingMWIStatusEvent& v)
    {
      w.StartObject();
      Write(w, "hasMessages", v.hasMessages);
      Write(w, "items", v.items);
      Write(w, "accountHandle", v.accountHandle);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipMessageWaitingIndication::IncomingMWIStatusEvent& v)
    {
      Read(json, "hasMessages", v.hasMessages);
      Read(json, "items", v.items);
      Read(json, "accountHandle", v.accountHandle);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipMessageWaitingIndication::MWISubscriptionStateChangedEvent& v)
    {
      w.StartObject();
      Write(w, "subscriptionState", v.subscriptionState);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipMessageWaitingIndication::MWISubscriptionStateChangedEvent& v)
    {
      Read(json, "subscriptionState", v.subscriptionState);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipMessageWaitingIndication::ErrorEvent& v)
    {
      w.StartObject();
      Write(w, "errorText", v.errorText);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipMessageWaitingIndication::ErrorEvent& v)
    {
      Read(json, "errorText", v.errorText);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipMessageWaitingIndication::SipMessageWaitingIndicationHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipMessageWaitingIndication::SipMessageWaitingIndicationHandler& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // SipMessageWaitingIndication_JSON_HELPER_H