// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef PushNotification_JSON_HELPER_H
#define PushNotification_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/experimental/pushnotification/PushNotificationTypes.h>
#include <interface/experimental/pushnotification/PushNotificationClientHandler.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushNotification::PushNotificationRegistrationInfo& v)
    {
      w.StartObject();
      Write(w, "pushNetworkType", v.pushNetworkType);
      Write(w, "deviceToken", v.deviceToken);
      Write(w, "apnInfo", v.apnInfo);
      Write(w, "fcmInfo", v.fcmInfo);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushNotification::PushNotificationRegistrationInfo& v)
    {
      Read(json, "pushNetworkType", v.pushNetworkType);
      Read(json, "deviceToken", v.deviceToken);
      Read(json, "apnInfo", v.apnInfo);
      Read(json, "fcmInfo", v.fcmInfo);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushNotification::PushNotificationRegistrationInfo::APNInfo& v)
    {
      w.StartObject();
      Write(w, "apnsTopic", v.apnsTopic);
      Write(w, "apnsTopicPushKit", v.apnsTopicPushKit);
      Write(w, "pushKitToken", v.pushKitToken);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushNotification::PushNotificationRegistrationInfo::APNInfo& v)
    {
      Read(json, "apnsTopic", v.apnsTopic);
      Read(json, "apnsTopicPushKit", v.apnsTopicPushKit);
      Read(json, "pushKitToken", v.pushKitToken);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushNotification::PushNotificationRegistrationInfo::FCMInfo& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushNotification::PushNotificationRegistrationInfo::FCMInfo& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushNotification::PushProviderSettings& v)
    {
      w.StartObject();
      Write(w, "pushNetworkType", v.pushNetworkType);
      Write(w, "apnSettings", v.apnSettings);
      Write(w, "apnSandboxSettings", v.apnSandboxSettings);
      Write(w, "fcmSettings", v.fcmSettings);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushNotification::PushProviderSettings& v)
    {
      Read(json, "pushNetworkType", v.pushNetworkType);
      Read(json, "apnSettings", v.apnSettings);
      Read(json, "apnSandboxSettings", v.apnSandboxSettings);
      Read(json, "fcmSettings", v.fcmSettings);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushNotification::PushProviderSettings::APNSettings& v)
    {
      w.StartObject();
      Write(w, "apnUrl", v.apnUrl);
      Write(w, "apnKey", v.apnKey);
      Write(w, "p8file", v.p8file);
      Write(w, "authKeyId", v.authKeyId);
      Write(w, "teamId", v.teamId);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushNotification::PushProviderSettings::APNSettings& v)
    {
      Read(json, "apnUrl", v.apnUrl);
      Read(json, "apnKey", v.apnKey);
      Read(json, "p8file", v.p8file);
      Read(json, "authKeyId", v.authKeyId);
      Read(json, "teamId", v.teamId);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushNotification::PushProviderSettings::FCMSettings& v)
    {
      w.StartObject();
      Write(w, "fcmUrl", v.fcmUrl);
      Write(w, "fcmKey", v.fcmKey);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushNotification::PushProviderSettings::FCMSettings& v)
    {
      Read(json, "fcmUrl", v.fcmUrl);
      Read(json, "fcmKey", v.fcmKey);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushNotification::CustomDataField& v)
    {
      w.StartObject();
      Write(w, "name", v.name);
      Write(w, "dataType", v.dataType);
      Write(w, "boolVal", v.boolVal);
      Write(w, "intVal", v.intVal);
      Write(w, "doubleVal", v.doubleVal);
      Write(w, "stringVal", v.stringVal);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushNotification::CustomDataField& v)
    {
      Read(json, "name", v.name);
      Read(json, "dataType", v.dataType);
      Read(json, "boolVal", v.boolVal);
      Read(json, "intVal", v.intVal);
      Read(json, "doubleVal", v.doubleVal);
      Read(json, "stringVal", v.stringVal);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushNotification::PushNotificationRequest& v)
    {
      w.StartObject();
      Write(w, "title_loc_key", v.title_loc_key);
      Write(w, "title_loc_args", v.title_loc_args);
      Write(w, "body", v.body);
      Write(w, "customData", v.customData);
      Write(w, "options", v.options);
      Write(w, "apnInfo", v.apnInfo);
      Write(w, "fcmInfo", v.fcmInfo);
      Write(w, "wsInfo", v.wsInfo);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushNotification::PushNotificationRequest& v)
    {
      Read(json, "title_loc_key", v.title_loc_key);
      Read(json, "title_loc_args", v.title_loc_args);
      Read(json, "body", v.body);
      Read(json, "customData", v.customData);
      Read(json, "options", v.options);
      Read(json, "apnInfo", v.apnInfo);
      Read(json, "fcmInfo", v.fcmInfo);
      Read(json, "wsInfo", v.wsInfo);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushNotification::PushNotificationRequest::APNInfo& v)
    {
      w.StartObject();
      Write(w, "usePushKit", v.usePushKit);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushNotification::PushNotificationRequest::APNInfo& v)
    {
      Read(json, "usePushKit", v.usePushKit);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushNotification::PushNotificationRequest::FCMInfo& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushNotification::PushNotificationRequest::FCMInfo& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushNotification::PushNotificationRequest::WSInfo& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushNotification::PushNotificationRequest::WSInfo& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushNotification::PushNotificationEvent& v)
    {
      w.StartObject();
      Write(w, "jsonDocument", v.jsonDocument);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushNotification::PushNotificationEvent& v)
    {
      Read(json, "jsonDocument", v.jsonDocument);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushNotification::PushNotificationClientHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushNotification::PushNotificationClientHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushNotification::PushNotificationClientHandlerStub& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushNotification::PushNotificationClientHandlerStub& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // PushNotification_JSON_HELPER_H