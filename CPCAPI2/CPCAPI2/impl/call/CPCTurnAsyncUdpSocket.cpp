#include "CPCTurnAsyncUdpSocket.h"
#include "brand_branded.h"

using namespace CPCAPI2;

CPCTurnAsyncUdpSocket::CPCTurnAsyncUdpSocket(
   resip::HighPerfReactor& reactor,
   resip::Resolver <resip::HighPerfReactor>& resolver,
   reTurn::TurnAsyncSocketHandler* turnAsyncSocketHandler,
   const resip::Data& address, 
   unsigned short port)
   : TurnAsyncUdpSocket(reactor, resolver, turnAsyncSocketHandler, address, port)
{
}

reTurn::StunMessage* CPCTurnAsyncUdpSocket::createNewStunMessage(UInt16 stunclass, UInt16 method, bool addAuthInfo )
{
   reTurn::StunMessage *result = TurnAsyncUdpSocket::createNewStunMessage( stunclass, method, addAuthInfo );
   return result;
}