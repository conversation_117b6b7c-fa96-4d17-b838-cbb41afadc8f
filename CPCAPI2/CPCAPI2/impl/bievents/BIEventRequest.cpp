#include <brand_branded.h>

#include <sstream>
#include <errno.h>

#include <util/strettorpc/RPCIdentifier.h>

#include <util/MachineIdentification.h>
#include <util/PlatformUtils.h>
#include <util/cpc_logger.h>
#include <util/LogSubsystems.h>

#include "BIEventRequest.h"
#include "BIEventsInternalTypes.h"

#if (CPCAPI2_BRAND_BIEVENTS_MODULE == 1)

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::BI_EVENTS

using namespace CPCAPI2::BIEvents;

BIEventRequest::BIEventRequest(
   const std::string & sessionID,
   const std::string & authToken,
   const std::string & buildStamp,
   const std::string & strettoUserNameOrID,
   const std::list<BIEventRecord>& eventRecords )
   : CPCAPI2::RPCRequest( "event_post", sessionID, authToken )
{
   RPCValue eventsVal;
   eventsVal.m_Type = RPCValueType_array;

   // Add each of the events to the param list for marshaling.
   // For now there's only one of these events per body.
   // TODO: add an optimization here
   for( std::list< BIEventRecord >::const_iterator iter = eventRecords.begin() ;
        iter != eventRecords.end() ; ++iter )
   {
      // Deserialize each event
      BIEventHeader eventHeader;
      BIEventBody   eventBody;
      if( !iter->Deserialize( eventHeader, eventBody ))
         continue;

      // Success or failure, we're creating one.
      RPCValue eventVal;
      eventVal.m_Type = RPCValueType_object;

      // First try to convert the timestamp. This is a canary of sorts
      RPCTimestamp eTimestamp( eventHeader.MillisSinceEpoch );
      std::string timestampStr;
      if( !eTimestamp.toString( timestampStr ))
      {
         ErrLog(<< "BI timestamp converstaion failure; attempting to report problem to BI server. MillisSinceEpoch " << eventHeader.MillisSinceEpoch);
      
         // Failed to convert the timestamp. Something is weird. report
         // an error to the BI Server by manually constructing a DEBUG
         // type message (this facility can be expanded later)
         std::stringstream ss;
         ss << "gmtime failure, error code is " << errno;

         eTimestamp = RPCTimestamp(); // reset to current time
         if( !eTimestamp.toString( timestampStr ))
         {
            ErrLog(<< "BI timestamp conversation still failing; bailing out");
            continue; // still failing. just bail on the event
         }
         
         eventVal.m_ObjectVal[ "source" ] = RPCValue( "DEBUG" );
         eventVal.m_ObjectVal[ "type"   ] = RPCValue( "DEBUG" );
         eventVal.m_ObjectVal[ "time"   ] = RPCValue( timestampStr );

         RPCValue tagsValue;
         tagsValue.m_Type = RPCValueType_array;
         tagsValue.m_ArrayVal.push_back( RPCValue( "error" ));
         eventVal.m_ObjectVal[ "tags" ] = tagsValue;

         RPCValue dataVal;
         dataVal.m_Type = RPCValueType_object;

         RPCValue rVal;
         rVal.m_Type = RPCValueType_string;
         rVal.m_StringVal = ss.str();
         dataVal.m_ObjectVal[ "Message" ] = rVal;

         eventVal.m_ObjectVal[ "data" ] = dataVal;
      }
      else
      {
         StackLog(<< "BIEventRequest for " << eventHeader.Summary);
      
         // timestamp succeeded, so continue with other attributes.
         std::string sourceString;
         sourceString.assign( eventHeader.Source, 5 );
         eventVal.m_ObjectVal[ "source" ] = RPCValue( sourceString );

         std::string typeString;
         typeString.assign( eventHeader.Type, 5 );
         eventVal.m_ObjectVal[ "type"   ] = RPCValue( typeString );

         eventVal.m_ObjectVal[ "time" ] = RPCValue( timestampStr );

         if( eventHeader.Group != 0 )
            eventVal.m_ObjectVal[ "group" ] = RPCValue(( int64_t ) eventHeader.Group );

         if( eventHeader.Context != 0 )
            eventVal.m_ObjectVal[ "context" ] = RPCValue(( int64_t ) eventHeader.Context );

         if( eventHeader.Summary.size() > 0 )
            eventVal.m_ObjectVal[ "summary" ] = RPCValue( eventHeader.Summary );

         if( eventHeader.Labels.size() > 0 )
         {
            // Add the labels as an array under the "tags" field
            RPCValue tagsValue;
            tagsValue.m_Type = RPCValueType_array;

            for( cpc::vector< cpc::string >::const_iterator iter = eventHeader.Labels.begin() ;
                 iter != eventHeader.Labels.end() ; ++iter )
            {
               tagsValue.m_ArrayVal.push_back( RPCValue( *iter ));
            }
            eventVal.m_ObjectVal[ "tags" ] = tagsValue;
         }

         // The important part.
         if( eventBody.size() > 0 )
         {
            // Dynamically build the json document with elements which are built from
            // the information present in the eventBody.
            RPCValue dataVal;
            dataVal.m_Type = RPCValueType_object;

            for( cpc::vector< BIPair >::const_iterator iter = eventBody.begin() ; iter != eventBody.end() ; ++iter )
            {
               if( iter->Name.size() <= 0 )
                  continue;

               RPCValue rVal;

               // Kind of pointless conversion here. Possible to unite these types?
               switch( iter->Value.ValueType )
               {
               case BIValueType_bool:
                  rVal.m_Type = RPCValueType_bool;
                  rVal.m_BoolVal = iter->Value.BoolValue;
                  dataVal.m_ObjectVal[ iter->Name.c_str() ] = rVal;
                  break;
               case BIValueType_int:
                  rVal.m_Type = RPCValueType_int;
                  rVal.m_IntVal = iter->Value.IntValue;
                  dataVal.m_ObjectVal[ iter->Name.c_str() ] = rVal;
                  break;
               case BIValueType_double:
                  rVal.m_Type = RPCValueType_double;
                  rVal.m_DoubleVal = iter->Value.DoubleValue;
                  dataVal.m_ObjectVal[ iter->Name.c_str() ] = rVal;
                  break;
               case BIValueType_string:
                  rVal.m_Type = RPCValueType_string;
                  rVal.m_StringVal = iter->Value.StringValue.c_str();
                  dataVal.m_ObjectVal[ iter->Name.c_str() ] = rVal;
                  break;
               default:
                  // unknown/unsupported type
                  break;
               }
            }

            eventVal.m_ObjectVal[ "data" ] = dataVal;
         }
      }

      eventsVal.m_ArrayVal.push_back( eventVal );
   }

   addParameter( RPCParam( "events", eventsVal ));
}

#endif
