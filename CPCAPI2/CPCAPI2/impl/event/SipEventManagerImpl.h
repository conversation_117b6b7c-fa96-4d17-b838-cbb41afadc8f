#pragma once

#if !defined(CPCAPI2_SIP_EVENT_MANAGER_IMPL_H)
#define CPCAPI2_SIP_EVENT_MANAGER_IMPL_H

#include "cpcapi2defs.h"
#include "event/SipEventManager.h"
#include "event/SipEventSubscriptionHandler.h"
#include "../account/SipAccountImpl.h"
#include "../account/SipAccountAwareFeature.h"
//#include "SipEventSubscriptionCreationInfo.h"

#include <resip/dum/SubscriptionHandler.hxx>

#include <string>

namespace CPCAPI2
{
namespace SipEvent
{
class SipEventSubsriptionHandler;
class SipEventManagerInterface;
class SipEventSubscriptionCreationInfo;
class RLMIContents;

class SipEventManagerImpl : public CPCAPI2::SipAccount::SipAccountAwareFeature,
                            public resip::ClientSubscriptionHandler,
                            public resip::ServerSubscriptionHandler
{
public:
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, SipEventManagerImpl*> AccountMap;
   SipEventManagerImpl(std::shared_ptr<AccountMap> parentMap, CPCAPI2::SipAccount::SipAccountImpl& account, CPCAPI2::SipEvent::SipEventManagerInterface* sipEventIf);
   virtual ~SipEventManagerImpl();

   void addEventType(const std::string& eventType);
   cpc::vector<SipEventSubscriptionHandle> getSubscriptions() const;

   //
   // IAccountAware
   //
   virtual int adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile) OVERRIDE;
   virtual int addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig) OVERRIDE;
   virtual int onDumBeingDestroyed() OVERRIDE;
   virtual int onRegistrationSuccess(const SipAccount::SipRegistrationSuccessEvent& args) OVERRIDE;
   virtual void release() OVERRIDE;
   virtual int registerSdkDialogSetFactory(CPCAPI2::SipAccount::AppDialogSetFactory& factory) OVERRIDE;

   SipEventSubscriptionCreationInfo* getCreationInfo(const SipEventSubscriptionHandle& h) const;
   void addCreationInfo(const SipEventSubscriptionHandle& h, SipEventSubscriptionCreationInfo* ci);
   void removeCreationInfo(const SipEventSubscriptionHandle& h);
   resip::SharedPtr<resip::DialogUsageManager> getDum() { return mDum; }
   void fireSubscriptionStateChanged(SipEventSubscriptionHandle subscription, SubscriptionStateChangedEvent& args, const resip::Data& eventPackage);

   void fireError(const SipEventSubscriptionHandle& subscription, const cpc::string& errorText, const resip::Data& eventPackage);

   // ClientSubscriptionHandler
   //
   virtual void onUpdatePending(resip::ClientSubscriptionHandle, const resip::SipMessage& notify, bool outOfOrder) OVERRIDE;
   virtual void onUpdateActive(resip::ClientSubscriptionHandle, const resip::SipMessage& notify, bool outOfOrder) OVERRIDE;
   virtual void onUpdateExtension(resip::ClientSubscriptionHandle, const resip::SipMessage& notify, bool outOfOrder) OVERRIDE;
   virtual int onRequestRetry(resip::ClientSubscriptionHandle, int retrySeconds, const resip::SipMessage& response) OVERRIDE;
   virtual void onTerminated(resip::ClientSubscriptionHandle, const resip::SipMessage* msg) OVERRIDE;
   virtual void onNewSubscription(resip::ClientSubscriptionHandle, const resip::SipMessage& notify) OVERRIDE;

   // ServerSubscriptionHandler
   //
   virtual void onNewSubscription(resip::ServerSubscriptionHandle, const resip::SipMessage& sub) OVERRIDE;
   virtual void onTerminated(resip::ServerSubscriptionHandle) OVERRIDE;
   virtual void onNotifyRejected(resip::ServerSubscriptionHandle h, const resip::SipMessage& msg) OVERRIDE;
   virtual void onRefresh(resip::ServerSubscriptionHandle, const resip::SipMessage& sub) OVERRIDE;

private:
   SipEventSubscriptionHandle findSipEventSubscriptionHandleByResipHandle(resip::ServerSubscriptionHandle h);
   SipEventSubscriptionHandle findSipEventSubscriptionHandleByResipHandle(resip::ClientSubscriptionHandle h);
   typedef std::map<cpc::string, SipEventResourceInstance> ResourceContentMap;
   void processRlmiContents(const RLMIContents& rlmi, IncomingResourceListEvent& args, ResourceContentMap& mapCidToResourceInstance) const;
   int calcNextRetryInterval();

private:
   std::weak_ptr<AccountMap> mParentMap;
   CPCAPI2::SipAccount::SipAccountImpl& mAccount;
   std::set<std::string> mEventTypes;
   resip::SharedPtr<resip::DialogUsageManager> mDum;
   typedef std::map<SipEventSubscriptionHandle, SipEventSubscriptionCreationInfo*> SipEventSubscriptionCreationInfoMap;
   SipEventSubscriptionCreationInfoMap mCreationInfo;
   CPCAPI2::SipEvent::SipEventManagerInterface* mSipEventIf;
   int mNextRetryIntervalSec;
};
}
}
#endif // CPCAPI2_SIP_EVENT_MANAGER_IMPL_H
