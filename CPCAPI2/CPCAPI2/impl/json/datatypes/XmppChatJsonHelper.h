// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef XmppChat_JSON_HELPER_H
#define XmppChat_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/public/xmpp/XmppChatManager.h>
#include <interface/public/xmpp/XmppChatHandler.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::XmppChatManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::XmppChatManager& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::NewChatEvent& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      Write(w, "chatType", v.chatType);
      Write(w, "remote", v.remote);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::NewChatEvent& v)
    {
      Read(json, "account", v.account);
      Read(json, "chatType", v.chatType);
      Read(json, "remote", v.remote);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::IsComposingMessageEvent& v)
    {
      w.StartObject();
      Write(w, "state", v.state);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::IsComposingMessageEvent& v)
    {
      Read(json, "state", v.state);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::NewMessageEvent& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      Write(w, "message", v.message);
      Write(w, "messageId", v.messageId);
      Write(w, "originId", v.originId);
      Write(w, "threadId", v.threadId);
      Write(w, "from", v.from);
      Write(w, "to", v.to);
      Write(w, "messageContent", v.messageContent);
      Write(w, "htmlText", v.htmlText);
      Write(w, "subject", v.subject);
      Write(w, "timestamp", v.timestamp);
      Write(w, "millisecond", v.millisecond);
      Write(w, "isDelayedDelivery", v.isDelayedDelivery);
      Write(w, "isOutbound", v.isOutbound);
      Write(w, "replaces", v.replaces);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::NewMessageEvent& v)
    {
      Read(json, "account", v.account);
      Read(json, "message", v.message);
      Read(json, "messageId", v.messageId);
      Read(json, "originId", v.originId);
      Read(json, "threadId", v.threadId);
      Read(json, "from", v.from);
      Read(json, "to", v.to);
      Read(json, "messageContent", v.messageContent);
      Read(json, "htmlText", v.htmlText);
      Read(json, "subject", v.subject);
      Read(json, "timestamp", v.timestamp);
      Read(json, "millisecond", v.millisecond);
      Read(json, "isDelayedDelivery", v.isDelayedDelivery);
      Read(json, "isOutbound", v.isOutbound);
      Read(json, "replaces", v.replaces);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::NewReactionEvent& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      Write(w, "message", v.message);
      Write(w, "messageId", v.messageId);
      Write(w, "threadId", v.threadId);
      Write(w, "from", v.from);
      Write(w, "reactionTarget", v.reactionTarget);
      Write(w, "reactions", v.reactions);
      Write(w, "timestamp", v.timestamp);
      Write(w, "millisecond", v.millisecond);
      Write(w, "isDelayedDelivery", v.isDelayedDelivery);
      Write(w, "isOutbound", v.isOutbound);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::NewReactionEvent& v)
    {
      Read(json, "account", v.account);
      Read(json, "message", v.message);
      Read(json, "messageId", v.messageId);
      Read(json, "threadId", v.threadId);
      Read(json, "from", v.from);
      Read(json, "reactionTarget", v.reactionTarget);
      Read(json, "reactions", v.reactions);
      Read(json, "timestamp", v.timestamp);
      Read(json, "millisecond", v.millisecond);
      Read(json, "isDelayedDelivery", v.isDelayedDelivery);
      Read(json, "isOutbound", v.isOutbound);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::NewMessageRetractionEvent& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      Write(w, "message", v.message);
      Write(w, "messageId", v.messageId);
      Write(w, "threadId", v.threadId);
      Write(w, "from", v.from);
      Write(w, "retractTarget", v.retractTarget);
      Write(w, "timestamp", v.timestamp);
      Write(w, "millisecond", v.millisecond);
      Write(w, "isDelayedDelivery", v.isDelayedDelivery);
      Write(w, "isOutbound", v.isOutbound);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::NewMessageRetractionEvent& v)
    {
      Read(json, "account", v.account);
      Read(json, "message", v.message);
      Read(json, "messageId", v.messageId);
      Read(json, "threadId", v.threadId);
      Read(json, "from", v.from);
      Read(json, "retractTarget", v.retractTarget);
      Read(json, "timestamp", v.timestamp);
      Read(json, "millisecond", v.millisecond);
      Read(json, "isDelayedDelivery", v.isDelayedDelivery);
      Read(json, "isOutbound", v.isOutbound);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::MessageDeliveredEvent& v)
    {
      w.StartObject();
      Write(w, "message", v.message);
      Write(w, "messageDeliveryStatus", v.messageDeliveryStatus);
      Write(w, "messageId", v.messageId);
      Write(w, "threadId", v.threadId);
      Write(w, "from", v.from);
      Write(w, "timestamp", v.timestamp);
      Write(w, "millisecond", v.millisecond);
      Write(w, "isDelayedDelivery", v.isDelayedDelivery);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::MessageDeliveredEvent& v)
    {
      Read(json, "message", v.message);
      Read(json, "messageDeliveryStatus", v.messageDeliveryStatus);
      Read(json, "messageId", v.messageId);
      Read(json, "threadId", v.threadId);
      Read(json, "from", v.from);
      Read(json, "timestamp", v.timestamp);
      Read(json, "millisecond", v.millisecond);
      Read(json, "isDelayedDelivery", v.isDelayedDelivery);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::MessageDeliveryErrorEvent& v)
    {
      w.StartObject();
      Write(w, "message", v.message);
      Write(w, "messageDeliveryStatus", v.messageDeliveryStatus);
      Write(w, "from", v.from);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::MessageDeliveryErrorEvent& v)
    {
      Read(json, "message", v.message);
      Read(json, "messageDeliveryStatus", v.messageDeliveryStatus);
      Read(json, "from", v.from);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::MessageDisplayedEvent& v)
    {
      w.StartObject();
      Write(w, "message", v.message);
      Write(w, "messageDisplayStatus", v.messageDisplayStatus);
      Write(w, "from", v.from);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::MessageDisplayedEvent& v)
    {
      Read(json, "message", v.message);
      Read(json, "messageDisplayStatus", v.messageDisplayStatus);
      Read(json, "from", v.from);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::MessageReadEvent& v)
    {
      w.StartObject();
      Write(w, "messageId", v.messageId);
      Write(w, "threadId", v.threadId);
      Write(w, "from", v.from);
      Write(w, "timestamp", v.timestamp);
      Write(w, "millisecond", v.millisecond);
      Write(w, "isDelayedDelivery", v.isDelayedDelivery);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::MessageReadEvent& v)
    {
      Read(json, "messageId", v.messageId);
      Read(json, "threadId", v.threadId);
      Read(json, "from", v.from);
      Read(json, "timestamp", v.timestamp);
      Read(json, "millisecond", v.millisecond);
      Read(json, "isDelayedDelivery", v.isDelayedDelivery);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::SendMessageSuccessEvent& v)
    {
      w.StartObject();
      Write(w, "message", v.message);
      Write(w, "messageId", v.messageId);
      Write(w, "threadId", v.threadId);
      Write(w, "replaces", v.replaces);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::SendMessageSuccessEvent& v)
    {
      Read(json, "message", v.message);
      Read(json, "messageId", v.messageId);
      Read(json, "threadId", v.threadId);
      Read(json, "replaces", v.replaces);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::SendMessageFailureEvent& v)
    {
      w.StartObject();
      Write(w, "message", v.message);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::SendMessageFailureEvent& v)
    {
      Read(json, "message", v.message);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::NotifyMessageDeliveredSuccessEvent& v)
    {
      w.StartObject();
      Write(w, "notification", v.notification);
      Write(w, "message", v.message);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::NotifyMessageDeliveredSuccessEvent& v)
    {
      Read(json, "notification", v.notification);
      Read(json, "message", v.message);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::NotifyMessageDisplayedSuccessEvent& v)
    {
      w.StartObject();
      Write(w, "notification", v.notification);
      Write(w, "message", v.message);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::NotifyMessageDisplayedSuccessEvent& v)
    {
      Read(json, "notification", v.notification);
      Read(json, "message", v.message);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::ChatEndedEvent& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      Write(w, "chat", v.chat);
      Write(w, "endReason", v.endReason);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::ChatEndedEvent& v)
    {
      Read(json, "account", v.account);
      Read(json, "chat", v.chat);
      Read(json, "endReason", v.endReason);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::ChatDiscoEvent& v)
    {
      w.StartObject();
      Write(w, "remoteJID", v.remoteJID);
      Write(w, "replaceMessageSupported", v.replaceMessageSupported);
      Write(w, "messageRetractionSupported", v.messageRetractionSupported);
      Write(w, "messageReactionsSupported", v.messageReactionsSupported);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::ChatDiscoEvent& v)
    {
      Read(json, "remoteJID", v.remoteJID);
      Read(json, "replaceMessageSupported", v.replaceMessageSupported);
      Read(json, "messageRetractionSupported", v.messageRetractionSupported);
      Read(json, "messageReactionsSupported", v.messageReactionsSupported);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::ChatDiscoErrorEvent& v)
    {
      w.StartObject();
      Write(w, "remoteJID", v.remoteJID);
      Write(w, "reason", v.reason);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::ChatDiscoErrorEvent& v)
    {
      Read(json, "remoteJID", v.remoteJID);
      Read(json, "reason", v.reason);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::ErrorEvent& v)
    {
      w.StartObject();
      Write(w, "errorText", v.errorText);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::ErrorEvent& v)
    {
      Read(json, "errorText", v.errorText);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::ValidateChatHandleEvent& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      Write(w, "chatHandleValid", v.chatHandleValid);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::ValidateChatHandleEvent& v)
    {
      Read(json, "account", v.account);
      Read(json, "chatHandleValid", v.chatHandleValid);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppChat::XmppChatHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppChat::XmppChatHandler& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // XmppChat_JSON_HELPER_H