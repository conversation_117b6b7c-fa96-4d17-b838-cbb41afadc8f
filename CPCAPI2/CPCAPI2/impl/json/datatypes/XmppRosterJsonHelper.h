// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef XmppRoster_JSON_HELPER_H
#define XmppRoster_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/public/xmpp/XmppRoster.h>
#include <interface/public/xmpp/XmppRosterHandler.h>
#include <interface/public/xmpp/XmppRosterTypes.h>
#include <interface/experimental/xmpp/XmppRosterJsonProxyStateHandler.h>
#include <impl/xmpp/XmppRosterHandlerInternal.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::ResourceItem& v)
    {
      w.StartObject();
      Write(w, "resource", v.resource);
      Write(w, "priority", v.priority);
      Write(w, "presenceType", v.presenceType);
      Write(w, "presenceStatusText", v.presenceStatusText);
      Write(w, "userActivityGeneralType", v.userActivityGeneralType);
      Write(w, "userActivitySpecificType", v.userActivitySpecificType);
      Write(w, "userActivityText", v.userActivityText);
      Write(w, "isCiscoRichPresence", v.isCiscoRichPresence);
      Write(w, "isCiscoCustomStatus", v.isCiscoCustomStatus);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::ResourceItem& v)
    {
      Read(json, "resource", v.resource);
      Read(json, "priority", v.priority);
      Read(json, "presenceType", v.presenceType);
      Read(json, "presenceStatusText", v.presenceStatusText);
      Read(json, "userActivityGeneralType", v.userActivityGeneralType);
      Read(json, "userActivitySpecificType", v.userActivitySpecificType);
      Read(json, "userActivityText", v.userActivityText);
      Read(json, "isCiscoRichPresence", v.isCiscoRichPresence);
      Read(json, "isCiscoCustomStatus", v.isCiscoCustomStatus);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::RosterItem& v)
    {
      w.StartObject();
      Write(w, "address", v.address);
      Write(w, "displayName", v.displayName);
      Write(w, "groups", v.groups);
      Write(w, "subscription", v.subscription);
      Write(w, "resources", v.resources);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::RosterItem& v)
    {
      Read(json, "address", v.address);
      Read(json, "displayName", v.displayName);
      Read(json, "groups", v.groups);
      Read(json, "subscription", v.subscription);
      Read(json, "resources", v.resources);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::XmppRosterManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::XmppRosterManager& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::XmppCannedPresence& v)
    {
      w.StartObject();
      Write(w, "resource", v.resource);
      Write(w, "status", v.status);
      Write(w, "note", v.note);
      Write(w, "priority", v.priority);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::XmppCannedPresence& v)
    {
      Read(json, "resource", v.resource);
      Read(json, "status", v.status);
      Read(json, "note", v.note);
      Read(json, "priority", v.priority);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::XmppRosterUpdateEvent& v)
    {
      w.StartObject();
      Write(w, "fullUpdate", v.fullUpdate);
      Write(w, "added", v.added);
      Write(w, "updated", v.updated);
      Write(w, "removed", v.removed);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::XmppRosterUpdateEvent& v)
    {
      Read(json, "fullUpdate", v.fullUpdate);
      Read(json, "added", v.added);
      Read(json, "updated", v.updated);
      Read(json, "removed", v.removed);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::XmppRosterUpdateEvent::ChangeItemAdd& v)
    {
      w.StartObject();
      Write(w, "item", v.item);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::XmppRosterUpdateEvent::ChangeItemAdd& v)
    {
      Read(json, "item", v.item);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::XmppRosterUpdateEvent::ChangeItemUpdate& v)
    {
      w.StartObject();
      Write(w, "item", v.item);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::XmppRosterUpdateEvent::ChangeItemUpdate& v)
    {
      Read(json, "item", v.item);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::XmppRosterUpdateEvent::ChangeItemRemove& v)
    {
      w.StartObject();
      Write(w, "address", v.address);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::XmppRosterUpdateEvent::ChangeItemRemove& v)
    {
      Read(json, "address", v.address);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::XmppRosterUpdateFailedEvent& v)
    {
      w.StartObject();
      Write(w, "errorCode", v.errorCode);
      Write(w, "errorText", v.errorText);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::XmppRosterUpdateFailedEvent& v)
    {
      Read(json, "errorCode", v.errorCode);
      Read(json, "errorText", v.errorText);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::XmppRosterPresenceEvent& v)
    {
      w.StartObject();
      Write(w, "rosterItem", v.rosterItem);
      Write(w, "resource", v.resource);
      Write(w, "compositeCannedPresence", v.compositeCannedPresence);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::XmppRosterPresenceEvent& v)
    {
      Read(json, "rosterItem", v.rosterItem);
      Read(json, "resource", v.resource);
      Read(json, "compositeCannedPresence", v.compositeCannedPresence);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::XmppRosterSubscriptionRequestEvent& v)
    {
      w.StartObject();
      Write(w, "address", v.address);
      Write(w, "msg", v.msg);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::XmppRosterSubscriptionRequestEvent& v)
    {
      Read(json, "address", v.address);
      Read(json, "msg", v.msg);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::XmppRosterUnsubscriptionRequestEvent& v)
    {
      w.StartObject();
      Write(w, "address", v.address);
      Write(w, "msg", v.msg);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::XmppRosterUnsubscriptionRequestEvent& v)
    {
      Read(json, "address", v.address);
      Read(json, "msg", v.msg);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::ErrorEvent& v)
    {
      w.StartObject();
      Write(w, "errorText", v.errorText);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::ErrorEvent& v)
    {
      Read(json, "errorText", v.errorText);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::XmppRosterHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::XmppRosterHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::XmppRosterState& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      Write(w, "roster", v.roster);
      Write(w, "rosterItems", v.rosterItems);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::XmppRosterState& v)
    {
      Read(json, "account", v.account);
      Read(json, "roster", v.roster);
      Read(json, "rosterItems", v.rosterItems);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::XmppRosterStateManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::XmppRosterStateManager& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::JsonProxyRosterStateEvent& v)
    {
      w.StartObject();
      Write(w, "rosterState", v.rosterState);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::JsonProxyRosterStateEvent& v)
    {
      Read(json, "rosterState", v.rosterState);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::JsonProxyRosterItemsEvent& v)
    {
      w.StartObject();
      Write(w, "roster", v.roster);
      Write(w, "rosterItems", v.rosterItems);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::JsonProxyRosterItemsEvent& v)
    {
      Read(json, "roster", v.roster);
      Read(json, "rosterItems", v.rosterItems);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::XmppRosterJsonProxyStateHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::XmppRosterJsonProxyStateHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::XmppRosterCreatedResultEvent& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::XmppRosterCreatedResultEvent& v)
    {
      Read(json, "account", v.account);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppRoster::XmppRosterHandlerInternal& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppRoster::XmppRosterHandlerInternal& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // XmppRoster_JSON_HELPER_H