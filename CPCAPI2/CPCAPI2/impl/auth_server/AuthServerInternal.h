#pragma once

#if !defined(CPCAPI2_AUTH_SERVER_INTERNAL_H)
#define CPCAPI2_AUTH_SERVER_INTERNAL_H

#include "cpcapi2defs.h"

namespace CPCAPI2
{

class Phone;

namespace AuthServer
{

class CPCAPI2_SHAREDLIBRARY_API AuthServerInternal
{

public:

#ifdef CPCAPI2_AUTO_TEST
   virtual void dropIncomingLoginRequests(bool enable) = 0;
   virtual void dropIncomingAddUserRequests(bool enable) = 0;
#endif

};

}

}

#endif // CPCAPI2_AUTH_SERVER_INTERNAL_H
