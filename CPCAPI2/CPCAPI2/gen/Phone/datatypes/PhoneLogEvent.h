#include "jsonrpc/OptionalSerialization.h"
#include "jsonrpc/StringSerialization.h"
#include <nlohmann/json.hpp>
#include "LogLevel.h"

#ifndef JSONRPC_CPCAPI2_PHONE_PHONELOGEVENT_H
#define JSONRPC_CPCAPI2_PHONE_PHONELOGEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  class PhoneLogEvent
  {
  public:
    std::optional<LogLevel> level;
    std::optional<cpc::string> subsystem;
    std::optional<cpc::string> appName;
    std::optional<cpc::string> file;
    std::optional<int32_t> line;
    std::optional<cpc::string> message;
    std::optional<cpc::string> messageWithHeaders;

    PhoneLogEvent() = default;

    PhoneLogEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const PhoneLogEvent& o)
    {
      j = nlohmann::json::object();
       if (o.level) { j["level"] = *(o.level); }
     if (o.subsystem) { j["subsystem"] = *(o.subsystem); }
     if (o.appName) { j["appName"] = *(o.appName); }
     if (o.file) { j["file"] = *(o.file); }
     if (o.line) { j["line"] = *(o.line); }
     if (o.message) { j["message"] = *(o.message); }
     if (o.messageWithHeaders) { j["messageWithHeaders"] = *(o.messageWithHeaders); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, PhoneLogEvent& o)
    {
     if (j.contains("level")) { o.level = j.at("level"); }
     if (j.contains("subsystem")) { o.subsystem = j.at("subsystem"); }
     if (j.contains("appName")) { o.appName = j.at("appName"); }
     if (j.contains("file")) { o.file = j.at("file"); }
     if (j.contains("line")) { o.line = j.at("line"); }
     if (j.contains("message")) { o.message = j.at("message"); }
     if (j.contains("messageWithHeaders")) { o.messageWithHeaders = j.at("messageWithHeaders"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_PHONELOGEVENT_H
