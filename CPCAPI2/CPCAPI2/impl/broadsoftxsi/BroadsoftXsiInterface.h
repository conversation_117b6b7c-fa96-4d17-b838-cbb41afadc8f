#pragma once

#ifndef CPCAPI2_BROADSOFT_XSI_INTERFACE_H
#define CPCAPI2_BROADSOFT_XSI_INTERFACE_H

#include <atomic>
#include <map>
#include <thread>

#include <boost/asio.hpp>

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>

#include "cpcapi2defs.h"
#include <phone/PhoneModule.h>
#include <broadsoftxsi/BroadsoftXsi.h>
#include <broadsoftxsi/BroadsoftXsiTypes.h>
#include <broadsoftxsi/BroadsoftXsiHandler.h>
#include "BroadsoftXsiTypesInternal.h"
#include "../util/AutoTestProcessor.h"

namespace CPCAPI2
{
   class Phone;

   namespace BroadsoftXsi
   {
      class BroadsoftXsiInterface :
         public BroadsoftXsi,
         public CPCAPI2::PhoneModule
#ifdef CPCAPI2_AUTO_TEST
       , public AutoTestProcessor
#endif
      {
      public:
         BroadsoftXsiInterface(Phone* phone);
         virtual ~BroadsoftXsiInterface();

         // PhoneModule implementation
         void Release() OVERRIDE;

         // BroadsoftXsi Implementation
         virtual BroadsoftXsiHandle create(void) OVERRIDE;
         virtual int configureSettings( const BroadsoftXsiHandle& h, const LocalSettings& settings ) OVERRIDE;
         virtual int applySettings( const BroadsoftXsiHandle& h ) OVERRIDE;
         virtual int queryServices(const BroadsoftXsiHandle& h) OVERRIDE;
         virtual int queryCallLogs( const BroadsoftXsiHandle& h, const CallLogType& callLogType ) OVERRIDE;
         virtual int deleteCallLogs( const BroadsoftXsiHandle& h, const CallLogType& callLogType ) OVERRIDE;
         virtual int deleteCallLog( const BroadsoftXsiHandle& h, const CallLogType& callLogType, const cpc::string& callLogId ) OVERRIDE;
         virtual int queryEnterpriseDirectory( const BroadsoftXsiHandle& h, const DirectorySearchParameters& searchParameters ) OVERRIDE;
         virtual int queryServiceSettings( const BroadsoftXsiHandle& h, const ServiceType ) OVERRIDE;
         virtual int setBroadworksAnywhereSettings(const BroadsoftXsiHandle& h, const BroadworksAnywhere& settings) OVERRIDE;
         virtual int setCallForwardAlwaysSettings(const BroadsoftXsiHandle& h, const CallForwardAlways& settings) OVERRIDE;
         virtual int setCallForwardBusySettings(const BroadsoftXsiHandle& h, const CallForwardBusy& settings) OVERRIDE;
         virtual int setCallForwardNoAnswerSettings(const BroadsoftXsiHandle& h, const CallForwardNoAnswer& settings) OVERRIDE;
         virtual int setDoNotDisturbSettings(const BroadsoftXsiHandle& h, const DoNotDisturb& settings) OVERRIDE;
         virtual int setRemoteOfficeSettings(const BroadsoftXsiHandle& h, const RemoteOffice& settings) OVERRIDE;
         virtual int setSimultaneousRingSettings(const BroadsoftXsiHandle& h, const SimultaneousRing& settings) OVERRIDE;
         virtual int createCall( const BroadsoftXsiHandle& connection, const cpc::string& number ) OVERRIDE;
         virtual int destroy( const BroadsoftXsiHandle& h ) OVERRIDE;
         virtual int setHandler( const BroadsoftXsiHandle& h, BroadsoftXsiHandler* handler ) OVERRIDE;
         virtual int process( unsigned int timeout ) OVERRIDE;
         virtual void postToProcessThread(void (*pfun)(void*), void* obj) OVERRIDE;

         virtual void setCallbackHook(void (*cbHook)(void*), void* context);
         void setCallbackHookImpl( void (*cbHook)(void*), void* context );

         int createImpl( const BroadsoftXsiHandle& h );
         int configureSettingsImpl( const BroadsoftXsiHandle& h, const LocalSettings& settings );
         int applySettingsImpl( const BroadsoftXsiHandle& h );
         int queryServicesImpl(const BroadsoftXsiHandle& h);
         int queryCallLogsImpl( const BroadsoftXsiHandle& h, const CallLogType& callLogType );
         int deleteCallLogsImpl( const BroadsoftXsiHandle& h, const CallLogType& callLogType );
         int deleteCallLogImpl( const BroadsoftXsiHandle& h, const CallLogType& callLogType, const cpc::string& callLogId );
         int queryEnterpriseDirectoryImpl( const BroadsoftXsiHandle& h, const DirectorySearchParameters& searchParameters );
         int queryServiceSettingsImpl( const BroadsoftXsiHandle& h, const ServiceType );
         int setBroadworksAnywhereSettingsImpl(const BroadsoftXsiHandle& h, const BroadworksAnywhere& settings);
         int setCallForwardAlwaysSettingsImpl(const BroadsoftXsiHandle& h, const CallForwardAlways& settings);
         int setCallForwardBusySettingsImpl(const BroadsoftXsiHandle& h, const CallForwardBusy& settings);
         int setCallForwardNoAnswerSettingsImpl(const BroadsoftXsiHandle& h, const CallForwardNoAnswer& settings);
         int setDoNotDisturbSettingsImpl(const BroadsoftXsiHandle& h, const DoNotDisturb& settings);
         int setRemoteOfficeSettingsImpl(const BroadsoftXsiHandle& h, const RemoteOffice& settings);
         int setSimultaneousRingSettingsImpl(const BroadsoftXsiHandle& h, const SimultaneousRing& settings);
         int createCallImpl(const BroadsoftXsiHandle& h,  const cpc::string& number);
         int destroyImpl( const BroadsoftXsiHandle& h );
         int setHandlerImpl( const BroadsoftXsiHandle& h, BroadsoftXsiHandler* handler);

#ifdef CPCAPI2_AUTO_TEST
         // AutoTestProcessor implementation
         AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif

      private: // methods

         // This method will set the out param to the channel info (internal) if it
         // exists, and return true. Otherwise, an error will be thrown to the application
         // and false will be returned.
         bool getInfo(const BroadsoftXsiHandle& h, BroadsoftXsiInfo*& pOutInfo);

      private: // data

         // Static handle counter
         static std::atomic< BroadsoftXsiHandle > s_CurrentHandle;

         // Map of AccountHandle(s) to BroadsoftXsiInfo(s)
         std::map< BroadsoftXsiHandle, BroadsoftXsiInfo* > m_InfoMap;

         // used for dispatching
         boost::asio::io_service& m_IOService;

         // Callback Fifo which should be used to marshal the events (process method should be called)
         resip::Fifo< resip::ReadCallbackBase > m_CallbackFifo;

         // Set to false once shutdown commences (to prevent further events)
         bool m_Shutdown;

         void (*m_CbHook)(void*);
         void* m_Context;
      };
   }
}
#endif // CPCAPI2_BROADSOFT_XSI_INTERFACE_H
