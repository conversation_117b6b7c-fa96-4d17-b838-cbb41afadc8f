#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_STUNSERVERSOURCETYPE_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_STUNSERVERSOURCETYPE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  enum class StunServerSourceType
  {
    None,
    Srv,
    Custom
  };

  inline std::string to_string(StunServerSourceType value)
  {
    switch (value)
    {
      case StunServerSourceType::None:
        return "None";
      case StunServerSourceType::Srv:
        return "SRV";
      case StunServerSourceType::Custom:
        return "Custom";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, StunServerSourceType value)
  {
    switch (value)
    {
      case StunServerSourceType::None:
        os << "None";
        break;
      case StunServerSourceType::Srv:
        os << "SRV";
        break;
      case StunServerSourceType::Custom:
        os << "Custom";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const StunServerSourceType& e)
  {
    switch (e)
    {
      case StunServerSourceType::None:
        j = "None";
        break;
      case StunServerSourceType::Srv:
        j = "SRV";
        break;
      case StunServerSourceType::Custom:
        j = "Custom";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, StunServerSourceType& e)
  {
      if (0 == j.get<std::string>().compare("None"))
    {
      e = StunServerSourceType::None;
    }
    else if (0 == j.get<std::string>().compare("SRV"))
    {
      e = StunServerSourceType::Srv;
    }
    else if (0 == j.get<std::string>().compare("Custom"))
    {
      e = StunServerSourceType::Custom;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum StunServerSourceType"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_STUNSERVERSOURCETYPE_H
