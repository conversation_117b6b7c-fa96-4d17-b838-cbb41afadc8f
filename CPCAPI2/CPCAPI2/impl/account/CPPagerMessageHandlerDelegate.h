#pragma once

#ifndef __CPCAPI2_CP_PAGER_MESSAGE_HANDLER_DELEGATE_H__
#define __CPCAPI2_CP_PAGER_MESSAGE_HANDLER_DELEGATE_H__

#include <resip/dum/PagerMessageHandler.hxx>

namespace CPCAPI2
{
namespace SipAccount
{

class CPPagerMessageHandlerDelegate : public resip::ServerPagerMessageHandler,
                                      public resip::ClientPagerMessageHandler
{
public:
   CPPagerMessageHandlerDelegate() {}
   virtual ~CPPagerMessageHandlerDelegate() {}

   // returns true if the message should be handled by this delegate
   virtual bool isMyMessage(const resip::SipMessage& msg) = 0;

   // returns true if the response should be handled by this delegate
   virtual bool isMyResponse(resip::ClientPagerMessageHandle h, const resip::SipMessage& msg) = 0;
};
typedef CPPagerMessageHandlerDelegate* CPPagerMessageHandlerDelegatePtr;

}
}

#endif // __CPCAPI2_CP_PAGER_MESSAGE_HANDLER_DELEGATE_H__