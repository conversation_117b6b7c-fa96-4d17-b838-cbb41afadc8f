#include "brand_branded.h"
#include "jsonapi/JsonApiClientHandler.h"

cpc::string CPCAPI2::JsonApi::StatusChangedEvent::get_debug_string(const CPCAPI2::JsonApi::StatusChangedEvent::JsonApiClientStatus& status)
{
   switch (status)
   {
   case CPCAPI2::JsonApi::StatusChangedEvent::JsonApiClientStatus::Status_Connected: return "connected";
   case CPCAPI2::JsonApi::StatusChangedEvent::JsonApiClientStatus::Status_Connecting: return "connecting";
   case CPCAPI2::JsonApi::StatusChangedEvent::JsonApiClientStatus::Status_Failed: return "failed";
   case CPCAPI2::JsonApi::StatusChangedEvent::JsonApiClientStatus::Status_Disconnected: return "disconnected";
   default: return "invalid";
   }
   return "invalid";
}

#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "cpcapi2utils.h"
#include "JsonApiClientInterface.h"
#include "JsonApiClient_WebSocket.h"
#include "json/JsonHelper.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"

#include <rutil/Random.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

using namespace resip;

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL
#define JSON_MODULE "JsonApiServer"

namespace CPCAPI2
{

namespace JsonApi
{

JsonApiClientInterface::JsonApiClientInterface(Phone* phone) :
EventSource<int, JsonApiClientHandler, JsonApiClientSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
mPhone(dynamic_cast<PhoneInterface*>(phone)),
mImpl(NULL),
mConnHandle(-1),
mLoginHandle(-1),
mJsonApiUserHandle(-1),
mServerGeneratedHandle(0),
mClientGeneratedHandle(0)
{
   mImpl = std::shared_ptr<JsonApiClientWebSocket>(new JsonApiClientWebSocket(mPhone));
   mImpl->setHandler(this);
   mFunctionMap["onLoginResult"] = std::bind(&JsonApiClientInterface::handleLoginResult, this, std::placeholders::_1);
   mFunctionMap["onLogoutResult"] = std::bind(&JsonApiClientInterface::handleLogoutResult, this, std::placeholders::_1);

   // the client generated handle is init'd to random, so that newly generated handles are less likely
   // to conflict with previously generated ones
   mClientGeneratedHandle = (int16_t)(resip::Random::getCryptoRandom() & 0x0000FFFF);

   StackLog(<< "JsonApiClientInterface::JsonApiClientInterface(): " << this << " phone: " << phone);
}

JsonApiClientInterface::~JsonApiClientInterface()
{
   StackLog(<< "JsonApiClientInterface::~JsonApiClientInterface(): " << this << " phone: " << mPhone);
   for (std::vector<std::function<void(void)>>::iterator i = mCallOnDestructFn.begin(); i != mCallOnDestructFn.end(); i++)
   {
      (*i)();
   }
   mCallOnDestructFn.clear();

   //mReactor.detach();

   if (mImpl)
   {
      shutdown();
   }

   mShutdown = true;
   interruptProcess();
}

void JsonApiClientInterface::Release()
{
   StackLog(<< "JsonApiClientInterface::Release(): " << this << " phone: " << mPhone);
   reactorSafeReleaseAfter(&mReactor);
}

void JsonApiClientInterface::release()
{
   StackLog(<< "JsonApiClientInterface::release(): " << this << " phone: " << mPhone);
   delete this;
}

void JsonApiClientInterface::setCallOnDestructFn(void (*func)(void*), void* context)
{
   mCallOnDestructFn.push_back(std::bind(func, context));
}

// JsonApiClientModule
void JsonApiClientInterface::setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport)
{
   StackLog(<< "JsonApiClientInterface::setTransport(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle);
}

int JsonApiClientInterface::processIncoming(const std::shared_ptr<rapidjson::Document>& request)
{
   StackLog(<< "JsonApiClientInterface::processIncoming(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle);
   postToSdkThread(resip::resip_safe_bind(&JsonApiClientInterface::processIncomingImpl, this, request));
   return kSuccess;
}

void JsonApiClientInterface::processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   DebugLog(<< "JsonApiClientInterface::processIncomingImpl(): " << this << " mPhone: " << mPhone << " triggering " << funcName);
   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

int JsonApiClientInterface::handleLoginResult(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "JsonApiClientInterface::handleLoginResult(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle);
   LoginResultEvent args;
   if (functionObjectVal.HasMember("success") && functionObjectVal["success"].IsBool())
   {
      args.success = functionObjectVal["success"].GetBool();
   }
   if (functionObjectVal.HasMember("serverGeneratedHandle") && functionObjectVal["serverGeneratedHandle"].IsUint())
   {
      mServerGeneratedHandle = (int16_t)functionObjectVal["serverGeneratedHandle"].GetUint();
   }
   if (functionObjectVal.HasMember("jsonApiUser") && functionObjectVal["jsonApiUser"].IsUint())
   {
      mJsonApiUserHandle = functionObjectVal["jsonApiUser"].GetUint();
   }
   fireEvent(cpcFunc(JsonApiClientHandler::onLoginResult), mLoginHandle, args);
   return kSuccess;
}

int JsonApiClientInterface::handleLogoutResult(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "JsonApiClientInterface::handleLogoutResult(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle);
   LogoutResultEvent args;
   if (functionObjectVal.HasMember("success") && functionObjectVal["success"].IsBool())
   {
      args.success = functionObjectVal["success"].GetBool();
   }

   fireEvent(cpcFunc(JsonApiClientHandler::onLogoutResult), mLoginHandle, args);

   mConnHandle = (-1);
   mLoginHandle = (-1);
   mJsonApiUserHandle = (-1);

   return kSuccess;
}

JsonApiConnectionHandle JsonApiClientInterface::connect(const cpc::string& serverUri)
{
   if (serverUri.empty())
   {
      throw "Empty serverUri!";
   }

   // StackLog(<< "JsonApiClientInterface::connect(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle << " serverUri: " << serverUri);
   if (mConnHandle == -1)
   {
      mConnHandle = 1;
      postToSdkThread(resip::resip_safe_bind(&JsonApiClientInterface::connectImpl, this, serverUri));
   }
   return kSuccess;
}

int JsonApiClientInterface::connectImpl(const cpc::string& serverUri)
{
   StackLog(<< "JsonApiClientInterface::connectImpl(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle << " serverUri: " << serverUri);
   if (mImpl == NULL)
   {
      DebugLog(<< "JsonApiClientInterface::connectImpl(): " << this << " mPhone: " << mPhone << " NULL impl for connection handle: " << mConnHandle << " serverUri: " << serverUri);
      return kError;
   }

   JsonApiClientWebSocketSettings settings;
   settings.serverUri = serverUri;
   mImpl->configureSettings(settings);
   mImpl->setHandler(this);
   return mImpl->connect();
}

int JsonApiClientInterface::shutdown()
{
   StackLog(<< "JsonApiClientInterface::shutdown(): " << this << " phone: " << mPhone);
   postToSdkThread(resip::resip_safe_bind(&JsonApiClientInterface::shutdownImpl, this));
   return kSuccess;
}

int JsonApiClientInterface::shutdownImpl()
{
   StackLog(<< "JsonApiClientInterface::shutdownImpl(): " << this << " phone: " << mPhone << " web-socket interface: " << mImpl);
   if (mImpl)
   {
      // Following functions have post calls, which can cause crashes during shutdown
      mImpl->setHandler(NULL);
      mImpl->shutdown();
      mImpl.reset();
      mImpl = NULL;
   }
   return kSuccess;
}

JsonApiLoginHandle JsonApiClientInterface::login(const cpc::string& authToken, const cpc::string& realm)
{
   // StackLog(<< "JsonApiClientInterface::login(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle);
   if (mLoginHandle == -1)
   {
      mLoginHandle = 1;
      postToSdkThread(resip::resip_safe_bind(&JsonApiClientInterface::loginImpl, this, authToken, realm));
   }
   return kSuccess;
}

int JsonApiClientInterface::loginImpl(const cpc::string& authToken, const cpc::string& realm)
{
   StackLog(<< "JsonApiClientInterface::loginImpl(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle);
   if (mImpl)
   {
      JsonFunctionCall(mImpl.get(), "login", JSON_VALUE(authToken), JSON_VALUE(realm));
   }
   else
   {
      InfoLog(<< "JsonApiClientInterface::loginImpl(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle << " web socket is null");
   }
   return kSuccess;
}

int JsonApiClientInterface::logout()
{
   StackLog(<< "JsonApiClientInterface::logout(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle);
   postToSdkThread(resip::resip_safe_bind(&JsonApiClientInterface::logoutImpl, this));
   return kSuccess;
}

int JsonApiClientInterface::logoutImpl()
{
   StackLog(<< "JsonApiClientInterface::logoutImpl(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle << " login handle: " << mLoginHandle << " json api user: " << mJsonApiUserHandle);
   if (mLoginHandle != -1)
   {
      if (mJsonApiUserHandle != -1)
      {
         JsonApiUserHandle jsonApiUser = mJsonApiUserHandle;
         if (mImpl)
         {
            JsonFunctionCall(mImpl.get(), "logout", JSON_VALUE(jsonApiUser));
         }
         else
         {
            InfoLog(<< "JsonApiClientInterface::logoutImpl(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle << " web socket is null");
         }
      }
      else
      {
         DebugLog(<< "JsonApiClientInterface::logoutImpl(): " << this << " ignoring logout request as json api user not set, connection handle: " << mConnHandle << " login handle: " << mLoginHandle << " json api user: " << mJsonApiUserHandle);
      }
   }
   else
   {
      DebugLog(<< "JsonApiClientInterface::logoutImpl(): " << this << " ignoring logout request as currently not logged in, connection handle: " << mConnHandle << " login handle: " << mLoginHandle << " json api user: " << mJsonApiUserHandle);
   }
   return kSuccess;
}

int JsonApiClientInterface::dropIncomingJsonMessages(bool enable)
{
   postToSdkThread(resip::resip_safe_bind(&JsonApiClientInterface::dropIncomingJsonMessagesImpl, this, enable));
   return kSuccess;
}

int JsonApiClientInterface::dropIncomingJsonMessagesImpl(bool enable)
{
   DebugLog(<< "JsonApiClientInterface::dropIncomingJsonMessagesImpl(): " << this << " phone: " << mPhone << " web-socket interface: " << mImpl << " drop message enable: " << enable);
   if (mImpl)
   {
      mImpl->dropIncomingJsonMessages(enable);
   }
   return kSuccess;
}

CPCAPI2::JsonApi::JsonApiTransport* JsonApiClientInterface::getTransport()
{
   // TODO: should pass the shared pointer rather than raw, need to update all the proxy transports
   return mImpl.get();
}

// Warning: may not occur on PhoneInterface thread
int JsonApiClientInterface::onStateChanged(JsonApiClientWebSocketHandler::State newState)
{
   StackLog(<< "JsonApiClientInterface::onStateChanged(): " << this << " phone: " << mPhone << " handle: " << mConnHandle << " new state: " << newState);
   postToSdkThread(resip::resip_safe_bind(&JsonApiClientInterface::onStateChangedImpl, this, newState));
   return kSuccess;
}

int JsonApiClientInterface::onStateChangedImpl(JsonApiClientWebSocketHandler::State newState)
{
   StackLog(<< "JsonApiClientInterface::onStateChangedImpl(): " << this << " phone: " << mPhone << " handle: " << mConnHandle << " new state: " << CPCAPI2::JsonApi::get_debug_string(newState));
   StatusChangedEvent args;

   switch (newState)
   {
      case JsonApiClientWebSocketHandler::State_Connected:
         args.status = StatusChangedEvent::Status_Connected;
         break;
      case JsonApiClientWebSocketHandler::State_Connecting:
         args.status = StatusChangedEvent::Status_Connecting;
         mLoginHandle = -1;
         break;
      case JsonApiClientWebSocketHandler::State_Failed:
         args.status = StatusChangedEvent::Status_Failed;
         mLoginHandle = -1;
         break;
      case JsonApiClientWebSocketHandler::State_Disconnected:
         args.status = StatusChangedEvent::Status_Disconnected;
         mLoginHandle = -1;
         break;
      default:
         args.status = StatusChangedEvent::Status_Failed;
         break;
   }

   fireEvent(cpcFunc(JsonApiClientHandler::onStatusChanged), mConnHandle, args);

   return kSuccess;
}

int JsonApiClientInterface::configureDefaultSettings(const JsonApiClientSettings& settings)
{
   // StackLog(<< "JsonApiClientInterface::configureDefaultSettings(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle);
   postToSdkThread(resip::resip_safe_bind(&JsonApiClientInterface::configureDefaultSettingsImpl, this, settings));
   return kSuccess;
}

int JsonApiClientInterface::configureDefaultSettingsImpl(const JsonApiClientSettings& settings)
{
   StackLog(<< "JsonApiClientInterface::configureDefaultSettingsImpl(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle);
   if (mImpl)
   {
      JsonApiClientWebSocketSettings wss;
      wss.serverUri = settings.serverUri;
      wss.ignoreCertVerification = settings.ignoreCertVerification;
      mImpl->configureSettings(wss);
      mSettings = settings;
   }
   else
   {
      InfoLog(<< "JsonApiClientInterface::configureDefaultSettingsImpl(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle << " web socket is null");
   }

   return kSuccess;
}

int JsonApiClientInterface::enable()
{
   // StackLog(<< "JsonApiClientInterface::enable(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle);
   postToSdkThread(resip::resip_safe_bind(&JsonApiClientInterface::enableImpl, this));
   return kSuccess;
}

int JsonApiClientInterface::enableImpl()
{
   StackLog(<< "JsonApiClientInterface::enableImpl(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle);
   if (mConnHandle == -1)
   {
      if (mImpl)
      {
         mConnHandle = 1;
         mImpl->setHandler(this);
         return mImpl->connect();
      }
      else
      {
         InfoLog(<< "JsonApiClientInterface::enableImpl(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle << " web socket is null");
      }
   }

   return kSuccess;
}

int JsonApiClientInterface::disable()
{
   StackLog(<< "JsonApiClientInterface::disable(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle);
   postToSdkThread(resip::resip_safe_bind(&JsonApiClientInterface::disableImpl, this));
   return kSuccess;
}

int JsonApiClientInterface::disableImpl()
{
   StackLog(<< "JsonApiClientInterface::disableImpl(): " << this << " mPhone: " << mPhone << " connection handle: " << mConnHandle);
   mConnHandle = -1;
   if (mImpl)
   {
      //mReactor.detach();

      mImpl->setHandler(NULL);
      mImpl->shutdown();

      StatusChangedEvent args;
      args.status = StatusChangedEvent::Status_Disconnected;
      fireEvent(cpcFunc(JsonApiClientHandler::onStatusChanged), mConnHandle, args);
   }

   return kSuccess;
}

int JsonApiClientInterface::generateHandle()
{
   // this is a 'pairing function'; see https://stackoverflow.com/questions/919612/mapping-two-integers-to-one-in-a-unique-and-deterministic-way
   int16_t a = mServerGeneratedHandle;
   int16_t b = mClientGeneratedHandle++; // only the client generated handle increments
   uint32_t A = (uint32_t)(a >= 0 ? 2 * a : -2 * a - 1);
   uint32_t B = (uint32_t)(b >= 0 ? 2 * b : -2 * b - 1);
   int32_t C = (int32_t)((A >= B ? A * A + A + B : A + B * B) / 2);
   return ((a < 0 && b < 0) || (a >= 0 && b >= 0)) ? C : -C - 1;
}

cpc::string get_debug_string(const CPCAPI2::JsonApi::JsonApiClientWebSocketHandler::State& state)
{
   switch (state)
   {
      case CPCAPI2::JsonApi::JsonApiClientWebSocketHandler::State_Connecting: return "connecting";
      case CPCAPI2::JsonApi::JsonApiClientWebSocketHandler::State_Connected: return "connected";
      case CPCAPI2::JsonApi::JsonApiClientWebSocketHandler::State_Failed: return "failed";
      case CPCAPI2::JsonApi::JsonApiClientWebSocketHandler::State_Disconnected: return "disconnected";
      default: return "invalid";
   }
   return "invalid";
}

}

}

#endif
