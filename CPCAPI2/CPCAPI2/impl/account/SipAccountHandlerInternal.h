#pragma once

#ifndef __CPCAPI2_SIPACCOUNTHANDLERINTERNAL_H__
#define __CPCAPI2_SIPACCOUNTHANDLERINTERNAL_H__

#include <phone/NetworkChangeManager.h>
#include <account/SipAccountSettings.h>
#include <account/SipAccountHandler.h>
#include <map>

namespace CPCAPI2
{

   namespace SipAccount
   {

      typedef struct SipAccountConfiguredEvent
      {
         NetworkTransport curTransport;
         std::map<NetworkTransport, SipAccountSettings> settings;
      } SipAccountConfiguredEvent;

      typedef struct SipAccountEnabledEvent
      {
         NetworkTransport curTransport;
         SipAccountSettings settings;
      } SipAccountEnabledEvent;

      typedef struct SipAccountDisabledEvent
      {
      } SipAccountDisabledEvent;

      typedef struct SipAccountDestroyedEvent
      {
      } SipAccountDestroyedEvent;

      typedef struct SipAccountClientAuthEvent
      {
         // if the response was a challenge, whether or not the SDK will
         // send an updated request to handle the challenge
         bool willSendUpdatedRequest;
         int responseStatusCode;
         cpc::string responseSourceIp;
         int responseSourcePort;
      } SipAccountClientAuthEvent;

      typedef struct SipAccountNetworkChangeEvent
      {
         NetworkTransport curTransport;
         SipAccountSettings settings;
      } SipAccountNetworkChangeEvent;

      typedef struct SipAccountDnsResetEvent
      {
         cpc::string currentAddress;
         int currentPort;
         IpVersion currentIpVersion;
         SipAccountTransportType currentTransportType;
         cpc::string preferredAddress;
         int preferredPort;
         IpVersion preferredIpVersion;
         SipAccountTransportType preferredTransportType;
      } SipAccountDnsResetEvent;

      typedef struct SipAccountRegistrationRportUpdateEvent
      {
         int rport;
         cpc::string receivedAddress;
      } SipAccountRegistrationRportUpdateEvent;

      /**
       * Interface which extends the public handler with new SDK-specific "internal" events.
       * This interface will be used mainly for the "SDK Observer" list instead of the official
       * handlers.
       *
       * The purpose of this internal handler is to a) provide internal access to the public events,
       * and also b) provide an inter-module communication mechanism by extending the public events
       * with private ones. The third purpose, c) is to provide a mechanism for the state classes.
       */
      class SipAccountHandlerInternal : public SipAccountHandler
      {

      public:

         // Fired after an event has been configured with all settings
         virtual int onAccountConfigured(
            SipAccountHandle account,
            const SipAccountConfiguredEvent& args) = 0;

         // Fired when an account has been enabled
         virtual int onAccountEnabled(
            SipAccountHandle account,
            const SipAccountEnabledEvent& args) = 0;

         // Fired when an account has been disabled
         virtual int onAccountDisabled(
            SipAccountHandle account,
            const SipAccountDisabledEvent& args) { return kSuccess; }

         // Fired when an account has been destroyed
         virtual int onAccountDestroyed(
            SipAccountHandle account,
            const SipAccountDestroyedEvent& args) { return kSuccess; }

         // wrapper around resip::ClientAuthManager::handle.
         // called whenever any response is received by the UAC
         virtual int onClientAuth(
            SipAccountHandle account,
            const SipAccountClientAuthEvent& args) { return kSuccess; }

         virtual int onNetworkChange(
            SipAccountHandle account,
            const SipAccountNetworkChangeEvent& args) { return kSuccess; }

         // Fired when DNS cache is reset, only applicable if the DNS reset feature has been enabled
         virtual int onDnsReset(
            SipAccountHandle account,
            const SipAccountDnsResetEvent& args) { return kSuccess; }

         // Fired when the contact binding is updated due to rport
         virtual int onRegistrationRportUpdate(
            SipAccountHandle account,
            const SipAccountRegistrationRportUpdateEvent& args) { return kSuccess; }

      };

      struct SipAccountMessageDecoratedEvent
      {
         cpc::string message;
         cpc::string source;
         cpc::string destination;
         cpc::string method;
         bool request;
      };

      struct SipAccountMessageDecoratedBindingReleasedEvent
      {
         cpc::vector<cpc::string> bindings; // contact binding list on decorated register message
         cpc::vector<cpc::string> releasedBindings; // released contact bindings added by the decorator
      };

      class SipAccountMessageDecoratorHandler
      {
      public:
         SipAccountMessageDecoratorHandler() {};
         virtual~ SipAccountMessageDecoratorHandler() {}

         /**
          * Event triggered when the sip account message decorator modifies a sip message just before transmission.
         */
         virtual int onMessageDecorated(SipAccountHandle handle, const SipAccountMessageDecoratedEvent& args) = 0;

         /**
          * Event triggered when the sip account message decorator releases a binding by adding an expires header.
          * Note that the bindings are released only when a network change has occurred.
         */
         virtual int onBindingReleased(SipAccountHandle handle, const SipAccountMessageDecoratedBindingReleasedEvent& args) = 0;
      };

   }

}

#endif /* __CPCAPI2_SIPACCOUNTHANDLERINTERNAL_H__ */
