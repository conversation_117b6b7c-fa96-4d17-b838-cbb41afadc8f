#pragma once

#if !defined(__SIP_MSRP_MESSAGING_MANAGER_H__)
#define __SIP_MSRP_MESSAGING_MANAGER_H__

#include "SipMsrpMessagingSessionInfo.h"
#include "CpmHelper.h"
#include "../account/SipAccountAwareFeature.h"

#include <resip/dum/InviteSessionHandler.hxx>

#include <msrp_forward_decl.h>
#include <utils/msrp_log.h>

using resip::InviteSession;
using resip::InviteSessionHandle;
using resip::ClientInviteSessionHandle;
using resip::ServerInviteSessionHandle;
using resip::SipMessage;
using resip::SdpContents;
using resip::Contents;
using resip::ClientSubscriptionHandle;
using resip::ServerSubscriptionHandle;

namespace CPCAPI2
{
namespace CPM
{

class SipMsrpMessagingManager;

struct CallbackInfo
{
   SipMsrpMessagingManager* _this;
   int session;
   cpc::string message;
   MessageType messageType;
};

/**
 * Responsible for managing messaging sessions estabilished with SIP while
 * individual messages are sent using the MSRP protocol, which is negociated as part of 
 * the SIP session establishement.
 */
class SipMsrpMessagingManager : public CPCAPI2::SipAccount::SipAccountAwareFeature,
                                public resip::InviteSessionHandler,
                                public resip::ReactorEventHandler
{
public:
   resip::SharedPtr<resip::DialogUsageManager> getDum() { return dum; }
   SipAccount::SipAccountImpl& getAccount() { return account; }

protected:
   SipMsrpMessagingManager(CPCAPI2::SipAccount::SipAccountImpl& account);
   virtual ~SipMsrpMessagingManager();

   virtual void releaseImpl() = 0;

   // InviteSessionHandler interface
   virtual void onNewSession(ClientInviteSessionHandle, InviteSession::OfferAnswerType oat, const SipMessage& msg) OVERRIDE;
   virtual void onNewSession(ServerInviteSessionHandle, InviteSession::OfferAnswerType oat, const SipMessage& msg) OVERRIDE;
   virtual void onOffer(InviteSessionHandle, const SipMessage& msg, const SdpContents&) OVERRIDE;      
   virtual void onTerminated(InviteSessionHandle, InviteSessionHandler::TerminatedReason reason, const SipMessage* related=0) OVERRIDE;
   virtual void onAnswer(InviteSessionHandle, const SipMessage& msg, const SdpContents&) OVERRIDE;
   virtual void onOfferRequired(InviteSessionHandle, const SipMessage& msg) OVERRIDE;

   // Methods that can be invoked by subclasses
   SipMsrpMessagingSessionInfo *getSessionInfo(int handle) const;
   void addSessionInfo(int handle, SipMsrpMessagingSessionInfo* sessionInfo);
   virtual void startSession(SipMsrpMessagingSessionInfo* sessionInfo, const cpc::string& featureTag);
   virtual void acceptSession(SipMsrpMessagingSessionInfo* sessionInfo);
   virtual void rejectSession(SipMsrpMessagingSessionInfo* sessionInfo, unsigned int reasonCode);
   virtual void terminateSession(SipMsrpMessagingSessionInfo *sessionInfo);
   virtual void sendMessage(SipMsrpMessagingSessionInfo* sessionInfo, const cpc::string& message, const CpimMessage& cpimMessage, MessageType messageType);

   // Methods that can be overridden by subclasses   
   virtual resip::Data buildSdp(msrp_uri_t* localUri, UInt32 localMediaPort, const resip::Mime& cpimContentType, bool isActive);

   // Methods that must be implemented by subclasses
   virtual void onMsrpMessageSendComplete(msrp_message_t* msrpMessage, int session, const cpc::string& message, MessageType messageType) = 0;
   virtual void onMsrpMessageRecvComplete(msrp_message_t* msrpMessage, int session, uint8_t* data, uint64_t dataLength) = 0;

protected:
   SipAccount::SipAccountImpl& account;

private:
   unsigned short msrpListeningPort;
   msrp_stack_t *msrpStack;
   bool isMsrpLibInitialized;
   resip::SharedPtr<resip::DialogUsageManager> dum;
   SipMsrpMessagingSessionInfoMap sessionInfoMap;
   resip::Tuple regServer;

   // InviteSessionHandler interface
   virtual void onConnected(ClientInviteSessionHandle, const SipMessage& msg) OVERRIDE {}
   virtual void onConnected(InviteSessionHandle, const SipMessage& msg) OVERRIDE {}
   virtual void onForkDestroyed(ClientInviteSessionHandle) OVERRIDE {};
   virtual void onRedirected(ClientInviteSessionHandle, const SipMessage& msg) OVERRIDE {};
   virtual void onFailure(ClientInviteSessionHandle, const SipMessage& msg) OVERRIDE {};
   virtual void onEarlyMedia(ClientInviteSessionHandle, const SipMessage&, const SdpContents&) OVERRIDE {};
   virtual void onProvisional(ClientInviteSessionHandle, const SipMessage&) OVERRIDE {};
   virtual void onOfferRejected(InviteSessionHandle, const SipMessage* msg) OVERRIDE {};
   virtual void onInfo(InviteSessionHandle, const SipMessage& msg) OVERRIDE {};
   virtual void onInfoSuccess(InviteSessionHandle, const SipMessage& msg) OVERRIDE {};
   virtual void onInfoFailure(InviteSessionHandle, const SipMessage& msg) OVERRIDE {};
   virtual void onMessage(InviteSessionHandle, const SipMessage& msg) OVERRIDE {};
   virtual void onMessageSuccess(InviteSessionHandle, const SipMessage& msg) OVERRIDE {};
   virtual void onMessageFailure(InviteSessionHandle, const SipMessage& msg) OVERRIDE {};
   virtual void onRefer(InviteSessionHandle, ServerSubscriptionHandle, const SipMessage& msg) OVERRIDE {};
   virtual void onReferNoSub(InviteSessionHandle, const SipMessage& msg) OVERRIDE {};
   virtual void onReferRejected(InviteSessionHandle, const SipMessage& msg) OVERRIDE {};
   virtual void onReferAccepted(InviteSessionHandle, ClientSubscriptionHandle, const SipMessage& msg) OVERRIDE {};

   // SipAccountAwareFeature interface
   virtual int adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile) OVERRIDE;
   virtual int addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig) OVERRIDE;
   virtual int onDumBeingDestroyed() OVERRIDE;
   virtual void release() OVERRIDE;
   virtual int onRegistrationSuccess(const SipAccount::SipRegistrationSuccessEvent& args) OVERRIDE;

   // ReactorEventHandler interface
   virtual void process(resip::ReactorEventHandler::FdSetType& fdset) OVERRIDE;
   virtual void buildFdSet(resip::ReactorEventHandler::FdSetType& fdset) OVERRIDE;
   virtual unsigned int getTimeTillNextProcessMS() OVERRIDE;
   virtual const char* getEventHandlerDesc() const OVERRIDE { return "SipMsrpMessagingManager"; }
   
   // Implementation methods
   static void msrp_session_message_created(msrp_session_t* msrpSession, msrp_message_t* msrpMessage, intptr_t userData);
   static void msrp_message_destroyed(msrp_message_t *msrpMessage, intptr_t userData);
   static void msrp_message_recv_complete(msrp_message_t* msrpMessage, intptr_t userData);   
   static void msrp_message_send_complete(msrp_message_t *p_message, intptr_t user_data);
   static void msrp_log_callback(msrp_log_level_enum_t level, ...);
};

}
}

#endif // __SIP_MSRP_MESSAGING_MANAGER_H__
