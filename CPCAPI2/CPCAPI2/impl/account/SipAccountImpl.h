#pragma once

#if !defined(CPCAPI2_ACCOUNT_IMPL_H)
#define CPCAPI2_ACCOUNT_IMPL_H

#ifdef __APPLE__
#include <TargetConditionals.h>
#endif

#include "cpcapi2defs.h"
#include "brand_branded.h"
#include "account/SipAccountSettings.h"
#include "account/SipAccountHandler.h"
#include "SipAccountHandlerInternal.h"
#include "account/SipAccountManagerInternal.h"
#include "../phone/PhoneInterface.h"
#include "phone/NetworkChangeManager.h"
#include "CPInviteHandlerSelector.h"
#include "CPPagerMessageHandler.h"
#include "regevent/SipRegEventManager.h"
#include "regevent/SipRegEventHandler.h"
#include "../strettotunnel/StrettoTunnelEventHandler.h"
#include "../interface/experimental/account/SipNetworkProbeHandler.h"

#include <resip/stack/SipStack.hxx>
#include <resip/dum/DialogUsageManager.hxx>
#include <resip/dum/RegistrationHandler.hxx>
#include <resip/dum/OutOfDialogHandler.hxx>
#include <resip/dum/DumShutdownHandler.hxx>
#include <resip/dum/RedirectHandler.hxx>
#include <rutil/SelectInterruptor.hxx>
#include <rutil/Socket.hxx>
#include <rutil/Fifo.hxx>
#include <rutil/DeadlineTimer.hxx>
#include <rutil/DnsUtil.hxx>
#include <resip/stack/UdpTransport.hxx>
#include <resip/stack/TcpTransport.hxx>
#include <resip/stack/ssl/TlsTransport.hxx>
#include <resip/dum/ClientAuthManager.hxx>

#include "../util/DumFpCommand.h"
#include "../util/StunClient.h"

#include "../tsm/TseTunnelManager.h"

#include <optional>

namespace CPCAPI2
{

namespace SipConversation
{
class BroadsoftCallControlMonitor;
}

struct NetworkChangeEvent;

namespace SipAccount
{

class SipAccountHandler;
class SipAccountAwareFeature;
class SipAccountAdornmentInternalHandler;
class SipAccountImplDisabler;
class SipAccountInterface;
class CPOptionsPingManager;
class CPDialogDnsResultManager;
class CPMessageDecorator;
class DialogDnsResultHandler;

enum Restriction
{
   UserDisabledRestriction,
   NetworkRestriction,
   BackgroundingRestriction,
   AppDisabledRestriction,
};

class SipAccountImpl : public resip::ClientRegistrationHandler,
                       public resip::OutOfDialogHandler,
                       public resip::DumShutdownHandler,
                       public resip::ReactorEventHandler,
                       public resip::DeadlineTimerHandler,
                       public resip::RedirectHandler,
                       public resip::StrettoTunnelEventHandler,
                       public CPCAPI2::EventSyncHandler<CPCAPI2::SipRegEvent::SipRegEventHandler>,
                       public resip::ReactorBinded
{

public:

   SipAccountImpl(CPCAPI2::SipAccount::SipAccountHandle h,
         const resip::Data& instanceId,
         CPCAPI2::SipAccount::SipAccountInterface* acctInterface,
         PhoneInterface* cpcPhone);
   virtual ~SipAccountImpl();

   void fireAccountError(const cpc::string& errorText);
   void fireDnsResultManagerError(const ErrorEvent& args);

   int enable();
   int disable(bool force = false);
   void destroy(bool force = false);
   void destroyImpl(bool bForce = false);
   int requestRegistrationRefresh(unsigned int deadlineSecondsFromNow);
   void resetDnsInitiated(const resip::Tuple& currentAddress, const resip::Tuple& preferredAddress);
   int shutdown();
   int setHandler(CPCAPI2::SipAccount::SipAccountHandler* handler);
   void stopRegEventSubscription();
   void registerAccountAwareFeature(SipAccountAwareFeature* accountAware);
   void unregisterAccountAwareFeature(SipAccountAwareFeature* accountAware);
   resip::SharedPtr<resip::DialogUsageManager> getDUM() const { return mDum; }
   SipAccountInterface* getAccountInterface() const { return mInterface; }

   CPCAPI2::SipAccount::SipAccountHandle getHandle() const { return mHandle; }
   int applySettings();
   SipAccountSettings getSettings() const;
   std::map<NetworkTransport, SipAccountSettings>& pendingSettings() { return mPendingSettings; }
   std::set<NetworkTransport>& restrictedNetworks() { return mRestrictedNetworks; }
   PhoneInterface* getPhone() const { return mPhone; }

   const bool isEnabled() const { return mEnabled; }
   const bool isDisabling() const { return mDisabling; }
   void addRestriction(Restriction restriction, bool forced = false);
   void removeRestriction(Restriction restriction, bool autoEnable = true);
   const std::set<Restriction>& restrictions() const { return mRestrictions; }
   const bool isRestrictedNetwork(const NetworkTransport currentNetworkTransport) const;
   const bool isUsingStrettoTunnel() const { return mUsingStrettoTunnel; }
   const bool isShutdown() const { return mShutdown; }
   const bool isDoingRportReReg() const { return (mAccountRegState == ARS_DoingRportReReg); }
   bool handle5xx(const resip::SipMessage& msg);

   void startRetryTimer(int interval);
   void stopRetryTimer();

   void setHibernationState(bool hibernation);

   void populateNameServer(resip::DnsStub::DnsSettings& dnsSettings) const;
   StunDnsSrvRecord getStunDnsSrvRecord();

   int sendOptionsMessage(const cpc::string target);
   int setSkipResetTransportOnNetworkChange(bool skip);

   // SdkModuleThreadUser
   virtual void process(resip::ReactorEventHandler::FdSetType& fdset) OVERRIDE;
   virtual void buildFdSet(resip::ReactorEventHandler::FdSetType& fdset) OVERRIDE;
   virtual unsigned int getTimeTillNextProcessMS() OVERRIDE;
   virtual const char* getEventHandlerDesc() const OVERRIDE { return "SipAccountImpl"; }

   void setMWIhandler(const std::function<bool(resip::ServerOutOfDialogReqHandle,const resip::SipMessage&)>& handler);

   //
   // ClientRegistrationHandler
   //
   virtual void onSuccess(resip::ClientRegistrationHandle h, const resip::SipMessage& response) OVERRIDE;
   virtual void onRemoved(resip::ClientRegistrationHandle h, const resip::SipMessage& response) OVERRIDE;
   virtual int onRequestRetry(resip::ClientRegistrationHandle h, int retrySeconds, const resip::SipMessage& response) OVERRIDE;
   virtual void onFailure(resip::ClientRegistrationHandle h, const resip::SipMessage& response) OVERRIDE;
   //virtual void onFlowTerminated(resip::ClientRegistrationHandle);
   virtual void onRegistrationConnectionTerminated(resip::ClientRegistrationHandle h) OVERRIDE; // Fake outbound

   // OutOfDialog - Client Handlers
   virtual bool onSuccess(resip::ClientOutOfDialogReqHandle, const resip::SipMessage& successResponse) OVERRIDE;
   virtual bool onFailure(resip::ClientOutOfDialogReqHandle, const resip::SipMessage& errorResponse) OVERRIDE;

   // OutOfDialog - Server Handlers
   virtual bool onReceivedRequest(resip::ServerOutOfDialogReqHandle h, const resip::SipMessage& request) OVERRIDE;

   //
   // DumShutdowHandler
   //
   virtual void onDumCanBeDeleted() OVERRIDE;

   //
   resip::Transport* findTransport(const resip::Transport* transport);
   int handleTransportHoldoverCalls(const NetworkChangeEvent& evt, bool& outContinueRegRefreshHandling);
   int refreshRegOnNetworkChange(const NetworkChangeEvent& evt);
   int refreshRegOnNetworkChangeImpl();
   void closeTransportConnections();
   void setFakeResponse(bool enable, cpc::string method, int responseCode, cpc::string responseReason, int warningCode);
   void resetTransports();

   void setDefaultInviteHandler(resip::InviteSessionHandler* handler) { mHandlerSelector.setDefaultInviteHandler(handler); }
   void setUdpAndTcpKeepAliveIntervals(resip::SharedPtr<resip::Profile> profile);

   // ReactorBinded
   void release();

   //
   // Probe status
   //
   void setProbeHandler(SipNetworkProbeHandler* handler);
   void setProbeMockDelay(IpVersion ipVersion, unsigned int delayMsecs);

   //
   // Adornment
   //
   void setAdornmentHandler(SipAccountAdornmentInternalHandler* handler);

   // Decorator
   void setDecoratorHandler(CPCAPI2::SipAccount::SipAccountMessageDecoratorHandler* handler);
   void fireMessageDecoratedEvent(resip::SipMessage &msg, const resip::Tuple &source, const resip::Tuple &destination);
   void fireReleaseBindingsEvent(SipAccountMessageDecoratedBindingReleasedEvent& args);

   // RedirectHandler
   void onRedirectReceived(resip::AppDialogSetHandle, const resip::SipMessage& originalRequest, const resip::SipMessage& response) OVERRIDE;
   bool onTryingNextTarget(resip::AppDialogSetHandle, resip::SipMessage& request, const resip::Uri& target) OVERRIDE;

   void addSdkObserver(SipAccountHandlerInternal* handler);
   void removeSdkObserver(SipAccountHandlerInternal* handler);

   void setDnsHandler(CPCAPI2::SipAccount::DialogDnsResultHandler* handler);

   // Handle dum timeout. Called when dum timer fires. This signals that registration
   // retry wait period is over and we just sent new registration request.
   void onDumTimer() OVERRIDE;

#if TARGET_OS_IPHONE
   // Unpublished API: install a callback for execution on every TCP/TLS read
   void setReadActivityCallback(void (*callback)(void *userData), void *userData);
#endif

   // GENBAND specific
   void setUseAlias(bool useAlias);

   // Stretto Tunnel specific
   void discardRegistration();
   void setSuppressUnregister(bool suppress);

   void setCertStorageLoadType(SipAccountManagerInternal::CertLoadStorageType type);
   void setCertStorageFileSystemPath(const std::string& path);

   void sendRegisterAfterConnProbe();
   void resetNetworkInterfaces();

   bool getSipServerIpFromLastReg(resip::Tuple& sipServerTuple) const {
      if (mSipServerIpFromLastReg.get() != NULL)
      {
         sipServerTuple = *mSipServerIpFromLastReg;
         return true;
      }
      return false;
   }

   resip::ClientRegistrationHandle getClientRegistrationHandle() { return mClientRegistration; }
   resip::SipStack* getSipStack() { return mStack; }
   StunClient* getStunClient() { return mStunClient; }

   // intended to be called by SIP conversation module
   void newCallActive(); // for outbound calls should be invoked before INVITE generation
   void allCallsEnded();

   static resip::SecurityTypes::SSLType getSSLType(SSLVersion sslVersion);
   static SSLVersion getSSLVersion(resip::SecurityTypes::SSLType sslType);
   static SSLVersion getSSLVersion(const resip::Data& sslType);

private:

   void populateMasterProfile(bool reuseExistingMasterProfile);
   bool enableTransports();
   void updateTransportStatus();
   void deleteDum();
   void appendTransportTypeParameter(resip::Uri& uri);
   void forceShutDown();
   void scheduleDelayedLicenseFraudCheck();
   void doDelayedLicenseFraudCheck();

   UInt32 calcPostNetworkChangeRegistrationExpires() const;
   bool shouldReInitDueToIpVersionChange() const;
   bool networkTransportChanged() const;
   bool ipVersionSupportChanged() const;
   bool autoIpVersion() const;
   bool initRegistration(const SipAccountSettings acctSettings);
   void sendRegisterOrProbeIfRequiredImpl(bool updateExpiry = true);
   void sendRegistrationStatusUpdateAfterNetworkChange();
   bool canCallBeSavedDuringNetworkChange() const;
   bool handleIpVersionChange();
   void handleNoRegistrarConfiguration();
   void sendRegisterOrProbeIfRequired();
   void setMessageDecoratorForNAT64IfRequired(resip::SharedPtr<resip::MasterProfile>& masterProfile);
   bool shouldDiscoverDns64Again() const;
   bool shouldDiscoverDns64Again(const resip::SipMessage& serverResponse) const;
   bool hasIp4ReceivedParam(const resip::SipMessage&) const;
   void refreshRegForDns64Rediscovery();
   bool mutualAuthCertConfigured(const SipAccountSettings&) const;

   // Will return a filler IP address "***********" if NAT64 support is enabled, an IPv6 transport is available and a NAT64 is detected.
   // In all other cases, will return an empty string for the source ip address.
   std::string getOverrideSourceIpForNAT64() const;
public:
   std::string getOverrideSourceIpForNAT64Unconditional() const { return "***********"; }
   resip::NameAddr getNetworkInterfaceForLastSuccessfulRegistration() const;
   bool shouldIgnoreNetworkChange(const std::optional<NetworkChangeEvent>& previousEvt, const NetworkChangeEvent& evt) const;
   void setIgnoreNetworkChangeStarcodeFilter(bool enabled);
   bool isIgnoreNetworkChangeStarcodeFilterEnabled() const;
   resip::SharedPtr<CPMessageDecorator> getOutboundDecorator();

private:

   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   void adornMessage(SipAccountImpl& acct, resip::SipMessage& msg) const;
   SipAccountStatusChangedEvent::Reason getAccountNtkRestrictionReason();
   resip::Transport* createTransport(resip::TransportType type, resip::IpVersion ipver, const SipAccountSettings& acctSettings);

   cpc::string getDomain() const;
   cpc::string getTunnelRecommendedDomain() const;
   resip::Transport* createTunnelTransport(resip::TransportType protocol, resip::IpVersion version, const SipAccountSettings& acctSettings, int& port);
#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)
   resip::Transport* createTscfTransport(resip::TransportType protocol, resip::IpVersion version, const SipAccountSettings& acctSettings, int& port);
#endif
#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
   resip::Transport* createStrettoTunnelTransport(resip::TransportType protocol, resip::IpVersion version, const SipAccountSettings& acctSettings, int& port);
#endif

   // StrettoTunnelEventHander
   virtual void onTunnelStarted(const resip::Tuple& transportTuple) OVERRIDE;
   virtual void onTunnelStopped(const resip::Tuple& transportTuple, resip::TransportFailure::FailureReason failureReason, int signalingCode, const std::string& signalingText) OVERRIDE;
   virtual void onTunnelReconnecting(const resip::Tuple& transportTuple, resip::StrettoTunnelEventHandler::ReconnectingReason reconnectingReason) OVERRIDE;

   // SipRegEventHandler
   virtual int onNewSubscription(CPCAPI2::SipRegEvent::SipRegEventSubscriptionHandle subscription, const CPCAPI2::SipRegEvent::NewRegEventSubscriptionEvent& args) OVERRIDE;
   virtual int onSubscriptionEnded(CPCAPI2::SipRegEvent::SipRegEventSubscriptionHandle subscription, const CPCAPI2::SipRegEvent::RegEventSubscriptionEndedEvent& args) OVERRIDE;
   virtual int onRegStateUpdated(CPCAPI2::SipRegEvent::SipRegEventSubscriptionHandle subscription, const CPCAPI2::SipRegEvent::RegEventUpdatedEvent& args) OVERRIDE;
   virtual int onSubscriptionStateChanged(CPCAPI2::SipRegEvent::SipRegEventSubscriptionHandle subscription, const CPCAPI2::SipRegEvent::RegEventSubscriptionStateChangedEvent& args) OVERRIDE;
   virtual int onError(CPCAPI2::SipRegEvent::SipRegEventSubscriptionHandle subscription, const CPCAPI2::SipRegEvent::ErrorEvent& args) OVERRIDE;

   enum AccountRegState
   {
      ARS_NotRegistered,
      ARS_Registering,
      ARS_Registered,
      ARS_Unregistering,
      ARS_DoingNetworkChangeReReg,
      ARS_DoingRportReReg,
      ARS_WaitingToRegister,
      ARS_Refreshing,
      ARS_DoingOutboundReReg
   };

   void transitionTo(AccountRegState ars);
   const char* ars_str(AccountRegState ars);
   const char* restriction_str(Restriction restriction);

   /**
    * Function starts increasing the registration failure interval time once the threshold
    * for the initial attempts to register has been reached.
    *
    * @return int Returns the updated registration failure interval time that should be used
   */
   int calcNextRegFailureRetryInterval();

   bool retryOn(cpc::string method, int responseCode);

   void handleLicensingError(const resip::Data& reasonText=resip::Data::Empty);

   bool serverIpForDisabledUseRegistrar(resip::Tuple& outTuple) const;

   void setTlsConnectionInfo();

   static bool warningMatch(const resip::SipMessage& response, int iResponseCode, int iWarningCode, const resip::Data& warning);
   static SipAccountTransportType getTransportType(resip::TransportType transportType);
   static resip::TransportType getTransportType(SipAccountTransportType sipTransportType);
   static cpc::string status_str(const SipAccountStatusChangedEvent::Status& eStatus);
   static cpc::string reason_str(const SipAccountStatusChangedEvent::Reason& eReason);

   resip::SharedPtr<resip::SipMessage> makeV4V6ConnectivityProbe(const resip::Uri& target, resip::IpVersion ipVersion);
   void updateStackIpPreference(const resip::SipMessage& response);
   bool preferredProbeResponseReceived();
   void probeStatus(CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent::Status status,
                    CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent::Reason reason,
                    cpc::string tid = "",
                    IpVersion probeVersion = IpVersion_V4,
                    const resip::SipMessage* response = NULL);
   void extractProbeInfo(resip::SipMessage& request, resip::SipMessage& response, CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent& args);
   void sendProbe(CPCAPI2::SipAccount::IpVersion ipVersion);
   void sendProbeAfterMockDelay();

   bool isIpv6CapableVersion(IpVersion ipv) const;
   bool isIpv4CapableVersion(IpVersion ipv) const;
   
   void patchStrettoTunnelAccountSettings(SipAccountSettings& sipAccountSettings) const;

   int checkBrandingDomainLock();

   /**
    * Set the checkPreviousStatus flag to true, to ensure that an account status update is only fired if
    * it is different from the previous status update. By default, the requested status event is fired
    * regardless of the previous status.
   */
   void fireAccountStatusEvent(SipAccountStatusChangedEvent& args, bool checkPreviousStatus = false);

private:

   PhoneInterface* mPhone;
   CPCAPI2::NetworkChangeManager* mNetworkChangeManagerIf;
   CPCAPI2::SipAccount::SipAccountInterface* mInterface;
   CPCAPI2::SipAccount::SipAccountHandle mHandle;
   resip::SipStack* mStack;
   resip::SharedPtr<resip::DialogUsageManager> mDum;
   resip::Data mInstanceId;
   std::map<NetworkTransport, SipAccountSettings> mPendingSettings;
   std::map<NetworkTransport, SipAccountSettings> mSettings;
   resip::ClientRegistrationHandle mClientRegistration;
   std::vector<SipAccountAwareFeature*> mAccountAwareFeatures;
   bool mShutdown;
   bool mDeleteOnShutdown;
   bool mReEnableOnShutdown;
   bool mDisabling;
   bool mEnabled;
   bool mSkipResetTransportOnNetworkChange;
   mutable bool mNat64DiscoveryFailure;
   std::set<Restriction> mRestrictions;
   resip::NameAddr mRportModifiedContact;
   unsigned short mRportReregTryCount;
   int mTransportErrorRetryCount;
   std::function<bool(resip::ServerOutOfDialogReqHandle,const resip::SipMessage&)> mMWIhandler;
   CPInviteHandlerSelector mHandlerSelector;
   CPPagerMessageHandler mPagerMessageHandler;
   resip::Transport* mUdpTransport; // note: Stretto Tunnel transport is mUdpTransport if enabled
   resip::Transport* mUdpTransportV6;
   resip::TcpTransport* mTcpTransport;
   resip::TcpTransport* mTcpTransportV6;
   resip::TlsTransport* mTlsTransport;
   resip::TlsTransport* mTlsTransportV6;

   struct RegistrationRetry
   {
      resip::DeadlineTimer<resip::MultiReactor> mTimer;
      int interval;

      RegistrationRetry(int timeout, resip::MultiReactor& service) : mTimer(service), interval(timeout) { }
   };
   RegistrationRetry mRegistrationRetry;
   resip::DeadlineTimer<resip::MultiReactor> mDisableTimer;

   struct RegAfterConnProbe
   {
      resip::DeadlineTimer<resip::MultiReactor> mTimer;
      resip::SharedPtr<resip::SipMessage> mRegister;
      resip::SharedPtr<resip::SipMessage> mOptionsV4;
      resip::SharedPtr<resip::SipMessage> mOptionsV6;
      resip::SharedPtr<resip::SipMessage> mResponseV4;
      resip::SharedPtr<resip::SipMessage> mResponseV6;

      resip::DeadlineTimer<resip::MultiReactor> mMockDelayTimer; // For testing purposes only
      IpVersion mMockDelayedProbeVersion;                        // For testing purposes only
      unsigned int mProbeMockDelayMsecs;                         // For testing purposes only

      RegAfterConnProbe(resip::MultiReactor& reactor, const resip::SharedPtr<resip::SipMessage>& reg)
         : mTimer(reactor), mRegister(reg), mMockDelayTimer(reactor), mMockDelayedProbeVersion(IpVersion_Auto), mProbeMockDelayMsecs(0) {}
   };

   RegAfterConnProbe mRegAfterConnProbe;

   SipNetworkProbeHandler* mProbeHandler;

   resip::SharedPtr<CPMessageDecorator> mMessageDecorator;
   SipAccountAdornmentInternalHandler* mAdornmentHandler;
   CPCAPI2::SipAccount::SipAccountMessageDecoratorHandler* mDecoratorHandler;

   resip::SharedPtr<resip::SipMessage> makeRegistration(const resip::NameAddr& target);

   SipAccountStatusChangedEvent mUnregisteredEvent;
   std::set<NetworkTransport> mRestrictedNetworks;

   AccountRegState mAccountRegState;
   SipAccountStatusChangedEvent::Status mLastAccountStatusFired;

   class NetworkChangeHistory
   {
   public:
      NetworkChangeHistory(time_t depth) : mDepth(depth) {}
      ~NetworkChangeHistory() {}

      void add();
      unsigned int numChanges() const;

   private:
      std::list<time_t> mHistory;
      time_t mDepth;
   };
   NetworkChangeHistory mNetworkChangeHistory;
   resip::NameAddr mNetworkInterfaceForLastSuccessfulRegistration;
   resip::DeadlineTimer<resip::MultiReactor> mNetworkChangeDelayTimer;

   int mCurrentFailureRegistrationIntervalSeconds;
   int mInitialFailureRegistrationRetryAttemptsPending;

   cpc::string mRegistrationCallId;
   SipRegEvent::SipRegEventSubscriptionHandle mRegEventSubscriptionHandle;

   resip::DeadlineTimer<resip::MultiReactor>* mLicenseFraudCheckTimer;
   CPCAPI2::SipConversation::BroadsoftCallControlMonitor* mBroadsoftCallControlMonitor;
#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)
   TseTunnelManager::TseTunnelInfo mTseTunnelInfo;
   cpc::string mTseFailureReason;
#endif
   bool mUsingStrettoTunnel;
#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
   bool mStrettoTunnelRequiresV4;
   resip::Transport* mStrettoTunnel;
   bool mSuppressUnregister;
#endif
   std::weak_ptr<SipAccountImplDisabler> mDisabler;
#if TARGET_OS_IPHONE
   void enableReadActivityCallback();
   void (*mReadActivityCallback)(void *userData);
   void *mReadActivityCallbackUserData;
#endif

   SipTLSConnectionInfo mTlsConnInfo;

   bool mUseAlias;

   short mCertStorageMask;
   resip::Data mCertStorageFileSystemPath;

   IpVersion mCurrentIpVersion;
   NetworkTransport mCurrentNetworkTransport;

   std::unique_ptr<resip::Tuple> mSipServerIpFromLastReg;
   const resip::Transport* mTransportFromLastReg;
   NetworkTransport mNetworkTransportFromLastReg;

   bool mWwanTransportHoldOverCall;
   bool mIgnoreNetworkChangeStarcodeFilterEnabled;

   CPOptionsPingManager* mOptionsPingManger;
   CPDialogDnsResultManager* mDialogDnsResultManager;

   class CpcClientAuthManager : public resip::ClientAuthManager
   {
   public:
      CpcClientAuthManager(SipAccountImpl& s) : mSipAccountImpl(s) {}
   private:
      virtual ~CpcClientAuthManager() {}
      virtual bool handle(resip::UserProfile& userProfile, resip::SipMessage& origRequest, const resip::SipMessage& response);

      SipAccountImpl& mSipAccountImpl;
   };
   friend class CpcClientAuthManager;
   void fireClientAuthEvent(const SipAccountClientAuthEvent& args);
   void fireDnsResetEvent(const SipAccountDnsResetEvent& args);
   void fireRegistrationRportUpdateEvent(const std::string& receivedAddress, const int rport);

   StunClient* mStunClient;

   int freezeSipDnsCache();
   int unfreezeSipDnsCache();
   bool mResetDnsAfterAllCallsEnded;
};

class SipAccountImplDisabler
{
public:
   SipAccountImplDisabler(SipAccountImpl* impl) : mImpl(impl) {}
   ~SipAccountImplDisabler() { if (mImpl && !mImpl->restrictions().empty()) mImpl->disable(); }
   void cancel() { mImpl = NULL; }
private:
   SipAccountImpl* mImpl;
};

typedef std::shared_ptr<SipAccountImplDisabler> AccountDisableCondition;
}

}

#endif // CPCAPI2_ACCOUNT_IMPL_H
