#pragma once

#ifndef JSON_SERIALIZE_DECL_H
#define JSON_SERIALIZE_DECL_H

#include <writer.h> // rapidjson

#include <interface/public/cpcstl/string.h>
#include <interface/public/cpcstl/vector.h>
#include <string>
#include <map>
#include <vector>
#include <rutil/Data.hxx>

namespace CPCAPI2
{
namespace Json
{
   template<typename Writer> inline void Serialize(Writer& w, const bool& v)          { w.<PERSON>(v); }
   template<typename Writer> inline void Serialize(Writer& w, const char& v)
   {
#ifndef __clang_analyzer__ // warning inside rapidjson: https://github.com/Tencent/rapidjson/issues/1174
      w.Int(v); // Promote to int
#endif
   }
   template<typename Writer> inline void Serialize(Writer& w, const int8_t& v)
   {
#ifndef __clang_analyzer__ // warning inside rapidjson
      w.Int(v); // Promote to int
#endif
   }
   template<typename Writer> inline void Serialize(Writer& w, const uint8_t& v)
   {
#ifndef __clang_analyzer__ // warning inside rapidjson
      w.Uint(v); // Promote to uint
#endif
   }
   template<typename Writer> inline void Serialize(Writer& w, const int16_t& v)
   {
#ifndef __clang_analyzer__ // warning inside rapidjson
      w.Int(v); // Promote to int
#endif
   }
   template<typename Writer> inline void Serialize(Writer& w, const uint16_t& v)
   {
#ifndef __clang_analyzer__ // warning inside rapidjson
      w.Uint(v);  // Promote to uint
#endif
   }
   template<typename Writer> inline void Serialize(Writer& w, const int32_t& v)
   {
#ifndef __clang_analyzer__ // warning inside rapidjson
      w.Int(v);
#endif
   }
   template<typename Writer> inline void Serialize(Writer& w, const uint32_t& v)
   {
#ifndef __clang_analyzer__ // warning inside rapidjson
      w.Uint(v);
#endif
   }
   template<typename Writer> inline void Serialize(Writer& w, const int64_t& v)
   {
#ifndef __clang_analyzer__ // warning inside rapidjson
   w.Int64(v);
#endif
   }
   template<typename Writer> inline void Serialize(Writer& w, const uint64_t& v)
   {
#ifndef __clang_analyzer__ // warning inside rapidjson
      w.Uint64(v);
#endif
   }
   template<typename Writer> inline void Serialize(Writer& w, const void* v)
   {
#ifndef __clang_analyzer__ // warning inside rapidjson
      w.Uint64((uint64_t)v);
#endif
   }
   template<typename Writer> inline void Serialize(Writer& w, const double& v)
   {
#ifndef __clang_analyzer__ // warning inside rapidjson
      w.Double(v);
#endif
   }
   template<typename Writer> inline void Serialize(Writer& w, const cpc::string& v)   { w.String(v.c_str(), v.size()); }
   template<typename Writer> inline void Serialize(Writer& w, const std::string& v)   { w.String(v.c_str(), v.size()); }
   template<typename Writer> inline void Serialize(Writer& w, const resip::Data& v)   { w.String(v.data(), v.size()); }
   template<typename Writer> inline void Serialize(Writer& w, const char* v, const size_t length) { w.String(v, length); } // Faster than without length
   template<typename Writer, size_t size> inline void Serialize(Writer& w, const char (&v)[size])
   {
      size_t length = strnlen(v, size);
      w.String(v, length);
   }

   // Need enable_if otherwise char v[32] would be ambiguous
   //template<typename Writer> void Serialize(Writer& w, const char* const &v)          { w.String(v); }
   template<typename Writer, typename T>
   inline typename std::enable_if<std::is_same<T, const char*>::value, void>::type Serialize(Writer& w, T const& v)
   {
      w.String(v);
   }

   template<typename Writer, typename T, size_t size> void Serialize(Writer& w, T (&v)[size]);
   template<typename Writer, typename T> void Serialize(Writer& w, const cpc::vector<T>& v);
   template<typename Writer, typename T> void Serialize(Writer& w, const std::vector<T>& v);
   template<typename Writer, typename T, typename U> void Serialize(Writer& w, const std::map<T, U>& v);

   template<typename Writer, typename T> void Write(Writer& w, const char* name, const T& v);

} // namespace Json
} // namespace CPCAPI2

#endif // JSON_SERIALIZE_DECL_H
