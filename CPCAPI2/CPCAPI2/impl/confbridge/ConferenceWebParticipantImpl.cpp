#include "brand_branded.h"

#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_MODULE == 1)
#include "cpcapi2utils.h"
#include "interface/public/media/video/Video.h"
#include "interface/public/media/audio/Audio.h"
#include "ConferenceWebParticipantImpl.h"
#include "ConferenceBridgeManagerImpl.h"
#include "ConferenceBridgeHandlerInternal.h"
#include "peerconnection/PeerConnectionManagerInterface.h"
#include "../media/VideoFrameSnapshotHelper.h"
#include "../media/MediaManagerInterface.h"
#include <MixerImpl.hxx>
#include <MediaStackImpl.hxx>
#include "../util/cpc_logger.h"
#include <chrono>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER

using namespace CPCAPI2::PeerConnection;

namespace CPCAPI2
{
namespace ConferenceBridge
{

uint64_t ConferenceWebParticipantImpl::mLastJoinTimestampMsecs = 0;

ConferenceWebParticipantImpl::ConferenceWebParticipantImpl(ConferenceParticipantHandle confPartHandle, CPCAPI2::Phone* phone, ConferenceBridgeManagerImpl* confBridgeMgr)
   : mPhone(phone), mPeerConnMgr(NULL), mConfBridgeMgr(confBridgeMgr), mConfPartHandle(confPartHandle), mHandle(0), mPeerConnectionAnswerHandler(NULL)
{
   mPartInfo.participant = confPartHandle;
   mPartInfo.participantType = ParticipantType_Web;
   mPartInfo.displayName = "web participant";
   auto msecs = std::chrono::time_point_cast<std::chrono::milliseconds>(std::chrono::high_resolution_clock::now()).time_since_epoch();
   uint64_t msecsCount = std::chrono::duration_cast<std::chrono::milliseconds>(msecs).count();
   ConferenceWebParticipantImpl::mLastJoinTimestampMsecs = ((msecsCount == ConferenceWebParticipantImpl::mLastJoinTimestampMsecs) ? (msecsCount + 1) : msecsCount);
   mPartInfo.joinTimestampMsecs = ConferenceWebParticipantImpl::mLastJoinTimestampMsecs;

   mAudioTransMgr = CPCAPI2::AudioTrans::AudioTransManager::getInterface(mPhone);

   mPeerConnMgr = CPCAPI2::PeerConnection::PeerConnectionManager::getInterface(phone);
   //dynamic_cast<PeerConnectionManagerInterface*>(mPeerConnMgr)->addSdkObserver(this);

   mHandle = mPeerConnMgr->createPeerConnection();
   mPeerConnMgr->setHandler(mHandle, (PeerConnectionHandler*)0xDEADBEFF);

//ifdef CPCAPI2_AUTO_TEST
   dynamic_cast<PeerConnectionManagerInterface*>(mPeerConnMgr)->addSdkObserver(mHandle, this);
//endif

   const ConferenceSettings& confSettings = mConfBridgeMgr->getConferenceSettings();
   PeerConnectionSettings pcSettings;
   pcSettings.certAor = "<EMAIL>";
   pcSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_ICE;
   if (mConfBridgeMgr->getConferenceInfo().natTraversalServerInfo.serverPublicIpAddress.empty())
   {
      pcSettings.natTraversalServerHostname = mConfBridgeMgr->getConferenceInfo().natTraversalServerInfo.natTraversalServerHostname; //"**************";
      pcSettings.natTraversalServerUsername = mConfBridgeMgr->getConferenceInfo().natTraversalServerInfo.natTraversalServerUsername;
      pcSettings.natTraversalServerPassword = mConfBridgeMgr->getConferenceInfo().natTraversalServerInfo.natTraversalServerPassword;
      pcSettings.natTraversalServerType = (PeerConnectionSettings::NatTraversalServerType)mConfBridgeMgr->getConferenceInfo().natTraversalServerInfo.natTraversalServerType2;
      if (pcSettings.natTraversalServerType == PeerConnectionSettings::NatTraversalServerType_StunAndTurn)
      {
         pcSettings.natTraversalServerType = PeerConnectionSettings::NatTraversalServerType_StunOnly;
      }
      pcSettings.natTraversalServerPort = mConfBridgeMgr->getConferenceInfo().natTraversalServerInfo.natTraversalServerPort;
   }
   else
   {
      pcSettings.natTraversalServerType = PeerConnectionSettings::NatTraversalServerType_StunOnly;
      pcSettings.forcedHostCandidate = mConfBridgeMgr->getConferenceInfo().natTraversalServerInfo.serverPublicIpAddress;
   }
   pcSettings.sessionName = "cwp";
   pcSettings.mixId = mConfBridgeMgr->getConferenceHandle();
   pcSettings.startPaused = (mConfBridgeMgr->getConferenceSettings().mixMode == ConferenceMixMode_SFU || mConfBridgeMgr->getConferenceSettings().mixMode == ConferenceMixMode_SFU_BiDi);
   pcSettings.adaptVideoCodecOnRemotePacketLoss = confSettings.adaptVideoCodecOnRemotePacketLoss;
   pcSettings.isScreenshare = (std::find(confSettings.tags.begin(), confSettings.tags.end(), 0x2000) != confSettings.tags.end() ? true : false);
   pcSettings.rtcpMux = true;
   pcSettings.mediaDscp = confSettings.mediaDscp;
   mPeerConnMgr->setDefaultSettings(mHandle, pcSettings);

   // 0x1000 --> audio conference
   if (!pcSettings.isScreenshare && 
      (std::find(confSettings.tags.begin(), confSettings.tags.end(), 0x1000) != confSettings.tags.end()))
   {
      ConferenceBridgeManagerInterface* confBridgeMgrIf = dynamic_cast<ConferenceBridgeManagerInterface*>(ConferenceBridgeManager::getInterface(mPhone));
      int inactivityTimeout = confBridgeMgrIf->getConferenceBridgeConfig().mediaInactivityTimeoutMs;
      DebugLog(<< "ConferenceWebParticipantImpl(): enabling media inactivity timeout: " << inactivityTimeout << " msecs for participant: " << mConfPartHandle);
      if (inactivityTimeout > 0)
      {
         dynamic_cast<PeerConnectionManagerInternal*>(mPeerConnMgr)->setMediaInactivityTimeoutMsecs(mHandle, inactivityTimeout);
         mPeerConnMgr->startMediaInactivityMonitor(mHandle);
      }
   }

   mPartInfo.webParticipantDetails.peerConnection = mHandle;

#if (CPCAPI2_BRAND_AUDIO_TRANS_MODULE == 1)
   if (mAudioTransMgr != NULL)
   {
      dynamic_cast<CPCAPI2::AudioTrans::AudioTransInterface*>(mAudioTransMgr)->addSdkObserver(this);
   }
#endif
}

ConferenceWebParticipantImpl::~ConferenceWebParticipantImpl()
{
   mMediaQueries.clear();
   mPeerConnMgr->close(mHandle);
   dynamic_cast<PeerConnectionManagerInterface*>(mPeerConnMgr)->removeSdkObserver(this);
#ifdef CPCAPI2_AUTO_TEST
   dynamic_cast<PeerConnectionManagerInterface*>(mPeerConnMgr)->removeSdkObserver(mHandle, this);
#endif
#if (CPCAPI2_BRAND_AUDIO_TRANS_MODULE == 1)
   if (mAudioTransMgr != NULL)
   {
      dynamic_cast<CPCAPI2::AudioTrans::AudioTransInterface*>(mAudioTransMgr)->removeSdkObserver(this);
   }
#endif
}

int ConferenceWebParticipantImpl::sendPeerConnectionOffer(const CPCAPI2::PeerConnection::SessionDescription& sdpOffer, PeerConnectionAnswerHandler* handler)
{
   if (mPeerConnectionAnswerHandler == NULL)
   {
      mPeerConnectionAnswerHandler = handler;

      CPCAPI2::PeerConnection::MediaInfo miAudio;
      miAudio.mediaType = PeerConnection::MediaType_Audio;
      miAudio.mediaDirection = PeerConnection::MediaDirection_SendRecv;
      if (mConfBridgeMgr->getConferenceInfo().mediaEncryptionMode == ConferenceMediaEncryptionMode_SRTP_SDES)
      {
         miAudio.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_SDES_Encrypted;
      }
      else if (mConfBridgeMgr->getConferenceInfo().mediaEncryptionMode == ConferenceMediaEncryptionMode_SRTP_DTLS)
      {
         miAudio.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
         miAudio.mediaEncryptionOptions.secureMediaRequired = true;
      }
      mPeerConnMgr->configureMedia(mHandle, mPeerConnMgr->createMediaStream(), miAudio);

      if (std::find(mConfBridgeMgr->getConferenceSettings().tags.begin(), mConfBridgeMgr->getConferenceSettings().tags.end(), 0x4000) == mConfBridgeMgr->getConferenceSettings().tags.end())
      {
         CPCAPI2::PeerConnection::MediaInfo miVideo;
         miVideo.mediaType = PeerConnection::MediaType_Video;
         miVideo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
         if (mConfBridgeMgr->getConferenceInfo().mediaEncryptionMode == ConferenceMediaEncryptionMode_SRTP_SDES)
         {
            miVideo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_SDES_Encrypted;
         }
         else if (mConfBridgeMgr->getConferenceInfo().mediaEncryptionMode == ConferenceMediaEncryptionMode_SRTP_DTLS)
         {
            miVideo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
            miVideo.mediaEncryptionOptions.secureMediaRequired = true;
         }
         mPeerConnMgr->configureMedia(mHandle, mPeerConnMgr->createMediaStream(), miVideo);
      }
      mPeerConnMgr->setRemoteDescription(mHandle, sdpOffer);
      mPeerConnMgr->createAnswer(mHandle);
   }
   return 0;
}

int ConferenceWebParticipantImpl::sendPeerConnectionAnswer(const CPCAPI2::PeerConnection::SessionDescription& sdpAnswer)
{
   mPeerConnMgr->setRemoteDescription(mHandle, sdpAnswer);
   return 0;
}

int ConferenceWebParticipantImpl::generateLocalOffer(PeerConnectionAnswerHandler* handler)
{
   if (mPeerConnectionAnswerHandler == NULL)
   {
      mPeerConnectionAnswerHandler = handler;

      CPCAPI2::PeerConnection::MediaInfo miAudio;
      miAudio.mediaType = PeerConnection::MediaType_Audio;
      miAudio.mediaDirection = PeerConnection::MediaDirection_SendRecv;
      if (mConfBridgeMgr->getConferenceInfo().mediaEncryptionMode == ConferenceMediaEncryptionMode_SRTP_SDES)
      {
         miAudio.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_SDES_Encrypted;
      }
      else if (mConfBridgeMgr->getConferenceInfo().mediaEncryptionMode == ConferenceMediaEncryptionMode_SRTP_DTLS)
      {
         miAudio.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
         miAudio.mediaEncryptionOptions.secureMediaRequired = true;
      }
      mPeerConnMgr->configureMedia(mHandle, mPeerConnMgr->createMediaStream(), miAudio);

      if (std::find(mConfBridgeMgr->getConferenceSettings().tags.begin(), mConfBridgeMgr->getConferenceSettings().tags.end(), 0x4000) == mConfBridgeMgr->getConferenceSettings().tags.end())
      {
         CPCAPI2::PeerConnection::MediaInfo miVideo;
         miVideo.mediaType = PeerConnection::MediaType_Video;
         miVideo.mediaDirection = PeerConnection::MediaDirection_SendRecv;
         if (mConfBridgeMgr->getConferenceInfo().mediaEncryptionMode == ConferenceMediaEncryptionMode_SRTP_SDES)
         {
            miVideo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_SDES_Encrypted;
         }
         else if (mConfBridgeMgr->getConferenceInfo().mediaEncryptionMode == ConferenceMediaEncryptionMode_SRTP_DTLS)
         {
            miVideo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
            miVideo.mediaEncryptionOptions.secureMediaRequired = true;
         }
         mPeerConnMgr->configureMedia(mHandle, mPeerConnMgr->createMediaStream(), miVideo);
      }
      mPeerConnMgr->createOffer(mHandle);
   }
   return 0;
}

int ConferenceWebParticipantImpl::destroyWebParticipant()
{
   if (mAudioTransMgr != NULL)
   {
      mAudioTransMgr->stopTranscription(mAudioTransHandle);
   }

   // we are a participant in the conference -- disconnect the presenter's send stream from our receive stream
   cpc::vector<CPCAPI2::PeerConnection::MediaInfo>::const_iterator it = mMediaInfo.begin();
   for (; it != mMediaInfo.end(); ++it)
   {
      if (it->mediaType == PeerConnection::MediaType_Audio)
      {
         int senderChannel = mConfBridgeMgr->getSFUSenderChannelAudio();

         DebugLog(<< "ConferenceWebParticipantImpl::destroyWebParticipant(): " << this << " pc: " << mHandle << " participant: " << mConfPartHandle << " sender-channel: " << senderChannel << " media-list: " << mMediaInfo.size());
         // StackLog(<< "ConferenceWebParticipantImpl::destroyWebParticipant(): pc: " << mHandle << " participant: " << mConfPartHandle << " sender-channel: " << senderChannel << " media-stream: " << it->mediaStream << " stream-id: " << it->mediaStreamId << " mediaType: " << it->mediaType);
         if (senderChannel != -1)
         {
            CPCAPI2::Media::MediaManagerInterface* mmi = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone));
            CPCAPI2::Media::Audio* audio = CPCAPI2::Media::Audio::getInterface(mmi);
            if (it->mediaStreamId == senderChannel)
            {
               DebugLog(<< "ConferenceWebParticipantImpl::destroyWebParticipant(): pc: " << mHandle << " participant: " << mConfPartHandle << " stream-id: " << it->mediaStreamId << " matches sender-channel: " << senderChannel << ", resetting bridge sender channel");
               mConfBridgeMgr->setSFUSenderChannelAudio(-1);
            }
            else
            {
               DebugLog(<< "ConferenceWebParticipantImpl::destroyWebParticipant(): pc: " << mHandle << " participant: " << mConfPartHandle << " disconnect stream-id: " << it->mediaStreamId << " and sender-channel: " << senderChannel);
               audio->disconnectAudioStreams(senderChannel, it->mediaStreamId);
            }
         }
      }
      else if (it->mediaType == PeerConnection::MediaType_Video)
      {
         int senderChannel = mConfBridgeMgr->getSFUSenderChannelVideo();

         DebugLog(<< "ConferenceWebParticipantImpl::destroyWebParticipant(): " << this << " pc: " << mHandle << " participant: " << mConfPartHandle << " sender-channel: " << senderChannel << " media-list: " << mMediaInfo.size());
         // StackLog(<< "ConferenceWebParticipantImpl::destroyWebParticipant(): pc: " << mHandle << " participant: " << mConfPartHandle << " sender-channel: " << senderChannel << " media-stream: " << it->mediaStream << " stream-id: " << it->mediaStreamId << " mediaType: " << it->mediaType);
         if (senderChannel != -1)
         {
            CPCAPI2::Media::MediaManagerInterface* mmi = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone));
            CPCAPI2::Media::Video* video = CPCAPI2::Media::Video::getInterface(mmi);
            if (it->mediaStreamId == senderChannel)
            {
               DebugLog(<< "ConferenceWebParticipantImpl::destroyWebParticipant(): pc: " << mHandle << " participant: " << mConfPartHandle << " stream-id: " << it->mediaStreamId << " matches sender-channel: " << senderChannel << ", resetting bridge sender channel");
               mConfBridgeMgr->setSFUSenderChannelVideo(-1);
            }
            else
            {
               DebugLog(<< "ConferenceWebParticipantImpl::destroyWebParticipant(): pc: " << mHandle << " participant: " << mConfPartHandle << " disconnect stream-id: " << it->mediaStreamId << " and sender-channel: " << senderChannel);
               video->disconnectVideoStreams(senderChannel, it->mediaStreamId);
            }
         }
      }
   }

   mMediaQueries.clear();
   mPeerConnMgr->close(mHandle);
   mHandle = 0;
   return 0;
}

int ConferenceWebParticipantImpl::takeParticipantSnapshot()
{
   bool isScreenshare = (std::find(mConfBridgeMgr->getConferenceSettings().tags.begin(), mConfBridgeMgr->getConferenceSettings().tags.end(), 0x2000) != mConfBridgeMgr->getConferenceSettings().tags.end() ? true : false);
   if (isScreenshare)
   {
      return 0;
   }

#if (CPCAPI2_BRAND_MOZJPEG == 1)
   cpc::vector<CPCAPI2::PeerConnection::MediaInfo>::const_iterator it = mMediaInfo.begin();
   for (; it != mMediaInfo.end(); ++it)
   {
      if (it->mediaType == PeerConnection::MediaType_Video)
      {
         CPCAPI2::Media::MediaManagerInterface* mmi = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone));
         std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mmi->media_stack_ptr()->mixer());
         resip::Data filenamestr;
         {
            resip::DataStream ds(filenamestr);
            ds << "snapshots/";
            ds << mConfBridgeMgr->getConferenceHandle() << "_";
            ds << it->mediaStreamId << ".jpg";
         }
         cpc::string filenameUtf8 = filenamestr.c_str();
         resip::ReadCallbackBase* completionHandler = resip::resip_bind(&ConferenceWebParticipantImpl::onSnapshotComplete, this, mHandle, filenameUtf8);
         CPCAPI2::Media::VideoFrameSnapshotHelper* snapshotHelper = new CPCAPI2::Media::VideoFrameSnapshotHelper(mPhone, completionHandler, filenameUtf8, mixer, it->mediaStreamId);
      }
   }
#endif
   return 0;
}

int ConferenceWebParticipantImpl::setParticipantPhoto(const cpc::string& photoFileNameUtf8)
{
   CPCAPI2::Media::MediaManagerInterface* mmi = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone));
   std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mmi->media_stack_ptr()->mixer());
   cpc::vector<CPCAPI2::PeerConnection::MediaInfo>::const_iterator it = mMediaInfo.begin();
   for (; it != mMediaInfo.end(); ++it)
   {
      if (it->mediaType == PeerConnection::MediaType_Video)
      {
         mixer->setParticipantPhoto(it->mediaStreamId, photoFileNameUtf8.c_str());
      }
   }
   mPartPhoto = photoFileNameUtf8;

   return 0;
}

int ConferenceWebParticipantImpl::addToFloor(bool suppressEvent)
{
   if (mConfBridgeMgr->getConferenceSettings().mixMode == ConferenceMixMode_SFU)
   {
      if (mConfBridgeMgr->doesFloorOwnerExist())
      {
         return -1;
      }
   }
   else if (mConfBridgeMgr->getConferenceSettings().mixMode == ConferenceMixMode_MCU)
   {
      CPCAPI2::Media::MediaManagerInterface* mmi = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone));
      std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mmi->media_stack_ptr()->mixer());
      cpc::vector<CPCAPI2::PeerConnection::MediaInfo>::const_iterator it = mMediaInfo.begin();
      for (; it != mMediaInfo.end(); ++it)
      {
         if (it->mediaType == PeerConnection::MediaType_Audio)
         {
            mixer->addToAudioFloor(it->mediaStreamId);
         }
         else if (it->mediaType == PeerConnection::MediaType_Video)
         {
            mixer->addToVideoFloor(it->mediaStreamId);
         }
      }
   }

   mPartInfo.hasFloor = true;

   if (!suppressEvent)
   {
      cpc::vector<ParticipantInfo> updatedParticipants;
      updatedParticipants.push_back(mPartInfo);
      mConfBridgeMgr->fireParticipantListState(cpc::vector<ParticipantInfo>(), updatedParticipants, cpc::vector<ParticipantInfo>());
   }
   return 0;
}

int ConferenceWebParticipantImpl::removeFromFloor()
{
   CPCAPI2::Media::MediaManagerInterface* mmi = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone));
   std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mmi->media_stack_ptr()->mixer());
   cpc::vector<CPCAPI2::PeerConnection::MediaInfo>::const_iterator it = mMediaInfo.begin();
   for (; it != mMediaInfo.end(); ++it)
   {
      if (it->mediaType == PeerConnection::MediaType_Audio)
      {
         mixer->removeFromAudioFloor(it->mediaStreamId);
      }
      else if (it->mediaType == PeerConnection::MediaType_Video)
      {
         mixer->removeFromVideoFloor(it->mediaStreamId);
      }
   }

   mPartInfo.hasFloor = false;

   cpc::vector<ParticipantInfo> updatedParticipants;
   updatedParticipants.push_back(mPartInfo);
   mConfBridgeMgr->fireParticipantListState(cpc::vector<ParticipantInfo>(), updatedParticipants, cpc::vector<ParticipantInfo>());

   return 0;
}

int ConferenceWebParticipantImpl::setTranscriptionEnabled(bool enabled)
{
   //ConferenceParticipantHandle participant = mConfPartHandle;
   if (enabled)
   {
      mTranscriptionEnabled = true;
      CPCAPI2::Media::MediaManagerInterface* mmi = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone));
      std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mmi->media_stack_ptr()->mixer());
      cpc::vector<CPCAPI2::PeerConnection::MediaInfo>::const_iterator it = mMediaInfo.begin();
      for (; it != mMediaInfo.end(); ++it)
      {
         if (it->mediaType == PeerConnection::MediaType_Audio)
         {
            mAudioTransHandle = mAudioTransMgr->createAudioTranscriptionSession();
            CPCAPI2::AudioTrans::AudioTransSettings settings;
            settings.audioRecvChannel = it->mediaStreamId;
            mAudioTransMgr->setAudioTransSettings(mAudioTransHandle, settings);
            mAudioTransMgr->startTranscription(mAudioTransHandle);
            break;
         }
      }

      cpc::vector<ParticipantInfo> updatedParticipants;
      updatedParticipants.push_back(mPartInfo);

      mConfBridgeMgr->fireParticipantListState(cpc::vector<ParticipantInfo>(), updatedParticipants, cpc::vector<ParticipantInfo>());
   }
   else
   {
      mTranscriptionEnabled = false;
      mAudioTransMgr->stopTranscription(mAudioTransHandle);
      mAudioTransHandle = (CPCAPI2::AudioTrans::AudioTransHandle)-1;
   }
   return 0;
}

int ConferenceWebParticipantImpl::queryMediaStatistics()
{
#ifdef CPCAPI2_AUTO_TEST
   if (mMediaQueries.size() > 0)
   {
      StackLog(<< "ConferenceWebParticipantImpl::queryMediaStatistics(): pc: " << mHandle << " - ignore media query as one already in progress for conference-bridge: " << mConfBridgeMgr->getConferenceHandle());
      return 0;
   }

   DebugLog(<< "ConferenceWebParticipantImpl::queryMediaStatistics(): " << this << " pc: " << mHandle << " media-list: " << mMediaInfo.size());
   for (cpc::vector<CPCAPI2::PeerConnection::MediaInfo>::const_iterator it = mMediaInfo.begin(); it != mMediaInfo.end(); ++it)
   {
      // StackLog(<< "ConferenceWebParticipantImpl::queryMediaStatistics(): pc: " << mHandle << " mediaStreamHandle: " << it->mediaStream << " mediaStreamId: " << it->mediaStreamId << " mediaType: " << it->mediaType);
      if (it->mediaType == PeerConnection::MediaType_Audio)
      {
         // StackLog(<< "ConferenceWebParticipantImpl::queryMediaStatistics(): pc: " << mHandle << " mediaStreamHandle: " << it->mediaStream << " with audio mediaType");
         PeerConnectionManagerInternal* pcMgr = dynamic_cast<PeerConnectionManagerInternal*>(mPeerConnMgr);
         if (pcMgr)
         {
            // DebugLog(<< "ConferenceWebParticipantImpl::queryMediaStatistics(): " << this << " query media statistics for pc: " << mHandle << " conference-bridge: " << mConfBridgeMgr->getConferenceHandle());
            mMediaQueries.insert(it->mediaStream);
            pcMgr->queryMediaStatistics(mHandle);
         }
      }
   }

   if (mMediaQueries.size() == 0)
   {
      DebugLog(<< "ConferenceWebParticipantImpl::queryMediaStatistics(): pc: " << mHandle << " - no existing media queries for conference-bridge: " << mConfBridgeMgr->getConferenceHandle());
      cpc::vector<ConferenceParticipantMediaStatistics> stats;
      mConfBridgeMgr->addWebParticipantMediaStatistics(mConfPartHandle, stats);
   }
   else
   {
      // StackLog(<< "ConferenceWebParticipantImpl::queryMediaStatistics(): pc: " << mHandle << " media query in progress for conference " << mConfBridgeMgr->getConferenceHandle());
   }
#endif
   return 0;
}

int ConferenceWebParticipantImpl::connectAllStreams()
{
   CPCAPI2::Media::MediaManagerInterface* mmi = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone));
   CPCAPI2::Media::Audio* audio = CPCAPI2::Media::Audio::getInterface(mmi);

   std::vector<int>::iterator itSfuSenders = mConfBridgeMgr->getSFUParticipantChannelsAudio().begin();
   for (; itSfuSenders != mConfBridgeMgr->getSFUParticipantChannelsAudio().end(); ++itSfuSenders)
   {
      std::vector<int>::iterator itSfuParts = mConfBridgeMgr->getSFUParticipantChannelsAudio().begin();
      for (; itSfuParts != mConfBridgeMgr->getSFUParticipantChannelsAudio().end(); ++itSfuParts)
      {
         if (*itSfuSenders != *itSfuParts)
         {
            audio->connectAudioStreams(*itSfuSenders, *itSfuParts);
         }
      }
   }
   return 0;
}

int ConferenceWebParticipantImpl::connectPresenterStream()
{
   cpc::vector<CPCAPI2::PeerConnection::MediaInfo>::const_iterator it = mMediaInfo.begin();
   for (; it != mMediaInfo.end(); ++it)
   {
      if (it->mediaType == PeerConnection::MediaType_Audio)
      {
         mConfBridgeMgr->setSFUSenderChannelAudio(it->mediaStreamId);
         CPCAPI2::Media::MediaManagerInterface* mmi = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone));
         CPCAPI2::Media::Audio* audio = CPCAPI2::Media::Audio::getInterface(mmi);

         std::vector<int>::iterator itSfuParts = mConfBridgeMgr->getSFUParticipantChannelsAudio().begin();
         for (; itSfuParts != mConfBridgeMgr->getSFUParticipantChannelsAudio().end(); ++itSfuParts)
         {
            if (it->mediaStreamId != *itSfuParts)
            {
               audio->connectAudioStreams(it->mediaStreamId, *itSfuParts);
            }
         }
         mConfBridgeMgr->getSFUParticipantChannelsAudio().clear();
      }
      else if (it->mediaType == PeerConnection::MediaType_Video)
      {
         mConfBridgeMgr->setSFUSenderChannelVideo(it->mediaStreamId);
         CPCAPI2::Media::MediaManagerInterface* mmi = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone));
         CPCAPI2::Media::Video* video = CPCAPI2::Media::Video::getInterface(mmi);
         video->connectVideoStreams(it->mediaStreamId, it->mediaStreamId);

         std::vector<std::weak_ptr<ConferenceWebParticipantImpl> >::iterator itSfuParts = mConfBridgeMgr->getSFUParticipantChannelsVideo().begin();
         for (; itSfuParts != mConfBridgeMgr->getSFUParticipantChannelsVideo().end(); ++itSfuParts)
         {
            if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = itSfuParts->lock())
            {
               if (cwp->mVideoSFUParticipantChannel != -1)
               {
                  video->connectVideoStreams(it->mediaStreamId, cwp->mVideoSFUParticipantChannel);
               }
            }
         }
         mConfBridgeMgr->getSFUParticipantChannelsVideo().clear();
      }
   }
   return 0;
}

int ConferenceWebParticipantImpl::connectParticipantStreams()
{
   // we are a participant in the conference -- connect the presenter's send stream to our receive stream
   cpc::vector<CPCAPI2::PeerConnection::MediaInfo>::const_iterator it = mMediaInfo.begin();
   for (; it != mMediaInfo.end(); ++it)
   {
      if (it->mediaType == PeerConnection::MediaType_Audio)
      {
         if (mConfBridgeMgr->getSFUSenderChannelAudio() == -1)
         {
            mConfBridgeMgr->getSFUParticipantChannelsAudio().push_back(it->mediaStreamId);
         }
         else
         {
            int senderChannel = mConfBridgeMgr->getSFUSenderChannelAudio();
            if (senderChannel != it->mediaStreamId)
            {
               CPCAPI2::Media::MediaManagerInterface* mmi = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone));
               CPCAPI2::Media::Audio* audio = CPCAPI2::Media::Audio::getInterface(mmi);
               audio->connectAudioStreams(senderChannel, it->mediaStreamId);
            }
         }
      }
      else if (it->mediaType == PeerConnection::MediaType_Video)
      {
         if (mConfBridgeMgr->getSFUSenderChannelVideo() == -1)
         {
            mVideoSFUParticipantChannel = it->mediaStreamId;
            mConfBridgeMgr->getSFUParticipantChannelsVideo().push_back(shared_from_this());
         }
         else
         {
            int senderChannel = mConfBridgeMgr->getSFUSenderChannelVideo();
            CPCAPI2::Media::MediaManagerInterface* mmi = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone));
            CPCAPI2::Media::Video* video = CPCAPI2::Media::Video::getInterface(mmi);
            video->connectVideoStreams(senderChannel, it->mediaStreamId);
         }
      }
   }
   return 0;
}

// PeerConnectionHandler

int ConferenceWebParticipantImpl::onSignalingStateChange(PeerConnectionHandle pc, const SignalingStateChangeEvent& args)
{
   return 0;
}

int ConferenceWebParticipantImpl::onCreateOfferResult(PeerConnectionHandle pc, const CreateOfferResult& args)
{
   if (pc == mHandle)
   {
      InfoLog(<< "ConferenceWebParticipantImpl::onCreateOfferResult(): pc: " << pc << ", sdp: " << args.sdp.sdpString);
      if (mPeerConnectionAnswerHandler != NULL)
      {
         mPeerConnMgr->setLocalDescription(mHandle, args.sdp);
         PeerConnectionAnswerEvent answerArgs;
         answerArgs.participant = mConfPartHandle;
         answerArgs.sdpAnswer = args.sdp;
         PeerConnectionAnswerHandler* pcah = mPeerConnectionAnswerHandler;
         mPeerConnectionAnswerHandler = NULL;
         DebugLog(<< "ConferenceWebParticipantImpl::onCreateOfferResult(): pc: " << pc << " - firing onPeerConnectionAnswer (oops I mean offer) for participant " << mConfPartHandle);
         mConfBridgeMgr->firePeerConnectionAnswerEvent(pcah, cpcFunc(PeerConnectionAnswerHandler::onPeerConnectionAnswer), mConfBridgeMgr->getConferenceHandle(), answerArgs);
      }
   }
   return 0;
}

int ConferenceWebParticipantImpl::onCreateAnswerResult(PeerConnectionHandle pc, const CreateAnswerResult& args)
{
   if (pc == mHandle)
   {
      InfoLog(<< "ConferenceWebParticipantImpl::onCreateAnswerResult(): pc: " << pc << ", sdp: " << args.sdp.sdpString);
      if (mPeerConnectionAnswerHandler != NULL)
      {
         mPeerConnMgr->setLocalDescription(mHandle, args.sdp);
         PeerConnectionAnswerEvent answerArgs;
         answerArgs.participant = mConfPartHandle;
         answerArgs.sdpAnswer = args.sdp;
         PeerConnectionAnswerHandler* pcah = mPeerConnectionAnswerHandler;
         mPeerConnectionAnswerHandler = NULL;
         DebugLog(<< "ConferenceWebParticipantImpl::onCreateAnswerResult(): pc: " << pc << " - firing onPeerConnectionAnswer for participant " << mConfPartHandle);
         mConfBridgeMgr->firePeerConnectionAnswerEvent(pcah, cpcFunc(PeerConnectionAnswerHandler::onPeerConnectionAnswer), mConfBridgeMgr->getConferenceHandle(), answerArgs);
      }
   }
   return 0;
}

int ConferenceWebParticipantImpl::onSetLocalSessionDescriptionResult(PeerConnectionHandle pc, const SetLocalSessionDescriptionResult& args)
{
   if (pc == mHandle)
   {
      mMediaInfo = args.mediaInfo;
      takeParticipantSnapshot();

      if (mTranscriptionEnabled && mAudioTransHandle == (CPCAPI2::AudioTrans::AudioTransHandle)-1)
      {
         setTranscriptionEnabled(true);
      }

      if (mPartPhoto.size() > 0)
      {
         setParticipantPhoto(mPartPhoto);
      }

      if (args.mediaInfo.size() > 0)
      {
         if (mConfBridgeMgr->getConferenceSettings().participantJoinSound.size() > 0)
         {
            mPeerConnMgr->playSound(mHandle, mConfBridgeMgr->getConferenceSettings().participantJoinSound, false);
         }
      }

      if (mConfBridgeMgr->getConferenceSettings().mixMode == ConferenceMixMode_MCU)
      {
         if (mPartInfo.hasFloor)
         {
            // re-add to floor here in case the ConferenceWebParticipant was created with hasFloor == true,
            // since at that time mMediaInfo would still be empty
            addToFloor(true);
         }
      }
      else if (mConfBridgeMgr->getConferenceSettings().mixMode == ConferenceMixMode_SFU)
      {
         if (mPartInfo.hasFloor)
         {
            // we are the presenter for this conference -- everyone should end up seeing only our send stream
            connectPresenterStream();
         }
         else
         {
            connectParticipantStreams();
         }
      }
      else if (mConfBridgeMgr->getConferenceSettings().mixMode == ConferenceMixMode_SFU_BiDi)
      {
         cpc::vector<CPCAPI2::PeerConnection::MediaInfo>::const_iterator it = mMediaInfo.begin();
         for (; it != mMediaInfo.end(); ++it)
         {
            if (it->mediaType == PeerConnection::MediaType_Audio)
            {
               mConfBridgeMgr->getSFUParticipantChannelsAudio().push_back(it->mediaStreamId);
            }
         }
         connectAllStreams();
      }

      //mConfBridgeMgr->queryConferenceDetails();
   }
   return 0;
}

int ConferenceWebParticipantImpl::onSetRemoteSessionDescriptionResult(PeerConnectionHandle pc, const SetRemoteSessionDescriptionResult& args)
{
   return 0;
}

int ConferenceWebParticipantImpl::onError(PeerConnectionHandle pc, const CPCAPI2::PeerConnection::ErrorEvent& args)
{
   return 0;
}

int ConferenceWebParticipantImpl::onMediaInactivity(PeerConnectionHandle pc, const CPCAPI2::PeerConnection::MediaInactivityEvent& args)
{
   if (pc == mHandle)
   {
      cpc::vector<CPCAPI2::PeerConnection::MediaInfo>::const_iterator it = mMediaInfo.begin();
      for (; it != mMediaInfo.end(); ++it)
      {
         if (it->mediaType == PeerConnection::MediaType_Audio)
         {
            if (mConfBridgeMgr->getSFUSenderChannelAudio() == it->mediaStreamId)
            {
               WarningLog(<< "ConferenceWebParticipantImpl::onMediaInactivity(): pc: " << pc << " participant: " << mConfPartHandle << " media-stream: " << it->mediaStream << " stream-id: " << it->mediaStreamId << " destroying web participant due to media inactivity");
               mConfBridgeMgr->destroyWebParticipant(mConfPartHandle);
               break;
            }
         }
      }
   }
   return 0;
}

int ConferenceWebParticipantImpl::onPeerConnectionMediaStatistics(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const PeerConnectionMediaStatisticsEvent& args)
{
   if (mMediaQueries.size() == 0)
   {
      DebugLog(<< "ConferenceWebParticipantImpl::onPeerConnectionMediaStatistics(): " << this << " pc: " << mHandle << " - ignore media statistics as no query in progress for conference-bridge: " << mConfBridgeMgr->getConferenceHandle());
      return 0;
   }

   DebugLog(<< "ConferenceWebParticipantImpl::onPeerConnectionMediaStatistics(): " << this << " pc: " << mHandle << " conference-bridge: " << mConfBridgeMgr->getConferenceHandle() << " media-query count: " << mMediaQueries.size() << " media-stats: " << args.mediaStreamStats.size());
   cpc::vector<ConferenceParticipantMediaStatistics> stats;
   for (cpc::vector<PeerConnectionMediaStreamStatistics>::const_iterator i = args.mediaStreamStats.begin(); i != args.mediaStreamStats.end(); ++i)
   {
      ConferenceParticipantMediaStatistics stat;
      stat.mediaStream = (*i).mediaStream;
      stat.rtpPacketCount = (*i).rtpPacketCount;
      stat.rtcpPacketCount = (*i).rtcpPacketCount;
      stats.push_back(stat);
      // DebugLog(<< "ConferenceWebParticipantImpl::onPeerConnectionMediaStatistics(): " << this << " pc: " << mHandle << " remove media-stream: " << (*i).mediaStream << " from query list");
      mMediaQueries.erase((*i).mediaStream);
   }

   if (mMediaQueries.size() == 0)
   {
      // DebugLog(<< "ConferenceWebParticipantImpl::onPeerConnectionMediaStatistics(): " << this << " pc: " << mHandle << " conference-bridge: " << mConfBridgeMgr->getConferenceHandle() << " mConfPartHandle: " << mConfPartHandle << " no media queries pending");
      mConfBridgeMgr->addWebParticipantMediaStatistics(mConfPartHandle, stats);
      return 0;
   }

   return 0;
}

void ConferenceWebParticipantImpl::onSnapshotComplete(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const cpc::string& filenameUtf8)
{
   if (pc == mHandle)
   {
      //mPartInfo.snapshotFilenameUtf8 = filenameUtf8;

      cpc::vector<ParticipantInfo> updatedParticipants;
      updatedParticipants.push_back(mPartInfo);

      mConfBridgeMgr->fireParticipantListState(cpc::vector<ParticipantInfo>(), updatedParticipants, cpc::vector<ParticipantInfo>());
   }
}

int ConferenceWebParticipantImpl::onTranscriptionResult(CPCAPI2::AudioTrans::AudioTransHandle audioTransSession, const CPCAPI2::AudioTrans::AudioTranscriptionResult& evt)
{
   InfoLog(<< "ConferenceWebParticipantImpl::onTranscriptionResult");
   if (mAudioTransHandle == audioTransSession)
   {
      ConferenceTranscriptionEvent args;
      args.participant = mPartInfo.participant;
      args.transcriptionResult = evt.result;
      args.resultSetId = evt.resultSetId;
      mConfBridgeMgr->fireEvent(cpcFunc(ConferenceBridgeHandler::onConferenceTranscriptionResult), args);
   }
   return 0;
}

}

}

#endif
