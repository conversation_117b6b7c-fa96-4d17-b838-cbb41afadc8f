#include <memory>
#include <optional>

#include "impl/jsonrpc/PhoneInstances.h"
#include "gen/Phone/server/IPhone.h"
#include "gen/SipAccount/server/ISipAccount.h"

namespace CPCAPI2
{
namespace SipAccount
{
class SipAccountManager;
}
}

namespace jsonrpc
{
namespace CPCAPI2
{
namespace SipAccount
{
class SipAccountCompatibility : public jsonrpc::CPCAPI2::SipAccount::ISipAccount
{
public:
	SipAccountCompatibility(const std::shared_ptr<::CPCAPI2::PhoneInstances>& phones);

	virtual int64_t create(const int64_t phoneHandle) override;

	virtual void destroy(const int64_t phoneHandle, const int64_t sipAccountHandle) override;

	virtual void configureDefaultAccountSettings(const int64_t phoneHandle, const int64_t sipAccountHandle, const SipAccountSettings& sipAccountSettings) override;

	virtual void configureTransportAccountSettings(const int64_t phoneHandle, const int64_t sipAccountHandle, const SipAccountSettings& sipAccountSettings, const SipAccountNetworkTransport transport) override;

	virtual void applySettings(const int64_t phoneHandle, const int64_t sipAccountHandle) override;

	virtual void enable(const int64_t phoneHandle, const int64_t sipAccountHandle) override;

	virtual void disable(const int64_t phoneHandle, const int64_t sipAccountHandle) override;

private:
	::CPCAPI2::SipAccount::SipAccountManager* getInstance(const int64_t phoneHandle);
	std::shared_ptr<::CPCAPI2::PhoneInstances> mPhones;
	


}; // class SipAccountCompatibility
} // namepace SipAccount
} // namepace CPCAPI2
} // namepace jsonrpc
