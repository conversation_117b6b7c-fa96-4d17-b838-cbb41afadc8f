#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTAPPLYSETTINGS_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTAPPLYSETTINGS_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  class SipAccountApplySettings
  {
  public:
    int32_t phoneHandle;
    int32_t sipAccountHandle;

    SipAccountApplySettings() = default;

    SipAccountApplySettings(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipAccountApplySettings& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["sipAccountHandle"] = o.sipAccountHandle;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipAccountApplySettings& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.sipAccountHandle = j.at("sipAccountHandle");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTAPPLYSETTINGS_H
