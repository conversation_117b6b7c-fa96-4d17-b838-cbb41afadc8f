#include "SipAccountStateImpl.h"
#include "SipAccountInterface.h"

namespace CPCAPI2
{
namespace SipAccount
{
SipAccountStateImpl::SipAccountStateImpl(SipAccountInterface* acctIf)
   : mAcctIf(acctIf)
{
}

SipAccountStateImpl::~SipAccountStateImpl()
{
}

void SipAccountStateImpl::Release()
{
   delete this;
}

int SipAccountStateImpl::getStateAllAccounts(cpc::vector<CPCAPI2::SipAccount::SipAccountState>& accountState)
{
   AccountStateMap::const_iterator it = mStateMap.begin();
   for (; it != mStateMap.end(); ++it)
   {
      accountState.push_back(it->second);
   }
   return 0;
}

int SipAccountStateImpl::onAccountConfigured(SipAccountHandle account, const SipAccountConfiguredEvent& args)
{
   return 0;
}

int SipAccountStateImpl::onAccountEnabled(SipAccountHandle account, const SipAccountEnabledEvent& args)
{
   if (mStateMap.count(account) == 0)
   {
      mStateMap[account] = SipAccountState();
   }
   AccountStateMap::iterator it = mStateMap.find(account);
   if (it != mStateMap.end())
   {
      it->second.account = account;
      it->second.settings = args.settings;
   }
   return 0;
}

int SipAccountStateImpl::onAccountStatusChanged(SipAccountHandle account, const SipAccountStatusChangedEvent& args)
{
   if (mStateMap.count(account) == 0)
   {
      mStateMap[account] = SipAccountState();
   }
   AccountStateMap::iterator it = mStateMap.find(account);
   if (it != mStateMap.end())
   {
      it->second.account = account;
      it->second.accountStatus = args.accountStatus;
   }
   return 0;
}

int SipAccountStateImpl::onError(SipAccountHandle account, const ErrorEvent& args)
{
   return 0;
}
}
}
