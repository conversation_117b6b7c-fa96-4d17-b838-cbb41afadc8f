#include "RLMIContents.h"
#include "../util/LogSubsystems.h"
#include "../util/libXmlHelper.h"
#include "../util/LibxmlSharedUsage.h"

#include <resip/stack/Contents.hxx>
#include <resip/stack/Symbols.hxx>
#include <rutil/Logger.hxx>
#include <sstream>

using namespace resip;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_EVENT



namespace CPCAPI2
{
namespace SipEvent
{

static bool invokeRLMIContentsInit = RLMIContents::init();

bool
RLMIContents::init()
{
   static ContentsFactory<RLMIContents> factory;
   (void)factory;
   return true;
}

RLMIContents::RLMIContents(void)
   : Contents(getStaticType()),
   mVersion(0),
   mState(RLMIContents::full),
   mContentLength(0)
{
}

RLMIContents::RLMIContents(const HeaderFieldValue& hfv, const Mime& contentType)
   : Contents(hfv, contentType),
   mVersion(0),
   mState(RLMIContents::full),
   mContentLength(hfv.getLength())
{
}

RLMIContents::RLMIContents(const RLMIContents& rhs)
   : Contents(rhs),
   mVersion(rhs.mVersion),
   mState(rhs.mState),
   mEntity(rhs.mEntity),
   mNames(rhs.getNames()),
   mResources(rhs.getResources()),
   mContentLength(rhs.mContentLength)
{
}

RLMIContents::~RLMIContents(void)
{
}

RLMIContents&
RLMIContents::operator=(const RLMIContents& rhs)
{
   if (this != &rhs)
   {
      Contents::operator=(rhs);
   }
   return *this;
}

Contents* 
RLMIContents::clone() const
{
   return new RLMIContents(*this);
}

const Mime& 
RLMIContents::getStaticType() 
{
   static Mime type("application","rlmi+xml");
   return type;
}

const RLMIContents::Resources& 
RLMIContents::getResources() const
{
   checkParsed();
   return mResources;
}

const RLMIContents::Names& 
RLMIContents::getNames() const
{
   checkParsed();
   return mNames;
}

std::ostream& 
RLMIContents::encodeParsed(std::ostream& str) const
{
   str << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" << Symbols::CRLF;
   str << "<list xmlns=\"urn:ietf:params:xml:ns:rlmi\"" << Symbols::CRLF;
   str << "   version=\"" << mVersion << "\"" << Symbols::CRLF;
   str << "   fullState=\"" << (mState == RLMIContents::full ? "true" : "false") << "\"" << Symbols::CRLF;
   str << "   uri=\"" << mEntity << "\">" << Symbols::CRLF;

   str << "</list>" << Symbols::CRLF;
   return str;
}

void
RLMIContents::parse(ParseBuffer& pb)
{
   int ret = 0;

   //std::cout << "parsing " << pb.start() << std::endl;

   //const char* origStart = pb.start();
   //unsigned int len = pb.skipToChars("</list>") + 7 - origStart;
   int len = (int)mContentLength;

   xmlTextReaderPtr reader = xmlReaderForMemory(
      pb.start(),
      len,
      NULL,
      "UTF-8",
      0);

   if (reader != NULL)
   {
      ret = xmlTextReaderRead(reader);
      while (ret == 1)
      {
         std::string nodeName = xmlString(xmlTextReaderName(reader));
         if (nodeName == "list")
         {
            if (xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT) // start element
            {
               if (xmlTextReaderHasAttributes(reader) == XML_READER_TYPE_ELEMENT)
               {
                  std::string ver = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"version"));
                  mVersion = atoi(ver.c_str());

                  std::string entity = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"uri"));
                  mEntity = resip::Uri(entity.c_str());

                  std::string state = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"fullState"));
                  mState = (state == "true" ? RLMIContents::full : RLMIContents::partial);
               }
            }
            else if (xmlTextReaderNodeType(reader) == XML_READER_TYPE_END_ELEMENT) // end element
            {
               break;
            }
         }
         else if (nodeName == "name" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            Name n;
            RLMIContents::parseName(reader, &n);
            mNames.push_back(n);
         }
         else if (nodeName == "resource" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            RLMIContents::Resource r;
            r.parse(reader);
            mResources.push_back(r);
         }
         ret = xmlTextReaderRead(reader);
      }
      xmlFreeTextReader(reader);
      if (ret != 0)
      {
         /* SUA_TRACES_ERROR(SUA_LEVEL_MIN, "XML parse error") */
      }
   }
}

RLMIContents::Resource::Resource(const RLMIContents::Resource& rhs)
   : mUri(rhs.mUri),
   mNames(rhs.mNames),
   mInstances(rhs.mInstances)
{
}

void
RLMIContents::Resource::parse(xmlTextReaderPtr reader)
{
   if (xmlTextReaderHasAttributes(reader) == XML_READER_TYPE_ELEMENT)
   {
      if (xmlTextReaderMoveToFirstAttribute(reader) == XML_READER_TYPE_ELEMENT)
      {
         do
         {
            std::string attribName = xmlString(xmlTextReaderName(reader));
            std::string attribVal= xmlString(xmlTextReaderValue(reader));

            if (attribName == "uri") // required
            {
               mUri = resip::Uri(attribVal.c_str());
            }
         }
         while (xmlTextReaderMoveToNextAttribute(reader) == XML_READER_TYPE_ELEMENT);
      }

      if (xmlTextReaderRead(reader) == XML_READER_TYPE_ELEMENT)
      {
         do
         {
            std::string elementName = xmlString(xmlTextReaderName(reader));
            int nodeType = xmlTextReaderNodeType(reader);

            if (elementName == "instance")
            {
               RLMIContents::Resource::Instance inst;
               inst.parse(reader);
               mInstances.push_back(inst);
            }
            else if (elementName == "name")
            {
               Name n;
               RLMIContents::parseName(reader, &n);
               mNames.push_back(n);
            }
            else if (elementName == "resource" && nodeType == XML_READER_TYPE_END_ELEMENT) // end element
            {
               break;
            }
         }
         while (xmlTextReaderNext(reader) == 1);
      }
   }
}

void 
RLMIContents::parseName(xmlTextReaderPtr reader, Name* pName)
{
   if (xmlTextReaderHasAttributes(reader) == 1)
   {
      std::string lang = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"xml:lang"));
      pName->first = lang.c_str();
   }
   int moveToElementResult = xmlTextReaderMoveToElement(reader);
   if (moveToElementResult == 1 || moveToElementResult == 0)
   {
      std::string elementValue = xmlString(xmlTextReaderReadString(reader));
      pName->second = elementValue.c_str();
   }
}

RLMIContents::Resource::Instance::Instance(const RLMIContents::Resource::Instance& rhs)
   : mId(rhs.mId),
   mSubscriptionState(rhs.mSubscriptionState),
   mTerminateReason(rhs.mTerminateReason),
   mContentId(rhs.mContentId)
{
}

resip::SubscriptionState 
RLMIContents::Resource::Instance::convertToSubscriptionState(const cpc::string& str)
{
   if (str == "active")
      return resip::Active;
   else if (str == "pending")
      return resip::Pending;
   else if (str == "terminated")
      return resip::Terminated;

   return resip::Terminated;
}

void
RLMIContents::Resource::Instance::parse(xmlTextReaderPtr reader)
{
   if (xmlTextReaderHasAttributes(reader) == 1)
   {
      if (xmlTextReaderMoveToFirstAttribute(reader) == 1)
      {
         do
         {
            std::string attribName = xmlString(xmlTextReaderName(reader));
            std::string attribVal = xmlString(xmlTextReaderValue(reader));

            if (attribName == "id") // required
            {
               mId = attribVal.c_str();
            }
            else if (attribName == "state") // required
            {
               mSubscriptionState = convertToSubscriptionState(attribVal.c_str());
            }
            else if (attribName == "reason") // optional
            {
               mTerminateReason = attribVal.c_str();
            }
            else if (attribName == "cid") // optional
            {
               mContentId = attribVal.c_str();
            }
         }
         while (xmlTextReaderMoveToNextAttribute(reader) == 1);
      }
   }
}

UInt32 RLMIContents::version(void) const
{
   checkParsed();
   return mVersion;
}

UInt32& RLMIContents::version(void)
{
   checkParsed();
   return (UInt32&)mVersion;
}

RLMIContents::StateType RLMIContents::stateType(void) const
{
   checkParsed();
   return mState;
}

RLMIContents::StateType& RLMIContents::stateType(void)
{
   checkParsed();
   return mState;
}

const resip::Uri& RLMIContents::entity(void) const
{
   checkParsed();
   return mEntity;
}

resip::Uri& RLMIContents::entity(void)
{
   checkParsed();
   return mEntity;
}

CPCAPI2::SipEvent::SipSubscriptionState RLMIContents::instanceStateToSdk(resip::SubscriptionState state)
{
   switch (state)
   {
   case resip::Active:
      return CPCAPI2::SipEvent::SipSubscriptionState_Active;
   case resip::Pending:
      return CPCAPI2::SipEvent::SipSubscriptionState_Pending;
   case resip::Terminated:
      return CPCAPI2::SipEvent::SipSubscriptionState_Terminated;
   default:
      return CPCAPI2::SipEvent::SipSubscriptionState_NotStarted;
   }  
}

}
}
