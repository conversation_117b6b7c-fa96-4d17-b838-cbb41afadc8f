#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "ConferenceConnectorJsonProxyInterface.h"
#include "confconnector/ConferenceConnectorHandler.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiClient.h"
#include "jsonapi/JsonApiClientInterface.h"
#include "json/JsonHelper.h"
#include "util/LogSubsystems.h"
#include "util/IpHelpers.h"

#include <rutil/Logger.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::CONF_CONNECTOR
#define JSON_MODULE "ConferenceConnectorJsonApi"

namespace CPCAPI2
{
namespace ConferenceConnector
{
ConferenceConnectorJsonProxyInterface::ConferenceConnectorJsonProxyInterface(Phone* phone)
   : CPCAPI2::EventSource<ConferenceConnectorHandle, ConferenceConnectorHandler, ConferenceConnectorJsonSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
     mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mTransport(NULL)
{
   mFunctionMap["onCreateConferenceConnector"] = std::bind(&ConferenceConnectorJsonProxyInterface::handleCreateConferenceConnector, this, std::placeholders::_1 );
   mFunctionMap["onCreateConferenceSession"] = std::bind(&ConferenceConnectorJsonProxyInterface::handleCreateConferenceSession, this, std::placeholders::_1);
   mFunctionMap["onServiceConnectionStatusChanged"] = std::bind(&ConferenceConnectorJsonProxyInterface::handleServiceConnectionStatusChanged, this, std::placeholders::_1);
   mFunctionMap["onConferenceCreated"] = std::bind(&ConferenceConnectorJsonProxyInterface::handleConferenceCreated, this, std::placeholders::_1);
   mFunctionMap["onConferenceEnded"] = std::bind(&ConferenceConnectorJsonProxyInterface::handleConferenceEnded, this, std::placeholders::_1);
   mFunctionMap["onConferenceListUpdated"] = std::bind(&ConferenceConnectorJsonProxyInterface::handleConfereceListUpdated, this, std::placeholders::_1);
   mFunctionMap["onConferenceParticipantListUpdated"] = std::bind(&ConferenceConnectorJsonProxyInterface::handleConfereceParticipantListUpdated, this, std::placeholders::_1);
   mFunctionMap["onConferenceSessionStatusChanged"] = std::bind(&ConferenceConnectorJsonProxyInterface::handleConferenceSessionStatusChanged, this, std::placeholders::_1);
   mFunctionMap["onConferenceSessionMediaStatusChanged"] = std::bind(&ConferenceConnectorJsonProxyInterface::handleConferenceSessionMediaStatusChanged, this, std::placeholders::_1);

   JsonApi::JsonApiClientInterface* jsonApiClientIf = dynamic_cast<JsonApi::JsonApiClientInterface*>(JsonApi::JsonApiClient::getInterface(phone));
   setTransport(jsonApiClientIf->getTransport());

   mPhone->addRefImpl();
}

ConferenceConnectorJsonProxyInterface::~ConferenceConnectorJsonProxyInterface()
{
   mPhone->releaseImpl();
}

void ConferenceConnectorJsonProxyInterface::Release()
{
   delete this;
}

// JsonApiClientModule
void ConferenceConnectorJsonProxyInterface::setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport)
{
   mTransport = transport;
}

int ConferenceConnectorJsonProxyInterface::processIncoming(const std::shared_ptr<rapidjson::Document>& request)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::processIncomingImpl, this, request));
   return kSuccess;
}

void ConferenceConnectorJsonProxyInterface::processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

int ConferenceConnectorJsonProxyInterface::setHandler(ConferenceConnectorHandle h, ConferenceConnectorHandler* handler)
{
   setAppHandler(h, handler);
   return kSuccess;
}

ConferenceConnectorHandle ConferenceConnectorJsonProxyInterface::createConferenceConnector()
{
   auto hF = mServerCreatedConfConnectorHandle.get_future();
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::createConferenceConnectorImpl, this));
   ConferenceConnectorHandle h = hF.get();
   mServerCreatedConfConnectorHandle = std::promise<ConferenceConnectorHandle>(); // reset for next use
   return h;
}

void ConferenceConnectorJsonProxyInterface::createConferenceConnectorImpl()
{
   StackLog(<< "ConferenceConnectorJsonProxyInterface::createConferenceConnectorImpl(): phone: " << mPhone);
   JsonFunctionCall(mTransport, "createConferenceConnector");
}

int ConferenceConnectorJsonProxyInterface::destroyConferenceConnector(ConferenceConnectorHandle conn)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::destroyConferenceConnectorImpl, this, conn));
   return kSuccess;
}

void ConferenceConnectorJsonProxyInterface::destroyConferenceConnectorImpl(ConferenceConnectorHandle conn)
{
   JsonFunctionCall(mTransport, "destroyConferenceConnector", JSON_VALUE(conn));
}

int ConferenceConnectorJsonProxyInterface::setConnectionSettings(ConferenceConnectorHandle conn, const ConferenceConnectorSettings& settings)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::setConnectionSettingsImpl, this, conn, settings));
   return kSuccess;
}

void ConferenceConnectorJsonProxyInterface::setConnectionSettingsImpl(ConferenceConnectorHandle conn, const ConferenceConnectorSettings& settings)
{
   JsonFunctionCall(mTransport, "setConnectionSettings", JSON_VALUE(conn), JSON_VALUE(settings));
}

int ConferenceConnectorJsonProxyInterface::connectToConferenceService(ConferenceConnectorHandle conn)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::connectToConferenceServiceImpl, this, conn));
   return kSuccess;
}

void ConferenceConnectorJsonProxyInterface::connectToConferenceServiceImpl(ConferenceConnectorHandle conn)
{
   JsonFunctionCall(mTransport, "connectToConferenceService", JSON_VALUE(conn));
}

int ConferenceConnectorJsonProxyInterface::disconnectFromConferenceService(ConferenceConnectorHandle conn)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::disconnectFromConferenceServiceImpl, this, conn));
   return kSuccess;
}

void ConferenceConnectorJsonProxyInterface::disconnectFromConferenceServiceImpl(ConferenceConnectorHandle conn)
{
   JsonFunctionCall(mTransport, "disconnectFromConferenceService", JSON_VALUE(conn));
}

int ConferenceConnectorJsonProxyInterface::queryConferenceList(ConferenceConnectorHandle conn)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::queryConferenceListImpl, this, conn));
   return kSuccess;
}

void ConferenceConnectorJsonProxyInterface::queryConferenceListImpl(ConferenceConnectorHandle conn)
{
   JsonFunctionCall(mTransport, "queryConferenceList", JSON_VALUE(conn));
}

int ConferenceConnectorJsonProxyInterface::createNewConference(ConferenceConnectorHandle conn, const CloudConferenceSettings& settings)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::createNewConferenceImpl, this, conn, settings));
   return kSuccess;
}

void ConferenceConnectorJsonProxyInterface::createNewConferenceImpl(ConferenceConnectorHandle conn, const CloudConferenceSettings& settings)
{
   JsonFunctionCall(mTransport, "createNewConference", JSON_VALUE(conn), JSON_VALUE(settings));
}

int ConferenceConnectorJsonProxyInterface::destroyConference(CloudConferenceHandle conference)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::destroyConferenceImpl, this, conference));
   return kSuccess;
}

void ConferenceConnectorJsonProxyInterface::destroyConferenceImpl(CloudConferenceHandle conference)
{
   JsonFunctionCall(mTransport, "destroyConference", JSON_VALUE(conference));
}

int ConferenceConnectorJsonProxyInterface::queryParticipantList(CloudConferenceHandle conference)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::queryParticipantListImpl, this, conference));
   return kSuccess;
}

void ConferenceConnectorJsonProxyInterface::queryParticipantListImpl(CloudConferenceHandle conference)
{
   JsonFunctionCall(mTransport, "queryParticipantList", JSON_VALUE(conference));
}

CloudConferenceSessionHandle ConferenceConnectorJsonProxyInterface::createConferenceSession(CloudConferenceHandle conference)
{
   auto hF = mServerCreatedConfSessionHandle.get_future();
   CloudConferenceSessionSettings settings;
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::createConferenceSessionImpl, this, conference, settings));
   CloudConferenceSessionHandle h = hF.get();
   mServerCreatedConfConnectorHandle = std::promise<CloudConferenceSessionHandle>(); // reset for next use
   return h;
}

CloudConferenceSessionHandle ConferenceConnectorJsonProxyInterface::createConferenceSession(CloudConferenceHandle conference, const CloudConferenceSessionSettings& settings)
{
   auto hF = mServerCreatedConfSessionHandle.get_future();
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::createConferenceSessionImpl, this, conference, settings));
   CloudConferenceSessionHandle h = hF.get();
   mServerCreatedConfConnectorHandle = std::promise<CloudConferenceSessionHandle>(); // reset for next use
   return h;
}

void ConferenceConnectorJsonProxyInterface::createConferenceSessionImpl(CloudConferenceHandle conference, const CloudConferenceSessionSettings& settings)
{
   JsonFunctionCall(mTransport, "createConferenceSession", JSON_VALUE(conference), JSON_VALUE(settings));
}

int ConferenceConnectorJsonProxyInterface::setSessionSettings(CloudConferenceSessionHandle session, const CloudConferenceSessionSettings& settings)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::setSessionSettingsImpl, this, session, settings));
   return kSuccess;
}

void ConferenceConnectorJsonProxyInterface::setSessionSettingsImpl(CloudConferenceSessionHandle session, const CloudConferenceSessionSettings& settings)
{
   JsonFunctionCall(mTransport, "setSessionSettings", JSON_VALUE(session), JSON_VALUE(settings));
}

int ConferenceConnectorJsonProxyInterface::setSessionMediaSettings(CloudConferenceSessionHandle session, const CloudConferenceSessionMediaSettings& settings)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::setSessionMediaSettingsImpl, this, session, settings));
   return kSuccess;
}

void ConferenceConnectorJsonProxyInterface::setSessionMediaSettingsImpl(CloudConferenceSessionHandle session, const CloudConferenceSessionMediaSettings& settings)
{
   JsonFunctionCall(mTransport, "setSessionMediaSettings", JSON_VALUE(session), JSON_VALUE(settings));
}

int ConferenceConnectorJsonProxyInterface::startSession(CloudConferenceSessionHandle session)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::startSessionImpl, this, session));
   return kSuccess;
}

void ConferenceConnectorJsonProxyInterface::startSessionImpl(CloudConferenceSessionHandle session)
{
   JsonFunctionCall(mTransport, "startSession", JSON_VALUE(session));
}

int ConferenceConnectorJsonProxyInterface::endSession(CloudConferenceSessionHandle session)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::endSessionImpl, this, session));
   return kSuccess;
}

void ConferenceConnectorJsonProxyInterface::endSessionImpl(CloudConferenceSessionHandle session)
{
   JsonFunctionCall(mTransport, "endSession", JSON_VALUE(session));
}

int ConferenceConnectorJsonProxyInterface::updateSessionMedia(CloudConferenceSessionHandle session)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::updateSessionMediaImpl, this, session));
   return kSuccess;
}

void ConferenceConnectorJsonProxyInterface::updateSessionMediaImpl(CloudConferenceSessionHandle session)
{
   JsonFunctionCall(mTransport, "updateSessionMedia", JSON_VALUE(session));
}

int ConferenceConnectorJsonProxyInterface::setParticipantPermissions(CloudConferenceSessionHandle session, CloudConferenceParticipantHandle participant, const ConferenceSessionPermissions& permissions)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::setParticipantPermissionsImpl, this, session, participant, permissions));
   return kSuccess;
}

void ConferenceConnectorJsonProxyInterface::setParticipantPermissionsImpl(CloudConferenceSessionHandle session, CloudConferenceParticipantHandle participant, const ConferenceSessionPermissions& permissions)
{
   JsonFunctionCall(mTransport, "setParticipantPermissions", JSON_VALUE(session), JSON_VALUE(participant), JSON_VALUE(permissions));
}

int ConferenceConnectorJsonProxyInterface::pauseSessionPlayout(CloudConferenceSessionHandle session)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::pauseSessionPlayoutImpl, this, session));
   return kSuccess;
}

void ConferenceConnectorJsonProxyInterface::pauseSessionPlayoutImpl(CloudConferenceSessionHandle session)
{
   JsonFunctionCall(mTransport, "pauseSessionPlayout", JSON_VALUE(session));
}

int ConferenceConnectorJsonProxyInterface::resumeSessionPlayout(CloudConferenceSessionHandle session)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonProxyInterface::resumeSessionPlayoutImpl, this, session));
   return kSuccess;
}

void ConferenceConnectorJsonProxyInterface::resumeSessionPlayoutImpl(CloudConferenceSessionHandle session)
{
   JsonFunctionCall(mTransport, "resumeSessionPlayout", JSON_VALUE(session));
}

int ConferenceConnectorJsonProxyInterface::handleCreateConferenceConnector(const rapidjson::Value& functionObjectVal)
{
   ConferenceConnectorHandle h = 0;
   JsonDeserialize(functionObjectVal, "conn", h);
   mServerCreatedConfConnectorHandle.set_value(h);
   return kSuccess;
}

int ConferenceConnectorJsonProxyInterface::handleCreateConferenceSession(const rapidjson::Value& functionObjectVal)
{
   CloudConferenceSessionHandle h = 0;
   JsonDeserialize(functionObjectVal, "session", h);
   mServerCreatedConfSessionHandle.set_value(h);
   return kSuccess;
}

int ConferenceConnectorJsonProxyInterface::handleServiceConnectionStatusChanged(const rapidjson::Value& functionObjectVal)
{
   ConferenceConnectorHandle h = 0;
   ServiceConnectionStatusEvent evt;
   JsonDeserialize(functionObjectVal, "conn", h,
      "authToken", evt.authToken.authToken,
      "connectionStatus", evt.connectionStatus,
      "serverUri", evt.serverUri,
      "statusDesc", evt.statusDesc);

   fireEvent(cpcFunc(ConferenceConnectorHandler::onServiceConnectionStatusChanged), h, evt);
   return kSuccess;
}

int ConferenceConnectorJsonProxyInterface::handleConferenceCreated(const rapidjson::Value& functionObjectVal)
{
   ConferenceConnectorHandle h = 0;
   ConferenceCreatedEvent evt;
   JsonDeserialize(functionObjectVal, "conn", h, "conference", evt.conference);

   fireEvent(cpcFunc(ConferenceConnectorHandler::onConferenceCreated), h, evt);
   return kSuccess;
}

int ConferenceConnectorJsonProxyInterface::handleConferenceEnded(const rapidjson::Value& functionObjectVal)
{
   ConferenceConnectorHandle h = 0;
   ConferenceEndedEvent evt;
   JsonDeserialize(functionObjectVal, "conn", h, "conference", evt.conference);

   fireEvent(cpcFunc(ConferenceConnectorHandler::onConferenceEnded), h, evt);
   return kSuccess;
}

int ConferenceConnectorJsonProxyInterface::handleConfereceListUpdated(const rapidjson::Value& functionObjectVal)
{
   ConferenceConnectorHandle h = 0;
   ConferenceListUpdatedEvent evt;
   JsonDeserialize(functionObjectVal, "conn", h, "conferenceList", evt.conferenceList);

   fireEvent(cpcFunc(ConferenceConnectorHandler::onConferenceListUpdated), h, evt);
   return kSuccess;
}

int ConferenceConnectorJsonProxyInterface::handleConfereceParticipantListUpdated(const rapidjson::Value& functionObjectVal)
{
   ConferenceConnectorHandle h = 0;
   ConferenceParticipantListUpdatedEvent evt;
   JsonDeserialize(functionObjectVal, "conn", h,
      "conference", evt.conference,
      "participantList", evt.participantList,
      "addedParticipants", evt.addedParticipants,
      "removedParticipants", evt.removedParticipants,
      "updatedParticipants", evt.updatedParticipants);

   fireEvent(cpcFunc(ConferenceConnectorHandler::onConferenceParticipantListUpdated), h, evt);
   return kSuccess;
}

int ConferenceConnectorJsonProxyInterface::handleConferenceSessionStatusChanged(const rapidjson::Value& functionObjectVal)
{
   ConferenceConnectorHandle h = 0;
   ConferenceSessionStatusChangedEvent evt;
   JsonDeserialize(functionObjectVal, "conn", h,
      "conference", evt.conference,
      "session", evt.session,
      "sessionStatus", evt.sessionStatus,
      "audio", evt.audio,
      "video", evt.video,
      "screenshare", evt.screenshare);

   fireEvent(cpcFunc(ConferenceConnectorHandler::onConferenceSessionStatusChanged), h, evt);
   return kSuccess;
}

int ConferenceConnectorJsonProxyInterface::handleConferenceSessionMediaStatusChanged(const rapidjson::Value& functionObjectVal)
{
   ConferenceConnectorHandle h = 0;
   ConferenceSessionMediaStatusChangedEvent evt;
   JsonDeserialize(functionObjectVal, "conn", h,
      "conference", evt.conference,
      "session", evt.session,
      "mediaStatus", evt.mediaStatus);

   fireEvent(cpcFunc(ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged), h, evt);
   return kSuccess;
}

}
}
#endif
