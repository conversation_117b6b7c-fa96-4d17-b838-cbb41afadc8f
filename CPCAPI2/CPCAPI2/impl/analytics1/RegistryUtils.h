#pragma once
#ifndef __CPCAPI2_REGISTRYUTILS_H__
#define __CPCAPI2_REGISTRYUTILS_H__

#include <string>
#include <vector>
#include <stdint.h>

// Forward decl
struct msrp_tree;

#define SEP "/"

namespace CPCAPI2
{
   namespace Analytics
   {
      /**
       * Enum, used to differentiate the different kinds of data which
       * may be stored in the registry.
       */
      typedef enum RegistryNodeType
      {
         RegistryNodeType_string,
         RegistryNodeType_bool,
         RegistryNodeType_int,
         RegistryNodeType_int64,
         RegistryNodeType_double,
         RegistryNodeType_list
      } RegistryNodeType;

      /**
       * Data structure which is stored at each node in the tree.
       */
      typedef struct RegistryNode
      {
         RegistryNodeType       type;  // how to interpret the bytes
         std::vector< uint8_t > bytes; // Raw information in byte form
      } RegistryNode;

      /**
       * This class contains helper methods to deal with the "registry". Note:
       * this is not the Windows registry, but a (portable) directory-like
       * internal structure similar to the windows registry.
       *
       * All methods return a boolean indicating whether the operation was
       * successful. And in the case of the "getXYZ" methods, an out param
       * for storage of the result.
       */
      class RegistryUtils
      {
      public:
         // Methods dealing with registry read/write for various types
         static bool getString( msrp_tree *registry, const char *path, std::string& outValue );
         static bool setString( msrp_tree *registry, const char *path, const std::string& value );
         static bool getBool(   msrp_tree *registry, const char *path, bool& outValue );
         static bool setBool(   msrp_tree *registry, const char *path, const bool& value );
         static bool getInt(    msrp_tree *registry, const char *path, int& outValue );
         static bool setInt(    msrp_tree *registry, const char *path, const int& value );
         static bool getInt64(  msrp_tree *registry, const char *path, int64_t& outValue );
         static bool setInt64(  msrp_tree *registry, const char *path, const int64_t& value );
         static bool getDouble( msrp_tree *registry, const char *path, double& outValue );
         static bool setDouble( msrp_tree *registry, const char *path, const double& value );

         // Returns true if (and only if) there is data stored at this position
         // NB: in order for this to return true, exists should also return true
         static bool hasData( msrp_tree *registry, const char *path );

         // Returns true if (and only if) the path exists in the registry
         static bool exists( msrp_tree *registry, const char *path );

         // Removes all data below (and including) the specified path
         static bool erase( msrp_tree *registry, const char *path );

         // Helper utilities for lists in the registry.
         //
         // The registry collapses lists into paths which are of the form:
         // [parent_path]/[item_name]_list/[item_index]/[item_name]

         // Checks whether or not the list exists (based on the parent path and item name)
         static bool listExists( msrp_tree *registry, const char *parentPath, const char *itemName, std::string& outListRoot );

         // Takes the parent path, and the item name, returns the listRoot path
         static bool createList( msrp_tree *registry, const char *parentPath, const char *itemName, std::string& outListRoot );

         // Returns the current size of the list
         static size_t getListSize( msrp_tree *registry, const char *listRoot );

         // Appends an item to the list (incrementing its size). Creates the item and returns
         // the full item path to the caller
         static bool addListItem( msrp_tree *registry, const char *listRoot, std::string& outItemPath );

         // Returns the full item path to the caller (or NULL if the index doesn't exist)
         static bool getListItem( msrp_tree *registry, const char *listRoot, const unsigned int& index, std::string& outItemPath );

         // Methods for "attributes" (only string type for the time being)
         static bool getAttribute( msrp_tree* registry, const char *path, const char *attrName, std::string &outAttrValue );
         static bool setAttribute( msrp_tree *registry, const char *path, const char *attrName, const std::string& attrValue );
      };
   }
}
#endif // __CPCAPI2_REGISTRYUTILS_H__
