#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_ANSWERMODE_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_ANSWERMODE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  enum class AnswerMode
  {
    Disabled,
    Manual,
    Auto
  };

  inline std::string to_string(AnswerMode value)
  {
    switch (value)
    {
      case AnswerMode::Disabled:
        return "Disabled";
      case AnswerMode::Manual:
        return "Manual";
      case AnswerMode::Auto:
        return "Auto";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, AnswerMode value)
  {
    switch (value)
    {
      case AnswerMode::Disabled:
        os << "Disabled";
        break;
      case AnswerMode::Manual:
        os << "Manual";
        break;
      case AnswerMode::Auto:
        os << "Auto";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const AnswerMode& e)
  {
    switch (e)
    {
      case AnswerMode::Disabled:
        j = "Disabled";
        break;
      case AnswerMode::Manual:
        j = "Manual";
        break;
      case AnswerMode::Auto:
        j = "Auto";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, AnswerMode& e)
  {
      if (0 == j.get<std::string>().compare("Disabled"))
    {
      e = AnswerMode::Disabled;
    }
    else if (0 == j.get<std::string>().compare("Manual"))
    {
      e = AnswerMode::Manual;
    }
    else if (0 == j.get<std::string>().compare("Auto"))
    {
      e = AnswerMode::Auto;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum AnswerMode"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_ANSWERMODE_H
