#pragma once

#if !defined(CPCAPI2_SIP_ACCOUNT_STATE_IMPL_H)
#define CPCAPI2_SIP_ACCOUNT_STATE_IMPL_H

#include "cpcapi2defs.h"
#include "SipAccountHandlerInternal.h"
#include "account/SipAccountState.h"
#include "../phone/PhoneModule.h"

#include <map>

namespace CPCAPI2
{
namespace SipAccount
{
class SipAccountInterface;
class SipAccountStateImpl : public SipAccountStateManager,
                            public SipAccountHandlerInternal,
                            public PhoneModule
{
public:
   SipAccountStateImpl(SipAccountInterface* acctIf);
   virtual ~SipAccountStateImpl();

   // SipAccountStateManager
   virtual int getStateAllAccounts(cpc::vector<CPCAPI2::SipAccount::SipAccountState>& accountState) OVERRIDE;

   // PhoneModule
   virtual void Release() OVERRIDE;

   // Inherited via SipAccountHandlerInternal
   virtual int onAccountConfigured(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountConfiguredEvent& args) OVERRIDE;
   virtual int onAccountEnabled(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountEnabledEvent& args) OVERRIDE;

   // Inherited via SipAccountHandler
   virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args) OVERRIDE;
   virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args) OVERRIDE;

private:
   SipAccountInterface* mAcctIf;
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, SipAccountState> AccountStateMap;
   AccountStateMap mStateMap;
};
}
}


#endif // CPCAPI2_SIP_ACCOUNT_STATE_IMPL_H
