#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOPLAYSOUNDCOMPLETEEVENT_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOPLAYSOUNDCOMPLETEEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
  class AudioPlaySoundCompleteEvent
  {
  public:
    std::optional<int32_t> soundClip;

    AudioPlaySoundCompleteEvent() = default;

    AudioPlaySoundCompleteEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const AudioPlaySoundCompleteEvent& o)
    {
      j = nlohmann::json::object();
       if (o.soundClip) { j["soundClip"] = *(o.soundClip); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, AudioPlaySoundCompleteEvent& o)
    {
     if (j.contains("soundClip")) { o.soundClip = j.at("soundClip"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOPLAYSOUNDCOMPLETEEVENT_H
