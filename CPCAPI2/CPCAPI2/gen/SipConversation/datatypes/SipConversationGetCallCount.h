#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONGETCALLCOUNT_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONGETCALLCOUNT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationGetCallCount
  {
  public:
    int32_t phoneHandle;

    SipConversationGetCallCount() = default;

    SipConversationGetCallCount(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationGetCallCount& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationGetCallCount& o)
    {
     o.phoneHandle = j.at("phoneHandle");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONGETCALLCOUNT_H
