#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACCOUNT_MODULE == 1)
#include <cpcapi2utils.h>
#include "../util/cpc_logger.h"
#include "../util/DeviceInfo.h"
#include "SipAccountInterface.h"
#include "../phone/PhoneInterface.h"
#include "account/SipAccountHandler.h"
#include "phone/Phone.h"
#include "json/JsonHelper.h"
#include "media/MediaManager.h"
#include "SipAccountImpl.h"
#include "CPDialogDnsResultManager.h"
#include "phone/NetworkChangeManagerInterface.h"
#include "../call/SipAVConversationManagerInterface.h"

#include <resip/stack/SipStack.hxx>
#include <rutil/Socket.hxx>
#include <rutil/Log.hxx>
#include <rutil/DnsUtil.hxx>
#include <rutil/ThreadIf.hxx>
#include <rutil/IpSynth.hxx>

#include <functional>
#include <algorithm>
#include <sstream>

using namespace resip;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_ACCOUNT

namespace CPCAPI2
{
namespace SipAccount
{

SipAccountInterface::SipAccountInterface(Phone* cpcPhone) :
   EventSource2< EventHandler<SipAccount::SipAccountHandler, SipAccount::SipAccountHandle> >(dynamic_cast<PhoneInterface*>(cpcPhone),
      /* enable logging */ true, RESIPROCATE_SUBSYSTEM),
   mNextSipAccountHandle(0x100),
   mShutdown(false),
   mPhone(dynamic_cast<PhoneInterface*>(cpcPhone)),
   mNetworkChangeManagerIf(dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(cpcPhone))),
   mReleased(0),
   mProcessLogTicks(0)
{
   InfoLog(<< "SipAccountInterface(): " << this << " Constructor");
   mPhone->addRefImpl();
   CPCAPI2::Media::MediaManager::getInterface(cpcPhone);
   mNetworkChangeManagerIf->addSdkObserver(this);
   logNetworkInterfaces();

   ReadCallbackBase* initCmd = resip::resip_bind(&SipAccountInterface::initImpl, this);
   postToSdkThread(initCmd);
}

SipAccountInterface::~SipAccountInterface()
{
   InfoLog(<< "~SipAccountInterface(): " << this << " Destructor");

   if (PhoneInterface* pi = dynamic_cast<PhoneInterface*>(mPhone))
   {
      if (pi->getSdkModuleThread().isCurrentThread() == false)
      {
         ErrLog(<< "SipAccountInterface dtor on unexpected thread");
         abort();
      }
   }

   mShutdown = true;
   postToSdkThread(new resip::ReadCallbackNoOp());

   mAccountMap.clear();
   mPhone->releaseImpl();

#ifdef CPCAPI2_AUTO_TEST
   mProbeEventMap.clear();
   mProbeHandlerMap.clear();
#endif
}

void SipAccountInterface::initImpl()
{
   SipConversation::SipAVConversationManagerInterface* conversationInterface = dynamic_cast<SipConversation::SipAVConversationManagerInterface*>(SipConversation::SipConversationManager::getInterface(mPhone));
   if (conversationInterface)
   {
      conversationInterface->addSdkObserver(this);
   }
}

void SipAccountInterface::PreRelease()
{
   InfoLog(<< "SipAccountInterface::PreRelease()");
   mNetworkChangeManagerIf->removeSdkObserver(this);

   SipConversation::SipAVConversationManagerInterface* conversationInterface = dynamic_cast<SipConversation::SipAVConversationManagerInterface*>(SipConversation::SipConversationManager::getInterface(mPhone));
   if (conversationInterface)
   {
      conversationInterface->removeSdkObserver(this);
   }

   mReleased = 0;
   AccountMap::iterator it = mAccountMap.begin();
   for (; it != mAccountMap.end(); ++it)
   {
      SipAccountImpl* acct = it->second;

      if (!acct->isEnabled() && !acct->isDisabling()) // && !acct->isShutdown())
      {
         WarningLog(<< "Account with handle " << it->first << " is not enabled; proceed to destroy");
         acct->destroy(true);
      }
      else if( acct->isShutdown() )
      {
         WarningLog(<< "Account with handle " << it->first << " is shutdown; proceed to destroy");
         acct->destroy(true);
      }
      else if (!acct->isShutdown())
      {
         WarningLog(<< "Account with handle " << it->first << " is not disabled; trying to disable now (may delay shutdown)");
         acct->destroy(false); // get SipAccountImpl to mark itself for destruction once the disable is complete
         acct->disable();
      }
   }
}

bool SipAccountInterface::PreReleaseCompleted()
{
   // If there are no more accounts, we're done. Accounts are removed from mAccountMap
   // inside of the method SipAccountInterface::accountDestroyed.
   return( mAccountMap.size() == 0 );
}

void SipAccountInterface::Release()
{
   InfoLog(<< "SipAccountInterface::Release()");

   AccountMap tempMap = mAccountMap;
   for (AccountMap::iterator it = tempMap.begin(); it != tempMap.end(); ++it)
   {
      SipAccountImpl* acct = it->second;

      // Forcefully destroy the SipAccountImpl, as there was no luck with a graceful exit
      InfoLog(<< "SipAccountInterface::Release(): " << this << " initiating a force-clean of the sip account: " << acct);
      acct->destroyImpl(true); // modifies mAccountMap
   }

   delete this;
}

void SipAccountInterface::tryCleanupAccount(CPCAPI2::SipAccount::SipAccountHandle h)
{
   SipAccountImpl* acct = getAccountImpl(h);
   if (acct && !acct->isShutdown())
   {
      WarningLog(<< "Account with handle " << h << " is not disabled; trying to disable now (may delay shutdown)");
      acct->disable();
   }
}

void SipAccountInterface::accountDestroyed(CPCAPI2::SipAccount::SipAccountHandle h)
{
   if (PhoneInterface* pi = dynamic_cast<PhoneInterface*>(mPhone))
   {
      if (pi->getSdkModuleThread().isCurrentThread() == false)
      {
         ErrLog(<< "SipAccountInterface::accountDestroyed: called on unexpected thread: " << ThreadIf::selfId());
         return;
      }
   }

   SipAccountImpl* account = getAccountImpl(h);
   if (account)
   {
      InfoLog(<< "SipAccountInterface::accountDestroyed: Removing from account list: " << h << " account list size: " << mAccountMap.size());
      mAccountMap.erase(h);

      SipAccountDestroyedEvent args;
      fireEvent(cpcEvent(SipAccountHandlerInternal, onAccountDestroyed), h, args);
   }
}

SipAccountImpl* SipAccountInterface::getAccountImpl(CPCAPI2::SipAccount::SipAccountHandle account)
{
   AccountMap::iterator it = mAccountMap.find(account);
   if (it != mAccountMap.end())
   {
      DebugLog(<< "SipAccountInterface::getAccountImpl (this=" << this << ", handle=" << account << "): " << it->second);
      return it->second;
   }

   WarningLog(<< "SipAccountInterface::getAccountImpl (this=" << this << ") invalid handle: " << account);
   return NULL;
}

SipAccountHandle SipAccountInterface::create(CPCAPI2::SipAccount::SipAccountHandle handle)
{
   ReadCallbackBase* cmd = resip::resip_bind(&SipAccountInterface::createImpl, this, handle);
   postToSdkThread(cmd);
   return handle;
}

SipAccountHandle SipAccountInterface::create()
{
   CPCAPI2::SipAccount::SipAccountHandle h = mNextSipAccountHandle += 0x100;
   return create(h);
}

SipAccountHandle SipAccountInterface::create(const SipAccountSettings& accountSettings, CPCAPI2::SipAccount::SipAccountHandle handle)
{
   ReadCallbackBase* cmd = resip::resip_bind(&SipAccountInterface::createImplWithSettings, this, handle, accountSettings);
   postToSdkThread(cmd);
   return handle;
}

SipAccountHandle SipAccountInterface::create(const SipAccountSettings& accountSettings)
{
   CPCAPI2::SipAccount::SipAccountHandle h = mNextSipAccountHandle += 0x100;
   return create(accountSettings, h);
}

void SipAccountInterface::createImpl(CPCAPI2::SipAccount::SipAccountHandle h)
{
   // prevent creating new accounts if phone initialization has not been done
   if (!mPhone->isInitialized())
   {
      fireError("Phone was not initialized. Account " + cpc::to_string(h) + " was not created.");
      return;
   }

   // prevent creating new accounts once licensing has failed
   if (!isLicenseOK())
   {
      fireError("License not valid. Account " + cpc::to_string(h) + " was not created.");
      return;
   }

   InfoLog(<< "SipAccountInterface::createImpl " << h);
   cpc::string instanceId;
   DeviceInfo::getInstanceId(instanceId);
   SipAccountImpl* acct = new SipAccountImpl(h, instanceId.c_str(), this, mPhone);

   acct->addRestriction(CPCAPI2::SipAccount::UserDisabledRestriction);

   NetworkTransport transport = mNetworkChangeManagerIf->networkTransport();
   if (acct->isRestrictedNetwork(transport) || transport == TransportNone)
   {
      // Restricted or no network available
      acct->addRestriction(CPCAPI2::SipAccount::NetworkRestriction);
   }

   mAccountMap[h] = acct;
}

void SipAccountInterface::createImplWithSettings(CPCAPI2::SipAccount::SipAccountHandle h, const SipAccountSettings& sipAccountSettings)
{
   // prevent creating new accounts if phone initialization has not been done
   if (!mPhone->isInitialized())
   {
      fireError("Phone was not initialized. Account " + cpc::to_string(h) + " was not created.");
      return;
   }

   // prevent creating new accounts once licensing has failed
   if (!isLicenseOK())
   {
      fireError("License not valid. SIP account " + cpc::to_string(h) + " was not created.");
      return;
   }

   InfoLog(<< "SipAccountInterface::createImplWithSettings " << h);
   cpc::string instanceId;
   DeviceInfo::getInstanceId(instanceId);
   SipAccountImpl* acct = new SipAccountImpl(h, instanceId.c_str(), this, mPhone);

   acct->pendingSettings()[TransportNone] = sipAccountSettings;
   acct->applySettings();
   acct->addRestriction(CPCAPI2::SipAccount::UserDisabledRestriction);

   NetworkTransport transport = mNetworkChangeManagerIf->networkTransport();
   if (acct->isRestrictedNetwork(transport) || transport == TransportNone)
   {
      if (acct->isRestrictedNetwork(transport))
      {
         DebugLog(<< "Adding network restriction because on restricted network");
      }
      else
      {
         std::stringstream ss;
         NetworkChangeManagerImpl::NetworkChangeManagerInterfaceSet localIfs = NetworkChangeManagerImpl::getLocalIPAddresses();
         for (NetworkChangeManagerImpl::NetworkChangeManagerInterfaceSet::const_iterator it = localIfs.begin(); it != localIfs.end(); ++it)
         {
            ss << *it << " ";
         }

         DebugLog(<< "Adding network restriction because network change manager reports no transport available. Interfaces: " << ss.str());
      }

      // Restricted or no network available
      acct->addRestriction(CPCAPI2::SipAccount::NetworkRestriction);
   }

   mAccountMap[h] = acct;
}

int SipAccountInterface::destroy(CPCAPI2::SipAccount::SipAccountHandle h)
{
   DebugLog(<< __FUNCTION__ << " " << h);

   postToSdkThread(resip::resip_bind(&SipAccountInterface::tryCleanupAccount, this, h));
   postToSdkThread(resip::resip_bind(&SipAccountInterface::destroyImpl, this, h));
   return kSuccess;
}

void SipAccountInterface::destroyImpl(CPCAPI2::SipAccount::SipAccountHandle h)
{
   SipAccountImpl* account = getAccountImpl(h);
   if (account)
   {
      InfoLog(<< "Destroying account " << h);
      account->destroyImpl();
   }
}

void SipAccountInterface::fireError(const cpc::string& errorText)
{
   ErrLog(<< "Firing account error: " << errorText);

   PhoneErrorEvent evt;
   evt.errorText = errorText;
   mPhone->fireEvent(cpcEvent(PhoneHandler, onError), cpc::string("SipAccountInterface"), evt);
   mPhone->fireEvent(cpcEvent(PhoneErrorHandler, onError), cpc::string("SipAccountInterface"), evt);
}

void SipAccountInterface::firePhoneError(const cpc::string& errorText, const cpc::string& module)
{
   ErrLog(<< "Firing account error: " << errorText);

   PhoneErrorEvent evt;
   evt.errorText = errorText;
   mPhone->fireEvent(cpcEvent(PhoneHandler, onError), cpc::string(module), evt);
   mPhone->fireEvent(cpcEvent(PhoneErrorHandler, onError), cpc::string(module), evt);
}

int SipAccountInterface::enable(CPCAPI2::SipAccount::SipAccountHandle account)
{
   InfoLog(<< "SipAccountInterface::enable " << account);
   postToSdkThread(resip::resip_bind(&SipAccountInterface::enableImpl, this, account));
   return kSuccess;
}

void SipAccountInterface::enableImpl(CPCAPI2::SipAccount::SipAccountHandle account)
{
   if (!mPhone->isInitialized())
   {
      fireError("Phone was not initialized. Account " + cpc::to_string(account) + " could not be enabled.");
      return;
   }

   if (!isLicenseOK())
   {
      fireError("License not valid. SIP account " + cpc::to_string(account) + " could not be enabled.");
      return;
   }

   InfoLog(<< "SipAccountInterface::enableImpl");
   SipAccountImpl* acct = getAccountImpl(account);
   if (acct)
   {
      acct->removeRestriction(CPCAPI2::SipAccount::UserDisabledRestriction);
   }
   else
   {
      fireError("Invalid account handle for enable");
      return;
   }

   // fire the new account enabled event here
   SipAccountEnabledEvent args;
   args.curTransport = mNetworkChangeManagerIf->networkTransport();
   args.settings = acct->getSettings();
   fireEvent(cpcEvent(SipAccountHandlerInternal, onAccountEnabled), account, args);
}

int SipAccountInterface::disable(CPCAPI2::SipAccount::SipAccountHandle account, bool force)
{
   InfoLog(<< "SipAccountInterface::disable " << account << ", force: " << force);
   postToSdkThread(resip::resip_bind(&SipAccountInterface::disableImpl, this, account, force));
   return kSuccess;
}

void SipAccountInterface::disableImpl(CPCAPI2::SipAccount::SipAccountHandle account, bool force)
{
   InfoLog(<< "SipAccountInterface::disableImpl " << account << ", force: " << force);
   SipAccountImpl* acct = getAccountImpl(account);
   if (acct)
   {
      // Account has been disabled explicitly
      acct->addRestriction(CPCAPI2::SipAccount::UserDisabledRestriction, force);
   }
   else
   {
      fireError("Invalid account handle for disable");
      return;
   }

   SipAccountDisabledEvent args;
   fireEvent(cpcEvent(SipAccountHandlerInternal, onAccountDisabled), account, args);
}

int SipAccountInterface::requestRegistrationRefresh(CPCAPI2::SipAccount::SipAccountHandle account, unsigned int deadline)
{
   postToSdkThread(resip::resip_bind(&SipAccountInterface::requestRegistrationRefreshImpl, this, account, deadline));
   return kSuccess;
}

void SipAccountInterface::requestRegistrationRefreshImpl(CPCAPI2::SipAccount::SipAccountHandle account, unsigned int deadline)
{
   SipAccountImpl* acct = getAccountImpl(account);
   if (acct)
   {
      acct->requestRegistrationRefresh(deadline);
   }
   else
   {
      fireError("Invalid account handle for requestRegistrationRefresh");
      return;
   }
}

int SipAccountInterface::setHandler(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipAccountHandler* handler)
{
   ReadCallbackBase* f = resip::resip_bind(&SipAccountInterface::setHandlerImpl, this, account, handler);

   if (handler == NULL)
   {
      // removing the handler involves two steps:
      // 1. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);

      // 2. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      executeOnSdkThread(f);
   }
   else
   {
      postToSdkThread(f);
   }

   return kSuccess;
}

void SipAccountInterface::setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipAccountHandler* handler)
{
   auto it = mHandlers.find(account);
   if (mHandlers.end() != it)
   {
      removeAppHandler(it->second, account);
   }
   mHandlers[account] = handler;

   if (nullptr != handler)
   {
      addAppHandler(handler, account);
   }
}

int SipAccountInterface::setNetworkRestriction(CPCAPI2::SipAccount::SipAccountHandle account, NetworkTransport transport, bool restricted)
{
   if (transport == TransportNone)
      return kError;

   postToSdkThread(resip::resip_bind(&SipAccountInterface::setNetworkRestrictionImpl, this, account, transport, restricted));
   return kSuccess;
}

void SipAccountInterface::setNetworkRestrictionImpl(CPCAPI2::SipAccount::SipAccountHandle account, NetworkTransport transport, bool restricted)
{
   DebugLog(<< "SipAccountInterface::setNetworkRestrictionImpl(): handle: " << account << ", transport: " << transport << ", restricted: " << restricted);
   SipAccountImpl* acct = getAccountImpl(account);

   if (acct == NULL)
   {
      fireError("Invalid account handle for setNetworkRestriction");
      return;
   }

   if (restricted != acct->isRestrictedNetwork(transport))
   {
      std::set<NetworkTransport> &networks = acct->restrictedNetworks();

      if (restricted)
      {
         networks.insert(transport);
      }
      else
      {
         networks.erase(transport);
      }

      NetworkTransport currentTransport = mNetworkChangeManagerIf->networkTransport();

      if (currentTransport == transport)
      {
         DebugLog(<< "SipAccountInterface::setNetworkRestrictionImpl(): Triggering network change for handle: " << account << ", transport: " << transport << ", restricted: " << restricted << ", current transport: " << currentTransport);
         NetworkChangeEvent params;
         params.networkTransport = transport;
         onNetworkChangeImpl(params);
      }
   }
}

void SipAccountInterface::logNetworkInterfaces() const
{
   std::ostringstream ss;

   NetworkChangeManagerImpl::NetworkChangeManagerInterfaceSet interfaces = NetworkChangeManagerImpl::getLocalIPAddresses();

   ss << "Current network interfaces: ";
   for (NetworkChangeManagerImpl::NetworkChangeManagerInterfaceSet::const_iterator it = interfaces.begin(); it != interfaces.end(); ++it)
   {
      ss << *it << "  ";
   }
   DebugLog( << ss.str() );
}

#ifdef CPCAPI2_AUTO_TEST

#include "../experimental/account/SipNetworkProbeHandler.h"

int SipAccountInterface::closeTransportConnections(CPCAPI2::SipAccount::SipAccountHandle handle)
{
   postToSdkThread(resip::resip_bind(&SipAccountInterface::closeTransportConnectionsImpl, this, handle));
   return kSuccess;
}

int SipAccountInterface::closeTransportConnectionsImpl(CPCAPI2::SipAccount::SipAccountHandle handle)
{
   if (SipAccountImpl* acct = getAccountImpl(handle))
   {
      acct->resetTransports();
   }

   return kSuccess;
}

int SipAccountInterface::setFakeResponse(CPCAPI2::SipAccount::SipAccountHandle handle, bool enable, cpc::string method, int responseCode, std::string responseReason, int warningCode)
{
   postToSdkThread(resip::resip_bind(&SipAccountInterface::setFakeResponseImpl, this, handle, enable, method, responseCode, responseReason, warningCode));
   return kSuccess;
}

int SipAccountInterface::setFakeResponseImpl(CPCAPI2::SipAccount::SipAccountHandle handle, bool enable, cpc::string method, int responseCode, std::string responseReason, int warningCode)
{
   if (SipAccountImpl* acct = getAccountImpl(handle))
   {
      acct->setFakeResponse(enable, method, responseCode, responseReason.c_str(), warningCode);
   }

   return kSuccess;
}

int SipAccountInterface::onNetworkProbeStatusChanged(SipAccountHandle account, CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent& args)
{
   ProbeHandlerMap::iterator it = mProbeHandlerMap.find(account);
   if (it == mProbeHandlerMap.end())
   {
      DebugLog(<< "SipAccountInterface::onNetworkProbeStatusChanged(): No probe handler found for account handle: " << account);
      return kSuccess;
   }

   if (!it->second)
   {
      DebugLog(<< "SipAccountInterface::onNetworkProbeStatusChanged(): Invalid probe handler for account handle: " << account);
      return kSuccess;
   }

   StackLog(<< "SipAccountInterface::onNetworkProbeStatusChanged(): Probe Status: " << args << " for account handle: " << account);
   mProbeEventMap[args.probeMessageId] = &args;

   it->second->onNetworkProbeStatusChanged(account, args);

   mProbeEventMap.erase(args.probeMessageId);
   assert(mProbeEventMap.empty());
   return kSuccess;
}

int SipAccountInterface::setProbeHandler(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipNetworkProbeHandler* handler)
{
   ReadCallbackBase* f = resip::resip_bind(&SipAccountInterface::setProbeHandlerImpl, this, account, handler);

   if (handler == NULL)
   {
      // removing the handler involves two steps:

      // 1. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      mPhone->getSdkModuleThread().execute(f);

#ifndef CPCAPI2_AUTO_TEST
      // 2. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);
#endif
   }
   else
   {
      postToSdkThread(f);
   }

   return kSuccess;
}

int SipAccountInterface::setProbeHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipNetworkProbeHandler* handler)
{
   if (handler == NULL)
   {
      ProbeHandlerMap::iterator it = mProbeHandlerMap.find(account);

      if (it != mProbeHandlerMap.end())
      {
         mProbeHandlerMap.erase(it);
      }
   }
   else
   {
      mProbeHandlerMap.insert(std::make_pair(account, handler));
   }

   if (SipAccountImpl* acct = getAccountImpl(account))
   {
      if (handler != NULL)
      {
         acct->setProbeHandler(this);
      }
      else
      {
         acct->setProbeHandler(NULL);
      }
   }

   return kSuccess;
}

int SipAccountInterface::setProbeMockDelay(CPCAPI2::SipAccount::SipAccountHandle account, IpVersion ipVersion, unsigned int delayMsecs)
{
   postToSdkThread(resip::resip_bind(&SipAccountInterface::setProbeMockDelayImpl, this, account, ipVersion, delayMsecs));
   return kSuccess;
}

int SipAccountInterface::setProbeMockDelayImpl(CPCAPI2::SipAccount::SipAccountHandle account, IpVersion ipVersion, unsigned int delayMsecs)
{
   if (SipAccountImpl* acct = getAccountImpl(account))
   {
      acct->setProbeMockDelay(ipVersion, delayMsecs);
   }

   return kSuccess;
}

int SipAccountInterface::setDnsHandler(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::DialogDnsResultHandler* handler)
{
   postToSdkThread(resip::resip_bind(&SipAccountInterface::setDnsHandlerImpl, this, account, handler));
   return kSuccess;
}

int SipAccountInterface::setDnsHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::DialogDnsResultHandler* handler)
{
   if (SipAccountImpl* acct = getAccountImpl(account))
   {
      acct->setDnsHandler(handler);
   }

   return kSuccess;
}

int SipAccountInterface::setDomainLockString(const cpc::string& brandingString)
{
   postToSdkThread(resip::resip_bind(&SipAccountInterface::setDomainLockStringImpl, this, brandingString));
   return kSuccess;
}

int SipAccountInterface::setDomainLockStringImpl(const cpc::string& brandingString) {
   mOverrideDomainLockString = brandingString;
   return kSuccess;
};

int SipAccountInterface::setIgnoreNetworkChangeStarcodeFilter(CPCAPI2::SipAccount::SipAccountHandle account, bool enabled)
{
   postToSdkThread(resip::resip_bind(&SipAccountInterface::setIgnoreNetworkChangeStarcodeFilterImpl, this, account, enabled));
   return kSuccess;
}

int SipAccountInterface::setIgnoreNetworkChangeStarcodeFilterImpl(CPCAPI2::SipAccount::SipAccountHandle account, bool enabled)
{
   if (SipAccountImpl* acct = getAccountImpl(account))
   {
      acct->setIgnoreNetworkChangeStarcodeFilter(enabled);
   }

   return kSuccess;
}

int SipAccountInterface::setDecoratorHandler(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipAccountMessageDecoratorHandler* handler)
{
   postToSdkThread(resip::resip_bind(&SipAccountInterface::setDecoratorHandlerImpl, this, account, handler));
   return kSuccess;
}

int SipAccountInterface::setDecoratorHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipAccountMessageDecoratorHandler* handler)
{
   if (SipAccountImpl* acct = getAccountImpl(account))
   {
      acct->setDecoratorHandler(handler);
   }

   return kSuccess;
}

#endif

void SipAccountInterface::postToProcessThread(void (*pfun)(void*), void* obj)
{
   std::function<void()> bfunc = std::bind(pfun, obj);
   ReadCallback* brcb = new ReadCallback(bfunc);
   postToSdkThread(brcb);
}

int SipAccountInterface::sendOptionsMessage(SipAccount::SipAccountHandle accountHandle, const cpc::string target)
{
   ReadCallbackBase* cmd = resip::resip_bind(&SipAccountInterface::sendOptionsMessageImpl, this, accountHandle, target);
   mPhone->getSdkModuleThread().post(cmd);

   return kSuccess;
}

void SipAccountInterface::sendOptionsMessageImpl(SipAccount::SipAccountHandle accountHandle, const cpc::string target)
{
   SipAccountImpl* acct = getAccountImpl(accountHandle);
   if (acct)
   {
      acct->sendOptionsMessage(target);
   }
}

int SipAccountInterface::setSkipResetTransportOnNetworkChange(SipAccount::SipAccountHandle account, bool skip)
{
   ReadCallbackBase* cmd = resip::resip_bind(&SipAccountInterface::setSkipResetTransportOnNetworkChangeImpl, this, account, skip);
   mPhone->getSdkModuleThread().post(cmd);

   return kSuccess;
}

void SipAccountInterface::setSkipResetTransportOnNetworkChangeImpl(SipAccount::SipAccountHandle account, bool skip)
{
   SipAccountImpl* acct = getAccountImpl(account);
   if (acct)
   {
      acct->setSkipResetTransportOnNetworkChange(skip);
   }
}

int SipAccountInterface::onNetworkChange(const NetworkChangeEvent& params)
{
   ReadCallbackBase* handleNetworkChangeCmd = resip::resip_bind(&SipAccountInterface::onNetworkChangeImpl, this, params);
   mPhone->getSdkModuleThread().post(handleNetworkChangeCmd);
   return 0;
}

int SipAccountInterface::onNetworkChangeImpl(const NetworkChangeEvent& params)
{
   IpSynthTools::reset(); // bliu: IpSynthTools to be reset upon network changed

   logNetworkInterfaces();

   for (AccountMap::iterator it = mAccountMap.begin(); it != mAccountMap.end(); ++it)
   {
      handleNetworkChangeEvent(it->first, params);
   }
   return kSuccess;
}

void SipAccountInterface::handleNetworkChangeEvent(SipAccountHandle account, const NetworkChangeEvent& params)
{
   logNetworkInterfaces();

   NetworkTransport transport = params.networkTransport;
   AccountMap::iterator i = mAccountMap.find(account);
   if (i != mAccountMap.end())
   {
      SipAccountImpl* accountImpl = i->second;
      
      if (accountImpl->shouldIgnoreNetworkChange(mLastNetworkChangeEvent, params))
      {
         if (accountImpl->isIgnoreNetworkChangeStarcodeFilterEnabled())
            mIgnoredNetworkChanges[account] = params;
      }
      else
      {
         if (transport == TransportNone)
         {
            InfoLog(<< "handleNetworkChangeEvent - no network");
            accountImpl->addRestriction(CPCAPI2::SipAccount::NetworkRestriction);
         }
         else if (accountImpl->isRestrictedNetwork(transport))
         {
            InfoLog(<< "handleNetworkChangeEvent - restricted network, transport=" << transport);
            accountImpl->addRestriction(CPCAPI2::SipAccount::NetworkRestriction);
            if (accountImpl->isEnabled())
            {
               // Must be in call (or similar) and disable is deferred
               DebugLog(<< "handleNetworkChangeEvent - refreshing on restricted network");
               accountImpl->refreshRegOnNetworkChange(params);
            }
         }
         else
         {
            InfoLog(<< "handleNetworkChangeEvent - unrestricted network");
            if (accountImpl->isEnabled())
            {
               DebugLog(<< "handleNetworkChangeEvent - refreshing on unrestricted network, transport=" << transport);
               accountImpl->refreshRegOnNetworkChange(params);
            }
            accountImpl->removeRestriction(CPCAPI2::SipAccount::NetworkRestriction);
         }
      }
   }

   mLastNetworkChangeEvent = params;
}

int SipAccountInterface::onConversationEnded(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationEndedEvent& args)
{
   // Post-ing required to ensure that the call-count in the conversation manager is valid
   ReadCallbackBase* endedEvent = resip::resip_bind(&SipAccountInterface::onConversationEndedImpl, this, conversation, args);
   mPhone->getSdkModuleThread().post(endedEvent);
   return kSuccess;
}

void SipAccountInterface::onConversationEndedImpl(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationEndedEvent& args)
{
   std::map<SipAccountHandle, NetworkChangeEvent> ignoredNetworkChanges = mIgnoredNetworkChanges;
   for (std::map<SipAccountHandle, NetworkChangeEvent>::iterator i = ignoredNetworkChanges.begin(); i != ignoredNetworkChanges.end(); ++i)
   {
      mIgnoredNetworkChanges.erase(i->first);
      // OBELISK-6350: disable deferred network change handling until we have a fix that avoids TLS reconnection issues
      //InfoLog(<< "Initiating deferred network change");
      //handleNetworkChangeEvent(i->first, i->second);
   }
}

int SipAccountInterface::configureDefaultAccountSettings(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountSettings& sipAccountSettings)
{
   postToSdkThread(resip::resip_bind(&SipAccountInterface::configureDefaultAccountSettingsImpl, this, account, sipAccountSettings));
   return kSuccess;
}

void SipAccountInterface::configureDefaultAccountSettingsImpl(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountSettings& sipAccountSettings)
{
   SipAccountImpl* acct = getAccountImpl(account);
   if (acct)
   {
      acct->pendingSettings()[TransportNone] = sipAccountSettings;
   }
   else
   {
      fireError("Invalid account handle for configureDefaultAccountSettings");
      return;
   }
}

int SipAccountInterface::configureTransportAccountSettings(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountSettings& sipAccountSettings, CPCAPI2::NetworkTransport transport)
{
   postToSdkThread(resip::resip_bind(&SipAccountInterface::configureTransportAccountSettingsImpl, this, account, sipAccountSettings, transport));
   return kSuccess;
}

void SipAccountInterface::configureTransportAccountSettingsImpl(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountSettings& sipAccountSettings, CPCAPI2::NetworkTransport transport)
{
   SipAccountImpl* acct = getAccountImpl(account);
   if (acct)
   {
      acct->pendingSettings()[transport] = sipAccountSettings;
   }
   else
   {
      fireError("Invalid account handle for configureTransportAccountSettings");
      return;
   }
}

int SipAccountInterface::applySettings(CPCAPI2::SipAccount::SipAccountHandle account)
{
   DebugLog(<< __FUNCTION__ << " " << account);

   ReadCallbackBase* cmd = resip::resip_bind(&SipAccountInterface::applySettingsImpl, this, account);
   postToSdkThread(cmd);
   return kSuccess;
}

void SipAccountInterface::applySettingsImpl(CPCAPI2::SipAccount::SipAccountHandle account)
{
   SipAccountImpl* acct = getAccountImpl(account);
   if (acct)
   {
      acct->applySettings();
   }
   else
   {
      fireError("Invalid account handle for applySettings");
      return;
   }
}

int SipAccountInterface::onAccountAdornment(SipAccountHandle account, SipAccountAdornmentInternalEvent& args)
{
   AdornmentHandlerMap::iterator it = mAdornmentHandlerMap.find(account);
   if (it == mAdornmentHandlerMap.end()) return kSuccess;
   if (!it->second) return kSuccess;

   mAdornmentEventMap[args.adornmentMessageId] = &args;

   it->second->onAccountAdornment(account, args);

   mAdornmentEventMap.erase(args.adornmentMessageId);
   assert(mAdornmentEventMap.empty());

   return kSuccess;
}

int SipAccountInterface::setAdornmentHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipAccountAdornmentHandler* handler)
{
   ReadCallbackBase* f = resip::resip_bind(&SipAccountInterface::setAdornmentHandlerImpl, this, account, handler);

   if (handler == NULL)
   {
      // removing the handler involves two steps:

      // 1. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      mPhone->getSdkModuleThread().execute(f);

      // 2. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);
   }
   else
   {
      postToSdkThread(f);
   }

   return kSuccess;
}

int SipAccountInterface::setAdornmentHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipAccountAdornmentHandler* handler)
{
   if(handler == NULL)
   {
      AdornmentHandlerMap::iterator it = mAdornmentHandlerMap.find(account);

      if(it != mAdornmentHandlerMap.end())
      {
         mAdornmentHandlerMap.erase(it);
      }
   }
   else
   {
      mAdornmentHandlerMap.insert(std::make_pair(account, handler));
   }

   if (SipAccountImpl* acct = getAccountImpl(account))
   {
      if(handler != NULL)
      {
         acct->setAdornmentHandler(this);
      }
      else
      {
         acct->setAdornmentHandler(NULL);
      }
   }

   return kSuccess;
}

int SipAccountInterface::adornMessage(SipAccountHandle conversation, unsigned int adornmentMessageId, const cpc::vector<SipHeader>& customHeaders)
{
   AdornmentEventMap::iterator it = mAdornmentEventMap.find(adornmentMessageId);

   it->second->customHeaders = customHeaders;

   return kSuccess;
}

cpc::vector<SipAccountHandle> SipAccountInterface::getAccountHandles()
{
   cpc::vector<SipAccountHandle> accountHandles;

   for (std::map<SipAccountHandle, SipAccountImpl*>::const_iterator it = mAccountMap.begin(); it != mAccountMap.end(); it++)
   {
      accountHandles.push_back(it->first);
   }

   return accountHandles;
}

void SipAccountInterface::onLicensingError()
{
   // SDK licensing failed - accounts no longer usable
   AccountMap::iterator it = mAccountMap.begin();
   for (; it != mAccountMap.end(); ++it)
   {
      SipAccountImpl* acct = it->second;
      if (!acct->isShutdown())
      {
         acct->disable();
      }
   }
}

void SipAccountInterface::setT1TimerValueMs(unsigned long t1ValueMs)
{
   if (t1ValueMs > 0)
      resip::Timer::T1 = t1ValueMs;
}

void SipAccountInterface::setT2TimerValueMs(unsigned long t2ValueMs)
{
   if (t2ValueMs > 0)
      resip::Timer::T2 = t2ValueMs;
}

void SipAccountInterface::setT4TimerValueMs(unsigned long t4ValueMs)
{
   if (t4ValueMs > 0)
      resip::Timer::T4 = t4ValueMs;
}

void SipAccountInterface::setTDTimerValueMs(unsigned long tDValueMs)
{
   if (tDValueMs > 0)
      resip::Timer::TD = tDValueMs;
}

void SipAccountInterface::setTFTimerValueMs(unsigned long tFValueMs)
{
   if (tFValueMs > 0)
      resip::Timer::TF = tFValueMs;
}

void SipAccountInterface::setSipTotalTransactionTimeoutMs(unsigned long sipTotalTransactionTimeoutMs)
{
   if (sipTotalTransactionTimeoutMs > 0)
   {
      resip::Timer::TB = sipTotalTransactionTimeoutMs;
      resip::Timer::TH = sipTotalTransactionTimeoutMs;
   }
}

unsigned long SipAccountInterface::getT1TimerValueMs()
{
   return resip::Timer::T1;
}

unsigned long SipAccountInterface::getT2TimerValueMs()
{
   return resip::Timer::T2;
}

unsigned long SipAccountInterface::getT4TimerValueMs()
{
   return resip::Timer::T4;
}

unsigned long SipAccountInterface::getTDTimerValueMs()
{
   return resip::Timer::TD;
}

unsigned long SipAccountInterface::getTFTimerValueMs()
{
   return resip::Timer::TF;
}

int SipAccountInterface::setSipAccountUseAlias(CPCAPI2::SipAccount::SipAccountHandle hdl, bool useAlias)
{
   postToSdkThread(resip::resip_bind(&SipAccountInterface::setSipAccountUseAliasImpl, this, hdl, useAlias));
   return kSuccess;
}

void SipAccountInterface::setSipAccountUseAliasImpl(CPCAPI2::SipAccount::SipAccountHandle hdl, bool useAlias)
{
   SipAccountImpl *acct = getAccountImpl(hdl);
   if (acct)
   {
      acct->setUseAlias(useAlias);
   }
}

int SipAccountInterface::setCertStorageLoadType(SipAccount::SipAccountHandle h, SipAccountManagerInternal::CertLoadStorageType type)
{
   postToSdkThread(resip::resip_bind(&SipAccountInterface::setCertStorageLoadTypeImpl,this, h, type));
   return kSuccess;
}

void SipAccountInterface::setCertStorageLoadTypeImpl(SipAccount::SipAccountHandle h, SipAccountManagerInternal::CertLoadStorageType type)
{
   if (SipAccountImpl *acct = getAccountImpl(h))
   {
      acct->setCertStorageLoadType(type);
   }
}

int SipAccountInterface::setCertStorageFileSystemPath(SipAccount::SipAccountHandle h, const cpc::string& path)
{
   postToSdkThread(resip::resip_bind(&SipAccountInterface::setCertStorageFileSystemPathImpl, this, h, path));
   return kSuccess;
}

void SipAccountInterface::setCertStorageFileSystemPathImpl(SipAccount::SipAccountHandle h, const cpc::string& path)
{
   if (SipAccountImpl *acct = getAccountImpl(h))
   {
      acct->setCertStorageFileSystemPath(path.c_str());
   }
}

int SipAccountInterface::discardRegistration(CPCAPI2::SipAccount::SipAccountHandle hdl)
{
   postToSdkThread(resip::resip_bind(&SipAccountInterface::discardRegistrationImpl, this, hdl));
   return kSuccess;
}

void SipAccountInterface::discardRegistrationImpl(CPCAPI2::SipAccount::SipAccountHandle hdl)
{
   SipAccountImpl *acct = getAccountImpl(hdl);
   if (acct)
   {
      acct->discardRegistration();
   }
}

int SipAccountInterface::decodeProvisioningResponse(const cpc::string& provisioningResponse, cpc::vector<SipAccountSettings>& outSipAccountSettings)
{
   rapidjson::Document provisionedJSON;
   provisionedJSON.Parse<0>(provisioningResponse.c_str());

   if (provisionedJSON.HasParseError())
   {
      WarningLog(<< "Invalid provisioning format, parse error occured:" << provisionedJSON.GetParseError() << "Aborting decode.");
      return kError;
   }

   if (!provisionedJSON.HasMember("sipAccount"))
   {
      WarningLog(<< "Invalid provisioning format, sipAccount node missing. Aborting decode.");
      return kError;
   }

   const rapidjson::Value& account = provisionedJSON["sipAccount"];
   if (!account.IsArray())
   {
      WarningLog(<< "Invalid provisioning format, sipAccount node not an array. Aborting decode.");
      return kError;
   }

   for (rapidjson::Value::ConstValueIterator itr = account.Begin(); itr != account.End(); ++itr)
   {
      if (!itr->HasMember("sipAccountSettings"))
      {
         WarningLog(<< "Invalid provisioning format, sipAccountSettings node missing.");
         continue;
      }

      const rapidjson::Value& accountSettings = (*itr)["sipAccountSettings"];
      if (!accountSettings.IsObject())
      {
         WarningLog(<< "Invalid provisioning format, sipAccountSettings not an object.");
         continue;
      }

      SipAccountSettings settings;
      Json::Deserialize(accountSettings, settings);
      outSipAccountSettings.push_back(settings);
   }
   return kSuccess;
}

cpc::string get_debug_string(const SipAccount::SipAccountTransportType& transport)
{
   switch (transport)
   {
      case SipAccountTransport_Auto: return "Auto";
      case SipAccountTransport_UDP: return "UDP";
      case SipAccountTransport_TCP: return "TCP";
      case SipAccountTransport_TLS: return "TLS";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const SipAccount::IpVersion& ip)
{
   switch (ip)
   {
      case IpVersion_V4: return "V4";
      case IpVersion_V6: return "V6";
      case IpVersion_Auto: return "Auto_PreferV4";
      case IpVersion_Auto_PreferV6: return "Auto_PreferV6";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const SipAccount::SSLVersion& ssl)
{
   switch (ssl)
   {
      case TLS_DEFAULT: return "TLS_DEFAULT";
      case SSL_NONE: return "NONE";
      case SSL_V2: return "SSLv2";
      case SSL_V3: return "SSLv3";
      case TLS_V1_0: return "TLS 1.0";
      case TLS_V1_1: return "TLS 1.1";
      case TLS_V1_2: return "TLS 1.2";
      case TLS_V1_3: return "TLS 1.3";
      case SSL_HIGHEST: return "SSL_HIGHEST";
      case TLS_NON_DEPRECATED: return "TLS_NON_DEPRECATED";
   }
   return "invalid";
}

std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountTransportType& transport)
{
   os << get_debug_string(transport);
   return os;
}

std::ostream& operator<<(std::ostream& os, const SipAccount::IpVersion& ip)
{
   os << get_debug_string(ip);
   return os;
}

std::ostream& operator<<(std::ostream& os, const SipAccount::SSLVersion& ssl)
{
   os << get_debug_string(ssl);
   return os;
}

std::string get_debug_string(const SipAccountStatusChangedEvent::Status status)
{
   switch (status)
   {
      case SipAccountStatusChangedEvent::Status_Registered: return "Registered";
      case SipAccountStatusChangedEvent::Status_Failure: return "Failure";
      case SipAccountStatusChangedEvent::Status_Unregistered: return "Unregistered";
      case SipAccountStatusChangedEvent::Status_Registering: return "Registering";
      case SipAccountStatusChangedEvent::Status_Unregistering: return "Unregistering";
      case SipAccountStatusChangedEvent::Status_WaitingToRegister: return "WaitingToRegister";
      case SipAccountStatusChangedEvent::Status_Refreshing: return "Refreshing";
   }
   return "unknown";
}

std::string get_debug_string(const SipAccountStatusChangedEvent::Reason reason)
{
   switch (reason)
   {
      case SipAccountStatusChangedEvent::Reason_None: return "None";
      case SipAccountStatusChangedEvent::Reason_No_Network: return "No_Network";
      case SipAccountStatusChangedEvent::Reason_Restricted_Network: return "Restricted_Network";
      case SipAccountStatusChangedEvent::Reason_New_Network: return "New_Network";
      case SipAccountStatusChangedEvent::Reason_Server_Response: return "Server_Response";
      case SipAccountStatusChangedEvent::Reason_Local_Timeout: return "Local_Timeout";
      case SipAccountStatusChangedEvent::Reason_NetworkDeregistered: return "NetworkDeregistered";
      case SipAccountStatusChangedEvent::Reason_Tunnel_Failure: return "Tunnel_Failure";
      case SipAccountStatusChangedEvent::Reason_Dns_Lookup: return "Dns_Lookup";
      case SipAccountStatusChangedEvent::Reason_Transport_Protocol_Mismatch: return "Transport_Protocol_Mismatch";
      case SipAccountStatusChangedEvent::Reason_No_Route_To_Host: return "No_Route_To_Host";
      case SipAccountStatusChangedEvent::Reason_TLS_Cipher_Mismatch: return "TLS_Cipher_Mismatch";
      case SipAccountStatusChangedEvent::Reason_Domain_Locked: return "Domain_Locked";
   }
   return "unknown";
}


std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountStatusChangedEvent& evt)
{
   return os << "SipAccountStatusChangedEvent accountStatus: " << get_debug_string(evt.accountStatus) << ", reason: " 
             << get_debug_string(evt.reason) << ", signalingStatusCode: " << evt.signalingStatusCode;
}

std::ostream& operator<<(std::ostream& os, const SipAccount::ErrorEvent& evt)
{
   return os << "SipAccount::ErrorEvent";
}

std::ostream& operator<<(std::ostream& os, const SipAccount::LicensingErrorEvent& evt)
{
   return os << "SipAccount::LicensingErrorEvent";
}

std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountConfiguredEvent& evt)
{
   return os << "SipAccountConfiguredEvent";
}

std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountEnabledEvent& evt)
{
   return os << "SipAccountEnabledEvent";
}

std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountDisabledEvent& evt)
{
   return os << "SipAccountDisabledEvent";
}

std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountDestroyedEvent& evt)
{
   return os << "SipAccountDestroyedEvent";
}

std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountClientAuthEvent& evt)
{
   return os << "SipAccountClientAuthEvent";
}

std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountNetworkChangeEvent& evt)
{
   return os << "SipAccountNetworkChangeEvent";
}

std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountDnsResetEvent& evt)
{
   return os << "SipAccountDnsResetEvent";
}

std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountRegistrationRportUpdateEvent& evt)
{
   return os << "SipAccountRegistrationRportUpdateEvent";
}

}

}

#endif // CPCAPI2_BRAND_ACCOUNT_MODULE
