#include "brand_branded.h"

#include "interface/experimental/dialogevent/SipDialogEventPublicationManager.h"
#include "interface/experimental/dialogevent/SipDialogEventSubscriptionManager.h"

#if (CPCAPI2_BRAND_SIP_DIALOG_EVENT_MODULE == 1)
#include "SipDialogEventSubscriptionManagerInterface.h"
#include "SipDialogEventPublicationManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace SipDialogEvent
{

SipDialogEventSubscriptionManager* SipDialogEventSubscriptionManager::getInterface(CPCAPI2::Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_SIP_DIALOG_EVENT_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<SipDialogEventSubscriptionManagerInterface>(phone, "SipDialogEventSubscriptionManagerInterface");
#else
   return NULL;
#endif
}

SipDialogEventPublicationManager* SipDialogEventPublicationManager::getInterface(CPCAPI2::Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_SIP_DIALOG_EVENT_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<SipDialogEventPublicationManagerInterface>(phone, "SipDialogEventPublicationManager");
#else
   return NULL;
#endif
}

}
}
