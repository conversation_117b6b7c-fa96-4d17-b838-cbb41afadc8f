#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIODEVICETYPE_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIODEVICETYPE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
  enum class AudioAudioDeviceType
  {
    Capture,
    Render
  };

  inline std::string to_string(AudioAudioDeviceType value)
  {
    switch (value)
    {
      case AudioAudioDeviceType::Capture:
        return "Capture";
      case AudioAudioDeviceType::Render:
        return "Render";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, AudioAudioDeviceType value)
  {
    switch (value)
    {
      case AudioAudioDeviceType::Capture:
        os << "Capture";
        break;
      case AudioAudioDeviceType::Render:
        os << "Render";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const AudioAudioDeviceType& e)
  {
    switch (e)
    {
      case AudioAudioDeviceType::Capture:
        j = "Capture";
        break;
      case AudioAudioDeviceType::Render:
        j = "Render";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, AudioAudioDeviceType& e)
  {
      if (0 == j.get<std::string>().compare("Capture"))
    {
      e = AudioAudioDeviceType::Capture;
    }
    else if (0 == j.get<std::string>().compare("Render"))
    {
      e = AudioAudioDeviceType::Render;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum AudioAudioDeviceType"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIODEVICETYPE_H
