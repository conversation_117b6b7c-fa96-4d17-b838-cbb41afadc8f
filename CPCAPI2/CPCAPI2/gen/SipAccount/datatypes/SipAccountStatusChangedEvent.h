#include "jsonrpc/OptionalSerialization.h"
#include "jsonrpc/StringSerialization.h"
#include <nlohmann/json.hpp>
#include "SipAccountReason.h"
#include "SipAccountTransportType.h"
#include "SipAccountStatus.h"

#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTSTATUSCHANGEDEVENT_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTSTATUSCHANGEDEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  class SipAccountStatusChangedEvent
  {
  public:
    std::optional<int32_t> sipAccountHandle;
    std::optional<SipAccountReason> reason;
    std::optional<int32_t> signalingStatusCode;
    std::optional<cpc::string> signalingResponseText;
    std::optional<cpc::string> accountBindingIpAddress;
    std::optional<cpc::string> rinstance;
    std::optional<SipAccountTransportType> transportType;
    std::optional<SipAccountStatus> accountStatus;
    std::optional<int32_t> responseTimeMs;

    SipAccountStatusChangedEvent() = default;

    SipAccountStatusChangedEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipAccountStatusChangedEvent& o)
    {
      j = nlohmann::json::object();
       if (o.sipAccountHandle) { j["sipAccountHandle"] = *(o.sipAccountHandle); }
     if (o.reason) { j["reason"] = *(o.reason); }
     if (o.signalingStatusCode) { j["signalingStatusCode"] = *(o.signalingStatusCode); }
     if (o.signalingResponseText) { j["signalingResponseText"] = *(o.signalingResponseText); }
     if (o.accountBindingIpAddress) { j["accountBindingIpAddress"] = *(o.accountBindingIpAddress); }
     if (o.rinstance) { j["rinstance"] = *(o.rinstance); }
     if (o.transportType) { j["transportType"] = *(o.transportType); }
     if (o.accountStatus) { j["accountStatus"] = *(o.accountStatus); }
     if (o.responseTimeMs) { j["responseTimeMs"] = *(o.responseTimeMs); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipAccountStatusChangedEvent& o)
    {
     if (j.contains("sipAccountHandle")) { o.sipAccountHandle = j.at("sipAccountHandle"); }
     if (j.contains("reason")) { o.reason = j.at("reason"); }
     if (j.contains("signalingStatusCode")) { o.signalingStatusCode = j.at("signalingStatusCode"); }
     if (j.contains("signalingResponseText")) { o.signalingResponseText = j.at("signalingResponseText"); }
     if (j.contains("accountBindingIpAddress")) { o.accountBindingIpAddress = j.at("accountBindingIpAddress"); }
     if (j.contains("rinstance")) { o.rinstance = j.at("rinstance"); }
     if (j.contains("transportType")) { o.transportType = j.at("transportType"); }
     if (j.contains("accountStatus")) { o.accountStatus = j.at("accountStatus"); }
     if (j.contains("responseTimeMs")) { o.responseTimeMs = j.at("responseTimeMs"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTSTATUSCHANGEDEVENT_H
