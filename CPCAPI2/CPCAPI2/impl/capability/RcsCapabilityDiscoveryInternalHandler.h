#pragma once

#if !defined(CPCAPI2_CAPABILITY_DISCOVERY_INTERNAL_HANDLER_H)
#define CPCAPI2_CAPABILITY_DISCOVERY_INTERNAL_HANDLER_H

#include "cpcapi2defs.h"

namespace CPCAPI2
{
namespace RcsCapabilityDiscovery
{

/**
* Do not use this interface; it covers alpha-grade  
* functionality that is not officially supported and is subject to change.
* Contains information about an incoming capability discovery. The next step must
* be to call RcsCapabilityDiscoveryManager::response(), passing in the handle that is passed in this event.
*
* @param capability The handle for this particular capability discovery transaction
* @param to The recipient of the message
* @param from The sender of the message
* @param caps The capability set
* @param account The %SipAccount that received the capability discovery
*/   
struct RcsIncomingCapabilityDiscoveryEvent
{
   RcsCapabilityDiscoveryInternalHandle   capability;
   NameAddress                            to;
   NameAddress                            from;
   RcsCapabilitySet                       caps;
   CPCAPI2::SipAccount::SipAccountHandle  account;
};

/**
* Do not use this interface; it covers alpha-grade  
* functionality that is not officially supported and is subject to change.
* Contains information about the success or failure of an outgoing capability discovery.
*
* @param capability: The handle for the specific capability discovery.
* @param signalingStatusCode: The standard SIP code for the success (e.g. 200) or failure (e.g. 404)
* @param signalingResponseText: The text description of the SIP code, for example, "OK".
* @param caps: The capability set
*/

struct RcsOutgoingCapabilityDiscoveryEvent
{
   RcsCapabilityDiscoveryInternalHandle   capability;
   NameAddress                            to;
   NameAddress                            from;
   unsigned int                           signalingStatusCode;
   cpc::string                           signalingResponseText;
   RcsCapabilitySet                       caps;
};

/**
* Do not use this interface; it covers alpha-grade  
* functionality that is not officially supported and is subject to change.
* Used to report general SDK error conditions, such as invalid handles, or cases
* where the call is not in a valid state for the requested operation.
*/
struct ErrorEvent
{
   cpc::string errorText;
};

/**
* Do not use this interface; it covers alpha-grade  
* functionality that is not officially supported and is subject to change.
 * Handler for events relating to incoming and outgoing capability discovery.
 */
class RcsCapabilityDiscoveryInternalHandler
{
public:
   /**
   * Notifies that a capability discovery has been received on the specified account. 
   */   
   virtual int onIncomingCapabilityDiscovery(CPCAPI2::SipAccount::SipAccountHandle account, const RcsIncomingCapabilityDiscoveryEvent& args) = 0;

   /**
   * Notifies that an outgoing capability discovery has been successfully delivered to the recipient.
   * @param account The %SipAccount that was used to deliver the the capability discovery
   * @param args Contains details about the success of the event 
   * which should always be 200 OK).
   */   
   virtual int onOutgoingCapabilityDiscoverySuccess(CPCAPI2::SipAccount::SipAccountHandle account, const RcsOutgoingCapabilityDiscoveryEvent& args) { return kSuccess; }

   /**
   * Notifies that delivery of an outgoing capability discovery has failed.
   * @param account The %SipAccount that was used to deliver the capability discovery
   * @param args Contains details about the failure of the event.
   */   
   virtual int onOutgoingCapabilityDiscoveryFailure(CPCAPI2::SipAccount::SipAccountHandle account, const RcsOutgoingCapabilityDiscoveryEvent& args) { return kSuccess; }

   virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const ErrorEvent& args) = 0;

   virtual int onReleased() = 0;
};

}
}
#endif // CPCAPI2_CAPABILITY_DISCOVERY_INTERNAL_HANDLER_H
