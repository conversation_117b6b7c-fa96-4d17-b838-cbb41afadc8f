#pragma once

#if !defined(__CPCAPI2_LICENSING_CLIENT_MANAGER_INTERFACE_H__)
#define __CPCAPI2_LICENSING_CLIENT_MANAGER_INTERFACE_H__

#include "cpcapi2defs.h"
#include "licensing/LicensingClientManager.h"
#include "../phone/PhoneModule.h"
#include "../phone/PhoneInterface.h"

#include <rutil/Fifo.hxx>

namespace CPCAPI2
{
namespace Licensing
{
class LicensingClientManagerImpl;
struct LicensingClientInfo;

class LicensingClientManagerInterface : public PhoneModule,
                                        public LicensingClientManager
{
public:
   LicensingClientManagerInterface(Phone* phone);
   virtual ~LicensingClientManagerInterface();

   // LicensingClientManager interface
   virtual LicensingClientHandle create() OVERRIDE;
   virtual int setHandler(LicensingClientHandle client, LicensingClientHandler* handler) OVERRIDE;
   virtual int applySettings(LicensingClientHandle client, const LicensingClientSettings& settings) OVERRIDE;
   virtual int validateLicenses(LicensingClientHandle client) OVERRIDE;
   virtual int destroy(LicensingClientHandle client) OVERRIDE;
   virtual int checkValidLicenseKeyFormat(const cpc::string& licenseKey) OVERRIDE;
   virtual int getHardwareId(cpc::string& hardwareId) OVERRIDE;
   virtual int getHardwareId(LicensingClientHandle client, cpc::string& hardwareId) OVERRIDE;
   virtual int getHardwareHash(cpc::string& hardwareHash) OVERRIDE;
   virtual int setBrandedExpiry(LicensingClientSettings& settings) const OVERRIDE;
   virtual int process(unsigned int timeout) OVERRIDE;
   virtual void interruptProcess() OVERRIDE;
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE;
   virtual bool appResourceSignatureCheckEnabled() const OVERRIDE;

   void postCallback(resip::ReadCallbackBase* f);   
   void post(resip::ReadCallbackBase* f);

   void addSdkObserver(LicensingClientHandler* observer);
   void removeSdkObserver(LicensingClientHandler* observer);
   PhoneInterface* getPhoneInterface() { return mPhone; }

   void setHandlerImpl(LicensingClientHandle client, LicensingClientHandler* handler);
   LicensingClientHandle createImpl();
   void applySettingsImpl(LicensingClientHandle client, const LicensingClientSettings& settings);
   void validateLicensesImpl(LicensingClientHandle client);
   void destroyImpl(LicensingClientHandle client);

   bool checkValidateLicensesAttempted() const;
   void resetValidateLicensesAttempted();

   void onPermissionGranted(int requestCode, CPCAPI2::Permission permission);

private:
   PhoneInterface* mPhone;
   std::set<LicensingClientHandler*> mSdkObservers;
   typedef std::map<LicensingClientHandle, LicensingClientManagerImpl*> ManagerImplMap;
   ManagerImplMap mManagerImplMap;
   resip::Fifo<resip::ReadCallbackBase> mCallbackFifo;
   bool mShutdown;
   std::function<void(void)> mCbHook;

   typedef std::set<LicensingClientHandle> PendingValidationList;
   PendingValidationList mPendingLicenseValidations;

   // PhoneModule interface
   virtual void Release() OVERRIDE;

   LicensingClientManagerImpl* getLicensingClientManager(LicensingClientHandle client) const;
   LicensingClientInfo* getLicensingClientInfo(LicensingClientHandle client) const;

   void addSdkObserverImpl(LicensingClientHandler* observer);
   void removeSdkObserverImpl(LicensingClientHandler* observer);
   void createImpl2(LicensingClientHandle client);
};

std::ostream& operator<<(std::ostream& os, const LicensingClientSettings& transport);

}
}

#endif // __CPCAPI2_LICENSING_CLIENT_MANAGER_INTERFACE_H__
