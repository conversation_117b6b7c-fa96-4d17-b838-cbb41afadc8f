#include "jsonrpc/StringSerialization.h"
#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_PHONE_PHONESETFILELOGGINGENABLED_H
#define JSONRPC_CPCAPI2_PHONE_PHONESETFILELOGGINGENABLED_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  class PhoneSetFileLoggingEnabled
  {
  public:
    int32_t handle;
    bool enabled;
    std::optional<cpc::string> id;

    PhoneSetFileLoggingEnabled() = default;

    PhoneSetFileLoggingEnabled(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const PhoneSetFileLoggingEnabled& o)
    {
      j = nlohmann::json::object();
       j["handle"] = o.handle;
     j["enabled"] = o.enabled;
     if (o.id) { j["id"] = *(o.id); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, PhoneSetFileLoggingEnabled& o)
    {
     o.handle = j.at("handle");
     o.enabled = j.at("enabled");
     if (j.contains("id")) { o.id = j.at("id"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_PHONESETFILELOGGINGENABLED_H
