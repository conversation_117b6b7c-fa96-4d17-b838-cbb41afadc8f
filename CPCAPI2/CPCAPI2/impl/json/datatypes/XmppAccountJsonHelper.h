// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef XmppAccount_JSON_HELPER_H
#define XmppAccount_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/public/xmpp/XmppAccount.h>
#include <interface/public/xmpp/XmppAccountHandler.h>
#include <interface/public/xmpp/XmppAccountSettings.h>
#include <interface/public/xmpp/XmppAccountState.h>
#include <interface/experimental/xmpp/XmppAccountJsonProxyStateHandler.h>
#include <impl/xmpp/XmppAccountHandlerInternal.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::XmppAccountSettings& v)
    {
      w.StartObject();
      Write(w, "username", v.username);
      Write(w, "domain", v.domain);
      Write(w, "password", v.password);
      Write(w, "proxy", v.proxy);
      Write(w, "port", v.port);
      Write(w, "resource", v.resource);
      Write(w, "priority", v.priority);
      Write(w, "softwareName", v.softwareName);
      Write(w, "softwareVersion", v.softwareVersion);
      Write(w, "softwareOS", v.softwareOS);
      Write(w, "identityCategory", v.identityCategory);
      Write(w, "identityType", v.identityType);
      Write(w, "connectTimeOut", v.connectTimeOut);
      Write(w, "connectRandomInterval", v.connectRandomInterval);
      Write(w, "keepAliveTime", v.keepAliveTime);
      Write(w, "usePingKeepAlive", v.usePingKeepAlive);
      Write(w, "fileTransfileProxies", v.fileTransfileProxies);
      Write(w, "enableLocalSocks5Proxy", v.enableLocalSocks5Proxy);
      Write(w, "enableRemoteStreamHostDiscovery", v.enableRemoteStreamHostDiscovery);
      Write(w, "httpFileUploadTransferRate", v.httpFileUploadTransferRate);
      Write(w, "sslVersion", v.sslVersion);
      Write(w, "cipherSuite", v.cipherSuite);
      Write(w, "ignoreCertVerification", v.ignoreCertVerification);
      Write(w, "additionalCertPeerNames", v.additionalCertPeerNames);
      Write(w, "acceptedCertPublicKeys", v.acceptedCertPublicKeys);
      Write(w, "requiredCertPublicKeys", v.requiredCertPublicKeys);
      Write(w, "logXmppStanzas", v.logXmppStanzas);
      Write(w, "ipVersion", v.ipVersion);
      Write(w, "nameServers", v.nameServers);
      Write(w, "additionalNameServers", v.additionalNameServers);
      Write(w, "enableStreamManagement", v.enableStreamManagement);
      Write(w, "enableStreamResumption", v.enableStreamResumption);
      Write(w, "maxStreamResumptionTimeout", v.maxStreamResumptionTimeout);
      Write(w, "streamManagementId", v.streamManagementId);
      Write(w, "streamManagementSequence", v.streamManagementSequence);
      Write(w, "publishInitialPresenceAsAvailable", v.publishInitialPresenceAsAvailable);
      Write(w, "fallbackOnResourceConflict", v.fallbackOnResourceConflict);
      Write(w, "enableCompression", v.enableCompression);
      Write(w, "enableXmppPresence", v.enableXmppPresence);
      Write(w, "enableXmppStanza", v.enableXmppStanza);
      Write(w, "unrequestedAckSendIntervalSec", v.unrequestedAckSendIntervalSec);
      Write(w, "enableSeeOtherHost", v.enableSeeOtherHost);
      Write(w, "logTlsEncryptionKey", v.logTlsEncryptionKey);
      Write(w, "legacyChatXepSupport", v.legacyChatXepSupport);
      Write(w, "enableChatDeliveryReceipts", v.enableChatDeliveryReceipts);
      Write(w, "enableChatReadReceipts", v.enableChatReadReceipts);
      Write(w, "supportChatMessageStyling", v.supportChatMessageStyling);
      Write(w, "enableGroupChatDeliveryReceipts", v.enableGroupChatDeliveryReceipts);
      Write(w, "enableGroupChatReadReceipts", v.enableGroupChatReadReceipts);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::XmppAccountSettings& v)
    {
      Read(json, "username", v.username);
      Read(json, "domain", v.domain);
      Read(json, "password", v.password);
      Read(json, "proxy", v.proxy);
      Read(json, "port", v.port);
      Read(json, "resource", v.resource);
      Read(json, "priority", v.priority);
      Read(json, "softwareName", v.softwareName);
      Read(json, "softwareVersion", v.softwareVersion);
      Read(json, "softwareOS", v.softwareOS);
      Read(json, "identityCategory", v.identityCategory);
      Read(json, "identityType", v.identityType);
      Read(json, "connectTimeOut", v.connectTimeOut);
      Read(json, "connectRandomInterval", v.connectRandomInterval);
      Read(json, "keepAliveTime", v.keepAliveTime);
      Read(json, "usePingKeepAlive", v.usePingKeepAlive);
      Read(json, "fileTransfileProxies", v.fileTransfileProxies);
      Read(json, "enableLocalSocks5Proxy", v.enableLocalSocks5Proxy);
      Read(json, "enableRemoteStreamHostDiscovery", v.enableRemoteStreamHostDiscovery);
      Read(json, "httpFileUploadTransferRate", v.httpFileUploadTransferRate);
      Read(json, "sslVersion", v.sslVersion);
      Read(json, "cipherSuite", v.cipherSuite);
      Read(json, "ignoreCertVerification", v.ignoreCertVerification);
      Read(json, "additionalCertPeerNames", v.additionalCertPeerNames);
      Read(json, "acceptedCertPublicKeys", v.acceptedCertPublicKeys);
      Read(json, "requiredCertPublicKeys", v.requiredCertPublicKeys);
      Read(json, "logXmppStanzas", v.logXmppStanzas);
      Read(json, "ipVersion", v.ipVersion);
      Read(json, "nameServers", v.nameServers);
      Read(json, "additionalNameServers", v.additionalNameServers);
      Read(json, "enableStreamManagement", v.enableStreamManagement);
      Read(json, "enableStreamResumption", v.enableStreamResumption);
      Read(json, "maxStreamResumptionTimeout", v.maxStreamResumptionTimeout);
      Read(json, "streamManagementId", v.streamManagementId);
      Read(json, "streamManagementSequence", v.streamManagementSequence);
      Read(json, "publishInitialPresenceAsAvailable", v.publishInitialPresenceAsAvailable);
      Read(json, "fallbackOnResourceConflict", v.fallbackOnResourceConflict);
      Read(json, "enableCompression", v.enableCompression);
      Read(json, "enableXmppPresence", v.enableXmppPresence);
      Read(json, "enableXmppStanza", v.enableXmppStanza);
      Read(json, "unrequestedAckSendIntervalSec", v.unrequestedAckSendIntervalSec);
      Read(json, "enableSeeOtherHost", v.enableSeeOtherHost);
      Read(json, "logTlsEncryptionKey", v.logTlsEncryptionKey);
      Read(json, "legacyChatXepSupport", v.legacyChatXepSupport);
      Read(json, "enableChatDeliveryReceipts", v.enableChatDeliveryReceipts);
      Read(json, "enableChatReadReceipts", v.enableChatReadReceipts);
      Read(json, "supportChatMessageStyling", v.supportChatMessageStyling);
      Read(json, "enableGroupChatDeliveryReceipts", v.enableGroupChatDeliveryReceipts);
      Read(json, "enableGroupChatReadReceipts", v.enableGroupChatReadReceipts);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::XmppDataFormField& v)
    {
      w.StartObject();
      Write(w, "type", v.type);
      Write(w, "name", v.name);
      Write(w, "required", v.required);
      Write(w, "label", v.label);
      Write(w, "values", v.values);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::XmppDataFormField& v)
    {
      Read(json, "type", v.type);
      Read(json, "name", v.name);
      Read(json, "required", v.required);
      Read(json, "label", v.label);
      Read(json, "values", v.values);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::XmppDataForm& v)
    {
      w.StartObject();
      Write(w, "type", v.type);
      Write(w, "instructions", v.instructions);
      Write(w, "title", v.title);
      Write(w, "fields", v.fields);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::XmppDataForm& v)
    {
      Read(json, "type", v.type);
      Read(json, "instructions", v.instructions);
      Read(json, "title", v.title);
      Read(json, "fields", v.fields);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::XmppStorageData& v)
    {
      w.StartObject();
      Write(w, "name", v.name);
      Write(w, "value", v.value);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::XmppStorageData& v)
    {
      Read(json, "name", v.name);
      Read(json, "value", v.value);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::XmppAccountManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::XmppAccountManager& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::XmppTLSConnectionInfo& v)
    {
      w.StartObject();
      Write(w, "certificateStatus", v.certificateStatus);
      Write(w, "issuer", v.issuer);
      Write(w, "server", v.server);
      Write(w, "peerNames", v.peerNames);
      Write(w, "protocol", v.protocol);
      Write(w, "cipher", v.cipher);
      Write(w, "mac", v.mac);
      Write(w, "compression", v.compression);
      Write(w, "publicKey", v.publicKey);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::XmppTLSConnectionInfo& v)
    {
      Read(json, "certificateStatus", v.certificateStatus);
      Read(json, "issuer", v.issuer);
      Read(json, "server", v.server);
      Read(json, "peerNames", v.peerNames);
      Read(json, "protocol", v.protocol);
      Read(json, "cipher", v.cipher);
      Read(json, "mac", v.mac);
      Read(json, "compression", v.compression);
      Read(json, "publicKey", v.publicKey);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& v)
    {
      w.StartObject();
      Write(w, "accountStatus", v.accountStatus);
      Write(w, "errorCode", v.errorCode);
      Write(w, "errorText", v.errorText);
      Write(w, "tlsInfo", v.tlsInfo);
      Write(w, "remote", v.remote);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& v)
    {
      Read(json, "accountStatus", v.accountStatus);
      Read(json, "errorCode", v.errorCode);
      Read(json, "errorText", v.errorText);
      Read(json, "tlsInfo", v.tlsInfo);
      Read(json, "remote", v.remote);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::ErrorEvent& v)
    {
      w.StartObject();
      Write(w, "errorText", v.errorText);
      Write(w, "errorCode", v.errorCode);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::ErrorEvent& v)
    {
      Read(json, "errorText", v.errorText);
      Read(json, "errorCode", v.errorCode);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::LicensingErrorEvent& v)
    {
      w.StartObject();
      Write(w, "errorText", v.errorText);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::LicensingErrorEvent& v)
    {
      Read(json, "errorText", v.errorText);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::EntityTimeEvent& v)
    {
      w.StartObject();
      Write(w, "errorCode", v.errorCode);
      Write(w, "from", v.from);
      Write(w, "timestamp", v.timestamp);
      Write(w, "millisecond", v.millisecond);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::EntityTimeEvent& v)
    {
      Read(json, "errorCode", v.errorCode);
      Read(json, "from", v.from);
      Read(json, "timestamp", v.timestamp);
      Read(json, "millisecond", v.millisecond);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::EntityFeatureEvent& v)
    {
      w.StartObject();
      Write(w, "entity", v.entity);
      Write(w, "features", v.features);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::EntityFeatureEvent& v)
    {
      Read(json, "entity", v.entity);
      Read(json, "features", v.features);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::StreamManagementStateEvent& v)
    {
      w.StartObject();
      Write(w, "id", v.id);
      Write(w, "sequence", v.sequence);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::StreamManagementStateEvent& v)
    {
      Read(json, "id", v.id);
      Read(json, "sequence", v.sequence);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::PrivateStorageDataEvent& v)
    {
      w.StartObject();
      Write(w, "data", v.data);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::PrivateStorageDataEvent& v)
    {
      Read(json, "data", v.data);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::XmppAccountHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::XmppAccountHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::XmppAccountState& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      Write(w, "accountStatus", v.accountStatus);
      Write(w, "settings", v.settings);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::XmppAccountState& v)
    {
      Read(json, "account", v.account);
      Read(json, "accountStatus", v.accountStatus);
      Read(json, "settings", v.settings);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::XmppAccountStateManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::XmppAccountStateManager& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::JsonProxyAccountStateEvent& v)
    {
      w.StartObject();
      Write(w, "accountState", v.accountState);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::JsonProxyAccountStateEvent& v)
    {
      Read(json, "accountState", v.accountState);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::XmppAccountJsonProxyStateHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::XmppAccountJsonProxyStateHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::XmppAccountConfiguredEvent& v)
    {
      w.StartObject();
      Write(w, "settings", v.settings);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::XmppAccountConfiguredEvent& v)
    {
      Read(json, "settings", v.settings);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::XmppAccountEnabledEvent& v)
    {
      w.StartObject();
      Write(w, "settings", v.settings);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::XmppAccountEnabledEvent& v)
    {
      Read(json, "settings", v.settings);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::XmppAccountDisabledEvent& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::XmppAccountDisabledEvent& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAccount::XmppAccountHandlerInternal& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAccount::XmppAccountHandlerInternal& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // XmppAccount_JSON_HELPER_H