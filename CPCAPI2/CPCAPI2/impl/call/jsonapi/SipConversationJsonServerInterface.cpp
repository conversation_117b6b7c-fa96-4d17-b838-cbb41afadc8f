#include "brand_branded.h"

#if (CPCAPI2_BRAND_CALL_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "SipConversationJsonServerInterface.h"
#include "call/SipAVConversationManagerInterface.h"
#include "peerconnection/PeerConnectionManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "json/JsonHelper.h"
#include "util/LogSubsystems.h"

#include <MediaStackImpl.hxx>
#include <MixerImpl.hxx>

#include <rutil/Logger.hxx>
#include <rutil/Random.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

using CPCAPI2::PeerConnection::PeerConnectionManager;
using CPCAPI2::PeerConnection::PeerConnectionSettings;
using CPCAPI2::PeerConnection::SessionDescription;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_CALL
#define JSON_MODULE "SipConversationManagerJsonProxy"

namespace CPCAPI2
{

namespace SipConversation
{

SipConversationJsonServerInterface::SipConversationJsonServerInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mPeerConnMgr(CPCAPI2::PeerConnection::PeerConnectionManager::getInterface(phone))
{
   SipConversationManager* sipConv = SipConversationManager::getInterface(phone);
   SipAVConversationManagerInterface* sipConvIf = dynamic_cast<SipAVConversationManagerInterface*>(sipConv);
   sipConvIf->addSdkObserver(this);

   mFunctionMap["peerConnectionOffer"] = std::bind(&SipConversationJsonServerInterface::handlePeerConnectionOffer, this, std::placeholders::_1);
   mFunctionMap["peerConnectionAnswer"] = std::bind(&SipConversationJsonServerInterface::handlePeerConnectionAnswer, this, std::placeholders::_1);
   mFunctionMap["provideSdpOffer"] = std::bind(&SipConversationJsonServerInterface::handleProvideSdpOffer, this, std::placeholders::_1);
   mFunctionMap["provideSdpAnswer"] = std::bind(&SipConversationJsonServerInterface::handleProvideSdpAnswer, this, std::placeholders::_1);

   mFunctionMap["createConversation"] = std::bind(&SipConversationJsonServerInterface::handleCreateConversation, this, std::placeholders::_1);
   mFunctionMap["setHandler"] = std::bind(&SipConversationJsonServerInterface::handleSetHandler, this, std::placeholders::_1);
   mFunctionMap["setDefaultSettings"] = std::bind(&SipConversationJsonServerInterface::handleSetDefaultSettings, this, std::placeholders::_1);
   mFunctionMap["addParticipant"] = std::bind(&SipConversationJsonServerInterface::handleAddParticipant, this, std::placeholders::_1);
   mFunctionMap["start"] = std::bind(&SipConversationJsonServerInterface::handleStart, this, std::placeholders::_1);
   mFunctionMap["end"] = std::bind(&SipConversationJsonServerInterface::handleEnd, this, std::placeholders::_1);
   mFunctionMap["accept"] = std::bind(&SipConversationJsonServerInterface::handleAccept, this, std::placeholders::_1);
   mFunctionMap["reject"] = std::bind(&SipConversationJsonServerInterface::handleReject, this, std::placeholders::_1);
   mFunctionMap["requestStateAllConversations"] = std::bind(&SipConversationJsonServerInterface::handleRequestStateAllConversations, this, std::placeholders::_1);
   mFunctionMap["joinRemoteConversation"] = std::bind(&SipConversationJsonServerInterface::handleJoinRemoteConversation, this, std::placeholders::_1);
   mFunctionMap["configureMedia"] = std::bind(&SipConversationJsonServerInterface::handleConfigureMedia, this, std::placeholders::_1);
   mFunctionMap["setCryptoSuitesForMedia"] = std::bind(&SipConversationJsonServerInterface::handleSetCryptoSuitesForMedia, this, std::placeholders::_1);
   mFunctionMap["setMediaEnabled"] = std::bind(&SipConversationJsonServerInterface::handleSetMediaEnabled, this, std::placeholders::_1);
   mFunctionMap["sendMediaChangeRequest"] = std::bind(&SipConversationJsonServerInterface::handleSendMediaChangeRequest, this, std::placeholders::_1);
   mFunctionMap["hold"] = std::bind(&SipConversationJsonServerInterface::handleHold, this, std::placeholders::_1);
   mFunctionMap["unhold"] = std::bind(&SipConversationJsonServerInterface::handleUnhold, this, std::placeholders::_1);
   mFunctionMap["setCallInitiatedFromPush"] = std::bind(&SipConversationJsonServerInterface::handleSetCallInitiatedFromPush, this, std::placeholders::_1);
   mFunctionMap["setMediaEnabledByDirection"] = std::bind(&SipConversationJsonServerInterface::handleSetMediaEnabledByDirection, this, std::placeholders::_1);
   mFunctionMap["setMediaCryptoSuites"] = std::bind(&SipConversationJsonServerInterface::handleSetMediaCryptoSuites, this, std::placeholders::_1);
   mFunctionMap["setAnonymousMode"] = std::bind(&SipConversationJsonServerInterface::handleSetAnonymousMode, this, std::placeholders::_1);
   mFunctionMap["setBestEffortMediaEncryption"] = std::bind(&SipConversationJsonServerInterface::handleSetBestEffortMediaEncryption, this, std::placeholders::_1);
   mFunctionMap["redirect"] = std::bind(&SipConversationJsonServerInterface::handleRedirect, this, std::placeholders::_1);
   mFunctionMap["sendRingingResponse"] = std::bind(&SipConversationJsonServerInterface::handleSendRingingResponse, this, std::placeholders::_1);
   mFunctionMap["acceptIncomingTransferRequest"] = std::bind(&SipConversationJsonServerInterface::handleAcceptIncomingTransferRequest, this, std::placeholders::_1);
   mFunctionMap["rejectIncomingTransferRequest"] = std::bind(&SipConversationJsonServerInterface::handleRejectIncomingTransferRequest, this, std::placeholders::_1);
   mFunctionMap["transfer"] = std::bind(&SipConversationJsonServerInterface::handleTransfer, this, std::placeholders::_1);
   mFunctionMap["setDtmfMode"] = std::bind(&SipConversationJsonServerInterface::handleSetDtmfMode, this, std::placeholders::_1);
   mFunctionMap["startDtmfTone"] = std::bind(&SipConversationJsonServerInterface::handleStartDtmfTone, this, std::placeholders::_1);
   mFunctionMap["stopDtmfTone"] = std::bind(&SipConversationJsonServerInterface::handleStopDtmfTone, this, std::placeholders::_1);
   mFunctionMap["setAnswerMode"] = std::bind(&SipConversationJsonServerInterface::handleSetAnswerMode, this, std::placeholders::_1);
   mFunctionMap["refreshConversationStatistics"] = std::bind(&SipConversationJsonServerInterface::handleRefreshConversationStatistics, this, std::placeholders::_1);
   mFunctionMap["setAdornmentHeaders"] = std::bind(&SipConversationJsonServerInterface::handleSetAdornmentHeaders, this, std::placeholders::_1);
   mFunctionMap["adornMessage"] = std::bind(&SipConversationJsonServerInterface::handleAdornMessage, this, std::placeholders::_1);
   mFunctionMap["playSound"] = std::bind(&SipConversationJsonServerInterface::handlePlaySound, this, std::placeholders::_1);
   mFunctionMap["stopPlaySound"] = std::bind(&SipConversationJsonServerInterface::handleStopPlaySound, this, std::placeholders::_1);
   mFunctionMap["startMonitoringAudioDeviceLevels"] = std::bind(&SipConversationJsonServerInterface::handleStartMonitoringAudioDeviceLevels, this, std::placeholders::_1);
   mFunctionMap["stopMonitoringAudioDeviceLevels"] = std::bind(&SipConversationJsonServerInterface::handleStopMonitoringAudioDeviceLevels, this, std::placeholders::_1);
   mFunctionMap["setCallKitMode"] = std::bind(&SipConversationJsonServerInterface::handleSetCallKitMode, this, std::placeholders::_1);
   mFunctionMap["setTelecomFrameworkMode"] = std::bind(&SipConversationJsonServerInterface::handleSetTelecomFrameworkMode, this, std::placeholders::_1);
   mFunctionMap["setIncomingVideoRenderTarget"] = std::bind(&SipConversationJsonServerInterface::handleSetIncomingVideoRenderTarget, this, std::placeholders::_1);
   mFunctionMap["setVideoNackSettings"] = std::bind(&SipConversationJsonServerInterface::handleSetVideoNackSettings, this, std::placeholders::_1);

   dynamic_cast<PeerConnection::PeerConnectionManagerInterface*>(mPeerConnMgr)->addSdkObserver(this);

   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
   mVideoStreamingMgr = CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(phone);
   mMediaMgr = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(phone));
}

SipConversationJsonServerInterface::~SipConversationJsonServerInterface()
{
   dynamic_cast<PeerConnection::PeerConnectionManagerInterface*>(mPeerConnMgr)->removeSdkObserver(this);
}

void SipConversationJsonServerInterface::Release()
{
}

void SipConversationJsonServerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void SipConversationJsonServerInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int SipConversationJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&SipConversationJsonServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

void SipConversationJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

int SipConversationJsonServerInterface::handleCreateConversation(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);
   SipAccount::SipAccountHandle acctHandle = 0;
   JsonDeserialize(functionObjectVal, "account", acctHandle);
   SipConversationHandle conversation = sipConvManager->createConversation(acctHandle);

   JsonFunctionCall(mTransport, "createConversationResult", JSON_VALUE(conversation));
   return kSuccess;
}

int SipConversationJsonServerInterface::handleSetHandler(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);
   SipAccount::SipAccountHandle acctHandle = 0;
   JsonDeserialize(functionObjectVal, "account", acctHandle);
   sipConvManager->setHandler(acctHandle, this);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleSetDefaultSettings(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipAccount::SipAccountHandle acctHandle = 0;
   SipConversationSettings convSettings;
   JsonDeserialize(functionObjectVal, "account", acctHandle, "settings", convSettings);

   if (functionObjectVal.HasMember("transport"))
   {
      CPCAPI2::NetworkTransport transport = CPCAPI2::TransportNone;
      JsonDeserialize(functionObjectVal, "transport", transport);
      sipConvManager->setDefaultSettings(acctHandle, convSettings, transport);
   }
   else
   {
      sipConvManager->setDefaultSettings(acctHandle, convSettings);
   }
   return kSuccess;
}

int SipConversationJsonServerInterface::handleAddParticipant(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   cpc::string participantAddress;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), "participantAddress", participantAddress);

   sipConvManager->addParticipant(conversation, participantAddress);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleStart(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation));

   sipConvManager->start(conversation);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleEnd(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation));

   sipConvManager->end(conversation);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleAccept(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation));

   sipConvManager->accept(conversation);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleReject(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation));

   sipConvManager->reject(conversation);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleConfigureMedia(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   CPCAPI2::SipConversation::MediaInfo mediaDescriptor;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(mediaDescriptor));

   sipConvManager->configureMedia(conversation, mediaDescriptor);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleSetCryptoSuitesForMedia(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   CPCAPI2::SipConversation::MediaType mediaType = CPCAPI2::SipConversation::MediaType_Audio;
   cpc::vector<CPCAPI2::SipConversation::MediaCryptoSuite> mediaCryptoSuites;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(mediaType), JSON_VALUE(mediaCryptoSuites));

   sipConvManager->setCryptoSuitesForMedia(conversation, mediaType, mediaCryptoSuites);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleSetMediaEnabled(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   CPCAPI2::SipConversation::MediaType mediaType = CPCAPI2::SipConversation::MediaType_Audio;
   bool enabled = false;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(mediaType), JSON_VALUE(enabled));

   sipConvManager->setMediaEnabled(conversation, mediaType, enabled);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleSendMediaChangeRequest(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation));

   sipConvManager->sendMediaChangeRequest(conversation);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleHold(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation));

   sipConvManager->hold(conversation);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleUnhold(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation));

   sipConvManager->unhold(conversation);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleSetCallInitiatedFromPush(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation));

   sipConvManager->setCallInitiatedFromPush(conversation);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleSetMediaEnabledByDirection(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   CPCAPI2::SipConversation::MediaType mediaType = CPCAPI2::SipConversation::MediaType_Audio;;
   bool enabled = false;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(mediaType), JSON_VALUE(enabled));

   sipConvManager->setMediaEnabledByDirection(conversation, mediaType, enabled);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleSetMediaCryptoSuites(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   cpc::vector<MediaCryptoSuite> cryptoSuites;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(cryptoSuites));

   sipConvManager->setMediaCryptoSuites(conversation, cryptoSuites);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleSetAnonymousMode(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   unsigned int anonymousMode = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(anonymousMode));

   sipConvManager->setAnonymousMode(conversation, anonymousMode);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleSetBestEffortMediaEncryption(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   bool enabled = false;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(enabled));

   sipConvManager->setBestEffortMediaEncryption(conversation, enabled);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleRedirect(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   cpc::string targetAddress;
   cpc::string reason;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(targetAddress), JSON_VALUE(reason));

   sipConvManager->redirect(conversation, targetAddress, reason);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleSendRingingResponse(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation));

   sipConvManager->sendRingingResponse(conversation);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleAcceptIncomingTransferRequest(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle transferTargetConversation = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(transferTargetConversation));

   sipConvManager->acceptIncomingTransferRequest(transferTargetConversation);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleRejectIncomingTransferRequest(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle transferTargetConversation = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(transferTargetConversation));

   sipConvManager->rejectIncomingTransferRequest(transferTargetConversation);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleTransfer(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle transfereeConversation = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(transfereeConversation));

   if (functionObjectVal.HasMember("transferTargetConversation"))
   {
      SipConversationHandle transferTargetConversation = 0;
      JsonDeserialize(functionObjectVal, JSON_VALUE(transferTargetConversation));

      if (functionObjectVal.HasMember("endTargetConversationOnSuccess"))
      {
         bool endTargetConversationOnSuccess = false;
         JsonDeserialize(functionObjectVal, JSON_VALUE(endTargetConversationOnSuccess));
         sipConvManager->transfer(transferTargetConversation, transfereeConversation, endTargetConversationOnSuccess);
         return kSuccess;
      }
      else
      {
         sipConvManager->transfer(transferTargetConversation, transfereeConversation);
         return kSuccess;
      }
   }
   else if (functionObjectVal.HasMember("targetAddress"))
   {
      cpc::string targetAddress;
      JsonDeserialize(functionObjectVal, JSON_VALUE(targetAddress));
      sipConvManager->transfer(transfereeConversation, targetAddress);
      return kSuccess;
   }
   return kError;
}

int SipConversationJsonServerInterface::handleSetDtmfMode(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   CPCAPI2::SipAccount::SipAccountHandle account = 0;
   unsigned int ordinal = 0;
   DtmfMode dtmfMode = DtmfMode_RFC2833;
   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(ordinal), JSON_VALUE(dtmfMode));

   sipConvManager->setDtmfMode(account, ordinal, dtmfMode);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleStartDtmfTone(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   unsigned int toneId = 0;
   bool playLocally = false;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(toneId), JSON_VALUE(playLocally));

   sipConvManager->startDtmfTone(conversation, toneId, playLocally);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleStopDtmfTone(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);
   sipConvManager->stopDtmfTone();
   return kSuccess;
}

int SipConversationJsonServerInterface::handleSetAnswerMode(const rapidjson::Value& functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);
   CPCAPI2::SipAccount::SipAccountHandle account = 0;
   AnswerModeSettings answerMode;
   CPCAPI2::NetworkTransport transport = CPCAPI2::TransportNone;
   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(answerMode), JSON_VALUE(transport));
   sipConvManager->setAnswerMode(account, answerMode, transport);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleRefreshConversationStatistics(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);
   SipConversationHandle conversation = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation));

   if (functionObjectVal.HasMember("includeNetworkStatistics"))
   {
      bool includeNetworkStatistics = false, includeJitterStatistics = false, includeRemoteStatistics = false;
      JsonDeserialize(functionObjectVal, JSON_VALUE(includeNetworkStatistics), JSON_VALUE(includeJitterStatistics), JSON_VALUE(includeRemoteStatistics));
      sipConvManager->refreshConversationStatistics(conversation);
   }
   else
   {
      sipConvManager->refreshConversationStatistics(conversation);
   }
   return kSuccess;
}

int SipConversationJsonServerInterface::handleSetAdornmentHeaders(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipAccount::SipAccountHandle acctHandle = 0;
   JsonDeserialize(functionObjectVal, "account", acctHandle, "headers", mAdornHeaders);

   sipConvManager->setAdornmentHandler(acctHandle, mAdornHeaders.empty() ? nullptr : this);

   return kSuccess;
}

int SipConversationJsonServerInterface::handleAdornMessage(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   unsigned int adornmentMessageId = 0;
   cpc::vector<SipHeader> customHeaders;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(adornmentMessageId), JSON_VALUE(customHeaders));

   sipConvManager->adornMessage(conversation, adornmentMessageId, customHeaders);
   return kSuccess;
}

int SipConversationJsonServerInterface::handlePlaySound(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   cpc::string resourceUri;
   bool repeat = false;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(resourceUri), JSON_VALUE(repeat));

   sipConvManager->playSound(conversation, resourceUri, repeat);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleStopPlaySound(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation));

   sipConvManager->stopPlaySound(conversation);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleStartMonitoringAudioDeviceLevels(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation));

   sipConvManager->startMonitoringAudioDeviceLevels(conversation);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleStopMonitoringAudioDeviceLevels(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation));

   sipConvManager->stopMonitoringAudioDeviceLevels(conversation);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleSetCallKitMode(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation));

   sipConvManager->setCallKitMode(conversation);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleSetTelecomFrameworkMode(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation));

   sipConvManager->setTelecomFrameworkMode(conversation);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleSetIncomingVideoRenderTarget(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   int64_t surface = 0;
   Media::VideoSurfaceType type = Media::VideoSurfaceType_Default;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(surface), JSON_VALUE(type));

   sipConvManager->setIncomingVideoRenderTarget(conversation, (void*)surface, type);
   return kSuccess;
}

int SipConversationJsonServerInterface::handleSetVideoNackSettings(const rapidjson::Value & functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);

   SipConversationHandle conversation = 0;
   NackSettings nackSettings;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(nackSettings));

   dynamic_cast<SipConversationManagerExt*>(sipConvManager)->setVideoNackSettings(conversation, nackSettings);
   return kSuccess;
}

int SipConversationJsonServerInterface::onNewConversation(SipConversationHandle conversation, const NewConversationEvent& args)
{
   mMediaMappingInfo[conversation].remoteSdp = args.sessionDescription;

   JsonFunctionCall(mTransport, "onNewConversation", JSON_VALUE(conversation), JSON_VALUE(args));

   return kSuccess;
}

int SipConversationJsonServerInterface::onSdpOfferAnswer(SipConversationHandle conversation, const SdpOfferAnswerEvent& args)
{
   std::map<SipConversationHandle, MediaMappingInfo>::iterator it = mMediaMappingInfo.find(conversation);
   if (it != mMediaMappingInfo.end())
   {
      JsonFunctionCall(mTransport, "onSdpOfferAnswer", JSON_VALUE(conversation), JSON_VALUE(args));
      InfoLog(<< "remote party offer: " << resip::Data(resip::Data::Share, args.sdp.sdpString, args.sdp.sdpLen));
   }
   return kSuccess;
}

int SipConversationJsonServerInterface::onLocalSdpOffer(SipConversationHandle conversation, const LocalSdpOfferEvent& args)
{
   JsonFunctionCall(mTransport, "onLocalSdpOffer", JSON_VALUE(conversation), JSON_VALUE(args));
   return kSuccess;
}

int SipConversationJsonServerInterface::onLocalSdpAnswer(SipConversationHandle conversation, const LocalSdpAnswerEvent& args)
{
   JsonFunctionCall(mTransport, "onLocalSdpAnswer", JSON_VALUE(conversation), JSON_VALUE(args));
   return kSuccess;
}

int SipConversationJsonServerInterface::onConversationEnded(SipConversationHandle conversation, const ConversationEndedEvent& args)
{
   mMediaMappingInfo.erase(conversation);
   
   auto itVs = mVideoStreamingHandleMap.find(conversation);
   if (itVs != mVideoStreamingHandleMap.end())
   {
      mVideoStreamingMgr->stopVideoStream(itVs->second);
      mVideoStreamingHandleMap.erase(itVs);
   }

   JsonFunctionCall(mTransport, "onConversationEnded", JSON_VALUE(conversation), JSON_VALUE(args));
   return kSuccess;
}

int SipConversationJsonServerInterface::onConversationStateChangeRequest(SipConversationHandle conversation, const ConversationStateChangeRequestEvent& args)
{
   JsonFunctionCall(mTransport, "onConversationStateChangeRequest", JSON_VALUE(conversation), JSON_VALUE(args));
   return kSuccess;
}

int SipConversationJsonServerInterface::onConversationStateChanged(SipConversationHandle conversation, const ConversationStateChangedEvent& args)
{
   JsonFunctionCall(mTransport, "onConversationStateChanged", JSON_VALUE(conversation), JSON_VALUE(args));
   return kSuccess;
}

int SipConversationJsonServerInterface::onConversationMediaChangeRequest(SipConversationHandle conversation, const ConversationMediaChangeRequestEvent& args)
{
   JsonFunctionCall(mTransport, "onConversationMediaChangeRequest", JSON_VALUE(conversation), JSON_VALUE(args));
   return kSuccess;
}

int SipConversationJsonServerInterface::onConversationMediaChanged(SipConversationHandle conversation, const ConversationMediaChangedEvent& _args)
{
   ConversationMediaChangedEvent args = _args;
   if (mVideoStreamingMgr != NULL)
   {
      if(mVideoStreamingHandleMap.count(conversation) != 0)
      {
         mVideoStreamingMgr->stopVideoStream(mVideoStreamingHandleMap[conversation]);
         mVideoStreamingHandleMap.erase(conversation);
      }
      for (MediaInfo& mi : args.remoteMediaInfo)
      {
         if (mi.mediaType == SipConversation::MediaType_Video)
         {
            std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mMediaMgr->media_stack_ptr()->mixer());

            CPCAPI2::VideoStreaming::VideoStreamHandle vs = mVideoStreamingMgr->createVideoStream();
            CPCAPI2::VideoStreaming::VideoStreamSettings vsSettings;
            vsSettings.videoReceiveChannel = mi.mediaStreamId;
            vsSettings.streamFormat = VideoStreaming::StreamFormat_I420;
            mVideoStreamingMgr->setVideoStreamSettings(vs, vsSettings);
            mVideoStreamingMgr->startVideoStream(vs);

            mi.mediaStreamId = vs;

            //CPCAPI2::Media::Video::getInterface(mPhone)->requestKeyFrame(mi.mediaStreamId);

            mVideoStreamingHandleMap[conversation] = vs;
         }
      }
   }

   JsonFunctionCall(mTransport, "onConversationMediaChanged", JSON_VALUE(conversation), JSON_VALUE(args));
   return kSuccess;
}

int SipConversationJsonServerInterface::onConversationInitiated(SipConversationHandle conversation, const ConversationInitiatedEvent& args)
{
	JsonFunctionCall(mTransport, "onConversationInitiated", JSON_VALUE(conversation), JSON_VALUE(args));
	return kSuccess;
}

int SipConversationJsonServerInterface::onIncomingTransferRequest(SipConversationHandle conversation, const TransferRequestEvent& args)
{
	JsonFunctionCall(mTransport, "onIncomingTransferRequest", JSON_VALUE(conversation), JSON_VALUE(args));
	return kSuccess;
}

int SipConversationJsonServerInterface::onIncomingRedirectRequest(SipConversationHandle conversation, const RedirectRequestEvent& args)
{
	JsonFunctionCall(mTransport, "onIncomingRedirectRequest", JSON_VALUE(conversation), JSON_VALUE(args));
	return kSuccess;
}

int SipConversationJsonServerInterface::onIncomingTargetChangeRequest(SipConversationHandle conversation, const TargetChangeRequestEvent& args)
{
	JsonFunctionCall(mTransport, "onIncomingTargetChangeRequest", JSON_VALUE(conversation), JSON_VALUE(args));
	return kSuccess;
}

int SipConversationJsonServerInterface::onIncomingHangupRequest(SipConversationHandle conversation, const HangupRequestEvent& args)
{
	JsonFunctionCall(mTransport, "onIncomingHangupRequest", JSON_VALUE(conversation), JSON_VALUE(args));
	return kSuccess;
}

int SipConversationJsonServerInterface::onIncomingBroadsoftTalkRequest(SipConversationHandle conversation, const BroadsoftTalkEvent& args)
{
	JsonFunctionCall(mTransport, "onIncomingBroadsoftTalkRequest", JSON_VALUE(conversation), JSON_VALUE(args));
	return kSuccess;
}

int SipConversationJsonServerInterface::onIncomingBroadsoftHoldRequest(SipConversationHandle conversation, const BroadsoftHoldEvent& args)
{
	JsonFunctionCall(mTransport, "onIncomingBroadsoftHoldRequest", JSON_VALUE(conversation), JSON_VALUE(args));
	return kSuccess;
}

int SipConversationJsonServerInterface::onTransferProgress(SipConversationHandle conversation, const TransferProgressEvent& args)
{
	JsonFunctionCall(mTransport, "onTransferProgress", JSON_VALUE(conversation), JSON_VALUE(args));
	return kSuccess;
}

int SipConversationJsonServerInterface::onConversationStatisticsUpdated(SipConversationHandle conversation, const ConversationStatisticsUpdatedEvent& args)
{
	JsonFunctionCall(mTransport, "onConversationStatisticsUpdated", JSON_VALUE(conversation), JSON_VALUE(args));
	return kSuccess;
}

int SipConversationJsonServerInterface::onAudioDeviceLevelChange(SipConversationHandle conversation, const ConversationAudioDeviceLevelChangeEvent& args)
{
	JsonFunctionCall(mTransport, "onAudioDeviceLevelChange", JSON_VALUE(conversation), JSON_VALUE(args));
	return kSuccess;
}

int SipConversationJsonServerInterface::onError(SipConversationHandle conversation, const ErrorEvent& args)
{
	JsonFunctionCall(mTransport, "onError", JSON_VALUE(conversation), JSON_VALUE(args));
	return kSuccess;
}

int SipConversationJsonServerInterface::onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args)
{
   if (!mAdornHeaders.empty()) {
      SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);
      sipConvManager->adornMessage(args.account, args.adornmentMessageId, mAdornHeaders);
   }

   return kSuccess;
}

int SipConversationJsonServerInterface::handlePeerConnectionOffer(const rapidjson::Value & functionObjectVal)
{
   cpc::string sessionIdStr;
   cpc::string sdpOfferStr;
   JsonDeserialize(functionObjectVal, "sessionId", sessionIdStr, "sdpOffer", sdpOfferStr);

   CPCAPI2::PeerConnection::SessionDescription sdpOffer;
   sdpOffer.sdpLen = sdpOfferStr.size();
   sdpOffer.sdpString = sdpOfferStr;
   sdpOffer.sdpType = CPCAPI2::PeerConnection::SessionDescription::SessionDescriptionType_Offer;

   CPCAPI2::PeerConnection::PeerConnectionHandle pc;
   std::map<cpc::string, PeerConnectionInfo>::iterator itPcInfo = mMapPeerConnections.find(sessionIdStr);
   if (itPcInfo == mMapPeerConnections.end())
   {
      PeerConnectionSettings bobSettings;
      bobSettings.certAor = "<EMAIL>";
      bobSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_Auto;
      //bobSettings.natTraversalServerHostname = "stun.counterpath.com";
      //bobSettings.natTraversalServerPort = 3478;
      //bobSettings.natTraversalServerType = PeerConnectionSettings::NatTraversalServerType_StunOnly;
      bobSettings.sessionName = "CPCAPI2-SipConversationJsonServerInterface";

      pc = mPeerConnMgr->createPeerConnection();

      PeerConnectionInfo pcInfo;
      pcInfo.sessionId = sessionIdStr;
      pcInfo.pc = pc;
      mMapPeerConnections[sessionIdStr] = pcInfo;

      mPeerConnMgr->setHandler(pc, (PeerConnectionHandler*)0xDEADBEEF);
      mPeerConnMgr->setDefaultSettings(pc, bobSettings);
   }
   else
   {
      //pc = itPcInfo->second.pc;
   }

   //CPCAPI2::PeerConnection::MediaInfo miAudio;
   //miAudio.mediaDirection = CPCAPI2::PeerConnection::MediaDirection_SendRecv;
   //miAudio.mediaType = CPCAPI2::PeerConnection::MediaType_Audio;
   //miAudio.mediaEncryptionOptions.mediaEncryptionMode = CPCAPI2::PeerConnection::MediaEncryptionMode_Unencrypted;
   //mPeerConnMgr->configureMedia(pc, miAudio);
   //CPCAPI2::PeerConnection::MediaInfo miVideo;
   //miVideo.mediaDirection = CPCAPI2::PeerConnection::MediaDirection_SendRecv;
   //miVideo.mediaType = CPCAPI2::PeerConnection::MediaType_Video;
   //miVideo.mediaEncryptionOptions.mediaEncryptionMode = CPCAPI2::PeerConnection::MediaEncryptionMode_Unencrypted;
   //mPeerConnMgr->configureMedia(pc, miVideo);
   //mPeerConnMgr->setRemoteDescription(pc, sdpOffer);
   //mPeerConnMgr->createAnswer(pc);
   return kSuccess;
}

int SipConversationJsonServerInterface::handlePeerConnectionAnswer(const rapidjson::Value& functionObjectVal)
{
   cpc::string sessionIdStr;
   cpc::string remoteSdpStr;
   JsonDeserialize(functionObjectVal, "sessionId", sessionIdStr, "sdpAnswer", remoteSdpStr);

   CPCAPI2::PeerConnection::SessionDescription remoteSdp;
   remoteSdp.sdpLen = (unsigned short)remoteSdpStr.size();
   remoteSdp.sdpString = remoteSdpStr;
   remoteSdp.sdpType = CPCAPI2::PeerConnection::SessionDescription::SessionDescriptionType_Answer;

   CPCAPI2::PeerConnection::PeerConnectionHandle pc;
   std::map<cpc::string, PeerConnectionInfo>::iterator itPcInfo = mMapPeerConnections.find(sessionIdStr);
   if (itPcInfo != mMapPeerConnections.end())
   {
      pc = itPcInfo->second.pc;
      mPeerConnMgr->setRemoteDescription(pc, remoteSdp);
   }
   return kSuccess;
}

int SipConversationJsonServerInterface::handleProvideSdpOffer(const rapidjson::Value& functionObjectVal)
{
   SipConversationHandle conversation = -1;
   cpc::string remoteSdpOfferStr;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), "sdpOffer", remoteSdpOfferStr);

   CPCAPI2::SipConversation::SessionDescription remoteSdpOffer;
   remoteSdpOffer.sdpLen = (unsigned short)remoteSdpOfferStr.size();
   remoteSdpOffer.sdpString = remoteSdpOfferStr;


   if (conversation != -1)
   {
      SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);
      SipConversationManagerInternal* sipConvManagerInternal = dynamic_cast<SipConversationManagerInternal*>(sipConvManager);
      sipConvManagerInternal->provideSdpOffer(conversation, remoteSdpOffer);
   }
   return kSuccess;
}

int SipConversationJsonServerInterface::handleProvideSdpAnswer(const rapidjson::Value& functionObjectVal)
{
   SipConversationHandle conversation = -1;
   cpc::string remoteSdpOfferStr;
   cpc::string remoteSdpAnswerStr;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), "sdpOffer", remoteSdpOfferStr, "sdpAnswer", remoteSdpAnswerStr);

   CPCAPI2::SipConversation::SessionDescription remoteSdpOffer;
   remoteSdpOffer.sdpLen = (unsigned short)remoteSdpOfferStr.size();
   remoteSdpOffer.sdpString = remoteSdpOfferStr;
   CPCAPI2::SipConversation::SessionDescription remoteSdpAnswer;
   remoteSdpAnswer.sdpLen = (unsigned short)remoteSdpAnswerStr.size();
   remoteSdpAnswer.sdpString = remoteSdpAnswerStr;

   if (conversation != -1)
   {
      SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);
      SipConversationManagerInternal* sipConvManagerInternal = dynamic_cast<SipConversationManagerInternal*>(sipConvManager);
      sipConvManagerInternal->provideSdpAnswer(conversation, remoteSdpOffer, remoteSdpAnswer);
   }
   return kSuccess;
}

int SipConversationJsonServerInterface::handleRequestStateAllConversations(const rapidjson::Value& functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);
   SipConversationStateManager* convStateMgr = SipConversationStateManager::getInterface(sipConvManager);
   cpc::vector<SipConversationState> conversationStateArray;
   convStateMgr->getStateAllConversations(conversationStateArray);

   JsonFunctionCall(mTransport, "onConversationState", JSON_VALUE(conversationStateArray));
   return kSuccess;
}

int SipConversationJsonServerInterface::handleJoinRemoteConversation(const rapidjson::Value& functionObjectVal)
{
   SipConversationManager* sipConvManager = SipConversationManager::getInterface(mPhone);
   SipConversationManagerInternal* sipConvManagerInternal = dynamic_cast<SipConversationManagerInternal*>(sipConvManager);

   SipConversationHandle conversation = 0;
   RemoteMediaMode remoteMediaMode = RemoteMediaMode_Relay;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(remoteMediaMode));

   if (remoteMediaMode == RemoteMediaMode_Bypass || remoteMediaMode == RemoteMediaMode_Relay)
   {
      sipConvManagerInternal->setExternalSdpHandlingEnabled(conversation, true);
   }
   return kSuccess;
}

int SipConversationJsonServerInterface::handleJoinRemoteConversationFullMediaStack()
{
   //PeerConnectionSettings aliceSettings;
   //aliceSettings.certAor = "<EMAIL>";
   //aliceSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_Auto;
   ////aliceSettings.natTraversalServerHostname = "stun.counterpath.com";
   ////aliceSettings.natTraversalServerPort = 3478;
   ////aliceSettings.natTraversalServerType = PeerConnectionSettings::NatTraversalServerType_StunOnly;
   //aliceSettings.secureMediaMode = PeerConnectionSettings::SecureMediaMode_DTLS;
   //aliceSettings.secureMediaRequired = true;
   //aliceSettings.sessionName = "CPCAPI2-SipConversationJsonServerInterface";

   //cpc::string sessionId(resip::Random::getCryptoRandomBase64(8).c_str());
   //mMapPeerConnections[sessionId].sessionId = sessionId;

   //CPCAPI2::PeerConnection::PeerConnectionHandle pc = mPeerConnMgr->createPeerConnection();
   //mMapPeerConnections[sessionId].pc = pc;
   //mPeerConnMgr->setHandler(pc, (PeerConnectionHandler*)0xDEADBEEF);
   //mPeerConnMgr->setDefaultSettings(pc, aliceSettings);
   //CPCAPI2::PeerConnection::MediaInfo miAudio;
   //miAudio.mediaDirection = CPCAPI2::PeerConnection::MediaDirection_SendRecv;
   //miAudio.mediaType = CPCAPI2::PeerConnection::MediaType_Audio;
   //miAudio.mediaEncryptionOptions.mediaEncryptionMode = CPCAPI2::PeerConnection::MediaEncryptionMode_Unencrypted;
   //mPeerConnMgr->configureMedia(pc, miAudio);
   //mPeerConnMgr->createOffer(pc);
   return kSuccess;
}

std::map<cpc::string, SipConversationJsonServerInterface::PeerConnectionInfo>::iterator SipConversationJsonServerInterface::findPeerConnectionInfo(CPCAPI2::PeerConnection::PeerConnectionHandle pc)
{
   std::map<cpc::string, PeerConnectionInfo>::iterator itPcInfo = mMapPeerConnections.begin();
   for (; itPcInfo != mMapPeerConnections.end(); ++itPcInfo)
   {
      if (itPcInfo->second.pc == pc)
      {
         return itPcInfo;
      }
   }
   return mMapPeerConnections.end();
}

// PeerConnectionHandler
int SipConversationJsonServerInterface::onSignalingStateChange(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SignalingStateChangeEvent& args)
{
   return kSuccess;
}

int SipConversationJsonServerInterface::onCreateOfferResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateOfferResult& args)
{
   std::map<cpc::string, PeerConnectionInfo>::iterator itPcInfo = findPeerConnectionInfo(pc);
   if (itPcInfo != mMapPeerConnections.end())
   {
      mPeerConnMgr->setLocalDescription(pc, args.sdp);
      JsonFunctionCall(mTransport, "peerConnectionOffer", "sessionId", itPcInfo->first, "sdpOffer", args.sdp.sdpString);
      InfoLog(<< "remote SDK offer: " << resip::Data(resip::Data::Share, args.sdp.sdpString, args.sdp.sdpLen));
   }
   return kSuccess;
}

int SipConversationJsonServerInterface::onCreateAnswerResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args)
{
   std::map<cpc::string, PeerConnectionInfo>::iterator itPcInfo = findPeerConnectionInfo(pc);
   if (itPcInfo != mMapPeerConnections.end())
   {
      mPeerConnMgr->setLocalDescription(pc, args.sdp);

      InfoLog(<< "bob answer: " << resip::Data(args.sdp.sdpString, args.sdp.sdpLen));

      JsonFunctionCall(mTransport, "peerConnectionAnswer", "sessionId", itPcInfo->first, "sdpAnswer", args.sdp.sdpString);
   }
   return kSuccess;
}

int SipConversationJsonServerInterface::onSetLocalSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetLocalSessionDescriptionResult& args)
{
   return kSuccess;
}

int SipConversationJsonServerInterface::onSetRemoteSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetRemoteSessionDescriptionResult& args)
{
   return kSuccess;
}

int SipConversationJsonServerInterface::onError(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::ErrorEvent& args)
{
   return kSuccess;
}

}

}

#endif
