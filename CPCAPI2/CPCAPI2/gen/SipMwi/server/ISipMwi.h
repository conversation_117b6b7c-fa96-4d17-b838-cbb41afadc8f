#include "interface/experimental/jsonrpc/StringSerialization.h"
#include "interface/experimental/jsonrpc/OptionalSerialization.h"
#include "interface/experimental/jsonrpc/VectorSerialization.h"


#ifndef JSONRPC_CPCAPI2_SIPMWI_ISIPMWI_H
#define JSONRPC_CPCAPI2_SIPMWI_ISIPMWI_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipMwi {
class ISipMwi
{
public:
    virtual void registerForEventQueue(const int64_t phoneHandle, const int64_t accountHandle) = 0;

	virtual void unRegisterForEventQueue(const int64_t phoneHandle, const int64_t accountHandle) = 0;

	virtual int64_t createSubscription(const int64_t phoneHandle, const int64_t accountHandle) = 0;

	virtual void start(const int64_t phoneHandle, const int64_t mwiSubscriptionHandle) = 0;

	virtual void end(const int64_t phoneHandle, const int64_t mwiSubscriptionHandle) = 0;
}; // class SipMwi
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipMwi
#endif // JSONRPC_CPCAPI2_SIPMWI_ISIPMWI_H
