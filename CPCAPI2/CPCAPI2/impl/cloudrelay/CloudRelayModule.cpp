#include "brand_branded.h"

#include "interface/experimental/cloudrelay/CloudRelayManager.h"

#if (CPCAPI2_BRAND_CLOUD_RELAY_MODULE == 1)
#include "../phone/PhoneInterface.h"
#include "CloudRelayManagerInterface.h"
#endif

namespace CPCAPI2
{
namespace CloudRelay
{
CloudRelayManager* CloudRelayManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_CLOUD_RELAY_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<CloudRelayManagerInterface>(phone, "CloudRelayManagerInterface");
#else
   return NULL;
#endif
}

const cpc::string& CloudRelayManager::getServiceId()
{
   static cpc::string serviceId = "cloudrelay";
   return serviceId;
}

}
}
