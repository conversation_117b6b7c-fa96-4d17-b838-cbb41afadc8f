#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOSETCODECPRIORITY_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOSETCODECPRIORITY_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
  class AudioSetCodecPriority
  {
  public:
    int32_t phoneHandle;
    int32_t codecId;
    int32_t priority;

    AudioSetCodecPriority() = default;

    AudioSetCodecPriority(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const AudioSetCodecPriority& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["codecId"] = o.codecId;
     j["priority"] = o.priority;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, AudioSetCodecPriority& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.codecId = j.at("codecId");
     o.priority = j.at("priority");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOSETCODECPRIORITY_H
