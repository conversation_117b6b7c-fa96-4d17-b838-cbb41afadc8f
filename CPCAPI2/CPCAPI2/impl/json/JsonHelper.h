#pragma once

#ifndef JSON_HELPER_H
#define JSON_HELPER_H

// Documentation at https://intranet.counterpath.com/content/31795

#include "json/JsonData.h"
#include "JsonDeserialize.h"
#include "JsonSerialize.h"
#include "JsonFunctionSerialize.h"

#include <writer.h> // rapidjson

#include <memory>

#define CPCAPI2_JSON_NO_REQUEST_ID (-1)

namespace CPCAPI2
{
   namespace Json
   {
      template <typename Encoding>
      struct StdStringBuffer_Enc {
         typedef typename Encoding::Ch Ch;

         StdStringBuffer_Enc(std::string& cpstr) : cpstr_(cpstr) { cpstr_.reserve(1024); }

         void Put(Ch c) { cpstr_.append(1, c); }
         void PutUnsafe(Ch c) { cpstr_.append(1, c); }
         void Flush() {}

         void Reserve(size_t count) { cpstr_.reserve(count); }

         void Clear() { cpstr_.clear(); }

         const Ch* GetString() const { return cpstr_.c_str(); }
         size_t GetSize() const { return cpstr_.size(); }

         std::string& cpstr_;
      };

      typedef StdStringBuffer_Enc< rapidjson::UTF8<> > StdStringBuffer;
      typedef rapidjson::Writer<StdStringBuffer> StdStringWriter;
   } // namespace Json
} // CPCPAPI2

#endif
