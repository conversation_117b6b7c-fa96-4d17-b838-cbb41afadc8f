#include "../datatypes/SipAccountCreate.h"
#include "../datatypes/SipAccountDestroy.h"
#include "../datatypes/SipAccountConfigureDefaultAccountSettings.h"
#include "../datatypes/SipAccountSettings.h"
#include "../datatypes/SipAccountConfigureTransportAccountSettings.h"
#include "../datatypes/SipAccountNetworkTransport.h"
#include "../datatypes/SipAccountApplySettings.h"
#include "../datatypes/SipAccountEnable.h"
#include "../datatypes/SipAccountDisable.h"

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
int64_t SipAccount::create(int64_t phoneHandle)
{
    SipAccountCreate params = new SipAccountCreate(phoneHandle);
    nlohmann::json j = this.transport.request("create", params.marshal());
    return j.get<int64_t>();
};

void SipAccount::destroy(int64_t phoneHandle, int64_t sipAccountHandle)
{
    SipAccountDestroy params = new SipAccountDestroy(phoneHandle, sipAccountHandle);
    nlohmann::json j = this.transport.request("destroy", params.marshal());
    
};

void SipAccount::configureDefaultAccountSettings(int64_t phoneHandle, int64_t sipAccountHandle, SipAccountSettings sipAccountSettings)
{
    SipAccountConfigureDefaultAccountSettings params = new SipAccountConfigureDefaultAccountSettings(phoneHandle, sipAccountHandle, sipAccountSettings);
    nlohmann::json j = this.transport.request("configureDefaultAccountSettings", params.marshal());
    
};

void SipAccount::configureTransportAccountSettings(int64_t phoneHandle, int64_t sipAccountHandle, SipAccountSettings sipAccountSettings, SipAccountNetworkTransport transport)
{
    SipAccountConfigureTransportAccountSettings params = new SipAccountConfigureTransportAccountSettings(phoneHandle, sipAccountHandle, sipAccountSettings, transport);
    nlohmann::json j = this.transport.request("configureTransportAccountSettings", params.marshal());
    
};

void SipAccount::applySettings(int64_t phoneHandle, int64_t sipAccountHandle)
{
    SipAccountApplySettings params = new SipAccountApplySettings(phoneHandle, sipAccountHandle);
    nlohmann::json j = this.transport.request("applySettings", params.marshal());
    
};

void SipAccount::enable(int64_t phoneHandle, int64_t sipAccountHandle)
{
    SipAccountEnable params = new SipAccountEnable(phoneHandle, sipAccountHandle);
    nlohmann::json j = this.transport.request("enable", params.marshal());
    
};

void SipAccount::disable(int64_t phoneHandle, int64_t sipAccountHandle)
{
    SipAccountDisable params = new SipAccountDisable(phoneHandle, sipAccountHandle);
    nlohmann::json j = this.transport.request("disable", params.marshal());
    
};
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount