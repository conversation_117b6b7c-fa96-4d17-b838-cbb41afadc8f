#pragma once

#if !defined(__CPCAPI2_SIP_DIALOG_EVENT_SUBSCRIPTION_MANAGER_INTERFACE_H__)
#define __CPCAPI2_SIP_DIALOG_EVENT_SUBSCRIPTION_MANAGER_INTERFACE_H__

#include "cpcapi2defs.h"
#include "../phone/PhoneModule.h"
#include "../account/SipAccountInterface.h"
#include "../event/SipEventManagerInterface.h"
#include "dialogevent/SipDialogEventSubscriptionManager.h"
#include "dialogevent/SipDialogEventSubscriptionHandler.h"

namespace CPCAPI2
{
class Phone;

namespace SipDialogEvent
{
class SipDialogEventSubscriptionManagerImpl;

class SipDialogEventSubscriptionManagerInterface : public CPCAPI2::EventSource2< CPCAPI2::EventHandler<SipDialogEventSubscriptionHandler, SipAccount::SipAccountHandle> >,
                                                   public SipDialogEventSubscriptionManager,
                                                   public PhoneModule
{
public:
   SipDialogEventSubscriptionManagerInterface(Phone* phone);
   virtual ~SipDialogEventSubscriptionManagerInterface();

   FORWARD_EVENT_PROCESSOR(SipDialogEventSubscriptionManagerInterface);

   // SipDialogEventSubscriptionManager interface
   virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipDialogEventSubscriptionHandler* handler) OVERRIDE;
   virtual SipDialogEventSubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;
   virtual SipDialogEventSubscriptionHandle createSubscriptionImpl(CPCAPI2::SipAccount::SipAccountHandle account);
   virtual int recreateSubscription(CPCAPI2::SipAccount::SipAccountHandle account, SipDialogEventSubscriptionHandle h) OVERRIDE;
   virtual int applySubscriptionSettings(SipDialogEventSubscriptionHandle subscription, const SipDialogEventSubscriptionSettings& settings) OVERRIDE;
   int applySubscriptionSettingsImpl(SipDialogEventSubscriptionHandle subscription, const SipDialogEventSubscriptionSettings& settings);
   virtual int addParticipant(SipDialogEventSubscriptionHandle subscription, const cpc::string& targetAddress) OVERRIDE;
   int addParticipantImpl(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const cpc::string& targetAddress, bool isEventServer = false);
   virtual int setEventServer(SipDialogEventSubscriptionHandle subscription, const cpc::string& targetAddress) OVERRIDE;
   virtual int start(SipDialogEventSubscriptionHandle subscription) OVERRIDE;
   int startImpl(SipDialogEventSubscriptionHandle subscription);
   virtual int end(SipDialogEventSubscriptionHandle subscription) OVERRIDE;
   virtual int reject(SipDialogEventSubscriptionHandle subscription, unsigned int rejectReason) OVERRIDE;
   virtual int accept(SipDialogEventSubscriptionHandle subscription) OVERRIDE;
   virtual int notify(SipDialogEventSubscriptionHandle subscription, const DialogInfoDocument& dialogInfoDoc);
   int endImpl(SipDialogEventSubscriptionHandle subscription);
   int setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipDialogEventSubscriptionHandler* handler);

private:
   CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;
   typedef std::map<SipAccount::SipAccountHandle, SipDialogEventSubscriptionManagerImpl*> AccountMap;
   std::shared_ptr<AccountMap> mAccountMapPtr;
   AccountMap& mAccountMap;
   std::map<CPCAPI2::SipAccount::SipAccountHandle, SipDialogEventSubscriptionHandler*> mHandlers;
   CPCAPI2::SipEvent::SipEventManagerInterface* mSipEventIf;
   PhoneInterface* mPhone;

   // PhoneModule interface
   virtual void Release() OVERRIDE;

   
   int acceptImpl(SipDialogEventSubscriptionHandle subscription);
   int notifyImpl(SipDialogEventSubscriptionHandle subscription, const DialogInfoDocument& dialogInfoDoc);

   SipDialogEventSubscriptionManagerImpl* getManagerForAccount(CPCAPI2::SipAccount::SipAccountHandle account);
   SipDialogEventSubscriptionManagerImpl* getManagerForSubscription(SipDialogEventSubscriptionHandle subscription);
};

std::ostream& operator<<(std::ostream& os, const NewDialogEventSubscriptionEvent& evt);
std::ostream& operator<<(std::ostream& os, const DialogEventSubscriptionEndedEvent& evt);
std::ostream& operator<<(std::ostream& os, const DialogEventSubscriptionStateChangedEvent& evt);
std::ostream& operator<<(std::ostream& os, const IncomingDialogInfoEvent& evt);
std::ostream& operator<<(std::ostream& os, const DialogResourceListEvent& evt);
std::ostream& operator<<(std::ostream& os, const NotifyDialogInfoFailureEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipDialogEvent::ErrorEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipDialogEvent::DialogInfo& di);
std::ostream& operator<<(std::ostream& os, const SipDialogEvent::ParticipantInfo& pi);
std::ostream& operator<<(std::ostream& os, const SipDialogEvent::IdentityInfo& ii);
std::ostream& operator<<(std::ostream& os, const SipDialogEvent::TargetInfo& ti);
std::ostream& operator<<(std::ostream& os, const Parameter& p);

}
}

#endif // __CPCAPI2_SIP_DIALOG_EVENT_SUBSCRIPTION_MANAGER_INTERFACE_H__
