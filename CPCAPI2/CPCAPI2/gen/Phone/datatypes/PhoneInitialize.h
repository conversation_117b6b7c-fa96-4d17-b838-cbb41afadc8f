#include "jsonrpc/StringSerialization.h"
#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>
#include "LicenseInfo.h"
#include "SslCipherOptions.h"

#ifndef JSONRPC_CPCAPI2_PHONE_PHONEINITIALIZE_H
#define JSONRPC_CPCAPI2_PHONE_PHONEINITIALIZE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  class PhoneInitialize
  {
  public:
    int32_t handle;
    LicenseInfo licenseInfo;
    bool useNetworkChangeManager;
    std::optional<SslCipherOptions> ciphers;

    PhoneInitialize() = default;

    PhoneInitialize(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const PhoneInitialize& o)
    {
      j = nlohmann::json::object();
       j["handle"] = o.handle;
     j["licenseInfo"] = o.licenseInfo;
     j["useNetworkChangeManager"] = o.useNetworkChangeManager;
     if (o.ciphers) { j["ciphers"] = *(o.ciphers); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, PhoneInitialize& o)
    {
     o.handle = j.at("handle");
     o.licenseInfo = j.at("licenseInfo");
     o.useNetworkChangeManager = j.at("useNetworkChangeManager");
     if (j.contains("ciphers")) { o.ciphers = j.at("ciphers"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_PHONEINITIALIZE_H
