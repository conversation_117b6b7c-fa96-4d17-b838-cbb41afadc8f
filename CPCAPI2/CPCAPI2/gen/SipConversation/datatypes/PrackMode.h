#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_PRACKMODE_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_PRACKMODE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  enum class PrackMode
  {
    Disabled,
    Supported,
    Required,
    SupportUasAndUac
  };

  inline std::string to_string(PrackMode value)
  {
    switch (value)
    {
      case PrackMode::Disabled:
        return "Disabled";
      case PrackMode::Supported:
        return "Supported";
      case PrackMode::Required:
        return "Required";
      case PrackMode::SupportUasAndUac:
        return "SupportUasAndUac";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, PrackMode value)
  {
    switch (value)
    {
      case PrackMode::Disabled:
        os << "Disabled";
        break;
      case PrackMode::Supported:
        os << "Supported";
        break;
      case PrackMode::Required:
        os << "Required";
        break;
      case PrackMode::SupportUasAndUac:
        os << "SupportUasAndUac";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const PrackMode& e)
  {
    switch (e)
    {
      case PrackMode::Disabled:
        j = "Disabled";
        break;
      case PrackMode::Supported:
        j = "Supported";
        break;
      case PrackMode::Required:
        j = "Required";
        break;
      case PrackMode::SupportUasAndUac:
        j = "SupportUasAndUac";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, PrackMode& e)
  {
      if (0 == j.get<std::string>().compare("Disabled"))
    {
      e = PrackMode::Disabled;
    }
    else if (0 == j.get<std::string>().compare("Supported"))
    {
      e = PrackMode::Supported;
    }
    else if (0 == j.get<std::string>().compare("Required"))
    {
      e = PrackMode::Required;
    }
    else if (0 == j.get<std::string>().compare("SupportUasAndUac"))
    {
      e = PrackMode::SupportUasAndUac;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum PrackMode"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_PRACKMODE_H
