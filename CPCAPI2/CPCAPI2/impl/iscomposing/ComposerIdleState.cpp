#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_CHAT_MODULE==1 || CPCAPI2_BRAND_IM_MODULE==1 || CPCAPI2_BRAND_XMPP_CHAT_MODULE==1 || CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
#include "ComposerIdleState.h"
#include "ComposerActiveState.h"
#include "IsComposingManager.h"
#include "IsComposingInfo.h"

namespace CPCAPI2
{
namespace IsComposing
{

ComposerIdleState ComposerIdleState::instance;

void ComposerIdleState::onIsComposingMessage(IsComposingManager* manager, IsComposingInfo* info)
{
   if (info->composerIdleInterval == 0) return;

   // Send a notification with the Active state
   manager->sendIsComposingMessageNotification(info, IsComposingMessageState_Active, info->composerContentType, info->composerRefreshInterval, info->composerLastActive);

   // Move to the Active state
   manager->transitionTo(info, &ComposerActiveState::instance);
}

void ComposerIdleState::onMessageSent(IsComposingManager* manager, IsComposingInfo* info)
{
   // Don't send any notification
   // Stay in this state
}

}
}
#endif
