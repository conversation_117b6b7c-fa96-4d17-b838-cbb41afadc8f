// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef XmppIMCommand_JSON_HELPER_H
#define XmppIMCommand_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/public/xmpp/XmppIMCommandManager.h>
#include <interface/public/xmpp/XmppIMCommandHandler.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppIMCommand::ChatIMCommandReceivedEvent& v)
    {
      w.StartObject();
      Write(w, "remote", v.remote);
      Write(w, "type", v.type);
      Write(w, "payload", v.payload);
      Write(w, "timestamp", v.timestamp);
      Write(w, "millisecond", v.millisecond);
      Write(w, "isDelayedDelivery", v.isDelayedDelivery);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppIMCommand::ChatIMCommandReceivedEvent& v)
    {
      Read(json, "remote", v.remote);
      Read(json, "type", v.type);
      Read(json, "payload", v.payload);
      Read(json, "timestamp", v.timestamp);
      Read(json, "millisecond", v.millisecond);
      Read(json, "isDelayedDelivery", v.isDelayedDelivery);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppIMCommand::MultiUserChatIMCommandReceivedEvent& v)
    {
      w.StartObject();
      Write(w, "remote", v.remote);
      Write(w, "nickname", v.nickname);
      Write(w, "type", v.type);
      Write(w, "payload", v.payload);
      Write(w, "timestamp", v.timestamp);
      Write(w, "millisecond", v.millisecond);
      Write(w, "isDelayedDelivery", v.isDelayedDelivery);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppIMCommand::MultiUserChatIMCommandReceivedEvent& v)
    {
      Read(json, "remote", v.remote);
      Read(json, "nickname", v.nickname);
      Read(json, "type", v.type);
      Read(json, "payload", v.payload);
      Read(json, "timestamp", v.timestamp);
      Read(json, "millisecond", v.millisecond);
      Read(json, "isDelayedDelivery", v.isDelayedDelivery);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppIMCommand::ChatIMCommandSentEvent& v)
    {
      w.StartObject();
      Write(w, "message", v.message);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppIMCommand::ChatIMCommandSentEvent& v)
    {
      Read(json, "message", v.message);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppIMCommand::MultiUserChatIMCommandSentEvent& v)
    {
      w.StartObject();
      Write(w, "message", v.message);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppIMCommand::MultiUserChatIMCommandSentEvent& v)
    {
      Read(json, "message", v.message);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppIMCommand::XmppChatIMCommandHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppIMCommand::XmppChatIMCommandHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppIMCommand::XmppMultiUserChatIMCommandHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppIMCommand::XmppMultiUserChatIMCommandHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppIMCommand::XmppIMCommandManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppIMCommand::XmppIMCommandManager& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // XmppIMCommand_JSON_HELPER_H