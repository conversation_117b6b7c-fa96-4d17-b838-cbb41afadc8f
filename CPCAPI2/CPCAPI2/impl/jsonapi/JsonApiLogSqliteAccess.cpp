#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)

#include "JsonApiLogSqliteAccess.h"

#include <webrtc/system_wrappers/interface/clock.h>

#include <soci/soci.h>
#include <soci/sqlite3/soci-sqlite3.h>

#include <future>

#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER

using namespace soci;

namespace CPCAPI2
{

namespace JsonApi
{

   JsonApiLogSqliteAccess::JsonApiLogSqliteAccess() :
mReactor("JsonApiLogSqliteAccess")
{
   mReactor.start();
}

   JsonApiLogSqliteAccess::~JsonApiLogSqliteAccess()
{
}

void JsonApiLogSqliteAccess::createTables()
{
   (*mSOCI) <<
      "CREATE TABLE IF NOT EXISTS JsonApiLog ( "
      " id INTEGER PRIMARY KEY, "
      " userContext TEXT, "
      " moduleName TEXT, "
      " functionName TEXT, "
      " jsonFunctionObject TEXT, "
      " invocationTime DATETIME"
      " )";
}

int JsonApiLogSqliteAccess::initialize(const JsonApiLogDbAccessConfig& serverConfig, const std::function<void(bool, const cpc::string&)>& initCb)
{
   mReactor.post(resip::resip_bind(&JsonApiLogSqliteAccess::initializeImpl, this, serverConfig, initCb));
   return 0;
}

void JsonApiLogSqliteAccess::initializeImpl(const JsonApiLogDbAccessConfig& serverConfig, const std::function<void(bool, const cpc::string&)>& initCb)
{
   bool success = true;
   cpc::string statusDesc = "OK";

   try
   {
      mSOCI.reset(new soci::session());
      mSOCI->open(soci::sqlite3, serverConfig.dbFileName);

      // std::ostream* os = new std::ostream(NULL);
      // os->rdbuf(std::cout.rdbuf()); // uses cout's buffer
      // mSOCI->set_log_stream(os);

      mSOCI->begin();

      if (serverConfig.wipeOnRun)
      {
         flushAllImpl();
      }
      createTables();

      mSOCI->commit();
   }
   catch (soci::soci_error&)
   {
      success = false;
      statusDesc = "Error";
      mSOCI->close();
   }
}

void JsonApiLogSqliteAccess::shutdown()
{
   mReactor.stop();
}

int JsonApiLogSqliteAccess::addApiLog(const AddApiLogArgs& apiLogInfo, const std::function<void(bool, const AddApiLogResult&)>& apiLogAddedCb)
{
   mReactor.post(resip::resip_bind(&JsonApiLogSqliteAccess::addApiLogImpl, this, apiLogInfo, apiLogAddedCb));
   return 0;
}

void JsonApiLogSqliteAccess::addApiLogImpl(const AddApiLogArgs& apiLogInfo, const std::function<void(bool, const AddApiLogResult&)>& apiLogAddedCb)
{
   //int64_t lastUpdateTimeMs = webrtc::Clock::GetRealTimeClock()->CurrentNtpInMilliseconds();
   if (apiLogInfo.userContext.size() > 1)
   {
      try
      {
         const std::string userContext(apiLogInfo.userContext);
         const std::string moduleName(apiLogInfo.moduleName);
         const std::string functionName(apiLogInfo.functionName);
         const std::string jsonFunctionObject(apiLogInfo.jsonFunctionObject);

         // warning: don't use temporary variables for inserting.
         // from http://soci.sourceforge.net/doc/3.2/exchange.html
         // Object lifetime and immutability:
         //
         // SOCI assumes that local variables provided as use elements live at least as long at it takes to execute the whole statement.
         // In short statement forms like above, the statement is executed sometime at the end of the full expression and the whole process
         // is driven by the invisible temporary object handled by the library. If the data provided by user comes from another temporary
         // variable, it might be possible for the compiler to arrange them in a way that the user data will be destroyed before the statement
         // will have its chance to execute, referencing objects that no longer exist:

         (*mSOCI) << "INSERT OR REPLACE INTO JsonApiLog(userContext, moduleName, functionName, jsonFunctionObject, invocationTime) VALUES(:userContext, :moduleName, :functionName, :jsonFunctionObject, :invocationTime)",
            use(userContext),
            use(moduleName),
            use(functionName),
            use(jsonFunctionObject),
            use(apiLogInfo.invocationTime);

         AddApiLogResult res;
         res.result = 0;
         apiLogAddedCb(true, res);
      }
      catch (std::exception const& exc)
      {
      }
      return;
   }

   StackLog(<< "SqliteAccess::addApiLogImpl(): error inserting data");

   AddApiLogResult res;
   res.result = -1;
   apiLogAddedCb(true, res);
}

int JsonApiLogSqliteAccess::queryApiLog(const QueryApiLogArgs& queryArgs, const std::function<void(bool, const QueryApiLogResult&)>& queryApiLogResultCb)
{
   mReactor.post(resip::resip_bind(&JsonApiLogSqliteAccess::queryApiLogImpl, this, queryArgs, queryApiLogResultCb));
   return 0;
}

void JsonApiLogSqliteAccess::queryApiLogImpl(const QueryApiLogArgs& queryArgs, const std::function<void(bool, const QueryApiLogResult&)>& queryApiLogResultCb)
{
   QueryApiLogResult res;
   res.result = -1;

   soci::indicator isqlid;
   soci::indicator iusercontext;
   soci::indicator imodulename;
   soci::indicator ifunctionname;
   soci::indicator ijsonfunctionobject;
   soci::indicator iinvocationtime;
   int64_t sqlid = -1;
   std::string userContext;
   std::string moduleName;
   std::string functionName;
   std::string jsonFunctionObject;
   int64_t invocationTime = -1;

   (*mSOCI) << "SELECT id, userContext, moduleName, functionName, jsonFunctionObject, invocationTime FROM JsonApiLog", // WHERE userContext = :userContext",
      into(sqlid, isqlid),
      into(userContext, iusercontext),
      into(moduleName, imodulename),
      into(functionName, ifunctionname),
      into(jsonFunctionObject, ijsonfunctionobject),
      into(invocationTime, iinvocationtime);
      //use(userDeviceId, "userDeviceId");

   if (mSOCI->got_data())
   {
      if (isqlid == soci::i_ok)
      {
         res.result = 0;
      }
   }
   else
   {
      DebugLog(<< "SqliteAccess::queryApiLogImpl(): query failed: " << mSOCI->get_last_query());
   }

   queryApiLogResultCb(true, res);

}

int JsonApiLogSqliteAccess::flushAll()
{
   mReactor.post(resip::resip_bind(&JsonApiLogSqliteAccess::flushAllImpl, this));
   return 0;
}

void JsonApiLogSqliteAccess::flushAllImpl()
{
   (*mSOCI) << "DROP TABLE IF EXISTS JsonApiLog";
}

void JsonApiLogSqliteAccess::dump()
{
   // int count;
   // (*mSOCI) << "SELECT count(*) FROM JsonApiLog", into(count);

   soci::rowset<soci::row> rows = ((*mSOCI).prepare << "SELECT * FROM JsonApiLog");

   int count(0);
   std::stringstream dump;
   dump << "<JsonApiLog>" << std::endl;
   for (soci::rowset<soci::row>::const_iterator it = rows.begin(); it != rows.end(); ++it, ++count)
   {
      dump << "<row>" << std::endl;
      soci::row const& row = (*it);
      for (std::size_t i = 0; i != row.size(); ++i)
      {
         const column_properties& props = row.get_properties(i);

         dump << '<' << props.get_name() << '>';

         switch(props.get_data_type())
         {
            case dt_string: dump << row.get<std::string>(i); break;
            case dt_double: dump << row.get<double>(i); break;
            case dt_integer: dump << row.get<int>(i); break;
            case dt_long_long: dump << row.get<long long>(i); break;
            case dt_unsigned_long_long: dump << row.get<unsigned long long>(i); break;
            case dt_xml: dump << row.get<std::string>(i); break;
            case dt_blob: break;
            case dt_date: std::tm when = row.get<std::tm>(i); dump << asctime(&when); break;
         }

         dump << "</" << props.get_name() << '>' << std::endl;
      }
      dump << "</row>" << std::endl;
   }
   dump << "</JsonApiLog>";

   StackLog(<< "SqliteAccess::dump(): table rows: " << count << std::endl << dump.str() << std::endl);
}

}

}

#endif
