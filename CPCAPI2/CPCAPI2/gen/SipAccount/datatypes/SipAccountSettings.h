#include "jsonrpc/StringSerialization.h"
#include "jsonrpc/OptionalSerialization.h"
#include <any>
#include "jsonrpc/VectorSerialization.h"
#include <nlohmann/json.hpp>
#include "SipAccountTransportType.h"
#include "SipAccountSessionTimerMode.h"
#include "StunServerSourceType.h"
#include "IpVersion.h"
#include "SslVersion.h"
#include "SipResponseType.h"
#include "TunnelConfig.h"
#include "SipParameterType.h"
#include "KeepAliveMode.h"
#include "TransportHoldover.h"

#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTSETTINGS_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTSETTINGS_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  class SipAccountSettings
  {
  public:
    std::optional<cpc::string> mUsername;
    std::optional<cpc::string> mDomain;
    std::optional<cpc::string> mPassword;
    std::optional<cpc::string> mDisplayName;
    std::optional<cpc::string> mAuthUsername;
    std::optional<cpc::string> mAuthRealm;
    std::optional<bool> mUseRegistrar;
    std::optional<cpc::string> mOutboundProxy;
    std::optional<bool> mAlwaysRouteViaOutboundProxy;
    std::optional<int32_t> mRegistrationIntervalSeconds;
    std::optional<int32_t> mMinimumRegistrationIntervalSeconds;
    std::optional<int32_t> mMaximumRegistrationIntervalSeconds;
    std::optional<bool> mUseRport;
    std::optional<SipAccountTransportType> mSipTransportType;
    std::optional<bool> mExcludeEncryptedTransports;
    std::optional<cpc::string> mUserAgent;
    std::optional<int32_t> mUdpKeepAliveTime;
    std::optional<int32_t> mTcpKeepAliveTime;
    std::optional<bool> mUseOutbound;
    std::optional<bool> mUseGruu;
    std::optional<cpc::string> mOtherNonEscapedCharsInUri;
    std::optional<cpc::vector<cpc::string>> mNameServers;
    std::optional<cpc::vector<cpc::string>> mAdditionalNameServers;
    std::optional<SipAccountSessionTimerMode> mSessionTimerMode;
    std::optional<int32_t> mSessionTimeSeconds;
    std::optional<StunServerSourceType> mStunServerSource;
    std::optional<cpc::string> mStunServer;
    std::optional<bool> mIgnoreCertVerification;
    std::optional<cpc::vector<cpc::string>> mAdditionalCertPeerNames;
    std::optional<cpc::vector<cpc::string>> mAcceptedCertPublicKeys;
    std::optional<cpc::vector<cpc::string>> mRequiredCertPublicKeys;
    std::optional<int32_t> mSipQosSettings;
    std::optional<bool> mUseImsAuthHeader;
    std::optional<int32_t> mMinSipPort;
    std::optional<int32_t> mMaxSipPort;
    std::optional<int32_t> mDefaultSipPort;
    std::optional<int32_t> mDefaultSipsPort;
    std::optional<bool> mUseMethodParamInReferTo;
    std::optional<bool> mUseInstanceId;
    std::optional<bool> mAnswerModeSupported;
    std::optional<IpVersion> mIpVersion;
    std::optional<SslVersion> mSslVersion;
    std::optional<cpc::string> mCipherSuite;
    std::optional<bool> mEnableLegacyServerConnect;
    std::optional<cpc::vector<SipResponseType>> mReRegisterOnResponseTypes;
    std::optional<bool> mEnableRegeventDeregistration;
    std::optional<bool> mEnableDnsResetOnRegistrationRefresh;
    std::optional<bool> mEnableAuthResetUponDnsReset;
    std::optional<cpc::string> mXcapRoot;
    std::optional<TunnelConfig> mTunnelConfig;
    std::optional<cpc::vector<SipParameterType>> mCapabilities;
    std::optional<cpc::vector<SipParameterType>> mAdditionalFromParameters;
    std::optional<cpc::string> mSourceAddress;
    std::optional<bool> mPreferPAssertedIdentity;
    std::optional<bool> mAutoRetryOnTransportDisconnect;
    std::optional<KeepAliveMode> mKeepAliveMode;
    std::optional<bool> mUseRinstance;
    std::optional<bool> mEnableNat64Support;
    std::optional<bool> mUsePrivacyHeaderOnlyForAnonymous;
    std::optional<TransportHoldover> mTransportHoldover;
    std::optional<bool> mUseOptionsPing;
    std::optional<int32_t> mOptionsPingInterval;
    std::optional<cpc::string> mUserCertificatePem;
    std::optional<cpc::string> mUserPrivateKeyPem;
    std::optional<bool> mForceListenSocket;
    std::optional<cpc::string> mLocalGroup;
    std::optional<int32_t> mOverrideMsecsTimerF;

    SipAccountSettings() = default;

    SipAccountSettings(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipAccountSettings& o)
    {
      j = nlohmann::json::object();
       if (o.mUsername) { j["mUsername"] = *(o.mUsername); }
     if (o.mDomain) { j["mDomain"] = *(o.mDomain); }
     if (o.mPassword) { j["mPassword"] = *(o.mPassword); }
     if (o.mDisplayName) { j["mDisplayName"] = *(o.mDisplayName); }
     if (o.mAuthUsername) { j["mAuthUsername"] = *(o.mAuthUsername); }
     if (o.mAuthRealm) { j["mAuthRealm"] = *(o.mAuthRealm); }
     if (o.mUseRegistrar) { j["mUseRegistrar"] = *(o.mUseRegistrar); }
     if (o.mOutboundProxy) { j["mOutboundProxy"] = *(o.mOutboundProxy); }
     if (o.mAlwaysRouteViaOutboundProxy) { j["mAlwaysRouteViaOutboundProxy"] = *(o.mAlwaysRouteViaOutboundProxy); }
     if (o.mRegistrationIntervalSeconds) { j["mRegistrationIntervalSeconds"] = *(o.mRegistrationIntervalSeconds); }
     if (o.mMinimumRegistrationIntervalSeconds) { j["mMinimumRegistrationIntervalSeconds"] = *(o.mMinimumRegistrationIntervalSeconds); }
     if (o.mMaximumRegistrationIntervalSeconds) { j["mMaximumRegistrationIntervalSeconds"] = *(o.mMaximumRegistrationIntervalSeconds); }
     if (o.mUseRport) { j["mUseRport"] = *(o.mUseRport); }
     if (o.mSipTransportType) { j["mSipTransportType"] = *(o.mSipTransportType); }
     if (o.mExcludeEncryptedTransports) { j["mExcludeEncryptedTransports"] = *(o.mExcludeEncryptedTransports); }
     if (o.mUserAgent) { j["mUserAgent"] = *(o.mUserAgent); }
     if (o.mUdpKeepAliveTime) { j["mUdpKeepAliveTime"] = *(o.mUdpKeepAliveTime); }
     if (o.mTcpKeepAliveTime) { j["mTcpKeepAliveTime"] = *(o.mTcpKeepAliveTime); }
     if (o.mUseOutbound) { j["mUseOutbound"] = *(o.mUseOutbound); }
     if (o.mUseGruu) { j["mUseGruu"] = *(o.mUseGruu); }
     if (o.mOtherNonEscapedCharsInUri) { j["mOtherNonEscapedCharsInUri"] = *(o.mOtherNonEscapedCharsInUri); }
     if (o.mNameServers) { j["mNameServers"] = *(o.mNameServers); }
     if (o.mAdditionalNameServers) { j["mAdditionalNameServers"] = *(o.mAdditionalNameServers); }
     if (o.mSessionTimerMode) { j["mSessionTimerMode"] = *(o.mSessionTimerMode); }
     if (o.mSessionTimeSeconds) { j["mSessionTimeSeconds"] = *(o.mSessionTimeSeconds); }
     if (o.mStunServerSource) { j["mStunServerSource"] = *(o.mStunServerSource); }
     if (o.mStunServer) { j["mStunServer"] = *(o.mStunServer); }
     if (o.mIgnoreCertVerification) { j["mIgnoreCertVerification"] = *(o.mIgnoreCertVerification); }
     if (o.mAdditionalCertPeerNames) { j["mAdditionalCertPeerNames"] = *(o.mAdditionalCertPeerNames); }
     if (o.mAcceptedCertPublicKeys) { j["mAcceptedCertPublicKeys"] = *(o.mAcceptedCertPublicKeys); }
     if (o.mRequiredCertPublicKeys) { j["mRequiredCertPublicKeys"] = *(o.mRequiredCertPublicKeys); }
     if (o.mSipQosSettings) { j["mSipQosSettings"] = *(o.mSipQosSettings); }
     if (o.mUseImsAuthHeader) { j["mUseImsAuthHeader"] = *(o.mUseImsAuthHeader); }
     if (o.mMinSipPort) { j["mMinSipPort"] = *(o.mMinSipPort); }
     if (o.mMaxSipPort) { j["mMaxSipPort"] = *(o.mMaxSipPort); }
     if (o.mDefaultSipPort) { j["mDefaultSipPort"] = *(o.mDefaultSipPort); }
     if (o.mDefaultSipsPort) { j["mDefaultSipsPort"] = *(o.mDefaultSipsPort); }
     if (o.mUseMethodParamInReferTo) { j["mUseMethodParamInReferTo"] = *(o.mUseMethodParamInReferTo); }
     if (o.mUseInstanceId) { j["mUseInstanceId"] = *(o.mUseInstanceId); }
     if (o.mAnswerModeSupported) { j["mAnswerModeSupported"] = *(o.mAnswerModeSupported); }
     if (o.mIpVersion) { j["mIpVersion"] = *(o.mIpVersion); }
     if (o.mSslVersion) { j["mSslVersion"] = *(o.mSslVersion); }
     if (o.mCipherSuite) { j["mCipherSuite"] = *(o.mCipherSuite); }
     if (o.mEnableLegacyServerConnect) { j["mEnableLegacyServerConnect"] = *(o.mEnableLegacyServerConnect); }
     if (o.mReRegisterOnResponseTypes) { j["mReRegisterOnResponseTypes"] = *(o.mReRegisterOnResponseTypes); }
     if (o.mEnableRegeventDeregistration) { j["mEnableRegeventDeregistration"] = *(o.mEnableRegeventDeregistration); }
     if (o.mEnableDnsResetOnRegistrationRefresh) { j["mEnableDnsResetOnRegistrationRefresh"] = *(o.mEnableDnsResetOnRegistrationRefresh); }
     if (o.mEnableAuthResetUponDnsReset) { j["mEnableAuthResetUponDnsReset"] = *(o.mEnableAuthResetUponDnsReset); }
     if (o.mXcapRoot) { j["mXcapRoot"] = *(o.mXcapRoot); }
     if (o.mTunnelConfig) { j["mTunnelConfig"] = *(o.mTunnelConfig); }
     if (o.mCapabilities) { j["mCapabilities"] = *(o.mCapabilities); }
     if (o.mAdditionalFromParameters) { j["mAdditionalFromParameters"] = *(o.mAdditionalFromParameters); }
     if (o.mSourceAddress) { j["mSourceAddress"] = *(o.mSourceAddress); }
     if (o.mPreferPAssertedIdentity) { j["mPreferPAssertedIdentity"] = *(o.mPreferPAssertedIdentity); }
     if (o.mAutoRetryOnTransportDisconnect) { j["mAutoRetryOnTransportDisconnect"] = *(o.mAutoRetryOnTransportDisconnect); }
     if (o.mKeepAliveMode) { j["mKeepAliveMode"] = *(o.mKeepAliveMode); }
     if (o.mUseRinstance) { j["mUseRinstance"] = *(o.mUseRinstance); }
     if (o.mEnableNat64Support) { j["mEnableNat64Support"] = *(o.mEnableNat64Support); }
     if (o.mUsePrivacyHeaderOnlyForAnonymous) { j["mUsePrivacyHeaderOnlyForAnonymous"] = *(o.mUsePrivacyHeaderOnlyForAnonymous); }
     if (o.mTransportHoldover) { j["mTransportHoldover"] = *(o.mTransportHoldover); }
     if (o.mUseOptionsPing) { j["mUseOptionsPing"] = *(o.mUseOptionsPing); }
     if (o.mOptionsPingInterval) { j["mOptionsPingInterval"] = *(o.mOptionsPingInterval); }
     if (o.mUserCertificatePem) { j["mUserCertificatePem"] = *(o.mUserCertificatePem); }
     if (o.mUserPrivateKeyPem) { j["mUserPrivateKeyPem"] = *(o.mUserPrivateKeyPem); }
     if (o.mForceListenSocket) { j["mForceListenSocket"] = *(o.mForceListenSocket); }
     if (o.mLocalGroup) { j["mLocalGroup"] = *(o.mLocalGroup); }
     if (o.mOverrideMsecsTimerF) { j["mOverrideMsecsTimerF"] = *(o.mOverrideMsecsTimerF); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipAccountSettings& o)
    {
     if (j.contains("mUsername")) { o.mUsername = j.at("mUsername"); }
     if (j.contains("mDomain")) { o.mDomain = j.at("mDomain"); }
     if (j.contains("mPassword")) { o.mPassword = j.at("mPassword"); }
     if (j.contains("mDisplayName")) { o.mDisplayName = j.at("mDisplayName"); }
     if (j.contains("mAuthUsername")) { o.mAuthUsername = j.at("mAuthUsername"); }
     if (j.contains("mAuthRealm")) { o.mAuthRealm = j.at("mAuthRealm"); }
     if (j.contains("mUseRegistrar")) { o.mUseRegistrar = j.at("mUseRegistrar"); }
     if (j.contains("mOutboundProxy")) { o.mOutboundProxy = j.at("mOutboundProxy"); }
     if (j.contains("mAlwaysRouteViaOutboundProxy")) { o.mAlwaysRouteViaOutboundProxy = j.at("mAlwaysRouteViaOutboundProxy"); }
     if (j.contains("mRegistrationIntervalSeconds")) { o.mRegistrationIntervalSeconds = j.at("mRegistrationIntervalSeconds"); }
     if (j.contains("mMinimumRegistrationIntervalSeconds")) { o.mMinimumRegistrationIntervalSeconds = j.at("mMinimumRegistrationIntervalSeconds"); }
     if (j.contains("mMaximumRegistrationIntervalSeconds")) { o.mMaximumRegistrationIntervalSeconds = j.at("mMaximumRegistrationIntervalSeconds"); }
     if (j.contains("mUseRport")) { o.mUseRport = j.at("mUseRport"); }
     if (j.contains("mSipTransportType")) { o.mSipTransportType = j.at("mSipTransportType"); }
     if (j.contains("mExcludeEncryptedTransports")) { o.mExcludeEncryptedTransports = j.at("mExcludeEncryptedTransports"); }
     if (j.contains("mUserAgent")) { o.mUserAgent = j.at("mUserAgent"); }
     if (j.contains("mUdpKeepAliveTime")) { o.mUdpKeepAliveTime = j.at("mUdpKeepAliveTime"); }
     if (j.contains("mTcpKeepAliveTime")) { o.mTcpKeepAliveTime = j.at("mTcpKeepAliveTime"); }
     if (j.contains("mUseOutbound")) { o.mUseOutbound = j.at("mUseOutbound"); }
     if (j.contains("mUseGruu")) { o.mUseGruu = j.at("mUseGruu"); }
     if (j.contains("mOtherNonEscapedCharsInUri")) { o.mOtherNonEscapedCharsInUri = j.at("mOtherNonEscapedCharsInUri"); }
     if (j.contains("mNameServers")) { o.mNameServers = j.at("mNameServers"); }
     if (j.contains("mAdditionalNameServers")) { o.mAdditionalNameServers = j.at("mAdditionalNameServers"); }
     if (j.contains("mSessionTimerMode")) { o.mSessionTimerMode = j.at("mSessionTimerMode"); }
     if (j.contains("mSessionTimeSeconds")) { o.mSessionTimeSeconds = j.at("mSessionTimeSeconds"); }
     if (j.contains("mStunServerSource")) { o.mStunServerSource = j.at("mStunServerSource"); }
     if (j.contains("mStunServer")) { o.mStunServer = j.at("mStunServer"); }
     if (j.contains("mIgnoreCertVerification")) { o.mIgnoreCertVerification = j.at("mIgnoreCertVerification"); }
     if (j.contains("mAdditionalCertPeerNames")) { o.mAdditionalCertPeerNames = j.at("mAdditionalCertPeerNames"); }
     if (j.contains("mAcceptedCertPublicKeys")) { o.mAcceptedCertPublicKeys = j.at("mAcceptedCertPublicKeys"); }
     if (j.contains("mRequiredCertPublicKeys")) { o.mRequiredCertPublicKeys = j.at("mRequiredCertPublicKeys"); }
     if (j.contains("mSipQosSettings")) { o.mSipQosSettings = j.at("mSipQosSettings"); }
     if (j.contains("mUseImsAuthHeader")) { o.mUseImsAuthHeader = j.at("mUseImsAuthHeader"); }
     if (j.contains("mMinSipPort")) { o.mMinSipPort = j.at("mMinSipPort"); }
     if (j.contains("mMaxSipPort")) { o.mMaxSipPort = j.at("mMaxSipPort"); }
     if (j.contains("mDefaultSipPort")) { o.mDefaultSipPort = j.at("mDefaultSipPort"); }
     if (j.contains("mDefaultSipsPort")) { o.mDefaultSipsPort = j.at("mDefaultSipsPort"); }
     if (j.contains("mUseMethodParamInReferTo")) { o.mUseMethodParamInReferTo = j.at("mUseMethodParamInReferTo"); }
     if (j.contains("mUseInstanceId")) { o.mUseInstanceId = j.at("mUseInstanceId"); }
     if (j.contains("mAnswerModeSupported")) { o.mAnswerModeSupported = j.at("mAnswerModeSupported"); }
     if (j.contains("mIpVersion")) { o.mIpVersion = j.at("mIpVersion"); }
     if (j.contains("mSslVersion")) { o.mSslVersion = j.at("mSslVersion"); }
     if (j.contains("mCipherSuite")) { o.mCipherSuite = j.at("mCipherSuite"); }
     if (j.contains("mEnableLegacyServerConnect")) { o.mEnableLegacyServerConnect = j.at("mEnableLegacyServerConnect"); }
     if (j.contains("mReRegisterOnResponseTypes")) { o.mReRegisterOnResponseTypes = j.at("mReRegisterOnResponseTypes"); }
     if (j.contains("mEnableRegeventDeregistration")) { o.mEnableRegeventDeregistration = j.at("mEnableRegeventDeregistration"); }
     if (j.contains("mEnableDnsResetOnRegistrationRefresh")) { o.mEnableDnsResetOnRegistrationRefresh = j.at("mEnableDnsResetOnRegistrationRefresh"); }
     if (j.contains("mEnableAuthResetUponDnsReset")) { o.mEnableAuthResetUponDnsReset = j.at("mEnableAuthResetUponDnsReset"); }
     if (j.contains("mXcapRoot")) { o.mXcapRoot = j.at("mXcapRoot"); }
     if (j.contains("mTunnelConfig")) { o.mTunnelConfig = j.at("mTunnelConfig"); }
     if (j.contains("mCapabilities")) { o.mCapabilities = j.at("mCapabilities"); }
     if (j.contains("mAdditionalFromParameters")) { o.mAdditionalFromParameters = j.at("mAdditionalFromParameters"); }
     if (j.contains("mSourceAddress")) { o.mSourceAddress = j.at("mSourceAddress"); }
     if (j.contains("mPreferPAssertedIdentity")) { o.mPreferPAssertedIdentity = j.at("mPreferPAssertedIdentity"); }
     if (j.contains("mAutoRetryOnTransportDisconnect")) { o.mAutoRetryOnTransportDisconnect = j.at("mAutoRetryOnTransportDisconnect"); }
     if (j.contains("mKeepAliveMode")) { o.mKeepAliveMode = j.at("mKeepAliveMode"); }
     if (j.contains("mUseRinstance")) { o.mUseRinstance = j.at("mUseRinstance"); }
     if (j.contains("mEnableNat64Support")) { o.mEnableNat64Support = j.at("mEnableNat64Support"); }
     if (j.contains("mUsePrivacyHeaderOnlyForAnonymous")) { o.mUsePrivacyHeaderOnlyForAnonymous = j.at("mUsePrivacyHeaderOnlyForAnonymous"); }
     if (j.contains("mTransportHoldover")) { o.mTransportHoldover = j.at("mTransportHoldover"); }
     if (j.contains("mUseOptionsPing")) { o.mUseOptionsPing = j.at("mUseOptionsPing"); }
     if (j.contains("mOptionsPingInterval")) { o.mOptionsPingInterval = j.at("mOptionsPingInterval"); }
     if (j.contains("mUserCertificatePem")) { o.mUserCertificatePem = j.at("mUserCertificatePem"); }
     if (j.contains("mUserPrivateKeyPem")) { o.mUserPrivateKeyPem = j.at("mUserPrivateKeyPem"); }
     if (j.contains("mForceListenSocket")) { o.mForceListenSocket = j.at("mForceListenSocket"); }
     if (j.contains("mLocalGroup")) { o.mLocalGroup = j.at("mLocalGroup"); }
     if (j.contains("mOverrideMsecsTimerF")) { o.mOverrideMsecsTimerF = j.at("mOverrideMsecsTimerF"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTSETTINGS_H
