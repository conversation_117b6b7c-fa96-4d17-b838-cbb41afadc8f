#pragma once

#ifndef __CPCAPI2_CP_INVITE_HANDLER_SELECTOR_H__
#define __CPCAPI2_CP_INVITE_HANDLER_SELECTOR_H__

#include <resip/dum/HandlerSelector.hxx>

namespace CPCAPI2
{
   namespace SipAccount
   {
      class CPInviteHandlerSelector : public resip::InviteSessionHandlerSelector
      {
      public:
         CPInviteHandlerSelector();
         ~CPInviteHandlerSelector();

         virtual resip::InviteSessionHandler* operator()(resip::InviteSessionHandle session);

         void setDefaultInviteHandler(resip::InviteSessionHandler* handler) { defaultHandler = handler; }

      private:
         resip::InviteSessionHandler* defaultHandler;
      };
   }
}

#endif // __CPCAPI2_CP_INVITE_HANDLER_SELECTOR_H__