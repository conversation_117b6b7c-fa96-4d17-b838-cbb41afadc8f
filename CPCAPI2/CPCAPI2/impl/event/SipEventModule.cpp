#include "brand_branded.h"

#include "interface/public/event/SipEventManager.h"
#include "interface/public/event/SipPublicationManager.h"

#if (CPCAPI2_BRAND_SIP_EVENT_MODULE == 1)
#include "SipEventManagerInterface.h"
#include "SipEventPublicationManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace SipEvent
{

SipEventManager* SipEventManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_SIP_EVENT_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<SipEventManagerInterface>(phone, "SipEventManagerInterface");
#else
   return NULL;
#endif
}

SipEventPublicationManager* SipEventPublicationManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_SIP_EVENT_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<SipEventPublicationManagerInterface>(phone, "SipEventPublicationManagerInterface");
#else
   return NULL;
#endif
}

}
}
