#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_TUNNELTYPE_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_TUNNELTYPE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  enum class TunnelType
  {
    Tscf,
    StrettoTunnel,
    Unknown
  };

  inline std::string to_string(TunnelType value)
  {
    switch (value)
    {
      case TunnelType::Tscf:
        return "TSCF";
      case TunnelType::StrettoTunnel:
        return "StrettoTunnel";
      case TunnelType::Unknown:
        return "Unknown";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, TunnelType value)
  {
    switch (value)
    {
      case TunnelType::Tscf:
        os << "TSCF";
        break;
      case TunnelType::StrettoTunnel:
        os << "StrettoTunnel";
        break;
      case TunnelType::Unknown:
        os << "Unknown";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const TunnelType& e)
  {
    switch (e)
    {
      case TunnelType::Tscf:
        j = "TSCF";
        break;
      case TunnelType::StrettoTunnel:
        j = "StrettoTunnel";
        break;
      case TunnelType::Unknown:
        j = "Unknown";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, TunnelType& e)
  {
      if (0 == j.get<std::string>().compare("TSCF"))
    {
      e = TunnelType::Tscf;
    }
    else if (0 == j.get<std::string>().compare("StrettoTunnel"))
    {
      e = TunnelType::StrettoTunnel;
    }
    else if (0 == j.get<std::string>().compare("Unknown"))
    {
      e = TunnelType::Unknown;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum TunnelType"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_TUNNELTYPE_H
