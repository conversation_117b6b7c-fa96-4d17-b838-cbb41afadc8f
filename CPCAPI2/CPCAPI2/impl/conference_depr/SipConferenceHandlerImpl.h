#pragma once

#if !defined(CPCAPI2_CONFERENCE_HANDLER_IMPL_H)
#define CPCAPI2_CONFERENCE_HANDLER_IMPL_H

#include "cpcapi2defs.h"
#include "event/SipEventState.h"
#include "event/SipEventSubscriptionHandler.h"
#include "event/SipEventPublicationHandler.h"
#include "SipConferenceManagerImpl.h"
#include "conference/SipConferenceHandler.h"

#include "../util/DumFpCommand.h"
//#include <ConversationManager.hxx>


namespace CPCAPI2
{
namespace SipEvent
{
class SipEventManagerInterface;
};

using namespace SipConversation;

namespace SipConference
{
class SipConferenceHandler;

class SipConferenceHandlerImpl : public CPCAPI2::SipEvent::SipEventSubscriptionHandler,
								 public CPCAPI2::EventSyncHandler<CPCAPI2::SipConversation::SipConversationHandler>
{
public:
	SipConferenceHandlerImpl(CPCAPI2::SipAccount::SipAccountImpl& acct,
							 CPCAPI2::SipEvent::SipEventManagerInterface& sipEventIf,
							 CPCAPI2::SipConference::SipConferenceManagerImpl& confManager);

	void setSubscriptionHandler(CPCAPI2::SipConference::SipConferenceHandler* handler);
    virtual int onNewSubscription(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NewSubscriptionEvent& args);
    virtual int onSubscriptionEnded(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionEndedEvent& args);
    virtual int onIncomingEventState(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingEventStateEvent& args);
    virtual int onSubscriptionStateChanged(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionStateChangedEvent& args);

    virtual int onNotifySuccess(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifySuccessEvent& args);
    virtual int onNotifyFailure(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifyFailureEvent& args);
    virtual int onError(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::ErrorEvent& args);

	int onParticipationApproval(SipConferenceHandle conference, const cpc::string& targetAddress);
	int onParticipationNotAllowed(SipConferenceHandle conference, const cpc::string& targetAddress);

	/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	// SipConversationHandler interface
	/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

   virtual int onNewConversation(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::NewConversationEvent& args);

   virtual int onConversationEnded(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationEndedEvent& args);

   virtual int onIncomingTransferRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferRequestEvent& args);
   virtual int onIncomingRedirectRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::RedirectRequestEvent& args);
   virtual int onIncomingTargetChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TargetChangeRequestEvent& args);
   virtual int onIncomingHangupRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::HangupRequestEvent& args);
   virtual int onIncomingBroadsoftTalkRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftTalkEvent& args);
   virtual int onIncomingBroadsoftHoldRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftHoldEvent& args);
   virtual int onTransferProgress(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferProgressEvent& args);

   virtual int onConversationStateChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangeRequestEvent& args);
   virtual int onConversationStateChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangedEvent& args);

   virtual int onConversationMediaChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent& args);
   virtual int onConversationMediaChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangedEvent& args);

   virtual int onConversationStatisticsUpdated(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent& args);

   virtual int onError(CPCAPI2::SipConversation::SipConversationHandle subscription, const CPCAPI2::SipConversation::ErrorEvent& args);
private:
	bool parseContactField(cpc::string contactHeaderField, cpc::string &focusUri);

	CPCAPI2::SipConference::SipConferenceHandler* subscriptionHandler;
	CPCAPI2::SipAccount::SipAccountImpl& account;
	CPCAPI2::SipEvent::SipEventManagerInterface& sipEventIf;
	CPCAPI2::SipConference::SipConferenceManagerImpl& confManager;
};

}
}

#endif // CPCAPI2_CONFERENCE_HANDLER_IMPL_H
