#include "../datatypes/NetworkTransport.h"

namespace jsonrpc {
namespace CPCAPI2 {
namespace NetworkChangeManager {
class NetworkChangeManager
{
public:
    NetworkChangeManager(ITransport& transport)
    {
        this.transport = transport;
    }

    NetworkTransport networkTransport(int64_t handle);

private:
    ITransport* transport;
}; // class NetworkChangeManager
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace NetworkChangeManager
