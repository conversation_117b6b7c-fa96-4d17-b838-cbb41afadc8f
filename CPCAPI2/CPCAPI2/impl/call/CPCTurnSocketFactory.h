#pragma once

#ifndef __CPC_TURNSOCKETFACTORY_H__
#define __CPC_TURNSOCKETFACTORY_H__

#include <reflow/TurnSocketFactory.hxx>

namespace CPCAPI2
{
   class CPCTurnSocketFactory : public flowmanager::TurnSocketFactory
   {
   public:
      CPCTurnSocketFactory();

      virtual std::shared_ptr<reTurn::TurnAsyncSocket> createSocket(
         resip::HighPerfReactor& reactor,
         resip::Resolver <resip::HighPerfReactor>& resolver,
         reTurn::TurnAsyncSocketHandler *handler,
         reTurn::StunTuple localBinding
      );
   };
}

#endif /* __SUA_TURNSOCKETFACTORY_H__ */