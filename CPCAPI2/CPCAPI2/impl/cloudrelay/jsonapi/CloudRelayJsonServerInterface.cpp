#include "brand_branded.h"

#if (CPCAPI2_BRAND_CLOUD_RELAY_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "CloudRelayJsonServerInterface.h"
#include "cloudrelay/CloudRelayManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "jsonapi/JsonApiServerSendTransportInternal.h"
#include "json/JsonHelper.h"
#include "util/LogSubsystems.h"

#include <rutil/Logger.hxx>
#include <rutil/Random.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#include <ostream>
#include <fstream>

using CPCAPI2::PeerConnection::SessionDescription;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_CALL
#define JSON_MODULE "CloudRelayManagerJsonProxy"

namespace CPCAPI2
{
namespace CloudRelay
{
CloudRelayJsonServerInterface::CloudRelayJsonServerInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mConfBridgeMgr(CPCAPI2::CloudRelay::CloudRelayManager::getInterface(phone))
{
   CloudRelayManagerInterface* confBridgeMgrIf = dynamic_cast<CloudRelayManagerInterface*>(mConfBridgeMgr);
   confBridgeMgrIf->addSdkObserver(this);

   mFunctionMap["setHandler"] = std::bind(&CloudRelayJsonServerInterface::handleSetHandler, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["getRelay"] = std::bind(&CloudRelayJsonServerInterface::handleGetRelay, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["broadcast"] = std::bind(&CloudRelayJsonServerInterface::handleBroadcast, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["sendTo"] = std::bind(&CloudRelayJsonServerInterface::handleSendTo, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);

   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
}

CloudRelayJsonServerInterface::~CloudRelayJsonServerInterface()
{
   dynamic_cast<CloudRelayManagerInterface*>(mConfBridgeMgr)->removeSdkObserver(this);
}

void CloudRelayJsonServerInterface::Release()
{
}

void CloudRelayJsonServerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void CloudRelayJsonServerInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int CloudRelayJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request, const std::shared_ptr<resip::Data>& binaryData)
{
   post(resip::resip_bind(&CloudRelayJsonServerInterface::processIncomingImpl, this, conn, request, binaryData));
   return kSuccess;
}

void CloudRelayJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request, const std::shared_ptr<resip::Data>& binaryData)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(conn, functionObjectVal, binaryData);
   }
}

int CloudRelayJsonServerInterface::handleConnectionClosed(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn)
{
   post(resip::resip_bind(&CloudRelayJsonServerInterface::handleConnectionClosedImpl, this, conn));
   return kSuccess;
}

void CloudRelayJsonServerInterface::handleConnectionClosedImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn)
{
}

int CloudRelayJsonServerInterface::handleSetHandler(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   return kSuccess;
}

int CloudRelayJsonServerInterface::handleGetRelay(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   CloudRelayHandle relay;

   JsonDeserialize(functionObjectVal, JSON_VALUE(relay));

   dynamic_cast<CloudRelayManagerInterface*>(mConfBridgeMgr)->getRelay();
   return kSuccess;
}

int CloudRelayJsonServerInterface::handleBroadcast(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   CloudRelayHandle relay;
   cpc::string msg;

   JsonDeserialize(functionObjectVal, JSON_VALUE(relay), JSON_VALUE(msg));

   mConfBridgeMgr->getRelay();
   dynamic_cast<CloudRelayManagerInterface*>(mConfBridgeMgr)->broadcast(connId.jsonUserHandle, relay, msg);
   return kSuccess;
}

int CloudRelayJsonServerInterface::handleSendTo(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   CloudRelayHandle relay;
   CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser;
   cpc::string msg;

   JsonDeserialize(functionObjectVal, JSON_VALUE(relay), JSON_VALUE(jsonApiUser), JSON_VALUE(msg));

   mConfBridgeMgr->getRelay();
   dynamic_cast<CloudRelayManagerInterface*>(mConfBridgeMgr)->sendTo(relay, jsonApiUser, msg);
   return kSuccess;
}

int CloudRelayJsonServerInterface::onBroadcast(CloudRelayHandle relay, const BroadcastEvent& args)
{
   JsonFunctionCall(mTransport, "onBroadcast", JSON_VALUE(relay), JSON_VALUE(args));
   return kSuccess;
}

int CloudRelayJsonServerInterface::onMessage(CloudRelayHandle relay, const MessageEvent& args)
{
   //JsonFunctionCall(mTransport, "onMessage", JSON_VALUE(relay), JSON_VALUE(args));
   {
      CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
      CPCAPI2::Json::JsonFunctionSerialize serializer(json, true, JSON_MODULE, "onMessage");
      serializer.addValue("relay", relay);
      serializer.addValue("args", args);
      serializer.finalize();

      dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInternal*>(mTransport)->send(args.jsonApiUser, json);
   }

   return kSuccess;
}


}
}
#endif
