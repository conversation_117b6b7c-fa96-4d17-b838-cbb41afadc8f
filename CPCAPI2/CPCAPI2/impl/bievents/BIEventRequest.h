#pragma once
#ifndef __CPCAPI2_BIEVENTS_REQUEST_H__
#define __CPCAPI2_BIEVENTS_REQUEST_H__

#include <list>
#include <bievents/BIEventsTypes.h>
#include <util/strettorpc/RPCRequest.h>

#include "BIEventsInternalTypes.h"

namespace CPCAPI2
{
   namespace BIEvents
   {
      class BIEventRequest : public CPCAPI2::RPCRequest
      {
      public:
         BIEventRequest(
            const std::string& sessionID,
            const std::string& authToken,
            const std::string& buildStamp,
            const std::string& strettoUserNameOrID,
            const std::list< BIEventRecord >& eventRecords );
      };
   }
}

#endif // __CPCAPI2_BIEVENTS_REQUEST_H__