#include "brand_branded.h"

#include "interface/experimental/busylampfield/SipBusyLampFieldManager.h"
#include "interface/experimental/busylampfield/SipBusyLampFieldState.h"

#if (CPCAPI2_BRAND_SIP_BUSY_LAMP_FIELD_MODULE == 1)
#include "SipBusyLampFieldManagerInterface.h"
#include "SipBusyLampFieldStateImpl.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace SipBusyLampField
{

SipBusyLampFieldManager* SipBusyLampFieldManager::getInterface(CPCAPI2::Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_SIP_BUSY_LAMP_FIELD_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<SipBusyLampFieldManagerInterface>(phone, "SipBusyLampFieldManagerInterface");
#else
   return NULL;
#endif
}

SipBusyLampFieldStateManager* SipBusyLampFieldStateManager::getInterface(SipBusyLampFieldManager* blfManager)
{
#if (CPCAPI2_BRAND_SIP_BUSY_LAMP_FIELD_MODULE == 1)
   SipBusyLampFieldManagerInterface* parent = dynamic_cast<SipBusyLampFieldManagerInterface*>(blfManager);
   if (parent == NULL) return NULL;
   PhoneInterface* phone = parent->phoneInterface();
   return _GetInterfaceEx<SipBusyLampFieldStateImpl>(phone, "SipBusyLampFieldStateManager", parent);
#else 
   return NULL;
#endif
}

}
}

// CPCAPI2_BRAND_SIP_BUSY_LAMP_FIELD_MODULE
