#include "brand_branded.h"


#if CPCAPI2_BRAND_USE_LIBANALYZE == 1

#include <resip/dum/DumFeature.hxx>
#include <resip/dum/OutgoingEvent.hxx>
#include <CAnalyze.h>

using namespace resip;

class AnalyzeMessageFeature : public resip::DumFeature
{
public:
   AnalyzeMessageFeature(resip::DialogUsageManager& dum) : DumFeature(dum, dum.dumOutgoingTarget()) {}
   virtual ~AnalyzeMessageFeature() {}

   virtual ProcessingResult process(Message* msg)
   {
      OutgoingEvent* event = dynamic_cast<OutgoingEvent*>(msg);

      if (!event) return FeatureDone;

      SipMessage& sipmsg = *event->message();

      if (sipmsg.isRequest())
      {
         libanalyze::CAnalyze::GatherStats(sipmsg);
      }
      return FeatureDone;
   }

private:
   resip::Uri mPubIp;
};

#endif // CPCAPI2_BRAND_USE_LIBANALYZE