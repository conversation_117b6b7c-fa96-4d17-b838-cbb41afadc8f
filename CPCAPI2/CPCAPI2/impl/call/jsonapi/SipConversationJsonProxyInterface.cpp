#include "brand_branded.h"

#if (CPCAPI2_BRAND_CALL_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "SipConversationJsonProxyInterface.h"
#include "call/SipConversationJsonProxyStateHandler.h"
#include "../../account/jsonapi/SipAccountJsonProxyInterface.h"
#include "call/SipAVConversationManagerInterface.h"
#include "peerconnection/PeerConnectionManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "json/JsonHelper.h"
#include "jsonapi/JsonApiClient.h"
#include "jsonapi/JsonApiClientInterface.h"
#include "util/LogSubsystems.h"
#include "util/IpHelpers.h"

#include <rutil/Logger.hxx>
#include <rutil/Random.hxx>
#include <rutil/ParseBuffer.hxx>
#include <resip/stack/SdpContents.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

using CPCAPI2::PeerConnection::PeerConnectionManager;
using CPCAPI2::PeerConnection::PeerConnectionSettings;
using CPCAPI2::PeerConnection::SessionDescription;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_CALL
#define JSON_MODULE "SipConversationJsonApi"

namespace CPCAPI2
{
namespace SipConversation
{

SipConversationJsonProxyInterface::SipConversationJsonProxyInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mTransport(NULL),
     mPeerConnMgr(CPCAPI2::PeerConnection::PeerConnectionManager::getInterface(phone)),
     mStateHandler(NULL)
{
   mFunctionMap["createConversationResult"] = std::bind(&SipConversationJsonProxyInterface::handleCreateConversationResult, this, std::placeholders::_1);
   mFunctionMap["onNewConversation"] = std::bind(&SipConversationJsonProxyInterface::handleNewConversation, this, std::placeholders::_1);
   mFunctionMap["onConversationEnded"] = std::bind(&SipConversationJsonProxyInterface::handleConversationEnded, this, std::placeholders::_1);
   mFunctionMap["onConversationMediaChanged"] = std::bind(&SipConversationJsonProxyInterface::handleConversationMediaChanged, this, std::placeholders::_1);
   mFunctionMap["onConversationMediaChangeRequest"] = std::bind(&SipConversationJsonProxyInterface::handleConversationMediaChangeRequest, this, std::placeholders::_1);
   mFunctionMap["onConversationStateChanged"] = std::bind(&SipConversationJsonProxyInterface::handleConversationStateChanged, this, std::placeholders::_1);
   mFunctionMap["onConversationStateChangeRequest"] = std::bind(&SipConversationJsonProxyInterface::handleConversationStateChangeRequest, this, std::placeholders::_1);
   mFunctionMap["onConversationState"] = std::bind(&SipConversationJsonProxyInterface::handleConversationState, this, std::placeholders::_1);
   mFunctionMap["onSdpOfferAnswer"] = std::bind(&SipConversationJsonProxyInterface::handleSdpOfferAnswer, this, std::placeholders::_1);
   mFunctionMap["onLocalSdpOffer"] = std::bind(&SipConversationJsonProxyInterface::handleLocalSdpOffer, this, std::placeholders::_1);
   mFunctionMap["onLocalSdpAnswer"] = std::bind(&SipConversationJsonProxyInterface::handleLocalSdpAnswer, this, std::placeholders::_1);
   mFunctionMap["onConversationInitiated"] = std::bind(&SipConversationJsonProxyInterface::handleConversationInitiated, this, std::placeholders::_1);
   mFunctionMap["onIncomingTransferRequest"] = std::bind(&SipConversationJsonProxyInterface::handleIncomingTransferRequest, this, std::placeholders::_1);
   mFunctionMap["onIncomingRedirectRequest"] = std::bind(&SipConversationJsonProxyInterface::handleIncomingRedirectRequest, this, std::placeholders::_1);
   mFunctionMap["onIncomingTargetChangeRequest"] = std::bind(&SipConversationJsonProxyInterface::handleIncomingTargetChangeRequest, this, std::placeholders::_1);
   mFunctionMap["onIncomingHangupRequest"] = std::bind(&SipConversationJsonProxyInterface::handleIncomingHangupRequest, this, std::placeholders::_1);
   mFunctionMap["onIncomingBroadsoftTalkRequest"] = std::bind(&SipConversationJsonProxyInterface::handleIncomingBroadsoftTalkRequest, this, std::placeholders::_1);
   mFunctionMap["onIncomingBroadsoftHoldRequest"] = std::bind(&SipConversationJsonProxyInterface::handleIncomingBroadsoftHoldRequest, this, std::placeholders::_1);
   mFunctionMap["onTransferProgress"] = std::bind(&SipConversationJsonProxyInterface::handleTransferProgress, this, std::placeholders::_1);
   mFunctionMap["onConversationStatisticsUpdated"] = std::bind(&SipConversationJsonProxyInterface::handleConversationStatisticsUpdated, this, std::placeholders::_1);
   mFunctionMap["onAudioDeviceLevelChange"] = std::bind(&SipConversationJsonProxyInterface::handleAudioDeviceLevelChange, this, std::placeholders::_1);
   mFunctionMap["onError"] = std::bind(&SipConversationJsonProxyInterface::handleError, this, std::placeholders::_1);

   JsonApi::JsonApiClientInterface* jsonApiClientIf = dynamic_cast<JsonApi::JsonApiClientInterface*>(JsonApi::JsonApiClient::getInterface(phone));
   setTransport(jsonApiClientIf->getTransport());

   dynamic_cast<PeerConnection::PeerConnectionManagerInterface*>(mPeerConnMgr)->addSdkObserver(this);
}

SipConversationJsonProxyInterface::~SipConversationJsonProxyInterface()
{
   dynamic_cast<PeerConnection::PeerConnectionManagerInterface*>(mPeerConnMgr)->removeSdkObserver(this);
}

void SipConversationJsonProxyInterface::Release()
{
}

void SipConversationJsonProxyInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void SipConversationJsonProxyInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

// JsonApiClientModule
void SipConversationJsonProxyInterface::setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport)
{
   mTransport = transport;
}

int SipConversationJsonProxyInterface::processIncoming(const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::processIncomingImpl, this, request));
   return kSuccess;
}

void SipConversationJsonProxyInterface::processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

int SipConversationJsonProxyInterface::handleCreateConversationResult(const rapidjson::Value& functionObjectVal)
{
   JsonDeserialize(functionObjectVal, "conversation", mServerCreatedConvHandle);
   mCondCreated.notify_one();
   return kSuccess;
}

int SipConversationJsonProxyInterface::handleNewConversation(const rapidjson::Value & functionObjectVal)
{
   SipConversationHandle conversation = -1;
   SipConversation::NewConversationEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));

   mMapConvHandleToAccountHandle[conversation] = args.account;

   std::map<SipAccount::SipAccountHandle, SipConversationHandler*>::iterator it = mAppHandlers.find(args.account);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(SipConversationHandler::onNewConversation, it->second, conversation, args));
   }
   return kSuccess;
}

int SipConversationJsonProxyInterface::handleConversationEnded(const rapidjson::Value & functionObjectVal)
{
   SipConversationHandle conversation = -1;
   SipConversation::ConversationEndedEvent args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));


   SipAccount::SipAccountHandle account = mMapConvHandleToAccountHandle[conversation];
   std::map<SipAccount::SipAccountHandle, SipConversationHandler*>::iterator it = mAppHandlers.find(account);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(SipConversationHandler::onConversationEnded, it->second, conversation, args));
   }

   RemoteConvStateMap::iterator itRemoteConv = mRemoteConvStateMap.find(conversation);
   if (itRemoteConv != mRemoteConvStateMap.end())
   {
      mPeerConnMgr->close(itRemoteConv->second.peerConnection());
      itRemoteConv->second.peerConnection() = -1;
   }
   return kSuccess;
}

int SipConversationJsonProxyInterface::handleConversationStateChangeRequest(const rapidjson::Value & functionObjectVal)
{
   SipConversationHandle conversation = -1;
   SipConversation::ConversationStateChangeRequestEvent args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation)/*, JSON_VALUE(args)*/);

   SipAccount::SipAccountHandle account = mMapConvHandleToAccountHandle[conversation];
   std::map<SipAccount::SipAccountHandle, SipConversationHandler*>::iterator it = mAppHandlers.find(account);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(SipConversationHandler::onConversationStateChangeRequest, it->second, conversation, args));
   }
   return kSuccess;
}

int SipConversationJsonProxyInterface::handleConversationStateChanged(const rapidjson::Value & functionObjectVal)
{
   SipConversationHandle conversation = -1;
   SipConversation::ConversationStateChangedEvent args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));

   SipAccount::SipAccountHandle account = mMapConvHandleToAccountHandle[conversation];
   std::map<SipAccount::SipAccountHandle, SipConversationHandler*>::iterator it = mAppHandlers.find(account);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(SipConversationHandler::onConversationStateChanged, it->second, conversation, args));
   }
   return kSuccess;
}

int SipConversationJsonProxyInterface::handleConversationMediaChangeRequest(const rapidjson::Value & functionObjectVal)
{
   SipConversationHandle conversation = -1;
   SipConversation::ConversationMediaChangeRequestEvent args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));

   SipAccount::SipAccountHandle account = mMapConvHandleToAccountHandle[conversation];
   std::map<SipAccount::SipAccountHandle, SipConversationHandler*>::iterator it = mAppHandlers.find(account);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(SipConversationHandler::onConversationMediaChangeRequest, it->second, conversation, args));
   }
   return kSuccess;
}

int SipConversationJsonProxyInterface::handleConversationMediaChanged(const rapidjson::Value & functionObjectVal)
{
   SipConversationHandle conversation = -1;
   SipConversation::ConversationMediaChangedEvent args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));

   SipAccount::SipAccountHandle account = mMapConvHandleToAccountHandle[conversation];
   std::map<SipAccount::SipAccountHandle, SipConversationHandler*>::iterator it = mAppHandlers.find(account);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(SipConversationHandler::onConversationMediaChanged, it->second, conversation, args));
   }
   return kSuccess;
}

int SipConversationJsonProxyInterface::setHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipConversationHandler * handler)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&SipConversationJsonProxyInterface::setHandlerImpl, this, account, handler);
   if (handler == NULL)
   {
      execute(f);
      SipAccount::SipAccountManagerJsonProxy::getInterface(mPhone)->process(-1);
   }
   else
   {
      post(f);
   }

   return kSuccess;
}

void SipConversationJsonProxyInterface::setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipConversationHandler * handler)
{
   mAppHandlers[account] = handler;

   JsonFunctionCall(mTransport, "setHandler", JSON_VALUE(account));
}

int SipConversationJsonProxyInterface::setDefaultSettings(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipConversationSettings& settings)
{
   post(resip::resip_bind(static_cast<void (SipConversationJsonProxyInterface::*)(CPCAPI2::SipAccount::SipAccountHandle,const CPCAPI2::SipConversationSettings&)>(&SipConversationJsonProxyInterface::setDefaultSettingsImpl),
     this, account, settings));
   return kSuccess;
}

void SipConversationJsonProxyInterface::setDefaultSettingsImpl(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipConversationSettings& settings)
{
   mMapAcctToConvSettings[account] = settings;
   JsonFunctionCall(mTransport, "setDefaultSettings", JSON_VALUE(account), JSON_VALUE(settings));
}

int SipConversationJsonProxyInterface::setDefaultSettings(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipConversationSettings& settings, CPCAPI2::NetworkTransport transport)
{
   post(resip::resip_bind(static_cast<void (SipConversationJsonProxyInterface::*)(CPCAPI2::SipAccount::SipAccountHandle,const CPCAPI2::SipConversationSettings&,CPCAPI2::NetworkTransport)>(&SipConversationJsonProxyInterface::setDefaultSettingsImpl),
      this, account, settings, transport));
   return kSuccess;
}

void SipConversationJsonProxyInterface::setDefaultSettingsImpl(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipConversationSettings& settings, CPCAPI2::NetworkTransport transport)
{
   mMapAcctToConvSettings[account] = settings;
   JsonFunctionCall(mTransport, "setDefaultSettings", JSON_VALUE(account), JSON_VALUE(settings), JSON_VALUE(transport));
}

int SipConversationJsonProxyInterface::setCallInitiatedFromPush(SipConversationHandle conversation)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::setCallInitiatedFromPushImpl, this, conversation));
   return kSuccess;
}

void SipConversationJsonProxyInterface::setCallInitiatedFromPushImpl(SipConversationHandle conversation)
{
   JsonFunctionCall(mTransport, "setCallInitiatedFromPush", JSON_VALUE(conversation));
}

SipConversationHandle SipConversationJsonProxyInterface::createConversation(CPCAPI2::SipAccount::SipAccountHandle account)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::createConversationImpl, this, account));

   std::unique_lock<std::mutex> lk(mMutex);
   mCondCreated.wait(lk);

   return mServerCreatedConvHandle;
}
void SipConversationJsonProxyInterface::createConversationImpl(SipAccount::SipAccountHandle account)
{
   mServerCreatedConvHandle = -1;
   JsonFunctionCall(mTransport, "createConversation", JSON_VALUE(account));
}

int SipConversationJsonProxyInterface::addParticipant(SipConversationHandle conversation, const cpc::string& participantAddress)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::addParticipantImpl, this, conversation, participantAddress));
   return kSuccess;
}
void SipConversationJsonProxyInterface::addParticipantImpl(SipConversationHandle conversation, const cpc::string& participantAddress)
{
   JsonFunctionCall(mTransport, "addParticipant", JSON_VALUE(conversation), JSON_VALUE(participantAddress));
}

int SipConversationJsonProxyInterface::configureMedia(SipConversationHandle conversation, const MediaInfo& mediaDescriptor)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::configureMediaImpl, this, conversation, mediaDescriptor));
   return kSuccess;
}

void SipConversationJsonProxyInterface::configureMediaImpl(SipConversationHandle conversation, const MediaInfo& mediaDescriptor)
{
   JsonFunctionCall(mTransport, "configureMedia", JSON_VALUE(conversation), JSON_VALUE(mediaDescriptor));
}

int SipConversationJsonProxyInterface::setCryptoSuitesForMedia(SipConversationHandle conversation, CPCAPI2::SipConversation::MediaType mediaType, cpc::vector<MediaCryptoSuite> cryptoSuites)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::setCryptoSuitesForMediaImpl, this, conversation, mediaType, cryptoSuites));
   return kSuccess;
}

void SipConversationJsonProxyInterface::setCryptoSuitesForMediaImpl(SipConversationHandle conversation, CPCAPI2::SipConversation::MediaType mediaType, cpc::vector<MediaCryptoSuite> mediaCryptoSuites)
{
   JsonFunctionCall(mTransport, "configureMedia", JSON_VALUE(conversation), JSON_VALUE(mediaType), JSON_VALUE(mediaCryptoSuites));
}

int SipConversationJsonProxyInterface::setMediaEnabled(SipConversationHandle conversation, MediaType mediaType, bool enabled)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::setMediaEnabledImpl, this, conversation, mediaType, enabled));
   return kSuccess;
}
void SipConversationJsonProxyInterface::setMediaEnabledImpl(SipConversationHandle conversation, MediaType mediaType, bool enabled)
{
   JsonFunctionCall(mTransport, "setMediaEnabled", JSON_VALUE(conversation), JSON_VALUE(mediaType), JSON_VALUE(enabled));
}

int SipConversationJsonProxyInterface::setMediaEnabledByDirection(SipConversationHandle conversation, CPCAPI2::SipConversation::MediaType mediaType, bool enabled)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::setMediaEnabledByDirectionImpl, this, conversation, mediaType, enabled));
   return kSuccess;
}
void SipConversationJsonProxyInterface::setMediaEnabledByDirectionImpl(SipConversationHandle conversation, CPCAPI2::SipConversation::MediaType mediaType, bool enabled)
{
   JsonFunctionCall(mTransport, "setMediaEnabledByDirection", JSON_VALUE(conversation), JSON_VALUE(mediaType), JSON_VALUE(enabled));
}

int SipConversationJsonProxyInterface::setMediaCryptoSuites(SipConversationHandle conversation, cpc::vector<MediaCryptoSuite> cryptoSuites)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::setMediaCryptoSuitesImpl, this, conversation, cryptoSuites));
   return kSuccess;
}

void SipConversationJsonProxyInterface::setMediaCryptoSuitesImpl(SipConversationHandle conversation, cpc::vector<MediaCryptoSuite> mediaCryptoSuites)
{
   JsonFunctionCall(mTransport, "setMediaCryptoSuitesImpl", JSON_VALUE(conversation), JSON_VALUE(mediaCryptoSuites));
}

int SipConversationJsonProxyInterface::setAnonymousMode(SipConversationHandle conversation, unsigned int anonymousMode)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::setAnonymousModeImpl, this, conversation, anonymousMode));
   return kSuccess;
}

void SipConversationJsonProxyInterface::setAnonymousModeImpl(SipConversationHandle conversation, unsigned int anonymousMode)
{
   JsonFunctionCall(mTransport, "setAnonymousMode", JSON_VALUE(conversation), JSON_VALUE(anonymousMode));
}

int SipConversationJsonProxyInterface::setBestEffortMediaEncryption(SipConversationHandle conversation, bool enabled)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::setBestEffortMediaEncryptionImpl, this, conversation, enabled));
   return kSuccess;
}

void SipConversationJsonProxyInterface::setBestEffortMediaEncryptionImpl(SipConversationHandle conversation, bool enabled)
{
   JsonFunctionCall(mTransport, "setBestEffortMediaEncryption", JSON_VALUE(conversation), JSON_VALUE(enabled));
}

int SipConversationJsonProxyInterface::sendMediaChangeRequest(SipConversationHandle conversation)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::sendMediaChangeRequestImpl, this, conversation));
   return kSuccess;
}
void SipConversationJsonProxyInterface::sendMediaChangeRequestImpl(SipConversationHandle conversation)
{
   JsonFunctionCall(mTransport, "sendMediaChangeRequest", JSON_VALUE(conversation));
}

int SipConversationJsonProxyInterface::hold(SipConversationHandle conversation)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::holdImpl, this, conversation));
   return kSuccess;
}
void SipConversationJsonProxyInterface::holdImpl(SipConversationHandle conversation)
{
   JsonFunctionCall(mTransport, "hold", JSON_VALUE(conversation));
}

int SipConversationJsonProxyInterface::unhold(SipConversationHandle conversation)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::unholdImpl, this, conversation));
   return kSuccess;
}
void SipConversationJsonProxyInterface::unholdImpl(SipConversationHandle conversation)
{
   JsonFunctionCall(mTransport, "unhold", JSON_VALUE(conversation));
}

int SipConversationJsonProxyInterface::acceptIncomingTransferRequest(SipConversationHandle transferTargetConversation)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::acceptIncomingTransferRequestImpl, this, transferTargetConversation));
   return kSuccess;
}
void SipConversationJsonProxyInterface::acceptIncomingTransferRequestImpl(SipConversationHandle transferTargetConversation)
{
   JsonFunctionCall(mTransport, "acceptIncomingTransferRequest", JSON_VALUE(transferTargetConversation));
}

int SipConversationJsonProxyInterface::rejectIncomingTransferRequest(SipConversationHandle transferTargetConversation)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::rejectIncomingTransferRequestImpl, this, transferTargetConversation));
   return kSuccess;
}
void SipConversationJsonProxyInterface::rejectIncomingTransferRequestImpl(SipConversationHandle transferTargetConversation)
{
   JsonFunctionCall(mTransport, "rejectIncomingTransferRequest", JSON_VALUE(transferTargetConversation));

}

int SipConversationJsonProxyInterface::transfer(SipConversationHandle transferTargetConversation, SipConversationHandle transfereeConversation)
{
   post(resip::resip_bind(static_cast<void (SipConversationJsonProxyInterface::*)(SipConversationHandle,SipConversationHandle)>(&SipConversationJsonProxyInterface::transferImpl),
      this, transferTargetConversation, transfereeConversation));
   return kSuccess;
}
void SipConversationJsonProxyInterface::transferImpl(SipConversationHandle transferTargetConversation, SipConversationHandle transfereeConversation)
{
   JsonFunctionCall(mTransport, "transfer", JSON_VALUE(transferTargetConversation), JSON_VALUE(transfereeConversation));
}

int SipConversationJsonProxyInterface::transfer(SipConversationHandle transferTargetConversation, SipConversationHandle transfereeConversation, bool endTargetConversationOnSuccess)
{
   post(resip::resip_bind(static_cast<void (SipConversationJsonProxyInterface::*)(SipConversationHandle,SipConversationHandle,bool)>(&SipConversationJsonProxyInterface::transferImpl),
      this, transferTargetConversation, transfereeConversation, endTargetConversationOnSuccess));
   return kSuccess;
}
void SipConversationJsonProxyInterface::transferImpl(SipConversationHandle transferTargetConversation, SipConversationHandle transfereeConversation, bool endTargetConversationOnSuccess)
{
   JsonFunctionCall(mTransport, "transfer", JSON_VALUE(transferTargetConversation), JSON_VALUE(transfereeConversation), JSON_VALUE(endTargetConversationOnSuccess));
}

int SipConversationJsonProxyInterface::transfer(SipConversationHandle transfereeConversation, const cpc::string& targetAddress)
{
   post(resip::resip_bind(static_cast<void (SipConversationJsonProxyInterface::*)(SipConversationHandle,const cpc::string&)>(&SipConversationJsonProxyInterface::transferImpl),
      this, transfereeConversation, targetAddress));
   return kSuccess;
}
void SipConversationJsonProxyInterface::transferImpl(SipConversationHandle transfereeConversation, const cpc::string& targetAddress)
{
   JsonFunctionCall(mTransport, "transfer", JSON_VALUE(transfereeConversation), JSON_VALUE(targetAddress));
}

int SipConversationJsonProxyInterface::setDtmfMode(CPCAPI2::SipAccount::SipAccountHandle account, unsigned int ordinal, DtmfMode dtmfMode)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::setDtmfModeImpl, this, account, ordinal, dtmfMode));
   return kSuccess;
}

void SipConversationJsonProxyInterface::setDtmfModeImpl(CPCAPI2::SipAccount::SipAccountHandle account, unsigned int ordinal, DtmfMode dtmfMode)
{
   JsonFunctionCall(mTransport, "setDtmfMode", JSON_VALUE(account), JSON_VALUE(ordinal), JSON_VALUE(dtmfMode));
}

int SipConversationJsonProxyInterface::startDtmfTone(SipConversationHandle conversation, unsigned int toneId, bool playLocally)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::startDtmfToneImpl, this, conversation, toneId, playLocally));
   return kSuccess;
}
void SipConversationJsonProxyInterface::startDtmfToneImpl(SipConversationHandle conversation, unsigned int toneId, bool playLocally)
{
   JsonFunctionCall(mTransport, "startDtmfTone", JSON_VALUE(conversation), JSON_VALUE(toneId), JSON_VALUE(playLocally));
}

int SipConversationJsonProxyInterface::stopDtmfTone()
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::stopDtmfToneImpl, this));
   return kSuccess;
}
void SipConversationJsonProxyInterface::stopDtmfToneImpl()
{
  JsonFunctionCall(mTransport, "stopDtmfTone");
}

int SipConversationJsonProxyInterface::setAnswerMode(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipConversation::AnswerModeSettings answerMode, CPCAPI2::NetworkTransport transport)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::setAnswerModeImpl, this, account, answerMode, transport));
   return kSuccess;
}

void SipConversationJsonProxyInterface::setAnswerModeImpl(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipConversation::AnswerModeSettings answerMode, CPCAPI2::NetworkTransport transport)
{
   JsonFunctionCall(mTransport, "setAnswerMode", JSON_VALUE(account), JSON_VALUE(answerMode), JSON_VALUE(transport));
}

int SipConversationJsonProxyInterface::refreshConversationStatistics(SipConversationHandle conversation)
{
   post(resip::resip_bind(static_cast<void (SipConversationJsonProxyInterface::*)(SipConversationHandle)>(&SipConversationJsonProxyInterface::refreshConversationStatisticsImpl), this, conversation));
   return kSuccess;
}

void SipConversationJsonProxyInterface::refreshConversationStatisticsImpl(SipConversationHandle conversation)
{
  JsonFunctionCall(mTransport, "refreshConversationStatistics", JSON_VALUE(conversation));
}

int SipConversationJsonProxyInterface::refreshConversationStatistics(SipConversationHandle conversation, bool includeNetworkStatistics, bool includeJitterStatistics, bool includeRemoteStatistics)
{
   post(resip::resip_bind(static_cast<void (SipConversationJsonProxyInterface::*)(SipConversationHandle,bool,bool,bool)>(&SipConversationJsonProxyInterface::refreshConversationStatisticsImpl),
     this, conversation, includeNetworkStatistics, includeJitterStatistics, includeRemoteStatistics));
   return kSuccess;
}
void SipConversationJsonProxyInterface::refreshConversationStatisticsImpl(SipConversationHandle conversation, bool includeNetworkStatistics, bool includeJitterStatistics, bool includeRemoteStatistics)
{
   JsonFunctionCall(mTransport, "refreshConversationStatistics", JSON_VALUE(conversation), JSON_VALUE(includeNetworkStatistics), JSON_VALUE(includeJitterStatistics), JSON_VALUE(includeRemoteStatistics));
}

int SipConversationJsonProxyInterface::start(SipConversationHandle conversation)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::startImpl, this, conversation));
   return kSuccess;
}
void SipConversationJsonProxyInterface::startImpl(SipConversationHandle conversation)
{
   JsonFunctionCall(mTransport, "start", JSON_VALUE(conversation));
}

int SipConversationJsonProxyInterface::end(SipConversationHandle conversation)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::endImpl, this, conversation));
   return kSuccess;
}
void SipConversationJsonProxyInterface::endImpl(SipConversationHandle conversation)
{
   JsonFunctionCall(mTransport, "end", JSON_VALUE(conversation));
}

int SipConversationJsonProxyInterface::redirect(SipConversationHandle conversation, const cpc::string& targetAddress, const cpc::string& reason)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::redirectImpl, this, conversation, targetAddress, reason));
   return kSuccess;
}
void SipConversationJsonProxyInterface::redirectImpl(SipConversationHandle conversation, const cpc::string& targetAddress, const cpc::string& reason)
{
   JsonFunctionCall(mTransport, "redirect", JSON_VALUE(conversation), JSON_VALUE(targetAddress), JSON_VALUE(reason));
}

int SipConversationJsonProxyInterface::sendRingingResponse(SipConversationHandle conversation)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::sendRingingResponseImpl, this, conversation));
   return kSuccess;
}
void SipConversationJsonProxyInterface::sendRingingResponseImpl(SipConversationHandle conversation)
{
   JsonFunctionCall(mTransport, "sendRingingResponse", JSON_VALUE(conversation));
}

int SipConversationJsonProxyInterface::accept(SipConversationHandle conversation)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::acceptImpl, this, conversation));
   return kSuccess;
}
void SipConversationJsonProxyInterface::acceptImpl(SipConversationHandle conversation)
{
   JsonFunctionCall(mTransport, "accept", JSON_VALUE(conversation));
}

int SipConversationJsonProxyInterface::reject(SipConversationHandle conversation, unsigned int rejectReason)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::rejectImpl, this, conversation, rejectReason));
   return kSuccess;
}
void SipConversationJsonProxyInterface::rejectImpl(SipConversationHandle conversation, unsigned int rejectReason)
{
   JsonFunctionCall(mTransport, "reject", JSON_VALUE(conversation));
}

void SipConversationJsonProxyInterface::postCallback(resip::ReadCallbackBase* fp)
{
   SipAccount::SipAccountManagerJsonProxy* sipAcctJsonIf = SipAccount::SipAccountManagerJsonProxy::getInterface(mPhone);
   dynamic_cast<SipAccount::SipAccountJsonProxyInterface*>(sipAcctJsonIf)->postCallback(fp);
}

int SipConversationJsonProxyInterface::setStateHandler(SipConversationJsonProxyStateHandler* handler)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::setStateHandlerImpl, this, handler));
   return kSuccess;
}

void SipConversationJsonProxyInterface::setStateHandlerImpl(SipConversationJsonProxyStateHandler* handler)
{
   mStateHandler = handler;
}

int SipConversationJsonProxyInterface::joinRemoteConversation(SipConversationHandle conversation, RemoteMediaMode remoteMediaMode)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::joinRemoteConversationImpl, this, conversation, remoteMediaMode));
   return kSuccess;
}

void SipConversationJsonProxyInterface::joinRemoteConversationImpl(SipConversationHandle conversation, RemoteMediaMode remoteMediaMode)
{
   mRemoteConvStateMap[conversation]; // required in case of outgoing call
   JsonFunctionCall(mTransport, "joinRemoteConversation", JSON_VALUE(conversation), JSON_VALUE(remoteMediaMode));
}

int SipConversationJsonProxyInterface::requestStateAllConversations()
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::requestStateAllConversationsImpl, this));
   return kSuccess;
}

void SipConversationJsonProxyInterface::requestStateAllConversationsImpl()
{
   JsonFunctionCall(mTransport, "requestStateAllConversations");
}

int SipConversationJsonProxyInterface::adornMessage(SipConversationHandle conversation, unsigned int adornmentMessageId, const cpc::vector<SipHeader>& customHeaders)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::adornMessageImpl, this, conversation, adornmentMessageId, customHeaders));
   return kSuccess;
}
void SipConversationJsonProxyInterface::adornMessageImpl(SipConversationHandle conversation, unsigned int adornmentMessageId, const cpc::vector<SipHeader>& customHeaders)
{
  JsonFunctionCall(mTransport, "joinRemoteConversation", JSON_VALUE(conversation), JSON_VALUE(adornmentMessageId), JSON_VALUE(customHeaders));
}

int SipConversationJsonProxyInterface::playSound(SipConversationHandle conversation, const cpc::string& resourceUri, bool repeat)
{
   post(resip::resip_bind(static_cast<void (SipConversationJsonProxyInterface::*)(SipConversationHandle,const cpc::string&,bool)>(&SipConversationJsonProxyInterface::playSoundImpl),
      this, conversation, resourceUri, repeat));
   return kSuccess;
}
void SipConversationJsonProxyInterface::playSoundImpl(SipConversationHandle conversation, const cpc::string& resourceUri, bool repeat)
{
  JsonFunctionCall(mTransport, "playSound", JSON_VALUE(conversation), JSON_VALUE(resourceUri), JSON_VALUE(repeat));
}

int SipConversationJsonProxyInterface::stopPlaySound(SipConversationHandle conversation)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::stopPlaySoundImpl, this, conversation));
   return kSuccess;
}
void SipConversationJsonProxyInterface::stopPlaySoundImpl(SipConversationHandle conversation)
{
  JsonFunctionCall(mTransport, "stopPlaySound", JSON_VALUE(conversation));
}

int SipConversationJsonProxyInterface::startMonitoringAudioDeviceLevels(SipConversationHandle conversation)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::startMonitoringAudioDeviceLevelsImpl, this, conversation));
   return kSuccess;
}
void SipConversationJsonProxyInterface::startMonitoringAudioDeviceLevelsImpl(SipConversationHandle conversation)
{
  JsonFunctionCall(mTransport, "startMonitoringAudioDeviceLevels", JSON_VALUE(conversation));
}

int SipConversationJsonProxyInterface::stopMonitoringAudioDeviceLevels(SipConversationHandle conversation)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::stopMonitoringAudioDeviceLevelsImpl, this, conversation));
   return kSuccess;
}
void SipConversationJsonProxyInterface::stopMonitoringAudioDeviceLevelsImpl(SipConversationHandle conversation)
{
  JsonFunctionCall(mTransport, "stopMonitoringAudioDeviceLevels", JSON_VALUE(conversation));
}

int SipConversationJsonProxyInterface::setCallKitMode(SipConversationHandle conversation)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::setCallKitModeImpl, this, conversation));
   return kSuccess;
}
void SipConversationJsonProxyInterface::setCallKitModeImpl(SipConversationHandle conversation)
{
  JsonFunctionCall(mTransport, "setCallKitMode", JSON_VALUE(conversation));
}

int SipConversationJsonProxyInterface::setTelecomFrameworkMode(SipConversationHandle conversation)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::setTelecomFrameworkModeImpl, this, conversation));
   return kSuccess;
}
void SipConversationJsonProxyInterface::setTelecomFrameworkModeImpl(SipConversationHandle conversation)
{
  JsonFunctionCall(mTransport, "setTelecomFrameworkMode", JSON_VALUE(conversation));
}

int SipConversationJsonProxyInterface::setIncomingVideoRenderTarget(SipConversationHandle conversation, void* surface, Media::VideoSurfaceType type)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::setIncomingVideoRenderTargetImpl, this, conversation, surface, type));
   return kSuccess;
}
void SipConversationJsonProxyInterface::setIncomingVideoRenderTargetImpl(SipConversationHandle conversation, void* surface, Media::VideoSurfaceType type)
{
  JsonFunctionCall(mTransport, "setIncomingVideoRenderTarget", JSON_VALUE(conversation), "surface", (int64_t)surface, JSON_VALUE(type));
}

int SipConversationJsonProxyInterface::setAudioDeviceCloseDelay(SipConversationHandle conversation, int audioDeviceCloseDelay)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::setAudioDeviceCloseDelayImpl, this, conversation, audioDeviceCloseDelay));
   return kSuccess;
}
void SipConversationJsonProxyInterface::setAudioDeviceCloseDelayImpl(SipConversationHandle conversation, int audioDeviceCloseDelay)
{
  JsonFunctionCall(mTransport, "setAudioDeviceCloseDelay", JSON_VALUE(conversation), JSON_VALUE(audioDeviceCloseDelay));
}

int SipConversationJsonProxyInterface::setVideoNackSettings(SipConversationHandle conversation, const NackSettings& nackSettings)
{
   post(resip::resip_bind(&SipConversationJsonProxyInterface::setVideoNackSettingsImpl, this, conversation, nackSettings));
   return kSuccess;
}
void SipConversationJsonProxyInterface::setVideoNackSettingsImpl(SipConversationHandle conversation, const NackSettings& nackSettings)
{
   JsonFunctionCall(mTransport, "setVideoNackSettings", JSON_VALUE(conversation), JSON_VALUE(nackSettings));
}

// PeerConnectionHandler
int SipConversationJsonProxyInterface::onSignalingStateChange(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SignalingStateChangeEvent& args)
{
   return kSuccess;
}

int SipConversationJsonProxyInterface::onCreateOfferResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateOfferResult& args)
{
   RemoteConvStateMap::iterator itRemConv = mRemoteConvStateMap.begin();
   for (; itRemConv != mRemoteConvStateMap.end(); ++itRemConv)
   {
      if (itRemConv->second.peerConnection() == pc)
      {
         break;
      }
   }

   if (itRemConv == mRemoteConvStateMap.end())
   {
      // this isn't a peer connection that we created; it was probably created manually using PeerConnectionManager
      return kSuccess;
   }

   mPeerConnMgr->setLocalDescription(pc, args.sdp);
   if (itRemConv->second.offerAnswerState() == RemoteConvOfferAnswerState_CreatingLocalOffer)
   {
      itRemConv->second.setOfferAnswerState(RemoteConvOfferAnswerState_HaveLocalOffer_WaitingForRemoteAnswer);

      JsonFunctionCall(mTransport, "provideSdpOffer", "conversation", itRemConv->first, "sdpOffer", args.sdp.sdpString);
   }
   return kSuccess;
}

int SipConversationJsonProxyInterface::onCreateAnswerResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args)
{
   RemoteConvStateMap::iterator itRemConv = mRemoteConvStateMap.begin();
   for (; itRemConv != mRemoteConvStateMap.end(); ++itRemConv)
   {
      if (itRemConv->second.peerConnection() == pc)
      {
         break;
      }
   }

   if (itRemConv == mRemoteConvStateMap.end())
   {
      // this isn't a peer connection that we created; it was probably created manually using PeerConnectionManager
      return kSuccess;
   }

   if (!itRemConv->second.hasRemoteOffer())
   {
      ErrLog(<< "We created an answer, but there is no remote offer???");
      return kError;
   }

   mPeerConnMgr->setLocalDescription(pc, args.sdp);

   InfoLog(<< "local SDK answer: " << resip::Data(args.sdp.sdpString, args.sdp.sdpLen));

   JsonFunctionCall(mTransport, "provideSdpAnswer", "conversation", itRemConv->first, "sdpOffer", itRemConv->second.remoteOffer().sdpString, "sdpAnswer", args.sdp.sdpString);

   itRemConv->second.resetRemoteOffer();

   return kSuccess;
}

int SipConversationJsonProxyInterface::onSetLocalSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetLocalSessionDescriptionResult& args)
{
   return kSuccess;
}

int SipConversationJsonProxyInterface::onSetRemoteSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetRemoteSessionDescriptionResult& args)
{
   return kSuccess;
}

int SipConversationJsonProxyInterface::onError(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::ErrorEvent& args)
{
   return kSuccess;
}

int SipConversationJsonProxyInterface::handleSdpOfferAnswer(const rapidjson::Value& functionObjectVal)
{
   SipConversationHandle conversation;
   SipConversation::SdpOfferAnswerEvent args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));

   CPCAPI2::PeerConnection::SessionDescription sdp;
   sdp.sdpString = args.sdp.sdpString;
   sdp.sdpLen = args.sdp.sdpLen;
   sdp.sdpType = (CPCAPI2::PeerConnection::SessionDescription::SessionDescriptionType)args.sdp.sdpType;

   RemoteConvStateMap::iterator it = mRemoteConvStateMap.find(conversation);
   if (it != mRemoteConvStateMap.end())
   {
      if (sdp.sdpType == CPCAPI2::PeerConnection::SessionDescription::SessionDescriptionType_Answer &&
          it->second.offerAnswerState() == RemoteConvOfferAnswerState_HaveLocalOffer_WaitingForRemoteAnswer)
      {
         mPeerConnMgr->setRemoteDescription(it->second.peerConnection(), sdp);
         it->second.setOfferAnswerState(RemoteConvOfferAnswerState_Stable);
      }
   }
   return kSuccess;
}

int SipConversationJsonProxyInterface::handleLocalSdpOffer(const rapidjson::Value& functionObjectVal)
{
   SipConversationHandle conversation = 0;
   SipConversation::LocalSdpOfferEvent args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));

   RemoteConvStateMap::iterator itRemoteConv = mRemoteConvStateMap.find(conversation);
   if (itRemoteConv != mRemoteConvStateMap.end())
   {
      if (itRemoteConv->second.peerConnection() == -1)
      {
         itRemoteConv->second.peerConnection() = initPeerConnection(conversation, NULL);
      }

      for (auto it = args.localMediaInfo.begin(); it != args.localMediaInfo.end(); ++it)
      {
         CPCAPI2::PeerConnection::MediaInfo mediaInfo((CPCAPI2::PeerConnection::MediaType)it->mediaType, (CPCAPI2::PeerConnection::MediaDirection)it->mediaDirection, it->mediaStreamId);
         mPeerConnMgr->configureMedia(itRemoteConv->second.peerConnection(), it->mediaStreamId, mediaInfo);
      }

      if (args.localMediaInfo.size() > 0)
      {
         itRemoteConv->second.setOfferAnswerState(RemoteConvOfferAnswerState_CreatingLocalOffer);
         mPeerConnMgr->createOffer(itRemoteConv->second.peerConnection());
      }
   }
   return kSuccess;
}

int SipConversationJsonProxyInterface::handleLocalSdpAnswer(const rapidjson::Value& functionObjectVal)
{
   SipConversationHandle conversation = 0;
   SipConversation::LocalSdpAnswerEvent args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));

   CPCAPI2::PeerConnection::SessionDescription sdpOffer;
   sdpOffer.sdpString = args.sdpOffer.sdpString;
   sdpOffer.sdpLen = args.sdpOffer.sdpLen;
   sdpOffer.sdpType = (CPCAPI2::PeerConnection::SessionDescription::SessionDescriptionType)args.sdpOffer.sdpType;

   RemoteConvStateMap::iterator itRemoteConv = mRemoteConvStateMap.find(conversation);
   if (itRemoteConv != mRemoteConvStateMap.end())
   {
      itRemoteConv->second.setRemoteOffer(sdpOffer);

      if (itRemoteConv->second.peerConnection() == -1)
      {
         itRemoteConv->second.peerConnection() = initPeerConnection(conversation, &sdpOffer);
      }

      for (auto it = args.localMediaInfo.begin(); it != args.localMediaInfo.end(); ++it)
      {
         CPCAPI2::PeerConnection::MediaInfo mediaInfo((CPCAPI2::PeerConnection::MediaType)it->mediaType, (CPCAPI2::PeerConnection::MediaDirection)it->mediaDirection, it->mediaStreamId);
         mPeerConnMgr->configureMedia(itRemoteConv->second.peerConnection(), it->mediaStreamId, mediaInfo);
      }

      if (args.localMediaInfo.size() > 0)
      {
         mPeerConnMgr->setRemoteDescription(itRemoteConv->second.peerConnection(), sdpOffer);
         mPeerConnMgr->createAnswer(itRemoteConv->second.peerConnection());
      }
   }
   return kSuccess;
}

CPCAPI2::PeerConnection::PeerConnectionHandle SipConversationJsonProxyInterface::initPeerConnection(SipConversationHandle conversation, const CPCAPI2::PeerConnection::SessionDescription* sdpOffer)
{
   CPCAPI2::PeerConnection::PeerConnectionHandle pc = -1;

   PeerConnectionSettings bobSettings;
   bobSettings.certAor = "<EMAIL>";
   bobSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_None;
   bobSettings.sessionName = "-";

   std::map<SipConversationHandle, SipAccount::SipAccountHandle>::iterator itConv = mMapConvHandleToAccountHandle.find(conversation);
   if (itConv != mMapConvHandleToAccountHandle.end())
   {
      std::map<SipAccount::SipAccountHandle, SipConversationSettings>::iterator itConvSettings = mMapAcctToConvSettings.find(itConv->second);
      if (itConvSettings != mMapAcctToConvSettings.end())
      {
         bobSettings.sessionName = itConvSettings->second.sessionName;
         bobSettings.natTraversalMode = (PeerConnectionSettings::NatTraversalMode)itConvSettings->second.natTraversalMode;
         bobSettings.natTraversalServerHostname = itConvSettings->second.natTraversalServer;
         bobSettings.natTraversalServerPort = 3478;
         bobSettings.natTraversalServerType = (PeerConnectionSettings::NatTraversalServerType)itConvSettings->second.natTraversalServerType;
      }
   }

   if (sdpOffer != NULL)
   {
      resip::ParseBuffer pb(sdpOffer->sdpString, sdpOffer->sdpLen);
      resip::SdpContents resipSdpOffer;
      resipSdpOffer.parse(pb);

      if (resipSdpOffer.session().isConnection())
      {
         resip::Data localInterfaceAddr;
         CPCAPI2::IpHelpers::getPreferredLocalIpAddress(resip::Tuple(resipSdpOffer.session().connection().getAddress(), 53, resip::V4), localInterfaceAddr);
         bobSettings.localInterface = localInterfaceAddr.c_str();
      }
   }

   pc = mPeerConnMgr->createPeerConnection();

   mPeerConnMgr->setHandler(pc, (PeerConnectionHandler*)0xDEADBEEF);
   mPeerConnMgr->setDefaultSettings(pc, bobSettings);

   return pc;
}

int SipConversationJsonProxyInterface::handleConversationState(const rapidjson::Value& functionObjectVal)
{
   SipConversation::JsonProxyConversationStateEvent args;
   JsonDeserialize(functionObjectVal, "conversationStateArray", args.conversationState);

   cpc::vector<SipConversationState>::const_iterator itConvState = args.conversationState.begin();
   for (; itConvState != args.conversationState.end(); ++itConvState)
   {
      mMapConvHandleToAccountHandle[itConvState->conversation] = itConvState->account;
   }

   if (mStateHandler != NULL)
   {
      postCallback(makeFpCommand(SipConversationJsonProxyStateHandler::onConversationState, mStateHandler, 0, args));
   }
   return kSuccess;
}

int SipConversationJsonProxyInterface::handleConversationInitiated(const rapidjson::Value& functionObjectVal)
{
	SipConversationHandle conversation = -1;
	SipConversation::ConversationInitiatedEvent args;
	JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));

	// TODO Post callback to SipConversationHandlerInternal

	return kSuccess;
}

int SipConversationJsonProxyInterface::handleIncomingTransferRequest(const rapidjson::Value& functionObjectVal)
{
	SipConversationHandle conversation = -1;
	SipConversation::TransferRequestEvent args;
	JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));

	SipAccount::SipAccountHandle account = mMapConvHandleToAccountHandle[conversation];
	std::map<SipAccount::SipAccountHandle, SipConversationHandler*>::iterator it = mAppHandlers.find(account);
	if (it != mAppHandlers.end())
	{
		postCallback(makeFpCommand(SipConversationHandler::onIncomingTransferRequest, it->second, conversation, args));
	}
	return kSuccess;
}

int SipConversationJsonProxyInterface::handleIncomingRedirectRequest(const rapidjson::Value& functionObjectVal)
{
	SipConversationHandle conversation = -1;
	SipConversation::RedirectRequestEvent args;
	JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));

	SipAccount::SipAccountHandle account = mMapConvHandleToAccountHandle[conversation];
	std::map<SipAccount::SipAccountHandle, SipConversationHandler*>::iterator it = mAppHandlers.find(account);
	if (it != mAppHandlers.end())
	{
		postCallback(makeFpCommand(SipConversationHandler::onIncomingRedirectRequest, it->second, conversation, args));
	}
	return kSuccess;
}

int SipConversationJsonProxyInterface::handleIncomingTargetChangeRequest(const rapidjson::Value& functionObjectVal)
{
	SipConversationHandle conversation = -1;
	SipConversation::TargetChangeRequestEvent args;
	JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));

	SipAccount::SipAccountHandle account = mMapConvHandleToAccountHandle[conversation];
	std::map<SipAccount::SipAccountHandle, SipConversationHandler*>::iterator it = mAppHandlers.find(account);
	if (it != mAppHandlers.end())
	{
		postCallback(makeFpCommand(SipConversationHandler::onIncomingTargetChangeRequest, it->second, conversation, args));
	}
	return kSuccess;
}

int SipConversationJsonProxyInterface::handleIncomingHangupRequest(const rapidjson::Value& functionObjectVal)
{
	SipConversationHandle conversation = -1;
	SipConversation::HangupRequestEvent args;
	JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));

	SipAccount::SipAccountHandle account = mMapConvHandleToAccountHandle[conversation];
	std::map<SipAccount::SipAccountHandle, SipConversationHandler*>::iterator it = mAppHandlers.find(account);
	if (it != mAppHandlers.end())
	{
		postCallback(makeFpCommand(SipConversationHandler::onIncomingHangupRequest, it->second, conversation, args));
	}
	return kSuccess;
}

int SipConversationJsonProxyInterface::handleIncomingBroadsoftTalkRequest(const rapidjson::Value& functionObjectVal)
{
	SipConversationHandle conversation = -1;
	SipConversation::BroadsoftTalkEvent args;
	JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));

	SipAccount::SipAccountHandle account = mMapConvHandleToAccountHandle[conversation];
	std::map<SipAccount::SipAccountHandle, SipConversationHandler*>::iterator it = mAppHandlers.find(account);
	if (it != mAppHandlers.end())
	{
		postCallback(makeFpCommand(SipConversationHandler::onIncomingBroadsoftTalkRequest, it->second, conversation, args));
	}
	return kSuccess;
}

int SipConversationJsonProxyInterface::handleIncomingBroadsoftHoldRequest(const rapidjson::Value& functionObjectVal)
{
	SipConversationHandle conversation = -1;
	SipConversation::BroadsoftHoldEvent args;
	JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));

	SipAccount::SipAccountHandle account = mMapConvHandleToAccountHandle[conversation];
	std::map<SipAccount::SipAccountHandle, SipConversationHandler*>::iterator it = mAppHandlers.find(account);
	if (it != mAppHandlers.end())
	{
		postCallback(makeFpCommand(SipConversationHandler::onIncomingBroadsoftHoldRequest, it->second, conversation, args));
	}
	return kSuccess;
}

int SipConversationJsonProxyInterface::handleTransferProgress(const rapidjson::Value& functionObjectVal)
{
	SipConversationHandle conversation = -1;
	SipConversation::TransferProgressEvent args;
	JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));

	SipAccount::SipAccountHandle account = mMapConvHandleToAccountHandle[conversation];
	std::map<SipAccount::SipAccountHandle, SipConversationHandler*>::iterator it = mAppHandlers.find(account);
	if (it != mAppHandlers.end())
	{
		postCallback(makeFpCommand(SipConversationHandler::onTransferProgress, it->second, conversation, args));
	}
	return kSuccess;
}

int SipConversationJsonProxyInterface::handleConversationStatisticsUpdated(const rapidjson::Value& functionObjectVal)
{
	SipConversationHandle conversation = -1;
	SipConversation::ConversationStatisticsUpdatedEvent args;
	JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));

	SipAccount::SipAccountHandle account = mMapConvHandleToAccountHandle[conversation];
	std::map<SipAccount::SipAccountHandle, SipConversationHandler*>::iterator it = mAppHandlers.find(account);
	if (it != mAppHandlers.end())
	{
		postCallback(makeFpCommand(SipConversationHandler::onConversationStatisticsUpdated, it->second, conversation, args));
	}
	return kSuccess;
}

int SipConversationJsonProxyInterface::handleAudioDeviceLevelChange(const rapidjson::Value& functionObjectVal)
{
	SipConversationHandle conversation = -1;
	SipConversation::ConversationAudioDeviceLevelChangeEvent args;
	JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));

	SipAccount::SipAccountHandle account = mMapConvHandleToAccountHandle[conversation];
	std::map<SipAccount::SipAccountHandle, SipConversationHandler*>::iterator it = mAppHandlers.find(account);
	if (it != mAppHandlers.end())
	{
		postCallback(makeFpCommand(SipConversationHandler::onAudioDeviceLevelChange, it->second, conversation, args));
	}
	return kSuccess;
}

int SipConversationJsonProxyInterface::handleError(const rapidjson::Value& functionObjectVal)
{
	SipConversationHandle conversation = -1;
	SipConversation::ErrorEvent args;
	JsonDeserialize(functionObjectVal, JSON_VALUE(conversation), JSON_VALUE(args));

	SipAccount::SipAccountHandle account = mMapConvHandleToAccountHandle[conversation];
	std::map<SipAccount::SipAccountHandle, SipConversationHandler*>::iterator it = mAppHandlers.find(account);
	if (it != mAppHandlers.end())
	{
		postCallback(makeFpCommand(SipConversationHandler::onError, it->second, conversation, args));
	}
	return kSuccess;
}


}
}
#endif
