#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1)

#include "SipFileTransferStateImpl.h"
#include "SipFileTransferManagerInterface.h"
#include "../phone/PhoneInterface.h"

using namespace CPCAPI2::SipFileTransfer;

SipFileTransferStateImpl::SipFileTransferStateImpl(SipFileTransferManagerInterface* convManIf)
   : mConvManIf(convManIf)
{
}

SipFileTransferStateImpl::~SipFileTransferStateImpl()
{
   //mConvManIf->removeSdkObserver(this);
}

void SipFileTransferStateImpl::Release()
{
   delete this;
}

int SipFileTransferStateImpl::getState(SipFileTransferHandle h, SipFileTransferState& fileTransferState)
{
   std::map<SipFileTransferHandle,SipFileTransferState>::iterator it = mStateMap.find(h);
   if (it != mStateMap.end())
   {
      fileTransferState = it->second;
      return kSuccess;
   }
   return kError;
}

int SipFileTransferStateImpl::onNewFileTransfer( const SipFileTransferHandle& fileTransfer, const NewFileTransferEvent& args )
{
   SipFileTransferState state;
   state.account           = args.account;
   state.fileTransferState = args.fileTransferState;
   state.fileTransferType  = args.fileTransferType;
   state.endReason         = FileTransferEndReason_Unknown;
   state.remoteAddress     = args.remoteAddress;
   state.remoteDisplayName = args.remoteDisplayName;

   mStateMap[ fileTransfer ] = state; // default copy ctor (primitive types)
   return kSuccess;
}

int SipFileTransferStateImpl::onFileTransferConfigured( const SipFileTransferHandle& fileTransfer, const FileTransferConfiguredEvent& event )
{
   std::map<SipFileTransferHandle,SipFileTransferState>::iterator it = mStateMap.find(fileTransfer);
   if( it != mStateMap.end() )
   {
      it->second.fileTransferState = event.fileTransferState;
      it->second.fileTransferType  = event.fileTransferType;
      it->second.fileItems         = event.fileItems;
   }
   return kSuccess;
}

int SipFileTransferStateImpl::onFileTransferEnded( const SipFileTransferHandle& fileTransfer, const FileTransferEndedEvent& args )
{
   std::map<SipFileTransferHandle,SipFileTransferState>::iterator it = mStateMap.find(fileTransfer);
   if (it != mStateMap.end())
   {
      //it->second.account = args.account;
      it->second.fileTransferState = args.fileTransferState;
      //it->second.fileTransferType = args.fileTransferType;
      it->second.endReason = args.endReason;
      //it->second.remoteAddress = args.remoteAddress;
      //it->second.remoteDisplayName = args.remoteDisplayName;
   }
   return kSuccess;
}

/**
 * Progress indication (in percent) of an ongoing file transfer item
 */
int SipFileTransferStateImpl::onFileTransferItemProgress( const SipFileTransferHandle& fileTransfer, const FileTransferItemProgressEvent& args )
{
   std::map<SipFileTransferHandle,SipFileTransferState>::iterator it = mStateMap.find(fileTransfer);
   if (it != mStateMap.end())
   {
      SipFileTransferItems::iterator itItem = it->second.fileItems.begin();
      for (; itItem != it->second.fileItems.end(); ++itItem)
      {
         if( itItem->handle != args.fileTransferItem )
            continue;

         itItem->percentComplete = args.percent;
      }
   }
   return kSuccess;
}

/**
 * Invoked by the SDK when a file (item) has finished transfering
 */
int SipFileTransferStateImpl::onFileTransferItemEnded( const SipFileTransferHandle& fileTransfer, const FileTransferItemEndedEvent& args )
{
   std::map<SipFileTransferHandle,SipFileTransferState>::iterator it = mStateMap.find(fileTransfer);
   if (it != mStateMap.end())
   {
      SipFileTransferItems::iterator itItem = it->second.fileItems.begin();
      for (; itItem != it->second.fileItems.end(); ++itItem)
      {
         if( itItem->handle != args.fileTransferItem )
            continue;

         // TODO: what to set here? Do we need file transfer item state as part of details?
      }
   }
   return kSuccess;
}

int SipFileTransferStateImpl::onError(const SipFileTransferHandle& subscription, const ErrorEvent& args)
{
   return kSuccess;
}

#endif