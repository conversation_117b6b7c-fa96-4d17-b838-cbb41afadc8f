#pragma once

#ifndef __CPC_TURNASYNCUDPSOCKET_H__
#define __CPC_TURNASYNCUDPSOCKET_H__

#include <rutil/compat.hxx>
#include <reTurn/client/TurnAsyncUdpSocket_no_asio.hxx>
#include <reTurn/StunMessage.hxx>

namespace reTurn
{
   class StunMessage;
   class TurnAsyncSocketHandler;
}

namespace CPCAPI2
{
   class CPCTurnAsyncUdpSocket : public reTurn::TurnAsyncUdpSocket
   {
   public:
      explicit CPCTurnAsyncUdpSocket(
         resip::HighPerfReactor& reactor,
         resip::Resolver <resip::HighPerfReactor>& resolver,
         reTurn::TurnAsyncSocketHandler* turnAsyncSocketHandler,
         const resip::Data& address, 
         unsigned short port);

   protected:
      virtual reTurn::StunMessage* createNewStunMessage(UInt16 stunclass, UInt16 method, bool addAuthInfo=true);
   };
}

#endif /* __CPC_TURNASYNCUDPSOCKET_H__ */