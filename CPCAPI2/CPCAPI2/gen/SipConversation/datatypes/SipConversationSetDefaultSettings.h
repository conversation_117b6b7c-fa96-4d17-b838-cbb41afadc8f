#include "jsonrpc/StringSerialization.h"
#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>
#include "SipConversationSettings.h"

#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSETDEFAULTSETTINGS_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSETDEFAULTSETTINGS_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationSetDefaultSettings
  {
  public:
    int32_t phoneHandle;
    int32_t accountHandle;
    SipConversationSettings settings;

    SipConversationSetDefaultSettings() = default;

    SipConversationSetDefaultSettings(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationSetDefaultSettings& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["accountHandle"] = o.accountHandle;
     j["settings"] = o.settings;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationSetDefaultSettings& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.accountHandle = j.at("accountHandle");
     o.settings = j.at("settings");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSETDEFAULTSETTINGS_H
