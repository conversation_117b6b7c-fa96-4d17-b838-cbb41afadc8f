#pragma once

#if !defined(CPCAPI2_CLOUD_CONNECTOR_IMPL_H)
#define CPCAPI2_CLOUD_CONNECTOR_IMPL_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "cloudconnector/CloudConnector.h"
#include "jsonapi/JsonApiClient.h"
#include "cloudconnector/ServiceDesc.h"
#include "CloudServerConnectionObserver.h"
#include "phone/NetworkChangeManager.h"

#include <rutil/Data.hxx>
#include <map>
#include <memory>

namespace CPCAPI2
{

class PhoneInterface;

namespace CloudConnector
{

class CloudConnectorInterface;
class CloudServerConnection;
struct CloudServerConnStatusEvent;
   
enum Restriction
{
   Restriction_FromUser,
   Restriction_FromNetwork
};

class CloudConnectorImpl : public CloudServerConnectionObserver,
                           public std::enable_shared_from_this<CloudConnectorImpl>
{

public:

   CloudConnectorImpl(CloudConnectorInterface* ccif, CloudConnectorHandle h);
   virtual ~CloudConnectorImpl();

   int setConnectionSettings(const CloudConnectorSettings& settings);
   int requestService(const ServiceDescription& service);
   int connectToServices();
   int disconnectService(const ServiceDescription& service);
   int addUser(const AddUserRequest& req);

   void destroy();
   void logout();

   // CloudServerConnectionObserver
   virtual void handleCloudServerConnectionStatusChanged(CloudServerConnection* conn, const CloudServerConnStatusEvent& args);
   
   void addRestriction(Restriction restriction);
   void removeRestriction(Restriction restriction);
   std::set<NetworkTransport>& restrictedNetworks() { return mRestrictedNetworks; }
   const bool isRestrictedNetwork(const NetworkTransport currentNetworkTransport) const;
   const bool isRestricted() const { return !mRestrictions.empty(); }

private:

   void releaseConnections(bool sendStatus = true, ServiceConnectionStatus reason = ServiceConnectionStatus::ServiceConnectionStatus_ConnFailure);
   void getServicesForUri(cpc::string uri, cpc::vector<ServiceDescription>& services);
   bool getUriForService(ServiceDesc service, cpc::string& uri);
   bool getUriForService(ServiceDescription service, cpc::string& uri);
   void getServicesForConnection(cpc::string uri, std::set<ServiceDesc>& services);
   void getServicesForConnection(cpc::string uri, cpc::vector<ServiceDescription>& services);
   
   CloudConnectorInterface* mInterface;
   CloudConnectorHandle mHandle;
   CloudConnectorSettings mSettings;
   
   std::set<Restriction> mRestrictions;
   std::set<NetworkTransport> mRestrictedNetworks;
   size_t mReconnectCount;
   resip::Data mCurrentInterfaceIPAddress;

   std::map<ServiceDesc, cpc::string> mMapServiceToUri;

   std::map<cpc::string, std::shared_ptr<CloudServerConnection>> mMapUriToConnection;
   
   /** For debug use only, cloud connector will return the loopback url for the server address, instead of value returned from orchestrator */
   std::map<ServiceDesc, cpc::string> mMapServiceToLoopbackUri;

   resip::MultiReactor mWebRequestThread;

};

}

}

#endif // CPCAPI2_CLOUD_CONNECTOR_IMPL_H

