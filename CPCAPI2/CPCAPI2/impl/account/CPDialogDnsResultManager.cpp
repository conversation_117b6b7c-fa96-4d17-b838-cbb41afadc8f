#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACCOUNT_MODULE == 1)
#include "CPDialogDnsResultManager.h"

#include "../util/cpc_logger.h"
#include "../util/SipHelpers.h"

#include "SipAccountInterface.h"
#include "../call/SipAVConversationManagerInterface.h"
#include "../util/DnsClient.h"
#include "../util/StunClient.h"

#include <resip/stack/DnsResultMessage.hxx>
#include <resip/stack/ExtensionHeader.hxx>
#include <resip/dum/ClientRegistration.hxx>
#include <resip/dum/ClientAuthManager.hxx>
#include <rutil/dns/DnsStub.hxx>
#include <rutil/TransportType.hxx>


#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_ACCOUNT
#define DNS_RESET_HISTORY_DEPTH_IN_SECONDS 60*10
#define DNS_RESET_MAXIMUM_RESET_IN_DEPTH 10
#define DNS_RESET_PRE_REGISTRATION_MINIMUM_IN_SECONDS 10
#define PROBE_TIMER_ID 1
#define REFRESH_TIMER_ID 2
#define PROBE_TIMEOUT_MSECS 2000

bool gVerboseLoggingEnabled = false;
#define DnsLog(args) if (gVerboseLoggingEnabled) { StackLog(args); }


namespace CPCAPI2
{

namespace SipAccount
{

CPDialogDnsResultManager::DnsResetStateFactory::DnsResetStateFactory(CPDialogDnsResultManager* manager) :
mManager(manager)
{
   create();
}

CPDialogDnsResultManager::DnsResetStateFactory::~DnsResetStateFactory()
{
   for (DnsResetStates::iterator i = mStates.begin(); i != mStates.end(); ++i)
   {
      delete (i->second);
   }
   mStates.clear();
}

void CPDialogDnsResultManager::DnsResetStateFactory::create()
{
   if (mStates.size() > 0)
   {
      ErrLog(<< "CPDialogDnsResultManager::DnsResetStateFactory::create(): " << this << " manager: " << mManager << " account: " << " state factory already initialized");
      return;
   }

   mStates[Dns_Reset_Startup_State] = create(Dns_Reset_Startup_State);
   mStates[Dns_Reset_Initialized_State] = create(Dns_Reset_Initialized_State);
   mStates[Dns_Reset_Primary_Set_State] = create(Dns_Reset_Primary_Set_State);
   mStates[Dns_Reset_Registered_Primary_State] = create(Dns_Reset_Registered_Primary_State);
   mStates[Dns_Reset_Registered_Secondary_State] = create(Dns_Reset_Registered_Secondary_State);
   mStates[Dns_Reset_Probing_State] = create(Dns_Reset_Probing_State);
   mStates[Dns_Reset_Disabled_State] = create(Dns_Reset_Disabled_State);
   mStates[Dns_Reset_Invalid_State] = create(Dns_Reset_Invalid_State);

   for (DnsResetStates::iterator i = mStates.begin(); i != mStates.end(); ++i)
   {
      DnsLog(<< "CPDialogDnsResultManager::DnsResetStateFactory::create(): " << DnsResetState::getName(i->first) << " : " << i->first)
   }
}

CPDialogDnsResultManager::DnsResetState* CPDialogDnsResultManager::DnsResetStateFactory::create(DnsResetStateType type)
{
   DnsResetState* state = NULL;
   switch (type)
   {
      case Dns_Reset_Startup_State: state = new DnsResetStartupState(mManager); break;
      case Dns_Reset_Initialized_State: state = new DnsResetInitializedState(mManager); break;
      case Dns_Reset_Primary_Set_State: state = new DnsResetPrimarySetState(mManager); break;
      case Dns_Reset_Registered_Primary_State: state = new DnsResetRegisteredPrimaryState(mManager); break;
      case Dns_Reset_Registered_Secondary_State: state = new DnsResetRegisteredSecondaryState(mManager); break;
      case Dns_Reset_Probing_State: state = new DnsResetProbingState(mManager); break;
      case Dns_Reset_Disabled_State: state = new DnsResetDisabledState(mManager); break;
      case Dns_Reset_Invalid_State: state = new DnsResetInvalidState(mManager); break;
      default: break;
   }

   return state;
}

CPDialogDnsResultManager::DnsResetState* CPDialogDnsResultManager::DnsResetStateFactory::getState(DnsResetStateType type)
{
   return mStates[type];
}

CPDialogDnsResultManager::DnsResetState::DnsResetState(CPDialogDnsResultManager* manager, DnsResetStateType state) :
mManager(manager),
mAccount(manager->getAccount()),
mState(state),
mHandle(mAccount->getHandle())
{
}

CPDialogDnsResultManager::DnsResetState::~DnsResetState()
{
}

resip::Tuple& CPDialogDnsResultManager::DnsResetState::getPreferredTarget()
{
   return mManager->getPreferredTarget();
}

resip::Tuple& CPDialogDnsResultManager::DnsResetState::getCurrentTarget()
{
   return mManager->getCurrentTarget();
}

bool CPDialogDnsResultManager::DnsResetState::isDnsResetEnabled()
{
   return mManager->isDnsResetEnabled();
}

bool CPDialogDnsResultManager::DnsResetState::isAuthResetEnabled()
{
   return mManager->isAuthResetEnabled();
}

void CPDialogDnsResultManager::DnsResetState::changeState(DnsResetStateType state)
{
   DnsLog(<< "CPDialogDnsResultManager::DnsResetState::changeState(): " << this << " account: " << mHandle << " manager: " << mManager << " changing state from: " << getName() << " to: " << getName(state))
   mManager->changeState(state);
}

DnsResetStateType CPDialogDnsResultManager::DnsResetState::getType()
{
   return mState;
}

std::string CPDialogDnsResultManager::DnsResetState::getName()
{
   return (DnsResetState::getName(mState));
}

std::string CPDialogDnsResultManager::DnsResetState::getName(DnsResetStateType type)
{
   std::string name = "Invalid_State";
   switch (type)
   {
      case Dns_Reset_Startup_State: name = "Dns_Reset_Startup_State"; break;
      case Dns_Reset_Initialized_State: name = "Dns_Reset_Initialized_State"; break;
      case Dns_Reset_Primary_Set_State: name = "Dns_Reset_Primary_Set_State"; break;
      case Dns_Reset_Registered_Primary_State: name = "Dns_Reset_Registered_Primary_State"; break;
      case Dns_Reset_Registered_Secondary_State: name = "Dns_Reset_Registered_Secondary_State"; break;
      case Dns_Reset_Probing_State: name = "Dns_Reset_Probing_State"; break;
      case Dns_Reset_Disabled_State: name = "Dns_Reset_Disabled_State"; break;
      case Dns_Reset_Invalid_State: name = "Dns_Reset_Invalid_State"; break;
      default: break;
   }

   return name;
}

// SipAccountHandlerInternal

int CPDialogDnsResultManager::DnsResetState::onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args)
{
   StackLog(<< "CPDialogDnsResultManager::DnsResetState::onAccountStatusChanged(): " << this << " account: " << account << " manager: " << mManager << " state: " << getName() << " status: " << args.accountStatus);

   if ((args.accountStatus == SipAccountStatusChangedEvent::Status_Unregistered) || (args.accountStatus == SipAccountStatusChangedEvent::Status_Unregistering))
   {
      InfoLog(<< "CPDialogDnsResultManager::DnsResetState::onAccountStatusChanged(): account: " << account << " manager: " << mManager << " reset state as account is being unregistered" << " state: " << getName());
      changeState(Dns_Reset_Disabled_State);
   }

   return kSuccess;
}

int CPDialogDnsResultManager::DnsResetState::onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args)
{
   DebugLog(<< "CPDialogDnsResultManager::DnsResetState::onError(): " << this << " manager: " << mManager << " account: " << mHandle << " state: " << getName());
   changeState(Dns_Reset_Invalid_State);
   return kSuccess;
}

int CPDialogDnsResultManager::DnsResetState::onAccountConfigured(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountConfiguredEvent& args)
{
   StackLog(<< "CPDialogDnsResultManager::DnsResetState::onAccountConfigured() account: " << account << " manager: " << mManager << " handling account configuration change to settings for network type: " << args.curTransport << " state: " << getName());
   const CPCAPI2::SipAccount::SipAccountSettings& settings(args.settings.size() > (size_t)(args.curTransport) ? args.settings.at(args.curTransport) : args.settings.at(CPCAPI2::NetworkTransport::TransportNone));
   mManager->resetConfig(settings);
   if (isDnsResetEnabled())
   {
      changeState(Dns_Reset_Startup_State);
   }
   else
   {
      changeState(Dns_Reset_Disabled_State);
   }
   return kSuccess;
}

int CPDialogDnsResultManager::DnsResetState::onAccountEnabled(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountEnabledEvent& args)
{
   StackLog(<< "CPDialogDnsResultManager::DnsResetState::(): " << this << " manager: " << mManager << " account: " << mHandle << " state: " << getName());
   return kSuccess;
}

int CPDialogDnsResultManager::DnsResetState::onNetworkChange(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountNetworkChangeEvent& args)
{
   DebugLog(<< "CPDialogDnsResultManager::DnsResetState::onNetworkChange(): " << this << " manager: " << mManager << " account: " << mHandle << " state: " << getName() << " handling network change to network type: " << args.curTransport);
   mManager->resetConfig(args.settings);
   if (isDnsResetEnabled())
   {
      resip::SharedPtr<resip::DialogUsageManager>& dum = mManager->getDum();
      if (dum)
      {
         changeState(Dns_Reset_Initialized_State);
      }
      else
      {
         changeState(Dns_Reset_Startup_State);
      }
   }
   else
   {
      changeState(Dns_Reset_Disabled_State);
   }
   return kSuccess;
}

// DialogDnsResultHandler

void CPDialogDnsResultManager::DnsResetState::onDnsResult(const resip::DnsResultDataMessage& result)
{
   if (result.getResults().size() > 0)
   {
      mManager->getCurrentTarget() = result.getResults()[0];
      DebugLog(<< "CPDialogDnsResultManager::DnsResetState::onDnsResult(): account: " << mHandle << " manager: " << mManager << " current-target: "
               << getCurrentTarget() << " preferred-target: " << getPreferredTarget() << " state: " << getName());
   }
   else
   {
      DebugLog(<< "CPDialogDnsResultManager::DnsResetState::onDnsResult(): account: " << mHandle << " manager: " << mManager << " current-target: "
               << getCurrentTarget() << " preferred-target: " << getPreferredTarget() << " dns update contains no results" << " state: " << getName() );
   }
   return;
}

// SipAccountAwareFeature

int CPDialogDnsResultManager::DnsResetState::adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile)
{
   StackLog(<< "CPDialogDnsResultManager::DnsResetState::adornMasterProfile(): " << this << " manager: " << mManager << " account " << mHandle << " state: " << getName());
   return kSuccess;
}

int CPDialogDnsResultManager::DnsResetState::addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig)
{
   StackLog(<< "CPDialogDnsResultManager::DnsResetState::addHandlers(): " << this << " manager: " << mManager << " account " << mHandle << " state: " << getName());
   return kSuccess;
}

int CPDialogDnsResultManager::DnsResetState::onRegistrationSuccess(const SipRegistrationSuccessEvent& args)
{
   StackLog(<< "CPDialogDnsResultManager::DnsResetState::onRegistrationSuccess(): " << this << " manager: " << mManager << " account " << mHandle << " state: " << getName());
   return kSuccess;
}

int CPDialogDnsResultManager::DnsResetState::onDumBeingDestroyed()
{
   DebugLog(<< "CPDialogDnsResultManager::DnsResetState::onDumBeingDestroyed(): " << this << " manager: " << mManager << " account " << mHandle << " state: " << getName());
   return kSuccess;
}

void CPDialogDnsResultManager::DnsResetState::release()
{
   DebugLog(<< "CPDialogDnsResultManager::DnsResetState::release(): " << this << " manager: " << mManager << " account " << mHandle << " state: " << getName());
}

CPDialogDnsResultManager::DnsResetStartupState::DnsResetStartupState(CPDialogDnsResultManager* manager) :
DnsResetState(manager, DnsResetStateType::Dns_Reset_Startup_State)
{
}

CPDialogDnsResultManager::DnsResetStartupState::~DnsResetStartupState()
{
}

void CPDialogDnsResultManager::DnsResetStartupState::onEntry()
{
   DnsLog(<< "CPDialogDnsResultManager::DnsResetStartupState::onEntry(): " << this << " account: " << mHandle << " manager: " << mManager)
}

void CPDialogDnsResultManager::DnsResetStartupState::onExit()
{
   DnsLog(<< "CPDialogDnsResultManager::DnsResetStartupState::onExit(): " << this << " account: " << mHandle << " manager: " << mManager)
}

// SipAccountHandlerInternal

int CPDialogDnsResultManager::DnsResetStartupState::onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args)
{
   // Ignore status updates based on stale dialogs, as the dialog has not yet been initialized
   // (e.g. with enable/disable or applySettings, we will get unregisteration status updates for the previous registration)
   StackLog(<< "CPDialogDnsResultManager::DnsResetStartupState::onDnsResult(): " << this << " account: " << mHandle << " manager: " << mManager << " status: " << args.accountStatus << " dns reset enabled: " << isDnsResetEnabled() << " ignoring status update as the dialog is not initialized");
   return kSuccess;
}

int CPDialogDnsResultManager::DnsResetStartupState::onAccountConfigured(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountConfiguredEvent& args)
{
   const CPCAPI2::SipAccount::SipAccountSettings& settings(args.settings.size() > (size_t)(args.curTransport) ? args.settings.at(args.curTransport) : args.settings.at(CPCAPI2::NetworkTransport::TransportNone));

   DebugLog(<< "CPDialogDnsResultManager::DnsResetStartupState::onAccountConfigured() account: " << account << " manager: " << mManager << " handling account configuration change to settings for network type: " << args.curTransport);
   mManager->resetConfig(settings);
   if (!isDnsResetEnabled())
   {
      changeState(Dns_Reset_Disabled_State);
   }
   return kSuccess;
}

int CPDialogDnsResultManager::DnsResetStartupState::onNetworkChange(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountNetworkChangeEvent& args)
{
   DebugLog(<< "CPDialogDnsResultManager::DnsResetStartupState::onNetworkChange() account: " << account << " manager: " << mManager << " handling network change to network type: " << args.curTransport);
   mManager->resetConfig(args.settings);
   if (!isDnsResetEnabled())
   {
      changeState(Dns_Reset_Disabled_State);
   }
   return kSuccess;
}

// SipAccountAwareFeature
int CPDialogDnsResultManager::DnsResetStartupState::addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig)
{
   DebugLog(<< "CPDialogDnsResultManager::DnsResetStartupState::addHandlers(): " << this << " account: " << mHandle << " manager: " << mManager);
   changeState(Dns_Reset_Initialized_State);
   return kSuccess;
}

CPDialogDnsResultManager::DnsResetInitializedState::DnsResetInitializedState(CPDialogDnsResultManager* manager) :
DnsResetState(manager, DnsResetStateType::Dns_Reset_Initialized_State)
{
}

CPDialogDnsResultManager::DnsResetInitializedState::~DnsResetInitializedState()
{
}

void CPDialogDnsResultManager::DnsResetInitializedState::onEntry()
{
   DnsLog(<< "CPDialogDnsResultManager::DnsResetInitializedState::onEntry(): " << this << " account: " << mHandle << " manager: " << mManager)
}

void CPDialogDnsResultManager::DnsResetInitializedState::onExit()
{
   DnsLog(<< "CPDialogDnsResultManager::DnsResetInitializedState::onExit(): " << this << " account: " << mHandle << " manager: " << mManager)
}

// DialogDnsResultHandler

void CPDialogDnsResultManager::DnsResetInitializedState::onDnsResult(const resip::DnsResultDataMessage& result)
{
   // TODO: Do we need to ensure that dns queries triggered by other requests are not impacting dns result manager state,
   // as really all we want is the dns results from registration requests

   StackLog(<< "CPDialogDnsResultManager::DnsResetInitializedState::onDnsResult(): " << this << " account: " << mHandle << " manager: " << mManager << " result: " << result << " dns reset enabled: " << isDnsResetEnabled());

   if (result.getResults().size() > 0)
   {
      mManager->getCurrentTarget() = result.getResults()[0];
      mManager->getPreferredTarget() = result.getResults()[0];
      DebugLog(<< "CPDialogDnsResultManager::DnsResetInitializedState::onDnsResult(): account: " << mHandle << " manager: " << mManager << " current-target: "
         << getCurrentTarget() << " preferred-target: " << getPreferredTarget());
      changeState(Dns_Reset_Primary_Set_State);
   }
   else
   {
      DebugLog(<< "CPDialogDnsResultManager::DnsResetInitializedState::onDnsResult(): account: " << mHandle << " manager: " << mManager << " current-target: "
         << getCurrentTarget() << " preferred-target: " << getPreferredTarget() << " dns update contains no results");
   }
}

CPDialogDnsResultManager::DnsResetPrimarySetState::DnsResetPrimarySetState(CPDialogDnsResultManager* manager) :
DnsResetState(manager, DnsResetStateType::Dns_Reset_Primary_Set_State)
{
}

CPDialogDnsResultManager::DnsResetPrimarySetState::~DnsResetPrimarySetState()
{
}

void CPDialogDnsResultManager::DnsResetPrimarySetState::onEntry()
{
   DnsLog(<< "CPDialogDnsResultManager::DnsResetPrimarySetState::onEntry(): " << this << " account: " << mHandle << " manager: " << mManager)
}

void CPDialogDnsResultManager::DnsResetPrimarySetState::onExit()
{
   DnsLog(<< "CPDialogDnsResultManager::DnsResetPrimarySetState::onExit(): " << this << " account: " << mHandle << " manager: " << mManager)
}

// SipAccountHandlerInternal

int CPDialogDnsResultManager::DnsResetPrimarySetState::onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args)
{
   StackLog(<< "CPDialogDnsResultManager::DnsResetPrimarySetState::onAccountStatusChanged(): " << this << " account: " << account << " manager: " << mManager << " status: " << args.accountStatus);

   if (SipAccountStatusChangedEvent::Status_Registered == args.accountStatus)
   {
      mManager->resetConfig(mAccount->getSettings(), false);

      if (mAccount->getSipServerIpFromLastReg(getCurrentTarget()))
      {
         DebugLog(<< "CPDialogDnsResultManager::DnsResetPrimarySetState::onAccountStatusChanged(): account: " << account << " manager: " << mManager << " last server was " << getCurrentTarget());

         if (getCurrentTarget() == getPreferredTarget())
         {
            changeState(Dns_Reset_Registered_Primary_State);
         }
         else
         {
            changeState(Dns_Reset_Registered_Secondary_State);
         }
      }
      else
      {
         ErrLog(<< "CPDialogDnsResultManager::DnsResetPrimarySetState::onAccountStatusChanged(): account: " << account << " manager: " << mManager << " could not get the IP address of last registration");
         // Treating this as critical error and stopping dns resets
         changeState(Dns_Reset_Invalid_State);
      }
   }
   else
   {
      // Check to see if the dialog is ready, as we do not want to handle status updates based on stale dialogs
      // (e.g. with enable/disable or applySettings, we will get unregisteration status updates for the previous registration)
      bool dialogReady = (mManager->getDum().get());
      DebugLog(<< "CPDialogDnsResultManager::DnsResetPrimarySetState::onAccountStatusChanged(): account: " << account << " manager: " << mManager << " current state: " << args.accountStatus << " dns reset enabled: " << isDnsResetEnabled() << " dialog initialized: " << dialogReady);
      if (dialogReady && ((args.accountStatus == SipAccountStatusChangedEvent::Status_Unregistered) || (args.accountStatus == SipAccountStatusChangedEvent::Status_Unregistering)))
      {
         InfoLog(<< "CPDialogDnsResultManager::DnsResetPrimarySetState::onAccountStatusChanged(): account: " << account << " manager: " << mManager << " reset state as account is being unregistered");
         mManager->reset();
         changeState(Dns_Reset_Disabled_State);
      }
   }

   return kSuccess;
}

CPDialogDnsResultManager::DnsResetRegisteredPrimaryState::DnsResetRegisteredPrimaryState(CPDialogDnsResultManager* manager) :
DnsResetState(manager, DnsResetStateType::Dns_Reset_Registered_Primary_State)
{
}

CPDialogDnsResultManager::DnsResetRegisteredPrimaryState::~DnsResetRegisteredPrimaryState()
{
}

void CPDialogDnsResultManager::DnsResetRegisteredPrimaryState::onEntry()
{
   // Account is registered to preferred dns result
   DnsLog(<< "CPDialogDnsResultManager::DnsResetRegisteredPrimaryState::onEntry(): " << this << " account: " << mHandle << " manager: " << mManager)
}

void CPDialogDnsResultManager::DnsResetRegisteredPrimaryState::onExit()
{
   DnsLog(<< "CPDialogDnsResultManager::DnsResetRegisteredPrimaryState::onExit(): " << this << " account: " << mHandle << " manager: " << mManager)
}

CPDialogDnsResultManager::DnsResetRegisteredSecondaryState::DnsResetRegisteredSecondaryState(CPDialogDnsResultManager* manager) :
DnsResetState(manager, DnsResetStateType::Dns_Reset_Registered_Secondary_State),
mRefreshTimer(manager->getReactor())
{
}

CPDialogDnsResultManager::DnsResetRegisteredSecondaryState::~DnsResetRegisteredSecondaryState()
{
   mRefreshTimer.cancel();
}

void CPDialogDnsResultManager::DnsResetRegisteredSecondaryState::onEntry()
{
   DnsLog(<< "CPDialogDnsResultManager::DnsResetRegisteredSecondaryState::onEntry(): " << this << " account: " << mHandle << " manager: " << mManager)
   // If in this state, it indicates that:
   // - dns reset is enabled
   // - account is registered
   // - preferred target is not active, i.e. secondary target has been selected
   // - preferred target is populated
   resetRefreshTimer();
}

void CPDialogDnsResultManager::DnsResetRegisteredSecondaryState::onExit()
{
   DnsLog(<< "CPDialogDnsResultManager::DnsResetRegisteredSecondaryState::onExit(): " << this << " account: " << mHandle << " manager: " << mManager)
   mRefreshTimer.cancel();
}

// resip::DeadlineTimerHandler

void CPDialogDnsResultManager::DnsResetRegisteredSecondaryState::onTimer(unsigned short timerId, void* appState)
{
   if (timerId == REFRESH_TIMER_ID)
   {
      mRefreshTimer.cancel();
      if (shouldResetDns())
      {
         StackLog(<< "CPDialogDnsResultManager::DnsResetRegisteredSecondaryState::onTimer(): " << this << " account: " << mHandle << " manager: " << mManager << " timer-id: " << timerId << " initiate probe to check if preferred target is active");
         changeState(Dns_Reset_Probing_State);
      }
      else
      {
         StackLog(<< "CPDialogDnsResultManager::DnsResetRegisteredSecondaryState::onTimer(): " << this << " account: " << mHandle << " manager: " << mManager << " timer-id: " << timerId << " reset refresh timer to next registration");
         resetRefreshTimer();
      }
   }
   else
   {
      DebugLog(<< "CPDialogDnsResultManager::DnsResetRegisteredSecondaryState::onTimer(): " << this << " account: " << mHandle << " manager: " << mManager << " invalid timer-id: " << timerId);
   }
}

bool CPDialogDnsResultManager::DnsResetRegisteredSecondaryState::shouldResetDns()
{
   bool reset = false;

   SipConversation::SipAVConversationManagerInterface* conversationInterface = dynamic_cast<SipConversation::SipAVConversationManagerInterface*>(SipConversation::SipConversationManager::getInterface(mAccount->getPhone()));
   int callCount = 0;
   if (conversationInterface != NULL)
   {
      callCount = conversationInterface->getCallCount();
   }

   int resetCount = mManager->getHistoricalChangeCount();

   if ((callCount == 0) && (resetCount <= DNS_RESET_MAXIMUM_RESET_IN_DEPTH))
   {
      reset = true;
   }

   DebugLog(<< "CPDialogDnsResultManager::DnsResetRegisteredSecondaryState::shouldResetDns(): dns reset enabled for registered account " << mHandle
            << " manager: " << mManager << " historical dns-reset count: " << resetCount << " call-count: " << callCount << " current target: " << mManager->getCurrentTarget()
            << " preferred target: " << mManager->getPreferredTarget() << (reset ? " should proceed with ping test" : " conditions not met for dns-reset"));

   return reset;
}

void CPDialogDnsResultManager::DnsResetRegisteredSecondaryState::resetRefreshTimer()
{
   if (mAccount->getClientRegistrationHandle().isValid())
   {
      StackLog(<< "CPDialogDnsResultManager::DnsResetRegisteredSecondaryState::resetRefreshTimer(): client registration handle retrieved for account: " << mHandle << " manager: " << mManager);
      uint32_t nextRefresh = mAccount->getClientRegistrationHandle()->whenRefresh();
      uint32_t registrationExpiry = mAccount->getClientRegistrationHandle()->registrationExpiry();
      if (registrationExpiry == 0)
      {
         CPCAPI2::SipAccount::SipAccountSettings settings = mAccount->getSettings();
         registrationExpiry = settings.registrationIntervalSeconds;
      }
      nextRefresh = (nextRefresh + registrationExpiry);

      // Ignore the ping test for now but trigger the ping test after the next re-registration.
      if (nextRefresh <= DNS_RESET_PRE_REGISTRATION_MINIMUM_IN_SECONDS)
      {
         ErrLog(<< "CPDialogDnsResultManager::DnsResetRegisteredSecondaryState::resetRefreshTimer(): account: " << mHandle << " manager: " << mManager << " client registration does not seem to be in valid state");
         // Treating this as critical error and stopping dns resets
         changeState(Dns_Reset_Invalid_State);
      }
      else
      {
         nextRefresh = nextRefresh - DNS_RESET_PRE_REGISTRATION_MINIMUM_IN_SECONDS;
         mRefreshTimer.cancel();
         mRefreshTimer.expires_from_now(nextRefresh * 1000);
         mRefreshTimer.async_wait(this, REFRESH_TIMER_ID, NULL);
         StackLog(<< "CPDialogDnsResultManager::DnsResetRegisteredSecondaryState::resetRefreshTimer(): account: " << mHandle << " manager: " << mManager << " reset refresh timer for " << nextRefresh << " seconds");
      }
   }
   else
   {
      ErrLog(<< "CPDialogDnsResultManager::DnsResetRegisteredSecondaryState::resetRefreshTimer(): account: " << mHandle << " manager: " << mManager << " could not get the valid client registration handle");
      // Treating this as critical error and stopping dns resets
      changeState(Dns_Reset_Invalid_State);
   }
}

CPDialogDnsResultManager::DnsResetProbingState::DnsResetProbingState(CPDialogDnsResultManager* manager) :
DnsResetState(manager, DnsResetStateType::Dns_Reset_Probing_State),
mProbeTimer(manager->getReactor())
{
}

CPDialogDnsResultManager::DnsResetProbingState::~DnsResetProbingState()
{
   mOptionsPing.reset();
   mProbeTimer.cancel();
}

void CPDialogDnsResultManager::DnsResetProbingState::onEntry()
{
   DnsLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onEntry(): " << this << " account: " << mHandle << " manager: " << mManager)
   // If in this state, it indicates that we should proceed with the dns-reset as long as the preferred target is responsive, i.e.
   // - dns reset is enabled
   // - account is registered
   // - secondary target has been selected
   // - preferred target is populated
   // - no active calls
   // - within historical dns-reset threshold

   resip::SharedPtr<resip::DialogUsageManager>& dum = mManager->getDum();
   if (dum)
   {
      if (dum->doesOutOfDialogHandlerExist(resip::OPTIONS, this) == false)
         dum->addOutOfDialogHandler(resip::OPTIONS, this, false);
      sendPing();
   }
   else
   {
      ErrLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onEntry(): " << this << " account: " << mHandle << " manager: " << mManager << " dum is not initialized");
      SipAccount::ErrorEvent args;
      args.errorText = "Invalid DialogUsageManager";
      mAccount->fireDnsResultManagerError(args);
   }
}

void CPDialogDnsResultManager::DnsResetProbingState::onExit()
{
   DnsLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onExit(): " << this << " account: " << mHandle << " manager: " << mManager)
   mOptionsPing.reset();
   mProbeTimer.cancel();
   resip::SharedPtr<resip::DialogUsageManager>& dum = mManager->getDum();
   if (dum)
   {
      if (dum->doesOutOfDialogHandlerExist(resip::OPTIONS, this))
         dum->removeOutOfDialogHandler(resip::OPTIONS, this);
   }
   else
   {
      ErrLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onExit(): " << this << " account: " << mHandle << " manager: " << mManager << " dum is not initialized");
      SipAccount::ErrorEvent args;
      args.errorText = "Invalid DialogUsageManager";
      mAccount->fireDnsResultManagerError(args);
   }
}

// resip::DeadlineTimerHandler

void CPDialogDnsResultManager::DnsResetProbingState::onTimer(unsigned short timerId, void* appState)
{
   if (timerId == PROBE_TIMER_ID)
   {
      StackLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onTimer(): " << this << " account: " << mHandle << " manager: " << mManager << " timer-id: " << timerId << " did not receive ping response");
      mProbeTimer.cancel();
      mOptionsPing.reset();
      changeState(Dns_Reset_Registered_Secondary_State);
   }
   else
   {
      DebugLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onTimer(): " << this << " account: " << mHandle << " manager: " << mManager << " invalid timer-id: " << timerId);
   }
}

// SipAccountHandler

int CPDialogDnsResultManager::DnsResetProbingState::onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args)
{
   StackLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onAccountStatusChanged(): " << this << " account: " << account << " manager: " << mManager << " state: " << getName() << " status: " << args.accountStatus);

   if ((args.accountStatus == SipAccountStatusChangedEvent::Status_Unregistered) || (args.accountStatus == SipAccountStatusChangedEvent::Status_Unregistering))
   {
      InfoLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onAccountStatusChanged(): account: " << account << " manager: " << mManager << " go to disabled state as account is being unregistered");
      changeState(Dns_Reset_Disabled_State);
   }
   else if (args.accountStatus == SipAccountStatusChangedEvent::Status_Refreshing)
   {
      InfoLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onAccountStatusChanged(): " << this << " account: " << account << " manager: " << mManager << " account has been refreshed due to dns reset");
      changeState(Dns_Reset_Initialized_State);
   }

   return kSuccess;
}

// OutOfDialogHandler

bool CPDialogDnsResultManager::DnsResetProbingState::onSuccess(resip::ClientOutOfDialogReqHandle h, const resip::SipMessage& successResponse)
{
   if (!h.isValid())
   {
      DebugLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onSuccess(): account: " << mHandle << " manager: " << mManager << " probe OPTIONS dialog handle is invalid");
      return false;
   }

   const resip::Data& tid = successResponse.getTransactionId();
   if (mOptionsPing->getTransactionId() != tid)
   {
      DebugLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onSuccess(): account: " << mHandle << " probe OPTIONS response tid: " << tid << " does not match request tid: " << mOptionsPing->getTransactionId());
      return false;
   }

   mProbeTimer.cancel();
   mOptionsPing.reset();

   if (!successResponse.isResponse())
   {
      DebugLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onSuccess(): account: " << mHandle << " manager: " << mManager << " probe OPTIONS response message has invalid type");
      return false;
   }

   DebugLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onSuccess(): account: " << mHandle << " manager: " << mManager << " received successful ping response for tid: " << tid);
   resetDns();
   return true;
}

bool CPDialogDnsResultManager::DnsResetProbingState::onFailure(resip::ClientOutOfDialogReqHandle h, const resip::SipMessage& errorResponse)
{
   if (!h.isValid())
   {
      DebugLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onFailure(): account: " << mHandle << " manager: " << mManager << " probe OPTIONS handle is invalid");
      return false;
   }

   const resip::Data& tid = errorResponse.getTransactionId();
   if (mOptionsPing->getTransactionId() != tid)
   {
      DebugLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onFailure(): account: " << mHandle << " manager: " << mManager << " probe OPTIONS response tid: " << tid << " does not match request tid: " << mOptionsPing->getTransactionId());
      return false;
   }

   mOptionsPing.reset();
   mProbeTimer.cancel();

   if (!errorResponse.isResponse())
   {
      DebugLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onFailure(): account: " << mHandle << " manager: " << mManager << " probe OPTIONS response message has invalid type");
      return false;
   }

   int status = errorResponse.header(resip::h_StatusLine).responseCode();
   cpc::string reason = (errorResponse.header(resip::h_StatusLine).reason().size() > 0 ? errorResponse.header(resip::h_StatusLine).reason().c_str() : "");

   DebugLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onFailure(): account: " << mHandle << " manager: " << mManager << " received error response: " << status << " \"" << reason << "\" to ping request for tid: " << tid);
   if (errorResponse.getReceivedTransport() == 0)
   {
      DebugLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onFailure(): account: " << mHandle << " manager: " << mManager << " received internally generated " << status << " response with warning: "
               << (errorResponse.exists(resip::h_Warnings) ? errorResponse.header(resip::h_Warnings).front().code() : 0) << " "
               << (errorResponse.exists(resip::h_Warnings) ? errorResponse.header(resip::h_Warnings).front().text() : ""));
      changeState(Dns_Reset_Registered_Secondary_State);
   }
   else
   {
      resetDns();
   }

   return true;
}

bool CPDialogDnsResultManager::DnsResetProbingState::onReceivedRequest(resip::ServerOutOfDialogReqHandle, const resip::SipMessage& request)
{
   DebugLog(<< "CPDialogDnsResultManager::DnsResetProbingState::onReceivedRequest(): account: " << mHandle << " manager: " << mManager << " received request: " << request);
   return kSuccess;
}

bool CPDialogDnsResultManager::DnsResetProbingState::sendPing()
{
   resip::Tuple& preferredTarget = mManager->getPreferredTarget();
   resip::Tuple& currentTarget = mManager->getCurrentTarget();
   DebugLog(<< "CPDialogDnsResultManager::DnsResetProbingState::sendPing(): account: " << mHandle << " manager: " << mManager << " current target: " << currentTarget << " preferred target: " << preferredTarget);
   resip::Tuple empty;
   mOptionsPing.reset();
   mProbeTimer.cancel();

   mProbeTimer.expires_from_now(PROBE_TIMEOUT_MSECS);

   // resip::Data target = resip::Data("sip:") + mTarget.presentationFormat();
   // Cant reset dns atleast on this stack as the ping test will essentially impact the next registration or
   // invite even though the whole point is to not impact the current dns cache, either need a seperate sip
   // stack and associated dns stub to send seperate dns queries and options ping on them, or send the ping
   // on the current dns stub but enforce using a particular target
   resip::Uri target;
   target.host() = resip::Tuple::inet_ntop(preferredTarget); // preferredTarget.getTargetDomain();
   target.port() = preferredTarget.getPort();
   resip::SharedPtr<resip::DialogUsageManager>& dum = mManager->getDum();
   if (dum)
   {
      mOptionsPing = dum->makeOutOfDialogRequest(resip::NameAddr(target), resip::OPTIONS);
      mOptionsPing->header(resip::h_MaxForwards).value() = 0;

      resip::ExtensionHeader h_XConnectivityProbe("X-Connectivity-Probe-DNS");
      mOptionsPing->header(h_XConnectivityProbe).push_back(resip::StringCategory("0"));
      mOptionsPing->setForceTarget(target);
      mProbeTimer.async_wait(this, PROBE_TIMER_ID, NULL);

      DebugLog(<< "CPDialogDnsResultManager::DnsResetProbingState::sendPing(): account: " << mHandle << " manager: " << mManager << " current target: " << currentTarget << " preferred target: " << preferredTarget << " sending ping to: " << target);
      dum->send(mOptionsPing);
   }
   else
   {
      ErrLog(<< "CPDialogDnsResultManager::DnsResetProbingState::sendPing(): account " << mHandle << " manager: " << mManager << " dum is not initialized");
      SipAccount::ErrorEvent args;
      args.errorText = "Invalid DialogUsageManager";
      mAccount->fireDnsResultManagerError(args);
      return false; // Treat this as critical error
   }

   return true;
}

void CPDialogDnsResultManager::DnsResetProbingState::resetDns()
{
   SipConversation::SipAVConversationManagerInterface* conversationInterface = dynamic_cast<SipConversation::SipAVConversationManagerInterface*>(SipConversation::SipConversationManager::getInterface(mAccount->getPhone()));
   int callCount = 0;
   if (conversationInterface != NULL)
   {
      callCount = conversationInterface->getCallCount();
   }

   if (callCount == 0)
   {
      resip::SipStack* stack = mAccount->getSipStack();
      if (stack)
      {
         resip::Tuple& preferredTarget = mManager->getPreferredTarget();
         resip::Tuple& currentTarget = mManager->getCurrentTarget();

         DebugLog(<< "CPDialogDnsResultManager::DnsResetProbingState::resetDns(): account " << mHandle << " manager: " << mManager << " reset dns cache as current target: " << currentTarget << " does not match the preferred target: " << preferredTarget);
         resip::DnsStub::DnsSettings dnsSettings;
         mAccount->populateNameServer(dnsSettings);
         stack->resetDns(); // Clears white list, mark manager, dns cache and triggers reInit on dns stub
         stack->getDnsStub()->reInit(dnsSettings);

         if (mManager->isAuthResetEnabled())
         {
            if (mAccount->getClientRegistrationHandle().isValid())
            {
               DebugLog(<< "CPDialogDnsResultManager::DnsResetProbingState::resetDns(): account " << mHandle << " manager: " << mManager << " reset authentication");
               mAccount->getClientRegistrationHandle()->resetAuthenticationState();
            }
         }

         mAccount->getStunClient()->reInit(dnsSettings);
         DnsResetHistory& history = mManager->getDnsResetHistory();
         history.add();

         // Trigger registration refresh, if successful, dns reset checks will stop. If the preferred still does not get selected, the checks will continue.
         mAccount->resetDnsInitiated(currentTarget, preferredTarget);
         mAccount->requestRegistrationRefresh(0);

         // changeState(Dns_Reset_Startup_State);
      }
      else
      {
         ErrLog(<< "CPDialogDnsResultManager::DnsResetProbingState::resetDns(): account: " << mHandle << " manager: " << mManager << " stack not initialized");
         // Do we need to ensure that we try again, or stop at this moment, as no registration callback will be triggered as we did not initiate a dns reset
         // Treating this as critical error and stopping dns resets
         changeState(Dns_Reset_Invalid_State);
      }
   }
   else
   {
      // Do we need to ensure that we try again, or stop at this moment, as no registration callback will be triggered as it was not updated
      DebugLog(<< "CPDialogDnsResultManager::DnsResetProbingState::resetDns(): account " << mHandle << " manager: " << mManager << " failed reset validation due to call-count: " << callCount);
      changeState(Dns_Reset_Registered_Secondary_State);
   }
}

CPDialogDnsResultManager::DnsResetDisabledState::DnsResetDisabledState(CPDialogDnsResultManager* manager) :
DnsResetState(manager, DnsResetStateType::Dns_Reset_Disabled_State)
{
}

CPDialogDnsResultManager::DnsResetDisabledState::~DnsResetDisabledState()
{
}

void CPDialogDnsResultManager::DnsResetDisabledState::onEntry()
{
   DnsLog(<< "CPDialogDnsResultManager::DnsResetDisabledState::onEntry(): " << this << " account: " << mHandle << " manager: " << mManager)
}

void CPDialogDnsResultManager::DnsResetDisabledState::onExit()
{
   DnsLog(<< "CPDialogDnsResultManager::DnsResetDisabledState::onExit(): " << this << " account: " << mHandle << " manager: " << mManager)
}

int CPDialogDnsResultManager::DnsResetDisabledState::onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args)
{
   const CPCAPI2::SipAccount::SipAccountSettings settings = mAccount->getSettings();
   mManager->resetConfig(settings);
   StackLog(<< "CPDialogDnsResultManager::DnsResetDisabledState::onAccountStatusChanged(): " << this << " account: " << account << " manager: " << mManager << " state: " << getName() << " status: " << args.accountStatus << " dns-reset enabled: " << isDnsResetEnabled());

   if (isDnsResetEnabled())
   {
      if ((args.accountStatus == SipAccountStatusChangedEvent::Status_Refreshing)
         || (args.accountStatus == SipAccountStatusChangedEvent::Status_Registered)
         || (args.accountStatus == SipAccountStatusChangedEvent::Status_Registering))
      {
         InfoLog(<< "CPDialogDnsResultManager::DnsResetDisabledState::onAccountStatusChanged(): " << this << " account: " << account << " manager: " << mManager << " account being re-enabled");
         changeState(Dns_Reset_Initialized_State);
      }
   }

   return kSuccess;
}

int CPDialogDnsResultManager::DnsResetDisabledState::onAccountConfigured(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountConfiguredEvent& args)
{
   const CPCAPI2::SipAccount::SipAccountSettings& settings(args.settings.size() > (size_t)(args.curTransport) ? args.settings.at(args.curTransport) : args.settings.at(CPCAPI2::NetworkTransport::TransportNone));
   mManager->resetConfig(settings);
   DebugLog(<< "CPDialogDnsResultManager::DnsResetDisabledState::onAccountConfigured() account: " << account << " manager: " << mManager << " handling account configuration change to settings for network type: " << args.curTransport << " dns-reset enabled: " << isDnsResetEnabled());

   if (isDnsResetEnabled())
   {
      changeState(Dns_Reset_Startup_State);
   }
   return kSuccess;
}

int CPDialogDnsResultManager::DnsResetDisabledState::onNetworkChange(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountNetworkChangeEvent& args)
{
   mManager->resetConfig(args.settings);
   DebugLog(<< "CPDialogDnsResultManager::DnsResetDisabledState::onNetworkChange() account: " << account << " manager: " << mManager << " handling network change to network type: " << args.curTransport << " dns-reset enabled: " << isDnsResetEnabled());

   if (isDnsResetEnabled())
   {
      resip::SharedPtr<resip::DialogUsageManager>& dum = mManager->getDum();
      if (dum)
      {
         changeState(Dns_Reset_Initialized_State);
      }
      else
      {
         changeState(Dns_Reset_Startup_State);
      }
   }
   return kSuccess;
}

CPDialogDnsResultManager::DnsResetInvalidState::DnsResetInvalidState(CPDialogDnsResultManager* manager) :
DnsResetState(manager, DnsResetStateType::Dns_Reset_Invalid_State)
{
}

CPDialogDnsResultManager::DnsResetInvalidState::~DnsResetInvalidState()
{
}

void CPDialogDnsResultManager::DnsResetInvalidState::onEntry()
{
   // No getting out of this state, well except if we unregister and re-register
   DnsLog(<< "CPDialogDnsResultManager::DnsResetInvalidState::onEntry(): " << this << " account: " << mHandle << " manager: " << mManager)
}

void CPDialogDnsResultManager::DnsResetInvalidState::onExit()
{
   DnsLog(<< "CPDialogDnsResultManager::DnsResetInvalidState::onExit(): " << this << " account: " << mHandle << " manager: " << mManager)
}

int CPDialogDnsResultManager::DnsResetInvalidState::onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args)
{
   DebugLog(<< "CPDialogDnsResultManager::DnsResetInvalidState::onError(): " << this << " manager: " << mManager << " account: " << mHandle << " state: " << getName());
   return kSuccess;
}

CPDialogDnsResultManager::CPDialogDnsResultManager(CPCAPI2::Phone* cpcPhone, CPCAPI2::SipAccount::SipAccountImpl* account) :
mReactor(dynamic_cast<PhoneInterface*>(cpcPhone)->getSdkModuleThread()),
mDnsResetHistory(DNS_RESET_HISTORY_DEPTH_IN_SECONDS),
mAccount(account),
mHandle(account->getHandle()),
mDnsHandler(NULL),
mDnsResetEnabled(false),
mChangingState(false),
mStateFactory(new DnsResetStateFactory(this)),
mState(NULL)
{
   StackLog(<< "CPDialogDnsResultManager(): " << this << " account: " << mHandle);
   if (cpcPhone && account)
   {
      changeState(Dns_Reset_Startup_State);
   }
   else
   {
      changeState(Dns_Reset_Invalid_State);
   }
}

CPDialogDnsResultManager::~CPDialogDnsResultManager()
{
   StackLog(<< "~CPDialogDnsResultManager(): " << this << " account: " << mHandle << " deleting dns reset manager");
}

void CPDialogDnsResultManager::setHandler(CPCAPI2::SipAccount::DialogDnsResultHandler* handler)
{
   mDnsHandler = handler;
}

void CPDialogDnsResultManager::postCallback(CPCAPI2::SipAccount::DnsResetStatusChangedEvent& args)
{
   if (!mDnsHandler)
   {
      DnsLog(<< "CPDialogDnsResultManager()::postCallback: " << this << " account: " << mHandle << " no dns handler set")
      return;
   }

   if (!mAccount)
   {
      ErrLog(<< "CPDialogDnsResultManager()::postCallback: " << this << " account: " << mHandle << " invalid account");
      return;
   }

   mDnsHandler->onDialogDnsResetStatus(mHandle, args);
}

resip::MultiReactor& CPDialogDnsResultManager::getReactor()
{
   return mReactor;
}

resip::SharedPtr<resip::DialogUsageManager>& CPDialogDnsResultManager::getDum()
{
   return mDum;
}

void CPDialogDnsResultManager::changeState(DnsResetStateType type)
{
   DnsLog(<< "CPDialogDnsResultManager::changeState(): " << this << " account: " << mHandle << " called changing state from: " << (mState ? mState->getName() : "") << " to: " << DnsResetState::getName(type))
   assert(!mChangingState); // changing state when changing state is in progress
   mChangingState = true;
   DnsResetState* newState = mStateFactory->getState(type);
   if (!newState)
   {
      newState = mStateFactory->getState(Dns_Reset_Invalid_State);
      ErrLog(<< "CPDialogDnsResultManager::changeState(): " << this << " account: " << mHandle << " invalid state type: " << type << ", current-state: " << (mState ? mState->getName() : "") << " changing to invalid state: " << (newState ? newState->getName() : ""));
   }

   DebugLog(<< "CPDialogDnsResultManager::changeState(): " << this << " account: " << mHandle << " changing state from: " << (mState ? mState->getName() : "") << " to: " << (newState ? newState->getName() : ""));

   if (mState)
   {
      DnsLog(<< "CPDialogDnsResultManager::changeState(): " << this << " account: " << mHandle << " calling onExit on current state: " << (mState ? mState->getName() : ""))
      mState->onExit();
   }

   mState = newState;

   if (mState)
   {
      DnsLog(<< "CPDialogDnsResultManager::changeState(): " << this << " account: " << mHandle << " calling onEntry on new state: " << (mState ? mState->getName() : ""))
      mState->onEntry();

      DnsResetStatusChangedEvent args(mState->getType());
      postCallback(args);
   }

   mChangingState = false;
}

SipAccount::DnsResetStateType CPDialogDnsResultManager::getStateType()
{
   if (mState)
   {
      return mState->getType();
   }

   ErrLog(<< "CPDialogDnsResultManager::getStateType(): " << this << " account: " << mHandle << " manager is in an invalid state");
   return Dns_Reset_Invalid_State;
}

CPDialogDnsResultManager::DnsResetState* CPDialogDnsResultManager::getState()
{
   return mState;
}

CPCAPI2::SipAccount::SipAccountImpl* CPDialogDnsResultManager::getAccount()
{
   return mAccount;
}

CPCAPI2::SipAccount::SipAccountHandle CPDialogDnsResultManager::getHandle()
{
   return mHandle;
}

resip::Tuple& CPDialogDnsResultManager::getPreferredTarget()
{
   return mPreferredTarget;
}

resip::Tuple& CPDialogDnsResultManager::getCurrentTarget()
{
   return mCurrentTarget;
}

bool CPDialogDnsResultManager::isDnsResetEnabled()
{
   return mDnsResetEnabled;
}

bool CPDialogDnsResultManager::isAuthResetEnabled()
{
   return mAuthResetEnabled;
}

int CPDialogDnsResultManager::getHistoricalChangeCount()
{
   return mDnsResetHistory.numChanges();
}

CPDialogDnsResultManager::DnsResetHistory& CPDialogDnsResultManager::getDnsResetHistory()
{
   return mDnsResetHistory;
}

void CPDialogDnsResultManager::reset()
{
   DebugLog(<< "CPDialogDnsResultManager::reset(): " << this << " account: " << mHandle);
   mDnsResetEnabled = false;
   mAuthResetEnabled = false;

   resip::Tuple empty;
   mCurrentTarget = empty;
   mPreferredTarget = empty;
   mDnsResetHistory.reset();
}

void CPDialogDnsResultManager::resetConfig(const CPCAPI2::SipAccount::SipAccountSettings& settings, bool fullReset)
{
   if (fullReset)
   {
      reset();
   }

   if (settings.enableDNSResetOnRegistrationRefresh)
   {
      DebugLog(<< "CPDialogDnsResultManager::resetConfig(): " << this << " adding account: " << mHandle << " for dns reset");
      mDnsResetEnabled = true;
   }
   else
   {
      DebugLog(<< "CPDialogDnsResultManager::resetConfig(): " << this << " removing account: " << mHandle << " for dns reset");
      mDnsResetEnabled = false;
   }

   if (settings.enableAuthResetUponDNSReset)
   {
      DebugLog(<< "CPDialogDnsResultManager::resetConfig(): " << this << " adding account: " << mHandle << " for auth reset");
      mAuthResetEnabled = true;
   }
   else
   {
      DebugLog(<< "CPDialogDnsResultManager::resetConfig(): " << this << " removing account: " << mHandle << " for auth reset");
      mAuthResetEnabled = false;
   }
}

// DialogDnsResultHandler

void CPDialogDnsResultManager::onDnsResult(const resip::DnsResultDataMessage& result)
{
   DnsLog(<< "CPDialogDnsResultManager::onDnsResult(): " << this << " account: " << mHandle << " result: " << result << " state: " << mState->getName())
   mState->onDnsResult(result);
}

// SipAccountHandlerInternal

int CPDialogDnsResultManager::onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args)
{
   DnsLog(<< "CPDialogDnsResultManager::onAccountStatusChanged(): " << this << " account: " << account << " status: " << args.accountStatus << " state: " << mState->getName())
   mState->onAccountStatusChanged(account, args);
   return kSuccess;
}

int CPDialogDnsResultManager::onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args)
{
   DnsLog(<< "CPDialogDnsResultManager::onError(): " << this << " account: " << account << " error: " << args.errorText)
   mState->onError(account, args);
   return kSuccess;
}

int CPDialogDnsResultManager::onAccountConfigured(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountConfiguredEvent& args)
{
   DnsLog(<< "CPDialogDnsResultManager::onAccountConfigured(): " << this << " account: " << account << " handling account configuration change to settings for network type: " << args.curTransport)
   const CPCAPI2::SipAccount::SipAccountSettings& settings(args.settings.size() > (size_t)(args.curTransport) ? args.settings.at(args.curTransport) : args.settings.at(CPCAPI2::NetworkTransport::TransportNone));
   resetConfig(settings);
   mState->onAccountConfigured(account, args);
   return kSuccess;
}

int CPDialogDnsResultManager::onAccountEnabled(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountEnabledEvent& args)
{
   DnsLog(<< "CPDialogDnsResultManager::onAccountEnabled(): " << this << " account: " << account << " transport: " << args.curTransport)
   mState->onAccountEnabled(account, args);
   return kSuccess;
}

int CPDialogDnsResultManager::onNetworkChange(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountNetworkChangeEvent& args)
{
   DnsLog(<< "CPDialogDnsResultManager::onNetworkChange() " << this << " account: " << account << " handling network change to network type: " << args.curTransport)
   resetConfig(args.settings);
   mState->onNetworkChange(account, args);
   return kSuccess;
}

// SipAccountAwareFeature

int CPDialogDnsResultManager::adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile)
{
   DnsLog(<< "CPDialogDnsResultManager::adornMasterProfile(): " << this << " account: " << mHandle)
   mState->adornMasterProfile(profile);
   return kSuccess;
}

int CPDialogDnsResultManager::addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig)
{
   DnsLog(<< "CPDialogDnsResultManager::addHandlers(): " << this << " account: " << mHandle)
   mDum = dum;
   mDum->setDialogDnsResultHandler(this);
   mState->addHandlers(dum, overrideSourceIpSignalling, overrideSourceIpTransport, tscconfig);
   return kSuccess;
}

int CPDialogDnsResultManager::onDumBeingDestroyed()
{
   DnsLog(<< "CPDialogDnsResultManager::onDumBeingDestroyed(): " << this << " account: " << mHandle)
   mDum.reset();
   reset();
   mState->onDumBeingDestroyed();
   return kSuccess;
}

void CPDialogDnsResultManager::release()
{
   DnsLog(<< "CPDialogDnsResultManager::release(): " << this << " account: " << mHandle)
   reset();
   mState->release();
}

/*
bool CPDialogDnsResultManager::dnsPreCheck()
{
   DebugLog(<< "CPDialogDnsResultManager::dnsPreCheck(): account: " << mHandle << " current target: " << mCurrentTarget << " preferred target: " << mPreferredTarget);
   resip::DnsStub::DnsSettings dnsSettings;
   mAccount->populateNameServer(dnsSettings);
   Utils::DnsClient dnsClient(dnsSettings);

   resip::Tuple outTuple;
   int port = resip::Symbols::DefaultSipPort;
   std::stringstream target;
   target << "_sip.";
   SipAccountSettings settings = mAccount->getSettings();
   resip::TransportType resipTransport;
   switch (settings.sipTransportType)
   {
      case SipAccountTransport_UDP:
         target << "_udp.";
         resipTransport = resip::UDP;
         break;
      case SipAccountTransport_TCP:
         target << "_tcp.";
         resipTransport = resip::TCP;
         break;
      case SipAccountTransport_TLS:
         target << "_tls.";
         resipTransport = resip::TLS;
         port = resip::Symbols::DefaultSipsPort;
         break;
      case SipAccountTransport_Auto:
      default:
         target << "_udp.";
         resipTransport = resip::UDP;
         break;
   }

   resip::NameAddr obProxy = (mDum->getMasterProfile()->hasOutboundProxy() ? mDum->getMasterProfile()->getOutboundProxy() : mDum->getMasterProfile()->getDefaultFrom());

   if (obProxy.uri().port() > 0)
   {
      port = obProxy.uri().port();
   }

   resip::Data obProxyHost = obProxy.uri().host();
   if (resip::DnsUtil::isIpAddress(obProxyHost))
   {
      outTuple = resip::Tuple(obProxyHost, port, resipTransport);
      DebugLog(<< "CPDialogDnsResultManager::dnsPreCheck:  account " << mHandle << " outbound proxy tuple: " << outTuple);
      return true; // TODO: why
   }

   target << obProxyHost;

   //
   // TODO: NAPTR Query
   // resip::Data targetForHostLookup(obProxyHost);
   // DebugLog(<< "CPDialogDnsResultManager::dnsPreCheck: naptr query for service: " << resip::Protocol::Sip << " domain: " << obProxyHost);
   // Utils::DnsNaptrRecord dnsNaptrRecord = dnsClient.getDnsNaptrRecord(resip::Protocol::Sip, obProxyHost.c_str());
   // if (!dnsNaptrRecord.target.empty())
   // {
   //    targetForHostLookup = dnsNaptrRecord.target;
   //    DebugLog(<< "CPDialogDnsResultManager::dnsPreCheck: naptr record service: " << service << " target: " << dnsNaptrRecord.target);
   // }
   //

   resip::Data targetForHostLookup(obProxyHost);
   Utils::DnsSrvRecord dnsSrvRecord = dnsClient.getDnsSrvRecord(resip::Protocol::Sip, target.str().c_str());
   if (!dnsSrvRecord.target.empty())
   {
      targetForHostLookup = dnsSrvRecord.target;
      port = dnsSrvRecord.port;
      DebugLog(<< "CPDialogDnsResultManager::dnsPreCheck:  account " << mHandle << " srv record host target: " << targetForHostLookup << ":" << port);
   }

   Utils::DnsAorAAAARecord hostRecord = dnsClient.getDnsAorAAAARecord(targetForHostLookup, settings.ipVersion);
   if (hostRecord.valid)
   {
      outTuple = resip::Tuple(hostRecord.ipAddr, resipTransport);
      outTuple.setPort(port);
      DebugLog(<< "CPDialogDnsResultManager::dnsPreCheck:  account " << mHandle << " host record is valid, outbound tuple: " << outTuple);
   }

   switch (settings.sipTransportType)
   {
      case SipAccountTransport_UDP:
         target << "_udp.";
         resipTransport = resip::UDP;
         break;
      case SipAccountTransport_TCP:
         target << "_tcp.";
         resipTransport = resip::TCP;
         break;
      case SipAccountTransport_TLS:
         target << "_tls.";
         resipTransport = resip::TLS;
         port = resip::Symbols::DefaultSipsPort;
         break;
      case SipAccountTransport_Auto:
      default:
         // TODO: With auto, should we query all the protocols rather than just udp, or do NAPTR
         target << "_udp.";
         resipTransport = resip::UDP;
         break;
   }

   return true;
}
*/

void CPDialogDnsResultManager::DnsResetHistory::add()
{
   time_t now = ::time(0);
   mHistory.push_back(now);

   time_t point = now - mDepth;
   std::list<time_t>::iterator it = mHistory.begin();
   for (; it != mHistory.end();)
   {
      if ((*it) < point)
      {
         it = mHistory.erase(it);
      }
      else
      {
         ++it;
      }
   }
}

unsigned int CPDialogDnsResultManager::DnsResetHistory::numChanges() const
{
   return mHistory.size();
}

}

}

#endif
