#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_CLIENT_JSON_SERVER_INTERFACE_H)
#define CPCAPI2_PUSH_NOTIFICATION_CLIENT_JSON_SERVER_INTERFACE_H

#include <confconnector/ConferenceConnectorJsonApi.h>
#include <confconnector/ConferenceConnectorHandler.h>
#include <confconnector/ConferenceConnectorInterface.h>
#include "jsonapi/JsonApiServerModule.h"
#include "jsonapi/JsonApiServerSendTransport.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>

namespace CPCAPI2
{
class PhoneInterface;
namespace ConferenceConnector
{
class ConferenceConnectorJsonServerSyncHandler {};
class ConferenceConnectorJsonServerInterface : public CPCAPI2::EventSource<ConferenceConnectorHandle, ConferenceConnectorHandler, ConferenceConnectorJsonServerSyncHandler>,
                                               public CPCAPI2::ConferenceConnector::ConferenceConnector<PERSON>son<PERSON><PERSON>,
                                               public CPCAPI2::ConferenceConnector::ConferenceConnectorHandler,
                                               public CPCAPI2::ConferenceConnector::ConferenceConnectorSyncHandler,
                                               public CPCAPI2::JsonApi::JsonApiServerModule,
                                               public CPCAPI2::PhoneModule
{
public:
   ConferenceConnectorJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~ConferenceConnectorJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;
   virtual int handleConnectionClosed(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn) OVERRIDE;

   // ConferenceConnectorHandler
   virtual int onServiceConnectionStatusChanged(ConferenceConnectorHandle conn, const ServiceConnectionStatusEvent& args) OVERRIDE;
   virtual int onConferenceCreated(ConferenceConnectorHandle conn, const ConferenceCreatedEvent& args) OVERRIDE;
   virtual int onConferenceEnded(ConferenceConnectorHandle conn, const ConferenceEndedEvent& args) OVERRIDE;
   virtual int onConferenceListUpdated(ConferenceConnectorHandle conn, const ConferenceListUpdatedEvent& args) OVERRIDE;
   virtual int onConferenceParticipantListUpdated(ConferenceConnectorHandle conn, const ConferenceParticipantListUpdatedEvent& args) OVERRIDE;
   virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) OVERRIDE;
   virtual int onConferenceSessionMediaStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionMediaStatusChangedEvent& args) OVERRIDE;

private:
   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);
   void handleConnectionClosedImpl(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn);
   int handleCreateConferenceConnector(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleDestroyConferenceConnector(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleSetConnectionSettings(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleConnectToConferenceService(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleDisconnectFromConferenceService(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleQueryConferenceList(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleCreateNewConference(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleDestroyConference(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleQueryParticipantList(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleCreateConferenceSession(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleSetSessionSettings(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleSetSessionMediaSettings(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleStartSession(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleEndSession(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleUpdateSessionMedia(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleSetParticipantPermissions(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);

private:
   CPCAPI2::PhoneInterface* mPhone;
   CPCAPI2::ConferenceConnector::ConferenceConnectorManager* mConferenceConnectorManager;
   typedef std::map<std::string, std::function<int(CPCAPI2::JsonApi::JsonApiRequestInfo, const rapidjson::Value&)>> FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;

   std::map<CPCAPI2::JsonApi::JsonApiRequestInfo, ConferenceConnectorHandle> mMapConnToHdl;

};

}

}

#endif // CPCAPI2_PUSH_NOTIFICATION_CLIENT_JSON_SERVER_INTERFACE_H
