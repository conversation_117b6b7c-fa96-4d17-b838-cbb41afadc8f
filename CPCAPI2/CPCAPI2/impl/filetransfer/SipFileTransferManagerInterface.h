#pragma once

#ifndef __CPCAPI2_SIP_FILE_TRANSFER_MANAGER_INTERFACE_H__
#define __CPCAPI2_SIP_FILE_TRANSFER_MANAGER_INTERFACE_H__

#include <memory>
#include <set>

#include "cpcapi2defs.h"
#include "../phone/PhoneModule.h"
#include "../experimental/phone/Permissions.h"
#include "../account/SipAccountInterface.h"
#include "filetransfer/SipFileTransferManager.h"
#include "SipFileTransferInfo.h"

#include <msrp_forward_decl.h>

namespace CPCAPI2
{
   // Forward declarations
   class Phone;

   namespace SipFileTransfer
   {
      // Forward declarations
      class SipFileTransferManagerImpl;

      // Main file transfer interface
      class SipFileTransferManagerInterface : public CPCAPI2::EventSource2<CPCAPI2::EventHandler<SipFileTransferHandler, SipAccount::SipAccountHandle> >,
                                              public SipFileTransferManager,
                                              public PhoneModule
      {
      public:
         SipFileTransferManagerInterface( Phone* phone );
         virtual ~SipFileTransferManagerInterface();

         FORWARD_EVENT_PROCESSOR(SipFileTransferManagerInterface);

         // PhoneModule Interface
         virtual void Release() OVERRIDE;

         virtual int setHandler( SipAccount::SipAccountHandle account, SipFileTransferHandler* handler ) OVERRIDE;
         virtual SipFileTransferItemHandle createFileTransferItem( SipAccount::SipAccountHandle account ) OVERRIDE;
         virtual SipFileTransferHandle createFileTransfer( SipAccount::SipAccountHandle account ) OVERRIDE;
         virtual int addParticipant( SipFileTransferHandle fileTransfer, const cpc::string& targetAddress ) OVERRIDE;
         virtual int start( SipFileTransferHandle fileTransfer ) OVERRIDE;
         virtual int end( SipFileTransferHandle fileTransfer ) OVERRIDE;
         virtual int provisionalAccept( SipFileTransferHandle fileTransfer ) OVERRIDE;
         virtual int configureFileTransferItems( SipFileTransferHandle fileTransfer, const SipFileTransferItems& fileItems ) OVERRIDE;
         virtual int accept( SipFileTransferHandle fileTransfer ) OVERRIDE;
         virtual int reject( SipFileTransferHandle fileTransfer, unsigned int rejectReason ) OVERRIDE;
         virtual int cancelItem( SipFileTransferHandle fileTransfer, SipFileTransferItemHandle fileTransferItem ) OVERRIDE;

         PhoneInterface* phoneInterface();
         SipFileTransferInfo * getFileTransferInfo( SipFileTransferHandle h );

         // callbacks from the msrp library
         void messageCreatedCallback( msrp_session_t *p_session, msrp_message_t *p_message,
            SipFileTransferHandle fileTransfer, SipFileTransferItemHandle fileTransferItem );
         void messageProgressCallback( msrp_message_t *p_message,
            SipFileTransferHandle fileTransfer, SipFileTransferItemHandle fileTransferItem,
            unsigned short percent );
         void messageRecvCompletedCallback( msrp_message_t *p_message,
            SipFileTransferHandle fileTransfer, SipFileTransferItemHandle fileTransferItem );
         void messageCancelledCallback( msrp_message_t *p_message,
            SipFileTransferHandle fileTransfer, SipFileTransferItemHandle fileTransferItem );


         void onPermissionGranted(int requestCode, CPCAPI2::Permission permission);

      private: // methods invoked by DumFp thread asynchronously

         int setHandlerImpl( SipAccount::SipAccountHandle account, SipFileTransferHandler* handler );
         int createFileTransferItemImpl( SipAccount::SipAccountHandle account, SipFileTransferItemHandle itemHandle );
         int createFileTransferImpl( SipAccount::SipAccountHandle account, SipFileTransferHandle sessionHandle );
         int addParticipantImpl( SipFileTransferHandle fileTransfer, const cpc::string& targetAddress );
         int startImpl( SipFileTransferHandle fileTransfer );
         int endImpl( SipFileTransferHandle fileTransfer );
         int provisionalAcceptImpl( SipFileTransferHandle fileTransfer );
         int configureFileTransferItemsImpl( SipFileTransferHandle fileTransfer, const SipFileTransferItems& fileItems );
         int acceptImpl( SipFileTransferHandle fileTransfer );
         int rejectImpl( SipFileTransferHandle fileTransfer, unsigned int rejectReason );
         int cancelItemImpl( SipFileTransferHandle fileTransfer, SipFileTransferItemHandle fileTransferItem );


      private: // data

         typedef std::map< SipAccount::SipAccountHandle, SipFileTransferManagerImpl* > AccountMap;
         std::shared_ptr<AccountMap> mAccountMapPtr;
         AccountMap& mAccountMap;
         std::map<CPCAPI2::SipAccount::SipAccountHandle, SipFileTransferHandler*> mHandlers;

         SipAccount::SipAccountInterface* mAccountIf;
         PhoneInterface* mPhone;

         typedef std::set<SipFileTransferHandle> PendingTransfersList;
         PendingTransfersList mIncomingPendingPermission;
         PendingTransfersList mOutgoingPendingPermission;
      };

      std::ostream& operator<<(std::ostream& os, const NewFileTransferEvent& evt);
      std::ostream& operator<<(std::ostream& os, const FileTransferConfiguredEvent& evt);
      std::ostream& operator<<(std::ostream& os, const FileTransferEndedEvent& evt);
      std::ostream& operator<<(std::ostream& os, const FileTransferItemEndedEvent& evt);
      std::ostream& operator<<(std::ostream& os, const FileTransferItemProgressEvent& evt);
      std::ostream& operator<<(std::ostream& os, const SipFileTransfer::ErrorEvent& evt);
   }
}

#endif // __CPCAPI2_SIP_FILE_TRANSFER_MANAGER_INTERFACE_H__
