

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipMwi {
class SipMwi
{
public:
    SipMwi(ITransport& transport)
    {
        this.transport = transport;
    }

    void registerForEventQueue(int64_t phoneHandle, int64_t accountHandle);

	void unRegisterForEventQueue(int64_t phoneHandle, int64_t accountHandle);

	int64_t createSubscription(int64_t phoneHandle, int64_t accountHandle);

	void start(int64_t phoneHandle, int64_t mwiSubscriptionHandle);

	void end(int64_t phoneHandle, int64_t mwiSubscriptionHandle);

private:
    ITransport* transport;
}; // class SipMwi
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipMwi
