#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "JsonApiServer_WebSocket.h"
#include "JsonApiServerInterface.h"
#include "JsonApiLogSqliteAccess.h"
#include "jsonapi/JsonApiServerModule.h"
#include "websocket/json/JsonDataWebSocketCommand.h"
#include "cpcapi2utils.h"
#include "../util/FileUtils.h"
#include "../util/cpc_logger.h"
#include "../phone/PhoneInterface.h"
#include "log/LocalLogger.h"

#include <stdio.h>
#include <ostream>
#include <fstream>
#include <locale>
//include <codecvt>
#include <thread>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL
#define CP_LOCAL_LOGGER_VAR mLocalLogger

#include "util/BoostTlsHelper.h"

using namespace resip;

namespace CPCAPI2
{

namespace JsonApi
{

JsonApiWebSocketSession::JsonApiWebSocketSession(CPCAPI2::PhoneInterface* masterSdkPhone, JsonApiServerWebSocket* transport, boost::asio::io_context& ioContext, const websocketpp::connection_hdl& conn, const std::string& resource, bool isLocalConnection) :
mConn(conn),
mMsgQueue(32),
mMasterPhone(masterSdkPhone),
mJsonApiServer(dynamic_cast<JsonApiServerInterface*>(JsonApiServer::getInterface(masterSdkPhone))),
mSdkContext(NULL),
mTransport(transport),
mIsLoopbackConnection(isLocalConnection),
mIsLoggedOut(false),
mResource(resource),
mLocalLogger(NULL),
mRecvTimer(ioContext)
{
   StackLog(<< "JsonApiWebSocketSession::JsonApiWebSocketSession(): " << this << " master phone: " << mMasterPhone);
   // setup a timer to terminate dangling connections
   mRecvTimer.expires_from_now(boost::posix_time::seconds(20));
   mRecvTimer.async_wait(std::bind(&JsonApiWebSocketSession::onRecvTimer, this, std::placeholders::_1));
}

JsonApiWebSocketSession::~JsonApiWebSocketSession()
{
   StackLog(<< "JsonApiWebSocketSession::~JsonApiWebSocketSession(): " << this << " master phone: " << mMasterPhone);
   mRecvTimer.cancel();
}

JsonApiServerWebSocket::JsonApiServerWebSocket(CPCAPI2::PhoneInterface* phone) :
mServerThread(NULL),
mPhone(phone),
mStopServerDone(false),
mResipOStream(&mResipLogStream)
{
   StackLog(<< "JsonApiServerWebSocket::JsonApiServerWebSocket(): " << this << " phone: " << mPhone);
}

JsonApiServerWebSocket::~JsonApiServerWebSocket()
{
   StackLog(<< "JsonApiServerWebSocket::~JsonApiServerWebSocket(): " << this << " phone: " << mPhone);
}

template<typename tserver>
void init_and_run_server(tserver* server, const JsonApiServerConfig& serverConfig)
{
   try {
      // Set logging settings
      server->set_access_channels(websocketpp::log::alevel::all);
      server->clear_access_channels(websocketpp::log::alevel::all);

      // websocketpp's logging by default doesn't seem very useful, so comment out for now (but we can with the below redirect its logging to ours)
      //mWebSockServer.get_alog().set_ostream(&mResipOStream);

      // Initialize Asio
      server->init_asio();

      /*
      Tune Handshake resource limiters.
      WebSocket++ has two handshake limiters. One is time based, the other is cumulative byte based.
      The time based one helps mitigate situations where the attacker floods you with idle/incomplete connections.
      The default here for the asio transport is 5 seconds. This is a conservative value.
      from: https://groups.google.com/forum/#!topic/websocketpp/nFLxYRtBryY
      */
      server->set_open_handshake_timeout(10000);
      server->set_listen_backlog(8192 * 8);
      server->set_reuse_addr(true);
      server->set_pong_timeout(30000);

      // Listen on port n
      std::error_code listenEc;
      int listenTryCnt = 0;
      do
      {
         listenEc = std::error_code();
         server->listen(boost::asio::ip::tcp::v4(), serverConfig.websocketPort, listenEc);
         if (listenEc)
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
         }
      } while (listenEc && listenTryCnt++ < 10);

      if (listenEc)
      {
         ErrLog(<< "Failed to start listening on port " << serverConfig.websocketPort << "; error = " << listenEc.message());
      }

      // Start the server accept loop
      server->start_accept();

      // Start the ASIO io_service run loop
      //mWebSockServer.run();

      std::vector<std::thread> tg;
      int threadCount = serverConfig.numThreads;

      if (threadCount < 0)
         threadCount = std::thread::hardware_concurrency();

      // Ensure there's always at least one
      threadCount = std::max(threadCount, 1);

      for (int i = 0; i < threadCount; i++) {
         tg.emplace_back(&tserver::run, server);
      }

      for (auto& threadItem : tg)
      {
         if (threadItem.joinable())
         {
            threadItem.join();
         }
      }

      std::cout << "websocket server thread exit..." << std::endl;
   }
   catch (websocketpp::exception const & e) {
      std::cout << e.what() << std::endl;
   }
   catch (std::system_error& se) {
      std::cout << se.what() << std::endl;
   }
   catch (std::exception & e) {
      std::cout << "other exception: " << e.what() << std::endl;
   }
}

void JsonApiServerWebSocket::startServer(const JsonApiServerConfig& serverConfig, std::function<void()> onPrivilegedAccessCompleted)
{
   if (mServerThread != NULL)
   {
      return; // already started
   }

   mConfig = serverConfig;
   mOnPrivilegedAccessCompleted = onPrivilegedAccessCompleted;

   if (mConfig.apiLoggingEnabled)
   {
      mApiLogDb.reset(new JsonApiLogSqliteAccess());
      JsonApiLogDbAccessConfig apiLogConfig;
      apiLogConfig.dbFileName = "jsonapilog.db";
      mApiLogDb->initialize(apiLogConfig, [](bool res, const cpc::string& resinfo) {
      });
   }

   if (serverConfig.wssCertificateFilePath.empty())
   {
      mWebSockServer.reset(new server());
   }
   else
   {
      mSecureWebSockServer.reset(new server_tls());
   }

   mServerThread = new std::thread([&, serverConfig]() {
      // Register handlers
      if (mWebSockServer != NULL)
      {
         mWebSockServer->set_socket_init_handler(std::bind(&JsonApiServerWebSocket::on_socket_init, this, std::placeholders::_1, std::placeholders::_2));
         mWebSockServer->set_open_handler(std::bind(&JsonApiServerWebSocket::on_open, this, mWebSockServer.get(), std::placeholders::_1));
         init_and_run_server<server>(mWebSockServer.get(), serverConfig);
      }
      else if (mSecureWebSockServer != NULL)
      {
         mSecureWebSockServer->set_socket_init_handler(std::bind(&JsonApiServerWebSocket::on_tls_socket_init, this, std::placeholders::_1, std::placeholders::_2));
         mSecureWebSockServer->set_open_handler(std::bind(&JsonApiServerWebSocket::on_open_tls, this, mSecureWebSockServer.get(), std::placeholders::_1));
         mSecureWebSockServer->set_tls_init_handler(std::bind(&JsonApiServerWebSocket::on_tls_init, this, mSecureWebSockServer.get(), std::placeholders::_1));
         init_and_run_server<server_tls>(mSecureWebSockServer.get(), serverConfig);
      }

      {
         std::unique_lock<std::mutex> lock(mStopServerDoneMutex);
         mStopServerDone = true;
      }
      mStopServerDoneCv.notify_one();
   });
}

void JsonApiServerWebSocket::stopServer()
{
   {
      std::unique_lock<std::mutex> lock(mStopServerDoneMutex);
      mStopServerDone = false;
   }

   websocketpp::lib::error_code ec;
   if (mWebSockServer != NULL)
   {
      mWebSockServer->stop_listening(ec);
      mWebSockServer->stop_perpetual();
   }
   if (mSecureWebSockServer != NULL)
   {
      mSecureWebSockServer->stop_listening(ec);
      mSecureWebSockServer->stop_perpetual();
   }

   if (mApiLogDb)
   {
      mApiLogDb->shutdown();
   }
}

void JsonApiServerWebSocket::waitForShutdown()
{
   if (mServerThread)
   {
      {
         std::unique_lock<std::mutex> lock(mStopServerDoneMutex);
         if (false == mStopServerDoneCv.wait_for(lock, std::chrono::seconds(5), [&] { return mStopServerDone; }))
         {
            ErrLog(<< "JsonApiServerWebSocket server thread shutdown timed out; force stopping websockpp");
            if (mWebSockServer != NULL)
            {
               mWebSockServer->stop();
            }
            if (mSecureWebSockServer != NULL)
            {
               mSecureWebSockServer->stop();
            }
         }
      }
      mServerThread->join();
      delete mServerThread;
      mServerThread = NULL;
   }
}

void JsonApiServerWebSocket::on_tls_socket_init(websocketpp::connection_hdl hdl, boost::asio::ssl::stream<boost::asio::ip::tcp::socket> & s)
{
   boost::asio::ip::tcp::no_delay option(true);
   try
   {
      s.lowest_layer().set_option(option);
   }
   catch (const std::exception& ex)
   {
      WarningLog(<< "Caught exception " << ex.what() << " in JsonApiServerWebSocket::on_tls_socket_init");
   }
#if defined(__linux__)
   DebugLog(<< "JsonApiServerWebSocket::on_tls_socket_init(): " << this);
#else
   DebugLog(<< "JsonApiServerWebSocket::on_tls_socket_init(): " << this << " running at address " << s.lowest_layer().local_endpoint());
#endif
}

void JsonApiServerWebSocket::on_socket_init(websocketpp::connection_hdl, boost::asio::ip::tcp::socket& s)
{
   try
   {
      boost::asio::ip::tcp::no_delay option(true);
      s.set_option(option);
#if defined(__linux__)
      DebugLog(<< "JsonApiServerWebSocket::on_socket_init(): " << this);
#else
      DebugLog(<< "JsonApiServerWebSocket::on_socket_init(): " << this << " running at address " << s.local_endpoint());
#endif
   }
   catch (const boost::system::system_error& ex)
   {
      ErrLog(<< "on_socket_init exception: " << ex.what());
   }
}

void JsonApiWebSocketSession::setSdkContext(JsonApiUserContext* sdkContext)
{
   PhoneInterface* pi = dynamic_cast<PhoneInterface*>(sdkContext->phone);
   if (pi != NULL)
   {
      pi->getSdkModuleThread().registerEventHandler(this);
      mSdkContext = sdkContext;
      mLocalLogger = dynamic_cast<PhoneInterface*>(sdkContext->phone)->localLogger();
   }
}

void JsonApiWebSocketSession::setAuthToken(const AuthToken& authToken)
{
   mAuthToken = authToken;
   DebugLog(<< "JsonApiWebSocketSession::setAuthToken(..) session: " << this << " user: " << authToken.cp_user);
}

void JsonApiWebSocketSession::on_message_base(websocketpp::connection_hdl hdl, const std::string& payloadData, const websocketpp::uri_ptr& requestUri)
{
   if (!mAuthToken.authToken.empty())
   {
      mRecvTimer.cancel();
   }

   size_t jsonPayloadSize = payloadData.size();

   std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
   jsonRequest->Parse<0>(payloadData.c_str());

   if (jsonRequest->HasParseError())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << " Aborting decode. Contents: " << payloadData);
      return;
   }

   if (!jsonRequest->HasMember("moduleId"))
   {
      WarningLog(<< "Missing moduleId. Aborting decode.");
      return;
   }

   const rapidjson::Value& moduleIdVal = (*jsonRequest)["moduleId"];
   if (!moduleIdVal.IsString())
   {
      WarningLog(<< "moduleId is not a string. Aborting decode.");
      return;
   }

   resip::Data moduleIdStr(resip::Data::Share, moduleIdVal.GetString(), moduleIdVal.GetStringLength());
   StackLog(<< "Handle request for module: " << moduleIdStr << " Contents: " << payloadData);

   if (moduleIdStr == "JsonApiServer")
   {
      mMasterPhone->getSdkModuleThread().post(resip::resip_bind(&JsonApiWebSocketSession::handleJsonApiServerMessage, this, hdl, jsonRequest));
   }
   else
   {
      if (mAuthToken.authToken.empty())
      {
         InfoLog(<< "Closing connection because a login request was not received prior to other API traffic");
         close();
      }
      else
      {
         std::shared_ptr<resip::Data> binaryData;
         if (payloadData.size() > jsonPayloadSize)
         {
            binaryData.reset(new resip::Data(&payloadData[jsonPayloadSize+1], payloadData.size() - jsonPayloadSize - 1));
         }

         mMsgQueue.write(SdkMessage::FuncObj,
            hdl,
            std::string(moduleIdVal.GetString(), moduleIdVal.GetStringLength()),
            jsonRequest,
            binaryData,
            requestUri->get_resource());
         mSdkThreadInterrupt.interrupt();
       }
   }
}

void JsonApiWebSocketSession::on_message(server* s, websocketpp::connection_hdl hdl, message_ptr msg)
{
   websocketpp::uri_ptr requestUri = s->get_con_from_hdl(hdl)->get_uri();
   on_message_base(hdl, msg->get_payload(), requestUri);
}

void JsonApiWebSocketSession::on_message_tls(server_tls* s, websocketpp::connection_hdl hdl, tls_message_ptr msg)
{
   websocketpp::uri_ptr requestUri = s->get_con_from_hdl(hdl)->get_uri();
   on_message_base(hdl, msg->get_payload(), requestUri);
}

void JsonApiWebSocketSession::handleJsonApiServerMessage(websocketpp::connection_hdl hdl, const std::shared_ptr<rapidjson::Document>& doc)
{
   JsonApiServerInterface* jsonApiServerIf = dynamic_cast<JsonApiServerInterface*>(CPCAPI2::JsonApi::JsonApiServer::getInterface(mMasterPhone));
   jsonApiServerIf->processIncoming(this, doc);
}

void JsonApiWebSocketSession::handleMessage(SdkMessage* sdkMsg)
{
   PhoneInterface* pi = dynamic_cast<PhoneInterface*>(mSdkContext->phone);
   JsonApiServerModule* pm = dynamic_cast<JsonApiServerModule*>(pi->getInterfaceByName(cpc::string(sdkMsg->moduleIdStr.c_str())));
   if ((mSdkContext->permissions.size() == 1 && mSdkContext->permissions.count("*") > 0) ||
      (mSdkContext->permissions.count(sdkMsg->moduleIdStr) > 0))
   {
      if (pm != NULL)
      {
         if (!sdkMsg->jsonDoc->HasMember("functionObject"))
         {
            return;
         }

         const rapidjson::Value& functionObjVal = (*sdkMsg->jsonDoc)["functionObject"];
         if (!functionObjVal.IsObject())
         {
            return;
         }

         if (mTransport->getApiLog() != NULL)
         {
            AddApiLogArgs apiLogInfo;
            apiLogInfo.userContext = mAuthToken.cp_user.c_str();
            apiLogInfo.moduleName = sdkMsg->moduleIdStr;
            apiLogInfo.functionName = functionObjVal["functionName"].GetString();
            apiLogInfo.invocationTime = ::time(NULL);
            mTransport->getApiLog()->addApiLog(apiLogInfo, [](bool res, const AddApiLogResult& resinfo) {
            });
         }

         {
            JsonApiRequestInfo connId;
            connId.transportType = JsonApiRequestInfo::JsonApiTransportType_WS;
            connId.websocket_conn = sdkMsg->hdl;
            connId.jsonUserHandle = mSdkContext->handle;
            connId.authToken = mAuthToken;
            connId.websocketUrlResource = sdkMsg->websocketUrlResource;
            pm->processIncoming(connId, sdkMsg->jsonDoc, sdkMsg->binaryData);
         }
      }
   }
   else
   {
      DebugLog(<< "no permission to access SDK module " << sdkMsg->moduleIdStr);
   }
}

void JsonApiWebSocketSession::resetHandlers(server* s, websocketpp::connection_hdl hdl)
{
   if (auto conn = s->get_con_from_hdl(hdl))
   {
      conn->set_close_handler(websocketpp::close_handler());
      conn->set_fail_handler(websocketpp::fail_handler());
      conn->set_message_handler(server::connection_type::message_handler());
   }
}

void JsonApiWebSocketSession::resetHandlersTls(server_tls* s, websocketpp::connection_hdl hdl)
{
   if (auto conn = s->get_con_from_hdl(hdl))
   {
      conn->set_close_handler(websocketpp::close_handler());
      conn->set_fail_handler(websocketpp::fail_handler());
      conn->set_message_handler(server_tls::connection_type::message_handler());
   }
}

void JsonApiWebSocketSession::on_close(server* s, websocketpp::connection_hdl hdl)
{
   LocalMaxLog("JsonApiWebSocketSession::on_close(): session: {}", (void*)this);
   DebugLog(<< "JsonApiWebSocketSession::on_close(): " << this);
   resetHandlers(s, hdl);
   cleanupOnClose();
}

void JsonApiWebSocketSession::on_close_tls(server_tls* s, websocketpp::connection_hdl hdl)
{
   LocalMaxLog("JsonApiWebSocketSession::on_close_tls(): session: {}", (void*)this);
   DebugLog(<< "JsonApiWebSocketSession::on_close_tls(): " << this);
   resetHandlersTls(s, hdl);
   cleanupOnClose();
}

void JsonApiWebSocketSession::on_fail(server* s, websocketpp::connection_hdl hdl)
{
   LocalMaxLog("JsonApiWebSocketSession::on_fail(): session: {}", (void*)this);
   DebugLog(<< "JsonApiWebSocketSession::on_fail(): " << this);
   resetHandlers(s, hdl);
   cleanupOnClose();
}

void JsonApiWebSocketSession::on_fail_tls(server_tls* s, websocketpp::connection_hdl hdl)
{
   LocalMaxLog("JsonApiWebSocketSession::on_fail_tls(): session: {}", (void*)this);
   DebugLog(<< "JsonApiWebSocketSession::on_fail_tls(): " << this);
   resetHandlersTls(s, hdl);
   cleanupOnClose();
}

void JsonApiWebSocketSession::on_termination(server* s, conn_ptr conn)
{
   /*
   LocalMaxLog("JsonApiWebSocketSession::on_termination(): session: {}", (void*)this);
   DebugLog(<< "JsonApiWebSocketSession::on_termination(): " << this);
   mMsgQueue.write(SdkMessage::Close, conn->get_handle(), std::string(), std::shared_ptr<rapidjson::Document>(), std::shared_ptr<resip::Data>(), std::string());
   mSdkThreadInterrupt.interrupt();
   */
}

void JsonApiWebSocketSession::on_termination_tls(server_tls* s, tls_conn_ptr conn)
{
   /*
   LocalMaxLog("JsonApiWebSocketSession::on_termination_tls(): session: {}", (void*)this);
   DebugLog(<< "JsonApiWebSocketSession::on_termination_tls(): " << this);
   mMsgQueue.write(SdkMessage::Close, conn->get_handle(), std::string(), std::shared_ptr<rapidjson::Document>(), std::shared_ptr<resip::Data>(), std::string());
   mSdkThreadInterrupt.interrupt();
   */
}

void JsonApiWebSocketSession::handleClose(SdkMessage* sdkMsg)
{
   if (!mSdkContext)
   {
      DebugLog(<< "JsonApiWebSocketSession::handleClose(): " << this << " sdk context not initialized");
      return;
   }

   if (!(mSdkContext->phone))
   {
      DebugLog(<< "JsonApiWebSocketSession::handleClose(): " << this << " sdk context phone not initialized");
      return;
   }

   // If a shared context with multiple sessions, need to ensure that all web sessions
   // are destroyed before the phone is destroyed, crash has been noted on this dynamic_cast
   // as in some scenarios the phone has already been destroyed before all sessions.
   PhoneInterface* pi = dynamic_cast<PhoneInterface*>(mSdkContext->phone);
   LocalMaxLog("JsonApiWebSocketSession::handleClose(): session: {} phone: {}", (void*)this, (void*)pi);
   DebugLog(<< "JsonApiWebSocketSession::handleClose(): " << this << " phone: " << pi << " resource: " << mResource);
   std::vector<PhoneModule*> phoneModules;
   pi->getAllInterfaces(phoneModules);

   std::vector<PhoneModule*>::iterator it = phoneModules.begin();
   for (; it != phoneModules.end(); ++it)
   {
      JsonApiServerModule* pm = dynamic_cast<JsonApiServerModule*>(*it);
      if (pm != NULL)
      {
         JsonApiRequestInfo connId(sdkMsg->hdl);
         StackLog(<< "JsonApiWebSocketSession::handleClose(): " << this << " PhoneModule: " << pm << " user handle: " << connId.jsonUserHandle);
         pm->handleConnectionClosed(connId);
      }
   }

   pi->getSdkModuleThread().unregisterEventHandler(this);
   mJsonApiServer->handleConnectionClosed(mSdkContext->handle, this, [](JsonApiWebSocketSession* session){ delete session; });
}

void JsonApiWebSocketSession::onRecvTimer(const boost::system::error_code& ec)
{
   if (ec)
      return;

   WarningLog(<< "Closing connection because no message was received in the allowed time.");
   close(true);
}

void JsonApiWebSocketSession::cleanupOnClose()
{
   if (mSdkContext)
   {
      mMsgQueue.write(SdkMessage::Close, mConn, std::string(), std::shared_ptr<rapidjson::Document>(), std::shared_ptr<resip::Data>(), std::string());
      mSdkThreadInterrupt.interrupt();
   }
   else
   {
      mMasterPhone->getSdkModuleThread().safeDelete(this);
   }
}

// resip::ReactorEventHandler
void JsonApiWebSocketSession::process(resip::ReactorEventHandler::FdSetType& fdset)
{
   mSdkThreadInterrupt.process(fdset);

   while (!mMsgQueue.isEmpty())
   {
      SdkMessage* sdkMsg = mMsgQueue.frontPtr();
      if (sdkMsg->type == SdkMessage::FuncObj)
      {
         handleMessage(sdkMsg);
         mMsgQueue.popFront();
      }
      else if (sdkMsg->type == SdkMessage::Close)
      {
         handleClose(sdkMsg);
         // handleClose will ultimately end up triggerring the destruction of the web socket session, don't do anything else
         break;
      }
   }
}

void JsonApiWebSocketSession::buildFdSet(resip::ReactorEventHandler::FdSetType& fdset)
{
   mSdkThreadInterrupt.buildFdSet(fdset);
}

unsigned int JsonApiWebSocketSession::getTimeTillNextProcessMS()
{
   return 10000;
}

const char* JsonApiWebSocketSession::getEventHandlerDesc() const
{
   return "JsonApiWebSocketSession";
}

void JsonApiWebSocketSession::close(bool force)
{
   DebugLog(<< "JsonApiWebSocketSession::close(): " << this << " JsonApiUserHandle: " << (json_api_user_context() ? json_api_user_context()->handle : (-1)));
   if (mTransport)
   {
      mTransport->closeConnection(mConn, force);
   }
}

void JsonApiWebSocketSession::logout()
{
   DebugLog(<< "JsonApiWebSocketSession::logout(): " << this << " JsonApiUserHandle: " << (json_api_user_context() ? json_api_user_context()->handle : (-1)));
   mIsLoggedOut = true;
}

void JsonApiServerWebSocket::on_open(server* s, websocketpp::connection_hdl hdl)
{
   std::error_code ec;
   if (conn_ptr conn = mWebSockServer->get_con_from_hdl(hdl, ec))
   {
      if (!ec)
      {
         std::stringstream os;
         os << conn->get_next_layer().remote_endpoint().address().to_string() << ":" << conn->get_next_layer().remote_endpoint().port();
         // NOTE: Careful when modifying following log as pattern matching is used to extract the log information for debugging purposes
         InfoLog(<< "JsonApiServerWebSocket::on_open(): " << this << " phone: " << mPhone << " resource: " << conn->get_resource() << " remote-endpoint: " << os.str());

         JsonApiWebSocketSession* session = new JsonApiWebSocketSession(mPhone, this, mWebSockServer->get_io_service(), hdl, conn->get_resource(), conn->get_next_layer().remote_endpoint().address().is_loopback());
         conn->set_close_handler(std::bind(&JsonApiWebSocketSession::on_close, session, mWebSockServer.get(), std::placeholders::_1));
         conn->set_fail_handler(std::bind(&JsonApiWebSocketSession::on_fail, session, mWebSockServer.get(), std::placeholders::_1));
         //conn->set_termination_handler(std::bind(&JsonApiWebSocketSession::on_termination, session, mWebSockServer.get(), std::placeholders::_1));
         conn->set_message_handler(std::bind(&JsonApiWebSocketSession::on_message, session, mWebSockServer.get(), std::placeholders::_1, std::placeholders::_2));
      }
   }
}

void JsonApiServerWebSocket::on_open_tls(server_tls* s, websocketpp::connection_hdl hdl)
{
   std::error_code ec;
   if (tls_conn_ptr conn = mSecureWebSockServer->get_con_from_hdl(hdl, ec))
   {
      if (!ec)
      {
         JsonApiWebSocketSession* session = new JsonApiWebSocketSession(mPhone, this, mSecureWebSockServer->get_io_service(), hdl, conn->get_resource(), conn->get_next_layer().remote_endpoint().address().is_loopback());
         conn->set_close_handler(std::bind(&JsonApiWebSocketSession::on_close_tls, session, mSecureWebSockServer.get(), std::placeholders::_1));
         conn->set_fail_handler(std::bind(&JsonApiWebSocketSession::on_fail_tls, session, mSecureWebSockServer.get(), std::placeholders::_1));
         //conn->set_termination_handler(std::bind(&JsonApiWebSocketSession::on_termination_tls, session, mSecureWebSockServer.get(), std::placeholders::_1));
         conn->set_message_handler(std::bind(&JsonApiWebSocketSession::on_message_tls, session, mSecureWebSockServer.get(), std::placeholders::_1, std::placeholders::_2));

         InfoLog(<< "JsonApiServerWebSocket::on_open_tls(): " << this << " phone: " << mPhone << " resource: " << conn->get_resource() << " session: " << session);
      }
   }
}

std::string JsonApiServerWebSocket::get_password() const
{
  // TODO: should this be hard coded?
  return "zCQ/t):*d_N\\G*r=m3-p";
}

#if !defined(DISABLE_WSS_JSON_SERVER)
inline bool file_exists(const std::string& name) {
   std::ifstream f(name.c_str());
   return f.good();
}

websocketpp::lib::shared_ptr<boost::asio::ssl::context> JsonApiServerWebSocket::on_tls_init(server_tls* srvr, websocketpp::connection_hdl hdl)
{
   if (mTlsContext.get() == NULL)
   {
      SslCipherOptions tlsOptions = dynamic_cast<PhoneInterface*>(mPhone)->getSslCipherOptions();
      websocketpp::lib::shared_ptr<boost::asio::ssl::context> context = initializeBoostTlsContext(tlsOptions.getTLSVersion(SslCipherUsageWebSockets), tlsOptions.getCiphers(SslCipherUsageWebSockets).c_str(), resip::SecurityTypes::TLSMode_TLS_Server, mConfig.wssDiffieHellmanParamsFilePath.c_str());
      try
      {
         // https://stackoverflow.com/questions/6452756/exception-running-boost-asio-ssl-example
         context->set_password_callback(std::bind(&JsonApiServerWebSocket::get_password, this));

         if (mConfig.wssCertificateFilePath.size() < 256 && file_exists(mConfig.wssCertificateFilePath.c_str()))
         {
            context->use_certificate_chain_file(mConfig.wssCertificateFilePath.c_str());
         }
         else if (file_exists("server.crt"))
         {
            context->use_certificate_chain_file("server.crt");
         }
         else if (!mConfig.wssCertificateFilePath.empty())
         {
            context->use_certificate(boost::asio::const_buffer(mConfig.wssCertificateFilePath.c_str(), mConfig.wssCertificateFilePath.size()), boost::asio::ssl::context::pem);
         }

         if (mConfig.wssPrivateKeyFilePath.size() < 256 && file_exists(mConfig.wssPrivateKeyFilePath.c_str()))
         {
            context->use_private_key_file(mConfig.wssPrivateKeyFilePath.c_str(), boost::asio::ssl::context::pem);
         }
         else if (file_exists("server.key"))
         {
            context->use_private_key_file("server.key", boost::asio::ssl::context::pem);
         }
         else if (!mConfig.wssPrivateKeyFilePath.empty())
         {
            context->use_private_key(boost::asio::const_buffer(mConfig.wssPrivateKeyFilePath.c_str(), mConfig.wssPrivateKeyFilePath.size()), boost::asio::ssl::context::pem);
         }

         if (mConfig.wssDiffieHellmanParamsFilePath.size() < 256 && file_exists(mConfig.wssDiffieHellmanParamsFilePath.c_str()))
         {
            context->use_tmp_dh_file(mConfig.wssDiffieHellmanParamsFilePath.c_str());
         }
         else if (file_exists("dh2048.pem"))
         {
            context->use_tmp_dh_file("dh2048.pem");
         }
         else if (!mConfig.wssDiffieHellmanParamsFilePath.empty())
         {
            context->use_tmp_dh(boost::asio::const_buffer(mConfig.wssDiffieHellmanParamsFilePath.c_str(), mConfig.wssDiffieHellmanParamsFilePath.size()));
         }
         mTlsContext = context;
      }
      catch (std::exception& e)
      {
         ErrLog(<< "Encountered an exception while trying to setup TLS/certs for the websocket:" << e.what());
      }
   }

   if (mOnPrivilegedAccessCompleted) mOnPrivilegedAccessCompleted();

   return mTlsContext;
}
#endif // USE_WSS_JSON_SERVER

int JsonApiServerWebSocket::send(const CPCAPI2::Json::JsonDataPointer& jsonData, const websocketpp::connection_hdl& hdl)
{
   // StackLog(<< "JsonApiServerWebSocket::send(): " << this << " data: " << jsonData);
   JsonDataWebSocketCommand command(jsonData);
   websocketpp::lib::error_code err;
   if (mWebSockServer != NULL)
   {
      if (!command.sendMessage(0, hdl, *mWebSockServer, err))
      {
         DebugLog(<< "JsonApiServerWebSocket::send(): " << this << " Problem while sending: " << err.message().c_str());
         return false;
      }
   }
   else if (mSecureWebSockServer != NULL)
   {
      if (!command.sendMessage(0, hdl, *mSecureWebSockServer, err))
      {
         DebugLog(<< "JsonApiServerWebSocket::send(): " << this << " Problem while sending: " << err.message().c_str());
         return false;
      }
   }

   return 0;
}

int JsonApiServerWebSocket::send(const std::function<void(std::string&)>& buff_fill_fn, size_t size_hint, const websocketpp::connection_hdl& hdl)
{
   std::error_code ec;
   if (mWebSockServer)
   {
      message_ptr msgptr = get_message_ptr(hdl, size_hint);
      if (msgptr.get() != NULL)
      {
         buff_fill_fn(msgptr->get_raw_payload());
         mWebSockServer->send(hdl, msgptr, ec);
         return 0;
      }
   }
   else if (mSecureWebSockServer)
   {
      tls_message_ptr msgptr = get_tls_message_ptr(hdl, size_hint);
      if (msgptr.get() != NULL)
      {
         buff_fill_fn(msgptr->get_raw_payload());
         mSecureWebSockServer->send(hdl, msgptr, ec);
         if (ec)
         {
            ErrLog(<< "JsonApiServerWebSocket::send(..) error: " << ec.value() << ": " << ec.message());
         }
         return 0;
      }
   }
   return -1;
}

int JsonApiServerWebSocket::closeConnection(const websocketpp::connection_hdl& hdl, bool force)
{
   std::error_code ec;
   auto close_status = (force ? websocketpp::close::status::force_tcp_drop : websocketpp::close::status::going_away);
   if (mWebSockServer != NULL)
   {
      mWebSockServer->close(hdl, close_status, "shutdown", ec);
   }
   else if (mSecureWebSockServer != NULL)
   {
      mSecureWebSockServer->close(hdl, close_status, "shutdown", ec);
   }
   return 0;
}

message_ptr JsonApiServerWebSocket::get_message_ptr(websocketpp::connection_hdl hdl, size_t msgsize)
{
   std::error_code ec;
   if (conn_ptr conn = mWebSockServer->get_con_from_hdl(hdl, ec))
   {
      if (!ec)
      {
         message_ptr msg = conn->get_message(websocketpp::frame::opcode::binary, msgsize);
         return msg;
      }
   }

   StackLog(<< "JsonApiServerWebSocket::get_message_ptr(): " << this << " no valid conn_ptr for handle: ec: " << ec);
   return message_ptr();
}

tls_message_ptr JsonApiServerWebSocket::get_tls_message_ptr(websocketpp::connection_hdl hdl, size_t msgsize)
{
   std::error_code ec;
   if (tls_conn_ptr conn = mSecureWebSockServer->get_con_from_hdl(hdl, ec))
   {
      if (!ec)
      {
         tls_message_ptr msg = conn->get_message(websocketpp::frame::opcode::binary, msgsize);
         return msg;
      }
   }

   WarningLog(<< "JsonApiServerWebSocket::get_tls_message_ptr(): " << this << " no valid conn_ptr for handle: ec: " << ec);
   return tls_message_ptr();
}

int JsonApiServerWebSocket::LogStream::overflow(int ch)
{
   buffer.push_back(static_cast<char>(ch));
   if (ch == '\n') 
   {
      // End of line, write to logging output and clear buffer.

      DebugLog(<< "websocketpp: " << buffer);

      buffer.clear();
   }

   return ch;

   //  Return traits::eof() for failure.
}

}

}

#endif
