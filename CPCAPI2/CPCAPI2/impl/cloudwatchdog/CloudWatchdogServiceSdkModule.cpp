#include "brand_branded.h"

#include "cloudwatchdog/CloudWatchdogService.h"

#if (CPCAPI2_BRAND_CLOUD_WATCHDOG_SERVER_MODULE == 1)
#include "cloudwatchdog/CloudWatchdogServiceInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace CloudWatchdog
{
   CloudWatchdogService* CloudWatchdogService::getInterface(Phone* cpcPhone)
   {
#if (CPCAPI2_BRAND_CLOUD_SERVICE_CONFIG_MODULE == 1) && (CPCAPI2_BRAND_CLOUD_WATCHDOG_SERVER_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<CloudWatchdogServiceInterface>(phone, "CloudWatchdogService");
#else
      return NULL;
#endif
   }

}
}
