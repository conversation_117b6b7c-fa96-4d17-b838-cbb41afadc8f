#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_CONVERSATIONSTATE_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_CONVERSATIONSTATE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  enum class ConversationState
  {
    None,
    LocalOriginated,
    RemoteOriginated,
    RemoteRinging,
    LocalRinging,
    Connected,
    Early,
    Ended
  };

  inline std::string to_string(ConversationState value)
  {
    switch (value)
    {
      case ConversationState::None:
        return "None";
      case ConversationState::LocalOriginated:
        return "LocalOriginated";
      case ConversationState::RemoteOriginated:
        return "RemoteOriginated";
      case ConversationState::RemoteRinging:
        return "RemoteRinging";
      case ConversationState::LocalRinging:
        return "LocalRinging";
      case ConversationState::Connected:
        return "Connected";
      case ConversationState::Early:
        return "Early";
      case ConversationState::Ended:
        return "Ended";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, ConversationState value)
  {
    switch (value)
    {
      case ConversationState::None:
        os << "None";
        break;
      case ConversationState::LocalOriginated:
        os << "LocalOriginated";
        break;
      case ConversationState::RemoteOriginated:
        os << "RemoteOriginated";
        break;
      case ConversationState::RemoteRinging:
        os << "RemoteRinging";
        break;
      case ConversationState::LocalRinging:
        os << "LocalRinging";
        break;
      case ConversationState::Connected:
        os << "Connected";
        break;
      case ConversationState::Early:
        os << "Early";
        break;
      case ConversationState::Ended:
        os << "Ended";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const ConversationState& e)
  {
    switch (e)
    {
      case ConversationState::None:
        j = "None";
        break;
      case ConversationState::LocalOriginated:
        j = "LocalOriginated";
        break;
      case ConversationState::RemoteOriginated:
        j = "RemoteOriginated";
        break;
      case ConversationState::RemoteRinging:
        j = "RemoteRinging";
        break;
      case ConversationState::LocalRinging:
        j = "LocalRinging";
        break;
      case ConversationState::Connected:
        j = "Connected";
        break;
      case ConversationState::Early:
        j = "Early";
        break;
      case ConversationState::Ended:
        j = "Ended";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, ConversationState& e)
  {
      if (0 == j.get<std::string>().compare("None"))
    {
      e = ConversationState::None;
    }
    else if (0 == j.get<std::string>().compare("LocalOriginated"))
    {
      e = ConversationState::LocalOriginated;
    }
    else if (0 == j.get<std::string>().compare("RemoteOriginated"))
    {
      e = ConversationState::RemoteOriginated;
    }
    else if (0 == j.get<std::string>().compare("RemoteRinging"))
    {
      e = ConversationState::RemoteRinging;
    }
    else if (0 == j.get<std::string>().compare("LocalRinging"))
    {
      e = ConversationState::LocalRinging;
    }
    else if (0 == j.get<std::string>().compare("Connected"))
    {
      e = ConversationState::Connected;
    }
    else if (0 == j.get<std::string>().compare("Early"))
    {
      e = ConversationState::Early;
    }
    else if (0 == j.get<std::string>().compare("Ended"))
    {
      e = ConversationState::Ended;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum ConversationState"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_CONVERSATIONSTATE_H
