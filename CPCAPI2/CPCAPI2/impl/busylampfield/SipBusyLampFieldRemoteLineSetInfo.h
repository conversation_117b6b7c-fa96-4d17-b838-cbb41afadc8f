#pragma once

#if !defined(__CPCAPI2_SIP_BUSY_LAMP_FIELD_REMOTE_LINE_SET_INFO_H__)
#define __CPCAPI2_SIP_BUSY_LAMP_FIELD_REMOTE_LINE_SET_INFO_H__

#include "cpcapi2defs.h"
#include "busylampfield/SipBusyLampFieldTypes.h"

#include <map>

namespace CPCAPI2
{
namespace SipBusyLampField
{

// we keep track of the subscription state of a resource list or an individual line
enum SipBusyLampFieldSubscriptionState
{
   DefaultSubscribing,  // app is interested in this subscription and state is either subscribing or have been subscribed
   ResubscribePending,  // app is interested in this subscription but server has terminated, resubscribe is queued
   Unsubscribed         // app has specifically unsubscrubed, no resubscribe
};

struct SipBusyLampFieldRemoteLineInfo
{
   SipBusyLampFieldRemoteLineInfo() : mSubscriptionHandle(0) {};

   CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle mSubscriptionHandle;
   cpc::string mRemoteLineName;
};

struct SipBusyLampFieldRemoteLineSetInfo
{
   SipBusyLampFieldRemoteLineSetInfo(SipBusyLampFieldRemoteLineSetHandle handle);
   ~SipBusyLampFieldRemoteLineSetInfo();

   int mHandle;
   int mAccountHandle;
   SipBusyLampFieldRemoteLineSetSettings mSettings;
   std::map<SipBusyLampFieldRemoteLineHandle, SipBusyLampFieldRemoteLineInfo> mRemoteLineSubscriptions;
   CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle mResourceListSubscription;
   // whether the subscription is to a resource list or an individual line it'll appear in this list
   std::map<CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle, SipBusyLampFieldSubscriptionState> mSubscriptionStates;

   static SipBusyLampFieldRemoteLineSetHandle nextRemoteLineSet;
};
typedef std::map<SipBusyLampFieldRemoteLineSetHandle, SipBusyLampFieldRemoteLineSetInfo*> SipBusyLampFieldRemoteLineSetMap;

}
}

#endif // __CPCAPI2_SIP_BUSY_LAMP_FIELD_REMOTE_LINE_SET_INFO_H__
