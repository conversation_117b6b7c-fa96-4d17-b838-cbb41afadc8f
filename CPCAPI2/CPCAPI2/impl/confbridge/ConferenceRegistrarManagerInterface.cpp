#include "brand_branded.h"

#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_REGISTRAR_MODULE == 1)
#include "ConferenceRegistrarManagerInterface.h"
#include "ConferenceRegistrarManagerImpl.h"
#include "util/IpHelpers.h"
#include "json/JsonHelper.h"
#include "jsonapi/JsonApiServerSendTransportInterface.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "jsonapi/JsonApiServer_HTTP.h"
#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER

namespace CPCAPI2
{
namespace ConferenceBridge
{
ConferenceRegistrarManagerInterface::ConferenceRegistrarManagerInterface(Phone* phone)
   : CPCAPI2::EventSource<ConferenceRegistrarHandle, ConferenceRegistrarHandler, ConferenceRegistrarSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
     mPhone(dynamic_cast<PhoneInterface*>(phone))
{
}

ConferenceRegistrarManagerInterface::~ConferenceRegistrarManagerInterface()
{
}

void ConferenceRegistrarManagerInterface::Release()
{
   mImpl.reset();
   delete this;
}

int ConferenceRegistrarManagerInterface::setExternalConferenceRegistrar(ConferenceRegistrar* confRegistrar)
{
   mPhone->getSdkModuleThread().post(resip::resip_static_bind([&, confRegistrar]() {
      mImpl = dynamic_cast<ConferenceRegistrarManagerInterface*>(confRegistrar)->mImpl;
   }));
   return kSuccess;
}

void ConferenceRegistrarManagerInterface::setHandler(ConferenceRegistrarHandler* handler)
{
   setAppHandler(0, handler);
}

int ConferenceRegistrarManagerInterface::start(const ConferenceRegistrarConfig& confRegistrarConfig)
{
   mPhone->getSdkModuleThread().post(resip::resip_static_bind([&, confRegistrarConfig]() {
      if (mImpl.get() == NULL)
      {
         mImpl.reset(new ConferenceRegistrarManagerImpl(mPhone, this));
      }
      mImpl->start(confRegistrarConfig);
   }));
   return kSuccess;
}

int ConferenceRegistrarManagerInterface::shutdown()
{
   mPhone->getSdkModuleThread().post(resip::resip_static_bind([&]() {
      mImpl->shutdown();
   }));
   return kSuccess;
}

int ConferenceRegistrarManagerInterface::lookupConference(const cpc::string& joinUrl, LookupConferenceResultHandler* handler)
{
   mImpl->lookupConference(joinUrl, -1, handler);
   return kSuccess;
}

int ConferenceRegistrarManagerInterface::listNodes(ListNodesHandler* handler)
{
   mPhone->getSdkModuleThread().post(resip::resip_static_bind([&, handler]() {
      mImpl->listNodes(handler);
   }));
   return kSuccess;
}

bool ConferenceRegistrarManagerInterface::isInService()
{
   return mImpl->isPartOfCluster();
}
}
}

#endif
