#pragma once

#if !defined(__CPCAPI2_LICENSE_DOC_PARSER_H__)
#define __CPCAPI2_LICENSE_DOC_PARSER_H__

#include "LicensingTypes.h"
#include <libxml/tree.h>
#include <libxml/xmlreader.h>
#include <libxml/xmlstring.h>

namespace CPCAPI2
{
namespace Licensing
{

class LicenseDocParser
{
public:
   //typedef std::pair<std::string, bool> FeatureItem;
   //typedef std::vector<std::string> FeatureList;

   enum LicDocType
	{
      Error,
		Valid,
		Grace
	};

   LicenseDocParser(const std::string& document);

   int version() const  { return m_DocVersion; }
   LicDocType type() const                   { return m_Type; }
   LicenseList licenses() const              { return m_Licenses; }
   InvalidLicenseList invalidLicenses() const { return m_InvalidLicenses; }
   //const std::string& licenseType() const    { return m_LicenseType; }
   const std::string& userPortion() const    { return m_UserPortion; }
   const std::string& machineHash() const    { return m_MachineHash; }
   const std::string& instanceKey() const    { return m_InstanceKey; }
   //const std::string& key() const            { return m_Key; }
   const std::string& message() const        { return m_Message; }
   const std::string& url() const            { return m_Url; }
   const std::string& urlDescription() const { return m_UrlDescription; }
   const std::string& primaryMac() const     { return m_PrimaryMac; }
   const std::string& primaryHdd() const     { return m_PrimaryHdd; }
   const std::string& certificateId() const  { return m_CertificateId; }
   unsigned int checkDelay() const           { return m_CheckDelay; }
   unsigned int minInterval() const          { return m_MinInterval; }
   unsigned int endPercent() const           { return m_EndPercent; }
   unsigned long errorCode() const           { return m_ErrorCode; }
   time_t expiry() const                     { return m_Expiry; }
   //time_t fresh() const                      { return m_Refresh; }
   time_t startTime() const                  { return m_StartTime; }
   time_t duration() const                   { return m_Duration; }
   //const std::vector<XsLib::CXsString>& allMacs() const { return m_AllMacs; }
   const std::vector<std::string>& allMacs() const { return m_AllMacs; }
   //const std::vector<XsLib::CXsString>& allHdds() const { return m_AllHdds; }
   const std::vector<std::string>& allHdds() const { return m_AllHdds; }
   //FeatureList features() const;
   const std::string& brand() const { return m_Brand; }

private:
   void Parse(const std::string& document);
   void ParseMessage(xmlTextReaderPtr reader);
   
   
   void ParseMachineInformation(xmlTextReaderPtr);
   void ParseLicenses(xmlTextReaderPtr);
   License ParseLicense(xmlTextReaderPtr);
   void ParseErrors(xmlTextReaderPtr);
   void ParseError(xmlTextReaderPtr);
   void ParseErrorPre8(xmlTextReaderPtr);
   void ParseGlobalError(xmlTextReaderPtr);

   void ParseVersion8(xmlTextReaderPtr);
   void ParseVersionPre8(xmlTextReaderPtr);
   std::vector<std::string> parseFeatures(xmlTextReaderPtr);

   int            m_DocVersion;
   LicDocType     m_Type;
   std::string    m_LicenseType;
   std::string    m_MachineHash;
   std::string    m_InstanceKey;
   //std::string    m_Key;
   std::string    m_UserPortion;
   std::string    m_Message;
   std::string    m_Url;
   std::string    m_UrlDescription;
   std::string    m_PrimaryMac;
   std::string    m_PrimaryHdd;
   std::string    m_CertificateId;
   unsigned int   m_CheckDelay;
   unsigned int   m_MinInterval;
   unsigned int   m_EndPercent;
   unsigned long  m_ErrorCode;
   time_t         m_Expiry;
   //time_t         m_Refresh;
   time_t         m_StartTime;
   time_t         m_Duration;
   //std::vector<XsLib::CXsString>   m_AllMacs;
   std::vector<std::string>   m_AllMacs;
   //std::vector<XsLib::CXsString>   m_AllHdds;
   std::vector<std::string>   m_AllHdds;
   //FeatureList m_Features;   
   LicenseList m_Licenses;
   InvalidLicenseList m_InvalidLicenses;
   std::string m_Brand;

};

}
}

#endif // __CPCAPI2_LICENSE_DOC_PARSER_H__
