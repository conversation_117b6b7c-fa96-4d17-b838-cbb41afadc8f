#include "brand_branded.h"

#if (CPCAPI2_BRAND_CONFERENCE_CONNECTOR_MODULE == 1)
#include "ConferenceConnectorImpl.h"
#include "ConferenceConnectorInterface.h"
#include "cloudconnector/CloudServerConnection.h"
#include "cloudconnector/ServiceDesc.h"
#include "confconnector/ConferenceConnectorHandler.h"
#include "phone/PhoneInterface.h"
#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"
#include "util/DeviceInfo.h"
#include "auth_server/AuthServerJwtUtils.h"
#include "confbridge/jsonapi/ConferenceBridgeJsonProxyInterface.h"
#include "peerconnection/PeerConnectionManagerInterface.h"
#include "media/VideoInterface.h"

#include <iomanip>

#include <rutil/ParseBuffer.hxx>
#include <rutil/Random.hxx>

#include <client_https.hpp>
#include "websocketpp/uri.hpp"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>
#include <sstream>

#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::CONF_CONNECTOR

using CPCAPI2::CloudConnector::CloudServerConnection;
using namespace CPCAPI2::ConferenceBridge;
using namespace CPCAPI2::PeerConnection;

namespace CPCAPI2
{
namespace ConferenceConnector
{
class CreateWebParticipantHandlerDispatcher : public CPCAPI2::ConferenceBridge::CreateWebParticipantHandler
{
public:
   CreateWebParticipantHandlerDispatcher(const std::function<int(CPCAPI2::ConferenceBridge::ConferenceHandle, const CPCAPI2::ConferenceBridge::WebParticipantCreatedEvent&)>& fn) : mFn(fn) {}
   virtual ~CreateWebParticipantHandlerDispatcher() {}

   virtual int onWebParticipantCreated(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::WebParticipantCreatedEvent& evt) OVERRIDE
   {
      mFn(conference, evt);
      delete this;
      return kSuccess;
   }
private:
   std::function<int(CPCAPI2::ConferenceBridge::ConferenceHandle, const CPCAPI2::ConferenceBridge::WebParticipantCreatedEvent&)> mFn;
};

std::shared_ptr<ConferenceConnectorImpl> ConferenceConnectorImpl::Create(ConferenceConnectorInterface* ccif, ConferenceConnectorHandle h)
{
   std::shared_ptr<ConferenceConnectorImpl> ret(new ConferenceConnectorImpl(ccif, h));
   ret->init();
   return ret;
}


ConferenceConnectorImpl::ConferenceConnectorImpl(ConferenceConnectorInterface* ccif, ConferenceConnectorHandle h)
   : mInterface(ccif),
     mHandle(h),
     mWebRequestThread("ConferenceConnectorImpl"),
     mConfServiceConnStatus(CloudConnector::CloudServerConnStatus_Disconnected),
     mConfPhone(NULL),
     mConfBridgeMgr(NULL),
     mVideoIf(NULL)
{
   StackLog(<< "ConferenceConnectorImpl::ConferenceConnectorImpl(): " << this << " handle: " << mHandle);
   mWebRequestThread.start();

   mPeerConnMgr = CPCAPI2::PeerConnection::PeerConnectionManager::getInterface(mInterface->getThisSdkPhone());

   mVideoStreamingMgr = CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(mInterface->getThisSdkPhone());

   CPCAPI2::Media::MediaManager* mm = CPCAPI2::Media::MediaManager::getInterface(mInterface->getThisSdkPhone());
   mVideoIf = CPCAPI2::Media::Video::getInterface(mm);
   mVideoExtIf = CPCAPI2::Media::VideoExt::getInterface(mm);

   mConferenceConnectorDisconnecting = false;
   mConferenceConnectorDestroyed = false;
   mConferenceConnectorDisconnectState = static_cast<int>(ConnectionDisconnectState_None);
}

ConferenceConnectorImpl::~ConferenceConnectorImpl()
{
   DebugLog(<< "ConferenceConnectorImpl::~ConferenceConnectorImpl(): " << this << " handle: " << mHandle);

   if (mConfPhone != NULL)
   {
      CPCAPI2::ConferenceBridge::ConferenceBridgeManagerJsonProxy* confBridgeMgr = CPCAPI2::ConferenceBridge::ConferenceBridgeManagerJsonProxy::getInterface(mConfPhone);
      if (confBridgeMgr != NULL)
      {
         dynamic_cast<ConferenceBridgeJsonProxyInterface*>(confBridgeMgr)->clearQueryConferenceListHandler();
      }
   }

   mConferenceConnectorDisconnecting = false;
   mConferenceConnectorDestroyed = true;
   mConferenceConnectorDisconnectState = static_cast<int>(ConnectionDisconnectState_None);

   mWebRequestThread.stop();
   mWebRequestThread.join();
   
}

void ConferenceConnectorImpl::init()
{
   dynamic_cast<PeerConnectionManagerInterface*>(mPeerConnMgr)->addSdkObserverSafe(shared_from_this());
}

int ConferenceConnectorImpl::destroy()
{
   DebugLog(<< "ConferenceConnectorImpl::destroy(): " << this << get_debug_string(*this));

   if (mConfPhone != NULL)
   {
      CPCAPI2::ConferenceBridge::ConferenceBridgeManagerJsonProxy* confBridgeMgr = CPCAPI2::ConferenceBridge::ConferenceBridgeManagerJsonProxy::getInterface(mConfPhone);
      if (confBridgeMgr != NULL)
      {
         dynamic_cast<ConferenceBridgeJsonProxyInterface*>(confBridgeMgr)->clearQueryConferenceListHandler();
      }
   }

   std::map<CloudConferenceSessionHandle, SessionState>::iterator itSess = mSessStateMap.begin();
   for (; itSess != mSessStateMap.end();)
   {
      CloudConferenceSessionHandle h = itSess->first;
      // endSession removes from mSessStateMap
      ++itSess;
      endSession(h, false);
   }

   mConferenceConnectorDestroyed = true;
   disconnectService();
   release();

   auto itConf = mConfStateMap.begin();
   for (; itConf != mConfStateMap.end(); ++itConf)
   {
      mInterface->unmapConferenceHandle(itConf->cloudConfHandle);
   }

   // safe to do this because we have an explicit dependency on PeerConnectionManager in ConferenceConnectorSdkModule
   dynamic_cast<PeerConnectionManagerInterface*>(mPeerConnMgr)->removeSdkObserverSafe(shared_from_this());

   return kSuccess;
}

int ConferenceConnectorImpl::setConnectionSettings(const ConferenceConnectorSettings& settings)
{
   mSettings = settings;
   if (!mSettings.authServerApiKey.empty())
   {
      resip::Data apiKeyFixedUp;
      resip::ParseBuffer pb(mSettings.authServerApiKey.c_str());
      //const char* anchor = pb.position();
      {
         resip::DataStream ds(apiKeyFixedUp);
         ds << "-----BEGIN PUBLIC KEY-----\n";
         if (resip::Data(resip::Data::Share, pb.position()).prefix("-----BEGIN PUBLIC KEY-----"))
         {
            pb.skipChars("-----BEGIN PUBLIC KEY-----");
         }
         if (*pb.position() == '\n')
         {
            pb.skipChars("\n");
         }
         const char* anchor = pb.position();
         pb.skipN(64);
         ds << pb.data(anchor);
         ds << "\n";
         if (*pb.position() == '\n')
         {
            pb.skipChars("\n");
         }
         anchor = pb.position();
         if (resip::Data(resip::Data::Share, pb.position()).find("-----END PUBLIC KEY-----") != resip::Data::npos)
         {
            pb.skipToChars("-----END PUBLIC KEY-----");
            pb.skipBackWhitespace();
         }
         else
         {
            pb.skipToChar('=');
            while (!pb.eof() && *pb.position() == '=')
            {
               pb.skipChar();
            }
         }
         ds << pb.data(anchor);
         ds << "\n";
         ds << "-----END PUBLIC KEY-----";
      }
      mSettings.authServerApiKey = apiKeyFixedUp.c_str();
   }
   return kSuccess;
}

int ConferenceConnectorImpl::requestService()
{
   return kSuccess;
}

class ConfBridgeRequestor
{
public:
   ConfBridgeRequestor(resip::MultiReactor& requestServiceThread, resip::MultiReactor& callbackThread, const resip::Data& joinUrl, const resip::Data& authToken, bool ignoreCertVerification, SslCipherOptions tlsSettings, int orchTimeoutSecs, const std::function<void(int, const std::string)>& resultCb)
      : mReactor(requestServiceThread),
      mCallbackThread(callbackThread),
      mJoinUrl(joinUrl),
      mAuthToken(authToken),
      mIgnoreCertVerification(ignoreCertVerification), 
      mTlsSettings(tlsSettings),
      mOrchTimeoutSecs(orchTimeoutSecs),
      mResultCb(resultCb)
   {
   }
   ~ConfBridgeRequestor()
   {
   }

   void start()
   {
      mReactor.post(resip::resip_bind(&ConfBridgeRequestor::doGetConferenceWsUrlFlow, this));
   }

private:
   void doGetConferenceWsUrlFlow()
   {
      std::unique_ptr<ConfBridgeRequestor> thisDeleter(this);
      std::string websocketUrl;
      std::string orchServerUrl(mJoinUrl.c_str(), mJoinUrl.size());

      try
      {
         int response = 0;
         std::string responseBodyStr;
         for (int retries = 0; retries < 10; retries++)
         {
#if 1
            /* now hit orchestration server */
            CurlPPHelper helper;
            curlpp::Easy confbridgeRequest;
            //std::string orchMessageBody = "{"
            //   "\"moduleId\":\"OrchestrationServer\","
            //   "\"functionObject\" : {"
            //   "\"functionName\":\"requestService\","
            //   "   \"serviceRequests\" : [{"
            //   "      \"service\" : \"xmppagent\","
            //   "      \"region\" : \"NA\""
            //   "}]"
            //   "}"
            //   "}";

            helper.setDefaultOptions(confbridgeRequest, orchServerUrl, "POST");
            helper.setTimeoutOption(confbridgeRequest, mOrchTimeoutSecs);

            std::list<std::string> header;
            header.push_back(std::string("Authorization: bearer ") + mAuthToken.c_str());
            confbridgeRequest.setOpt(new curlpp::options::HttpHeader(header));

            int acceptableFailures(0);
            if (mIgnoreCertVerification)
               acceptableFailures = (int)CurlPPSSL::E_CERT_WHATEVER_ERROR;

            CurlPPSSL cssl(mTlsSettings, acceptableFailures);
            confbridgeRequest.setOpt(new curlpp::options::SslCtxFunction(cssl));

            std::stringstream responseBody;
            confbridgeRequest.setOpt(new curlpp::options::WriteStream(&responseBody));
            confbridgeRequest.perform();

            response = curlpp::infos::ResponseCode::get(confbridgeRequest);

            if (response != 408)
            {
               responseBodyStr = responseBody.str();
               break;
            }

            std::this_thread::sleep_for(std::chrono::seconds(1));
#else
            resip::Data ourld(resip::Data::Share, orchServerUrl.c_str(), orchServerUrl.size());
            if (!ourld.postfix("/"))
            {
               orchServerUrl = orchServerUrl + "/";
            }
            websocketpp::uri urlParsed(orchServerUrl);
            SimpleWeb::Client<SimpleWeb::HTTPS> swClient(urlParsed.get_host_port(), false);

            SimpleWeb::CaseInsensitiveMultimap reqHeaders;
            reqHeaders.emplace("Authorization", std::string("bearer ") + mAuthToken.c_str());

            auto swResp = swClient.request("POST", urlParsed.get_resource(), "", reqHeaders);
            resip::Data respCodeStr = swResp->status_code.c_str();
            response = respCodeStr.convertInt();
            responseBodyStr = swResp->content.string();

            if (response == 301)
            {
               auto itLoc = swResp->header.find("Location");
               if (itLoc != swResp->header.end())
               {
                  orchServerUrl = itLoc->second;
               }
            }
            else if (response != 408)
            {
               break;
            }

            std::this_thread::sleep_for(std::chrono::seconds(1));
#endif
         }

         StackLog(<< "ConferenceConnectorImpl::doGetConferenceWsUrlFlow(): orchServer: " << orchServerUrl << " response: " << responseBodyStr);
         if (response == 200)
         {
            std::unique_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
            jsonRequest->Parse<0>(responseBodyStr.c_str());
            if (jsonRequest->HasParseError())
            {
               DebugLog(<< "ConferenceConnectorImpl::doGetConferenceWsUrlFlow(): response parse error");
               postFailureCallback();
               return;
            }

            if (!jsonRequest->HasMember("websocketUrl"))
            {
               DebugLog(<< "ConferenceConnectorImpl::doGetConferenceWsUrlFlow(): response missing websocketUrl");
               postFailureCallback();
               return;
            }

            const rapidjson::Value& websocketUrlVal = (*jsonRequest)["websocketUrl"];
            if (!websocketUrlVal.IsString())
            {
               DebugLog(<< "ConferenceConnectorImpl::doGetConferenceWsUrlFlow(): response websocketUrl type mismatch");
               postFailureCallback();
               return;
            }

            websocketUrl = websocketUrlVal.GetString();
            assert(!websocketUrl.empty());

            resip::ReadCallbackBase* cb = resip::resip_static_bind(mResultCb, 0, websocketUrl);
            mCallbackThread.post(cb);
         }
         else
         {
            DebugLog(<< "ConferenceConnectorImpl::doGetConferenceWsUrlFlow(): failure response: " << response);
            postFailureCallback();
         }
      }
      catch (curlpp::LibcurlRuntimeError& e)
      {
         DebugLog(<< "ConferenceConnectorImpl::doGetConferenceWsUrlFlow(): runtime error: " << e.what());
         // Use -2 to indicate a timeout
         postFailureCallback(e.whatCode() == CURLE_OPERATION_TIMEDOUT ? -2 : -1);
      }
      catch (curlpp::RuntimeError& e)
      {
         DebugLog(<< "ConferenceConnectorImpl::doGetConferenceWsUrlFlow(): runtime error: " << e.what());
         postFailureCallback();
      }
      catch (const SimpleWeb::system_error& e)
      {
         DebugLog(<< "ConferenceConnectorImpl::doGetConferenceWsUrlFlow(): runtime error: " << e.what());
         postFailureCallback();
      }
   }

   void postFailureCallback(int resultCode = -1)
   {
      resip::ReadCallbackBase* cb = resip::resip_static_bind(mResultCb, resultCode, "");
      mCallbackThread.post(cb);
   }

private:
   resip::MultiReactor& mReactor;
   resip::MultiReactor& mCallbackThread;
   resip::Data mJoinUrl;
   resip::Data mAuthToken;
   bool mIgnoreCertVerification;
   SslCipherOptions mTlsSettings;
   int mOrchTimeoutSecs;
   std::function<void(int, const std::string&)> mResultCb;
};

int ConferenceConnectorImpl::doFirstTimeConnect(const resip::Data& authToken, const resip::Data& realm, const CPCAPI2::CloudConnector::ServiceDesc& confbridgeServiceDesc)
{
   std::weak_ptr<ConferenceConnectorImpl> weakThis(shared_from_this());
   std::vector<CPCAPI2::CloudConnector::ServiceDesc> serviceRequests;
   serviceRequests.push_back(confbridgeServiceDesc);
   CloudServerConnection::doOrchestrationFlow(mWebRequestThread,
      mInterface->getThisSdkPhone()->getSdkModuleThread(),
      serviceRequests,
      mSettings.orchestrationServerUrl.c_str(),
      authToken,
      mSettings.ignoreCertVerification,
      mInterface->getThisSdkPhone()->getSslCipherOptions(),
      mSettings.orchestrationTimeoutSeconds,
      [weakThis, authToken, realm, confbridgeServiceDesc](int orchResultCode, const std::vector<CPCAPI2::CloudConnector::ServiceDescUrl>& serviceUrls, const cpc::string& details)
   {
      if (std::shared_ptr<ConferenceConnectorImpl> thisPtr = weakThis.lock())
      {
         if (!(thisPtr->mConferenceConnectorDisconnecting || thisPtr->mConferenceConnectorDestroyed))
         {
         if (serviceUrls.size() > 0)
         {
            DebugLog(<< "ConferenceConnectorImpl::doFirstTimeConnect(): connector: " << thisPtr->mHandle << " service url: " << serviceUrls.front().url << " region: " << confbridgeServiceDesc.region << " service: " << confbridgeServiceDesc.service);
            if (thisPtr->mConfServiceConn.get() == NULL)
            {
               std::shared_ptr<CloudServerConnection> serverConn(new CloudServerConnection(thisPtr.get()));
               serverConn->initialize(thisPtr->mInterface->getThisSdkPhone());
               serverConn->addService(confbridgeServiceDesc);
               thisPtr->mConfServiceConn = serverConn;
            }
            else
            {
               thisPtr->mConfServiceConn->addService(confbridgeServiceDesc);
            }

            thisPtr->mConfServiceConn->connect(serviceUrls.front().url, authToken.c_str(), realm.c_str(), thisPtr->mSettings.ignoreCertVerification);
         }
         else
         {
            DebugLog(<< "ConferenceConnectorImpl::doFirstTimeConnect(): connector: " << thisPtr->mHandle << " service urls not populated, region: " << confbridgeServiceDesc.region << " service: " << confbridgeServiceDesc.service << " trigger connection failure, error: " << details.c_str());
            ServiceConnectionStatusEvent evt;
            evt.connectionStatus = ServiceConnectionStatus_ConnFailure;
            evt.serverUri = thisPtr->mSettings.orchestrationServerUrl;
            evt.statusDesc = details;
            thisPtr->mInterface->fireConnStatusEvent(thisPtr->mHandle, evt);
         }
         }
         else
         {
            WarningLog(<< "ConferenceConnectorImpl::doFirstTimeConnect(): connector: " << thisPtr->mHandle << " rejecting doOrchestrationFlow, disconnecting: " << thisPtr->mConferenceConnectorDisconnecting << " destroyed: " << thisPtr->mConferenceConnectorDestroyed);
         }
      }
   });
   return kSuccess;
}

int ConferenceConnectorImpl::connectToServices()
{
   DebugLog(<< "ConferenceConnectorImpl::connectToServices(): " << this << get_debug_string(*this));

   if (mSettings.username.size() == 0 || mSettings.authServerUrl.size() == 0)
   {
      ServiceConnectionStatusEvent evt;
      evt.connectionStatus = ServiceConnectionStatus_AuthFailure;
      evt.serverUri = mSettings.orchestrationServerUrl;
      evt.statusDesc = "missing auth username or authServerUrl";
      mInterface->fireConnStatusEvent(mHandle, evt);
      return kSuccess;
   }

   resip::Data loginUrl;
   {
      resip::DataStream loginUrlDs(loginUrl);
      loginUrlDs << mSettings.authServerUrl;
      loginUrlDs << "/login_v2";
   }

   resip::Data resolvedUsername;
   {
      resip::DataStream usernameDs(resolvedUsername);
      usernameDs << mSettings.username;
   }

   resip::Data passwordHash;
   if (mSettings.authType == AuthType_Stretto)
   {
      passwordHash = mSettings.password;
   }
   else
   {
      resip::DataStream passwordHashDs(passwordHash);
      unsigned char hash[SHA256_DIGEST_LENGTH];
      SHA256(reinterpret_cast<const unsigned char*>(mSettings.password.c_str()), mSettings.password.size(), hash);
      for (int i = 0; i < SHA256_DIGEST_LENGTH; i++)
      {
         passwordHashDs << std::hex << std::setw(2) << std::setfill('0') << (int)hash[i];
      }
   }

   std::vector<resip::Data> requestedResources;
   if (!mSettings.joinUrl.empty())
   {
      requestedResources.push_back(mSettings.joinUrl.c_str());
   }

   resip::Data realmStr;
   if (!mSettings.orchestrationServerUrl.empty())
   {
      realmStr = mSettings.orchestrationServerUrl.c_str();
      if (realmStr.postfix("/jsonApi"))
      {
         realmStr = realmStr.substr(0, realmStr.size() - 8);
      }
   }

   std::weak_ptr<ConferenceConnectorImpl> weakThis(shared_from_this());
   CloudServerConnection::doAuthFlow(mWebRequestThread,
      mInterface->getThisSdkPhone()->getSdkModuleThread(),
      loginUrl,
      resolvedUsername,
      passwordHash,
      requestedResources,
      mSettings.authServerApiKey.c_str(),
      mSettings.ignoreCertVerification,
      mInterface->getThisSdkPhone()->getSslCipherOptions(),
      mSettings.authenticationTimeoutSeconds,
      [weakThis, realmStr, resolvedUsername](int resultCode, const resip::Data& authToken, const cpc::string& details)
   {
      if (std::shared_ptr<ConferenceConnectorImpl> thisPtr = weakThis.lock())
      {
         if (!(thisPtr->mConferenceConnectorDisconnecting || thisPtr->mConferenceConnectorDestroyed))
         {
            if (resultCode == 0)
            {
               CPCAPI2::CloudConnector::ServiceDesc confbridgeServiceDesc;
               confbridgeServiceDesc.region = thisPtr->mSettings.regionCode;
               confbridgeServiceDesc.service = "confbridge";
               if (thisPtr->mSettings.joinUrl.size() > 0)
               {
                  ConfBridgeRequestor* wsUrlRequest = new ConfBridgeRequestor(thisPtr->mWebRequestThread,
                     thisPtr->mInterface->getThisSdkPhone()->getSdkModuleThread(),
                     thisPtr->mSettings.joinUrl.c_str(),
                     authToken,
                     thisPtr->mSettings.ignoreCertVerification,
                     thisPtr->mInterface->getThisSdkPhone()->getSslCipherOptions(),
                     thisPtr->mSettings.orchestrationTimeoutSeconds,
                     [weakThis, authToken, realmStr, confbridgeServiceDesc, resolvedUsername](int resultCode, const std::string& wsUrl)
                  {
                     if (std::shared_ptr<ConferenceConnectorImpl> thisPtr = weakThis.lock())
                     {
                        if (!(thisPtr->mConferenceConnectorDisconnecting || thisPtr->mConferenceConnectorDestroyed))
                        {
                           if (resultCode == -1)
                           {
                              // probably means that this user hasn't ever logged in to the server,
                              // so the resource URL for them isn't up and running yet (nginx gives us a 502)
                              InfoLog(<< "ConferenceConnectorImpl::connectToServices(): connector: " << thisPtr->mHandle << " user has never logged into this server, do first-time connect for: " << resolvedUsername << " joinUrl: " << thisPtr->mSettings.joinUrl.c_str());
                              thisPtr->doFirstTimeConnect(authToken, realmStr, confbridgeServiceDesc);
                           }
                           else if (resultCode == -2)
                           {
                              WarningLog(<< "ConferenceConnectorImpl::connectToServices(): connector: " << thisPtr->mHandle << " timed out connecting to service.");
                              ServiceConnectionStatusEvent evt;
                              evt.connectionStatus = ServiceConnectionStatus_Timeout;
                              evt.serverUri = thisPtr->mSettings.joinUrl;
                              thisPtr->mInterface->fireConnStatusEvent(thisPtr->mHandle, evt);
                           }
                           else
                           {
                              if (thisPtr->mConfServiceConn.get() == NULL)
                              {
                                 DebugLog(<< "ConferenceConnectorImpl::connectToServices(): connector: " << thisPtr->mHandle << " cloud server connection never initialized, create connection for service: " << confbridgeServiceDesc.service << " region: " << confbridgeServiceDesc.region << " user: " << resolvedUsername << " joinUrl: " << thisPtr->mSettings.joinUrl.c_str());
                                 std::shared_ptr<CloudServerConnection> serverConn(new CloudServerConnection(thisPtr.get()));
                                 serverConn->initialize(thisPtr->mInterface->getThisSdkPhone());
                                 serverConn->addService(confbridgeServiceDesc);
                                 thisPtr->mConfServiceConn = serverConn;
                              }
                              else
                              {
                                 DebugLog(<< "ConferenceConnectorImpl::connectToServices(): connector: " << thisPtr->mHandle << " add service: " << confbridgeServiceDesc.service << " region: " << confbridgeServiceDesc.region << " for: " << resolvedUsername << " joinUrl: " << thisPtr->mSettings.joinUrl.c_str());
                                 thisPtr->mConfServiceConn->addService(confbridgeServiceDesc);
                              }

                              thisPtr->mConfServiceConn->connect(wsUrl.c_str(), authToken.c_str(), realmStr.c_str(), thisPtr->mSettings.ignoreCertVerification);
                           }
                        }
                        else
                        {
                           WarningLog(<< "ConferenceConnectorImpl::connectToServices(): " << thisPtr.get() << " rejecting doGetConferenceWsUrlFlow," << get_debug_string(*thisPtr));
                        }
                     }
                  });
                  wsUrlRequest->start();
               }
               else
               {
                  DebugLog(<< "ConferenceConnectorImpl::connectToServices(): connector: " << thisPtr->mHandle << " joinUrl needs to be initialized, do first-time connect");
                  thisPtr->doFirstTimeConnect(authToken, realmStr, confbridgeServiceDesc);
               }
            }
            else
            {
               DebugLog(<< "ConferenceConnectorImpl::connectToServices(): connector: " << thisPtr->mHandle << " failure connecting to service, error-code: " << resultCode << " error-details: " << details.c_str());
               ServiceConnectionStatusEvent evt;
               if (resultCode == (-2))
               {
                  evt.connectionStatus = ServiceConnectionStatus_ConnFailure;
               }
               else
               {
                  evt.connectionStatus = ServiceConnectionStatus_AuthFailure;
               }
               evt.serverUri = thisPtr->mSettings.authServerUrl;
               evt.statusDesc = details;
               thisPtr->mInterface->fireConnStatusEvent(thisPtr->mHandle, evt);
            }
         }
         else
         {
            WarningLog(<< "ConferenceConnectorImpl::connectToServices(): connector: " << thisPtr.get() << " rejecting doAuthFlow," << get_debug_string(*thisPtr));
         }
      }
   });

   return kSuccess;
}

int ConferenceConnectorImpl::disconnectService()
{
   InfoLog(<< "ConferenceConnectorImpl::disconnectService(): " << this << get_debug_string(*this));

   if (mConfPhone != NULL)
   {
      CPCAPI2::ConferenceBridge::ConferenceBridgeManagerJsonProxy* confBridgeMgr = CPCAPI2::ConferenceBridge::ConferenceBridgeManagerJsonProxy::getInterface(mConfPhone);
      if (confBridgeMgr != NULL)
      {
         dynamic_cast<ConferenceBridgeJsonProxyInterface*>(confBridgeMgr)->removeSdkObserver(this);
      }
   }
   mConferenceConnectorDisconnecting = true;
   mConfServiceUri = "";
   if (mConfServiceConn.get() != NULL)
   {
      InfoLog(<< "ConferenceConnectorImpl::disconnectService(): " << this << " handle: " << mHandle << " destroying connection to server");
      mConferenceConnectorDisconnectState = static_cast<int>(ConnectionDisconnectState_DisconnectSent);
      mConfServiceConn->disconnect();
   }
   else
   {
      mConferenceConnectorDisconnectState = static_cast<int>(ConnectionDisconnectState_DisconnectNotSent);
   }

   return kSuccess;
}

/*
// ConferenceConnectorImpl

// Declarations
std::function<void(void)> mCbHook;
std::vector<std::function<void(void)>> mCallOnDestructFn;

void ConferenceConnectorImpl::setCallbackHook(void (*cbHook)(void*), void* context)
{
   mCbHook = std::bind(cbHook, context);
}

void ConferenceConnectorImpl::setCallOnDestructFn(void (*func)(void*), void* context)
{
   mCallOnDestructFn.push_back(std::bind(func, context));
}

// void ConferenceConnectorImpl::triggerCallback(resip::ReadCallbackBase* f)
void ConferenceConnectorImpl::triggerCallback()
{
   // mCallbackFifo.add(f);
   if (mCbHook) { mCbHook(); }
}

void ConferenceConnectorImpl::triggerCallOnDestruct()
{
   for (std::vector<std::function<void(void)>>::iterator i = mCallOnDestructFn.begin(); i != mCallOnDestructFn.end(); i++)
   {
      (*i)();
   }
}

// CloudServerConnection

// Declaration
void handleConferenceConnectorCallback();
static void conferenceConnectorDestroyed(std::weak_ptr<ConferenceConnectorImpl> weakSelf);

void conferenceConnectorCallbackHook(void* context)
{
   CloudServerConnection* ctx = (CloudServerConnection*)context;
   if (ctx)
      ctx->handleConferenceConnectorCallback();
}

void CloudServerConnection::handleConferenceConnectorCallback()
{
   // mPhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
}

void CloudServerConnection::initializeConferenceConnectorCallbacks()
{
   // Initialize callback in CloudServerConnection
   mConferenceConnector->setCallbackHook(conferenceConnectorCallbackHook, this);
   mConferenceConnector->setCallOnDestructFn(conferenceConnectorDestroyedHook, new ConferenceConnectorCallbackStruct(shared_from_this()));
}

struct ConferenceConnectorCallbackStruct
{
   std::weak_ptr<ConferenceConnectorImpl> weak_connector;
   ConferenceConnectorCallbackStruct(std::weak_ptr<ConferenceConnectorImpl> weak_this) : weak_connector(weak_this) {}
};

void conferenceConnectorDestroyedHook(void* context)
{
   // TODO: validate that context is valid before dereferencing it
   StackLog(<< "CloudServerConnection::conferenceConnectorDestroyedHook(): " << context << ": conference connector being destroyed");
   ConferenceConnectorCallbackStruct* cb = (ConferenceConnectorCallbackStruct*)context;
   if (cb)
   {
      ConferenceConnectorImpl::conferenceConnectorDestroyed(cb->weak_connector);
   }
   delete cb;
   cb = NULL;
}

void CloudServerConnection::conferenceConnectorDestroyed(std::weak_ptr<ConferenceConnectorImpl> weakSelf)
{
   DebugLog(<< "CloudServerConnection::conferenceConnectorDestroyed(): disconnect cloud-server connection, as conference connector may has been destroyed, thread-id: " << resip::ThreadIf::selfId());

   if (std::shared_ptr<ConferenceConnectorImpl> self = weakSelf.lock())
   {
      if (self->mConfServiceConn.get())
      {
         self->mConfServiceConn->disconnect();
      }
   }
   else
   {
      DebugLog(<< "CloudServerConnection::conferenceConnectorDestroyed(): invalid connector");
   }
}
*/

int ConferenceConnectorImpl::queryConferenceList()
{
   mQueryConferenceListPending = true;
   mConfBridgeMgr->queryConferenceList(this);
   return kSuccess;
}

CloudConferenceHandle ConferenceConnectorImpl::createConferenceHandle(const CPCAPI2::ConferenceBridge::ConferenceSettings& settings)
{
   ConferenceHandle conf = mConfBridgeMgr->createConference(settings);
   return (CloudConferenceHandle)conf;
}

CloudConferenceHandle ConferenceConnectorImpl::createNewConference(const CloudConferenceSettings& inSettings)
{
   CloudConferenceSettings settings = inSettings;
   if (settings.conferenceId.empty())
   {
      settings.conferenceId = resip::Random::getCryptoRandom(4).base64encode(true).substr(0, 4).uppercase().c_str();
   }

   ConferenceSettings conferenceSettings;
   conferenceSettings.label = settings.conferenceDescription;
   conferenceSettings.tags = settings.conferenceTags;
   if (settings.conferenceType == CloudConferenceType_AudioVideo_SFU)
   {
      conferenceSettings.tags.push_back(0x1000);
   }
   else if (settings.conferenceType == CloudConferenceType_AudioVideo_MCU)
   {
      conferenceSettings.tags.push_back(0x1500);
   }
   else if (settings.conferenceType == CloudConferenceType_Screenshare)
   {
      conferenceSettings.tags.push_back(0x2000);
   }

   // if there's a query string, get rid of it
   std::string joinUrlStr = settings.conferenceId.c_str();
   auto confPos = joinUrlStr.find("/conf/");
   if (confPos != cpc::string::npos)
   {
      size_t qsPos = joinUrlStr.rfind("?");
      if (qsPos != std::string::npos)
      {
         joinUrlStr = joinUrlStr.substr(0, qsPos);
      }
      joinUrlStr = joinUrlStr.substr(confPos + 6); // now we point to ABCD
      size_t slashPos = joinUrlStr.find("/");
      if (slashPos != std::string::npos)
      {
         joinUrlStr = joinUrlStr.substr(0, slashPos);
      }
      joinUrlStr = curlpp::unescape(joinUrlStr);
      conferenceSettings.conferenceToken = joinUrlStr.c_str();
   }
   else
   {
      conferenceSettings.conferenceToken = settings.conferenceId.c_str();
   }
   conferenceSettings.adaptVideoCodecOnRemotePacketLoss = (settings.conferenceType == CloudConferenceType_AudioVideo_MCU || settings.conferenceType == CloudConferenceType_AudioVideo_SFU);
   conferenceSettings.persistent = settings.persistent;
   conferenceSettings.mixMode = (settings.conferenceType == CloudConferenceType_AudioVideo_MCU ? ConferenceMixMode_MCU : ConferenceMixMode_SFU);

   CloudConferenceHandle h = createConferenceHandle(conferenceSettings);

   auto itCs = mConfStateMap.begin();
   for (; itCs != mConfStateMap.end(); ++itCs)
   {
      if (itCs->settings.conferenceId == settings.conferenceId &&
         itCs->confHandle != h)
      {
         // there's an existing conference in the map, but the handle is mis-matched;
         InfoLog(<< "ConferenceConnectorImpl::createNewConference(): conference handle mismatch for: " << settings.conferenceId.c_str() << " updating handle from: " << h << " to: " << itCs->confHandle);
         h = itCs->confHandle;
         break;
      }
   }

   itCs = getConferenceState_ConfHandle(h);
   if (itCs != mConfStateMap.end())
   {
      itCs->owner = !settings.persistent;
      itCs->waitingForCreatedEvent = true;
      itCs->settings = settings;
   }
   else
   {
      ConferenceState cs;
      cs.confHandle = h;
      cs.cloudConfHandle = h;
      cs.settings = settings;
      cs.owner = !settings.persistent;
      cs.waitingForCreatedEvent = true;
      mConfStateMap.push_back(cs);
      mInterface->mapConferenceHandleToInst(h, shared_from_this());
   }

   mConfBridgeMgr->setStreamingEnabled(h, true);
   mConfBridgeMgr->setRecordingEnabled(h, false);

   InfoLog(<< "ConferenceConnectorImpl::createNewConference(): conference created: {" << h << ", id: " << settings.conferenceId << "}");

   //mConfBridgeMgr->queryConferenceDetails(h);
   //queryConferenceList();

   /*
   WebParticipantIdentity identityInfo;
   identityInfo.owner = true;
   ConferenceParticipantHandle part = mConfBridgeMgr->createWebParticipant(h, identityInfo, NULL);
   mConfStateMap[h].part = part;
   */

   //if (settings.parentConference != (CloudConferenceHandle)0xffffffff)
   //{
   //   mConfBridgeMgr->addAssociatedConference(settings.parentConference, h);
   //}
   //ConferenceCreatedEvent evt;
   //evt.conference = h;
   //mInterface->fireConfCreatedEvent(mHandle, evt);
   return h;
}

std::vector<ConferenceConnectorImpl::ConferenceState>::iterator ConferenceConnectorImpl::getConferenceState(CloudConferenceHandle h)
{
   auto it = mConfStateMap.begin();
   for (; it != mConfStateMap.end(); ++it)
   {
      if (it->cloudConfHandle == h)
         return it;
   }
   return mConfStateMap.end();
}

std::vector<ConferenceConnectorImpl::ConferenceState>::iterator ConferenceConnectorImpl::getConferenceState_ConfHandle(CPCAPI2::ConferenceBridge::ConferenceHandle h)
{
   auto it = mConfStateMap.begin();
   for (; it != mConfStateMap.end(); ++it)
   {
      if (it->confHandle == h)
         return it;
   }
   return mConfStateMap.end();
}

int ConferenceConnectorImpl::destroyConference(CloudConferenceHandle conference)
{
   auto it = getConferenceState(conference);
   if (it != mConfStateMap.end())
   {
      mConfBridgeMgr->destroyConference(it->confHandle);
   }
   return kSuccess;
}

int ConferenceConnectorImpl::queryParticipantList(CloudConferenceHandle conference)
{
   auto it = getConferenceState(conference);
   if (it != mConfStateMap.end())
   {
      mConfBridgeMgr->queryParticipantList(it->confHandle, this);
   }
   return kSuccess;
}

int ConferenceConnectorImpl::createConferenceSession(CloudConferenceHandle conference, CloudConferenceSessionHandle session, const CloudConferenceSessionSettings& settings)
{
   auto it = getConferenceState(conference);
   if (it != mConfStateMap.end())
   {
      mSessStateMap[session].cloudConference = conference;
      if (it->part == (ConferenceParticipantHandle)-1)
      {
         auto itConf = getConferenceState(conference);
         WebParticipantIdentity identityInfo;
         if (itConf != mConfStateMap.end())
         {
            identityInfo.owner = itConf->owner;
            identityInfo.address = settings.address;
            identityInfo.displayName = settings.displayName;
            identityInfo.tags = settings.tags;
         }
         mSessStateMap[session].sessionSettings = settings;
         mSessStateMap[session].part = mConfBridgeMgr->createWebParticipant(it->confHandle, identityInfo, settings.addToFloor, new CreateWebParticipantHandlerDispatcher([conference, session, this](ConferenceHandle conf, const WebParticipantCreatedEvent& evt) {
            auto itConf = getConferenceState(conference);
            if (evt.success)
            {
               itConf->part = mSessStateMap[session].part;
               itConf->hasFloor = evt.hasFloor;
            }
            else
            {
               endSession(session, false);

               CPCAPI2::ConferenceBridge::ConferenceEndedEvent confEndedEvt;
               onConferenceEnded(conf, confEndedEvt);
            }
            return kSuccess;
         }));
      }
      else
      {
         ConferenceParticipantHandle cph = it->part;
         mSessStateMap[session].part = cph;
      }
   }
   return kSuccess;
}

int ConferenceConnectorImpl::setSessionSettings(CloudConferenceSessionHandle session, const CloudConferenceSessionSettings& settings)
{
   auto it = mSessStateMap.find(session);
   if (it != mSessStateMap.end())
   {
      it->second.sessionSettings = settings;
      it->second.sessionSettingsUpdated = true;
   }
   return kSuccess;
}

int ConferenceConnectorImpl::setSessionMediaSettings(CloudConferenceSessionHandle session, const CloudConferenceSessionMediaSettings& settings)
{
   auto it = mSessStateMap.find(session);
   if (it != mSessStateMap.end())
   {
      it->second.pendingMediaSettings.reset(new CloudConferenceSessionMediaSettings(settings));
   }
   return kSuccess;
}

int ConferenceConnectorImpl::dropIncomingJsonMessages(bool enable)
{
   if (mConfServiceConn.get() != NULL)
   {
      InfoLog(<< "ConferenceConnectorImpl::dropIncomingJsonMessages(): " << this << " handle: " << mHandle << " drop message enable: " << enable);
      mConfServiceConn->dropIncomingJsonMessages(enable);
   }
   return kSuccess;
}

int ConferenceConnectorImpl::queryMediaStatistics(CloudConferenceSessionHandle session)
{
   auto it = mSessStateMap.find(session);
   if (it == mSessStateMap.end())
   {
      return kError;
   }

   InfoLog(<< "ConferenceConnectorImpl::queryMediaStatistics()");

   PeerConnection::PeerConnectionManagerInternal* peerMgrInternal = dynamic_cast<PeerConnection::PeerConnectionManagerInternal*>(mPeerConnMgr);
   if (peerMgrInternal)
      peerMgrInternal->queryMediaStatistics(it->second.pc);
   else
   {
      assert(0);
      return kError;
   }

   // wait for PeerConnectionHandler::onPeerConnectionMediaStatistics(..)
   return kSuccess;
}

int ConferenceConnectorImpl::setMediaInactivityMonitor(CloudConferenceSessionHandle session, bool enableMediaInactivityMonitor, int mediaInactivityIntervalMsecs)
{
   auto i = mSessStateMap.find(session);
   if (i != mSessStateMap.end())
   {
      i->second.enableMediaInactivityMonitor = enableMediaInactivityMonitor;
      i->second.mediaInactivityIntervalMsecs = mediaInactivityIntervalMsecs;
   }
   return kSuccess;
}

int ConferenceConnectorImpl::startSession(CloudConferenceSessionHandle session)
{
   auto it = mSessStateMap.find(session);
   if (it != mSessStateMap.end())
   {
      auto itConf = getConferenceState(it->second.cloudConference);
      if (itConf == mConfStateMap.end())
      {
         return kError;
      }

      if (it->second.sessionSettingsUpdated)
      {
         it->second.sessionSettingsUpdated = false;

         WebParticipantIdentity identityInfo;
         identityInfo.address = it->second.sessionSettings.address;
         identityInfo.displayName = it->second.sessionSettings.displayName;
         identityInfo.owner = itConf->owner;
         mConfBridgeMgr->setWebParticipantIdentity(it->second.part, identityInfo);
      }

      PeerConnectionHandle pc = mPeerConnMgr->createPeerConnection();

      ConferenceSessionStatusChangedEvent connectingStatusEvt;
      connectingStatusEvt.conference = itConf->cloudConfHandle;
      connectingStatusEvt.session = session;
      connectingStatusEvt.sessionStatus = SessionStatus_Connecting;
      connectingStatusEvt.peerConnection = pc;
      connectingStatusEvt.hasFloor = itConf->hasFloor;
      mInterface->fireConfSessionStatusChanged(mHandle, connectingStatusEvt);

      PeerConnectionSettings pcSettings;
      pcSettings.adaptVideoCodecOnRemotePacketLoss = (itConf->settings.conferenceType == CloudConferenceType_AudioVideo_MCU || itConf->settings.conferenceType == CloudConferenceType_AudioVideo_SFU);
      pcSettings.isScreenshare = (itConf->settings.conferenceType == CloudConferenceType_Screenshare);
      pcSettings.mixId = itConf->confHandle;
      pcSettings.mixContribution = 0; // do NOT mix this stream with any others
      pcSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_ICE;
      pcSettings.natTraversalServerHostname = itConf->confInfo.natTraversalServerInfo.natTraversalServerHostname; //"cpsipv6.counterpath.net";
      pcSettings.natTraversalServerUsername = itConf->confInfo.natTraversalServerInfo.natTraversalServerUsername; //"cpcapi2";
      pcSettings.natTraversalServerPassword = itConf->confInfo.natTraversalServerInfo.natTraversalServerPassword; //"cpcapi2";
      pcSettings.natTraversalServerType = (PeerConnectionSettings::NatTraversalServerType)itConf->confInfo.natTraversalServerInfo.natTraversalServerType2;
      pcSettings.sessionName = "cpcconf";
      pcSettings.certAor = "<EMAIL>";
      pcSettings.rtcpMux = true;

      if (itConf->confInfo.mediaEncryptionMode == CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode_SRTP_DTLS)
      {
         pcSettings.secureMediaMode = PeerConnectionSettings::SecureMediaMode_DTLS;
         pcSettings.secureMediaRequired = true;
      }
      else if (itConf->confInfo.mediaEncryptionMode == CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode_SRTP_SDES)
      {
         pcSettings.secureMediaMode = PeerConnectionSettings::SecureMediaMode_SDES;
         pcSettings.secureMediaRequired = true;
      }
      else
      {
         pcSettings.secureMediaMode = PeerConnectionSettings::SecureMediaMode_None;
         pcSettings.secureMediaRequired = false;
      }

      pcSettings.videoCaptureDeviceId = (itConf->settings.conferenceType == CloudConferenceType_Screenshare ? 0x00008001 : -1);
      pcSettings.defaultVideoRenderSurface = (void*)0xDEADBEEF; // prevents the default render surface from getting used
      pcSettings.videoMaxBitrateKbps = itConf->confInfo.bitrateConfig.videoMaxBitrateKbps;
      pcSettings.videoStartBitrateKbps = itConf->confInfo.bitrateConfig.videoStartBitrateKbps;
      pcSettings.videoTargetBitrateKbps = itConf->confInfo.bitrateConfig.videoTargetBitrateKbps;
      pcSettings.videoMinBitrateKbps = itConf->confInfo.bitrateConfig.videoMinBitrateKbps;
      pcSettings.mediaDscp = it->second.sessionSettings.mediaDscp;
      mPeerConnMgr->setDefaultSettings(pc, pcSettings);
      PeerConnection::PeerConnectionManagerInternal* peerMgrInternal = dynamic_cast<PeerConnection::PeerConnectionManagerInternal*>(mPeerConnMgr);
      if (peerMgrInternal && (it->second.mediaInactivityIntervalMsecs > 0))
      {
         peerMgrInternal->setMediaInactivityTimeoutMsecs(pc, it->second.mediaInactivityIntervalMsecs);
      }

      if (it->second.pendingMediaSettings->audioDirection != ConferenceConnector::MediaDirection_None)
      {
         it->second.audioMediaStream = mPeerConnMgr->createMediaStream();
         CPCAPI2::PeerConnection::MediaInfo miAudio;
         miAudio.mediaDirection = (PeerConnection::MediaDirection)it->second.pendingMediaSettings->audioDirection;
         miAudio.mediaType = CPCAPI2::PeerConnection::MediaType_Audio;
         if (itConf->confInfo.mediaEncryptionMode == CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode_SRTP_DTLS)
         {
            miAudio.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
            miAudio.mediaEncryptionOptions.secureMediaRequired = true;
         }
         else if (itConf->confInfo.mediaEncryptionMode == CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode_SRTP_SDES)
         {
            miAudio.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_SDES_Encrypted;
            miAudio.mediaEncryptionOptions.secureMediaRequired = true;
         }
         mPeerConnMgr->configureMedia(pc, it->second.audioMediaStream, miAudio);
      }
      if (it->second.pendingMediaSettings->videoDirection != ConferenceConnector::MediaDirection_None)
      {
         it->second.videoMediaStream = mPeerConnMgr->createMediaStream();
         CPCAPI2::PeerConnection::MediaInfo miVideo;
         if (itConf->settings.conferenceType == CloudConferenceType_Screenshare && it->second.sessionSettings.role == CloudConferenceRole_Participant)
         {
            miVideo.mediaDirection = CPCAPI2::PeerConnection::MediaDirection_RecvOnly;
            it->second.pendingMediaSettings->videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
         }
         else
         {
            miVideo.mediaDirection = (PeerConnection::MediaDirection)it->second.pendingMediaSettings->videoDirection;
         }
         miVideo.mediaType = CPCAPI2::PeerConnection::MediaType_Video;
         if (itConf->confInfo.mediaEncryptionMode == CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode_SRTP_DTLS)
         {
            miVideo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted;
            miVideo.mediaEncryptionOptions.secureMediaRequired = true;
         }
         else if (itConf->confInfo.mediaEncryptionMode == CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode_SRTP_SDES)
         {
            miVideo.mediaEncryptionOptions.mediaEncryptionMode = PeerConnection::MediaEncryptionMode_SRTP_SDES_Encrypted;
            miVideo.mediaEncryptionOptions.secureMediaRequired = true;
         }
         CPCAPI2::PeerConnection::MediaCodec h264codec;
         h264codec.codecFrequency = 90000;
         h264codec.codecPayloadName = "H264";
         miVideo.codecs.push_back(h264codec);
         mPeerConnMgr->configureMedia(pc, it->second.videoMediaStream, miVideo);
      }

      it->second.pc = pc;

      mPeerConnMgr->createOffer(pc);

      if (it->second.enableMediaInactivityMonitor && (it->second.mediaInactivityIntervalMsecs > 0))
      {
         mPeerConnMgr->startMediaInactivityMonitor(pc);
      }

      InfoLog(<< "ConferenceConnectorImpl::startSession(..): pc = " << pc);

      // wait for PeerConnectionHandler::onCreateOfferResult(..)
   }
   return kSuccess;
}

int ConferenceConnectorImpl::endSession(CloudConferenceSessionHandle session, bool isReconnecting)
{
   auto it = mSessStateMap.find(session);
   if (it != mSessStateMap.end())
   {
      auto itConf = getConferenceState(it->second.cloudConference);
      if (itConf == mConfStateMap.end())
      {
         return kError;
      }

      if (it->second.isCapturingLocalScreen)
      {
         mVideoExtIf->stopScreenshare();
      }
      if (it->second.isCapturingLocalVideo)
      {
         mVideoIf->stopCapture();
      }

      itConf->lastConnectedEvent.reset();

      mPeerConnMgr->close(it->second.pc);
      ConferenceParticipantHandle cph = it->second.part;
      DebugLog(<< "calling destroyWebParticipant(" << cph << ")");
      mConfBridgeMgr->destroyWebParticipant(cph);
      it->second.part = -1;
      itConf->part = -1;

      if (it->second.vidStream != (CPCAPI2::VideoStreaming::VideoStreamHandle)-1)
      {
         if (mVideoStreamingMgr != NULL)
         {
            mVideoStreamingMgr->stopVideoStream(it->second.vidStream);
         }
         it->second.vidStream = (CPCAPI2::VideoStreaming::VideoStreamHandle)-1;
      }

      ConferenceSessionStatusChangedEvent statusEvt;
      statusEvt.conference = itConf->cloudConfHandle;
      statusEvt.session = session;
      statusEvt.sessionStatus = (isReconnecting ? SessionStatus_Reconnecting : SessionStatus_NotConnected);
      statusEvt.peerConnection = it->second.pc;
      statusEvt.hasFloor = itConf->hasFloor;

      mInterface->unmapSessionHandle(it->first);

      mSessStateMap.erase(it);

      mInterface->fireConfSessionStatusChanged(mHandle, statusEvt);
   }
   return kSuccess;
}

int ConferenceConnectorImpl::updateSessionMedia(CloudConferenceSessionHandle session)
{
   auto it = mSessStateMap.find(session);
   if (it != mSessStateMap.end())
   {
      auto itConf = getConferenceState(it->second.cloudConference);
      if (itConf == mConfStateMap.end())
      {
         return kError;
      }

      InfoLog(<< "ConferenceConnectorImpl::updateSessionMedia(): active audio direction: " << it->second.activeMediaSettings.audioDirection <<
         " active video direction: " << it->second.activeMediaSettings.videoDirection <<
         " pending audio direction: " << it->second.pendingMediaSettings->audioDirection <<
         " pending video direction: " << it->second.pendingMediaSettings->videoDirection);

      if (it->second.pendingMediaSettings->audioDirection != it->second.activeMediaSettings.audioDirection ||
         it->second.pendingMediaSettings->videoDirection != it->second.activeMediaSettings.videoDirection)
      {
         if (it->second.pendingMediaSettings->audioDirection != ConferenceConnector::MediaDirection_None)
         {
            CPCAPI2::PeerConnection::MediaInfo miAudio;
            miAudio.mediaDirection = (PeerConnection::MediaDirection)it->second.pendingMediaSettings->audioDirection;
            miAudio.mediaType = CPCAPI2::PeerConnection::MediaType_Audio;
            mPeerConnMgr->configureMedia(it->second.pc, it->second.audioMediaStream, miAudio);
         }

         if (it->second.pendingMediaSettings->videoDirection != ConferenceConnector::MediaDirection_None)
         {
            CPCAPI2::PeerConnection::MediaInfo miVideo;
            if (itConf->settings.conferenceType == CloudConferenceType_Screenshare && it->second.sessionSettings.role == CloudConferenceRole_Host)
            {
               miVideo.mediaDirection = (PeerConnection::MediaDirection)it->second.pendingMediaSettings->videoDirection;
            }
            else if (itConf->settings.conferenceType == CloudConferenceType_Screenshare && it->second.sessionSettings.role == CloudConferenceRole_Participant)
            {
               miVideo.mediaDirection = CPCAPI2::PeerConnection::MediaDirection_RecvOnly;
            }
            else
            {
               miVideo.mediaDirection = (PeerConnection::MediaDirection)it->second.pendingMediaSettings->videoDirection;
            }
            miVideo.mediaType = CPCAPI2::PeerConnection::MediaType_Video;
            mPeerConnMgr->configureMedia(it->second.pc, it->second.videoMediaStream, miVideo);
         }

         mPeerConnMgr->createOffer(it->second.pc);
      }
      else
      {
         if (it->second.pendingMediaSettings.get() != NULL)
         {
            resip::ReadCallbackBase* rcbSetIncomingVideoRenderTarget = resip::resip_static_bind(&ConferenceConnectorImpl::setIncomingVideoRenderTargetWrap, mVideoIf, it->second.videoRecvMediaStreamId, it->second.pendingMediaSettings->remoteVideoRenderSurface, it->second.pendingMediaSettings->remoteVideoRenderSurfaceType);
            dynamic_cast<CPCAPI2::Media::VideoInterface*>(mVideoIf)->postCallback(rcbSetIncomingVideoRenderTarget);

            it->second.activeMediaSettings = *it->second.pendingMediaSettings;
            it->second.pendingMediaSettings.reset();
            if (itConf->lastConnectedEvent.get() != NULL)
            {
               mInterface->fireConfSessionStatusChanged(mHandle, *itConf->lastConnectedEvent);
            }
         }

      }

      // wait for PeerConnectionHandler::onCreateOfferResult(..)
   }
   return kSuccess;
}

int ConferenceConnectorImpl::setParticipantPermissions(CloudConferenceSessionHandle session, CloudConferenceParticipantHandle participant, const ConferenceSessionPermissions& permissions)
{
   auto it = mSessStateMap.find(session);
   if (it != mSessStateMap.end())
   {
      auto itConf = getConferenceState(it->second.cloudConference);
      if (itConf == mConfStateMap.end())
      {
         return kError;
      }

      CPCAPI2::ConferenceBridge::ParticipantPermissions confBridgePartPermissions;
      confBridgePartPermissions.canCreateConference = permissions.canCreateConference;
      mConfBridgeMgr->setParticipantPermissions(participant, confBridgePartPermissions);
   }
   return kSuccess;
}

int ConferenceConnectorImpl::pauseSessionPlayout(CloudConferenceSessionHandle session)
{
   auto it = mSessStateMap.find(session);
   if (it != mSessStateMap.end())
   {
      mPeerConnMgr->pauseAudioPlayout(it->second.pc);
   }
   return kSuccess;
}

int ConferenceConnectorImpl::resumeSessionPlayout(CloudConferenceSessionHandle session)
{
   auto it = mSessStateMap.find(session);
   if (it != mSessStateMap.end())
   {
      mPeerConnMgr->resumeAudioPlayout(it->second.pc);
   }
   return kSuccess;
}

void ConferenceConnectorImpl::handleCloudServerConnectionStatusChanged(CloudServerConnection* conn, const CPCAPI2::CloudConnector::CloudServerConnStatusEvent& args)
{
   InfoLog(<< "ConferenceConnectorImpl::handleCloudServerConnectionStatusChanged(): " << this << " callback-connection: " << conn << " callback-status: " << CloudServerConnection::get_debug_string(args.status) << get_debug_string(*this));

   if (mConferenceConnectorDisconnectState == static_cast<int>(ConnectionDisconnectState_DisconnectNotSent))
   {
      if (mConferenceConnectorDestroyed)
      {
         // Ensure that the server connection is destroyed for race-conditions when the conference connector is being destroyed,
         // while the server connection was attempting to connect.
         InfoLog(<< "ConferenceConnectorImpl::handleCloudServerConnectionStatusChanged(): " << this << " destroying connection: " << conn << " as conference connector is being destroyed");
         mConferenceConnectorDisconnectState = static_cast<int>(ConnectionDisconnectState_None);
         conn->disconnect();
         conn->release();
         mConfServiceConn.reset();
         return;
      }
      else if (mConferenceConnectorDisconnecting)
      {
         // Ensure that the server connection is disconnected for race-conditions when the conference connector is being disconnected,
         // while the server connection was attempting to connect.
         InfoLog(<< "ConferenceConnectorImpl::handleCloudServerConnectionStatusChanged(): " << this << " disconnecting connection: " << conn << " as conference connector is being disconnected");
         mConferenceConnectorDisconnectState = static_cast<int>(ConnectionDisconnectState_None);
         conn->disconnect();
         return;
      }
      else if (mConfServiceConn && (mConfServiceConn.get() != conn))
      {
         // Ensure that the server connection is destroyed for race-conditions when the conference connector creates another server connection,
         // while the server connection was attempting to connect.
         InfoLog(<< "ConferenceConnectorImpl::handleCloudServerConnectionStatusChanged(): " << this << " destroying connection: " << conn << " as it mismatches the server connection: " << mConfServiceConn.get() << " being used by the conference connector");
         mConferenceConnectorDisconnectState = static_cast<int>(ConnectionDisconnectState_None);
         conn->disconnect();
         conn->release();
         mConfServiceConn.reset();
         return;
      }
   }

   if (mConfServiceConn && (mConfServiceConn.get() != conn))
   {
      InfoLog(<< "ConferenceConnectorImpl::handleCloudServerConnectionStatusChanged(): " << this << " connection: " << conn << " ignoring server connection callback as it mismatches the server connection: " << mConfServiceConn.get() << " being used by the conference connector");
      return;
   }

   // translate into a per-service set of events
   ServiceConnectionStatusEvent evt;
   if (args.status == CPCAPI2::CloudConnector::CloudServerConnStatus_Connected)
   {
      const std::set<CPCAPI2::CloudConnector::ServiceDesc>& services = conn->getServices();
      std::set<CPCAPI2::CloudConnector::ServiceDesc>::const_iterator it = services.begin();
      for (; it != services.end(); ++it)
      {
         mConfPhone = conn->getPhone();
         mConfBridgeMgr = CPCAPI2::ConferenceBridge::ConferenceBridgeManagerJsonProxy::getInterface(mConfPhone);
         dynamic_cast<ConferenceBridgeJsonProxyInterface*>(mConfBridgeMgr)->addSdkObserver(this);
         evt.authToken.authToken = conn->getAuthToken();

         auto itSess = mSessStateMap.begin();
         while (itSess != mSessStateMap.end())
         {
            CloudConferenceSessionHandle sess = itSess->first;
            InfoLog(<< "ConferenceConnectorImpl::handleCloudServerConnectionStatusChanged(): restarting session due to network change; connection: " << conn << " handle: " << mHandle << " session: " << sess);
            mInterface->endSession(sess, true);
            auto itConf = getConferenceState(itSess->second.cloudConference);
            itConf->part = (ConferenceParticipantHandle)-1;
            mInterface->createConferenceSession(sess, itSess->second.cloudConference, itSess->second.sessionSettings);
            mInterface->setSessionMediaSettings(sess, itSess->second.activeMediaSettings);
            mInterface->startSession(sess);
            itSess++;
         }
      }
   }
   else if (args.status == CPCAPI2::CloudConnector::CloudServerConnStatus_Disconnected)
   {
      mConferenceConnectorDisconnecting = false;
      mConferenceConnectorDisconnectState = static_cast<int>(ConnectionDisconnectState_None);
   }

   if (mConfServiceConnStatus == args.status)
   {
      return;
   }

   mConfServiceConnStatus = args.status;
   evt.serverUri = conn->getUrl();
   evt.connectionStatus = (ServiceConnectionStatus)args.status;

   StackLog(<< "ConferenceConnectorImpl::handleCloudServerConnectionStatusChanged(): " << this << " connection: " << conn << " handle: " << mHandle << " updated connection status: " << CloudServerConnection::get_debug_string(args.status));
   mInterface->fireConnStatusEvent(mHandle, evt);
}

void ConferenceConnectorImpl::release()
{
   DebugLog(<< "ConferenceConnectorImpl::release(): " << this << " mHandle: " << mHandle);

   if (mConfServiceConn.get() != NULL)
   {
      mConfServiceConn->release();
      mConfServiceConn.reset();
      mConfPhone = NULL;
   }
   mConfServiceUri = "";
}

void ConferenceConnectorImpl::resolveNatTraversalServerHostname(const cpc::string& natTraversalServerHostname, int natTraversalServerPort)
{
   InfoLog(<< "pre-resolve NAT traversal server hostname: " << natTraversalServerHostname);
   if (!natTraversalServerHostname.empty())
   {
      dynamic_cast<PeerConnectionManagerInterface*>(mPeerConnMgr)->preinitDns(natTraversalServerHostname, natTraversalServerPort);
   }
}

int ConferenceConnectorImpl::onConferenceDetails(ConferenceHandle conference, const ConferenceDetailsResult& args)
{
   InfoLog(<< "ConferenceConnectorImpl::onConferenceDetails(): incoming conference: {" << conference << ", label: " << args.conferenceInfo.label << ", token: " << args.conferenceToken << "}");

   /*
   // Debug Begins
   for (auto i = mConfStateMap.begin(); i != mConfStateMap.end(); ++i)
   {
      InfoLog(<< "ConferenceConnectorImpl::onConferenceDetails(): conference: " << conference << " state: {" << (*i) << "}");
   }

   InfoLog(<< "ConferenceConnectorImpl::onConferenceDetails(): ConferenceDetailsResult: {" << args << "}");
   // Debug Ends
   */

   bool doQuery = false;
   auto it = mConfStateMap.begin();
   for (; it != mConfStateMap.end(); ++it)
   {
      if (it->settings.conferenceId == args.conferenceToken &&
          it->cloudConfHandle != conference)
      {
         InfoLog(<< "ConferenceConnectorImpl::onConferenceDetails(): " << this << " incoming conference: {" << conference << ", label: " << args.conferenceInfo.label << ", token: " << args.conferenceToken << "}, conf-state conference: {" << it->cloudConfHandle << ", label: " << it->confInfo.label << ", token: " << it->confInfo.conferenceToken << ", id: " << it->settings.conferenceId << "}, race-condition with conference-id: " <<  it->settings.conferenceId << " waiting for created event: " << it->waitingForCreatedEvent);

         // there's an existing conference in the map, but the handle is mis-matched;
         // the conferenceToken is the definitive identifier, so force a handle match
         // (this can happen if two endpoints create a conference with the same conferenceId/conferenceToken)
         it->confHandle = conference;
         doQuery = true;
         break;
      }
   }
   if (it == mConfStateMap.end())
   {
      it = getConferenceState_ConfHandle(conference);
   }
   if (it != mConfStateMap.end())
   {
      if (it->waitingForCreatedEvent)
      {
         StackLog(<< "ConferenceConnectorImpl::onConferenceDetails(): conference: {" << conference << ", " << it->cloudConfHandle << ", label: " << it->confInfo.label << ", token: " << it->confInfo.conferenceToken << ", id: " << it->settings.conferenceId << "} successfully created, updating state");
         it->waitingForCreatedEvent = false;
         it->confInfo = args.conferenceInfo;

         resolveNatTraversalServerHostname(args.conferenceInfo.natTraversalServerInfo.natTraversalServerHostname, args.conferenceInfo.natTraversalServerInfo.natTraversalServerPort);

         ConferenceCreatedEvent evt;
         evt.conference = it->cloudConfHandle;
         evt.displayName = it->confInfo.label;
         mInterface->fireConfCreatedEvent(mHandle, evt);
      }
      else
      {
         StackLog(<< "ConferenceConnectorImpl::onConferenceDetails(): conference: {" << conference << ", " << it->cloudConfHandle << ", id: " << it->settings.conferenceId << "} already created in conference state list");
      }
   }
   else
   {
      DebugLog(<< "ConferenceConnectorImpl::onConferenceDetails(): conference: {" << conference << ", id: " << args.conferenceToken << "} not in conference state list, send conference query");
      doQuery = true;
   }
   if (doQuery)
   {
      mConfBridgeMgr->queryConferenceList(this);
   }
   return kSuccess;
}

int ConferenceConnectorImpl::onConferenceEnded(ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceEndedEvent& args)
{
   InfoLog(<< "ConferenceConnectorImpl::onConferenceEnded(" << conference << ")");
   auto itConf = mConfStateMap.begin();
   for (; itConf != mConfStateMap.end(); ++itConf)
   {
      if (itConf->confHandle == conference)
      {
         auto itSess = mSessStateMap.begin();
         for (; itSess != mSessStateMap.end(); ++itSess)
         {
            if (itSess->second.cloudConference == itConf->cloudConfHandle)
            {
               endSession(itSess->first, false);
               break;
            }
         }
         ConferenceEndedEvent evt;
         evt.conference = itConf->cloudConfHandle;
         evt.displayName = itConf->confInfo.label;
         mInterface->fireConfEndedEvent(mHandle, evt);
         mInterface->unmapConferenceHandle(itConf->cloudConfHandle);
         mConfStateMap.erase(itConf);
         break;
      }
   }
   mConfBridgeMgr->queryConferenceList(this);
   return kSuccess;
}

int ConferenceConnectorImpl::onParticipantListState(ConferenceHandle conference, const ParticipantListState& args)
{
   InfoLog(<< "ConferenceConnectorImpl::onParticipantListState(" << conference << ")");

   auto itConf = mConfStateMap.begin();
   for (; itConf != mConfStateMap.end(); ++itConf)
   {
      if (itConf->confHandle == conference)
      {
         ConferenceParticipantListUpdatedEvent evt;
         evt.conference = itConf->cloudConfHandle;

         itConf->participantPermissions = ParticipantPermissions();

         for (const CPCAPI2::ConferenceBridge::ParticipantInfo& pi : args.participants)
         {
            CloudConferenceParticipantInfo cpi;
            cpi.participant = pi.participant;
            cpi.address = pi.address;
            cpi.displayName = pi.displayName;
            cpi.hasFloor = pi.hasFloor;
            cpi.tags = pi.tags;
            cpi.peerConnection = pi.webParticipantDetails.peerConnection;
            cpi.joinTimestampMsecs = pi.joinTimestampMsecs;
            evt.participantList.push_back(cpi);

            if (pi.participant == itConf->part)
            {
               // if the participant is me
               itConf->participantPermissions = pi.permissions;
            }
         }

         for (const CPCAPI2::ConferenceBridge::ParticipantInfo& pi : args.addedParticipants)
         {
            CloudConferenceParticipantInfo cpi;
            cpi.participant = pi.participant;
            cpi.address = pi.address;
            cpi.displayName = pi.displayName;
            cpi.hasFloor = pi.hasFloor;
            cpi.tags = pi.tags;
            cpi.peerConnection = pi.webParticipantDetails.peerConnection;
            cpi.joinTimestampMsecs = pi.joinTimestampMsecs;
            evt.addedParticipants.push_back(cpi);
            if (pi.participant == itConf->part)
            {
               // if the participant is me
               itConf->participantPermissions = pi.permissions;
            }
         }

         for (const CPCAPI2::ConferenceBridge::ParticipantInfo& pi : args.updatedParticipants)
         {
            CloudConferenceParticipantInfo cpi;
            cpi.participant = pi.participant;
            cpi.address = pi.address;
            cpi.displayName = pi.displayName;
            cpi.hasFloor = pi.hasFloor;
            cpi.tags = pi.tags;
            cpi.peerConnection = pi.webParticipantDetails.peerConnection;
            cpi.joinTimestampMsecs = pi.joinTimestampMsecs;
            evt.updatedParticipants.push_back(cpi);
            if (pi.participant == itConf->part)
            {
               // if the participant is me
               itConf->participantPermissions = pi.permissions;
            }
         }

         for (const CPCAPI2::ConferenceBridge::ParticipantInfo& pi : args.removedParticipants)
         {
            CloudConferenceParticipantInfo cpi;
            cpi.participant = pi.participant;
            cpi.address = pi.address;
            cpi.displayName = pi.displayName;
            cpi.hasFloor = pi.hasFloor;
            cpi.tags = pi.tags;
            cpi.peerConnection = pi.webParticipantDetails.peerConnection;
            cpi.joinTimestampMsecs = pi.joinTimestampMsecs;
            evt.removedParticipants.push_back(cpi);
         }

         if (args.isPartialUpdate)
         {
            auto itEvtPart = evt.participantList.begin();
            for (; itEvtPart != evt.participantList.end(); ++itEvtPart)
            {
               mPendingParticipantListUpdateEvent[evt.conference].conference = evt.conference;
               mPendingParticipantListUpdateEvent[evt.conference].participantList.push_back(*itEvtPart);
            }

            if (args.isLastChunk)
            {
               mInterface->firePartListUpdatedEvent(mHandle, mPendingParticipantListUpdateEvent[evt.conference]);
               mPendingParticipantListUpdateEvent[evt.conference].participantList.clear();

               if (itConf->lastConnectedEvent.get() != NULL)
               {
                  if (itConf->participantPermissions.canCreateConference != itConf->lastConnectedEvent->permissions.canCreateConference)
                  {
                     itConf->lastConnectedEvent->permissions.canCreateConference = itConf->participantPermissions.canCreateConference;
                     mInterface->fireConfSessionStatusChanged(mHandle, *itConf->lastConnectedEvent);
                  }
               }
            }
         }
         else
         {
            mInterface->firePartListUpdatedEvent(mHandle, evt);

            if (itConf->lastConnectedEvent.get() != NULL)
            {
               if (itConf->participantPermissions.canCreateConference != itConf->lastConnectedEvent->permissions.canCreateConference)
               {
                  itConf->lastConnectedEvent->permissions.canCreateConference = itConf->participantPermissions.canCreateConference;
                  mInterface->fireConfSessionStatusChanged(mHandle, *itConf->lastConnectedEvent);
               }
            }
         }

         break;
      }
   }
   return kSuccess;
}

int ConferenceConnectorImpl::onPeerConnectionAnswer(ConferenceHandle conference, const PeerConnectionAnswerEvent& args)
{
   InfoLog(<< "XXX ConferenceConnectorImpl::onPeerConnectionAnswer(" << conference << ")");
   auto itSess = mSessStateMap.begin();
   for (; itSess != mSessStateMap.end(); ++itSess)
   {
      auto itConf = getConferenceState(itSess->second.cloudConference);
      if (itConf != mConfStateMap.end())
      {
         if (itConf->confHandle == conference && itSess->second.part == args.participant)
         {
            mPeerConnMgr->setRemoteDescription(itSess->second.pc, args.sdpAnswer);
/*
            // if we were capturing before we disconnected let's restore the capturing

            if (itSess->second.isCapturingLocalScreen)
            {
               mVideoExtIf->startScreenshare();
            }
            if (itSess->second.isCapturingLocalVideo)
            {
               mVideoIf->startCapture();
            }
*/
            ConferenceSessionMediaStatusChangedEvent statusEvt;
            statusEvt.conference = itSess->second.cloudConference;
            statusEvt.session = itSess->first;
            statusEvt.peerConnection = itSess->second.pc;
            statusEvt.mediaStatus = SessionMediaStatus_AnswerReceived;
            mInterface->fireConfSessionMediaStatusChanged(mHandle, statusEvt);
            break;
         }
      }
   }
   return kSuccess;
}

int ConferenceConnectorImpl::onConferenceTranscriptionResult(ConferenceHandle conference, const ConferenceTranscriptionEvent& args)
{
   return kSuccess;
}

int ConferenceConnectorImpl::onConferenceList(const CPCAPI2::ConferenceBridge::ConferenceListResult& args)
{
   std::set<CloudConferenceHandle> serverSet;
   std::set<CloudConferenceHandle> localSet;
   auto itLocal = mConfStateMap.begin();
   for (; itLocal != mConfStateMap.end(); ++itLocal)
   {
      localSet.insert(itLocal->confHandle);
   }

   ConferenceListUpdatedEvent evt;
   for (const CPCAPI2::ConferenceBridge::ConferenceDetailsResult& cdr : args.conferences)
   {
      serverSet.insert((CloudConferenceHandle)cdr.conference);

      std::vector<ConferenceState>::iterator itCs = getConferenceState_ConfHandle(cdr.conference);

      CloudConferenceInfo cci;
      if (itCs != mConfStateMap.end())
      {
         cci.conference = itCs->cloudConfHandle;
      }
      else
      {
         cci.conference = (CloudConferenceHandle)cdr.conference;
      }
      //cci.conferenceType = (std::find(cdr.conferenceInfo.tags.begin(), cdr.conferenceInfo.tags.end(), 0x2000) != cdr.conferenceInfo.tags.end() ? CloudConferenceType_Screenshare : CloudConferenceType_AudioVideo_MCU);
      if (std::find(cdr.conferenceInfo.tags.begin(), cdr.conferenceInfo.tags.end(), 0x2000) != cdr.conferenceInfo.tags.end())
      {
         cci.conferenceType = CloudConferenceType_Screenshare;
      }
      else if (std::find(cdr.conferenceInfo.tags.begin(), cdr.conferenceInfo.tags.end(), 0x1000) != cdr.conferenceInfo.tags.end())
      {
         cci.conferenceType = CloudConferenceType_AudioVideo_SFU;
      }
      else
      {
         cci.conferenceType = CloudConferenceType_AudioVideo_MCU;
      }
      cci.displayName = cdr.conferenceInfo.label;
      cci.joinUrl = cdr.conferenceJoinUrl;
      cci.conferenceId = cdr.conferenceToken;
      cci.description = cdr.conferenceInfo.description;
      cci.tags = cdr.conferenceInfo.tags;
      cci.persistent = cdr.persistent;
      evt.conferenceList.push_back(cci);

      mInterface->mapConferenceHandleToInst(cci.conference, shared_from_this());

      if (itCs != mConfStateMap.end())
      {
         itCs->settings.conferenceType = cci.conferenceType;
         itCs->confInfo = cdr.conferenceInfo;
      }
      else
      {
         ConferenceState cs;
         cs.confHandle = cdr.conference;
         cs.cloudConfHandle = cdr.conference;
         cs.settings.conferenceType = cci.conferenceType;
         cs.confInfo = cdr.conferenceInfo;
         cs.settings.conferenceId = cdr.conferenceToken;
         mConfStateMap.push_back(cs);
      }
      resolveNatTraversalServerHostname(cdr.conferenceInfo.natTraversalServerInfo.natTraversalServerHostname, cdr.conferenceInfo.natTraversalServerInfo.natTraversalServerPort);
   }

   if (serverSet != localSet || mQueryConferenceListPending)
   {
      mQueryConferenceListPending = false;
      mInterface->fireConfListUpdatedEvent(mHandle, evt);
   }
   return kSuccess;
}

int ConferenceConnectorImpl::onSignalingStateChange(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SignalingStateChangeEvent& args)
{
   return kSuccess;
}

int ConferenceConnectorImpl::onCreateOfferResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateOfferResult& args)
{
   InfoLog(<< "XXX ConferenceConnectorImpl::onCreateOfferResult(" << pc << ")");
   auto it = mSessStateMap.begin();
   for (; it != mSessStateMap.end(); ++it)
   {
      if (it->second.pc == pc)
      {
         auto itConf = getConferenceState(it->second.cloudConference);
         if (itConf != mConfStateMap.end())
         {
            mPeerConnMgr->setLocalDescription(pc, args.sdp);
            if (it->second.part == (ConferenceParticipantHandle)-1)
            {
               it->second.part = mConfBridgeMgr->createWebParticipant(itConf->confHandle);
               itConf->part = it->second.part;
               WebParticipantIdentity identityInfo;
               identityInfo.address = it->second.sessionSettings.address;
               identityInfo.displayName = it->second.sessionSettings.displayName;
               identityInfo.tags = it->second.sessionSettings.tags;
               identityInfo.owner = itConf->owner;
               mConfBridgeMgr->setWebParticipantIdentity(it->second.part, identityInfo);
            }
            mConfBridgeMgr->sendPeerConnectionOffer(it->second.part, args.sdp, this);

            InfoLog(<< "XXX sendPeerConnectionOffer: " << args.sdp.sdpString.c_str());

            // wait for ConferenceBridgeHandler::onPeerConnectionAnswer(..)

            ConferenceSessionMediaStatusChangedEvent statusEvt;
            statusEvt.conference = it->second.cloudConference;
            statusEvt.session = it->first;
            statusEvt.peerConnection = it->second.pc;
            statusEvt.mediaStatus = SessionMediaStatus_OfferSent;
            mInterface->fireConfSessionMediaStatusChanged(mHandle, statusEvt);

            break;
         }
      }
   }
   return kSuccess;
}

int ConferenceConnectorImpl::onCreateAnswerResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args)
{
   InfoLog(<< "ConferenceConnectorImpl::onCreateAnswerResult(" << pc << ")");
   return kSuccess;
}
int ConferenceConnectorImpl::onSetLocalSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetLocalSessionDescriptionResult& args)
{
   InfoLog(<< "ConferenceConnectorImpl::onSetLocalSessionDescriptionResult(" << pc << ")");
   return kSuccess;
}

void ConferenceConnectorImpl::setIncomingVideoRenderTargetWrap(CPCAPI2::Media::Video* videoIf, int mediaStreamId, void* surface, Media::VideoSurfaceType type)
{
   videoIf->setIncomingVideoRenderTarget(mediaStreamId, surface, type);
}

int ConferenceConnectorImpl::onSetRemoteSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetRemoteSessionDescriptionResult& args)
{
   InfoLog(<< "ConferenceConnectorImpl::onSetRemoteSessionDescriptionResult(" << pc << ")");
   auto itSess = mSessStateMap.begin();
   for (; itSess != mSessStateMap.end(); ++itSess)
   {
      if (itSess->second.pc == pc)
      {
         CloudConferenceType confType = CloudConferenceType_Screenshare;
         auto itConf = getConferenceState(itSess->second.cloudConference);
         if (itConf != mConfStateMap.end())
         {
            confType = itConf->settings.conferenceType;
         }

         ConferenceSessionStatusChangedEvent connectedStatusEvt;
         connectedStatusEvt.conference = itConf->cloudConfHandle;
         connectedStatusEvt.session = itSess->first;
         connectedStatusEvt.sessionStatus = SessionStatus_Connected;
         connectedStatusEvt.audio.mediaDirection = ConferenceConnector::MediaDirection_None;
         connectedStatusEvt.video.mediaDirection = ConferenceConnector::MediaDirection_None;
         connectedStatusEvt.screenshare.mediaDirection = ConferenceConnector::MediaDirection_None;
         connectedStatusEvt.permissions.canCreateConference = itConf->participantPermissions.canCreateConference;
         connectedStatusEvt.peerConnection = itSess->second.pc;
         connectedStatusEvt.hasFloor = itConf->hasFloor;

         for (const CPCAPI2::PeerConnection::MediaInfo& mi : args.mediaInfo)
         {
            if (mi.mediaType == PeerConnection::MediaType_Audio)
            {
               connectedStatusEvt.audio.mediaDirection = (ConferenceConnector::MediaDirection)mi.mediaDirection;
               connectedStatusEvt.audio.mediaStreamId = mi.mediaStreamId;
               connectedStatusEvt.audio.mediaType = SessionMediaType_Audio;
            }
            else if (mi.mediaType == PeerConnection::MediaType_Video)
            {
               if (itSess->second.pendingMediaSettings.get() != NULL)
               {
                  resip::ReadCallbackBase* rcbSetIncomingVideoRenderTarget = resip::resip_static_bind(&ConferenceConnectorImpl::setIncomingVideoRenderTargetWrap, mVideoIf, mi.mediaStreamId, itSess->second.pendingMediaSettings->remoteVideoRenderSurface, itSess->second.pendingMediaSettings->remoteVideoRenderSurfaceType);
                  dynamic_cast<CPCAPI2::Media::VideoInterface*>(mVideoIf)->postCallback(rcbSetIncomingVideoRenderTarget);
                  mVideoExtIf->setScreenshareCaptureMaxFramerate(itSess->second.pendingMediaSettings->screenCaptureMaxFrameRate);
               }
               itSess->second.videoRecvMediaStreamId = mi.mediaStreamId;

               if (confType == CloudConferenceType_Screenshare)
               {
                  connectedStatusEvt.screenshare.mediaDirection = (ConferenceConnector::MediaDirection)mi.mediaDirection;
                  connectedStatusEvt.screenshare.mediaType = SessionMediaType_Screenshare;
                  if (mi.mediaDirection == PeerConnection::MediaDirection_SendOnly || mi.mediaDirection == PeerConnection::MediaDirection_SendRecv)
                  {
                     mVideoExtIf->startScreenshare(this);
                     itSess->second.isCapturingLocalScreen = true;
                  }
                  if (mi.mediaDirection == PeerConnection::MediaDirection_RecvOnly || mi.mediaDirection == PeerConnection::MediaDirection_SendRecv)
                  {
                     if (mVideoStreamingMgr != NULL)
                     {
                        itSess->second.vidStream = mVideoStreamingMgr->createVideoStream();
                        CPCAPI2::VideoStreaming::VideoStreamSettings vsSettings;
                        vsSettings.streamFormat = CPCAPI2::VideoStreaming::StreamFormat_I420;
                        vsSettings.videoReceiveChannel = mi.mediaStreamId;
                        mVideoStreamingMgr->setVideoStreamSettings(itSess->second.vidStream, vsSettings);
                        mVideoStreamingMgr->startVideoStream(itSess->second.vidStream);

                        mVideoIf->requestKeyFrame(mi.mediaStreamId);

                        connectedStatusEvt.screenshare.mediaStreamId = itSess->second.vidStream;
                     }
                  }
               }
               else
               {
                  connectedStatusEvt.video.mediaDirection = (ConferenceConnector::MediaDirection)mi.mediaDirection;
                  connectedStatusEvt.video.mediaType = SessionMediaType_Video;
                  if (mi.mediaDirection == PeerConnection::MediaDirection_SendRecv || mi.mediaDirection == PeerConnection::MediaDirection_SendOnly)
                  {
                     mVideoIf->startCapture();
                     itSess->second.isCapturingLocalVideo = true;
                  }
                  if (mi.mediaDirection == PeerConnection::MediaDirection_RecvOnly || mi.mediaDirection == PeerConnection::MediaDirection_SendRecv)
                  {
                     if (mVideoStreamingMgr != NULL)
                     {
                        itSess->second.vidStream = mVideoStreamingMgr->createVideoStream();
                        CPCAPI2::VideoStreaming::VideoStreamSettings vsSettings;
                        vsSettings.streamFormat = CPCAPI2::VideoStreaming::StreamFormat_I420;
                        vsSettings.videoReceiveChannel = mi.mediaStreamId;
                        mVideoStreamingMgr->setVideoStreamSettings(itSess->second.vidStream, vsSettings);
                        mVideoStreamingMgr->startVideoStream(itSess->second.vidStream);

                        mVideoIf->requestKeyFrame(mi.mediaStreamId);

                        connectedStatusEvt.video.mediaStreamId = itSess->second.vidStream;
                     }
                  }
               }
            }
         }

         if (NULL != itSess->second.pendingMediaSettings.get())
         {
            itSess->second.activeMediaSettings = *itSess->second.pendingMediaSettings;
            itSess->second.pendingMediaSettings.reset();
         }

         itConf->lastConnectedEvent.reset(new ConferenceSessionStatusChangedEvent(connectedStatusEvt));
         mInterface->fireConfSessionStatusChanged(mHandle, connectedStatusEvt);
         break;
      }
   }

   return kSuccess;
}

int ConferenceConnectorImpl::onMediaInactivity(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::MediaInactivityEvent& args)
{
   InfoLog(<< "ConferenceConnectorImpl::onMediaInactivity(" << pc << ")");
   // TODO: Should we cleanup the conference session due to media inactivity
   /*
   auto it = mSessStateMap.begin();
   for (; it != mSessStateMap.end(); ++it)
   {
      if (it->second.pc == pc)
      {
         auto itConf = mConfStateMap.find(it->second.cloudConference);
         if (itConf != mConfStateMap.end())
         {
            // ConferenceSessionStatusChangedEvent failStatusEvt;
            // failStatusEvt.conference = itConf->first;
            // failStatusEvt.session = it->first;
            // failStatusEvt.sessionStatus = SessionStatus_ConnectionFailed;
            // mInterface->fireConfSessionStatusChanged(mHandle, failStatusEvt);

            endSession(it->first, false);

            CPCAPI2::ConferenceBridge::ConferenceEndedEvent confEndedEvt;
            onConferenceEnded(it->second.cloudConference, confEndedEvt);
            break;
         }
      }
   }
   */
   return kSuccess;
}

int ConferenceConnectorImpl::onError(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::ErrorEvent& args)
{
   InfoLog(<< "ConferenceConnectorImpl::onError(" << pc << ")");
   auto it = mSessStateMap.begin();
   for (; it != mSessStateMap.end(); ++it)
   {
      if (it->second.pc == pc)
      {
         auto itConf = getConferenceState(it->second.cloudConference);
         if (itConf != mConfStateMap.end())
         {
            ConferenceSessionStatusChangedEvent failStatusEvt;
            failStatusEvt.conference = itConf->cloudConfHandle;
            failStatusEvt.session = it->first;
            failStatusEvt.sessionStatus = SessionStatus_ConnectionFailed;
            failStatusEvt.peerConnection = pc;
            mInterface->fireConfSessionStatusChanged(mHandle, failStatusEvt);
            break;
         }
      }
   }

   return kSuccess;
}

int ConferenceConnectorImpl::onPeerConnectionMediaStatistics(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const PeerConnection::PeerConnectionMediaStatisticsEvent& args)
{
   InfoLog(<< "ConferenceConnectorImpl::onPeerConnectionMediaStatistics(" << pc << ")");
   auto it = mSessStateMap.begin();
   for (; it != mSessStateMap.end(); ++it)
   {
      if (it->second.pc == pc)
      {
         auto itConf = getConferenceState(it->second.cloudConference);
         if (itConf != mConfStateMap.end())
         {
            ConferenceConnectorMediaStatisticsEvent statEvent;
            for (auto i = args.mediaStreamStats.begin(); i != args.mediaStreamStats.end(); ++i)
            {
               ConferenceConnectorMediaStatistics stats;
               stats.connectionId = pc;
               stats.mediaStreamId = i->mediaStream;
               stats.rtpPacketReceiveCount = i->rtpPacketCount;
               stats.rtcpPacketReceiveCount = i->rtcpPacketCount;
               stats.videoReceiveFrameWidth = i->videoReceiveFrameWidth;
               stats.videoReceiveFrameHeight = i->videoReceiveFrameHeight;
               stats.videoReceiveFps = i->videoReceiveFps;
               statEvent.mediaStreamStats.push_back(stats);
            }

            mInterface->fireConfConnMediaStatistics(mHandle, statEvent);
            break;
         }
      }
   }

   return kSuccess;
}

void ConferenceConnectorImpl::onVideoWebsocketServerStarted(int mediaStreamId, const CPCAPI2::Media::VideoWebsocketServerStartedEvent& args)
{
   //WebVideoServerReadyEvent wvsReadyArgs;
   //mPCM_if->firePcEvent(cpcFunc(PeerConnectionHandler::onWebVideoServerReady), mHandle, wvsReadyArgs);
}

void ConferenceConnectorImpl::onScreenShareError()
{
   mInterface->getThisSdkPhone()->getSdkModuleThread().post(resip::resip_bind(&ConferenceConnectorImpl::handleScreenShareError, this));
}

void ConferenceConnectorImpl::handleScreenShareError()
{
   InfoLog(<< "ConferenceConnectorImpl::onScreenShareError");
   auto it = mSessStateMap.begin();
   for (; it != mSessStateMap.end(); ++it)
   {
      if (it->second.isCapturingLocalScreen)
      {
         endSession(it->first, false);
         break;
      }
   }
}

cpc::string get_debug_string_for_confinfo(const CPCAPI2::ConferenceBridge::ConferenceInfo& info)
{
   std::stringstream ss;
   ss << "label: " << info.label << " owner: " << info.owner << " conferenceToken: " << info.conferenceToken << " conference: " << info.conference << " description: " << info.description << " creatorDisplayName: " << info.creatorDisplayName
      << " streamId: " << info.streamId << " transcriptionEnabled: " << info.transcriptionEnabled << " numParticipants: " << info.numParticipants;
   // << " natTraversalServerInfo: {" << get_debug_string(info.natTraversalServerInfo)
   // << "} bitrateConfig: {" << get_debug_string(info.bitrateConfig) << "} mediaEncryptionMode: " << info.mediaEncryptionMode;
   ss << " mediaEncryptionMode: " << info.mediaEncryptionMode;
   ss << " tag-count: " << info.tags.size() << " { ";
   for (cpc::vector<int>::const_iterator i = info.tags.begin(); i != info.tags.end(); i++)
   {
      ss  << (*i) << " ";
   }
   ss << "}";
   /*
   ss << " associated-conferences-count: " << info.associatedConferences.size() << " { ";
   for (cpc::vector<ConferenceInfo>::const_iterator j = info.associatedConferences.begin(); j != info.associatedConferences.end(); j++)
   {
      ss  << (*j) << " ";
   }
   ss << "}";
   */
   return ss.str().c_str();
}

cpc::string ConferenceConnectorImpl::get_debug_string(const CPCAPI2::ConferenceConnector::ConferenceConnectorImpl::ConferenceState& state)
{
   std::stringstream ss;
   // ConferenceSessionStatusChangedEvent
   ss << "confHandle: " << state.confHandle << " cloudConfHandle: " << state.cloudConfHandle << " part: " << state.part << " owner: " << state.owner << " waitingForCreatedEvent: " << state.waitingForCreatedEvent
      << " participantPermissions: " << state.participantPermissions.canCreateConference << " confInfo: {" << get_debug_string_for_confinfo(state.confInfo) << "}";
   return ss.str().c_str();
}

cpc::string ConferenceConnectorImpl::get_debug_string(const CPCAPI2::ConferenceConnector::ConferenceConnectorImpl::SessionState& state)
{
   std::stringstream ss;
   // CloudConferenceSessionSettings
   // CloudConferenceSessionMediaSettings
   ss << "participant: " << state.part << " pc: " << state.pc << " sessionSettingsUpdated: " << state.sessionSettingsUpdated << " cloudConference: " << state.cloudConference << " isCapturingLocalVideo: " << state.isCapturingLocalVideo
      << " isCapturingLocalScreen: " << state.isCapturingLocalScreen << " videoRecvMediaStreamId: " << state.videoRecvMediaStreamId << " audioMediaStream: " << state.audioMediaStream << " videoMediaStream: " << state.videoMediaStream
      << " vidStream: " << state.vidStream << " enableMediaInactivityMonitor: " << state.enableMediaInactivityMonitor << " mediaInactivityIntervalMsecs: " << state.mediaInactivityIntervalMsecs;
   return ss.str().c_str();
}

cpc::string ConferenceConnectorImpl::get_debug_string(const CPCAPI2::ConferenceConnector::ConferenceConnectorImpl& impl)
{
   std::stringstream ss;
   ss << " conference-handle: " << impl.mHandle << " service-uri: " << impl.mConfServiceUri.c_str() << " server-connection: " << impl.mConfServiceConn.get()
      << " service-status: " << CloudServerConnection::get_debug_string(impl.mConfServiceConnStatus) << " conf-phone: " << impl.mConfPhone << " disconnecting: "
      << impl.mConferenceConnectorDisconnecting << " destroyed: " << impl.mConferenceConnectorDestroyed << " disconnect-state: " << impl.mConferenceConnectorDisconnectState;
   return ss.str().c_str();
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceConnectorImpl::ConferenceState& state)
{
   os << CPCAPI2::ConferenceConnector::ConferenceConnectorImpl::get_debug_string(state);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceConnectorImpl::SessionState& state)
{
   os << CPCAPI2::ConferenceConnector::ConferenceConnectorImpl::get_debug_string(state);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceConnectorImpl& impl)
{
   os << CPCAPI2::ConferenceConnector::ConferenceConnectorImpl::get_debug_string(impl);
   return os;
}

}

}

#endif // CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE
