#include "jsonrpc/StringSerialization.h"
#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_PHONE_LICENSEINFO_H
#define JSONRPC_CPCAPI2_PHONE_LICENSEINFO_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  class LicenseInfo
  {
  public:
    std::optional<cpc::string> licenseKey;
    std::optional<cpc::string> licenseDocumentLocation;
    std::optional<cpc::string> licenseAor;

    LicenseInfo() = default;

    LicenseInfo(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const LicenseInfo& o)
    {
      j = nlohmann::json::object();
       if (o.licenseKey) { j["licenseKey"] = *(o.licenseKey); }
     if (o.licenseDocumentLocation) { j["licenseDocumentLocation"] = *(o.licenseDocumentLocation); }
     if (o.licenseAor) { j["licenseAor"] = *(o.licenseAor); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, LicenseInfo& o)
    {
     if (j.contains("licenseKey")) { o.licenseKey = j.at("licenseKey"); }
     if (j.contains("licenseDocumentLocation")) { o.licenseDocumentLocation = j.at("licenseDocumentLocation"); }
     if (j.contains("licenseAor")) { o.licenseAor = j.at("licenseAor"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_LICENSEINFO_H
