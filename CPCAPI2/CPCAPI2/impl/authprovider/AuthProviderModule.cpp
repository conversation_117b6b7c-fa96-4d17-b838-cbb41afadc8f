#include "brand_branded.h"

#include "authprovider/AuthProviderInterface.h"
#include "authprovider/AuthProviderMockInterface.h"

#if (CPCAPI2_BRAND_AUTH_PROVIDER_MODULE == 1)
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace Auth
{

AuthProvider* AuthProvider::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_AUTH_PROVIDER_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<AuthProviderInterface>(phone, "AuthProviderInterface");
#else
   return NULL;
#endif
}

AuthProviderMock* AuthProviderMock::getInterface(AuthProvider* authProvider)
{
#if (CPCAPI2_BRAND_AUTH_PROVIDER_MODULE == 1 && CPCAPI2_BRAND_AUTH_PROVIDER_MOCK_MODULE == 1)
   AuthProviderInterface* parent = dynamic_cast<AuthProviderInterface*>(authProvider);
   if (parent == NULL) return NULL;
   PhoneInterface* phone = parent->phoneInterface();
   return _GetInterface<AuthProviderMockInterface>(phone, "AuthProviderMockInterface", parent);
#else
   return NULL;
#endif
}

}
}
