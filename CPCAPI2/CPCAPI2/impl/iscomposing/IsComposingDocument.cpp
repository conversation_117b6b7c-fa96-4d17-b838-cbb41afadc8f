#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_CHAT_MODULE==1 || CPCAPI2_BRAND_IM_MODULE==1 || CPCAPI2_BRAND_XMPP_CHAT_MODULE==1 || CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
#include "cpcapi2utils.h"
#include "IsComposingDocument.h"
#include "IsComposingHelper.h"

#include <sstream>

#include <assert.h>

#include <libxml/tree.h>

namespace CPCAPI2
{
namespace IsComposing
{

IsComposingDocument::IsComposingDocument() 
   : refresh(0)
{
   CpmHelper::initializeDateTime(lastActive);

}

IsComposingDocument::~IsComposingDocument()
{
   // do not call here; can cause crashes. see LibxmlSharedUsage.h
   //::xmlCleanupParser();
}

cpc::string IsComposingDocument::toString() const
{
   // Populate the XML tree
   xmlDocPtr doc = xmlNewDoc(BAD_CAST "1.0");
   xmlNodePtr rootNode = xmlNewNode(NULL, BAD_CAST "isComposing");
   xmlNewProp(rootNode, BAD_CAST "xmlns", BAD_CAST "urn:ietf:params:xml:ns:im-iscomposing");
   xmlNewProp(rootNode, BAD_CAST "xmlns:xsi", BAD_CAST "http://www.w3.org/2001/XMLSchema-instance");
   xmlNewProp(rootNode, BAD_CAST "xsi:schemaLocation", BAD_CAST "urn:ietf:params:xml:ns:im-composing iscomposing.xsd");
   xmlDocSetRootElement(doc, rootNode);
   cpc::string stateStr = stateToString(state);
   xmlNewChild(rootNode, NULL, BAD_CAST "state", BAD_CAST stateStr.c_str());
#ifndef __THREADX // TODO: date is not reliable due to timezone possibly not set on threadx environment
   cpc::string lastActiveStr = IsComposingHelper::createDateTimeString(lastActive);
   xmlNewChild(rootNode, NULL, BAD_CAST "lastactive", BAD_CAST lastActiveStr.c_str());
#endif
   cpc::string contentTypeStr = IsComposingHelper::contentTypeToString(contentType);
   xmlNewChild(rootNode, NULL, BAD_CAST "contenttype", BAD_CAST contentTypeStr.c_str());
   if (refresh > 0)
   {
      cpc::string refreshStr = refreshToString(refresh);
      xmlNewChild(rootNode, NULL, BAD_CAST "refresh", BAD_CAST refreshStr.c_str());
   }

   // Convert the XML tree to a string
   xmlChar* out = NULL;
   int size;
   xmlDocDumpFormatMemoryEnc(doc, &out, &size, "UTF-8", 1);
   cpc::string xml = (const char *) out;
   xmlFree(out);

   // Destroy the XML tree
   ::xmlFreeDoc(doc);

   return xml;
}

IsComposingDocument* IsComposingDocument::parse(const cpc::string& content)
{
   IsComposingDocument *isComposingDocument = NULL;

   // Parse and build a XML tree
   xmlDocPtr doc = ::xmlReadMemory(content.c_str(), (int)content.size(), "noname.xml", NULL, 0);
   xmlNodePtr rootNode = xmlDocGetRootElement(doc);

   if(rootNode != NULL)
   {
      isComposingDocument = new IsComposingDocument();
      
      // Get the state (mandatory)
      cpc::string stateStr = IsComposingHelper::getChildNodeText(rootNode, "state");
      assert(!stateStr.empty());
      isComposingDocument->state = stringToState(stateStr);

      // Get last active (optional)
      cpc::string lastActiveStr = IsComposingHelper::getChildNodeText(rootNode, "lastactive");
      if (!lastActiveStr.empty())
      {
         isComposingDocument->lastActive = IsComposingHelper::extractDateTime(lastActiveStr);
      }

      // Get content type (optional)
      cpc::string contentTypeStr = IsComposingHelper::getChildNodeText(rootNode, "contenttype");
      if (!contentTypeStr.empty())
      {
         isComposingDocument->contentType = IsComposingHelper::stringToContentType(contentTypeStr);
      }

      // Get refresh (optional)
      cpc::string refreshStr = IsComposingHelper::getChildNodeText(rootNode, "refresh");
      if (!refreshStr.empty())
      {
         isComposingDocument->refresh = stringToRefresh(refreshStr);
      }
   }
   
   // Destroy the XML tree
   ::xmlFreeDoc(doc);

   return isComposingDocument;
}

cpc::string IsComposingDocument::stateToString(IsComposingMessageState state)
{
   if (state == IsComposingMessageState_Active)
   {
      return "active";
   }
   else if (state == IsComposingMessageState_Idle)
   {
      return "idle";
   }
   else
   {
      // Unsupported state
      assert(false);
   }

   return "";
}

cpc::string IsComposingDocument::refreshToString(int refresh)
{
   std::ostringstream ss;
   ss << refresh;
   return ss.str().c_str();
}

IsComposingMessageState IsComposingDocument::stringToState(const cpc::string &stateStr)
{
   std::string lowerStateStr = stateStr.c_str();
   std::transform(lowerStateStr.begin(), lowerStateStr.end(), lowerStateStr.begin(), tolower);
   if (lowerStateStr == std::string("active"))
   {
      return IsComposingMessageState_Active;
   }
   else if (lowerStateStr == std::string("idle"))
   {
      return IsComposingMessageState_Idle;
   }
   else
   {
      // Unsupported state
      assert(false);
   }

   return IsComposingMessageState_Idle;
}

int IsComposingDocument::stringToRefresh(const cpc::string& refreshStr)
{
   std::istringstream ss(refreshStr.c_str());
   int refresh;
   ss >> refresh;
   return refresh;
}

}
}
#endif
