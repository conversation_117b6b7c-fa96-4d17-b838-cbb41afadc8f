#include "brand_branded.h"

#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_REGISTRAR_MODULE == 1)
#include "ConferenceRegistrarManagerImpl.h"
#include "ConferenceRegistrarManagerInterface.h"
#include "util/IpHelpers.h"
#include "json/JsonHelper.h"
#include "jsonapi/JsonApiServerSendTransportInterface.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "jsonapi/JsonApiServer_HTTP.h"
#include "cloudconnector/CloudServerConnection.h"
#include "../util/cpc_logger.h"
#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

#include <rutil/Random.hxx>

#include <client_https.hpp>
#include "websocketpp/uri.hpp"
#include <soci/sqlite3/soci-sqlite3.h>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>
#include <prettywriter.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER
#define AUTH_TIMEOUT_SECONDS 5
#define ORCH_TIMEOUT_SECONDS 5
#define CONF_REG_DB_THREADS 4

namespace CPCAPI2
{
namespace ConferenceBridge
{
std::atomic_int ConferenceRegistrarManagerImpl::sNextNodeId(1);

ConferenceRegistrarManagerImpl::ConferenceRegistrarManagerImpl(Phone* phone, ConferenceRegistrarManagerInterface* crmint)
   : mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mInterface(crmint),
     mCheckinTimer(mPhone->getSdkModuleThread()),
     mWebRequestThread("ConferenceRegistrar"),
     mLookupWebRequestThread("ConfRegLookup"),
     mNodeId(-1),
     mInService(false),
     mIsPartOfCluster(false),
     mStateLock(webrtc::RWLockWrapper::CreateRWLock()),
     mFollowerCacheLock(webrtc::RWLockWrapper::CreateRWLock()),
     mDbPool(CONF_REG_DB_THREADS),
     mDbLock(webrtc::RWLockWrapper::CreateRWLock())
{
   mWebRequestThread.start();
   mLookupWebRequestThread.start();
}

ConferenceRegistrarManagerImpl::~ConferenceRegistrarManagerImpl()
{
   shutdown();
   mWebRequestThread.stop();
   mWebRequestThread.join();
   mLookupWebRequestThread.stop();
   mLookupWebRequestThread.join();
}

int ConferenceRegistrarManagerImpl::start(const ConferenceRegistrarConfig& confRegistrarConfig)
{
   InfoLog(<< "ConferenceRegistrarManagerImpl::start(..)");

   mLocalConfig = confRegistrarConfig;
   mInService = true;

   resip::Data srcIp = "127.0.0.1";
   if (confRegistrarConfig.conferenceRegistrarServiceIp.empty())
   {
      resip::Tuple googDns("*******", 53, resip::UDP);
      IpHelpers::getPreferredLocalIpAddress(googDns, srcIp);
   }
   else
   {
      srcIp = confRegistrarConfig.conferenceRegistrarServiceIp.c_str();
   }
   srcIp += ":";
   srcIp += resip::Data::from(confRegistrarConfig.conferenceRegistrarServicePort);
   mNodeId = confRegistrarConfig.nodeId;

   initializeDb(mLocalConfig.urlMapFilename.empty() ? "urlmap.db" : mLocalConfig.urlMapFilename);

   addQueryContextHandler();
   addJoinClusterHandler();
   addLeaderHelloHandler();
   addRegisterContextHandler();

   mCheckinTimer.expires_from_now(10000);
   mCheckinTimer.async_wait(this, 0, NULL);

   doJoinClusterHTTP(mLocalConfig.joinClusterUrl);

   return kSuccess;
}

int ConferenceRegistrarManagerImpl::shutdown()
{
   mInService = false;
   mCheckinTimer.cancel();
   mNodeId = -1;
   {
      webrtc::WriteLockScoped lck(*mStateLock);
      mLeaderState.mLocalIsLeader = false;
      mLeaderState.mFollowerMap.clear();
      mJoinClusterUrl.clear();
      mQueryContextUrl.clear();
      mLeaderNodeId = -1;
      mIsPartOfCluster = false;

      if (resip::Data(mLocalConfig.urlMapFilename.c_str()).postfix("_autotests.db"))
      {
         flushUrlMapDb();
      }

      webrtc::WriteLockScoped dbLck(*mDbLock);
      for (size_t i = 0; i != CONF_REG_DB_THREADS; ++i)
      {
         mDbPool.at(i).close();
      }
   }
   return kSuccess;
}

bool ConferenceRegistrarManagerImpl::isPartOfCluster()
{
   return mIsPartOfCluster;
}

bool ConferenceRegistrarManagerImpl::localIsLeader()
{
   webrtc::ReadLockScoped lck(*mStateLock);
   return mLeaderState.mLocalIsLeader;
}

int ConferenceRegistrarManagerImpl::addFollower(int nodeId, const cpc::string& ipAndPort)
{
   InfoLog(<< "addFollower: " << nodeId << ": " << ipAndPort);

   ConferenceRegistrarAddFollowerResult addFollowerResult;
   addFollowerResult.success = true;
   mInterface->fireEvent(cpcFunc(ConferenceRegistrarHandler::onAddFollowerComplete), 0, addFollowerResult);
   return kSuccess;
}

int ConferenceRegistrarManagerImpl::registerConference(const cpc::string& joinUrl, int nodeId, bool overrideExisting, RegisterConferenceResultHandler* handler)
{
   RegisterConferenceResult res;
   if (localIsLeader())
   {
      resip::Data contextStr = joinUrl.c_str();
      if (contextStr.prefix("/"))
      {
         contextStr = contextStr.substr(1);
      }
      if (contextStr.prefix("screenshare"))
      {
         contextStr = contextStr.substr(12);
      }

      res.success = true;
      {
         webrtc::WriteLockScoped lck(*mStateLock);

         int newNode = -1;
         cpc::string newNodeUrl;
         auto itFol = mLeaderState.mFollowerMap.find(nodeId);
         if (itFol != mLeaderState.mFollowerMap.end())
         {
            newNode = itFol->first;
            newNodeUrl = itFol->second.wsUrlBase;
         }
         if (newNode == -1)
         {
            newNodeUrl = mLocalConfig.wsUrlBase;
         }

         upsertUrlMappingDb(contextStr.c_str(), newNodeUrl);
         mLeaderState.mRecentUrlMappings[contextStr.c_str()] = newNodeUrl;
      }
      mInterface->fireCompletionHandlerEvent(handler, cpcFunc(RegisterConferenceResultHandler::onRegisterConferenceComplete), 0, res);
   }
   else
   {
      doRegisterConferenceHTTP(joinUrl, nodeId, overrideExisting, handler);
   }
   return kSuccess;
}

// querySourceNodeId == -1 when the lookup happened locally vs. via handleQueryContext(..)
int ConferenceRegistrarManagerImpl::lookupConference(const cpc::string& joinUrlIn, int querySourceNodeId, LookupConferenceResultHandler* handler)
{
   if (localIsLeader())
   {
      return lookupConferenceAsLeader(joinUrlIn, querySourceNodeId, handler);
   }
   return lookupConferenceAsFollower(joinUrlIn, querySourceNodeId, handler);
}

int ConferenceRegistrarManagerImpl::lookupConferenceAsLeader(const cpc::string& joinUrlIn, int querySourceNodeId, LookupConferenceResultHandler* handler)
{
   resip::Data joinUrl = joinUrlIn.c_str();
   if (joinUrl.prefix("/"))
   {
      joinUrl = joinUrl.substr(1);
   }
   InfoLog(<< "lookupConference: joinUrl: " << joinUrl << ", querySourceNodeId: " << querySourceNodeId);

   LookupConferenceResult res;
   res.success = false;
   res.shouldRetry = false;

   bool foundMatch = false;
   {
      webrtc::ReadLockScoped rlck(*mStateLock);
      res.success = true;
         
      bool urlExists = false;
      cpc::string wsBaseUrl;
      queryJoinUrl(joinUrlIn, urlExists, wsBaseUrl);

      if (urlExists)
      {
         auto itFol = mLeaderState.mFollowerMap.begin();
         for (; itFol != mLeaderState.mFollowerMap.end(); ++itFol)
         {
            if (itFol->second.wsUrlBase == wsBaseUrl)
            {
               if (itFol->second.isOK)
               {
                  res.wsUrl = wsBaseUrl + cpc::string("/") + cpc::string(joinUrl.c_str());
                  foundMatch = true;
               }
               break;
            }
         }
         if (wsBaseUrl == mLocalConfig.wsUrlBase)
         {
            res.wsUrl = wsBaseUrl + cpc::string("/") + cpc::string(joinUrl.c_str());
            foundMatch = true;
         }
      }
   }
   if (!foundMatch)
   {
      webrtc::WriteLockScoped lck(*mStateLock);
      int newNode = -1;
      cpc::string newNodeUrl;
      auto itFol = mLeaderState.mFollowerMap.find(querySourceNodeId);
      if (itFol != mLeaderState.mFollowerMap.end())
      {
         newNode = itFol->first;
         newNodeUrl = itFol->second.wsUrlBase;
      }
      if (newNode == -1)
      {
         newNodeUrl = mLocalConfig.wsUrlBase;
      }

      upsertUrlMappingDb(joinUrlIn, newNodeUrl);
      mLeaderState.mRecentUrlMappings[joinUrlIn] = newNodeUrl;
      res.wsUrl = newNodeUrl + cpc::string("/") + cpc::string(joinUrl.c_str());
   }

   mInterface->fireCompletionHandlerEvent(handler, cpcFunc(LookupConferenceResultHandler::onLookupConferenceComplete), 0, res);

   return kSuccess;
}

int ConferenceRegistrarManagerImpl::lookupConferenceAsFollower(const cpc::string& joinUrlIn, int querySourceNodeId, LookupConferenceResultHandler* handler)
{
   resip::Data joinUrl = joinUrlIn.c_str();
   if (joinUrl.prefix("/"))
   {
      joinUrl = joinUrl.substr(1);
   }
   InfoLog(<< "lookupConference: joinUrl: " << joinUrl << ", querySourceNodeId: " << querySourceNodeId);

   LookupConferenceResult res;
   res.success = false;
   res.shouldRetry = false;

   std::stringstream leaderQueryContextUrl;
   {
      webrtc::ReadLockScoped lck(*mStateLock);
      leaderQueryContextUrl << mQueryContextUrl;
      if (!mQueryContextUrl.postfix("/") && !resip::Data(joinUrlIn.c_str(), joinUrlIn.size()).prefix("/"))
      {
         leaderQueryContextUrl << "/";
      }
   }
   leaderQueryContextUrl << "screenshare/";
   leaderQueryContextUrl << joinUrlIn;

   LookupCacheItem lookupInCacheResult;
   if (lookupInCache(leaderQueryContextUrl.str(), lookupInCacheResult))
   {
      res.success = true;
      res.wsUrl = lookupInCacheResult.wsUrl.c_str();
      mInterface->fireCompletionHandlerEvent(handler, cpcFunc(LookupConferenceResultHandler::onLookupConferenceComplete), 0, res);
   }
   else
   {
      std::weak_ptr<ConferenceRegistrarManagerImpl> weakThis = shared_from_this();
      std::string leaderQueryContextUrlStr = leaderQueryContextUrl.str();
      doLookupConferenceHTTP(leaderQueryContextUrlStr.c_str(), [weakThis, leaderQueryContextUrlStr, joinUrl, joinUrlIn, handler](int resultCode, const std::string& wsUrl) {
         if (auto thisPtr = weakThis.lock())
         {
            if (resultCode == 0)
            {
               webrtc::WriteLockScoped cs(*thisPtr->mFollowerCacheLock);
               LookupCacheItem lci;
               lci.wsUrl = wsUrl;
               lci.entryTime = std::chrono::system_clock::now();
               lci.path = leaderQueryContextUrlStr;
               thisPtr->mFollowerLookupCache.push_back(lci);

               auto timeNow = std::chrono::system_clock::now();
               auto itLc = thisPtr->mFollowerLookupCache.begin();
               while (itLc != thisPtr->mFollowerLookupCache.end())
               {
                  if (itLc->entryTime <= (timeNow - std::chrono::seconds(20)))
                  {
                     itLc = thisPtr->mFollowerLookupCache.erase(itLc);
                  }
                  else
                  {
                     itLc++;
                  }
               }
            }
            else
            {
               WarningLog(<< "Doing local fallback query due to doLookupConferenceHTTP(..) failure!");
               bool urlExists = false;
               cpc::string wsBaseUrl;
               thisPtr->queryJoinUrl(joinUrlIn, urlExists, wsBaseUrl);
               if (urlExists)
               {
                  LookupConferenceResult res;
                  res.success = true;
                  res.wsUrl = wsBaseUrl + cpc::string("/") + cpc::string(joinUrl.c_str());
                  thisPtr->mInterface->fireCompletionHandlerEvent(handler, cpcFunc(LookupConferenceResultHandler::onLookupConferenceComplete), 0, res);
                  return;
               }
            }
            LookupConferenceResult lookupConfRes;
            lookupConfRes.success = (resultCode == 0);
            lookupConfRes.wsUrl = wsUrl.c_str();
            InfoLog(<< "ConferenceRegistrarManagerImplImpl::doLookupConferenceHTTP(): success: " << lookupConfRes.success << ", wsUrl: " << wsUrl);
            thisPtr->mInterface->fireCompletionHandlerEvent(handler, cpcFunc(LookupConferenceResultHandler::onLookupConferenceComplete), 0, lookupConfRes);
         }
      }, NULL);
   }

   return kSuccess;
}

int ConferenceRegistrarManagerImpl::unregisterConference(const cpc::string& joinUrl)
{
   resip::Data contextStr = joinUrl.c_str();
   if (contextStr.prefix("/"))
   {
      contextStr = contextStr.substr(1);
   }
   if (contextStr.prefix("screenshare"))
   {
      contextStr = contextStr.substr(12);
   }
   if (contextStr.at(2) == '/')
   {
      contextStr = contextStr.substr(3);
   }
   InfoLog(<< "unregisterConference: " << contextStr);

   return kSuccess;
}

int ConferenceRegistrarManagerImpl::bulkUpdate(const cpc::vector<ConferenceRegistrationInfo>& conferencesToAdd, const cpc::vector<cpc::string>& conferencesToRemove, RegisterConferenceResultHandler* handler)
{
   return kSuccess;
}

int ConferenceRegistrarManagerImpl::listNodes(ListNodesHandler* handler)
{
   cpc::vector<cpc::string> retVal;
   if (mNodeId != mLeaderNodeId)
   {
      rapidjson::Document doc;
      doc.SetObject();

      doc.AddMember("nodeId", rapidjson::Value(("L" + std::to_string(mLeaderNodeId)).c_str(), doc.GetAllocator()), doc.GetAllocator());

      rapidjson::StringBuffer buffer(0, 2048);
      rapidjson::PrettyWriter<rapidjson::StringBuffer> writer (buffer);
      doc.Accept(writer);

      retVal.push_back(buffer.GetString());
   }
   else
   {
      webrtc::ReadLockScoped lck(*mStateLock);
      auto it = mLeaderState.mFollowerMap.begin();
      for (; it != mLeaderState.mFollowerMap.end(); ++it)
      {
         if (it->first != mNodeId && it->first != mLeaderNodeId)
         {
            rapidjson::Document doc;
            doc.SetObject();

            doc.AddMember("nodeId", it->first, doc.GetAllocator());
            doc.AddMember("isOK", it->second.isOK, doc.GetAllocator());
            doc.AddMember("ipAndPort", rapidjson::Value(it->second.ipAndPort.c_str(), doc.GetAllocator()), doc.GetAllocator());
            doc.AddMember("wsUrlBase", rapidjson::Value(it->second.wsUrlBase.c_str(), doc.GetAllocator()), doc.GetAllocator());

            rapidjson::StringBuffer buffer(0, 2048);
            rapidjson::PrettyWriter<rapidjson::StringBuffer> writer (buffer);
            doc.Accept(writer);

            retVal.push_back(buffer.GetString());
         }
      }
   }

   ListNodesResult res;
   res.success = true;
   res.nodes = retVal;
   mInterface->fireCompletionHandlerEvent(handler, cpcFunc(ListNodesHandler::onListNodesComplete), 0, res);
   return kSuccess;
}

void ConferenceRegistrarManagerImpl::onTimer(unsigned short timerId, void* appState)
{
   bool clocalIsLeader = localIsLeader();
   if (clocalIsLeader)
   {
      std::map<int, FollowerInfo> followerMap;
      std::map<cpc::string, cpc::string> recentUrlMappings;
      {
         webrtc::WriteLockScoped lck(*mStateLock);
         followerMap = mLeaderState.mFollowerMap;
         recentUrlMappings = mLeaderState.mRecentUrlMappings;
         mLeaderState.mRecentUrlMappings.clear();
      }
      appendRandomUrlMappingsFromDb(recentUrlMappings);
      auto it = followerMap.begin();
      for (; it != followerMap.end(); ++it)
      {
         doLeaderHello(it->first, it->second.ipAndPort, recentUrlMappings);
      }
   }
   if (!clocalIsLeader)
   {
      auto timeNow = std::chrono::system_clock::now();
      std::chrono::system_clock::time_point lastHelloTime;
      {
         {
            webrtc::ReadLockScoped rlck(*mStateLock);
            lastHelloTime = mLastHelloTime;
         }
         if (timeNow - lastHelloTime > std::chrono::seconds(15))
         {
            cpc::string joinClusterUrl;
            {
               InfoLog(<< "Join using default join cluster URL");
               joinClusterUrl = mLocalConfig.joinClusterUrl;
            }
            if (!joinClusterUrl.empty())
            {
               doJoinClusterHTTP(joinClusterUrl);
            }
         }
      }
   }
   mCheckinTimer.async_wait(this, 0, NULL);
}

class LookupConferenceResultHandlerFuncObj : public LookupConferenceResultHandler
{
public:
   LookupConferenceResultHandlerFuncObj(const cpc::string& joinUrl, const std::shared_ptr<ConferenceRegistrarManagerImpl>& impl, PhoneInterface* phone, const std::function<void(ConferenceRegistrarHandle, const LookupConferenceResult&)>& func)
      : mFunc(func), mJoinUrl(joinUrl), mImpl(impl), mPhone(phone) {}
   virtual ~LookupConferenceResultHandlerFuncObj() {}

   virtual int onLookupConferenceComplete(ConferenceRegistrarHandle registrar, const LookupConferenceResult& args) override {
      mFunc(registrar, args);
      delete this;
      return kSuccess;
   }
   virtual bool synchronous() const override {
      return true;
   }

private:
   std::function<void(ConferenceRegistrarHandle, const LookupConferenceResult&)> mFunc;
   cpc::string mJoinUrl;
   std::shared_ptr<ConferenceRegistrarManagerImpl> mImpl;
   PhoneInterface* mPhone;
};

bool ConferenceRegistrarManagerImpl::lookupInCache(const std::string& url, LookupCacheItem& result)
{
   webrtc::ReadLockScoped cs(*mFollowerCacheLock);

   auto timeNow = std::chrono::system_clock::now();
   auto it = mFollowerLookupCache.begin();
   for (; it != mFollowerLookupCache.end(); ++it)
   {
      if (it->path == url)
      {
         if (it->entryTime > (timeNow - std::chrono::seconds(20)))
         {
            result.path = url;
            result.wsUrl = it->wsUrl;
            result.entryTime = it->entryTime;
            return true;
         }
      }
   }
   return false;
}

template <typename T>
void ConferenceRegistrarManagerImpl::handleQueryContext(std::shared_ptr<typename SimpleWeb::Server<T>::Response> response, std::shared_ptr<typename SimpleWeb::Server<T>::Request> request)
{
   InfoLog(<< "handleQueryContext: " << request->path);

   if (!mInService)
   {
      WarningLog(<< "Not in service");
      response->write(SimpleWeb::StatusCode::server_error_service_unavailable);
      return;
   }

   if (!mIsPartOfCluster)
   {
      WarningLog(<< "Not part of cluster");
      response->write(SimpleWeb::StatusCode::server_error_service_unavailable);
      return;
   }

   resip::Data authToken;
   CPCAPI2::JsonApi::JsonApiServerHTTP::getAuthTokenFromRequest<T>(request, authToken);

   CPCAPI2::JsonApi::JsonApiServerSendTransportInterface* jsonApiServerSendIf = dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInterface*>(CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(mPhone));
   CPCAPI2::JsonApi::JsonApiServerInterface* jsonApiServerIf = jsonApiServerSendIf->getJsonApiServer();

   resip::Data userIdentity, deviceId;
   std::map<resip::Data, resip::Data> publicClaims;
   std::vector<resip::Data> requestedResources;
   if (jsonApiServerIf->validateAuthToken(authToken, userIdentity, deviceId, publicClaims, requestedResources) != 0)
   {
      *response << "HTTP/1.1 400 Invalid Auth Token\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   static std::string STR_SCREENSHARE_PATH = "/screenshare/";
   std::string::size_type cidPos = request->path.rfind(STR_SCREENSHARE_PATH);
   if (cidPos == std::string::npos)
   {
      *response << "HTTP/1.1 400 Missing cid\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   if (localIsLeader())
   {
      handleQueryContextAsLeader<T>(response, request);
   }
   else
   {
      handleQueryContextAsFollower<T>(response, request);
   }
}

template <typename T>
void ConferenceRegistrarManagerImpl::handleQueryContextAsLeader(std::shared_ptr<typename SimpleWeb::Server<T>::Response> response, std::shared_ptr<typename SimpleWeb::Server<T>::Request> request)
{
   std::string reqContent = request->content.string();
   int querySourceNodeId = -1;
   if (!reqContent.empty())
   {
      InfoLog(<< "handleQueryContextAsLeader - request body: " << reqContent);

      std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
      jsonRequest->Parse<0>(reqContent.c_str(), reqContent.size());

      if (jsonRequest->HasParseError())
      {
         WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
         *response << "HTTP/1.1 400 Parse Error\r\nContent-Length: 0\r\n\r\n";
         return;
      }

      if (!jsonRequest->HasMember("nodeId"))
      {
         WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
         *response << "HTTP/1.1 400 Missing nodeId\r\nContent-Length: 0\r\n\r\n";
         return;
      }

      const rapidjson::Value& nodeIdVal = (*jsonRequest)["nodeId"];
      if (!nodeIdVal.IsInt())
      {
         WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
         *response << "HTTP/1.1 400 Missing int nodeId\r\nContent-Length: 0\r\n\r\n";
         return;
      }

      querySourceNodeId = nodeIdVal.GetInt();
   }

   resip::Data contextStr = request->path.c_str(); // doesn't contain the query string
   if (contextStr.prefix("/"))
   {
      contextStr = contextStr.substr(1);
   }
   if (contextStr.prefix("screenshare"))
   {
      contextStr = contextStr.substr(12);
   }
   if (contextStr.at(2) == '/')
   {
      contextStr = contextStr.substr(3);
   }
   auto dashPos = contextStr.find("/conf/");
   if (dashPos != resip::Data::npos)
   {
      contextStr = contextStr.substr(0, dashPos);
   }
   if (contextStr.postfix("/"))
   {
      contextStr = contextStr.substr(0, contextStr.size() - 1);
   }

   LookupConferenceResultHandlerFuncObj* resultHandler = new LookupConferenceResultHandlerFuncObj(contextStr.c_str(), shared_from_this(), mPhone, [&, response, request, contextStr](ConferenceRegistrarHandle registrar, const LookupConferenceResult& lookupResult) {
      rapidjson::Document respJson;
      respJson.SetObject();

      std::stringstream ssUrl;
      if (lookupResult.wsUrl.empty() && lookupResult.shouldRetry)
      {
         DebugLog(<< "Raft isn't ready; send 408");
         SimpleWeb::CaseInsensitiveMultimap respHeaders;
         respHeaders.emplace("Access-Control-Allow-Origin", "*");
         respHeaders.emplace("Access-Control-Allow-Headers", "Authorization");
         respHeaders.emplace("Cache-Control", "no-cache, no-store, must-revalidate");
         respHeaders.emplace("Retry-After", "10");
         response->write(SimpleWeb::StatusCode::client_error_request_timeout, "Lookup Failed Please Retry", respHeaders);
      }
      else
      {
         if (lookupResult.wsUrl.empty())
         {
            assert(0);
         }
         else
         {
            ssUrl << lookupResult.wsUrl;
         }

         InfoLog(<< "handleUsersRequest returning websocketUrl: " << ssUrl.str());

         rapidjson::Value wsUrlVal(ssUrl.str().c_str(), respJson.GetAllocator());
         respJson.AddMember("websocketUrl", wsUrlVal, respJson.GetAllocator());

         rapidjson::StringBuffer buffer(0, 1024);
         rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
         respJson.Accept(writer);

         SimpleWeb::CaseInsensitiveMultimap respHeaders;
         respHeaders.emplace("Access-Control-Allow-Origin", "*");
         respHeaders.emplace("Access-Control-Allow-Headers", "Authorization");
         respHeaders.emplace("Cache-Control", "no-cache, no-store, must-revalidate");
         response->write(std::string(buffer.GetString(), buffer.GetSize()), respHeaders);
      }
   });
   mPhone->getSdkModuleThread().post(resip::resip_bind(&ConferenceRegistrarManagerImpl::lookupConferenceAsLeader, this, cpc::string(contextStr.c_str(), contextStr.size()), querySourceNodeId, resultHandler));
}

template <typename T>
void ConferenceRegistrarManagerImpl::handleQueryContextAsFollower(std::shared_ptr<typename SimpleWeb::Server<T>::Response> response, std::shared_ptr<typename SimpleWeb::Server<T>::Request> request)
{
   std::stringstream leaderQueryContextUrl;
   {
      webrtc::ReadLockScoped lck(*mStateLock);
      leaderQueryContextUrl << mQueryContextUrl;
   }

   leaderQueryContextUrl << request->path;
   std::string leaderQueryContextUrlStr = leaderQueryContextUrl.str();

   LookupCacheItem lookupInCacheResult;
   if (lookupInCache(leaderQueryContextUrlStr, lookupInCacheResult))
   {
      rapidjson::Document respJson;
      respJson.SetObject();

      InfoLog(<< "handleQueryContext returning cached websocketUrl: " << lookupInCacheResult.wsUrl);

      rapidjson::Value wsUrlVal(lookupInCacheResult.wsUrl.c_str(), respJson.GetAllocator());
      respJson.AddMember("websocketUrl", wsUrlVal, respJson.GetAllocator());

      rapidjson::StringBuffer buffer(0, 1024);
      rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
      respJson.Accept(writer);

      SimpleWeb::CaseInsensitiveMultimap respHeaders;
      respHeaders.emplace("Access-Control-Allow-Origin", "*");
      respHeaders.emplace("Access-Control-Allow-Headers", "Authorization");
      respHeaders.emplace("Cache-Control", "no-cache, no-store, must-revalidate");
      response->write(std::string(buffer.GetString(), buffer.GetSize()), respHeaders);
   }
   else
   {
      DebugLog(<< "handleQueryContext doing remote lookup: " << leaderQueryContextUrlStr);
      std::weak_ptr<ConferenceRegistrarManagerImpl> weakThis(shared_from_this());

      doLookupConferenceHTTP(leaderQueryContextUrlStr.c_str(), [weakThis, leaderQueryContextUrlStr, response, request](int resultCode, const std::string& wsUrl) {
         if (resultCode == 0)
         {
            LookupCacheItem lci;
            lci.path = leaderQueryContextUrlStr;
            lci.wsUrl = wsUrl;
            lci.entryTime = std::chrono::system_clock::now();

            if (auto thisPtr = weakThis.lock())
            {
               webrtc::WriteLockScoped cs(*thisPtr->mFollowerCacheLock);
               thisPtr->mFollowerLookupCache.push_back(lci);

               auto timeNow = std::chrono::system_clock::now();
               auto itLc = thisPtr->mFollowerLookupCache.begin();
               while (itLc != thisPtr->mFollowerLookupCache.end())
               {
                  if (itLc->entryTime <= (timeNow - std::chrono::seconds(20)))
                  {
                     itLc = thisPtr->mFollowerLookupCache.erase(itLc);
                  }
                  else
                  {
                     itLc++;
                  }
               }
            }

            rapidjson::Document respJson;
            respJson.SetObject();

            InfoLog(<< "handleQueryContext returning websocketUrl: " << lci.wsUrl);

            rapidjson::Value wsUrlVal(lci.wsUrl.c_str(), respJson.GetAllocator());
            respJson.AddMember("websocketUrl", wsUrlVal, respJson.GetAllocator());

            rapidjson::StringBuffer buffer(0, 1024);
            rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
            respJson.Accept(writer);

            SimpleWeb::CaseInsensitiveMultimap respHeaders;
            respHeaders.emplace("Access-Control-Allow-Origin", "*");
            respHeaders.emplace("Access-Control-Allow-Headers", "Authorization");
            respHeaders.emplace("Cache-Control", "no-cache, no-store, must-revalidate");
            response->write(std::string(buffer.GetString(), buffer.GetSize()), respHeaders);
         }
         else
         {
            WarningLog(<< "Doing local fallback in handleQueryContextAsFollower due to doLookupConferenceHTTP(..) failure!");
            if (std::shared_ptr<ConferenceRegistrarManagerImpl> thisPtr = weakThis.lock())
            {
               thisPtr->handleQueryContextAsLeader<T>(response, request);
            }
         }
      }, NULL);
   }
}

void ConferenceRegistrarManagerImpl::addRegisterContextHandler()
{
   std::stringstream usersRegex; // ^\/confbridge\/jgeras[\/]?$
   usersRegex << "^/statusApi/registerContext[\/]?$";
   usersRegex.flush();

   CPCAPI2::JsonApi::JsonApiServerSendTransportInterface* jsonApiServerSendIf = dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInterface*>(CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(mPhone));
   CPCAPI2::JsonApi::JsonApiServerInterface* jsonApiServerIf = jsonApiServerSendIf->getJsonApiServer();
   std::shared_ptr<CPCAPI2::JsonApi::JsonApiServerHTTP> jsonApiServerHttp = jsonApiServerIf->getJsonApiHttpTransport();
   if (jsonApiServerHttp->useHttps())
   {
      jsonApiServerHttp->addHttpsResourceHandler("POST", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>& request) {
         try {
            handleRegisterContextRequest<SimpleWeb::HTTPS>(response, request);
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
      jsonApiServerHttp->addHttpsResourceHandler("OPTIONS", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>& request) {
         try {
            *response << "HTTP/1.1 200 OK\r\n"
               << "Access-Control-Allow-Origin: *\r\n"
               << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
      jsonApiServerHttp->addHttpsResourceHandler("GET", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>& request) {
         try {
            *response << "HTTP/1.1 200 OK\r\n"
               << "Access-Control-Allow-Origin: *\r\n"
               << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
   }
   else
   {
      jsonApiServerHttp->addHttpResourceHandler("POST", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Request>& request) {
         try {
            InfoLog(<< "Handling conference join request...");
            handleRegisterContextRequest<SimpleWeb::HTTP>(response, request);
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
      jsonApiServerHttp->addHttpResourceHandler("OPTIONS", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Request>& request) {
         try {
            *response << "HTTP/1.1 200 OK\r\n"
               << "Access-Control-Allow-Origin: *\r\n"
               << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
   }
}

void ConferenceRegistrarManagerImpl::addQueryContextHandler()
{
   std::stringstream usersRegex; // ^\/confbridge\/jgeras[\/]?$
   usersRegex << "^/screenshare/([^/]+)(/conf/([^/]+)(/)?)?$";
   usersRegex.flush();

   CPCAPI2::JsonApi::JsonApiServerSendTransportInterface* jsonApiServerSendIf = dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInterface*>(CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(mPhone));
   CPCAPI2::JsonApi::JsonApiServerInterface* jsonApiServerIf = jsonApiServerSendIf->getJsonApiServer();
   std::shared_ptr<CPCAPI2::JsonApi::JsonApiServerHTTP> jsonApiServerHttp = jsonApiServerIf->getJsonApiHttpTransport();
   if (jsonApiServerHttp->useHttps())
   {
      jsonApiServerHttp->addHttpsResourceHandler("POST", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>& request) {
         try {
            handleQueryContext<SimpleWeb::HTTPS>(response, request);
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
      jsonApiServerHttp->addHttpsResourceHandler("OPTIONS", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>& request) {
         try {
            *response << "HTTP/1.1 200 OK\r\n"
               << "Access-Control-Allow-Origin: *\r\n"
               << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
      jsonApiServerHttp->addHttpsResourceHandler("GET", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>& request) {
         try {
            *response << "HTTP/1.1 200 OK\r\n"
               << "Access-Control-Allow-Origin: *\r\n"
               << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
   }
   else
   {
      jsonApiServerHttp->addHttpResourceHandler("POST", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Request>& request) {
         try {
            InfoLog(<< "Handling conference join request...");
            handleQueryContext<SimpleWeb::HTTP>(response, request);
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
      jsonApiServerHttp->addHttpResourceHandler("OPTIONS", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Request>& request) {
         try {
            *response << "HTTP/1.1 200 OK\r\n"
               << "Access-Control-Allow-Origin: *\r\n"
               << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
   }
}

void ConferenceRegistrarManagerImpl::addJoinClusterHandler()
{
   std::stringstream usersRegex; // ^\/confbridge\/jgeras[\/]?$
   usersRegex << "^/statusApi/joinCluster[\/]?$";
   usersRegex.flush();

   CPCAPI2::JsonApi::JsonApiServerSendTransportInterface* jsonApiServerSendIf = dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInterface*>(CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(mPhone));
   CPCAPI2::JsonApi::JsonApiServerInterface* jsonApiServerIf = jsonApiServerSendIf->getJsonApiServer();
   std::shared_ptr<CPCAPI2::JsonApi::JsonApiServerHTTP> jsonApiServerHttp = jsonApiServerIf->getJsonApiHttpTransport();
   if (jsonApiServerHttp->useHttps())
   {
      jsonApiServerHttp->addHttpsResourceHandler("POST", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>& request) {
         try {
            handleJoinClusterRequest<SimpleWeb::HTTPS>(response, request);
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
      jsonApiServerHttp->addHttpsResourceHandler("OPTIONS", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>& request) {
         try {
            *response << "HTTP/1.1 200 OK\r\n"
               << "Access-Control-Allow-Origin: *\r\n"
               << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
      jsonApiServerHttp->addHttpsResourceHandler("GET", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>& request) {
         try {
            *response << "HTTP/1.1 200 OK\r\n"
               << "Access-Control-Allow-Origin: *\r\n"
               << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
   }
   else
   {
      jsonApiServerHttp->addHttpResourceHandler("POST", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Request>& request) {
         try {
            InfoLog(<< "Handling conference join request...");
            handleJoinClusterRequest<SimpleWeb::HTTP>(response, request);
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
      jsonApiServerHttp->addHttpResourceHandler("OPTIONS", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Request>& request) {
         try {
            *response << "HTTP/1.1 200 OK\r\n"
               << "Access-Control-Allow-Origin: *\r\n"
               << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
   }
}

void ConferenceRegistrarManagerImpl::addLeaderHelloHandler()
{
   std::stringstream usersRegex; // ^\/confbridge\/jgeras[\/]?$
   usersRegex << "^/statusApi/leaderHello[\/]?$";
   usersRegex.flush();

   CPCAPI2::JsonApi::JsonApiServerSendTransportInterface* jsonApiServerSendIf = dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInterface*>(CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(mPhone));
   CPCAPI2::JsonApi::JsonApiServerInterface* jsonApiServerIf = jsonApiServerSendIf->getJsonApiServer();
   std::shared_ptr<CPCAPI2::JsonApi::JsonApiServerHTTP> jsonApiServerHttp = jsonApiServerIf->getJsonApiHttpTransport();
   if (jsonApiServerHttp->useHttps())
   {
      jsonApiServerHttp->addHttpsResourceHandler("POST", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>& request) {
         try {
            handleLeaderHelloRequest<SimpleWeb::HTTPS>(response, request);
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
      jsonApiServerHttp->addHttpsResourceHandler("OPTIONS", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>& request) {
         try {
            *response << "HTTP/1.1 200 OK\r\n"
               << "Access-Control-Allow-Origin: *\r\n"
               << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
      jsonApiServerHttp->addHttpsResourceHandler("GET", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>& request) {
         try {
            *response << "HTTP/1.1 200 OK\r\n"
               << "Access-Control-Allow-Origin: *\r\n"
               << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
   }
   else
   {
      jsonApiServerHttp->addHttpResourceHandler("POST", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Request>& request) {
         try {
            InfoLog(<< "Handling conference join request...");
            handleLeaderHelloRequest<SimpleWeb::HTTP>(response, request);
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
      jsonApiServerHttp->addHttpResourceHandler("OPTIONS", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Request>& request) {
         try {
            *response << "HTTP/1.1 200 OK\r\n"
               << "Access-Control-Allow-Origin: *\r\n"
               << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
         }
         catch (const std::exception& e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strnlen(e.what(), 200) << "\r\n\r\n"
               << e.what();
         }
      });
   }
}

class JoinClusterRequest
{
public:
   JoinClusterRequest(resip::MultiReactor& requestServiceThread, resip::MultiReactor& callbackThread, const resip::Data& joinUrl, int nodeId, const resip::Data& leaderHelloUrl, const resip::Data& wsUrlBase, const resip::Data& authToken, bool ignoreCertVerification, SslCipherOptions tlsSettings, const std::function<void(int, const std::string)>& resultCb)
      : mReactor(requestServiceThread),
      mCallbackThread(callbackThread),
      mJoinUrl(joinUrl),
      mNodeId(nodeId),
      mLeaderHelloUrl(leaderHelloUrl),
      mWsUrlBase(wsUrlBase),
      mAuthToken(authToken),
      mIgnoreCertVerification(ignoreCertVerification),
      mTlsSettings(tlsSettings),
      mResultCb(resultCb)
   {
   }
   ~JoinClusterRequest()
   {
   }

   void start()
   {
      mReactor.post(resip::resip_bind(&JoinClusterRequest::doJoinClusterFlow, this));
   }

private:
   void doJoinClusterFlow()
   {
      std::unique_ptr<JoinClusterRequest> thisDeleter(this);
      std::string websocketUrl;
      std::string orchServerUrl(mJoinUrl.c_str(), mJoinUrl.size());

      if (orchServerUrl.empty())
      {
         postFailureCallback();
         return;
      }

      try
      {
         int response = 0;
         std::string responseBodyStr;
         if (mRetries-- > 0)
         {
            websocketpp::uri orchUri(orchServerUrl);
            SimpleWeb::Client<SimpleWeb::HTTPS> swClient(orchUri.get_host_port(), false);

            std::stringstream reqBody;
            reqBody << "{";
            reqBody << "   \"nodeId\": " << mNodeId << ",";
            reqBody << "   \"ipAndPort\": \"" << mLeaderHelloUrl << "\",";
            reqBody << "   \"wsUrlBase\": \"" << mWsUrlBase << "\"";
            reqBody << "}";
            std::string reqBodyStr = reqBody.str();
            InfoLog(<< "Sending joinCluster with body: " << reqBodyStr);

            SimpleWeb::CaseInsensitiveMultimap reqHeaders;
            reqHeaders.emplace("Authorization", std::string("bearer ") + mAuthToken.c_str());
            auto swResp = swClient.request("POST", orchUri.get_resource(), reqBodyStr, reqHeaders);
            resip::Data respCodeStr = swResp->status_code.c_str();
            response = respCodeStr.convertInt();
            responseBodyStr = swResp->content.string();

            if (response == 408 || response == 502)
            {
               thisDeleter.release();
               mReactor.postMS(resip::resip_bind(&JoinClusterRequest::doJoinClusterFlow, this), 1000);
               return;
            }
         }

         StackLog(<< "doJoinClusterFlow(): server: " << orchServerUrl << " response: " << response << " - " << responseBodyStr);
         if (response == 200)
         {
            resip::ReadCallbackBase* cb = resip::resip_static_bind(mResultCb, 0, websocketUrl);
            mCallbackThread.post(cb);
         }
         else
         {
            DebugLog(<< "doJoinClusterFlow(): failure response: " << response);
            postFailureCallback((response == 508 ? -2 : -1));
         }
      }
      catch (const SimpleWeb::system_error& e)
      {
         DebugLog(<< "doJoinClusterFlow(): runtime error: " << e.what());
         postFailureCallback();
      }
   }

   void postFailureCallback(int resultCode=-1)
   {
      resip::ReadCallbackBase* cb = resip::resip_static_bind(mResultCb, resultCode, "");
      mCallbackThread.post(cb);
   }

private:
   resip::MultiReactor& mReactor;
   resip::MultiReactor& mCallbackThread;
   resip::Data mJoinUrl;
   resip::Data mAuthToken;
   bool mIgnoreCertVerification;
   SslCipherOptions mTlsSettings;
   std::function<void(int, const std::string&)> mResultCb;
   int mNodeId;
   resip::Data mLeaderHelloUrl;
   resip::Data mWsUrlBase;
   int mRetries = 10;
};

int ConferenceRegistrarManagerImpl::doAuth(const std::function<void(int, const resip::Data&, const cpc::string&)>& resultCb)
{
   if (!mAuthToken.empty())
   {
      resip::Data userIdentity, deviceId;
      std::map<resip::Data, resip::Data> publicClaims;
      std::vector<resip::Data> requestedResources;
      CPCAPI2::JsonApi::JsonApiServerSendTransportInterface* jsonApiServerSendIf = dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInterface*>(CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(mPhone));
      CPCAPI2::JsonApi::JsonApiServerInterface* jsonApiServerIf = jsonApiServerSendIf->getJsonApiServer();
      if (jsonApiServerIf->validateAuthToken(mAuthToken, userIdentity, deviceId, publicClaims, requestedResources) == 0)
      {
         resultCb(0, mAuthToken, "");
         return kSuccess;
      }
      else
      {
         mAuthToken.clear();
      }
   }

   resip::Data loginUrl(mLocalConfig.authServiceUrl.c_str(), mLocalConfig.authServiceUrl.size());
   resip::Data resolvedUsername = "cpcapi2node";
   resip::Data passwordHash;
   {
      resip::Data password = "cpcapi2nodepass";
      resip::DataStream passwordHashDs(passwordHash);
      unsigned char hash[SHA256_DIGEST_LENGTH];
      SHA256(reinterpret_cast<const unsigned char*>(password.c_str()), password.size(), hash);
      for (int i = 0; i < SHA256_DIGEST_LENGTH; i++)
      {
         passwordHashDs << std::hex << std::setw(2) << std::setfill('0') << (int)hash[i];
      }
   }

   std::vector<resip::Data> requestedResources;

   resip::Data apiKey(mLocalConfig.authServiceApiKey.c_str(), mLocalConfig.authServiceApiKey.size());

   std::weak_ptr<ConferenceRegistrarManagerImpl> weakThis(shared_from_this());
   CPCAPI2::CloudConnector::CloudServerConnection::doAuthFlowWithSimpleWebClient(mWebRequestThread,
      mWebRequestThread,
      loginUrl,
      resolvedUsername,
      passwordHash,
      requestedResources,
      apiKey,
      true,
      mPhone->getSslCipherOptions(),
      mLocalConfig.authenticationTimeoutSeconds,
      [weakThis, resultCb, resolvedUsername](int resultCode, const resip::Data& authToken, const cpc::string& details)
   {
      if (std::shared_ptr<ConferenceRegistrarManagerImpl> thisPtr = weakThis.lock())
      {
         if (resultCode == 0)
         {
            thisPtr->mAuthToken = authToken;
            resultCb(resultCode, authToken, details);
         }
         else
         {
            ErrLog(<< "Auth failure: " << details);
            thisPtr->mIsPartOfCluster = false;
         }
      }
   });
   return kSuccess;
}

int ConferenceRegistrarManagerImpl::doJoinClusterHTTP(const cpc::string& joinClusterUrl)
{
   DebugLog(<< "ConferenceRegistrarManagerImplImpl::doJoinClusterHTTP(): " << joinClusterUrl);

   if (joinClusterUrl.empty())
   {
      return kSuccess;
   }

   std::weak_ptr<ConferenceRegistrarManagerImpl> weakThis(shared_from_this());
   int nodeId = mNodeId;
   resip::Data wsUrlBase(mLocalConfig.wsUrlBase.c_str(), mLocalConfig.wsUrlBase.size());
   doAuth([weakThis, nodeId, joinClusterUrl, wsUrlBase](int resultCode, const resip::Data& authToken, const cpc::string& details)
   {
      if (std::shared_ptr<ConferenceRegistrarManagerImpl> thisPtr = weakThis.lock())
      {
         if (resultCode == 0)
         {
            std::stringstream leaderHelloUrl;
            leaderHelloUrl << "https://";
            leaderHelloUrl << thisPtr->mLocalConfig.conferenceRegistrarServiceIp;
            leaderHelloUrl << "/statusApi/leaderHello";
            JoinClusterRequest* joinClusterReq = new JoinClusterRequest(thisPtr->mWebRequestThread,
               thisPtr->mPhone->getSdkModuleThread(),
               joinClusterUrl.c_str(),
               nodeId,
               leaderHelloUrl.str().c_str(),
               wsUrlBase,
               authToken,
               true,
               thisPtr->mPhone->getSslCipherOptions(),
               [weakThis, authToken, nodeId](int resultCode, const std::string& wsUrl)
            {
               if (std::shared_ptr<ConferenceRegistrarManagerImpl> thisPtr = weakThis.lock())
               {
                  if (resultCode == -2) // http 508 response
                  {
                     {
                        webrtc::WriteLockScoped lck(*thisPtr->mStateLock);
                        thisPtr->mLeaderState.mLocalIsLeader = true;
                        thisPtr->mLeaderNodeId = thisPtr->mNodeId;
                        thisPtr->mIsPartOfCluster = true;
                     }
                     ConferenceRegistrarStartupResult startupResult;
                     startupResult.success = true;
                     startupResult.localIsLeader = true;
                     thisPtr->mInterface->fireEvent(cpcFunc(ConferenceRegistrarHandler::onStartupComplete), 0, startupResult);
                     InfoLog(<< "ConferenceRegistrarManagerImpl::doJoinClusterHTTP(..) - startup complete; nodeId = " << nodeId);
                  }
                  else if (resultCode == -1)
                  {
                     ConferenceRegistrarStartupResult startupResult;
                     startupResult.success = false;
                     startupResult.localIsLeader = false;
                     thisPtr->mInterface->fireEvent(cpcFunc(ConferenceRegistrarHandler::onStartupComplete), 0, startupResult);
                     InfoLog(<< "ConferenceRegistrarManagerImpl::doJoinClusterHTTP(..) - startup failure; nodeId = " << nodeId);
                  }
                  else
                  {
                     webrtc::WriteLockScoped lck(*thisPtr->mStateLock);
                     thisPtr->mLastHelloTime = std::chrono::system_clock::now();
                     InfoLog(<< "ConferenceRegistrarManagerImpl::doJoinClusterHTTP(..) success, waiting for leader hello...");
                  }
               }
            });
            joinClusterReq->start();
         }
         else
         {
            ErrLog(<< "Auth failure: " << details);
         }
      }
   });

   return kSuccess;
}

class RegisterConferenceRequest
{
public:
   RegisterConferenceRequest(resip::MultiReactor& requestServiceThread, resip::MultiReactor& callbackThread, const resip::Data& registerUrl, int localNodeId, const resip::Data& joinUrl, int nodeId, bool overrideExisting, const resip::Data& authToken, bool ignoreCertVerification, SslCipherOptions tlsSettings, const std::function<void(int, const std::string)>& resultCb)
      : mReactor(requestServiceThread),
      mCallbackThread(callbackThread),
      mRegisterUrl(registerUrl),
      mLocalNodeId(localNodeId),
      mJoinUrl(joinUrl),
      mNodeId(nodeId),
      mOverrideExisting(overrideExisting),
      mAuthToken(authToken),
      mIgnoreCertVerification(ignoreCertVerification),
      mTlsSettings(tlsSettings),
      mResultCb(resultCb)
   {
   }
   ~RegisterConferenceRequest()
   {
   }

   void start()
   {
      mReactor.post(resip::resip_bind(&RegisterConferenceRequest::doRegisterConferenceFlow, this));
   }

private:
   void doRegisterConferenceFlow()
   {
      std::unique_ptr<RegisterConferenceRequest> thisDeleter(this);
      std::string websocketUrl;
      std::string orchServerUrl(mRegisterUrl.c_str(), mRegisterUrl.size());

      //if (orchServerUrl.empty())
      {
         postFailureCallback();
         return;
      }
#if 0
      try
      {
         int response = 0;
         std::string responseBodyStr;
         for (int retries = 0; retries < 32; retries++)
         {
            CurlPPHelper helper;
            curlpp::Easy joinClusterRequest;
            std::stringstream reqBody;
            reqBody << "{";
            reqBody << "   \"nodeId\": " << mNodeId << ",";
            reqBody << "   \"joinUrl\": \"" << mJoinUrl << "\",";
            reqBody << "   \"localNodeId\": \"" << mLocalNodeId << "\",";
            reqBody << "   \"overrideExisting\": " << (mOverrideExisting ? "true" : "false");
            reqBody << "}";
            std::string reqBodyStr = reqBody.str();

            helper.setDefaultOptions(joinClusterRequest, orchServerUrl, "POST", reqBodyStr.size());
            joinClusterRequest.setOpt(new curlpp::options::PostFields(reqBodyStr));
            helper.setTimeoutOption(joinClusterRequest, ORCH_TIMEOUT_SECONDS);

            std::list<std::string> header;
            header.push_back(std::string("Authorization: bearer ") + mAuthToken.c_str());
            joinClusterRequest.setOpt(new curlpp::options::HttpHeader(header));

            int acceptableFailures(0);
            if (mIgnoreCertVerification)
               acceptableFailures = (int)CurlPPSSL::E_CERT_WHATEVER_ERROR;

            CurlPPSSL cssl(mTlsSettings, acceptableFailures);
            joinClusterRequest.setOpt(new curlpp::options::SslCtxFunction(cssl));

            std::stringstream responseBody;
            joinClusterRequest.setOpt(new curlpp::options::WriteStream(&responseBody));
            joinClusterRequest.perform();

            response = curlpp::infos::ResponseCode::get(joinClusterRequest);

            if (response != 408 && response != 502)
            {
               responseBodyStr = responseBody.str();
               break;
            }

            std::this_thread::sleep_for(std::chrono::seconds(1));
         }

         StackLog(<< "doRegisterConferenceFlow(): orchServer: " << orchServerUrl << " response: " << responseBodyStr);
         if (response == 200)
         {
            resip::StdFunctionReadCallback2<std::function<void(int, const std::string&)>, int, std::string >* cb = new resip::StdFunctionReadCallback2<std::function<void(int, const std::string&)>, int, std::string >(mResultCb, 0, websocketUrl);
            mCallbackThread.post(cb);
         }
         else
         {
            DebugLog(<< "doRegisterConferenceFlow(): failure response: " << response);
            postFailureCallback();
         }
      }
      catch (curlpp::RuntimeError& e)
      {
         DebugLog(<< "doRegisterConferenceFlow(): runtime error: " << e.what());
         postFailureCallback();
      }
#endif
   }

   void postFailureCallback()
   {
      resip::ReadCallbackBase* cb = resip::resip_static_bind(mResultCb, -1, "");
      mCallbackThread.post(cb);
   }

private:
   resip::MultiReactor& mReactor;
   resip::MultiReactor& mCallbackThread;
   resip::Data mRegisterUrl;
   resip::Data mJoinUrl;
   resip::Data mAuthToken;
   bool mIgnoreCertVerification;
   SslCipherOptions mTlsSettings;
   std::function<void(int, const std::string&)> mResultCb;
   int mNodeId;
   int mLocalNodeId;
   bool mOverrideExisting;
};

int ConferenceRegistrarManagerImpl::doRegisterConferenceHTTP(const cpc::string& joinUrl, int nodeId, bool overrideExisting, RegisterConferenceResultHandler* handler)
{
   DebugLog(<< "ConferenceRegistrarManagerImplImpl::doRegisterConferenceHTTP()");

   std::weak_ptr<ConferenceRegistrarManagerImpl> weakThis(shared_from_this());
   int localNodeId = mNodeId;
   doAuth([weakThis, nodeId, joinUrl, localNodeId, overrideExisting, handler](int resultCode, const resip::Data& authToken, const cpc::string& details)
   {
      if (std::shared_ptr<ConferenceRegistrarManagerImpl> thisPtr = weakThis.lock())
      {
         if (resultCode == 0)
         {
            std::stringstream registerUrl;
            {
               webrtc::ReadLockScoped lck(*thisPtr->mStateLock);
               registerUrl << thisPtr->mQueryContextUrl;
            }
            registerUrl << "/statusApi/registerContext";
            RegisterConferenceRequest* registerConfReq = new RegisterConferenceRequest(thisPtr->mWebRequestThread,
               thisPtr->mPhone->getSdkModuleThread(),
               registerUrl.str().c_str(),
               localNodeId,
               joinUrl.c_str(),
               nodeId,
               overrideExisting,
               authToken,
               true,
               thisPtr->mPhone->getSslCipherOptions(),
               [weakThis, authToken, handler](int resultCode, const std::string& wsUrl)
            {
               if (std::shared_ptr<ConferenceRegistrarManagerImpl> thisPtr = weakThis.lock())
               {
                  RegisterConferenceResult regConfRes;
                  regConfRes.success = (resultCode == 0);
                  thisPtr->mInterface->fireCompletionHandlerEvent(handler, cpcFunc(RegisterConferenceResultHandler::onRegisterConferenceComplete), 0, regConfRes);
               }
            });
            registerConfReq->start();
         }
      }
   });

   return kSuccess;
}

class LookupConferenceRequest
{
public:
   LookupConferenceRequest(resip::MultiReactor& requestServiceThread, resip::MultiReactor& callbackThread, const resip::Data& joinUrl, int nodeId, const resip::Data& authToken, bool ignoreCertVerification, SslCipherOptions tlsSettings, const std::function<void(int, const std::string)>& resultCb)
      : mReactor(requestServiceThread),
      mCallbackThread(callbackThread),
      mJoinUrl(joinUrl),
      mNodeId(nodeId),
      mAuthToken(authToken),
      mIgnoreCertVerification(ignoreCertVerification),
      mTlsSettings(tlsSettings),
      mResultCb(resultCb)
   {
   }
   ~LookupConferenceRequest()
   {
   }

   void start()
   {
      mReactor.post(resip::resip_bind(&LookupConferenceRequest::doLookupConferenceFlow, this));
   }

   void startSync()
   {
      mSyncCallback = true;
      mReactor.post(resip::resip_bind(&LookupConferenceRequest::doLookupConferenceFlow, this));
   }

private:
   void doLookupConferenceFlow()
   {
      std::unique_ptr<LookupConferenceRequest> thisDeleter(this);
      std::string websocketUrl;
      std::string orchServerUrl(mJoinUrl.c_str(), mJoinUrl.size());

      if (orchServerUrl.empty())
      {
         doCallback(-1, "");
         return;
      }

      try
      {
         int response = 0;
         std::string responseBodyStr;
         if (mRetries-- > 0)
         {
            websocketpp::uri orchUri(orchServerUrl);
            SimpleWeb::Client<SimpleWeb::HTTPS> swClient(orchUri.get_host_port(), false);
            swClient.config.timeout = 5;

            std::stringstream reqBody;
            reqBody << "{";
            reqBody << "   \"nodeId\": " << mNodeId;
            reqBody << "}";
            std::string reqBodyStr = reqBody.str();

            SimpleWeb::CaseInsensitiveMultimap reqHeaders;
            reqHeaders.emplace("Authorization", std::string("bearer ") + mAuthToken.c_str());

            auto swResp = swClient.request("POST", orchUri.get_resource(), reqBodyStr, reqHeaders);
            resip::Data respCodeStr = swResp->status_code.c_str();
            response = respCodeStr.convertInt();
            responseBodyStr = swResp->content.string();

            if (response == 408 || response == 502)
            {
               thisDeleter.release();
               mReactor.postMS(resip::resip_bind(&LookupConferenceRequest::doLookupConferenceFlow, this), 1000);
               return;
            }
         }

         StackLog(<< "doLookupConferenceFlow(): orchServer: " << orchServerUrl << " response: " << responseBodyStr);
         if (response == 200)
         {
            std::unique_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
            jsonRequest->Parse<0>(responseBodyStr.c_str());
            if (jsonRequest->HasParseError())
            {
               DebugLog(<< "ConferenceConnectorImpl::doLookupConferenceFlow(): response parse error");
               doCallback(-1, "");
               return;
            }

            if (!jsonRequest->HasMember("websocketUrl"))
            {
               DebugLog(<< "ConferenceConnectorImpl::doLookupConferenceFlow(): response missing websocketUrl");
               doCallback(-1, "");
               return;
            }

            const rapidjson::Value& websocketUrlVal = (*jsonRequest)["websocketUrl"];
            if (!websocketUrlVal.IsString())
            {
               DebugLog(<< "ConferenceConnectorImpl::doLookupConferenceFlow(): response websocketUrl type mismatch");
               doCallback(-1, "");
               return;
            }

            websocketUrl = websocketUrlVal.GetString();
            assert(!websocketUrl.empty());

            doCallback(0, websocketUrl);
         }
         else
         {
            DebugLog(<< "doLookupConferenceFlow(): failure response: " << response);
            doCallback(-1, "");
         }
      }
      catch (const SimpleWeb::system_error& e)
      {
         DebugLog(<< "doLookupConferenceFlow(): runtime error: " << e.what());
         doCallback(-1, "");
      }
   }

   void doCallback(int retVal, const std::string& details)
   {
      if (mSyncCallback)
      {
         mResultCb(retVal, details);
      }
      else
      {
         resip::ReadCallbackBase* cb = resip::resip_static_bind(mResultCb, retVal, details);
         mCallbackThread.post(cb);
      }
   }

private:
   resip::MultiReactor& mReactor;
   resip::MultiReactor& mCallbackThread;
   resip::Data mJoinUrl;
   resip::Data mAuthToken;
   bool mIgnoreCertVerification;
   SslCipherOptions mTlsSettings;
   std::function<void(int, const std::string&)> mResultCb;
   int mNodeId;
   int mRetries = 2;
   bool mSyncCallback = false;
};

int ConferenceRegistrarManagerImpl::doLookupConferenceHTTP(const cpc::string& joinUrl, const std::function<void(int, const std::string)>& resultCb, LookupConferenceResultHandler* handler)
{
   InfoLog(<< "ConferenceRegistrarManagerImplImpl::doLookupConferenceHTTP(): joinUrl: " << joinUrl);

   std::weak_ptr<ConferenceRegistrarManagerImpl> weakThis(shared_from_this());
   int nodeId = mNodeId;
   doAuth([weakThis, nodeId, joinUrl, resultCb, handler](int resultCode, const resip::Data& authToken, const cpc::string& details)
   {
      if (std::shared_ptr<ConferenceRegistrarManagerImpl> thisPtr = weakThis.lock())
      {
         if (resultCode == 0)
         {
            resip::MultiReactor& reqThread = (std::abs(resip::Random::getCryptoRandom()) % 2 == 0 ? thisPtr->mLookupWebRequestThread : thisPtr->mWebRequestThread);
            LookupConferenceRequest* registerConfReq = new LookupConferenceRequest(reqThread,
               thisPtr->mPhone->getSdkModuleThread(),
               joinUrl.c_str(),
               nodeId,
               authToken,
               true,
               thisPtr->mPhone->getSslCipherOptions(),
               [weakThis, authToken, resultCb, handler](int resultCode, const std::string& wsUrl)
            {
               if (std::shared_ptr<ConferenceRegistrarManagerImpl> thisPtr = weakThis.lock())
               {
                  if (resultCb)
                  {
                     resultCb(resultCode, wsUrl);
                  }
               }
            });
            registerConfReq->startSync();
         }
      }
   });

   return kSuccess;
}


class LeaderHelloRequest
{
public:
   LeaderHelloRequest(resip::MultiReactor& requestServiceThread, resip::MultiReactor& callbackThread, const resip::Data& leaderHelloUrl, int nodeId, const resip::Data& queryContextUrl, const resip::Data& joinClusterUrl, const std::vector<std::pair<resip::Data, resip::Data> >& urlMappings, const resip::Data& authToken, bool ignoreCertVerification, SslCipherOptions tlsSettings, const std::function<void(int, const std::string)>& resultCb)
      : mReactor(requestServiceThread),
      mCallbackThread(callbackThread),
      mJoinUrl(leaderHelloUrl),
      mNodeId(nodeId),
      mQueryContextUrl(queryContextUrl),
      mJoinClusterUrl(joinClusterUrl),
      mMappings(urlMappings),
      mAuthToken(authToken),
      mIgnoreCertVerification(ignoreCertVerification),
      mTlsSettings(tlsSettings),
      mResultCb(resultCb)
   {
   }
   ~LeaderHelloRequest()
   {
   }

   void start()
   {
      mReactor.post(resip::resip_bind(&LeaderHelloRequest::doLeaderHelloFlow, this));
   }

private:
   void doLeaderHelloFlow()
   {
      std::unique_ptr<LeaderHelloRequest> thisDeleter(this);
      std::string websocketUrl;
      std::string orchServerUrl(mJoinUrl.c_str(), mJoinUrl.size());

      try
      {
         int response = 0;
         std::string responseBodyStr;
         if (mRetries-- > 0)
         {
            websocketpp::uri orchUri(orchServerUrl);
            SimpleWeb::Client<SimpleWeb::HTTPS> swClient(orchUri.get_host_port(), false);

            std::stringstream reqBody;
            reqBody << "{";
            reqBody << "   \"queryContextUrl\": \"" << mQueryContextUrl << "\",";
            reqBody << "   \"joinClusterUrl\": \"" << mJoinClusterUrl << "\",";
            reqBody << "   \"nodeId\":" << mNodeId << ",";
            reqBody << "   \"recentMappings\":" << "[";
            for (size_t ifol = 0; ifol < mMappings.size(); ifol++)
            {
               const auto& flwr = mMappings[ifol];
               reqBody << "{";
               reqBody << "    \"joinUrl\": \"" << flwr.first << "\",";
               reqBody << "    \"wsUrlBase\": \"" << flwr.second << "\"";
               reqBody << "}";
               if (ifol < mMappings.size() - 1)
               {
                  reqBody << ",";
               }
            }
            reqBody << "   ]";
            reqBody << "}";
            std::string reqBodyStr = reqBody.str();

            SimpleWeb::CaseInsensitiveMultimap reqHeaders;
            reqHeaders.emplace("Authorization", std::string("bearer ") + mAuthToken.c_str());
            auto swResp = swClient.request("POST", orchUri.get_resource(), reqBodyStr, reqHeaders);
            resip::Data respCodeStr = swResp->status_code.c_str();
            response = respCodeStr.convertInt();
            responseBodyStr = swResp->content.string();

            if (response == 408)
            {
               thisDeleter.release();
               mReactor.postMS(resip::resip_bind(&LeaderHelloRequest::doLeaderHelloFlow, this), 1000);
               return;
            }
         }

         StackLog(<< "doLeaderHelloFlow(): orchServer: " << orchServerUrl << " response: " << responseBodyStr);
         if (response == 200)
         {
            std::unique_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
            jsonRequest->Parse<0>(responseBodyStr.c_str());
            if (jsonRequest->HasParseError())
            {
               DebugLog(<< "ConferenceConnectorImpl::doLeaderHelloFlow(): response parse error");
               postFailureCallback();
               return;
            }

            if (!jsonRequest->HasMember("wsUrlBase"))
            {
               DebugLog(<< "ConferenceConnectorImpl::doLeaderHelloFlow(): response missing wsUrlBase");
               postFailureCallback();
               return;
            }

            const rapidjson::Value& websocketUrlVal = (*jsonRequest)["wsUrlBase"];
            if (!websocketUrlVal.IsString())
            {
               DebugLog(<< "ConferenceConnectorImpl::doLeaderHelloFlow(): response wsUrlBase type mismatch");
               postFailureCallback();
               return;
            }

            websocketUrl = websocketUrlVal.GetString();
            assert(!websocketUrl.empty());

            resip::ReadCallbackBase* cb = resip::resip_static_bind(mResultCb, 0, websocketUrl);
            mCallbackThread.post(cb);
         }
         else
         {
            DebugLog(<< "doLeaderHelloFlow(): failure response: " << response);
            postFailureCallback();
         }
      }
      catch (const SimpleWeb::system_error& e)
      {
         DebugLog(<< "doLeaderHelloFlow(): error sending leader hello to: " << orchServerUrl << " runtime error: " << e.what());
         if (mRetries > 0)
         {
            thisDeleter.release();
            mReactor.postMS(resip::resip_bind(&LeaderHelloRequest::doLeaderHelloFlow, this), 1000);
         }
         else
         {
            postFailureCallback();
         }
      }
   }

   void postFailureCallback()
   {
      resip::ReadCallbackBase* cb = resip::resip_static_bind(mResultCb, -1, "");
      mCallbackThread.post(cb);
   }

private:
   resip::MultiReactor& mReactor;
   resip::MultiReactor& mCallbackThread;
   resip::Data mJoinUrl;
   resip::Data mAuthToken;
   bool mIgnoreCertVerification;
   SslCipherOptions mTlsSettings;
   std::function<void(int, const std::string&)> mResultCb;
   int mNodeId;
   resip::Data mQueryContextUrl;
   resip::Data mJoinClusterUrl;
   std::vector<std::pair<resip::Data,resip::Data> > mMappings;
   int mRetries = 10;
};

int ConferenceRegistrarManagerImpl::doLeaderHello(int nodeId, const cpc::string& ipAndPort, const std::map<cpc::string, cpc::string>& recentUrlMappings)
{
   std::weak_ptr<ConferenceRegistrarManagerImpl> weakThis(shared_from_this());
   doAuth([weakThis, nodeId, ipAndPort, recentUrlMappings](int resultCode, const resip::Data& authToken, const cpc::string& details)
   {
      if (std::shared_ptr<ConferenceRegistrarManagerImpl> thisPtr = weakThis.lock())
      {
         if (resultCode == 0)
         {
            std::stringstream leaderHelloUrl;
            leaderHelloUrl << ipAndPort;
            std::stringstream queryContextUrl;
            queryContextUrl << "https://";
            queryContextUrl << thisPtr->mLocalConfig.conferenceRegistrarServiceIp;
            std::stringstream joinClusterUrl;
            joinClusterUrl << "https://";
            joinClusterUrl << thisPtr->mLocalConfig.conferenceRegistrarServiceIp;
            joinClusterUrl << "/statusApi/joinCluster";
            std::vector<std::pair<resip::Data, resip::Data> > urlMappingsArray;
            std::map<cpc::string,cpc::string>::const_iterator itFol = recentUrlMappings.begin();
            for (; itFol != recentUrlMappings.end(); ++itFol)
            {
               std::pair<resip::Data, resip::Data> pr;
               pr.first = itFol->first;
               pr.second = itFol->second;
               urlMappingsArray.push_back(pr);
            }
            LeaderHelloRequest* leaderHelloReq = new LeaderHelloRequest(thisPtr->mWebRequestThread,
               thisPtr->mPhone->getSdkModuleThread(),
               leaderHelloUrl.str().c_str(),
               thisPtr->mNodeId,
               queryContextUrl.str().c_str(),
               joinClusterUrl.str().c_str(),
               urlMappingsArray,
               authToken,
               true,
               thisPtr->mPhone->getSslCipherOptions(),
               [weakThis, nodeId, ipAndPort](int resultCode, const std::string& wsUrl)
            {
               if (std::shared_ptr<ConferenceRegistrarManagerImpl> thisPtr = weakThis.lock())
               {
                  if (resultCode == -1)
                  {
                     InfoLog(<< "Leader hello to node " << nodeId << " failed");
                     {
                        webrtc::WriteLockScoped lck(*thisPtr->mStateLock);
                        thisPtr->mLeaderState.mFollowerMap[nodeId].isOK = false;
                     }
                  }
                  else
                  {
                     webrtc::WriteLockScoped lck(*thisPtr->mStateLock);
                     if (thisPtr->mLeaderState.mFollowerMap.count(nodeId) == 0)
                     {
                        InfoLog(<< "Adding " << nodeId << " to follower map: " << nodeId << " --> " << wsUrl);
                     }
                     thisPtr->mLeaderState.mFollowerMap[nodeId].ipAndPort = ipAndPort;
                     thisPtr->mLeaderState.mFollowerMap[nodeId].isOK = true;
                     thisPtr->mLeaderState.mFollowerMap[nodeId].wsUrlBase = wsUrl.c_str();
                  }
               }
            });
            leaderHelloReq->start();
         }
      }
   });

   return kSuccess;
}

template <typename T>
void ConferenceRegistrarManagerImpl::handleRegisterContextRequest(std::shared_ptr<typename SimpleWeb::Server<T>::Response> response, std::shared_ptr<typename SimpleWeb::Server<T>::Request> request)
{
   InfoLog(<< "handleRegisterContextRequest: " << request->path);

   if (!mInService)
   {
      WarningLog(<< "Not in service");
      response->write(SimpleWeb::StatusCode::server_error_service_unavailable);
      return;
   }

   resip::Data authToken;
   CPCAPI2::JsonApi::JsonApiServerHTTP::getAuthTokenFromRequest<T>(request, authToken);

   CPCAPI2::JsonApi::JsonApiServerSendTransportInterface* jsonApiServerSendIf = dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInterface*>(CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(mPhone));
   CPCAPI2::JsonApi::JsonApiServerInterface* jsonApiServerIf = jsonApiServerSendIf->getJsonApiServer();

   resip::Data userIdentity, deviceId;
   std::map<resip::Data, resip::Data> publicClaims;
   std::vector<resip::Data> requestedResources;
   if (jsonApiServerIf->validateAuthToken(authToken, userIdentity, deviceId, publicClaims, requestedResources) != 0)
   {
      *response << "HTTP/1.1 400 Invalid Auth Token\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
   std::string reqContent = request->content.string();
   jsonRequest->Parse<0>(reqContent.c_str(), reqContent.size());

   if (jsonRequest->HasParseError())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Parse Error\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   if (!jsonRequest->HasMember("nodeId"))
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing nodeId\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   if (!jsonRequest->HasMember("localNodeId"))
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing localNodeId\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   if (!jsonRequest->HasMember("joinUrl"))
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing joinUrl\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   if (!jsonRequest->HasMember("overrideExisting"))
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing overrideExisting\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   const rapidjson::Value& nodeIdVal = (*jsonRequest)["nodeId"];
   if (!nodeIdVal.IsInt())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing int nodeId\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   const rapidjson::Value& localNodeIdVal = (*jsonRequest)["localNodeId"];
   if (!localNodeIdVal.IsInt())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing int localNodeId\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   const rapidjson::Value& joinUrlVal = (*jsonRequest)["joinUrl"];
   if (!joinUrlVal.IsString())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing string joinUrl\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   const rapidjson::Value& overrideExistingVal = (*jsonRequest)["overrideExisting"];
   if (!overrideExistingVal.IsBool())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing bool overrideExisting\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   if (localIsLeader())
   {
      registerConference(cpc::string(joinUrlVal.GetString(), joinUrlVal.GetStringLength()), nodeIdVal.GetInt(), overrideExistingVal.GetBool(), NULL);
      SimpleWeb::CaseInsensitiveMultimap authRespHeaders;
      authRespHeaders.emplace("Access-Control-Allow-Origin", "*");
      authRespHeaders.emplace("Access-Control-Allow-Headers", "Authorization");
      authRespHeaders.emplace("Cache-Control", "no-cache, no-store, must-revalidate");
      response->write(std::string(), authRespHeaders);
   }
   else
   {
      if (localNodeIdVal.GetInt() == mNodeId)
      {
         response->write(SimpleWeb::StatusCode::server_error_loop_detected);
      }
      else
      {
         response->write(SimpleWeb::StatusCode::server_error_not_implemented);
      }
   }
}

template <typename T>
void ConferenceRegistrarManagerImpl::handleJoinClusterRequest(std::shared_ptr<typename SimpleWeb::Server<T>::Response> response, std::shared_ptr<typename SimpleWeb::Server<T>::Request> request)
{
   InfoLog(<< "handleJoinClusterRequest: " << request->path);

   if (!mInService)
   {
      WarningLog(<< "Not in service");
      response->write(SimpleWeb::StatusCode::server_error_service_unavailable);
      return;
   }

   resip::Data authToken;
   CPCAPI2::JsonApi::JsonApiServerHTTP::getAuthTokenFromRequest<T>(request, authToken);

   CPCAPI2::JsonApi::JsonApiServerSendTransportInterface* jsonApiServerSendIf = dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInterface*>(CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(mPhone));
   CPCAPI2::JsonApi::JsonApiServerInterface* jsonApiServerIf = jsonApiServerSendIf->getJsonApiServer();

   resip::Data userIdentity, deviceId;
   std::map<resip::Data, resip::Data> publicClaims;
   std::vector<resip::Data> requestedResources;
   if (jsonApiServerIf->validateAuthToken(authToken, userIdentity, deviceId, publicClaims, requestedResources) != 0)
   {
      *response << "HTTP/1.1 400 Invalid Auth Token\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
   std::string reqContent = request->content.string();
   jsonRequest->Parse<0>(reqContent.c_str(), reqContent.size());

   if (jsonRequest->HasParseError())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Parse Error\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   if (!jsonRequest->HasMember("nodeId"))
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing nodeId\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   if (!jsonRequest->HasMember("ipAndPort"))
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing ipAndPort\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   if (!jsonRequest->HasMember("wsUrlBase"))
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing wsUrlBase\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   const rapidjson::Value& nodeIdVal = (*jsonRequest)["nodeId"];
   if (!nodeIdVal.IsInt())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing int nodeId\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   const rapidjson::Value& ipportVal = (*jsonRequest)["ipAndPort"];
   if (!ipportVal.IsString())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing string ipAndPort\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   const rapidjson::Value& wsUrlBaseVal = (*jsonRequest)["wsUrlBase"];
   if (!wsUrlBaseVal.IsString())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing string wsUrlBase\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   if (nodeIdVal.GetInt() == mNodeId)
   {
      response->write(SimpleWeb::StatusCode::server_error_loop_detected);
   }
   else if (localIsLeader())
   {
      std::map<cpc::string, cpc::string> recentUrlMappings;
      appendRandomUrlMappingsFromDb(recentUrlMappings);
      mPhone->getSdkModuleThread().post(resip::resip_bind(&ConferenceRegistrarManagerImpl::doLeaderHello, this, nodeIdVal.GetInt(), cpc::string(ipportVal.GetString(),ipportVal.GetStringLength()), recentUrlMappings));
      SimpleWeb::CaseInsensitiveMultimap authRespHeaders;
      authRespHeaders.emplace("Access-Control-Allow-Origin", "*");
      authRespHeaders.emplace("Access-Control-Allow-Headers", "Authorization");
      authRespHeaders.emplace("Cache-Control", "no-cache, no-store, must-revalidate");
      response->write(std::string(), authRespHeaders);
   }
   else
   {
      resip::Data joinClusterUrl;
      {
         webrtc::ReadLockScoped lck(*mStateLock);
         joinClusterUrl = mJoinClusterUrl;
      }
      if (joinClusterUrl.empty())
      {
         response->write(SimpleWeb::StatusCode::server_error_bad_gateway);
      }
      else
      {
         std::stringstream leaderJoinClusterUrl;
         leaderJoinClusterUrl << joinClusterUrl;
         response->write(SimpleWeb::StatusCode::redirection_temporary_redirect, { {"Location", leaderJoinClusterUrl.str()} });
      }
   }
}

template <typename T>
void ConferenceRegistrarManagerImpl::handleLeaderHelloRequest(std::shared_ptr<typename SimpleWeb::Server<T>::Response> response,
   std::shared_ptr<typename SimpleWeb::Server<T>::Request> request)
{
   InfoLog(<< "handleLeaderHelloRequest: " << request->path);

   if (!mInService)
   {
      WarningLog(<< "Not in service");
      response->write(SimpleWeb::StatusCode::server_error_service_unavailable);
      return;
   }

   resip::Data authToken;
   CPCAPI2::JsonApi::JsonApiServerHTTP::getAuthTokenFromRequest<T>(request, authToken);

   CPCAPI2::JsonApi::JsonApiServerSendTransportInterface* jsonApiServerSendIf = dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInterface*>(CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(mPhone));
   CPCAPI2::JsonApi::JsonApiServerInterface* jsonApiServerIf = jsonApiServerSendIf->getJsonApiServer();

   resip::Data userIdentity, deviceId;
   std::map<resip::Data, resip::Data> publicClaims;
   std::vector<resip::Data> requestedResources;
   if (jsonApiServerIf->validateAuthToken(authToken, userIdentity, deviceId, publicClaims, requestedResources) != 0)
   {
      *response << "HTTP/1.1 400 Invalid Auth Token\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
   std::string reqContent = request->content.string();
   jsonRequest->Parse<0>(reqContent.c_str(), reqContent.size());

   if (jsonRequest->HasParseError())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Parse Error\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   if (!jsonRequest->HasMember("nodeId"))
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing nodeId\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   if (!jsonRequest->HasMember("queryContextUrl"))
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing queryContextUrl\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   if (!jsonRequest->HasMember("joinClusterUrl"))
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing joinClusterUrl\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   if (!jsonRequest->HasMember("recentMappings"))
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing recentMappings\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   const rapidjson::Value& nodeIdVal = (*jsonRequest)["nodeId"];
   if (!nodeIdVal.IsInt())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing int nodeId\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   const rapidjson::Value& qrycontextVal = (*jsonRequest)["queryContextUrl"];
   if (!qrycontextVal.IsString())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing string queryContextUrl\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   const rapidjson::Value& joinclusterVal = (*jsonRequest)["joinClusterUrl"];
   if (!joinclusterVal.IsString())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing string joinClusterUrl\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   const rapidjson::Value& followersVal = (*jsonRequest)["recentMappings"];
   if (!followersVal.IsArray())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      *response << "HTTP/1.1 400 Missing array recentMappings\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   bool localIsLeader = false;
   {
      webrtc::ReadLockScoped lck(*mStateLock);
      localIsLeader = mLeaderState.mLocalIsLeader;
   }

   if (localIsLeader)
   {
      ErrLog(<< "Leader should not be getting leader hello!");
      return;
   }
   else
   {
      bool isStartup = false;
      {
         webrtc::WriteLockScoped lck(*mStateLock);
         isStartup = mQueryContextUrl.empty();
         mLastHelloTime = std::chrono::system_clock::now();
         mQueryContextUrl = resip::Data(qrycontextVal.GetString(), qrycontextVal.GetStringLength());
         mJoinClusterUrl = resip::Data(joinclusterVal.GetString(), joinclusterVal.GetStringLength());
         for (rapidjson::Value::ConstValueIterator itRR = followersVal.Begin(); itRR != followersVal.End(); ++itRR)
         {
            if (itRR->IsObject())
            {
               if (itRR->HasMember("joinUrl") && itRR->HasMember("wsUrlBase"))
               {
                  const rapidjson::Value& rmJoinUrlVal = (*itRR)["joinUrl"];
                  if (!rmJoinUrlVal.IsString())
                  {
                     continue;
                  }
                  const rapidjson::Value& rmWsUrlVal = (*itRR)["wsUrlBase"];
                  if (!rmWsUrlVal.IsString())
                  {
                     continue;
                  }
                  upsertUrlMappingDb(rmJoinUrlVal.GetString(), rmWsUrlVal.GetString());
               }
            }
         }
         mLeaderNodeId = nodeIdVal.GetInt();
         mIsPartOfCluster = true;
      }

      if (mInService)
      {
         rapidjson::Document respJson;
         respJson.SetObject();

         rapidjson::Value wsUrlVal(mLocalConfig.wsUrlBase, respJson.GetAllocator());
         respJson.AddMember("wsUrlBase", wsUrlVal, respJson.GetAllocator());

         rapidjson::StringBuffer buffer(0, 1024);
         rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
         respJson.Accept(writer);

         SimpleWeb::CaseInsensitiveMultimap respHeaders;
         respHeaders.emplace("Access-Control-Allow-Origin", "*");
         respHeaders.emplace("Access-Control-Allow-Headers", "Authorization");
         respHeaders.emplace("Cache-Control", "no-cache, no-store, must-revalidate");
         response->write(std::string(buffer.GetString(), buffer.GetSize()), respHeaders);

         if (isStartup)
         {
            ConferenceRegistrarStartupResult startupResult;
            startupResult.success = true;
            startupResult.localIsLeader = false;
            mInterface->fireEvent(cpcFunc(ConferenceRegistrarHandler::onStartupComplete), 0, startupResult);
            InfoLog(<< "ConferenceRegistrarManagerImpl::handleLeaderHelloRequest(..) - startup complete; nodeId = " << mNodeId);
         }
      }
      else
      {
         WarningLog(<< "Not in service");
         response->write(SimpleWeb::StatusCode::server_error_service_unavailable);
      }
   }
}

int ConferenceRegistrarManagerImpl::initializeDb(const cpc::string& dbfile)
{
   for (size_t i = 0; i != CONF_REG_DB_THREADS; ++i)
   {
      try
      {
         if (mDbPool.at(i).is_connected())
         {
            mDbPool.at(i).close();
         }
         mDbPool.at(i).open(soci::sqlite3, (std::string)dbfile);
      }
      catch (soci::soci_error&)
      {
         return -1;
      }
   }

   try
   {
      soci::session db(mDbPool);
      {
         webrtc::WriteLockScoped lock(*mDbLock);
         db <<
            "CREATE TABLE IF NOT EXISTS UrlMap ( "
            " joinUrl TEXT PRIMARY KEY, "
            " wsBaseUrl TEXT "
            " )";
      }
   }
   catch (soci::soci_error&)
   {
      return -2;
   }

   return 0;
}

int ConferenceRegistrarManagerImpl::upsertUrlMappingDb(const cpc::string& joinUrl, const cpc::string& wsBaseUrl)
{
   std::string joinUrlStr(joinUrl.c_str(), joinUrl.size());
   std::string wsBaseUrlStr(wsBaseUrl.c_str(), wsBaseUrl.size());
   soci::session db(mDbPool);
   try
   {
      webrtc::WriteLockScoped lock(*mDbLock);
      db << "INSERT INTO UrlMap(joinUrl, wsBaseUrl) VALUES(:joinUrl, :wsBaseUrl) ON CONFLICT(joinUrl) DO UPDATE SET wsBaseUrl=:wsBaseUrl", soci::use(joinUrlStr, "joinUrl"), soci::use(wsBaseUrlStr, "wsBaseUrl");
   }
   catch (soci::soci_error& socerr)
   {
      ErrLog(<< "soci error: " << socerr.what());
   }
   return 0;
}

int ConferenceRegistrarManagerImpl::queryJoinUrl(const cpc::string& joinUrl, bool& urlExists, cpc::string& wsBaseUrl)
{
   urlExists = false;

   soci::indicator ijoinurl;
   soci::indicator iwsbaseurl;
   std::string joinurlRes;
   std::string wsbaseurlRes;

   const std::string _joinurl(joinUrl);

   soci::session db(mDbPool);
   {
      webrtc::ReadLockScoped lock(*mDbLock);
      db << "SELECT joinUrl, wsBaseUrl FROM UrlMap WHERE joinUrl = :joinUrl", soci::into(joinurlRes, ijoinurl), soci::into(wsbaseurlRes, iwsbaseurl), soci::use(_joinurl, "joinUrl");
   }

   if (db.got_data())
   {
      if (ijoinurl == soci::i_ok)
      {
         urlExists = true;
         wsBaseUrl = cpc::string(wsbaseurlRes.c_str());
      }
   }
   return 0;
}

int ConferenceRegistrarManagerImpl::appendRandomUrlMappingsFromDb(std::map<cpc::string, cpc::string>& urlMappings)
{
   std::vector<std::string> joinurlRes(5);
   std::vector<std::string> wsbaseurlRes(5);

   soci::session db(mDbPool);
   {
      webrtc::ReadLockScoped lock(*mDbLock);
      db << "SELECT joinUrl, wsBaseUrl FROM UrlMap ORDER BY RANDOM() LIMIT 5", soci::into(joinurlRes), soci::into(wsbaseurlRes);
   }

   if (db.got_data())
   {
      if (joinurlRes.size() == wsbaseurlRes.size())
      {
         for (int i = 0; i < joinurlRes.size(); i++)
         {
            urlMappings[joinurlRes[i].c_str()] = wsbaseurlRes[i].c_str();
         }
      }
   }
   return 0;
}

int ConferenceRegistrarManagerImpl::flushUrlMapDb()
{
   soci::session db(mDbPool);
   try
   {
      webrtc::WriteLockScoped lock(*mDbLock);
      if (db.is_connected())
      {
         db << "DROP TABLE IF EXISTS UrlMap";
      }
   }
   catch (soci::soci_error& serr)
   {
      ErrLog(<< "soci error in flushUrlMapDb: " << serr.what());
   }
   return 0;
}

}
}

#endif // CPCAPI2_BRAND_CONFERENCE_BRIDGE_REGISTRAR_MODULE

