#include "jsonrpc/OptionalSerialization.h"
#include <any>
#include "jsonrpc/VectorSerialization.h"
#include "jsonrpc/StringSerialization.h"
#include <nlohmann/json.hpp>
#include "MediaType.h"
#include "MediaDirection.h"
#include "MediaCryptoSuite.h"
#include "MediaEncryptionOptions.h"
#include "AudioCodec.h"
#include "VideoCodec.h"

#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_MEDIAINFO_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_MEDIAINFO_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class MediaInfo
  {
  public:
    std::optional<MediaType> mediaType;
    std::optional<MediaDirection> mediaDirection;
    std::optional<MediaCryptoSuite> mediaCrypto;
    std::optional<MediaEncryptionOptions> mediaEncryptionOptions;
    std::optional<AudioCodec> audioCodec;
    std::optional<VideoCodec> videoCodec;
    std::optional<int32_t> conferenceMixContribution;
    std::optional<bool> isLocallyDisabled;
    std::optional<int32_t> conferenceMixId;
    std::optional<int32_t> mediaStreamId;
    std::optional<int32_t> videoCaptureDeviceId;

    MediaInfo() = default;

    MediaInfo(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const MediaInfo& o)
    {
      j = nlohmann::json::object();
       if (o.mediaType) { j["mediaType"] = *(o.mediaType); }
     if (o.mediaDirection) { j["mediaDirection"] = *(o.mediaDirection); }
     if (o.mediaCrypto) { j["mediaCrypto"] = *(o.mediaCrypto); }
     if (o.mediaEncryptionOptions) { j["mediaEncryptionOptions"] = *(o.mediaEncryptionOptions); }
     if (o.audioCodec) { j["audioCodec"] = *(o.audioCodec); }
     if (o.videoCodec) { j["videoCodec"] = *(o.videoCodec); }
     if (o.conferenceMixContribution) { j["conferenceMixContribution"] = *(o.conferenceMixContribution); }
     if (o.isLocallyDisabled) { j["isLocallyDisabled"] = *(o.isLocallyDisabled); }
     if (o.conferenceMixId) { j["conferenceMixId"] = *(o.conferenceMixId); }
     if (o.mediaStreamId) { j["mediaStreamId"] = *(o.mediaStreamId); }
     if (o.videoCaptureDeviceId) { j["videoCaptureDeviceId"] = *(o.videoCaptureDeviceId); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, MediaInfo& o)
    {
     if (j.contains("mediaType")) { o.mediaType = j.at("mediaType"); }
     if (j.contains("mediaDirection")) { o.mediaDirection = j.at("mediaDirection"); }
     if (j.contains("mediaCrypto")) { o.mediaCrypto = j.at("mediaCrypto"); }
     if (j.contains("mediaEncryptionOptions")) { o.mediaEncryptionOptions = j.at("mediaEncryptionOptions"); }
     if (j.contains("audioCodec")) { o.audioCodec = j.at("audioCodec"); }
     if (j.contains("videoCodec")) { o.videoCodec = j.at("videoCodec"); }
     if (j.contains("conferenceMixContribution")) { o.conferenceMixContribution = j.at("conferenceMixContribution"); }
     if (j.contains("isLocallyDisabled")) { o.isLocallyDisabled = j.at("isLocallyDisabled"); }
     if (j.contains("conferenceMixId")) { o.conferenceMixId = j.at("conferenceMixId"); }
     if (j.contains("mediaStreamId")) { o.mediaStreamId = j.at("mediaStreamId"); }
     if (j.contains("videoCaptureDeviceId")) { o.videoCaptureDeviceId = j.at("videoCaptureDeviceId"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_MEDIAINFO_H
