#pragma once

#if !defined(CPCAPI2_SIP_CONFERENCE_MANAGER_IMPL_H)
#define CPCAPI2_SIP_CONFERENCE_MANAGER_IMPL_H

#include "cpcapi2defs.h"
#include "conference/SipConferenceManager.h"
#include "conference/SipConferenceHandler.h"
#include "../event/SipEventManagerInterface.h"
#include "../event/SipEventSubscriptionCreationInfo.h"
#include "../phone/PhoneModule.h"
#include "../phone/PhoneInterface.h"
#include "../account/SipAccountInterface.h"
#include "../account/SipAccountImpl.h"
#include "../account/CPInviteHandlerSelectorDialogSet.h"
#include "SipConferenceHandleFactory.h"
#include "SipConferenceSessionInfo.h"
#include "../call/SipAVConversationManagerInterface.h"

#include <resip/dum/DialogUsageManager.hxx>
#include <resip/dum/InviteSessionHandler.hxx>
#include <resip/stack/MultipartMixedContents.hxx>
#include <resip/stack/SipMessage.hxx>

namespace CPCAPI2
{
class PhoneInterface;

namespace SipAccount
{
class SipAccountInterface;
}

using namespace SipConversation;

namespace SipConference
{
class SipConferenceHandlerImpl;
typedef std::map<SipAccount::SipAccountHandle, CPCAPI2::SipConference::SipConferenceHandlerImpl*> AccountMap;
typedef std::map<SipAccount::SipAccountHandle, CPCAPI2::SipConference::SipConferencePolicy&> AccountPolicyMap;
typedef std::map<CPCAPI2::SipConference::SipConferenceHandle, SipAccount::SipAccountHandle> ConferenceAccountMap;
typedef std::map<CPCAPI2::SipConference::SipConferenceHandle, CPCAPI2::SipConference::SipConferencePolicy&> ConferencePolicyMap;
typedef std::map<CPCAPI2::SipConference::SipConferenceHandle, CPCAPI2::SipConference::SipConferenceInfo*> ConferenceMap;
typedef std::map<CPCAPI2::SipConversation::SipConversationHandle, CPCAPI2::SipConference::SipConferenceHandle> ConfConversationMap;

using namespace resip;
class SipConferenceManagerImpl : public SipConferenceManager,
							     public PhoneModule 
{
public:
   SipConferenceManagerImpl(Phone* phone);

   virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipConferenceHandler* handler) OVERRIDE;
   virtual void Release() OVERRIDE;
   virtual int setDefaultPolicy(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipConference::SipConferencePolicy& policy) OVERRIDE;
   virtual CPCAPI2::SipConversation::SipAVConversationManagerInterface* getConversationInterface()  OVERRIDE { return mConversationIf; }

   virtual SipConferenceHandle createConference(CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;
   virtual int setPolicy(SipConferenceHandle conference, const CPCAPI2::SipConference::SipConferencePolicy& policy) OVERRIDE;
   virtual int addParticipant(SipConferenceHandle conference, const cpc::string& targetAddress) OVERRIDE;
   virtual int configureMedia(SipConferenceHandle conference, const SipConferenceMedia& mediaDescriptor) OVERRIDE;
   virtual int start(SipConferenceHandle conference) OVERRIDE;
   virtual int end(SipConferenceHandle conference) OVERRIDE;
   virtual int reject(SipConferenceHandle conference, unsigned int rejectReason = 0) OVERRIDE;
   virtual int accept(SipConferenceHandle conference) OVERRIDE;
   virtual int getFocusUri(SipConferenceHandle conference, cpc::string& uri) OVERRIDE;
   virtual int removeParticipant(SipConferenceHandle conference, const cpc::string& targetAddress) OVERRIDE;
   virtual int fetchConferenceInfo(SipConferenceHandle conference, const cpc::string& targetAddress) OVERRIDE;
   virtual int notify(SipConferenceHandle conference, const SipConferenceInfo& state) OVERRIDE;
   virtual int setSubject(cpc::string subject) OVERRIDE;
   virtual int delegateFocus(const cpc::string& targetAddress) OVERRIDE;

   virtual SipConferenceHandle createServerConference(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& confFactoryAddress) OVERRIDE;
   virtual int startServerConf(SipConferenceHandle conference) OVERRIDE;
   virtual int inviteToServerConference(SipConversationHandle conversation, cpc::string reason) OVERRIDE;
   virtual int setServerConfFocusUri(SipConversationHandle conversation, cpc::string focusUri) OVERRIDE;
   virtual int callServerConfParticipants(SipConferenceHandle conference) OVERRIDE;

private:
   int setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipConferenceHandler* handler);
   int setPolicyImpl(SipConferenceHandle conference, const CPCAPI2::SipConference::SipConferencePolicy& policy);
   int setDefaultPolicyImpl(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipConference::SipConferencePolicy& policy);
   int createConferenceImpl(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipConference::SipConferenceHandle conference);
   int addParticipantImpl(SipConferenceHandle conference, const cpc::string& targetAddress);
   int configureMediaImpl(SipConferenceHandle conference, const SipConferenceMedia& mediaDescriptor);
   int startImpl(SipConferenceHandle conference);

   int createServerConferenceImpl(CPCAPI2::SipAccount::SipAccountHandle account, 
										  CPCAPI2::SipConference::SipConferenceHandle conference, 
										  const cpc::string& confFactoryAddress);
   int startServerConfImpl(SipConferenceHandle conference);
   int inviteToServerConferenceImpl(SipConversationHandle conversation, cpc::string reason);

   cpc::string generateUniqueConferenceUri(CPCAPI2::SipAccount::SipAccountHandle account);
   cpc::string getUri(CPCAPI2::SipAccount::SipAccountHandle account);
   cpc::string currentDateTime();
   cpc::string buildSdp(CPCAPI2::SipAccount::SipAccountImpl* account);
   cpc::string buildUriList(SipConferenceHandle conference);

   /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
   // Members
   /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
   CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;
   CPCAPI2::SipEvent::SipEventManagerInterface* mSipEventIf;
   CPCAPI2::PhoneInterface* mPhone;
   CPCAPI2::SipConversation::SipAVConversationManagerInterface* mConversationIf;

   AccountMap mAccountMap;
   ConferencePolicyMap mConferencePolicyMap;
   AccountPolicyMap mAccountPolicyMap;
   ConferenceMap mConferenceMap;
   ConferenceAccountMap mConfAccountMap;
   ConfConversationMap mConfConversationMap;
   SipConferencePolicy confPolicy;
   
   boolean isFocus;
   boolean isConferenceAware;

   unsigned int localAudioPort;
   unsigned int localVideoPort;

};

}

}

#endif // CPCAPI2_SIP_CONFERENCE_MANAGER_IMPL_H
