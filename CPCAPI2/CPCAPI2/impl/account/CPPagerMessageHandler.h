#pragma once

#ifndef __CPCAPI2_CP_PAGER_MESSAGE_HANDLER_H__
#define __CPCAPI2_CP_PAGER_MESSAGE_HANDLER_H__

#include "cpcapi2defs.h"
#include "CPPagerMessageHandlerDelegate.h"

#include <resip/dum/PagerMessageHandler.hxx>

namespace CPCAPI2
{
namespace SipAccount
{

class CPPagerMessageHandler : public resip::ServerPagerMessageHandler,
                              public resip::ClientPagerMessageHandler
{
public:
   CPPagerMessageHandler() {}
   virtual ~CPPagerMessageHandler() {}

   void addDelegate(CPPagerMessageHandlerDelegatePtr delegate, bool addAtEnd = false);

   void clearDelegates();

protected:
   // ServerPagerMessageHandler interface
   virtual void onMessageArrived(resip::ServerPagerMessageHandle h, const resip::SipMessage& message) OVERRIDE;

   // ClientPagerMessageHandler interface
   virtual void onSuccess(resip::ClientPagerMessageHandle h, const resip::SipMessage& status) OVERRIDE;
   virtual void onFailure(resip::ClientPagerMessageHandle h, const resip::SipMessage& status, std::unique_ptr<resip::Contents> contents) OVERRIDE;

private:
   std::list<CPPagerMessageHandlerDelegatePtr> delegates;
};

}
}

#endif // __CPCAPI2_CP_PAGER_MESSAGE_HANDLER_H__
