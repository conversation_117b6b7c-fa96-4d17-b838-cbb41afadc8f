#include "DialogInfoDocumentHelper.h"
#include "../util/LogSubsystems.h"
#include "../util/libXmlHelper.h"
#include "../util/LibxmlSharedUsage.h"

#include <resip/stack/Contents.hxx>
#include <resip/stack/Symbols.hxx>
#include <rutil/Logger.hxx>
#include <sstream>

using namespace resip;
using namespace CPCAPI2::SipEvent;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_DIALOGEVENT

namespace CPCAPI2
{
namespace SipDialogEvent
{

const cpc::string DialogInfoDocumentHelper::EVENT_PACKAGE_NAME = "dialog";
const MimeType DialogInfoDocumentHelper::EVENT_MIME_TYPE = MimeType("application", "dialog-info+xml");
const cpc::string DialogInfoDocumentHelper::INCLUDE_SDP_PARAM_NAME = "include-session-description";
const cpc::string DialogInfoDocumentHelper::SHARED_APPEARANCE_DIALOG_EXTENSIONS_PARAM_NAME = "shared";

class LibXmlParserInitHelper
{
public:
   LibXmlParserInitHelper() { ::xmlInitParser(); }
   virtual ~LibXmlParserInitHelper() { /* ::xmlCleanupParser(); */ }
   static void libxmlErrorHandler(void* arg, const char* msg, xmlParserSeverities severity, xmlTextReaderLocatorPtr locator);
};

LibXmlParserInitHelper libXmlParserInitHelper;

void LibXmlParserInitHelper::libxmlErrorHandler(void* arg, const char* msg, xmlParserSeverities severity, xmlTextReaderLocatorPtr locator)
{
   DebugLog(<< "libxml error: " << msg);
   DebugLog(<< "libxml error severity: " << severity);
   DebugLog(<< "libxml error! in XML at line " << xmlTextReaderLocatorLineNumber(locator));
   bool *parsingError = (bool*) arg;
   assert(parsingError);
   *parsingError = true;
}

cpc::string DialogInfoDocumentHelper::toXml(DialogInfoDocument doc, bool includeSessionDescription, bool sharedAppearanceDialogExtensionsEnabled)
{
   std::ostringstream ss;
   encode(ss, doc, includeSessionDescription, sharedAppearanceDialogExtensionsEnabled);
   return ss.str().c_str();
}

 bool DialogInfoDocumentHelper::fromXml(const cpc::string& xml, DialogInfoDocument& doc, bool sharedAppearanceDialogExtensionsEnabled)
{
   resip::Data strContents(xml.c_str());

   // ALU's dialog-info document is missing the 'sa' prefix declaration (for dialog extensions)
   // so insert it before XML parsing of the document is performed
   if (sharedAppearanceDialogExtensionsEnabled)
   {
      if (xml.find("xmlns:sa") == cpc::string::npos)
      {
         int pos = xml.find("xmlns=");
         cpc::string str1 = xml.substr(0, pos);
         cpc::string str2 = xml.substr(pos);
         strContents = str1 + "xmlns:sa=\"urn:ietf:params:xml:ns:sa-dialog-info\" " + str2;
      }
   }

   if (strContents.find("</dialog-info>") == resip::Data::npos)
   {
      // not a complete <dialog-info>..</dialog-info>
      // either the sender is sending garbage, or perhaps this was sent
      // over UDP and we're missing part of the document
      ErrLog(<< "incomplete dialog-info contents!");
      return false;
   }

   DebugLog(<< "parsing " << strContents);

   xmlTextReaderPtr reader = xmlReaderForMemory(
      strContents.c_str(),//pb.start(),
      (int)strContents.size(),//len,
      NULL,
      "UTF-8",
      128 // XML_PARSE_PEDANTIC
   );

   bool ret = false;
   if (reader != NULL)
   {
      bool parsingError = false;
      xmlTextReaderSetErrorHandler(reader, &LibXmlParserInitHelper::libxmlErrorHandler, &parsingError);
      ret = parse(reader, &parsingError, doc, sharedAppearanceDialogExtensionsEnabled);
      xmlFreeTextReader(reader);
   }

   return ret;
}

std::ostream& DialogInfoDocumentHelper::encode(std::ostream& ds, const DialogInfoDocument& doc, bool includeSessionDescription, bool sharedAppearanceDialogExtensionsEnabled)
{
   ds << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" << Symbols::CRLF;
   ds << "<dialog-info xmlns=\"urn:ietf:params:xml:ns:dialog-info\"" << Symbols::CRLF;
   if (sharedAppearanceDialogExtensionsEnabled)
   {
      ds << "   xmlns:sa=\"urn:ietf:params:xml:ns:sa-dialog-info\"" << Symbols::CRLF;
   }
   ds << "   version=\"" << doc.version << "\"" << Symbols::CRLF;
   ds << "   state=\"" << dialogInfoDocumentStateToString(doc.state) << "\"" << Symbols::CRLF;
   ds << "   entity=\"" << doc.entity << "\">" << Symbols::CRLF;

   for (cpc::vector<DialogInfo>::const_iterator it = doc.dialogs.begin(); it != doc.dialogs.end(); it++)
   {
      encode(ds, *it, includeSessionDescription, sharedAppearanceDialogExtensionsEnabled);
   }

   ds << "</dialog-info>" << Symbols::CRLF;

   return ds;
}

std::ostream& DialogInfoDocumentHelper::encode(std::ostream& ds, const DialogStateInfo& dialogStateInfo)
{
   ds << "   <state";
   if (dialogStateInfo.reason != DialogStateReason_NotSpecified)
   {
      ds << " reason=\"" << dialogStateReasonToString(dialogStateInfo.reason) << "\"";
   }
   if (dialogStateInfo.code != 0)
   {
      ds << " code=\"" << dialogStateInfo.code << "\"";
   }
   ds << ">" << dialogStateToString(dialogStateInfo.state) << "</state>" << Symbols::CRLF;

   return ds;
}

std::ostream& DialogInfoDocumentHelper::encode(std::ostream& ds, const DialogInfo& dialog, bool includeSessionDescription, bool sharedAppearanceDialogExtensionsEnabled)
{
   ds << "<dialog id=\"" << dialog.id << "\" call-id=\"" << dialog.dialogId.callId << "\"" << Symbols::CRLF;
   ds << "   local-tag=\"" << dialog.dialogId.localTag << "\"" << Symbols::CRLF;
   ds << "   remote-tag=\"" << dialog.dialogId.remoteTag << "\"" << Symbols::CRLF;
   ds << "   direction=\"" << dialogDirectionToString(dialog.direction) << "\">" << Symbols::CRLF;
   encode(ds, dialog.stateInfo);
   if (dialog.duration > 0)
   {
      ds << "   <duration>";
      ds << dialog.duration;
      ds << "</duration>" << Symbols::CRLF;
   }

   if (dialog.remoteParticipant.identities.size() > 0)
   {
      ds << "   <remote>" << Symbols::CRLF;
      encode(ds, dialog.remoteParticipant, includeSessionDescription);
      ds << "   </remote>" << Symbols::CRLF;
   }

   if (dialog.localParticipant.identities.size() > 0)
   {
      ds << "   <local>" << Symbols::CRLF;
      encode(ds, dialog.localParticipant, includeSessionDescription);
      ds << "   </local>" << Symbols::CRLF;
   }

   if (dialog.routeSet.size() > 0)
   {
      ds << "   <route-set>" << Symbols::CRLF;         
      for (cpc::vector<cpc::string>::const_iterator it = dialog.routeSet.begin(); it != dialog.routeSet.end(); it++)
      {
         ds << "      <hop>" << *it << "</hop>" << Symbols::CRLF;
      }
      ds << "   </route-set>" << Symbols::CRLF;
   }

   if (!dialog.referredBy.address.empty())
   {
      ds << "   <referred-by";
      if (!dialog.referredBy.displayName.empty())
      {
         ds << " display-name=\"" << dialog.referredBy.displayName << "\"";
      }
      ds << ">" << dialog.referredBy.address;
      ds << "</referred-by>" << Symbols::CRLF;
   }

   if (!dialog.replaces.callId.empty())
   {
      ds << "   <replaces call-id=\"" << dialog.replaces.callId << "\"" << Symbols::CRLF;
      ds << "      local-tag=\"" << dialog.replaces.localTag << "\"" << Symbols::CRLF;
      ds << "      remote-tag=\"" << dialog.replaces.remoteTag << "\" />" << Symbols::CRLF;
   }

   if (sharedAppearanceDialogExtensionsEnabled)
   {
      ds << "   <sa:exclusive>" << dialogExclusiveToString(dialog.exclusive) << "</sa:exclusive>" << Symbols::CRLF;
      if (dialog.appearance > 0)
      {
         ds << "   <sa:appearance>" << dialog.appearance << "</sa:appearance>" << Symbols::CRLF;
      }
   }

   ds << "</dialog>" << Symbols::CRLF;

   return ds;
}

std::ostream& DialogInfoDocumentHelper::encode(std::ostream& ds, const ParticipantInfo& participant, bool includeSessionDescription)
{
   cpc::vector<IdentityInfo>::const_iterator it = participant.identities.begin();
   for (; it != participant.identities.end(); it++)
   {
      ds << "      <identity";
      if (!it->displayName.empty())
      {
         ds << " display-name=\"" << it->displayName << "\"";
      }
      ds << ">" << it->address << "</identity>" << Symbols::CRLF;
   }

   if (!participant.target.uri.empty())
   {
      ds << "      <target uri=\"" << participant.target.uri << "\">" << Symbols::CRLF;
      for (cpc::vector<Parameter>::const_iterator it = participant.target.params.begin(); it != participant.target.params.end(); it++)
      {
         ds << "         <param pname=\"" << it->name << "\" pval=\"" << it->value << "\"/>" << Symbols::CRLF;
      }
      ds << "      </target>" << Symbols::CRLF;
   }

   if (includeSessionDescription && !participant.sessionDescription.empty())
   {
      ds << "      <session-description";
      if (!participant.contentType.empty())
      {
         ds << " type=\"" << participant.contentType << "\"";
      }
      ds << ">";
      ds << participant.sessionDescription << "</session-description>" << Symbols::CRLF;
   }

   if (participant.cseq > 0)
   {
      ds << "      <cseq>" << participant.cseq << "</cseq>" << Symbols::CRLF;
   }

   return ds;
}

bool DialogInfoDocumentHelper::parse(xmlTextReaderPtr reader, bool *parsingError, DialogInfoDocument& doc, bool sharedAppearanceDialogExtensionsEnabled)
{
   int ret = xmlTextReaderRead(reader);
   while (ret == 1)
   {
		std::string nodeName = xmlString(xmlTextReaderName(reader));
      if (nodeName == "dialog-info" && xmlTextReaderNodeType(reader) == 1)
      {
         if (xmlTextReaderHasAttributes(reader) == 1)
         {
				std::string ver = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"version"));
            doc.version = atoi(ver.c_str());

				std::string entity = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"entity"));
            // allow uri's without "sip:" prepended, manually prepend when absent
            std::string tmpUri(entity);
            if (tmpUri.compare(0, 4, "sip:") != 0)
            {
               tmpUri.insert(0, "sip:");
            }
            doc.entity = tmpUri.data();

				std::string state = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"state"));
            doc.state = stringToDialogInfoDocumentState(state.c_str());
         }
      }
      else if (nodeName == "dialog")
      {
         DialogInfo dialogInfo;
         parse(reader, dialogInfo, sharedAppearanceDialogExtensionsEnabled);
         doc.dialogs.push_back(dialogInfo);
      }
      if (*parsingError)
      {
         doc.version = 0;
         xmlFreeTextReader(reader);
         return false;
      }
      ret = xmlTextReaderRead(reader);
   }

   return (ret == 0);
}

void DialogInfoDocumentHelper::parse(xmlTextReaderPtr reader, DialogInfo& dialogInfo, bool sharedAppearanceDialogExtensionsEnabled)
{
   if (xmlTextReaderHasAttributes(reader) == 1)
   {
      if (xmlTextReaderMoveToFirstAttribute(reader) == 1)
      {
         do
         {
				std::string attribName = xmlString(xmlTextReaderName(reader));
				std::string attribVal = xmlString(xmlTextReaderValue(reader));

            if (attribName == "id") // required
            {
               dialogInfo.id = attribVal.c_str();
            }
            else if (attribName == "call-id") // optional
            {
               dialogInfo.dialogId.callId = attribVal.c_str();
            }
            else if (attribName == "local-tag") // optional
            {
               dialogInfo.dialogId.localTag = attribVal.c_str();
            }
            else if (attribName == "remote-tag") // optional
            {
               dialogInfo.dialogId.remoteTag = attribVal.c_str();
            }
            else if (attribName == "direction") // optional
            {
               dialogInfo.direction = stringToDialogDirection(attribVal.c_str());
            }
         }
         while (xmlTextReaderMoveToNextAttribute(reader) == 1);
      }

      if (xmlTextReaderRead(reader) == 1)
      {
         bool inDialogElement = true;
         while (inDialogElement)
         {
				cpc::string elementName = xmlString(xmlTextReaderName(reader)).c_str();

            if (elementName == "dialog" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_END_ELEMENT) // 15 == end element
            {
               inDialogElement = false;
            }
            else if (elementName == "state")
            {
               parse(reader, dialogInfo.stateInfo);
            }
            else if (elementName == "duration")
            {
					std::string  elementValue = xmlGetElementText(reader);
               if (elementValue.length() > 0)
               {
                  dialogInfo.duration = (UInt32)atoi(elementValue.c_str());
               }
            }
            else if (elementName == "local")
            {
               parse(reader, elementName, dialogInfo.localParticipant);
            }
            else if (elementName == "remote")
            {
               parse(reader, elementName, dialogInfo.remoteParticipant);
            }
            else if (elementName == "route-set")
            {
               if (xmlTextReaderRead(reader) == XML_READER_TYPE_ELEMENT) // first child is "hop" (required)
               {
                  do
                  {
							std::string elementValue = xmlString(xmlTextReaderValue(reader));
                     dialogInfo.routeSet.push_back(elementValue.c_str());
                  }
                  while (xmlTextReaderRead(reader) == XML_READER_TYPE_ELEMENT);
               }
            }
            else if (elementName == "referred-by")
            {
               if (xmlTextReaderHasAttributes(reader) == 1)
               {
						std::string displayNameVal = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"display-name"));
						std::string referredByVal = xmlGetElementText(reader);
                  dialogInfo.referredBy.displayName = displayNameVal.c_str();
                  dialogInfo.referredBy.address = referredByVal.c_str();
               }
            }
            else if (elementName == "replaces")
            {
               resip::Data call_id, local_tag, remote_tag;

               if (xmlTextReaderHasAttributes(reader) == 1)
               {
						call_id = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"call-id")).c_str();
                  local_tag =  xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"local-tag")).c_str();
                  remote_tag = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"remote-tag")).c_str();

                  dialogInfo.replaces.callId = call_id.c_str();
                  dialogInfo.replaces.localTag = local_tag.c_str();
                  dialogInfo.replaces.remoteTag = remote_tag.c_str();
               }
            }
            else if (sharedAppearanceDialogExtensionsEnabled)
            {
               // Support for Shared Appearance Dialog Extensions
               // The element names should normally be prefixed with the Shared Appearance ('sa') XML namespace
               Data element(elementName);

               if (element.postfix(":appearance"))
               {
                  dialogInfo.appearance = (UInt32) atoi(xmlGetElementText(reader).c_str());
               }
               else if (element.postfix(":exclusive"))
               {
                  dialogInfo.exclusive = stringToDialogExclusive(xmlGetElementText(reader).c_str());
               }
               else if (element.postfix(":callpark"))
               {
                  // parked call information (Broadworks)
                  parse(reader, elementName, dialogInfo.parkedParticipant);
               }
            }

            inDialogElement = inDialogElement && (xmlTextReaderRead(reader) == 1);
         }
      }
   }
   if (dialogInfo.id.empty())
   {
      // workaround for Grandstream issue where the mandatory id attribute is missing
      dialogInfo.id = getIdBasedOnDialogId(dialogInfo.dialogId);
   }
}

void DialogInfoDocumentHelper::parse(xmlTextReaderPtr reader, const cpc::string& elementName, ParticipantInfo& participant)
{
   if (xmlTextReaderRead(reader) == 1)
   {
      do
      {
         int isEmpty = xmlTextReaderIsEmptyElement(reader); // e.g. <target uri=".." />
         std::string parsedElementName = xmlString(xmlTextReaderName(reader));
         int nodeType = xmlTextReaderNodeType(reader);

         if (nodeType == XML_READER_TYPE_ELEMENT) // start element
         {
            if (parsedElementName == "identity") 
            {
               NameAddress na;

               if (xmlTextReaderHasAttributes(reader))
               {
                  if (xmlTextReaderMoveToFirstAttribute(reader) == 1)
                  {
                     std::string attribName = xmlString(xmlTextReaderName(reader));
                     std::string attribVal = xmlString(xmlTextReaderValue(reader));

                     if (attribName == "display-name" || attribName == "display")
                     {
                        na.displayName = attribVal.c_str();
                     }
                  }
               }

               std::string uriString = xmlGetElementText(reader);
               try
               {
                  na.address = uriString.c_str();

                  IdentityInfo idInfo;
                  PopulateIdentityInfoFromNameAddress(na, idInfo);
                  participant.identities.push_back(idInfo);
               }
               catch (std::exception&)
               {
                  // Something is wrong with the identity provided, i.e. it doesn't follow the spec completely
                  assert(0);
               }
            }
            else if (parsedElementName == "target")
            {
               if (xmlTextReaderHasAttributes(reader))
               {
                  if (xmlTextReaderMoveToFirstAttribute(reader) == 1)
                  {
							std::string attribName = xmlString(xmlTextReaderName(reader));
							std::string attribVal = xmlString(xmlTextReaderValue(reader));

                     if (attribName == "uri")
                     {
                        participant.target.uri = attribVal.c_str();
                     }
                  }
               }

               if (isEmpty == 0 && xmlTextReaderRead(reader) == 1)
               {
                  do {
                     std::string parsedElementName = xmlString(xmlTextReaderName(reader));
                     int nodeType = xmlTextReaderNodeType(reader);

                     if (nodeType == XML_READER_TYPE_ELEMENT && parsedElementName == "param")
                     {
                        std::string pnameVal = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"pname"));
                        std::string pvalVal = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"pval"));

                        Parameter targetParam(pnameVal.c_str(), pvalVal.c_str());
                        participant.target.params.push_back(targetParam);
                     }
                     else if (nodeType == XML_READER_TYPE_END_ELEMENT && parsedElementName == "target")
                     {
                        // End of parameter(s) section
                        break;
                     }
                  }
                  while (xmlTextReaderRead(reader) == 1);
               }
            }
            else if (parsedElementName == "session-description")
            {
               cpc::string mimeType("application/sdp");
               if (xmlTextReaderHasAttributes(reader))
               {
                  if (xmlTextReaderMoveToFirstAttribute(reader) == 1)
                  {
							std::string attribName = xmlString(xmlTextReaderName(reader));
                     std::string attribVal = xmlString(xmlTextReaderValue(reader));

                     if (attribName == "type")
                     {
                        mimeType = attribVal.c_str();
                     }
                  }
               }
					std::string elementTextVal = xmlGetElementText(reader);
               participant.contentType = mimeType;
               participant.sessionDescription = elementTextVal.c_str();
            }
            else if (parsedElementName == "cseq")
            {
               participant.cseq = (UInt32) atoi(xmlGetElementText(reader).c_str());
            }
         }
         else if (parsedElementName == elementName.c_str() && nodeType == XML_READER_TYPE_END_ELEMENT) // end element
         {
            break;
         }
      }
      while (xmlTextReaderRead(reader) == 1);
   }
}

void DialogInfoDocumentHelper::parse(xmlTextReaderPtr reader, DialogStateInfo& dialogStateInfo)
{
   if (xmlTextReaderHasAttributes(reader) == 1)
   {
      if (xmlTextReaderMoveToFirstAttribute(reader) == 1)
      {
         do
         {
				std::string attribName = xmlString(xmlTextReaderName(reader));
				std::string attribVal = xmlString(xmlTextReaderValue(reader));

            if (attribName == "code")
            {
               dialogStateInfo.code = (UInt32)atoi(attribVal.c_str());
            }
            else if (attribName == "reason")
            {
               dialogStateInfo.reason = stringToDialogStateReason(attribVal.c_str());
            }
         }
         while (xmlTextReaderMoveToNextAttribute(reader) == 1);
      }
   }

   int moveToElementResult = xmlTextReaderMoveToElement(reader);
   if (moveToElementResult == 1 || moveToElementResult == 0)
   {
		std::string textVal = xmlGetElementText(reader);
      dialogStateInfo.state = stringToDialogState(textVal.c_str());
   }
}

void DialogInfoDocumentHelper::PopulateIdentityInfoFromNameAddress(const NameAddress& na, IdentityInfo& idInfo)
{
   idInfo.displayName = na.displayName;
   idInfo.address = na.address;

   resip::Data data(na.address.c_str());
   resip::Uri uri(data);
   if (uri.userIsTelephoneSubscriber())
   {
      resip::Token telSub = uri.getUserAsTelephoneSubscriber();
      resip::Data ext = telSub.param(resip::p_extension);
      if (!ext.empty())
         idInfo.uriParams.push_back(Parameter("ext", ext.c_str()));
   }

   idInfo.uri = uri.getAorAsUri().toString().c_str();
}

cpc::string DialogInfoDocumentHelper::dialogStateToString(const DialogState& dialogState)
{
   switch (dialogState)
   {
   case DialogState_Confirmed:
      return "confirmed";
   case DialogState_Early:
      return "early";
   case DialogState_Proceeding:
      return "proceeding";
   case DialogState_Terminated:
      return "terminated";
   case DialogState_Trying:
      return "trying";
   case DialogState_NotSpecified:
      return "";
   default:
      assert(false);
   }

   return "";
}

cpc::string DialogInfoDocumentHelper::dialogStateReasonToString(const DialogStateReason& reason)
{
   switch (reason)
   {
   case DialogStateReason_Cancelled:
      return "cancelled";
   case DialogStateReason_Error:
      return "error";
   case DialogStateReason_LocalBye:
      return "local-bye";
   case DialogStateReason_Rejected:
      return "rejected";
   case DialogStateReason_RemoteBye:
      return "remote-bye";
   case DialogStateReason_Replaced:
      return "replaced";
   case DialogStateReason_Timeout:
      return "timeout";
   case DialogStateReason_NotSpecified:
      return "";
   default:
      assert(false);
   }

   return "";
}

cpc::string DialogInfoDocumentHelper::dialogDirectionToString(const DialogDirection& direction)
{
   switch(direction)
   {
   case DialogDirection_Initiator:
      return "initiator";
   case DialogDirection_Recipient:
      return "recipient";
   default:
      assert(false);
   }

   return "";
}

cpc::string DialogInfoDocumentHelper::dialogInfoDocumentStateToString(const DialogInfoDocumentState& state)
{
   switch(state)
   {
   case DialogInfoDocumentState_Full:
      return "full";
   case DialogInfoDocumentState_Partial:
      return "partial";
   default:
      assert(false);
   }

   return "";
}

cpc::string DialogInfoDocumentHelper::dialogExclusiveToString(bool exclusive)
{
   return exclusive ? "true" : "false";
}

bool DialogInfoDocumentHelper::stringToDialogExclusive(const cpc::string& exclusiveStr)
{
   return !strcmp(exclusiveStr.c_str(), "true");
}

DialogInfoDocumentState DialogInfoDocumentHelper::stringToDialogInfoDocumentState(const cpc::string& stateStr)
{
   if (stateStr == "full")
      return DialogInfoDocumentState_Full;
   else if (stateStr == "partial")
      return DialogInfoDocumentState_Partial;
   else
      assert(false);

   return DialogInfoDocumentState_Full;
}

DialogStateReason DialogInfoDocumentHelper::stringToDialogStateReason(const cpc::string& reasonStr)
{
   if (reasonStr == "cancelled")
      return DialogStateReason_Cancelled;
   else if (reasonStr == "rejected")
      return DialogStateReason_Rejected;
   else if (reasonStr == "replaced")
      return DialogStateReason_Replaced;
   else if (reasonStr == "local-bye")
      return DialogStateReason_LocalBye;
   else if (reasonStr == "remote-bye")
      return DialogStateReason_RemoteBye;
   else if (reasonStr == "error")
      return DialogStateReason_Error;
   else if (reasonStr == "timeout")
      return DialogStateReason_Timeout;
   else
      assert(false);

   return DialogStateReason_NotSpecified;
}

DialogState DialogInfoDocumentHelper::stringToDialogState(const cpc::string& stateStr)
{
   if (stateStr == "trying")
      return DialogState_Trying;
   else if (stateStr == "proceeding")
      return DialogState_Proceeding;
   else if (stateStr == "early")
      return DialogState_Early;
   else if (stateStr == "confirmed")
      return DialogState_Confirmed;
   else if (stateStr == "terminated")
      return DialogState_Terminated;
   else
      assert(false);

   return DialogState_NotSpecified;
}

DialogDirection DialogInfoDocumentHelper::stringToDialogDirection(const cpc::string& directionStr)
{
   if (directionStr == "initiator")
      return DialogDirection_Initiator;
   else if (directionStr == "recipient")
      return DialogDirection_Recipient;
   else
      assert(false);

   return DialogDirection_Initiator;
}

cpc::string DialogInfoDocumentHelper::getIdBasedOnDialogId(const DialogId& dialogId)
{
   resip::DialogId resipDialogId(dialogId.callId.c_str(), dialogId.localTag.c_str(), dialogId.remoteTag.c_str());

   Data urn ("urn:uuid:");
   if (resipDialogId.getCallId().size() >= 6)
   {
      urn += /*4 bytes*/resipDialogId.getCallId().substr(0,4).hex(); // time-low
      urn += "-";
      urn += /*2 bytes*/resipDialogId.getCallId().substr(4,2).hex(); // time-mid
      urn += "-";
   }

   if (resipDialogId.getLocalTag().size() >= 3)
   {
      Data time_hi_and_version = /*2 bytes*/resipDialogId.getLocalTag().substr(0,2);
      time_hi_and_version[0] &= 0x0f;
      time_hi_and_version[0] |= 0x40;
      urn += time_hi_and_version.hex();

      urn += "-";

      Data clock_seq_hi_and_reserved = /*1 byte*/resipDialogId.getLocalTag().substr(2,1);
      clock_seq_hi_and_reserved[0] &= 0x3f;
      clock_seq_hi_and_reserved[0] |= 0x40;
      urn += clock_seq_hi_and_reserved.hex();
   }

   if (resipDialogId.getRemoteTag().size() >= 7)
   {
      urn += /*1 byte*/resipDialogId.getRemoteTag().substr(0,1).hex(); // clock-seq-low
      urn += "-";
      urn += /*6 bytes*/resipDialogId.getRemoteTag().substr(1,6).hex(); // node
   }
   return urn.c_str();
}

void DialogInfoDocumentHelper::fromDialogEventInfo(int version, const cpc::string& entity, DialogInfoDocumentState documentState, resip::DialogEventStateManager::DialogEventInfos dialogEventInfos, DialogInfoDocument &doc)
{
   doc.version = version;
   doc.entity = entity;
   doc.state = documentState;

   for (resip::DialogEventStateManager::DialogEventInfos::const_iterator it = dialogEventInfos.begin(); it != dialogEventInfos.end(); it++)
   {
      DialogInfo dialogInfo;
      fromDialogEventInfo(*it, dialogInfo);
      doc.dialogs.push_back(dialogInfo);
   }
}

void DialogInfoDocumentHelper::fromDialogEventInfo(const resip::DialogEventInfo& dialogEventInfo, DialogInfo& dialogInfo)
{
   dialogInfo.id = dialogEventInfo.getDialogEventId().c_str();
   dialogInfo.dialogId.callId = dialogEventInfo.getCallId().c_str();
   dialogInfo.dialogId.localTag = dialogEventInfo.getLocalTag().c_str();
   if (dialogEventInfo.hasRemoteTag())
   {
      dialogInfo.dialogId.remoteTag = dialogEventInfo.getRemoteTag().c_str();
   }
   dialogInfo.direction = dialogDirectionToSdk(dialogEventInfo.getDirection());
   dialogInfo.stateInfo.state = dialogStateToSdk(dialogEventInfo.getState());
   dialogInfo.stateInfo.reason = DialogStateReason_NotSpecified;
   dialogInfo.stateInfo.code = 0;
   dialogInfo.duration = (unsigned long) dialogEventInfo.getDurationSeconds();
   if (dialogEventInfo.hasRefferedBy())
   {
      dialogInfo.referredBy = nameAddressToSdk(dialogEventInfo.getRefferredBy());
   }
   if (dialogEventInfo.hasRouteSet())
   {
      resip::NameAddrs routeSet = dialogEventInfo.getRouteSet();
      for (resip::NameAddrs::iterator it = routeSet.begin(); it != routeSet.end(); it++)
      {
         NameAddress nameAddress = nameAddressToSdk(*it);
         dialogInfo.routeSet.push_back(nameAddress.address);
      }
   }
   if (dialogEventInfo.hasReplacesId())
   {
      dialogInfo.replaces.callId = dialogEventInfo.getReplacesId().getCallId().c_str();
      dialogInfo.replaces.localTag = dialogEventInfo.getReplacesId().getLocalTag().c_str();
      dialogInfo.replaces.remoteTag = dialogEventInfo.getReplacesId().getRemoteTag().c_str();
   }

   IdentityInfo idInfo;
   NameAddress na = nameAddressToSdk(dialogEventInfo.getLocalIdentity());
   PopulateIdentityInfoFromNameAddress(na, idInfo);
   dialogInfo.localParticipant.identities.push_back(idInfo);
   dialogInfo.localParticipant.cseq = 0;
   if (dialogEventInfo.hasLocalTarget())
   {
      dialogInfo.localParticipant.target = targetToSdk(dialogEventInfo.getLocalTarget());
   }
   if (dialogEventInfo.hasLocalOfferAnswer())
   {
      sessionDescriptionToSdk(dialogEventInfo.getLocalOfferAnswer(), dialogInfo.localParticipant);
   }

   na = nameAddressToSdk(dialogEventInfo.getRemoteIdentity());
   PopulateIdentityInfoFromNameAddress(na, idInfo);
   dialogInfo.remoteParticipant.identities.push_back(idInfo);
   dialogInfo.remoteParticipant.cseq = 0;
   if (dialogEventInfo.hasRemoteTarget())
   {
      dialogInfo.remoteParticipant.target = targetToSdk(dialogEventInfo.getRemoteTarget());
   }
   if (dialogEventInfo.hasRemoteOfferAnswer())
   {
      sessionDescriptionToSdk(dialogEventInfo.getRemoteOfferAnswer(), dialogInfo.remoteParticipant);
   }
}

DialogDirection DialogInfoDocumentHelper::dialogDirectionToSdk(const resip::DialogEventInfo::Direction& direction)
{
   switch(direction)
   {
   case resip::DialogEventInfo::Initiator:
      return DialogDirection_Initiator;
   case resip::DialogEventInfo::Recipient:
      return DialogDirection_Recipient;
   default:
      assert(false);
   }
  
   return DialogDirection_Initiator;
}

DialogState DialogInfoDocumentHelper::dialogStateToSdk(const resip::DialogEventInfo::State& state)
{
   switch(state)
   {
   case resip::DialogEventInfo::Trying:
      return DialogState_Trying;
   case resip::DialogEventInfo::Proceeding:
      return DialogState_Proceeding;
   case resip::DialogEventInfo::Early:
      return DialogState_Early;
   case resip::DialogEventInfo::Confirmed:
      return DialogState_Confirmed;
   case resip::DialogEventInfo::Terminated:
      return DialogState_Terminated;
   default:
      assert(false);
   }
  
   return DialogState_NotSpecified;
}

NameAddress DialogInfoDocumentHelper::nameAddressToSdk(const resip::NameAddr& nameAddr)
{
   NameAddress ret;
   ret.address = nameAddr.uri().toString().c_str();
   ret.displayName = nameAddr.displayName().c_str();
   return ret;
}

TargetInfo DialogInfoDocumentHelper::targetToSdk(const resip::Uri& target)
{
   // NOTE: target parameters not available from resip
   TargetInfo ret;
   ret.uri = target.toString().c_str();
   return ret;
}

void DialogInfoDocumentHelper::sessionDescriptionToSdk(const resip::Contents &contents, ParticipantInfo& participant)
{
   participant.sessionDescription = contents.getBodyData().c_str();
   participant.contentType = cpc::string(contents.getType().type().c_str()) + "/" + cpc::string(contents.getType().subType().c_str());
}

DialogStateReason DialogInfoDocumentHelper::dialogStateToSdk(const InviteSessionHandler::TerminatedReason& reason)
{
   switch(reason)
   {
   case resip::InviteSessionHandler::Error:
      return DialogStateReason_Error;
   case resip::InviteSessionHandler::Timeout:
      return DialogStateReason_Timeout;
   case resip::InviteSessionHandler::Replaced:
      return DialogStateReason_Replaced;
   case resip::InviteSessionHandler::LocalBye:
      return DialogStateReason_LocalBye;
   case resip::InviteSessionHandler::RemoteBye:
      return DialogStateReason_RemoteBye;
   case resip::InviteSessionHandler::LocalCancel:
   case resip::InviteSessionHandler::RemoteCancel:
      return DialogStateReason_Cancelled;
   case resip::InviteSessionHandler::Referred:
   case resip::InviteSessionHandler::Rejected:
      return DialogStateReason_Rejected;
   default:
      assert(false);
   }
  
   return DialogStateReason_NotSpecified;
}

void DialogInfoDocumentHelper::setDialogStateReasonAndCode(DialogInfoDocument& doc, const resip::TerminatedDialogEvent &dialogEvent)
{
   // Find the dialog in the document
   // Set the dialog state's reason and code with their corresponding values in the event 
   for (cpc::vector<DialogInfo>::iterator it = doc.dialogs.begin(); it != doc.dialogs.end(); it++)
   {
      if (it->id == dialogEvent.getEventInfo().getDialogEventId())
      {
         it->stateInfo.reason = dialogStateToSdk(dialogEvent.getTerminatedReason());
         it->stateInfo.code = dialogEvent.getResponseCode();
         break;
      }
   }
}

void DialogInfoDocumentHelper::setDialogStateReasonAndCode(DialogInfoDocument& doc, const resip::MultipleEventDialogEvent &dialogEvent)
{
   // Find the terminated dialog events in the list
   // Set the dialog's state reason and code with their corresponding values in the dialog terminated event
   for (resip::MultipleEventDialogEvent::EventVector::const_iterator it = dialogEvent.getEvents().begin(); it != dialogEvent.getEvents().end(); it++)
   {
      resip::SharedPtr<DialogEvent> eventPtr = *it;
      if (eventPtr->getType() == DialogEvent::DialogEventType_Terminated)
      {
         TerminatedDialogEvent* terminatedDialogEvent = dynamic_cast<TerminatedDialogEvent*>(eventPtr.get());
         if (terminatedDialogEvent)
         {
            DialogInfoDocumentHelper::setDialogStateReasonAndCode(doc, *terminatedDialogEvent);
         }
      }
   }
}

DialogInfoDocument DialogInfoDocumentHelper::createEmptyDocument(int version, const cpc::string& entity)
{
   DialogInfoDocument dialogInfoDoc;
   dialogInfoDoc.version = version;
   dialogInfoDoc.entity = entity;
   dialogInfoDoc.state = DialogInfoDocumentState_Full;   
   return dialogInfoDoc;
}

SipEventState DialogInfoDocumentHelper::createEventState(const DialogInfoDocument& dialogInfoDoc, bool includeSessionDescription, bool sharedAppearanceDialogExtensionsEnabled)
{
   // Convert the document to XML
   cpc::string dialogInfoDocXml = toXml(dialogInfoDoc, includeSessionDescription, sharedAppearanceDialogExtensionsEnabled);

   // Create the event state
   SipEventState eventState;
   eventState.eventPackage = DialogInfoDocumentHelper::EVENT_PACKAGE_NAME;
   eventState.expiresTimeMs = 3600;
   eventState.mimeType = DialogInfoDocumentHelper::EVENT_MIME_TYPE.mimeType;
   eventState.mimeSubType = DialogInfoDocumentHelper::EVENT_MIME_TYPE.mimeSubType;
   eventState.contentLength = dialogInfoDocXml.size();
   eventState.contentUTF8 = dialogInfoDocXml;

   return eventState;
}

}
}