#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)

#include "SipMsrpMessagingManager.h"
#include "../util/cpc_logger.h"
#include "../util/IpHelpers.h"

#include <resip/dum/ClientInviteSession.hxx>
#include <resip/dum/ServerInviteSession.hxx>

#include <msrp_stack.h>
#include <msrp_session.h>
#include <msrp_message.h>
#include <msrp_uri.h>
#include <msrp_content_type.h>
#include <utils/msrp_strndup.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_FILETRANSFER

namespace CPCAPI2
{
namespace CPM
{

SipMsrpMessagingManager::SipMsrpMessagingManager(CPCAPI2::SipAccount::SipAccountImpl& account)
   : account(account),
     isMsrpLibInitialized(false),
     msrpListeningPort(0),
     msrpStack(NULL)
{
}

SipMsrpMessagingManager::~SipMsrpMessagingManager()
{
   // Destroy the MSRP stack
   if (msrpStack)
   {
      msrp_stack_destroy(msrpStack);
      msrpStack = NULL;
   }
}

int SipMsrpMessagingManager::adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile)
{
   if (!profile->isMethodSupported(resip::INVITE)) profile->addSupportedMethod(resip::INVITE);
   if (!profile->isMethodSupported(resip::ACK)) profile->addSupportedMethod(resip::ACK);
   if (!profile->isMethodSupported(resip::CANCEL)) profile->addSupportedMethod(resip::CANCEL);
   if (!profile->isMethodSupported(resip::BYE)) profile->addSupportedMethod(resip::BYE);

   return kSuccess;
}

int SipMsrpMessagingManager::onDumBeingDestroyed()
{
   dum.reset();
   return kSuccess;
}

int SipMsrpMessagingManager::onRegistrationSuccess(const SipAccount::SipRegistrationSuccessEvent& args)
{
   regServer = args.server;
   return kSuccess;
}

void SipMsrpMessagingManager::release()
{
   this->releaseImpl(); // will delete this
}

int SipMsrpMessagingManager::addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig)
{
   // Keep a reference to the DUM
   this->dum = dum;

   return kSuccess;
}

void SipMsrpMessagingManager::onNewSession(ClientInviteSessionHandle cish, InviteSession::OfferAnswerType oat, const SipMessage& msg)
{
   // Get the session information associated with the SIP session received
   SipMsrpMessagingSessionInfo* sessionInfo = dynamic_cast<SipMsrpMessagingSessionInfo*>(cish->getAppDialogSet().get());
   assert(sessionInfo);
   assert(sessionInfo->handle != 0); // Should be set elsewhere

   // Populate the the session information object
   sessionInfo->clientInviteSessionHandle = cish;
   sessionInfo->accountHandle = account.getHandle();
   sessionInfo->isActive = true; // If we make the call, active is initially assumed
   resip::Uri toUri = msg.header(resip::h_To).uri();
   sessionInfo->remotePartyAddress = resip::NameAddr(toUri);

   // Add the info now
   addSessionInfo(sessionInfo->handle, sessionInfo);
}

void SipMsrpMessagingManager::onNewSession(ServerInviteSessionHandle sish, InviteSession::OfferAnswerType oat, const SipMessage& msg)
{
   // Send 180/Ringing response to give more time for the session invitee to accept (or reject) the invite
   sish->provisional();

   // Get the session information associated with the SIP session received
   SipMsrpMessagingSessionInfo* sessionInfo = dynamic_cast<SipMsrpMessagingSessionInfo*>(sish->getAppDialogSet().get());
   assert(sessionInfo);
   assert(sessionInfo->handle != 0); // Should be set elsewhere

   // Populate the the session information object
   sessionInfo->serverInviteSessionHandle = sish;
   sessionInfo->accountHandle = account.getHandle();
   sessionInfo->isActive = false; // If we receive the call, passive is initially assumed
   resip::Uri fromUri = msg.header(resip::h_From).uri();
   sessionInfo->remotePartyAddress = resip::NameAddr(fromUri);

   // Add the info now, but fire event later
   addSessionInfo(sessionInfo->handle, sessionInfo);
}

void SipMsrpMessagingManager::onOffer(InviteSessionHandle ish, const SipMessage& msg, const SdpContents& contents)
{
   // Get the session information associated with the SIP session received
   SipMsrpMessagingSessionInfo* sessionInfo = dynamic_cast<SipMsrpMessagingSessionInfo*>(ish->getAppDialogSet().get());
   assert(sessionInfo);

   // Find the first media of type message
   SdpContents::Session::Medium medium = CpmHelper::getMedium(contents, CpmHelper::MESSAGE_MEDIA_TYPE);
   assert(!medium.name().empty());

   // Set the local and remote URIs
   assert(!sessionInfo->localUri);
   assert(!sessionInfo->remoteUri);
   std::list<resip::Data> pathList = medium.getValues(CpmHelper::MSRP_PATH);
   assert(pathList.size() == 1);
   const char* msrpPath = pathList.front().c_str();
   sessionInfo->remoteUri = msrp_uri_parse(msrpPath, NULL);
   resip::Data localIp = IpHelpers::getPreferredLocalIpAddress();
   if (!regServer.isAnyInterface())
   {
      IpHelpers::getPreferredLocalIpAddress(regServer, localIp);
   }
   sessionInfo->localUri = CpmHelper::createLocalMsrpUri(localIp, msrpListeningPort);

   // Set whether this MSRP endpoint is active or not
   CpmHelper::setActiveMsrpEndpoint(contents, sessionInfo->isActive);
}

void SipMsrpMessagingManager::onOfferRequired(InviteSessionHandle ish, const SipMessage& msg)
{
   ish->reject(488);
}

void SipMsrpMessagingManager::onAnswer(InviteSessionHandle ish, const SipMessage& msg, const SdpContents& contents)
{
   // Get the session information associated with the SIP session received
   SipMsrpMessagingSessionInfo* sessionInfo = dynamic_cast<SipMsrpMessagingSessionInfo*>(ish->getAppDialogSet().get());
   assert(sessionInfo);

   // Find the first media of type message
   SdpContents::Session::Medium medium = CpmHelper::getMedium(contents, CpmHelper::MESSAGE_MEDIA_TYPE);
   assert(!medium.name().empty());

   // Set the remote URI
   assert(sessionInfo->localUri);
   assert(!sessionInfo->remoteUri);
   std::list<resip::Data> pathList = medium.getValues(CpmHelper::MSRP_PATH);
   assert(pathList.size() == 1);
   const char* msrpPath = pathList.front().c_str();
   sessionInfo->remoteUri = msrp_uri_parse(msrpPath, NULL);

   // Create the MSRP session
   assert(!sessionInfo->msrpSession);
   CpmHelper::setActiveMsrpEndpoint(contents, sessionInfo->isActive);
   sessionInfo->msrpSession = msrp_stack_session_create(msrpStack, sessionInfo->localUri, sessionInfo->remoteUri, sessionInfo->isActive);

   // Install the MSRP session callbacks
   msrp_session_callbacks_t msrpSessionCallbacks;
   msrp_memset(&msrpSessionCallbacks, 0, sizeof(msrp_session_callbacks_t));
   msrpSessionCallbacks.p_message_created_func = msrp_session_message_created;
   msrp_session_install_callbacks(sessionInfo->msrpSession, &msrpSessionCallbacks); 

   // Set the MSRP session's user data object
   CallbackInfo *userData = new CallbackInfo(); // Destroyed in onTerminated()
   userData->_this = this;
   userData->session = sessionInfo->handle;
   msrp_session_set_user_data(sessionInfo->msrpSession, (intptr_t) userData);
}

void SipMsrpMessagingManager::onTerminated(InviteSessionHandle ish, InviteSessionHandler::TerminatedReason reason, const SipMessage* related)
{
   // Get the session information associated with the SIP session received
   SipMsrpMessagingSessionInfo* sessionInfo = dynamic_cast<SipMsrpMessagingSessionInfo*>(ish->getAppDialogSet().get());
   assert(sessionInfo);

   // Destroy the MSRP session now
   if (sessionInfo->msrpSession)
   {
      // Destroy the associated user data
      CallbackInfo* callbackInfo = (CallbackInfo*) msrp_session_get_user_data(sessionInfo->msrpSession);
      assert(callbackInfo);      
      delete callbackInfo;

      // Destroy the session
      msrp_stack_session_destroy(msrpStack, sessionInfo->msrpSession);
      sessionInfo->msrpSession = NULL;      
   }

   // Remove the session info from the collection. It will be destoyed by resip
   SipMsrpMessagingSessionInfoMap::const_iterator it = sessionInfoMap.find(sessionInfo->handle);
   if (it != sessionInfoMap.end())
   {
      //SipMsrpMessagingSessionInfo* sessionInfo = it->second;
      sessionInfoMap.erase(it);
   }
}

void SipMsrpMessagingManager::rejectSession(SipMsrpMessagingSessionInfo* sessionInfo, unsigned int reasonCode)
{
   assert(sessionInfo);

   // Verify that the incoming SIP session is valid
   assert(sessionInfo->serverInviteSessionHandle.isValid());

   // Send an error response to the incoming INVITE
   sessionInfo->serverInviteSessionHandle->reject(reasonCode);
}

void SipMsrpMessagingManager::startSession(SipMsrpMessagingSessionInfo* sessionInfo, const cpc::string& featureTag)
{
   assert(sessionInfo);

   // NOTE: The SIP session begins. Note, we w't actually know the remote MSRP information until
   //       after the 200/OK comes back and we can process the SDP.

   // Set the local URI
   assert(!sessionInfo->localUri);
   resip::Data localIp = IpHelpers::getPreferredLocalIpAddress();
   if (!regServer.isAnyInterface())
   {
      IpHelpers::getPreferredLocalIpAddress(regServer, localIp);
   }
   sessionInfo->localUri = CpmHelper::createLocalMsrpUri(localIp, msrpListeningPort);

   // Create the SDP to put in the INVITE
   const resip::Data& sdpStr = buildSdp(sessionInfo->localUri, msrp_stack_get_listening_port(msrpStack), CpmHelper::CPIM_CONTENT_TYPE, sessionInfo->isActive);
   resip::HeaderFieldValue hfv(sdpStr.c_str(), (unsigned int) sdpStr.size());
   resip::SdpContents sdp(hfv, CpmHelper::SDP_CONTENT_TYPE);

   // Create the INVITE session and send
   resip::SharedPtr<resip::SipMessage> invite = dum->makeInviteSession(sessionInfo->remotePartyAddress, &sdp, resip::DialogUsageManager::None, 0, sessionInfo);
   invite->header(resip::h_ContentType) = CpmHelper::SDP_CONTENT_TYPE;
   CpmHelper::set3gppFeatureTag(*invite, featureTag);
   dum->send(invite);
}

void SipMsrpMessagingManager::acceptSession(SipMsrpMessagingSessionInfo* sessionInfo)
{
   assert(sessionInfo);

   // Verify that the incoming SIP session is valid
   assert(sessionInfo->serverInviteSessionHandle.isValid());

   // Create the MSRP session
   assert(sessionInfo->localUri);
   sessionInfo->msrpSession = msrp_stack_session_create(msrpStack, sessionInfo->localUri, sessionInfo->remoteUri, sessionInfo->isActive);

   // Install the MSRP session callbacks
   msrp_session_callbacks_t msrpSessionCallbacks;
   msrp_memset(&msrpSessionCallbacks, 0, sizeof(msrp_session_callbacks_t));
   msrpSessionCallbacks.p_message_created_func = msrp_session_message_created;
   msrp_session_install_callbacks(sessionInfo->msrpSession, &msrpSessionCallbacks); 

   // Set the MSRP session's user data object
   CallbackInfo *userData = new CallbackInfo(); // Destroyed in onTerminated()
   userData->_this = this;
   userData->session = sessionInfo->handle;
   msrp_session_set_user_data(sessionInfo->msrpSession, (intptr_t) userData);

   // Send a 200/OK back with SDP
   const resip::Data& sdpStr = buildSdp(sessionInfo->localUri, msrp_stack_get_listening_port(msrpStack), CpmHelper::CPIM_CONTENT_TYPE, sessionInfo->isActive);
   resip::HeaderFieldValue hfv(sdpStr.c_str(), (unsigned int) sdpStr.size());
   SdpContents sdp(hfv, CpmHelper::SDP_CONTENT_TYPE);
   sessionInfo->serverInviteSessionHandle->provideAnswer(sdp);
   sessionInfo->serverInviteSessionHandle->accept();
}

void SipMsrpMessagingManager::terminateSession(SipMsrpMessagingSessionInfo *sessionInfo)
{
   assert(sessionInfo);

   if (sessionInfo->clientInviteSessionHandle.isValid())
   {
      assert(!sessionInfo->serverInviteSessionHandle.isValid());

      // Terminate the SIP session (UAC side)
      sessionInfo->clientInviteSessionHandle->end();

      // NOTE: onTerminated() will be called, and most actions/events are fired
      //       in that code
   } 
   else if (sessionInfo->serverInviteSessionHandle.isValid())
   {
      assert(!sessionInfo->clientInviteSessionHandle.isValid());

      // Terminate the SIP session (UAS side)
      sessionInfo->serverInviteSessionHandle->end(resip::InviteSession::NotSpecified);

      // NOTE: onTerminated() will be called, and most actions/events are fired
      //       in that code
   } 
   else
   {
      // No SIP session established

      // Remove the session info from the collection
      SipMsrpMessagingSessionInfoMap::const_iterator it = sessionInfoMap.find(sessionInfo->handle);
      if (it != sessionInfoMap.end())
      {
         SipMsrpMessagingSessionInfo* sessionInfo = it->second;
         sessionInfoMap.erase(it);
         delete sessionInfo;
      }
   }
}

void SipMsrpMessagingManager::sendMessage(SipMsrpMessagingSessionInfo* sessionInfo, const cpc::string& message, const CpimMessage& cpimMessage, MessageType messageType)
{
   // Convert the content (CPIM) to a format that can be stored in the MSRP message
   resip::Data cpimMessageBytes = cpimMessage.toBytes();
   long dataLength = cpimMessageBytes.size();
   uint8_t* data = new uint8_t[ (unsigned int) dataLength ]; // Changed to use new[] to avoid allocator mismatch
   memcpy( data, cpimMessageBytes.c_str(), dataLength );

   // Create the MSRP message
   msrp_message_t* msrpMessage = msrp_message_create(msrp_stack_create_mid(), dataLength, MSRP_FALSE);
   msrp_message_set_content_type(msrpMessage, msrp_content_type_create_from_strings(CpmHelper::CPIM_CONTENT_TYPE.type().c_str(), CpmHelper::CPIM_CONTENT_TYPE.subType().c_str()));

   // Install the MSRP message's callbacks
   msrp_message_callbacks_t msrpMessageCallbacks;
   msrp_memset(&msrpMessageCallbacks, 0, sizeof(msrp_message_callbacks_t));
   msrpMessageCallbacks.p_message_destroyed_func = msrp_message_destroyed;
   msrpMessageCallbacks.p_message_send_complete_func = msrp_message_send_complete;
   msrp_message_install_callbacks(msrpMessage, &msrpMessageCallbacks);

   // Set the MSRP message's user data object
   CallbackInfo *userData = new CallbackInfo(); // Destroyed in msrp_message_destroyed()
   userData->_this = this;
   userData->session = sessionInfo->handle;
   userData->message = message;
   userData->messageType = messageType;
   msrp_message_set_user_data(msrpMessage, (intptr_t) userData);

   // Set backing store and start the message sending
   msrp_message_set_backing_store(msrpMessage, data);
   msrp_session_message_send(sessionInfo->msrpSession, msrpMessage);
}

resip::Data SipMsrpMessagingManager::buildSdp(msrp_uri_t* localUri, UInt32 localMediaPort, const resip::Mime& cpimContentType, bool isActive)
{
   return CpmHelper::buildSdp(localUri, localMediaPort, cpimContentType, isActive);
}

void SipMsrpMessagingManager::msrp_log_callback(msrp_log_level_enum_t level, ...)
{
   // todo: fix warning "passing an object that undergoes default argument promotion to 'va_start' has undefined behavior [-Wvarargs]"
   //va_list args;
   //va_start(args, level);
   //const char* fmt = va_arg(args, char*);
   //CpmHelper::logMsrpMessage(level, fmt, args);
   //va_end(args);
}

void SipMsrpMessagingManager::msrp_session_message_created(msrp_session_t* msrpSession, msrp_message_t* msrpMessage, intptr_t userData)
{
   assert(msrpSession);
   assert(msrpMessage);
   assert(userData);

   uint64_t messageLength = msrp_message_get_total_size(msrpMessage);
   uint8_t* data = new uint8_t[(unsigned int) messageLength]; // Destroyed in msrp_message_destroyed()

   // Install the MSRP message's callbacks
   msrp_message_callbacks_t msrpMessageCallbacks;
   msrp_memset(&msrpMessageCallbacks, 0, sizeof(msrp_message_callbacks_t));
   msrpMessageCallbacks.p_message_recv_complete_func = msrp_message_recv_complete;
   msrpMessageCallbacks.p_message_destroyed_func = msrp_message_destroyed;
   msrp_message_install_callbacks(msrpMessage, &msrpMessageCallbacks);

   // Set the MSRP message's user data object (clone the session's user data object)
   CallbackInfo* callbackInfo = (CallbackInfo*) userData;
   CallbackInfo* clonedCallbackInfo = new CallbackInfo(*callbackInfo); // Destroyed in msrp_message_destroyed()
   msrp_message_set_user_data(msrpMessage, (intptr_t) clonedCallbackInfo);

   // Set the MSRP message's backing store
   msrp_message_set_backing_store(msrpMessage, data);
}

void SipMsrpMessagingManager::msrp_message_recv_complete(msrp_message_t* msrpMessage, intptr_t userData)
{
   assert(msrpMessage);
   assert(userData);

   // Get the content of the backing store and its total size
   uint8_t* data = msrp_message_get_backing_store(msrpMessage);
   uint64_t dataLength = msrp_message_get_total_size(msrpMessage); 

   // Process the incoming message
   CallbackInfo* info = (CallbackInfo*) userData;
   info->_this->onMsrpMessageRecvComplete(msrpMessage, info->session, data, dataLength);
}

void SipMsrpMessagingManager::msrp_message_send_complete(msrp_message_t *msrpMessage, intptr_t userData)
{
   assert(msrpMessage);
   assert(userData);

   CallbackInfo* info = (CallbackInfo*) userData;
   info->_this->onMsrpMessageSendComplete(msrpMessage, info->session, info->message, info->messageType);
}

void SipMsrpMessagingManager::msrp_message_destroyed(msrp_message_t *msrpMessage, intptr_t userData)
{
   assert(msrpMessage);
   assert(userData);

   // Delete the MSRP message's backing store
   uint8_t* data = msrp_message_get_backing_store(msrpMessage);
   delete [] data;

   // Delete the MSRP message's callback user data
   CallbackInfo* info = (CallbackInfo*) userData;
   delete info;
   msrp_message_set_user_data(msrpMessage, 0);
}

void SipMsrpMessagingManager::process(resip::ReactorEventHandler::FdSetType& fdset)
{
   if (!isMsrpLibInitialized)
   {
      // Calls WSAInitialize on windows, best to do this in the same thread that will do
      // all the processing.
      msrp_lib_init(msrp_log_callback);

      // Create the MSRP stack and assign a listening port
      msrpStack = msrp_stack_create();
      msrpListeningPort = msrp_stack_get_free_port(msrpStack, "tcp");
      msrp_stack_set_listening_port(msrpStack, msrpListeningPort);
      isMsrpLibInitialized = true;
   }

   // Pump the msrp stack
   msrp_stack_process(msrpStack);
}

void SipMsrpMessagingManager::buildFdSet(resip::ReactorEventHandler::FdSetType& fdset)
{
   // NOTE: this method is called BEFORE process.
   if (!isMsrpLibInitialized)
   {
      return;
   }

   fd_set readset, writeset, exceptset;
   msrp_list_t *p_allfds = NULL;
   msrp_list_enum_t *p_enum = NULL;
   msrp_socket_t cur  = 0;

   // make sure the fd_sets are zeroed
   FD_ZERO(&readset);
   FD_ZERO(&writeset);
   FD_ZERO(&exceptset);

   // get the file descriptors from the msrp stack
   p_allfds = msrp_list_create();
   msrp_stack_get_fd_sets(msrpStack, p_allfds, &readset, &writeset, &exceptset);

   // fill the parameter "fdset" from the results. As it turns out
   // a "resip::Socket" and an msrp_socket_t are the same thing.
   p_enum = msrp_list_enum_create( p_allfds );
   while( msrp_list_enum_next( p_enum, ( void ** ) &cur ))
   {
      if (FD_ISSET(cur, &readset))
         fdset.setRead(cur);

      if (FD_ISSET(cur, &writeset))
         fdset.setWrite(cur);

      if (FD_ISSET(cur, &exceptset))
         fdset.setExcept(cur);
   }
   msrp_list_enum_destroy(p_enum);
   msrp_list_destroy(p_allfds);
}

unsigned int SipMsrpMessagingManager::getTimeTillNextProcessMS()
{
   time_t seconds = msrp_stack_get_min_timeout_seconds(msrpStack);
   return (unsigned int) (seconds * 1000);
}

SipMsrpMessagingSessionInfo *SipMsrpMessagingManager::getSessionInfo(int handle) const
{
   SipMsrpMessagingSessionInfoMap::const_iterator it = sessionInfoMap.find(handle);
   return (it != sessionInfoMap.end()) ? it->second : NULL;
}

void SipMsrpMessagingManager::addSessionInfo(int handle, SipMsrpMessagingSessionInfo* sessionInfo)
{
   assert(sessionInfoMap[handle] == NULL || sessionInfoMap[handle] == sessionInfo);
      
   // Keep the mapping session handle -> session information
   sessionInfoMap[handle] = sessionInfo;
}

}
}

#endif // CPCAPI2_BRAND_SIP_CHAT_MODULE
