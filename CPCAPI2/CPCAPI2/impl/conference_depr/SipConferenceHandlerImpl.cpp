#include "brand_branded.h"

#if (CPCAPI2_BRAND_CONFERENCE_MODULE == 1)
#include "cpcapi2utils.h"
#include "SipConferenceHandlerImpl.h"

using namespace CPCAPI2::SipEvent;
using namespace resip;

namespace CPCAPI2
{
namespace SipConference
{

SipConferenceHandlerImpl::SipConferenceHandlerImpl(CPCAPI2::SipAccount::SipAccountImpl& acct, 
												   CPCAPI2::SipEvent::SipEventManagerInterface& sipEventIf,
												   CPCAPI2::SipConference::SipConferenceManagerImpl& confManager)
	: subscriptionHandler(NULL),
     account(acct),
	 sipEventIf(sipEventIf),
	 confManager(confManager)
{
}

void SipConferenceHandlerImpl::setSubscriptionHandler(SipConferenceHandler* handler)
{
	subscriptionHandler = handler;
}

int SipConferenceHandlerImpl::onNewSubscription(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NewSubscriptionEvent& args)
{
	return kSuccess;
}

int SipConferenceHandlerImpl::onSubscriptionEnded(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionEndedEvent& args)
{
	return kSuccess;
}

int SipConferenceHandlerImpl::onIncomingEventState(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingEventStateEvent& args)
{
	return kSuccess;
}

int SipConferenceHandlerImpl::onSubscriptionStateChanged(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionStateChangedEvent& args)
{
	return kSuccess;
}

int SipConferenceHandlerImpl::onNotifySuccess(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifySuccessEvent& args)
{
	return kSuccess;
}

int SipConferenceHandlerImpl::onNotifyFailure(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifyFailureEvent& args)
{
	return kSuccess;
}

int SipConferenceHandlerImpl::onError(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::ErrorEvent& args)
{
	return kSuccess;
}

int SipConferenceHandlerImpl::onParticipationApproval(SipConferenceHandle conference, const cpc::string& targetAddress)
{
	return kSuccess;
}

int SipConferenceHandlerImpl::onParticipationNotAllowed(SipConferenceHandle conference, const cpc::string& targetAddress)
{
	return kSuccess;
}


/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// SipConversationHandler interface
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

int SipConferenceHandlerImpl::onNewConversation(SipConversationHandle conversation, const NewConversationEvent& args){return kSuccess;}

int SipConferenceHandlerImpl::onConversationEnded(SipConversationHandle conversation, const ConversationEndedEvent& args){return kSuccess;}

int SipConferenceHandlerImpl::onIncomingTransferRequest(SipConversationHandle conversation, const TransferRequestEvent& args){return kSuccess;}
int SipConferenceHandlerImpl::onIncomingRedirectRequest(SipConversationHandle conversation, const RedirectRequestEvent& args){return kSuccess;}
int SipConferenceHandlerImpl::onIncomingTargetChangeRequest(SipConversationHandle conversation, const TargetChangeRequestEvent& args){return kSuccess;}
int SipConferenceHandlerImpl::onIncomingHangupRequest(SipConversationHandle conversation, const HangupRequestEvent& args){return kSuccess;}
int SipConferenceHandlerImpl::onIncomingBroadsoftTalkRequest(SipConversationHandle conversation, const BroadsoftTalkEvent& args){return kSuccess;}
int SipConferenceHandlerImpl::onIncomingBroadsoftHoldRequest(SipConversationHandle conversation, const BroadsoftHoldEvent& args){return kSuccess;}
int SipConferenceHandlerImpl::onTransferProgress(SipConversationHandle conversation, const TransferProgressEvent& args){return kSuccess;}

int SipConferenceHandlerImpl::onConversationStateChangeRequest(SipConversationHandle conversation, const ConversationStateChangeRequestEvent& args){return kSuccess;}
int SipConferenceHandlerImpl::onConversationStateChanged(SipConversationHandle conversation, const ConversationStateChangedEvent& args)
{
	cpc::string focusUri = "";

	if(args.conversationState == ConversationState::ConversationState_Connected)
	{
	
		if(!args.contactHeaderField.empty())
		{
			if(parseContactField(args.contactHeaderField, focusUri))
			{
				confManager.setServerConfFocusUri(conversation, focusUri);
				confManager.callServerConfParticipants(conversation);
				return kSuccess;
			}
		}
		confManager.inviteToServerConference(conversation, "Add to conference.");
	}
	return kSuccess;
}

int SipConferenceHandlerImpl::onConversationMediaChangeRequest(SipConversationHandle conversation, const ConversationMediaChangeRequestEvent& args){return kSuccess;}
int SipConferenceHandlerImpl::onConversationMediaChanged(SipConversationHandle conversation, const ConversationMediaChangedEvent& args){return kSuccess;}

int SipConferenceHandlerImpl::onConversationStatisticsUpdated(SipConversationHandle conversation, const ConversationStatisticsUpdatedEvent& args){return kSuccess;}

int SipConferenceHandlerImpl::onError(SipConversationHandle subscription, const CPCAPI2::SipConversation::ErrorEvent& args){return kSuccess;}

bool SipConferenceHandlerImpl::parseContactField(cpc::string contactHeaderField, cpc::string &focusUri)
{
	size_t start;
	size_t end;

	if(contactHeaderField.find("isFocus") == cpc::string::npos)
	{
		return false;
	}
	if(start = contactHeaderField.find("<") == cpc::string::npos)
	{ 
		return false;
	}
	if(end = contactHeaderField.find(">") == cpc::string::npos)
	{ 
		return false;
	}
	start++;
	end--;
	focusUri = contactHeaderField.substr(start, end - start);

	return true;
}

}
}

#endif // CPCAPI2_BRAND_CONFERENCE_MODULE