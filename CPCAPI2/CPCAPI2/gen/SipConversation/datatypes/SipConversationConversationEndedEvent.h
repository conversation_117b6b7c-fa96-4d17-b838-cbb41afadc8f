#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>
#include "ConversationState.h"
#include "ConversationEndReason.h"

#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONCONVERSATIONENDEDEVENT_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONCONVERSATIONENDEDEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationConversationEndedEvent
  {
  public:
    std::optional<int32_t> conversationHandle;
    std::optional<ConversationState> conversationState;
    std::optional<ConversationEndReason> endReason;
    std::optional<int32_t> sipResponseCode;

    SipConversationConversationEndedEvent() = default;

    SipConversationConversationEndedEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationConversationEndedEvent& o)
    {
      j = nlohmann::json::object();
       if (o.conversationHandle) { j["conversationHandle"] = *(o.conversationHandle); }
     if (o.conversationState) { j["conversationState"] = *(o.conversationState); }
     if (o.endReason) { j["endReason"] = *(o.endReason); }
     if (o.sipResponseCode) { j["sipResponseCode"] = *(o.sipResponseCode); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationConversationEndedEvent& o)
    {
     if (j.contains("conversationHandle")) { o.conversationHandle = j.at("conversationHandle"); }
     if (j.contains("conversationState")) { o.conversationState = j.at("conversationState"); }
     if (j.contains("endReason")) { o.endReason = j.at("endReason"); }
     if (j.contains("sipResponseCode")) { o.sipResponseCode = j.at("sipResponseCode"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONCONVERSATIONENDEDEVENT_H
