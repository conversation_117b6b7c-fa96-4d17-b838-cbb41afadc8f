#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_MEDIATYPE_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_MEDIATYPE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  enum class MediaType
  {
    Audio,
    Video
  };

  inline std::string to_string(MediaType value)
  {
    switch (value)
    {
      case MediaType::Audio:
        return "Audio";
      case MediaType::Video:
        return "Video";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, MediaType value)
  {
    switch (value)
    {
      case MediaType::Audio:
        os << "Audio";
        break;
      case MediaType::Video:
        os << "Video";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const MediaType& e)
  {
    switch (e)
    {
      case MediaType::Audio:
        j = "Audio";
        break;
      case MediaType::Video:
        j = "Video";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, MediaType& e)
  {
      if (0 == j.get<std::string>().compare("Audio"))
    {
      e = MediaType::Audio;
    }
    else if (0 == j.get<std::string>().compare("Video"))
    {
      e = MediaType::Video;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum MediaType"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_MEDIATYPE_H
