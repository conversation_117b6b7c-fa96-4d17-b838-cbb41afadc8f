#include "MosEstimator.h"

#include <math.h>
#include "../util/cpc_logger.h"
#include "brand_branded.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_CALL

#if (CPCAPI2_BRAND_CALL_MODULE == 1)

#include <cmath>

using namespace CPCAPI2;

short MosEstimator::calculateNetworkMos(unsigned short localFractionLost, unsigned int totalReceivedPackets,
   int64_t rttMs, unsigned int averageJitterMs, unsigned int numIntervalsWithoutUpdate)
{

   // from https://technet.microsoft.com/en-ca/library/bb894481(v=office.12).aspx :
   //   
   //
   // Listening MOS
   //   Listening MOS is a prediction of the wideband Listening Quality(MOS - LQ)) of the audio stream 
   //   that is played to the user.This value takes into consideration the audio fidelity and distortion and 
   //    speech and noise levels, and from this data predicts how a large group of users would rate the quality 
   //    of the audio they hear.
   //
   //   The Listening MOS varies depending on :
   //      
   //     - The codec used
   //     - A wideband or narrowband codec
   //     - The characteristics of the audio capture device used by the person speaking(person sending the audio).
   //     - Any transcoding or mixing that occurred
   //     - Defects from packet loss or packet loss concealment
   //     - The speech level and background noise of the person speaking(person sending the audio)
   //
   //   Due to the large number of factors that influence this value, it is most useful to view the Listening MOS 
   //   statistically rather than by using a single call.
   //
   //
   //
   // Network MOS
   //   Network MOS is a prediction of the wideband Listening Quality Mean Opinion Score(MOS - LQ) 
   //   of audio that is played to the user.This value takes into consideration only network factors 
   //   such as codec used, packet loss, packet reorder, packet errors and jitter.
   //   The difference between Network MOS and Listening MOS is that the Network MOS considers only 
   //   the impact of the network on the listening quality, whereas Listening MOS also considers the 
   //   payload(speech level, noise level, etc).This makes Network MOS useful for identifying network 
   //   conditions impacting the audio quality being delivered.
   //



   // Refresh is usually around every 2 seconds. RTCP updates usually come around
   // every 5 seconds. So make the number of intervals > 3 to be safe.
   if (numIntervalsWithoutUpdate > 3)
   {
      return 0;
   }

   // note: averageJitterMs appears to be a running average, over the whole call. if so, it does not really fit into the 'snapshot' of MOS 
   // we are attempting to calculate here.

   double receivedPercentLoss = 0;
   if (localFractionLost > 0)
   {
      receivedPercentLoss = localFractionLost * 100.0 / 255.0;
   }

#if 1
   // https://www.pingman.com/kb/article/how-is-mos-calculated-in-pingplotter-pro-50.html

   double R;
   double effectiveLatencyMs = rttMs + (averageJitterMs * 2) + 10;
   if (effectiveLatencyMs < 160)
   {
      R = 93.2 - (effectiveLatencyMs / 40);
   }
   else
   {
      R = 93.2 - (effectiveLatencyMs - 120) / 10;
   }

   R = R - (receivedPercentLoss * 4.0); // .jza. adjusted to 4.0 from original 2.5
#else
   const double receivedLossRatio = localFractionLost / 255.0;

   // https://github.com/wobbals/opentok-mos-estimator/blob/master/OpenTokMOS-iOS/OpenTokMOS/Classes/OTSubscriberMOS.m

   const int LOCAL_DELAY = 20;
#define H(x) (x < 0 ? 0 : 1)
#define a 0 // ILBC: a=10
#define b 19.8
#define c 29.7

   double d = rttMs + LOCAL_DELAY;
   double Id = 0.024 * d + 0.11 * (d - 177.3) * H(d - 177.3);

   double Ie = a + b * log(1 + c * receivedLossRatio);

   double R = 94.2 - Id - Ie;

#endif


   // T-REC-G.107 (06/2015) page 15 -- factor R to MOS

   double mosFloat;
   if (R < 0)
   {
      mosFloat = 1;
   }
   else if (R > 100)
   {
      mosFloat = 4.5;
   }
   else
   {
      mosFloat = 1 + (0.035 * R) + ((.000007) * R * (R - 60) * (100 - R));
   }

   short mosShort = round(mosFloat * 10);

   GenericLog(RESIPROCATE_SUBSYSTEM, resip::Log::Info, << "=========== Network MOS: " << mosShort <<
      ", incoming packetloss: " << receivedPercentLoss << "%, rttMs: " << rttMs <<
      ", averageJitterMs: " << averageJitterMs);

   return mosShort;
}

#endif // CPCAPI2_BRAND_CALL_MODULE
