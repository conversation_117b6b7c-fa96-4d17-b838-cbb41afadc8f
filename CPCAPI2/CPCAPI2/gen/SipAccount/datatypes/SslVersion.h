#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_SSLVERSION_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_SSLVERSION_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  enum class SslVersion
  {
    TlsDefault,
    SslNone,
    SslV2,
    SslV3,
    TlsV1_0,
    TlsV1_1,
    TlsV1_2,
    TlsV1_3,
    Ssl<PERSON>ighest,
    TlsNonDeprecated
  };

  inline std::string to_string(SslVersion value)
  {
    switch (value)
    {
      case SslVersion::TlsDefault:
        return "TLS_DEFAULT";
      case SslVersion::SslNone:
        return "SSL_NONE";
      case SslVersion::SslV2:
        return "SSL_V2";
      case SslVersion::SslV3:
        return "SSL_V3";
      case SslVersion::TlsV1_0:
        return "TLS_V1_0";
      case SslVersion::TlsV1_1:
        return "TLS_V1_1";
      case SslVersion::TlsV1_2:
        return "TLS_V1_2";
      case SslVersion::TlsV1_3:
        return "TLS_V1_3";
      case SslVersion::SslHighest:
        return "SSL_HIGHEST";
      case SslVersion::TlsNonDeprecated:
        return "TLS_NON_DEPRECATED";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, SslVersion value)
  {
    switch (value)
    {
      case SslVersion::TlsDefault:
        os << "TLS_DEFAULT";
        break;
      case SslVersion::SslNone:
        os << "SSL_NONE";
        break;
      case SslVersion::SslV2:
        os << "SSL_V2";
        break;
      case SslVersion::SslV3:
        os << "SSL_V3";
        break;
      case SslVersion::TlsV1_0:
        os << "TLS_V1_0";
        break;
      case SslVersion::TlsV1_1:
        os << "TLS_V1_1";
        break;
      case SslVersion::TlsV1_2:
        os << "TLS_V1_2";
        break;
      case SslVersion::TlsV1_3:
        os << "TLS_V1_3";
        break;
      case SslVersion::SslHighest:
        os << "SSL_HIGHEST";
        break;
      case SslVersion::TlsNonDeprecated:
        os << "TLS_NON_DEPRECATED";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const SslVersion& e)
  {
    switch (e)
    {
      case SslVersion::TlsDefault:
        j = "TLS_DEFAULT";
        break;
      case SslVersion::SslNone:
        j = "SSL_NONE";
        break;
      case SslVersion::SslV2:
        j = "SSL_V2";
        break;
      case SslVersion::SslV3:
        j = "SSL_V3";
        break;
      case SslVersion::TlsV1_0:
        j = "TLS_V1_0";
        break;
      case SslVersion::TlsV1_1:
        j = "TLS_V1_1";
        break;
      case SslVersion::TlsV1_2:
        j = "TLS_V1_2";
        break;
      case SslVersion::TlsV1_3:
        j = "TLS_V1_3";
        break;
      case SslVersion::SslHighest:
        j = "SSL_HIGHEST";
        break;
      case SslVersion::TlsNonDeprecated:
        j = "TLS_NON_DEPRECATED";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, SslVersion& e)
  {
      if (0 == j.get<std::string>().compare("TLS_DEFAULT"))
    {
      e = SslVersion::TlsDefault;
    }
    else if (0 == j.get<std::string>().compare("SSL_NONE"))
    {
      e = SslVersion::SslNone;
    }
    else if (0 == j.get<std::string>().compare("SSL_V2"))
    {
      e = SslVersion::SslV2;
    }
    else if (0 == j.get<std::string>().compare("SSL_V3"))
    {
      e = SslVersion::SslV3;
    }
    else if (0 == j.get<std::string>().compare("TLS_V1_0"))
    {
      e = SslVersion::TlsV1_0;
    }
    else if (0 == j.get<std::string>().compare("TLS_V1_1"))
    {
      e = SslVersion::TlsV1_1;
    }
    else if (0 == j.get<std::string>().compare("TLS_V1_2"))
    {
      e = SslVersion::TlsV1_2;
    }
    else if (0 == j.get<std::string>().compare("TLS_V1_3"))
    {
      e = SslVersion::TlsV1_3;
    }
    else if (0 == j.get<std::string>().compare("SSL_HIGHEST"))
    {
      e = SslVersion::SslHighest;
    }
    else if (0 == j.get<std::string>().compare("TLS_NON_DEPRECATED"))
    {
      e = SslVersion::TlsNonDeprecated;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum SslVersion"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_SSLVERSION_H
