#pragma once

#if !defined(__CPCAPI2_SIP_CHAT_INFO_H__)
#define __CPCAPI2_SIP_CHAT_INFO_H__

#include "cpcapi2defs.h"
#include "chat/SipChatTypes.h"
#include "SipChatMessageInfo.h"
#include "../cpm/SipMsrpMessagingSessionInfo.h"
#include "../iscomposing/IsComposingInfo.h"

#include <rutil/MultiReactor.hxx>
#include <rutil/DeadlineTimer.hxx>

namespace CPCAPI2
{
namespace SipChat
{

struct SipChatInfo : public SipMsrpMessagingSessionInfo, public IsComposingInfo
{
   SipChatInfo(SipChatHandle handle, resip::SharedPtr<resip::DialogUsageManager> dum, resip::InviteSessionHandler* handler);
   ~SipChatInfo();

   bool hasMessageId(const SipChatMessageHandle& message);
   SipChatMessageInfo* getMessageInfo(const SipChatMessageHandle& message) const;
   void addMessageInfo(const SipChatMessageHandle& handle, SipChatMessageInfo* messageInfo);

   SipChatMessageInfoMap messageInfoMap;

   static SipChatHandle nextChatHandle;
};
typedef std::map<SipChatHandle, SipChatInfo*> SipChatInfoMap;

}
}

#endif // __CPCAPI2_SIP_CHAT_INFO_H__
