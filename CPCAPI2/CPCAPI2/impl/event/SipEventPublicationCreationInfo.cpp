#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_EVENT_MODULE == 1)

#include "SipEventPublicationCreationInfo.h"
#include "SipEventPublicationManagerImpl.h"
#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_EVENT

namespace CPCAPI2
{
namespace SipEvent
{
SipEventPublicationCreationInfo::SipEventPublicationCreationInfo(resip::SharedPtr<resip::DialogUsageManager> dum, CPCAPI2::SipEvent::SipEventPublicationManagerImpl* evtPubMgr)
      : AppDialogSet(*dum), eventPublicationManagerImpl(evtPubMgr)
{
   account = 0;
   sdkh = 0;
   expiresSeconds = 3600;
   dumClientPublicationHandle = resip::ClientPublicationHandle::NotValid();
   dumServerPublicationHandle = resip::ServerPublicationHandle::NotValid();
   publicationState = SipPublicationState_NotStarted;
   displaced = false;
}

SipEventPublicationCreationInfo::~SipEventPublicationCreationInfo()
{
   if (displaced)
   {
      StackLog(<< "SipEventPublicationCreationInfo::~SipEventPublicationCreationInfo(): " << this << " ignoring removal of creation info from publication impl for publication handle: " << sdkh);
   }
   else
   {
      eventPublicationManagerImpl->removeCreationInfo(sdkh);
   }
}

void SipEventPublicationCreationInfo::cleanup()
{
   if (!mDum.findAppDialogSet(getDialogSetId()).isValid())
   {
      StackLog(<< "SipEventPublicationCreationInfo::cleanup(): " << this << " publication handle: " << sdkh << " could not find association dialog in dum, delete directly");
      // DUM doesn't know about this, so we have to clean up manually
      delete this;
   }
   else if (dumClientPublicationHandle.isValid())
   {
      StackLog(<< "SipEventPublicationCreationInfo::cleanup(): " << this << " publication handle: " << sdkh << " end the client publication");
      // just kill the ClientPublication
      dumClientPublicationHandle->end(true);
   }
}
}
}
#endif //CPCAPI2_BRAND_SIP_EVENT_MODULE