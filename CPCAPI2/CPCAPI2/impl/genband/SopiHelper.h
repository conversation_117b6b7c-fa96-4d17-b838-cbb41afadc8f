#pragma once

#if !defined(__CPCAPI2_SOPI_HELPER_H__)
#define __CPCAPI2_SOPI_HELPER_H__

#include "brand_branded.h"

#if (CPCAPI2_BRAND_GENBAND_SOPI_MODULE == 1)
#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "genband/GenbandSopiTypes.h"

// WinCrypt macros related to X509 stomp on the openssl typedefs of the same name
#if _WIN32
#undef X509_NAME
#endif
#include <openssl/x509.h>
#include <openssl/x509v3.h>
#include <openssl/ssl.h>

#include "soapAddressBookUserServiceProxy.h"
#include "soapGABUserServiceProxy.h"
#include "soapPresenceAuthorizationUserServiceProxy.h"

#include <string>

namespace CPCAPI2
{
namespace GenbandSopi
{

class SopiHelper
{
public:
   static void makePABEntry(const AddressBookEntry& entryIn, ns3__AddressBookEntryDO& pabEntryOut);
   static void freePABEntry(ns3__AddressBookEntryDO& pabEntry);
   static void copyPABEntry(ns3__AddressBookEntryDO pabEntryIn, AddressBookEntry& entryOut);
   static void copyPABEntryArr(ArrayOfAddressBookEntryDO pabEntryArr, cpc::vector<AddressBookEntry>& entryVecOut);

   //static void copyPABGroupsArr(ArrayOfAddrBookGroupDO pabGroupArr, cpc::vector<cpc::string>& grpsVecOut);
   static void copyGABEntry(ns4__GABEntryDO gabEntryIn, AddressBookEntry& entryOut);
   static void copyGABEntryArr(ArrayOfGABEntryDO gabEntryArrIn, cpc::vector<AddressBookEntry>& addrBookVecOut);

   static void copyPAUEntryArr(ArrayOfPresenceAuthorizationUserDO gabEntryArrIn, cpc::vector<PresenceAuthorizationEntry>& presenceAuthVecOut);

   static void loadRootSSLCerts(SSL_CTX* ctx);

private:
   static cpc::string fromStringPointer(std::string* pStr);
   static std::string* toStringPointer(const cpc::string& input);
};

}
}




#endif

#endif
