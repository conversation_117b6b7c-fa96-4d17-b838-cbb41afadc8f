#pragma once

#if !defined(__CPCAPI2_LICENSE_AUXILARY_DOC_PARSER_H__)
#define __CPCAPI2_LICENSE_AUXILARY_DOC_PARSER_H__

#include <libxml/xmlreader.h>

namespace CPCAPI2
{
namespace Licensing
{

class LicenseAuxiliaryDocParser
{
public:
   LicenseAuxiliaryDocParser(const std::string& xml);

   std::string AccumulatedRuntime() const { return m_AccumulatedRuntime; };
   std::string CheckTime() const { return m_CheckTime; };

private:
   void Parse(const std::string& xml);

   std::string m_AccumulatedRuntime;
   std::string m_CheckTime;

};

}
}

#endif // __CPCAPI2_LICENSE_AUXILARY_DOC_PARSER_H__