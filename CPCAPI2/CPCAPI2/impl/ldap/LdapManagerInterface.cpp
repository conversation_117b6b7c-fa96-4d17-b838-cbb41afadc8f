#include "brand_branded.h"

#if (CPCAPI2_BRAND_OPEN_LDAP_MODULE == 1)

#include "phone/Phone.h"	
#include "../util/DumFpCommand.h"

#include "LdapClientImpl.h"

using namespace resip;

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::LDAP

namespace CPCAPI2
{
namespace OpenLdap
{
LdapClientHandle LdapHandleFactory::sNextHandle = 1;

LdapManagerInterface::LdapManagerInterface(Phone* phone, bool useSeparateThread) :
   mPhone(dynamic_cast<PhoneInterface*>(phone)), m_ServiceThread(NULL), mUseSeparateThread(useSeparateThread),
   mShutdown(false)
{
   if(mUseSeparateThread)
   {
      // Make sure the io service run method continues by filling it with "work"
      m_pWork.reset( new boost::asio::io_service::work( m_IOService ));
      
      // Pump the io service with a thread that calls run()
      m_ServiceThread = new std::thread([&]() { m_IOService.run(); });
   }
}

LdapManagerInterface::LdapManagerInterface(Phone* phone) :
   mPhone(dynamic_cast<PhoneInterface*>(phone)),
   m_ServiceThread(NULL),
   mUseSeparateThread(true),
   mShutdown(false)
{
    // Make sure the io service run method continues by filling it with "work"
    m_pWork.reset( new boost::asio::io_service::work( m_IOService ));

    // Pump the io service with a thread that calls run()
    m_ServiceThread = new std::thread([&]() { m_IOService.run(); });
}

LdapManagerInterface::~LdapManagerInterface()
{
   mShutdown = true;
   
   if(mUseSeparateThread)
   {
      m_pWork.reset();
      
      // Stop the IO Service
      m_IOService.stop();
      
      // Join the service thread. Wait for connections to die.
      m_ServiceThread->join();
      delete m_ServiceThread;
      m_ServiceThread = NULL;
   }
   else
   {
      interruptProcess();
   }
   
   for (auto const& ldap: mLdapMap)
   {
      delete ldap.second;
}
}

void LdapManagerInterface::Release()
{
   delete this;
}
	
int LdapManagerInterface::process(unsigned int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return kModuleDisabled;
   }
   ReadCallbackBase* fp = mCallbackFifo.getNext(timeout);
   while(fp)
   {
      (*fp)();
      delete fp;
      if (mShutdown)
      {
         return kModuleDisabled;
      }
      fp = mCallbackFifo.getNext(kBlockingModeNonBlocking);
   }
   return kSuccess;
}
   
void LdapManagerInterface::post(ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}
   
void LdapManagerInterface::interruptProcess()
{
   mPhone->getSdkModuleThread().getAsyncProcessHandler()->handleProcessNotification();
}
   
void LdapManagerInterface::postCallback(ReadCallbackBase* command)
{
   mCallbackFifo.add(command);
   if (mCbHook) { mCbHook(); }
}

void LdapManagerInterface::addSdkObserver(LdapHandler* observer)
{
   mSdkObservers.insert(observer);
}

void LdapManagerInterface::removeSdkObserver(LdapHandler* observer)
{
   mSdkObservers.erase(observer);
}

int LdapManagerInterface::getImpl(LdapClientHandle pc, LdapClientImpl*& pimpl)
{
   LdapMap::iterator it = mLdapMap.find(pc);
   if (it != mLdapMap.end())
   {
      pimpl = it->second;
      return kSuccess;
   }
   return kError;
}

LdapClientHandle LdapManagerInterface::createClient() 
{
   DebugLog(<< __FUNCTION__);

   LdapClientHandle h = LdapHandleFactory::getNext();
   if(mUseSeparateThread)
   {
      m_IOService.post( std::bind( &LdapManagerInterface::createClientImpl, this, h ));
   }
   else
   {
      ReadCallbackBase* rcb = resip::resip_bind(&LdapManagerInterface::createClientImpl, this, h);
      post(rcb);
   }
   return h;
}

int LdapManagerInterface::createClientImpl(LdapClientHandle pc)
{
   LdapClientImpl* ldapClientImpl = new LdapClientImpl( pc, this);
   ldapClientImpl->setSdkObserver(&mSdkObservers);
   mLdapMap[pc] = ldapClientImpl;
   return kSuccess;
}

int LdapManagerInterface::setHandler(LdapClientHandle pc, LdapHandler* handler)
{
   if(mUseSeparateThread)
   {
      m_IOService.post( std::bind( &LdapManagerInterface::SetHandlerImpl, this, pc, handler ));

      if(handler == NULL)
      {
         m_IOService.poll();
         process(-1);
      }
   }
   else
   {
      ReadCallbackBase* rcb = resip::resip_bind(&LdapManagerInterface::SetHandlerImpl, this, pc, handler);
      post(rcb);
   }
   return kSuccess;
}

int LdapManagerInterface::SetHandlerImpl(LdapClientHandle pc, LdapHandler* handler)
{
   LdapClientImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      pimpl->setHandler(handler);
      return kSuccess;
   }
   return kError;
}

int LdapManagerInterface::applySettings(LdapClientHandle pc,const LdapClientSettings& settings)
{
   DebugLog(<< __FUNCTION__ << " " << pc);

   if(mUseSeparateThread)
   {
      m_IOService.post( std::bind( &LdapManagerInterface::ApplySettingsImpl, this, pc,settings ));
   }
   else
   {
      ReadCallbackBase* rcb = resip::resip_bind(&LdapManagerInterface::ApplySettingsImpl, this, pc,settings);
      post(rcb);
   }
   return kSuccess;
}

int LdapManagerInterface::ApplySettingsImpl(LdapClientHandle pc,const LdapClientSettings settings)
{
   LdapClientImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      pimpl->LdapApplySettings(settings);
      return kSuccess;
   }
   return kError;
}

int LdapManagerInterface::connect(LdapClientHandle pc)
{
   DebugLog(<< __FUNCTION__ << " " << pc);

   if(mUseSeparateThread)
   {
      m_IOService.post( std::bind( &LdapManagerInterface::ConnectImpl, this, pc ));
   }
   else
   {
      ReadCallbackBase* rcb = resip::resip_bind(&LdapManagerInterface::ConnectImpl, this, pc);
      post(rcb);
   }
   return kSuccess;
}

int LdapManagerInterface::ConnectImpl(LdapClientHandle pc)
{
   LdapClientImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      pimpl->LdapInit();
	  pimpl->LdapConnect();
      return kSuccess;
   }
   return kError;
}

int LdapManagerInterface::disconnect(LdapClientHandle pc)
{
   DebugLog(<< __FUNCTION__ << " " << pc);

   if(mUseSeparateThread)
   {
      m_IOService.post( std::bind( &LdapManagerInterface::DisconnectImpl, this, pc ));
   }
   else
   {
      ReadCallbackBase* rcb = resip::resip_bind(&LdapManagerInterface::DisconnectImpl, this, pc);
      post(rcb);
   }
   return kSuccess;
}

int LdapManagerInterface::DisconnectImpl(LdapClientHandle pc)
{
   LdapClientImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
	  pimpl->LdapDisconnect();
      return kSuccess;
   }
   return kError;
}

int LdapManagerInterface::setDataMap(LdapClientHandle pc,LdapDataMap map)
{
   DebugLog(<< __FUNCTION__ << " " << pc);

   if(mUseSeparateThread)
   {
      m_IOService.post( std::bind( &LdapManagerInterface::SetDataMapImpl, this, pc,map ));
   }
   else
   {
      ReadCallbackBase* rcb = resip::resip_bind(&LdapManagerInterface::SetDataMapImpl, this, pc,map);
      post(rcb);
   }
   return kSuccess;
}

int LdapManagerInterface::SetDataMapImpl(LdapClientHandle pc,LdapDataMap map)
{
   LdapClientImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
	  pimpl->LdapSetDataMap(map);
      return kSuccess;
   }
   return kError;
}

int LdapManagerInterface::search(LdapClientHandle pc, cpc::string searchPattern, cpc::string rootDn, LdapSearchScope scopeSetting, int timeout, int maxsize, bool opErrIgnore)
{
   DebugLog(<< __FUNCTION__ << " " << pc);

   if(mUseSeparateThread)
   {
      m_IOService.post( std::bind( &LdapManagerInterface::SearchImpl, this, pc,searchPattern,rootDn,scopeSetting,timeout,maxsize, opErrIgnore));
   }
   else
   {
      ReadCallbackBase* rcb = resip::resip_bind(&LdapManagerInterface::SearchImpl, this, pc,searchPattern,rootDn,scopeSetting,timeout,maxsize,opErrIgnore);
      post(rcb);
   }
   return kSuccess;
}

int LdapManagerInterface::SearchImpl(LdapClientHandle pc, cpc::string searchPattern, cpc::string rootDn, LdapSearchScope scopeSetting, int timeout, int maxsize, bool opErrIgnore)
{
   LdapClientImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
	  pimpl->LdapSearch(searchPattern,rootDn,scopeSetting,timeout,maxsize,opErrIgnore);
      return kSuccess;
   }
   return kError;
}

void LdapManagerInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
   if(mUseSeparateThread)
   {
      m_IOService.post( std::bind( &LdapManagerInterface::setCallbackHookImpl, this, cbHook, context ));
   }
   else
   {
      ReadCallbackBase* rcb = resip::resip_bind(&LdapManagerInterface::setCallbackHookImpl, this, cbHook, context );
      post(rcb);
   }
}

void LdapManagerInterface::setCallbackHookImpl(void (*cbHook)(void*), void* context)
{
   mCbHook = std::bind(cbHook, context);
}

}//OpenLdap
}//CPCAPI2

#endif //CPCAPI2_BRAND_OPEN_LDAP_MODULE
