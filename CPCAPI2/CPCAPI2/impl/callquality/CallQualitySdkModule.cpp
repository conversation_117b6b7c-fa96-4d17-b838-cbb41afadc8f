#include "brand_branded.h"

#include "interface/public/callquality/CallQualityReportManager.h"

#if (CPCAPI2_BRAND_CALL_QUALITY_REPORT_MODULE == 1)
#include "CallQualityReportManagerInterface.h"
#include "interface/public/call/SipConversationManager.h"
#include "interface/public/event/SipPublicationManager.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace CallQuality
{
   CallQualityReportManager* CallQualityReportManager::getInterface(Phone* cpcPhone)
   {
#if (CPCAPI2_BRAND_CALL_QUALITY_REPORT_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      SipConversation::SipConversationManager::getInterface(phone);
      SipEvent::SipEventPublicationManager::getInterface(phone);
      return _GetInterface<CallQualityReportManagerInterface>(phone, "CallQualityReportManager");
#else
      return NULL;
#endif
   }

}
}
