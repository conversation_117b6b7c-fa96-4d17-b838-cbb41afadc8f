#pragma once

#if !defined(__CPCAPI2_COMPOSER_IDLE_STATE_H__)
#define __CPCAPI2_COMPOSER_IDLE_STATE_H__

#include "ComposerBaseState.h"

namespace CPCAPI2
{
namespace IsComposing
{

class ComposerIdleState : public ComposerBaseState
{
public:
   virtual void onIsComposingMessage(IsComposingManager* manager, IsComposingInfo* info);
   virtual void onMessageSent(IsComposingManager* manager, IsComposingInfo* info);

   static ComposerIdleState instance;
};

}
}

#endif // __CPCAPI2_COMPOSER_IDLE_STATE_H__