#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_TUNNELMEDIATRANSPORTTYPE_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_TUNNELMEDIATRANSPORTTYPE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  enum class TunnelMediaTransportType
  {
    Default,
    DatagramPreferred,
    DatagramOnly,
    StreamPreferred,
    StreamOnly
  };

  inline std::string to_string(TunnelMediaTransportType value)
  {
    switch (value)
    {
      case TunnelMediaTransportType::Default:
        return "Default";
      case TunnelMediaTransportType::DatagramPreferred:
        return "DatagramPreferred";
      case TunnelMediaTransportType::DatagramOnly:
        return "DatagramOnly";
      case TunnelMediaTransportType::StreamPreferred:
        return "StreamPreferred";
      case TunnelMediaTransportType::StreamOnly:
        return "StreamOnly";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, TunnelMediaTransportType value)
  {
    switch (value)
    {
      case TunnelMediaTransportType::Default:
        os << "Default";
        break;
      case TunnelMediaTransportType::DatagramPreferred:
        os << "DatagramPreferred";
        break;
      case TunnelMediaTransportType::DatagramOnly:
        os << "DatagramOnly";
        break;
      case TunnelMediaTransportType::StreamPreferred:
        os << "StreamPreferred";
        break;
      case TunnelMediaTransportType::StreamOnly:
        os << "StreamOnly";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const TunnelMediaTransportType& e)
  {
    switch (e)
    {
      case TunnelMediaTransportType::Default:
        j = "Default";
        break;
      case TunnelMediaTransportType::DatagramPreferred:
        j = "DatagramPreferred";
        break;
      case TunnelMediaTransportType::DatagramOnly:
        j = "DatagramOnly";
        break;
      case TunnelMediaTransportType::StreamPreferred:
        j = "StreamPreferred";
        break;
      case TunnelMediaTransportType::StreamOnly:
        j = "StreamOnly";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, TunnelMediaTransportType& e)
  {
      if (0 == j.get<std::string>().compare("Default"))
    {
      e = TunnelMediaTransportType::Default;
    }
    else if (0 == j.get<std::string>().compare("DatagramPreferred"))
    {
      e = TunnelMediaTransportType::DatagramPreferred;
    }
    else if (0 == j.get<std::string>().compare("DatagramOnly"))
    {
      e = TunnelMediaTransportType::DatagramOnly;
    }
    else if (0 == j.get<std::string>().compare("StreamPreferred"))
    {
      e = TunnelMediaTransportType::StreamPreferred;
    }
    else if (0 == j.get<std::string>().compare("StreamOnly"))
    {
      e = TunnelMediaTransportType::StreamOnly;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum TunnelMediaTransportType"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_TUNNELMEDIATRANSPORTTYPE_H
