#include "brand_branded.h"

#if (CPCAPI2_BRAND_CALL_MODULE == 1)
#include "ReconUserAgentImpl.h"

namespace CPCAPI2
{
namespace SipConversation
{
ReconRegManager::ReconRegManager()
      : mConvProfileHandle(0)
{
}

ReconRegManager::~ReconRegManager()
{
}

recon::ConversationProfileHandle ReconRegManager::findRegistration(const resip::Uri& requestURI)
{
   return mConvProfileHandle;
}

void ReconRegManager::setConversationProfile(recon::ConversationProfileHandle cph)
{
   mConvProfileHandle = cph;
}


ReconUserAgent::ReconUserAgent(const std::shared_ptr<webrtc_recon::MediaStackImpl>& ms)
   : mRegManager(new ReconRegManager()),
     mMediaStack(ms)
{
   mMinRTPPortMap[sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_NONE] = 0;
   mMinRTPPortMap[sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_UNKNOWN] = 0;
   mMinRTPPortMap[sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_AUDIO] = 0;
   mMinRTPPortMap[sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_VIDEO] = 0;
   mMinRTPPortMap[sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_TEXT] = 0;
   mMinRTPPortMap[sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_APPLICATION] = 0;
   mMinRTPPortMap[sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_MESSAGE] = 0;

   mMaxRTPPortMap[sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_NONE] = 0;
   mMaxRTPPortMap[sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_UNKNOWN] = 0;
   mMaxRTPPortMap[sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_AUDIO] = 0;
   mMaxRTPPortMap[sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_VIDEO] = 0;
   mMaxRTPPortMap[sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_TEXT] = 0;
   mMaxRTPPortMap[sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_APPLICATION] = 0;
   mMaxRTPPortMap[sdpcontainer::SdpMediaLine::SdpMediaType::MEDIA_TYPE_MESSAGE] = 0;
}

ReconUserAgent::~ReconUserAgent()
{
   delete mRegManager;
}

resip::DialogUsageManager* ReconUserAgent::getDialogUsageManager()
{
   return mDum.get();
}

void ReconUserAgent::getPortRange(unsigned int& minPort, unsigned int& maxPort, sdpcontainer::SdpMediaLine::SdpMediaType mediaType)
{
   minPort = mMinRTPPortMap[mediaType];
   maxPort = mMaxRTPPortMap[mediaType];
}



}
}
#endif // CPCAPI2_BRAND_CALL_MODULE
