#pragma once

#if !defined(CPCAPI2_CLOUD_SERVER_CONNECTION_H)
#define CPCAPI2_CLOUD_SERVER_CONNECTION_H

#include "cpcapi2defs.h"
#include "cloudconnector/CloudConnector.h"
#include "jsonapi/JsonApiClient.h"
#include "jsonapi/JsonApiClientHandler.h"
#include "jsonapi/JsonApiClientInterface.h"
#include "phone/PhoneHandler.h"
#include "phone/SslCipherOptions.h"
#include "cloudconnector/ServiceDesc.h"

#include <rutil/Data.hxx>
#include <rutil/RecursiveMutex.hxx>

#include <set>
#include <future>

namespace CPCAPI2
{

class PhoneInterface;
class PhoneInternal;

namespace CloudConnector
{

class CloudConnectorImpl;
class CloudServerConnectionObserver;

enum CloudServerConnStatus
{
   CloudServerConnStatus_Disconnecting,
   CloudServerConnStatus_Disconnected,
   CloudServerConnStatus_Connecting,
   CloudServerConnStatus_Authenticating,
   CloudServerConnStatus_Connected,
   CloudServerConnStatus_ConnFailure,
   CloudServerConnStatus_AuthFailure,
   CloudServerConnStatus_LoggedOut,
   CloudServerConnStatus_LogoutFailure
};

struct CloudServerConnStatusEvent
{
   CloudServerConnStatus status;
};

class CloudServerConnection : public CPCAPI2::PhoneHandler,
                              public CPCAPI2::JsonApi::JsonApiClientHandler,
                              public CPCAPI2::JsonApi::JsonApiClientSyncHandler,
                              public std::enable_shared_from_this<CloudServerConnection>
{

public:

   CloudServerConnection(CloudServerConnectionObserver* cl);
   virtual ~CloudServerConnection();

   int initialize(PhoneInternal* masterSdkPhone);
   void release();
   int connect(const cpc::string& serverUrl, const cpc::string& authToken, const cpc::string& realm, bool ignoreCertVerification);
   int disconnect();
   int logout();
   void addService(const ServiceDesc& service);
   const std::set<ServiceDesc>& getServices() const;
   const cpc::string& getUrl() const;
   CPCAPI2::PhoneInternal* getPhone() const;
   const cpc::string& getAuthToken() const;
   int dropIncomingJsonMessages(bool enable);

   static int doAuthFlow(resip::MultiReactor& requestServiceThread, resip::MultiReactor& callbackThread, const resip::Data& authServerUrl, const resip::Data& username, const resip::Data& password_hash, const std::vector<resip::Data>& requestedResources, const resip::Data& encryptionKey, bool ignoreCertVerification, const SslCipherOptions tlsSettings, const int authTimeoutSecs, const std::function<void(int, const resip::Data&, const cpc::string&)>& resultCb);
   static int doAuthFlowWithSimpleWebClient(resip::MultiReactor& requestServiceThread, resip::MultiReactor& callbackThread, const resip::Data& authServerUrl, const resip::Data& username, const resip::Data& password_hash, const std::vector<resip::Data>& requestedResources, const resip::Data& encryptionKey, bool ignoreCertVerification, const SslCipherOptions tlsSettings, const int authTimeoutSecs, const std::function<void(int, const resip::Data&, const cpc::string&)>& resultCb);
   static int doOrchestrationFlow(resip::MultiReactor& requestServiceThread, resip::MultiReactor& callbackThread, const std::vector<ServiceDesc>& serviceRequests, const resip::Data& orchServerUrl, const resip::Data& authToken, bool ignoreCertVerification, const SslCipherOptions tlsSettings, const int orchTimeoutSecs, const std::function<void(int, const std::vector<ServiceDescUrl>&, const cpc::string&)>& resultCb);
   static int doAddUserFlow(resip::MultiReactor& requestServiceThread, resip::MultiReactor& callbackThread, const resip::Data& username, const resip::Data& password, const resip::Data& authServerUrl, const resip::Data& encryptionKey, bool ignoreCertVerification, const SslCipherOptions tlsSettings, const int authTimeoutSecs, const std::function<void(int, const cpc::string&)>& resultCb);

   // PhoneHandler
   virtual int onError(const cpc::string& sourceModule, const PhoneErrorEvent& args) OVERRIDE;
   virtual int onLicensingError(const LicensingErrorEvent& args) OVERRIDE;
   virtual int onLicensingSuccess() OVERRIDE;

   // JsonApiClientHandler
   virtual int onLoginResult(CPCAPI2::JsonApi::JsonApiLoginHandle h, const CPCAPI2::JsonApi::LoginResultEvent& args) OVERRIDE;
   virtual int onLogoutResult(CPCAPI2::JsonApi::JsonApiLoginHandle h, const CPCAPI2::JsonApi::LogoutResultEvent& args) OVERRIDE;
   virtual int onStatusChanged(CPCAPI2::JsonApi::JsonApiLoginHandle h, const CPCAPI2::JsonApi::StatusChangedEvent& args) OVERRIDE;

   void handleSdkCallback();

   static void cloudPhoneDestroyed(std::weak_ptr<CloudServerConnection> weakSelf);
   static void cloudPhoneReleased(std::weak_ptr<CloudServerConnection> weakSelf);
   static void jsonClientDestroyed(std::weak_ptr<CloudServerConnection> weakSelf);
   static cpc::string get_debug_string(const CloudServerConnStatus& status);

private:

   void changeState(CloudServerConnStatus newStatus);

private:

   CloudServerConnectionObserver* mObs;
   CPCAPI2::PhoneInternal* mPhone;
   CPCAPI2::PhoneInternal* mMasterSdkPhone;
   CPCAPI2::JsonApi::JsonApiClient* mJsonApiClient;
   CPCAPI2::JsonApi::JsonApiConnectionHandle mJsonConnHdl;
   cpc::string mAuthToken;
   cpc::string mRealm;
   CloudServerConnStatus mStatus;
   std::set<ServiceDesc> mServices;
   cpc::string mUrl;
   bool mReleased;
   resip::RecursiveMutex mReleaseMutex;

};

}

}

#endif // CPCAPI2_CLOUD_SERVER_CONNECTION_H

