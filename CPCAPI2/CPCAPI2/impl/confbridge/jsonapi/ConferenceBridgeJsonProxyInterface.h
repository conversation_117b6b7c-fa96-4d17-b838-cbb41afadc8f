#pragma once

#if !defined(CPCAPI2_CONFERENCE_BRIDGE_JSON_PROXY_INTERFACE_H)
#define CPCAPI2_CONFERENCE_BRIDGE_JSON_PROXY_INTERFACE_H

#include "confbridge/ConferenceBridgeJsonProxy.h"
#include "confbridge/ConferenceBridgeSyncHandler.h"
#include "jsonapi/JsonApiClientModule.h"
#include "phone/PhoneModule.h"
#include "phone/Cpcapi2EventSource.h"

#include <rutil/Reactor.hxx>
#include <rutil/Fifo.hxx>

#include <mutex>
#include <condition_variable>

namespace CPCAPI2
{
class PhoneInterface;
namespace ConferenceBridge
{
class ConferenceBridgeJsonProxyInterface : public CPCAPI2::EventSource<ConferenceHandle, ConferenceBridgeHandler, ConferenceBridgeSyncHandler>, 
                                          public CPCAPI2::ConferenceBridge::ConferenceBridgeManagerJsonProxy,
                                          public CPCAPI2::JsonApi::JsonApiClientModule,
                                          public CPCAPI2::PhoneModule,
                                          public resip::ReactorBinded
{
public:
   ConferenceBridgeJsonProxyInterface(CPCAPI2::Phone* phone);
   virtual ~ConferenceBridgeJsonProxyInterface();

   void clearQueryConferenceListHandler();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiClientModule
   virtual void setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport) OVERRIDE;
   virtual int processIncoming(const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // Inherited via ConferenceBridgeManager
   virtual int process(unsigned int timeout) OVERRIDE {
      return kSuccess;
   }
   virtual void interruptProcess() OVERRIDE {}
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE {}
   virtual int start(const ConferenceBridgeConfig& bridgeConfig) OVERRIDE;
   virtual int shutdown() OVERRIDE {
      return kSuccess;
   }
   virtual ConferenceHandle createConference(const ConferenceSettings& conferenceSettings) OVERRIDE;
   virtual ConferenceHandle createConference(const ConferenceSettings& conferenceSettings, ConferenceBridgeHandler* handler) OVERRIDE;
   virtual int setHandler(ConferenceHandle conference, ConferenceBridgeHandler* handler) OVERRIDE;
   virtual int setHandler(ConferenceBridgeHandler* handler) OVERRIDE;
   virtual int destroyConference(ConferenceHandle conference) OVERRIDE;
   virtual int addSipEndpoint(ConferenceHandle conference, CPCAPI2::SipAccount::SipAccountHandle sipAccount) OVERRIDE;
   virtual int queryConferenceDetails(ConferenceHandle conference) OVERRIDE;
   virtual int queryConferenceDetails(ConferenceHandle conference, ConferenceDetailsHandler* handler) OVERRIDE;
   virtual int queryConferenceDetails(const cpc::string& conferenceToken, ConferenceDetailsHandler* handler) OVERRIDE;
   virtual int queryConferenceList(ConferenceListHandler* handler) OVERRIDE;
   virtual int queryParticipantList(ConferenceHandle conference, ParticipantListHandler* handler) OVERRIDE;
   virtual ConferenceParticipantHandle createWebParticipant(ConferenceHandle conference) OVERRIDE;
   virtual ConferenceParticipantHandle createWebParticipant(ConferenceHandle conference, const WebParticipantIdentity& identityInfo, bool addToFloor, CreateWebParticipantHandler* handler) OVERRIDE;
   virtual ConferenceParticipantHandle createWebParticipant(ConferenceHandle conference, CreateWebParticipantHandler* handler) OVERRIDE;
   virtual int setWebParticipantIdentity(ConferenceParticipantHandle webParticipant, const WebParticipantIdentity& identityInfo) OVERRIDE;
   virtual int destroyWebParticipant(ConferenceParticipantHandle webParticipant) OVERRIDE;
   virtual int setParticipantPermissions(ConferenceParticipantHandle participant, const ParticipantPermissions& permissionsInfo) OVERRIDE;
   virtual int sendPeerConnectionOffer(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpOffer, PeerConnectionAnswerHandler* handler) OVERRIDE;
   virtual int sendPeerConnectionAnswer(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpAnswer) OVERRIDE;
   virtual int generateLocalOffer(ConferenceParticipantHandle webParticipant, PeerConnectionAnswerHandler* handler) OVERRIDE {
      return kSuccess;
   }
   virtual int takeParticipantSnapshot(ConferenceParticipantHandle participant) OVERRIDE {
      return kSuccess;
   }
   virtual int setParticipantPhoto(ConferenceParticipantHandle participant, const cpc::string& photoFileNameUtf8) OVERRIDE {
      return kSuccess;
   }
   virtual int addAssociatedConference(ConferenceHandle conference, ConferenceHandle associatedConference) OVERRIDE;
   virtual int requestFloor(ConferenceParticipantHandle requestor) OVERRIDE;
   virtual int addToFloor(ConferenceParticipantHandle participant) OVERRIDE;
   virtual int removeFromFloor(ConferenceParticipantHandle participant) OVERRIDE;
   virtual int setVideoLayout(ConferenceHandle conference, VideoLayout layout) OVERRIDE;
   virtual int setStreamingEnabled(ConferenceHandle conference, bool enabled) OVERRIDE;
   virtual int setRecordingEnabled(ConferenceHandle conference, bool enabled) OVERRIDE;
   virtual int setTranscriptionEnabled(ConferenceHandle conference, bool enabled) OVERRIDE {
      return kSuccess;
   }
   virtual bool isValidJoinUrl(const cpc::string& joinUrl) OVERRIDE {
      return false;
   }
   virtual int updateJoinUrlBase(const cpc::string& joinUrl) OVERRIDE {
      return kSuccess;
   }

private:
   // ReactorBinded
   virtual void release() OVERRIDE;

   void processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request, const std::weak_ptr<int>& processCheck);
   int startImpl(const ConferenceBridgeConfig& bridgeConfig);
   int createConferenceImpl(ConferenceHandle conference, const ConferenceSettings& conferenceSettings, ConferenceBridgeHandler* handler=NULL);
   int destroyConferenceImpl(ConferenceHandle conference);
   int addSipEndpointImpl(ConferenceHandle conference, CPCAPI2::SipAccount::SipAccountHandle sipAccount);
   int queryConferenceDetailsImpl(ConferenceHandle conference, ConferenceDetailsHandler* handler);
   int queryConferenceDetailsImpl2(const cpc::string& conferenceToken, ConferenceDetailsHandler* handler);
   int queryConferenceListImpl(ConferenceListHandler* handler);
   int queryParticipantListImpl(ConferenceHandle conference, ParticipantListHandler* handler);
   int setConferenceInfoImpl(ConferenceHandle conference, const ConferenceInfo& conferenceInfo);
   int createWebParticipantImpl(ConferenceParticipantHandle participant, ConferenceHandle conference, const WebParticipantIdentity& identityInfo, bool addToFloor, CreateWebParticipantHandler* handler);
   int setWebParticipantIdentityImpl(ConferenceParticipantHandle webParticipant, const WebParticipantIdentity& identityInfo);
   int destroyWebParticipantImpl(ConferenceParticipantHandle participant);
   int setParticipantPermissionsImpl(ConferenceParticipantHandle participant, const ParticipantPermissions& permissionsInfo);
   int sendPeerConnectionOfferImpl(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpOffer, PeerConnectionAnswerHandler* handler);
   int sendPeerConnectionAnswerImpl(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpAnswer);
   int addAssociatedConferenceImpl(ConferenceHandle conference, ConferenceHandle associatedConference);
   int requestFloorImpl(ConferenceParticipantHandle requestor);
   int addToFloorImpl(ConferenceParticipantHandle participant);
   int removeFromFloorImpl(ConferenceParticipantHandle participant);
   int setVideoLayoutImpl(ConferenceHandle conference, VideoLayout layout);
   int setStreamingEnabledImpl(ConferenceHandle conference, bool enabled);
   int setRecordingEnabledImpl(ConferenceHandle conference, bool enabled);
   void clearQueryConferenceListHandlerImpl();

   int handleConferenceDetails(const rapidjson::Value& functionObjectVal);
   int handleConferenceEnded(const rapidjson::Value& functionObjectVal);
   int handleConferenceList(const rapidjson::Value& functionObjectVal);
   int handleParticipantListState(const rapidjson::Value& functionObjectVal);
   int handlePeerConnectionAnswer(const rapidjson::Value& functionObjectVal);
   int handleWebParticipantCreated(const rapidjson::Value& functionObjectVal);

private:
   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiTransport* mTransport;
   int mHandlePrefix;
   ConferenceHandle mNextConferenceHandle;
   ConferenceParticipantHandle mNextParticipantHandle;
   std::map<std::string, ConferenceDetailsHandler*> mQueryConferenceDetailsHandlers;
   ConferenceListHandler* mQueryConferenceListHandler;
   std::map<ConferenceParticipantHandle, PeerConnectionAnswerHandler*> mPeerConnectionAnswerHandlers;
   std::map<ConferenceHandle, ParticipantListHandler*> mQueryParticipantListHandlers;
   std::map<ConferenceHandle, CreateWebParticipantHandler*> mCreateWebParticipantHandlers;
   std::shared_ptr<int> mProcessCheck;
};
}
}
#endif // CPCAPI2_CONFERENCE_BRIDGE_JSON_PROXY_INTERFACE_H
