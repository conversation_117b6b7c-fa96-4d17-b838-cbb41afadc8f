#include "jsonrpc/OptionalSerialization.h"
#include "jsonrpc/StringSerialization.h"
#include <nlohmann/json.hpp>
#include "TunnelType.h"
#include "TunnelTransportType.h"
#include "TunnelMediaTransportType.h"

#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_TUNNELCONFIG_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_TUNNELCONFIG_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  class TunnelConfig
  {
  public:
    std::optional<bool> useTunnel;
    std::optional<TunnelType> tunnelType;
    std::optional<cpc::string> server;
    std::optional<TunnelTransportType> transportType;
    std::optional<TunnelMediaTransportType> mediaTransportType;
    std::optional<int32_t> redundancyFactor;
    std::optional<bool> doLoadBalancing;
    std::optional<bool> ignoreCertVerification;
    std::optional<bool> disableNagleAlgorithm;
    std::optional<cpc::string> strettoTunnelUrl;
    std::optional<cpc::string> strettoTunnelToken;
    std::optional<cpc::string> strettoTunnelSessionId;
    std::optional<bool> strettoTunnelTestConnection;
    std::optional<bool> logStrettoTunnelTransportTraces;
    std::optional<bool> strettoTunnelSkipHandshake;

    TunnelConfig() = default;

    TunnelConfig(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const TunnelConfig& o)
    {
      j = nlohmann::json::object();
       if (o.useTunnel) { j["useTunnel"] = *(o.useTunnel); }
     if (o.tunnelType) { j["tunnelType"] = *(o.tunnelType); }
     if (o.server) { j["server"] = *(o.server); }
     if (o.transportType) { j["transportType"] = *(o.transportType); }
     if (o.mediaTransportType) { j["mediaTransportType"] = *(o.mediaTransportType); }
     if (o.redundancyFactor) { j["redundancyFactor"] = *(o.redundancyFactor); }
     if (o.doLoadBalancing) { j["doLoadBalancing"] = *(o.doLoadBalancing); }
     if (o.ignoreCertVerification) { j["ignoreCertVerification"] = *(o.ignoreCertVerification); }
     if (o.disableNagleAlgorithm) { j["disableNagleAlgorithm"] = *(o.disableNagleAlgorithm); }
     if (o.strettoTunnelUrl) { j["strettoTunnelUrl"] = *(o.strettoTunnelUrl); }
     if (o.strettoTunnelToken) { j["strettoTunnelToken"] = *(o.strettoTunnelToken); }
     if (o.strettoTunnelSessionId) { j["strettoTunnelSessionId"] = *(o.strettoTunnelSessionId); }
     if (o.strettoTunnelTestConnection) { j["strettoTunnelTestConnection"] = *(o.strettoTunnelTestConnection); }
     if (o.logStrettoTunnelTransportTraces) { j["logStrettoTunnelTransportTraces"] = *(o.logStrettoTunnelTransportTraces); }
     if (o.strettoTunnelSkipHandshake) { j["strettoTunnelSkipHandshake"] = *(o.strettoTunnelSkipHandshake); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, TunnelConfig& o)
    {
     if (j.contains("useTunnel")) { o.useTunnel = j.at("useTunnel"); }
     if (j.contains("tunnelType")) { o.tunnelType = j.at("tunnelType"); }
     if (j.contains("server")) { o.server = j.at("server"); }
     if (j.contains("transportType")) { o.transportType = j.at("transportType"); }
     if (j.contains("mediaTransportType")) { o.mediaTransportType = j.at("mediaTransportType"); }
     if (j.contains("redundancyFactor")) { o.redundancyFactor = j.at("redundancyFactor"); }
     if (j.contains("doLoadBalancing")) { o.doLoadBalancing = j.at("doLoadBalancing"); }
     if (j.contains("ignoreCertVerification")) { o.ignoreCertVerification = j.at("ignoreCertVerification"); }
     if (j.contains("disableNagleAlgorithm")) { o.disableNagleAlgorithm = j.at("disableNagleAlgorithm"); }
     if (j.contains("strettoTunnelUrl")) { o.strettoTunnelUrl = j.at("strettoTunnelUrl"); }
     if (j.contains("strettoTunnelToken")) { o.strettoTunnelToken = j.at("strettoTunnelToken"); }
     if (j.contains("strettoTunnelSessionId")) { o.strettoTunnelSessionId = j.at("strettoTunnelSessionId"); }
     if (j.contains("strettoTunnelTestConnection")) { o.strettoTunnelTestConnection = j.at("strettoTunnelTestConnection"); }
     if (j.contains("logStrettoTunnelTransportTraces")) { o.logStrettoTunnelTransportTraces = j.at("logStrettoTunnelTransportTraces"); }
     if (j.contains("strettoTunnelSkipHandshake")) { o.strettoTunnelSkipHandshake = j.at("strettoTunnelSkipHandshake"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_TUNNELCONFIG_H
