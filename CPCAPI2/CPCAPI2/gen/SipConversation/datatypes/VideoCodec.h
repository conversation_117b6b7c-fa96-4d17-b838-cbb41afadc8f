#include "jsonrpc/StringSerialization.h"
#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_VIDEOCODEC_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_VIDEOCODEC_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class VideoCodec
  {
  public:
    std::optional<cpc::string> plName;
    std::optional<int32_t> plType;
    std::optional<int32_t> width;
    std::optional<int32_t> height;
    std::optional<int32_t> startBitrate;
    std::optional<int32_t> maxBitrate;
    std::optional<int32_t> minBitrate;
    std::optional<int32_t> maxFramerate;
    std::optional<bool> hadwareAccelerated;
    std::optional<int32_t> priority;
    std::optional<cpc::string> displayName;

    VideoCodec() = default;

    VideoCodec(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const VideoCodec& o)
    {
      j = nlohmann::json::object();
       if (o.plName) { j["plName"] = *(o.plName); }
     if (o.plType) { j["plType"] = *(o.plType); }
     if (o.width) { j["width"] = *(o.width); }
     if (o.height) { j["height"] = *(o.height); }
     if (o.startBitrate) { j["startBitrate"] = *(o.startBitrate); }
     if (o.maxBitrate) { j["maxBitrate"] = *(o.maxBitrate); }
     if (o.minBitrate) { j["minBitrate"] = *(o.minBitrate); }
     if (o.maxFramerate) { j["maxFramerate"] = *(o.maxFramerate); }
     if (o.hadwareAccelerated) { j["hadwareAccelerated"] = *(o.hadwareAccelerated); }
     if (o.priority) { j["priority"] = *(o.priority); }
     if (o.displayName) { j["displayName"] = *(o.displayName); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, VideoCodec& o)
    {
     if (j.contains("plName")) { o.plName = j.at("plName"); }
     if (j.contains("plType")) { o.plType = j.at("plType"); }
     if (j.contains("width")) { o.width = j.at("width"); }
     if (j.contains("height")) { o.height = j.at("height"); }
     if (j.contains("startBitrate")) { o.startBitrate = j.at("startBitrate"); }
     if (j.contains("maxBitrate")) { o.maxBitrate = j.at("maxBitrate"); }
     if (j.contains("minBitrate")) { o.minBitrate = j.at("minBitrate"); }
     if (j.contains("maxFramerate")) { o.maxFramerate = j.at("maxFramerate"); }
     if (j.contains("hadwareAccelerated")) { o.hadwareAccelerated = j.at("hadwareAccelerated"); }
     if (j.contains("priority")) { o.priority = j.at("priority"); }
     if (j.contains("displayName")) { o.displayName = j.at("displayName"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_VIDEOCODEC_H
