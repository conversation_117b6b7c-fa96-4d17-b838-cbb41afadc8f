#include "brand_branded.h"

#if (CPCAPI2_BRAND_CONFERENCE_CONNECTOR_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "ConferenceConnectorJsonServerInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "media/VideoJsonApi.h"
#include "media/jsonapi/VideoJsonServerInterface.h"
#include "json/JsonHelper.h"
#include "util/LogSubsystems.h"

#include <rutil/Logger.hxx>
#include <rutil/Random.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::CONF_CONNECTOR
#define JSON_MODULE "ConferenceConnectorManagerJsonProxy"

using namespace CPCAPI2::ConferenceConnector;

ConferenceConnectorJsonServerInterface::ConferenceConnectorJsonServerInterface(CPCAPI2::Phone* phone) :
   CPCAPI2::EventSource<ConferenceConnectorHandle, ConferenceConnectorHandler, ConferenceConnectorJsonServerSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
   mPhone(dynamic_cast<PhoneInterface*>(phone)),
   mConferenceConnectorManager(CPCAPI2::ConferenceConnector::ConferenceConnectorManager::getInterface(phone))
{
   mFunctionMap["createConferenceConnector"] = std::bind(&ConferenceConnectorJsonServerInterface::handleCreateConferenceConnector, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setConnectionSettings"] = std::bind(&ConferenceConnectorJsonServerInterface::handleSetConnectionSettings, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["connectToConferenceService"] = std::bind(&ConferenceConnectorJsonServerInterface::handleConnectToConferenceService, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["disconnectFromConferenceService"] = std::bind(&ConferenceConnectorJsonServerInterface::handleDisconnectFromConferenceService, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["queryConferenceList"] = std::bind(&ConferenceConnectorJsonServerInterface::handleQueryConferenceList, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["createNewConference"] = std::bind(&ConferenceConnectorJsonServerInterface::handleCreateNewConference, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["destroyConference"] = std::bind(&ConferenceConnectorJsonServerInterface::handleDestroyConference, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["queryParticipantList"] = std::bind(&ConferenceConnectorJsonServerInterface::handleQueryParticipantList, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["createConferenceSession"] = std::bind(&ConferenceConnectorJsonServerInterface::handleCreateConferenceSession, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setSessionSettings"] = std::bind(&ConferenceConnectorJsonServerInterface::handleSetSessionSettings, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setSessionMediaSettings"] = std::bind(&ConferenceConnectorJsonServerInterface::handleSetSessionMediaSettings, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["startSession"] = std::bind(&ConferenceConnectorJsonServerInterface::handleStartSession, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["endSession"] = std::bind(&ConferenceConnectorJsonServerInterface::handleEndSession, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["updateSessionMedia"] = std::bind(&ConferenceConnectorJsonServerInterface::handleUpdateSessionMedia, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setParticipantPermissions"] = std::bind(&ConferenceConnectorJsonServerInterface::handleSetParticipantPermissions, this, std::placeholders::_1, std::placeholders::_2);

   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
   dynamic_cast<ConferenceConnectorInterface*>(mConferenceConnectorManager)->addSdkObserver(this);
}

ConferenceConnectorJsonServerInterface::~ConferenceConnectorJsonServerInterface()
{
   dynamic_cast<ConferenceConnectorInterface*>(mConferenceConnectorManager)->removeSdkObserver(this);
}

void ConferenceConnectorJsonServerInterface::Release()
{
   delete this;
}

int ConferenceConnectorJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

void ConferenceConnectorJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(conn, functionObjectVal);
   }
}

int ConferenceConnectorJsonServerInterface::handleConnectionClosed(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorJsonServerInterface::handleConnectionClosedImpl, this, conn));
   return kSuccess;
}

void ConferenceConnectorJsonServerInterface::handleConnectionClosedImpl(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn)
{
   auto it = mMapConnToHdl.find(conn);
   if (it != mMapConnToHdl.end())
   {
      mConferenceConnectorManager->destroyConferenceConnector(it->second);
      mMapConnToHdl.erase(it);
   }

   // forward the connection closed message to the Video interface now, in case it already handled
   // the connection closed event for this connection and missed its chance to clean up video capture
   // devices, since the PeerConnection RTP streams weren't torn down yet
   CPCAPI2::Media::VideoJsonApi* videoJsonApi = CPCAPI2::Media::VideoJsonApi::getInterface(mPhone);
   dynamic_cast<CPCAPI2::Media::VideoJsonServerInterface*>(videoJsonApi)->handleConnectionClosed(conn);
}

int ConferenceConnectorJsonServerInterface::handleCreateConferenceConnector(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal)
{
   StackLog(<< "ConferenceConnectorJsonServerInterface::handleCreateConferenceConnector()");

   ConferenceConnectorHandle h = mConferenceConnectorManager->createConferenceConnector();

   if (h == 0)
   {
      InfoLog(<< "ConferenceConnectorJsonServerInterface::handleCreateConferenceConnector(): failure");
   }

   mMapConnToHdl[conn] = h;

   JsonFunctionCall(mTransport, "onCreateConferenceConnector", "conn", h);
   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::handleDestroyConferenceConnector(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal)
{
   StackLog(<< "ConferenceConnectorJsonServerInterface::handleDestroyConferenceConnector()");
   ConferenceConnectorHandle h = 0;
   JsonDeserialize(functionObjectVal, "conn", h);

   mConferenceConnectorManager->destroyConferenceConnector(h);

   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::handleSetConnectionSettings(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal)
{
   StackLog(<< "ConferenceConnectorJsonServerInterface::handleSetConnectionSettings()");

   ConferenceConnectorHandle h = 0;
   ConferenceConnectorSettings settingsInfo;
   JsonDeserialize(functionObjectVal, "conn", h, "settings", settingsInfo);

   mConferenceConnectorManager->setConnectionSettings(h, settingsInfo);

   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::handleConnectToConferenceService(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal)
{
   StackLog(<< "ConferenceConnectorJsonServerInterface::handleConnectToConferenceService()");
   ConferenceConnectorHandle h = 0;
   JsonDeserialize(functionObjectVal, "conn", h);
   mConferenceConnectorManager->connectToConferenceService(h);

   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::handleDisconnectFromConferenceService(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "ConferenceConnectorJsonServerInterface::handleDisconnectFromConferenceService()");
   ConferenceConnectorHandle h = 0;
   JsonDeserialize(functionObjectVal, "conn", h);
   mConferenceConnectorManager->disconnectFromConferenceService(h);

   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::handleQueryConferenceList(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "ConferenceConnectorJsonServerInterface::handleQueryConferenceList()");
   ConferenceConnectorHandle h = 0;
   JsonDeserialize(functionObjectVal, "conn", h);
   mConferenceConnectorManager->queryConferenceList(h);

   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::handleCreateNewConference(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "ConferenceConnectorJsonServerInterface::handleCreateNewConference()");
   ConferenceConnectorHandle h = 0;
   CloudConferenceSettings settings;
   JsonDeserialize(functionObjectVal, "conn", h, JSON_VALUE(settings));

   mConferenceConnectorManager->createNewConference(h, settings);

   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::handleDestroyConference(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "ConferenceConnectorJsonServerInterface::handleDestroyConference()");
   CloudConferenceHandle h = 0;
   JsonDeserialize(functionObjectVal, "conference", h);
   mConferenceConnectorManager->destroyConference(h);

   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::handleQueryParticipantList(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "ConferenceConnectorJsonServerInterface::handleQueryParticipantList()");
   CloudConferenceHandle h = 0;
   JsonDeserialize(functionObjectVal, "conference", h);
   mConferenceConnectorManager->queryParticipantList(h);

   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::handleCreateConferenceSession(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "ConferenceConnectorJsonServerInterface::handleCreateConferenceSession()");
   CloudConferenceHandle h = 0;
   JsonDeserialize(functionObjectVal, "conference", h);
   CloudConferenceSessionSettings settings;
   JsonDeserialize(functionObjectVal, "settings", settings);
   CloudConferenceSessionHandle sess = mConferenceConnectorManager->createConferenceSession(h, settings);

   JsonFunctionCall(mTransport, "onCreateConferenceSession", "conference", h, "session", sess);
   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::handleSetSessionSettings(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "ConferenceConnectorJsonServerInterface::handleSetSessionSettings()");
   CloudConferenceSessionHandle h = 0;
   CloudConferenceSessionSettings settings;
   JsonDeserialize(functionObjectVal, "session", h, JSON_VALUE(settings));
   mConferenceConnectorManager->setSessionSettings(h, settings);

   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::handleSetSessionMediaSettings(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "ConferenceConnectorJsonServerInterface::handleSetSessionMediaSettings()");
   CloudConferenceSessionHandle h = 0;
   CloudConferenceSessionMediaSettings settings;
   JsonDeserialize(functionObjectVal, "session", h, JSON_VALUE(settings));
   mConferenceConnectorManager->setSessionMediaSettings(h, settings);

   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::handleStartSession(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "ConferenceConnectorJsonServerInterface::handleStartSession()");
   CloudConferenceSessionHandle h = 0;
   JsonDeserialize(functionObjectVal, "session", h);
   mConferenceConnectorManager->startSession(h);

   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::handleEndSession(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "ConferenceConnectorJsonServerInterface::handleEndSession()");
   CloudConferenceSessionHandle h = 0;
   JsonDeserialize(functionObjectVal, "session", h);
   mConferenceConnectorManager->endSession(h);

   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::handleUpdateSessionMedia(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "ConferenceConnectorJsonServerInterface::handleUpdateSessionMedia()");
   CloudConferenceSessionHandle h = 0;
   JsonDeserialize(functionObjectVal, "session", h);
   mConferenceConnectorManager->updateSessionMedia(h);

   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::handleSetParticipantPermissions(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "ConferenceConnectorJsonServerInterface::handleSetParticipantPermissions()");
   CloudConferenceSessionHandle h = 0;
   CloudConferenceParticipantHandle participant = 0;
   ConferenceSessionPermissions permissions;
   JsonDeserialize(functionObjectVal, "session", h, "participant", participant, "permissions", permissions);
   mConferenceConnectorManager->setParticipantPermissions(h, participant, permissions);

   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::onServiceConnectionStatusChanged(ConferenceConnectorHandle conn, const ServiceConnectionStatusEvent& args)
{
   JsonFunctionCall(mTransport, "onServiceConnectionStatusChanged", JSON_VALUE(conn), "authToken", args.authToken.authToken, "serverUri", args.serverUri, "statusDesc", args.statusDesc, "connectionStatus", args.connectionStatus);
   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::onConferenceCreated(ConferenceConnectorHandle conn, const ConferenceCreatedEvent& args)
{
   JsonFunctionCall(mTransport, "onConferenceCreated", JSON_VALUE(conn), "conference", args.conference);
   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::onConferenceEnded(ConferenceConnectorHandle conn, const ConferenceEndedEvent& args)
{
   JsonFunctionCall(mTransport, "onConferenceEnded", JSON_VALUE(conn), "conference", args.conference);
   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::onConferenceListUpdated(ConferenceConnectorHandle conn, const ConferenceListUpdatedEvent& args)
{
   JsonFunctionCall(mTransport, "onConferenceListUpdated", JSON_VALUE(conn), "conferenceList", args.conferenceList);
   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::onConferenceParticipantListUpdated(ConferenceConnectorHandle conn, const ConferenceParticipantListUpdatedEvent& args)
{
   JsonFunctionCall(mTransport, "onConferenceParticipantListUpdated", JSON_VALUE(conn),
      "conference", args.conference,
      "participantList", args.participantList,
      "addedParticipants", args.addedParticipants,
      "removedParticipants", args.removedParticipants,
      "updatedParticipants", args.updatedParticipants);
   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args)
{
   JsonFunctionCall(mTransport, "onConferenceSessionStatusChanged", JSON_VALUE(conn),
      "conference", args.conference,
      "session", args.session,
      "sessionStatus", args.sessionStatus,
      "permissions", args.permissions,
      "audio", args.audio,
      "video", args.video,
      "screenshare", args.screenshare);
   return kSuccess;
}

int ConferenceConnectorJsonServerInterface::onConferenceSessionMediaStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionMediaStatusChangedEvent& args)
{
   JsonFunctionCall(mTransport, "onConferenceSessionMediaStatusChanged", JSON_VALUE(conn),
      "conference", args.conference,
      "session", args.session,
      "mediaStatus", args.mediaStatus);
   return kSuccess;
}

#endif
