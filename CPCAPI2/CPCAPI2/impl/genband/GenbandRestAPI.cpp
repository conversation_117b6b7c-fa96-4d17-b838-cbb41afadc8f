#include "brand_branded.h"

#ifdef CPCAPI2_GENBAND_MODULE

#include "cpcapi2utils.h"
#include "GenbandRestAPI.h"

using CPCAPI2::SipAccount::SipAccountImpl;
namespace CPCAPI2
{
namespace Genband
{

GenbandRestAPI::GenbandRestAPI(Phone* phone) //: mPhone()
{
	mPhone = dynamic_cast<CPCAPI2::PhoneInterface*>(phone);
}

void GenbandRestAPI::Release()
{
   delete this;
}

int GenbandRestAPI::setHandler(CPCAPI2::SipAccount::SipAccountHandle account, GenbandRestAPIHandler* handler)
{
   mAccountIf->postToSdkThread(resip::resip_bind(&GenbandRestAPI::setHandlerImpl, this, account, handler));
   return kSuccess;
}

int GenbandRestAPI::setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, GenbandRestAPIHandler* handler)
{
      AccountMap::iterator it = mAccountMap.find(account);
      GenbandRestAPIEventHandler* evtMan = (it == mAccountMap.end() ? NULL : it->second);
      if (evtMan == NULL)
      {
         SipAccountImpl* acct = mAccountIf->getAccountImpl(account);

         if (!acct)
         {
            mAccountIf->fireError(L"Invalid account handle for GenbandRestAPI::setHandler");
            return kError;
         }

         evtMan = new GenbandRestAPIEventHandler(acct, mSipEventIf);
         mAccountMap[account] = evtMan;
      }
      evtMan->setSubscriptionHandler(handler);
      mSipEventIf->setHandlerImpl(account, L"genband", evtMan);
      return kSuccess;
}

GenbandResult GenbandRestAPI::makeCall(GenbandHeader header, GenbandBody body)
{
	return genbandPost(header, body);
} // makeCall

GenbandResult GenbandRestAPI::answerCall(GenbandHeader header, GenbandBody body)
{
	return genbandPut(header, body);
} // answerCall

GenbandResult GenbandRestAPI::rejectCall(GenbandHeader header, GenbandBody body)
{
	return genbandPost(header, body);
} // rejectCall

GenbandResult GenbandRestAPI::forwardCall(GenbandHeader header, GenbandBody body)
{
	return genbandPost(header, body);
} // forwardCall

GenbandResult GenbandRestAPI::placeOnHold(GenbandHeader header, GenbandBody body)
{
	return genbandPut(header, body);
} // placeOnHold

GenbandResult GenbandRestAPI::endCall(GenbandHeader header, GenbandBody body)
{
	return genbandDelete(header, body);
} // endCall

GenbandResult GenbandRestAPI::conferenceCall(GenbandHeader header, GenbandBody body)
{
	return genbandPost(header, body);
} // conferenceCall

GenbandResult GenbandRestAPI::subscribe(GenbandHeader header, GenbandBody body, CPCAPI2::SipAccount::SipAccountHandle account)
{
	SipEventSubscriptionHandle subscriptionHandle = mSipEventIf->createSubscription(account);
	CPCAPI2::SipEvent::SipEventSubscriptionSettings subscriptionSettings;
	subscriptionSettings.eventPackage = L"genband";
	subscriptionSettings.expiresSeconds = 3600;
	//subscriptionSettings.supportedMimeTypes.push_back(MimeType(L"application", L"xcap-diff+xml"));
	mSipEventIf->applySubscriptionSettings(subscriptionHandle, subscriptionSettings);
	
	SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
	if(acct != NULL)
	{
		cpc::string localUri = L"sip:" + acct->getSettings().username + L"@" + acct->getSettings().domain;
		mSipEventIf->addParticipant(subscriptionHandle, localUri);
	}
	mSipEventIf->start(subscriptionHandle);

	GenbandResult result = genbandPost(header, body);
	int pos1 = result.ResultMessage.find("\"notificationChannel\":");
	int pos2 = result.ResultMessage.find(',', pos1);
	std::string message = result.ResultMessage.substr(pos1, pos2-pos1);
	if( webSocketClientManager.connect(message, subscriptionHandle, new GenbandRestAPIEventHandler(acct, mSipEventIf)) == NULL )
	{
		result.ResultCode = 400;
		result.ResultMessage = "Failed to connect!";
	}
	return result;
} // subscribe

GenbandResult GenbandRestAPI::extendSubscribtion(GenbandHeader header, GenbandBody body)
{
	return genbandPut(header, body);
} // extendSubscribtion

GenbandResult GenbandRestAPI::unsubcribe(GenbandHeader header, GenbandBody body, CPCAPI2::SipAccount::SipAccountHandle account)
{
	webSocketClientManager.close(account);
	return genbandDelete(header, body);
} // unsubcribe

static size_t sendCurlData(void *ptr, size_t size, size_t nmemb, void *userp)
{
	PutData* userdata = (PutData*)userp;
	size_t curl_size = nmemb * size;
	size_t to_copy = (userdata->len < curl_size) ? userdata->len : curl_size;
	memcpy(ptr, userdata->data, to_copy);
	userdata->data += to_copy;
	userdata->len -= to_copy;
	return to_copy;
}

/**
* Genband http post wrapper function.
*/
GenbandResult GenbandRestAPI::genbandPost(GenbandHeader header, GenbandBody body)
{
	CURL *curl;
	CURLcode res;
	struct curl_slist *headers = applyHeaders(header);
	char errorBuffer[CURL_ERROR_SIZE+1];
	PutData bodyData; 
	GenbandResult result;

	bodyData.len = body.Body.size();
	bodyData.data = new char[bodyData.len];
	memcpy(bodyData.data, body.Body.c_str(), bodyData.len);

	curl = curl_easy_init();
	if(curl) 
	{
		curl_easy_setopt(curl, CURLOPT_URL, body.Url.c_str());
		curl_easy_setopt(curl, CURLOPT_POST, 1L);
		curl_easy_setopt(curl, CURLOPT_ERRORBUFFER, errorBuffer);
		curl_easy_setopt(curl, CURLOPT_READFUNCTION, &sendCurlData);
		curl_easy_setopt(curl, CURLOPT_READDATA, &bodyData);
		curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, bodyData.len);
		curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers); 
		//Set http authentication, username and password
		curl_easy_setopt(curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
		resip::Data userpass = header.Username.c_str();
		userpass.append(":", 1);
		userpass.append(header.Password.c_str(), header.Password.size());
		curl_easy_setopt(curl, CURLOPT_USERPWD, userpass.c_str()); 

		// Disable SSL
		curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
		curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

		/* Perform the request, res will get the return code */ 
		res = curl_easy_perform(curl);
		/* Check for errors */ 
		if(res != CURLE_OK)
		{
			result.ResultCode = res;
			result.ResultMessage = errorBuffer;
		}
		else
		{
			curl_easy_getinfo (curl, CURLINFO_RESPONSE_CODE, &result.ResultCode);
			result.ResultMessage = bodyData.data;
		}

		/* always cleanup */ 
		curl_easy_cleanup(curl);
		curl_slist_free_all(headers);
	}
	return result;
}

/**
* Genband http put wrapper function.
*/
GenbandResult GenbandRestAPI::genbandPut(GenbandHeader header, GenbandBody body)
{
	CURL *curl;
	CURLcode res;
	struct curl_slist *headers = applyHeaders(header);
	char errorBuffer[CURL_ERROR_SIZE+1];
	PutData bodyData; 
	GenbandResult result;

	bodyData.len = body.Body.size();
	bodyData.data = new char[bodyData.len];
	memcpy(bodyData.data, body.Body.data(), bodyData.len);

	curl = curl_easy_init();
	if(curl) 
	{
		curl_easy_setopt(curl, CURLOPT_URL, body.Url.c_str());
		curl_easy_setopt(curl, CURLOPT_UPLOAD, 1L);
		curl_easy_setopt(curl, CURLOPT_ERRORBUFFER, errorBuffer);
		curl_easy_setopt(curl, CURLOPT_READFUNCTION, &sendCurlData);
		curl_easy_setopt(curl, CURLOPT_READDATA, &bodyData);
		curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers); 
		curl_easy_setopt(curl, CURLOPT_USERNAME, header.Username.c_str());
		curl_easy_setopt(curl, CURLOPT_PASSWORD, header.Password.c_str());

		/* Perform the request, res will get the return code */
		res = curl_easy_perform(curl);
		/* Check for errors */
		if(res != CURLE_OK)
		{
			result.ResultCode = res;
			result.ResultMessage = errorBuffer;
		}
		else
		{
			curl_easy_getinfo (curl, CURLINFO_RESPONSE_CODE, &result.ResultCode);
			result.ResultMessage = bodyData.data;
		}

		/* always cleanup */ 
		curl_easy_cleanup(curl);
		curl_slist_free_all(headers);
	}
	return result;
}

/**
* Genband http delete wrapper function.
*/
GenbandResult GenbandRestAPI::genbandDelete(GenbandHeader header, GenbandBody body)
{
	CURL *curl;
	CURLcode res;
	struct curl_slist *headers = applyHeaders(header);
	char errorBuffer[CURL_ERROR_SIZE+1];
	GenbandResult result;

	errorBuffer[CURL_ERROR_SIZE] = '\0';
 
    curl = curl_easy_init();
    if(curl) 
	{
        curl_easy_setopt(curl, CURLOPT_URL, body.Url.c_str());
		curl_easy_setopt(curl, CURLOPT_ERRORBUFFER, errorBuffer);
        curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST,"DELETE");
		curl_easy_setopt(curl, CURLOPT_USERNAME, header.Username.c_str());
		curl_easy_setopt(curl, CURLOPT_PASSWORD, header.Password.c_str());
        res = curl_easy_perform(curl);
        if(res != CURLE_OK)
		{
			result.ResultCode = res;
			result.ResultMessage = errorBuffer;
		}
		else
		{
			curl_easy_getinfo (curl, CURLINFO_RESPONSE_CODE, &result.ResultCode);
			result.ResultMessage = "OK";
		}
        curl_easy_cleanup(curl);
    }
    return result;
}

curl_slist* GenbandRestAPI::applyHeaders(GenbandHeader genbandHeaders)
{
	struct curl_slist *headers = NULL;
	resip::Data userAgent = "User-agent : ";
	resip::Data authorization = "Authorization : ";

	userAgent.append(genbandHeaders.UserAgent.c_str(), genbandHeaders.UserAgent.size());
	authorization.append(genbandHeaders.Authorization.c_str(), genbandHeaders.Authorization.size());
	headers = curl_slist_append(headers, userAgent.c_str());  
	headers = curl_slist_append(headers, authorization.c_str());  
	headers = curl_slist_append(headers, "Accept : application/json");
	headers = curl_slist_append(headers, "Connection : keep-alive");
	headers = curl_slist_append(headers, "Content-Type: application/json");

	return headers;
}

} // namespace Genband
} // namespace CPCAPI2
#endif // CPCAPI2_GENBAND_MODULE
