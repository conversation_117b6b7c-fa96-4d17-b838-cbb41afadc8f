#include "JsonDataImpl.h"

#include <atomic>

#include <writer.h> // rapidjson

namespace CPCAPI2
{
namespace Json
{

int32_t JsonDataImpl::getMessageSize() const
{
   return mBuffer.GetSize();
};

int32_t JsonDataImpl::getBinarySize() const
{
   return mBinData.size();
};

const char* JsonDataImpl::getMessageData() const
{
   return mBuffer.GetString();
};

const char* JsonDataImpl::getBinaryData() const
{
   return mBinData.c_str();
};

bool JsonDataImpl::isBinary() const
{
   return mBinData.size() > 0;
};

void JsonDataImpl::setBinaryData(const char* buffer, uint32_t size)
{
   mBinData.assign(buffer, size);
};

rapidjson::StringBuffer& JsonDataImpl::getStringBuffer()
{
   return mBuffer;
}

JsonDataPointer::JsonDataPointer()
{
   count = new std::atomic<std::int32_t>(1);
   ptr = new JsonDataImpl();
}

JsonDataPointer::JsonDataPointer(const JsonDataPointer& rhs) :
   count(rhs.count),
   ptr(rhs.ptr)
{
   increment();
}

JsonDataPointer& JsonDataPointer::operator=(const JsonDataPointer& rhs)
{
   if (&rhs != this)
   {
      decrement();
      count = rhs.count;
      ptr = rhs.ptr;
      increment();
   }
   return *this;
}

JsonDataPointer::~JsonDataPointer()
{
   decrement();
}

void JsonDataPointer::increment()
{
   (*(std::atomic<std::int32_t>*)count)++;
}

void JsonDataPointer::decrement()
{
   if (0 == --(*(std::atomic<std::int32_t>*)count))
   {
      delete (std::atomic<std::int32_t>*)count;
      count = nullptr;

      delete ptr;
      ptr = nullptr;
   }
}

JsonDataPointer MakeJsonDataPointer() { return JsonDataPointer(); }

} // namespace Json
} // namespace CPCAPI2
