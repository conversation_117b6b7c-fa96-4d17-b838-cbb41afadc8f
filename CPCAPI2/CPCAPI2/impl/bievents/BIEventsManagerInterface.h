#pragma once
#ifndef __CPCAPI2_BIEVENTS_MANAGER_INTERFACE_H__
#define __CPCAPI2_BIEVENTS_MANAGER_INTERFACE_H__

#include <mutex>
#include <map>
#include <thread>

#include <boost/asio.hpp>

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>

#include <curl/curl.h>

#include "cpcapi2defs.h"
#include "../phone/PhoneModule.h"
#include "../util/AutoTestProcessor.h"
#include <bievents/BIEventsManager.h>
#include "BIEventsInternalTypes.h"

namespace CPCAPI2
{
   class Phone;
   class RPCIdentifier;

   namespace BIEvents
   {
      class BIEventsManagerImpl;
      class BIEventsHelperInterface;

      class BIEventsManagerInterface :
         public BIEventsManager,
         public PhoneModule
#ifdef CPCAPI2_AUTO_TEST
       , public AutoTestProcessor
#endif
      {
      public:
         BIEventsManagerInterface(Phone* phone);

         // PhoneModule implementation
         void Release() OVERRIDE;
         void PreRelease() OVERRIDE;
         bool PreReleaseCompleted() OVERRIDE;

         // BIEventManager Implementation
         BIEventsHandle create( void ) OVERRIDE;
         int configureSettings( const BIEventsHandle& hEvents, const struct BIEventsSettings& settings ) OVERRIDE;
         int setHandler( const BIEventsHandle& hEvents, BIEventsHandler * notificationHandler ) OVERRIDE;
         int enable( const BIEventsHandle& hEvents ) OVERRIDE;
         int disable( const BIEventsHandle& hEvents ) OVERRIDE;
         int destroy( const BIEventsHandle& hEvents ) OVERRIDE;
         int postEvent( const BIEventsHandle& hEvents, const BIEventHeader& eventHeader, const BIEventBody& eventBody, EventID& outEventID ) OVERRIDE;
         int purgeCache( const BIEventsHandle& hEvents ) OVERRIDE;
         int process( int timeout ) OVERRIDE;

         virtual void setCallbackHook(void (*cbHook)(void*), void* context);
         void setCallbackHookImpl( void (*cbHook)(void*), void* context );

         // delegates
         int createImpl( const BIEventsHandle& hEvents );
         int configureSettingsImpl( const BIEventsHandle& hEvents, const struct BIEventsSettings& settings );
         int setHandlerImpl( const BIEventsHandle& hEvents, BIEventsHandler * notificationHandler );
         int enableImpl( const BIEventsHandle& hEvents );
         int disableImpl( const BIEventsHandle& hEvents );
         int destroyImpl( const BIEventsHandle& hEvents );
         int purgeCacheImpl( const BIEventsHandle& hEvents );
         int postEventImpl( const BIEventsHandle& hEvents, const BIEventHeader& eventHeader, const BIEventBody& eventBody, const CPCAPI2::RPCIdentifier& inEventID );

         // For sub-interface
         BIEventsHelperInterface *getHelper( void );

#ifdef CPCAPI2_AUTO_TEST
         // AutoTestProcessor implementation
         AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif

      private: // methods

         virtual ~BIEventsManagerInterface();

      private: // data

         BIEventsHelperInterface *m_Helper;

         // used for dispatching
         boost::asio::io_service& m_IOService;

         // Callback Fifo which should be used to marshal the events (process method should be called)
         resip::Fifo< resip::ReadCallbackBase > m_CallbackFifo;

         std::map< BIEventsHandle, std::shared_ptr< BIEventsManagerImpl > > m_InfoMap;

         std::vector< std::weak_ptr< BIEventsManagerImpl > > m_PendingShutdownImpls;

         // The only reason this mutex is here, is to protect the concurrent
         // access happening in the Release() method. It should never block
         // excepting in the situation where access to the Impl class is
         // happening while Release is ocurring.
         std::recursive_mutex m_InfoMutex;

         // Set to false once shutdown commences (to prevent further events)
         bool m_Shutdown;
         void (*m_CbHook)(void*);
         void* m_Context;
      };
   }
}
#endif // __CPCAPI2_BIEVENTS_MANAGER_INTERFACE_H__
