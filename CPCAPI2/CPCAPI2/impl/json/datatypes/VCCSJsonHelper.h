// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef VCCS_JSON_HELPER_H
#define VCCS_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/experimental/vccs/VccsAccountHandler.h>
#include <interface/experimental/vccs/VccsAccountManager.h>
#include <interface/experimental/vccs/VccsAccountSettings.h>
#include <interface/experimental/vccs/VccsAccountTypes.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::VCCS::Account::VccsAccountStateChangedEvent& v)
    {
      w.StartObject();
      Write(w, "oldState", v.oldState);
      Write(w, "newState", v.newState);
      Write(w, "reason", v.reason);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::VCCS::Account::VccsAccountStateChangedEvent& v)
    {
      Read(json, "oldState", v.oldState);
      Read(json, "newState", v.newState);
      Read(json, "reason", v.reason);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::VCCS::Account::ErrorEvent& v)
    {
      w.StartObject();
      Write(w, "errorText", v.errorText);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::VCCS::Account::ErrorEvent& v)
    {
      Read(json, "errorText", v.errorText);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::VCCS::Account::VccsAccountHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::VCCS::Account::VccsAccountHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::VCCS::Account::VccsAccountManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::VCCS::Account::VccsAccountManager& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::VCCS::Account::VccsAccountSettings& v)
    {
      w.StartObject();
      Write(w, "wsSettings", v.wsSettings);
      Write(w, "group", v.group);
      Write(w, "userName", v.userName);
      Write(w, "password", v.password);
      Write(w, "displayName", v.displayName);
      Write(w, "xmppUserName", v.xmppUserName);
      Write(w, "autoSubscribeAfterNetworkChange", v.autoSubscribeAfterNetworkChange);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::VCCS::Account::VccsAccountSettings& v)
    {
      Read(json, "wsSettings", v.wsSettings);
      Read(json, "group", v.group);
      Read(json, "userName", v.userName);
      Read(json, "password", v.password);
      Read(json, "displayName", v.displayName);
      Read(json, "xmppUserName", v.xmppUserName);
      Read(json, "autoSubscribeAfterNetworkChange", v.autoSubscribeAfterNetworkChange);
    }
  }
}
#pragma warning(pop)
#endif // VCCS_JSON_HELPER_H