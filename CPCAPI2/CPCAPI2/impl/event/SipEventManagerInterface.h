#pragma once

#if !defined(CPCAPI2_SIP_EVENT_MANAGER_INTERFACE_H)
#define CPCAPI2_SIP_EVENT_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "event/SipEventManager.h"
#include "event/SipEventManagerInternal.h"
#include "event/SipEventSubscriptionHandler.h"
#include "genband/GenbandWatcherinfoExt.h"
#include "../phone/PhoneModule.h"
#include "../phone/PhoneInterface.h"

#include <map>
#include <string>
#include <vector>

namespace CPCAPI2
{

namespace SipAccount
{
class SipAccountInterface;
}

namespace SipEvent
{
class SipEventManagerImpl;
class SipEventSubscriptionCreationInfo;

class SipEventManagerInterface : public CPCAPI2::EventSource2< CPCAPI2::EventHandler<SipEventSubscriptionHandler, CPCAPI2::SipAccount::SipAccountHandle, std::string> >,
                                 public SipEventManager,
                                 public GenbandWatcherinfoExt,
                                 public PhoneModule,
                                 public SipEventManagerInternal
{
public:
   
   SipEventManagerInterface(Phone* phone);
   virtual ~SipEventManagerInterface();
   
   virtual bool recreateSubscription(CPCAPI2::SipAccount::SipAccountHandle account, SipEventSubscriptionHandle h);
   virtual int setEventServer(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress) OVERRIDE;
   SipEventSubscriptionCreationInfo* getCreationInfo(SipEventSubscriptionHandle h);
   virtual cpc::vector<SipEventSubscriptionHandle> getSubscriptions(CPCAPI2::SipAccount::SipAccountHandle account);
   
   /** Functions with associated Impl functions */
   virtual void Release() OVERRIDE;
   virtual SipEventSubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;
   virtual SipEventSubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle account, SipEventSubscriptionHandle h);
   virtual int applySubscriptionSettings(SipEventSubscriptionHandle subscription, const SipEventSubscriptionSettings& settings) OVERRIDE;
   virtual int addParticipant(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress) OVERRIDE;
   virtual int start(SipEventSubscriptionHandle subscription) OVERRIDE;
   virtual int end(SipEventSubscriptionHandle subscription, SipSubscriptionTerminateReason reason = SipSubscriptionTerminateReason_NoResource, int retryAfter = -1) OVERRIDE;
   virtual int reject(SipEventSubscriptionHandle subscription, unsigned int rejectReason) OVERRIDE;
   virtual int accept(SipEventSubscriptionHandle subscription, const SipEventState& eventState) OVERRIDE;
   virtual int provisionalAccept(SipEventSubscriptionHandle subscription, const SipEventState& eventState) OVERRIDE;
   virtual int notify(SipEventSubscriptionHandle subscription, const SipEventState& eventState) OVERRIDE;
   virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& eventType, SipEventSubscriptionHandler* handler) OVERRIDE;
   virtual int refresh(SipEventSubscriptionHandle subscription) OVERRIDE;

   /** Genband eow extension */
   virtual int addSupportedEowHeader(SipEventSubscriptionHandle h) OVERRIDE;

   /** Exposed Impl functions */
   int setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& eventType, SipEventSubscriptionHandler* handler);
   int createSubscriptionImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipEventSubscriptionHandle h);
   int applySubscriptionSettingsImpl(SipEventSubscriptionHandle subscription, const SipEventSubscriptionSettings& settings);
   int startImpl(SipEventSubscriptionHandle subscription);
   int addParticipantImpl(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress, bool isEventServer = false);
   int notifyImpl(SipEventSubscriptionHandle subscription, const SipEventState& eventState, const std::string* contents = 0);
   int endImpl(SipEventSubscriptionHandle subscription, SipSubscriptionTerminateReason reason = SipSubscriptionTerminateReason_NoResource, int retryAfter = -1);
   
   // SipEventManagerInternal
   int accept(SipEventSubscriptionHandle subscription, const SipEventState& eventState, bool suppressNotify) OVERRIDE;
   
   // EventSource2
   void addAppHandler(SipEventSubscriptionHandler* handler, CPCAPI2::SipAccount::SipAccountHandle handle, std::string filter);
   void addSdkObserver(SipEventSubscriptionHandler* handler, CPCAPI2::SipAccount::SipAccountHandle handle, std::string filter);
   void addSdkObserverSafe(const std::shared_ptr<SipEventSubscriptionHandler>& handler, CPCAPI2::SipAccount::SipAccountHandle handle, std::string filter);


private:
   void addEventType(const CPCAPI2::SipAccount::SipAccountHandle& account, const std::string& eventType);

   /** Impl functions */
   int rejectImpl(SipEventSubscriptionHandle subscription, unsigned int rejectReason);
   int acceptImpl(SipEventSubscriptionHandle subscription, const SipEventState& eventState, bool suppressNotify);
   int provisionalAcceptImpl(SipEventSubscriptionHandle subscription, const SipEventState& eventState);
   int addSupportedEowHeaderImpl(SipEventSubscriptionHandle h);
   int refreshImpl(SipEventSubscriptionHandle subscription);

private:
   
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, SipEventManagerImpl*> AccountMap;
   typedef std::map<SipEventSubscriptionHandle, bool> EowMap;
   std::shared_ptr<AccountMap> mAccountMap;
   std::shared_ptr<EowMap> mEowMap;
   std::map<std::tuple<CPCAPI2::SipAccount::SipAccountHandle, std::string>, SipEventSubscriptionHandler*> mHandlers;

   CPCAPI2::PhoneInterface* mPhone;
   CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;
   SipEventSubscriptionHandle mNextSubscriptionHandle;
   
};

std::ostream& operator<<(std::ostream& os, const NewSubscriptionEvent& evt);
std::ostream& operator<<(std::ostream& os, const SubscriptionEndedEvent& evt);
std::ostream& operator<<(std::ostream& os, const IncomingEventStateEvent& evt);
std::ostream& operator<<(std::ostream& os, const IncomingResourceListEvent& evt);
std::ostream& operator<<(std::ostream& os, const SubscriptionStateChangedEvent& evt);
std::ostream& operator<<(std::ostream& os, const NotifySuccessEvent& evt);
std::ostream& operator<<(std::ostream& os, const NotifyFailureEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipEvent::ErrorEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipEventState& evt);
}
}
#endif // CPCAPI2_SIP_EVENT_MANAGER_INTERFACE_H
