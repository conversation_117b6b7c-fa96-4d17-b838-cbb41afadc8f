#pragma once
#ifndef __PROVISIONING_BROADSOFT_XSI_TYPES_INTERNAL_H__
#define __PROVISIONING_BROADSOFT_XSI_TYPES_INTERNAL_H__

#include <broadsoftxsi/BroadsoftXsiTypes.h>
#include <string>

namespace CPCAPI2
{
   namespace BroadsoftXsi
   {
      class BroadsoftXsiImpl;

      typedef struct BroadsoftXsiInfo
      {
         LocalSettings localSettings;

         BroadsoftXsiImpl *pImpl;
      } BroadsoftXsiInfo;

      typedef struct XsiParameter
      {
         cpc::string name;
         cpc::string value;
      } XsiParameter;

      enum XsiAction
      {
         XsiAction_Services,
         XsiAction_CallLogs,
         XsiAction_CallLogsMissed,
         XsiAction_CallLogsPlaced,
         XsiAction_CallLogsReceived,
         XsiAction_DeleteCallLogs,
         XsiAction_DeleteCallLogsMissed,
         XsiAction_DeleteCallLogsPlaced,
         XsiAction_DeleteCallLogsReceived,
         XsiAction_DeleteCallLogMissed,
         XsiAction_DeleteCallLogPlaced,
         XsiAction_DeleteCallLogReceived,
         XsiAction_EnterpriseDirectory,
         XsiAction_QueryBroadworksAnywhereSettings,
         XsiAction_QueryCallForwardingAlwaysSettings,
         XsiAction_QueryCallForwardingBusySettings,
         XsiAction_QueryCallForwardingNoAnswerSettings,
         XsiAction_QueryDoNotDisturbSettings,
         XsiAction_QueryRemoteOfficeSettings,
         XsiAction_QuerySimultaneousRingSettings,
         XsiAction_SetBroadworksAnywhereSettings,
         XsiAction_AddBroadworksAnywhereLocation,
         XsiAction_DeleteBroadworksAnywhereLocation,
         XsiAction_UpdateBroadworksAnywhereLocation,
         XsiAction_SetCallForwardingAlwaysSettings,
         XsiAction_SetCallForwardingBusySettings,
         XsiAction_SetCallForwardingNoAnswerSettings,
         XsiAction_SetDoNotDisturbSettings,
         XsiAction_SetRemoteOfficeSettings,
         XsiAction_SetSimultaneousRingSettings,
         XsiAction_CallStart
      };
   }
}

#endif /* __PROVISIONING_BROADSOFT_XSI_TYPES_INTERNAL_H__ */
