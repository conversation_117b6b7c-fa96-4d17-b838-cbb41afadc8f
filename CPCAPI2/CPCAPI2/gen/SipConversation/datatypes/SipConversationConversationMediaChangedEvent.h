#include "jsonrpc/OptionalSerialization.h"
#include <any>
#include "jsonrpc/VectorSerialization.h"
#include "jsonrpc/StringSerialization.h"
#include <nlohmann/json.hpp>
#include "MediaInfo.h"

#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONCONVERSATIONMEDIACHANGEDEVENT_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONCONVERSATIONMEDIACHANGEDEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationConversationMediaChangedEvent
  {
  public:
    std::optional<int32_t> conversationHandle;
    std::optional<cpc::vector<MediaInfo>> localMediaInfo;
    std::optional<cpc::vector<MediaInfo>> remoteMediaInfo;
    std::optional<bool> localHold;
    std::optional<bool> remoteHold;

    SipConversationConversationMediaChangedEvent() = default;

    SipConversationConversationMediaChangedEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationConversationMediaChangedEvent& o)
    {
      j = nlohmann::json::object();
       if (o.conversationHandle) { j["conversationHandle"] = *(o.conversationHandle); }
     if (o.localMediaInfo) { j["localMediaInfo"] = *(o.localMediaInfo); }
     if (o.remoteMediaInfo) { j["remoteMediaInfo"] = *(o.remoteMediaInfo); }
     if (o.localHold) { j["localHold"] = *(o.localHold); }
     if (o.remoteHold) { j["remoteHold"] = *(o.remoteHold); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationConversationMediaChangedEvent& o)
    {
     if (j.contains("conversationHandle")) { o.conversationHandle = j.at("conversationHandle"); }
     if (j.contains("localMediaInfo")) { o.localMediaInfo = j.at("localMediaInfo"); }
     if (j.contains("remoteMediaInfo")) { o.remoteMediaInfo = j.at("remoteMediaInfo"); }
     if (j.contains("localHold")) { o.localHold = j.at("localHold"); }
     if (j.contains("remoteHold")) { o.remoteHold = j.at("remoteHold"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONCONVERSATIONMEDIACHANGEDEVENT_H
