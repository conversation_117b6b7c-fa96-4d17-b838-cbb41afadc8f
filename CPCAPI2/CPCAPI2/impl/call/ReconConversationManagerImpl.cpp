#ifndef NOMINMAX
#define NOMINMAX
#endif

#include "brand_branded.h"

#if (CPCAPI2_BRAND_CALL_MODULE == 1)
#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_CALL
#include "ReconConversationManagerImpl.h"
#include "cpcapi2utils.h"
#include "SipAVConversationManagerInterface.h"
#include "SipDTMFRelayUtils.h"
#include "CPCTurnSocketFactory.h"
#include "../tsm/TseTurnSocketFactory.h"
#include "../tsm/TseRTPPortAllocator.h"
#include "SipAVConversationManagerImpl.h"
#include "BroadsoftCallControlMonitor.h"
#include "account/SipAccountInterface.h"
#include "../util/cpc_logger.h"
#include "../util/StunClient.h"
#include "../util/SipHelpers.h"
#include "../phone/NetworkChangeManagerImpl.h"
#include "../util/CharEncodingHelper.h"
#include "MosEstimator.h"

#include "../media/AudioInterface.h"
#include <MediaStackImpl.hxx>
#include <MixerImpl.hxx>
#include <CodecFactoryImpl.hxx>

#include <resip/dum/ClientInviteSession.hxx>
#include <resip/dum/ServerInviteSession.hxx>
#include <resip/dum/InviteSessionHandler.hxx>
#include <resip/dum/ClientSubscription.hxx>
#include <resip/dum/ServerSubscription.hxx>

#include <resip/stack/PlainContents.hxx>

#include <rutil/DnsUtil.hxx>
#if _WIN32
#include <rutil/WinCompat.hxx>
#endif
#include <rutil/IpSynth.hxx>

#include <resip/stack/InternalTransport.hxx>
#include <resip/stack/SipFrag.hxx>
#include <resip/stack/ExtensionParameter.hxx>
#include <resip/stack/ExtensionHeader.hxx>

#include <resip/recon/UserAgent.hxx>
#include <resip/recon/RemoteParticipant.hxx>
#include <resip/recon/ConversationManagerCmds.hxx>
#include <resip/recon/Conversation.hxx>
#include <resip/recon/media/MediaStack.hxx>
#include <resip/recon/media/RtpDecorator.hxx>

#include <voe_codec.h>
#include <vie_codec.h>
#include <voe_rtp_rtcp.h>
#include <voe_neteq_stats.h>
#include <vie_rtp_rtcp.h>
#include <voe_base.h>
#include <voe_file.h>
#include <webrtc/modules/audio_device/include/audio_device.h>

#include <../webrtc_recon/codecs/CpsiCodec.hxx>

#include "../util/IpHelpers.h"

#include <algorithm>
#include <sstream>

using namespace resip;
using namespace recon;
using namespace CPCAPI2::SipAccount;

namespace CPCAPI2
{
namespace SipConversation
{
// start conversation handle(s) at 1 so that 0 can be used as an invalid handle indicator
// and an initializer
SipConversationHandle SipConversationHandleFactory::sNextConversationHandle = 1;

ReconConversationManagerImpl::ReconConversationManagerImpl(recon::UserAgent& ua, SipAccountImpl& account, SipAVConversationManagerImpl& callAcct, const std::map<CPCAPI2::NetworkTransport, SipConversationSettings>& convSettings, const resip::Data& overrideSourceIpSignalling, const resip::Data& overrideSourceIpTransport, void* tscconfig, bool dtlsSupported)
   : recon::ConversationManager(ua),
     mAccount(account),
     mCallImpl(callAcct),
     mAcctSettings(account.getSettings()),
     mConvSettings(convSettings),
     mConvState(ConversationState_None),
     mDefaultConvProfileHandle(0),
     mAdornmentHandler(NULL),
     mAudioLevelMonitor(NULL),
     mOverrideSourceIpSignalling(overrideSourceIpSignalling),
     mOverrideSourceIpTransport(overrideSourceIpTransport),
     mNackPliSupported(true),
     mTmmbrSupported(true),
     mDtlsSupported(dtlsSupported),
     mThrottledSipInfoPliSender(new Utils::ThrottledInvoker(&mAccount.getPhone()->getSdkModuleThread(), 3000 /* limit SIP INFO PLI requests to one every 3s */))
{
   createConversationProfiles(ua.getDialogUsageManager());
   mLocalParticipant = createLocalParticipant();

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)
   if (mAcctSettings.tunnelConfig.useTunnel && mAcctSettings.tunnelConfig.tunnelType == TunnelType_TSCF)
   {
      delete mRTPAllocator; mRTPAllocator = NULL;
      mRTPAllocator = new TseRTPPortAllocator(&ua, tscconfig);

      getFlowManager().installTurnSocketFactory(new TseTurnSocketFactory(tscconfig));
   }
   else
#endif
   {
      getFlowManager().installTurnSocketFactory(new CPCTurnSocketFactory());
   }

   getFlowManager().createResolver(mDum->getSipStack().getDnsStub());
}

ReconConversationManagerImpl::~ReconConversationManagerImpl()
{
   if (mAudioLevelMonitor != nullptr)
   {
      mAudioLevelMonitor->destroy();
   }
   mCallImpl.setReconConvMan(NULL);

   if (mThrottledSipInfoPliSender)
   {
      mThrottledSipInfoPliSender->reactorSafeRelease(&mAccount.getPhone()->getSdkModuleThread());
   }
}

void ReconConversationManagerImpl::shutdown()
{
   // using the "normal" ConversationManager::shutdown() is bad when we try to "force" everything to get
   // cleaned up at SDK shutdown time; it doesn't clean up everything but instead pokes DUM to send out BYEs, etc.
   // and then finishes cleanup when the dialogset/dialogs terminate (and we can't wait that long)
   //ConversationManager::shutdown();
   destroyParticipant(mLocalParticipant);
}

void ReconConversationManagerImpl::destroyAllConversations()
{
   SipCallCreationInfoMap callCreationInfoCopy = mCallCreationInfo;
   SipCallCreationInfoMap::const_iterator it = callCreationInfoCopy.begin();
   for (; it != callCreationInfoCopy.end(); ++it)
   {
      if (it->second->reconConversation != 0)
      {
         destroyConversation(it->second->reconConversation);
      }
      // also destroy participant in case it was not part of the conversation (on hold)
      if (it->second->reconRemoteParticipant != 0)
      {
         RemoteParticipant* remotePart = dynamic_cast<RemoteParticipant*>(getParticipant(it->second->reconRemoteParticipant));
         if (remotePart)
            remotePart->destroyParticipant("UA Shutting Down");
      }
   }
}

void ReconConversationManagerImpl::onRtpStreamError(const std::shared_ptr<recon::RtpStream>& /*rtpStream*/, int errorCode, const resip::Data& errorMsg, int appState)
{
   SipConversationHandle h = (SipConversationHandle)appState;
   CPCAPI2::SipConversation::ErrorEvent args;
   {
      resip::Data errTxt;
      {
         resip::DataStream ds(errTxt);
         ds << "RTP stream error (" << errorCode << "): " << errorMsg;
      }
      args.errorText = errTxt.c_str();
   }
   mCallImpl.fireEvent(cpcEvent(SipConversationHandler, onError), h, args);
}

void ReconConversationManagerImpl::onKeyFrameRequest(const std::shared_ptr<recon::RtpStream>& rtpStream, int appState)
{
   if (!std::dynamic_pointer_cast<webrtc_recon::RtpStreamImpl>(rtpStream)->supportsKeyFrameRequestsInRTCP())
   {
      mThrottledSipInfoPliSender->reqInvoke([appState, this]()
      {
         recon::ParticipantHandle reconPartHandle = (recon::ParticipantHandle)appState;
         static resip::PlainContents videoUpdateContents(
            "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\r\n"
            "<media_control>\r\n"
            "  <vc_primitive>\r\n"
            "    <to_encoder>\r\n"
            "      <picture_fast_update />\r\n"
            "    </to_encoder>\r\n"
            "  </vc_primitive>\r\n"
            "</media_control>\r\n",
            resip::Mime("application", "media_control+xml"));
         sendInfo(reconPartHandle, videoUpdateContents);
      });
   }
}

recon::ConversationManager::MediaAttributes ReconConversationManagerImpl::toReconMediaAttribs(const cpc::vector<MediaInfo>& mi, unsigned int defaultMixId)
{
   recon::ConversationManager::MediaAttributes ret;
   ret.audioDirection = recon::ConversationManager::MediaDirection_None;
   ret.videoDirection = recon::ConversationManager::MediaDirection_None;
   cpc::vector<MediaInfo>::const_iterator it = mi.begin();
   for (; it != mi.end(); ++it)
   {
      recon::ConversationManager::MediaDirection reconMediaDir = recon::ConversationManager::MediaDirection_None;
      switch (it->mediaDirection)
      {
      case SipConversation::MediaDirection_Inactive:
         reconMediaDir = recon::ConversationManager::MediaDirection_Inactive;
         break;
      case SipConversation::MediaDirection_ReceiveOnly:
         reconMediaDir = recon::ConversationManager::MediaDirection_ReceiveOnly;
         break;
      case SipConversation::MediaDirection_SendOnly:
         reconMediaDir = recon::ConversationManager::MediaDirection_SendOnly;
         break;
      case SipConversation::MediaDirection_SendReceive:
         reconMediaDir = recon::ConversationManager::MediaDirection_SendReceive;
         break;
      case SipConversation::MediaDirection_None:
         FALL_THROUGH;
      default:
         break;
      }

      if (it->mediaType == MediaType_Audio)
      {
         ret.audioDirection = reconMediaDir;
         ret.audioMixContribution = it->conferenceMixContribution;
         ret.audioMixId = ((it->conferenceMixId == -1) ? defaultMixId : (unsigned int)it->conferenceMixId);
         
         switch (it->mediaEncryptionOptions.mediaEncryptionMode)
         {
            case MediaEncryptionMode_SRTP_SDES_Encrypted:
               ret.secureAudioMode = recon::ConversationManager::Srtp;
               break;
            case MediaEncryptionMode_SRTP_DTLS_Encrypted:
               ret.secureAudioMode = recon::ConversationManager::SrtpDtls;
               break;
            default:
               ret.secureAudioMode = recon::ConversationManager::NoSecureMedia;
               break;
         }
         
         ret.secureAudioRequired = it->mediaEncryptionOptions.secureMediaRequired;
         ret.secureAudioCrypto = ReconConversationManagerImpl::convertMediaCryptoSuite(it->mediaCrypto);
         ret.secureAudioDefaultCryptoSuites = ReconConversationManagerImpl::convertMediaCryptoSuites(it->mediaEncryptionOptions.mediaCryptoSuites);
         ret.audioIsLocallyDisabled = it->isLocallyDisabled;
      }
      else if (it->mediaType == MediaType_Video)
      {
         ret.videoDirection = reconMediaDir;
         ret.videoMixContribution = it->conferenceMixContribution;
         ret.videoMixId = ((it->conferenceMixId == -1) ? defaultMixId : (unsigned int)it->conferenceMixId);
         ret.videoCaptureDeviceId = (int)it->videoCaptureDeviceId;
         
         switch (it->mediaEncryptionOptions.mediaEncryptionMode)
         {
            case MediaEncryptionMode_SRTP_SDES_Encrypted:
               ret.secureVideoMode = recon::ConversationManager::Srtp;
               break;
            case MediaEncryptionMode_SRTP_DTLS_Encrypted:
               ret.secureVideoMode = recon::ConversationManager::SrtpDtls;
               break;
            default:
               ret.secureVideoMode = recon::ConversationManager::NoSecureMedia;
               break;
         }
         
         ret.secureVideoRequired = it->mediaEncryptionOptions.secureMediaRequired;
         ret.secureVideoCrypto = ReconConversationManagerImpl::convertMediaCryptoSuite(it->mediaCrypto);
         ret.secureVideoDefaultCryptoSuites = ReconConversationManagerImpl::convertMediaCryptoSuites(it->mediaEncryptionOptions.mediaCryptoSuites);
         ret.videoIsLocallyDisabled = it->isLocallyDisabled;
      }
   }
   
   return ret;
}
   
SipConversation::MediaDirection ReconConversationManagerImpl::convertMediaDirection(recon::ConversationManager::MediaDirection md)
{
   switch (md)
   {
   case recon::ConversationManager::MediaDirection_Inactive:
      return SipConversation::MediaDirection_Inactive;
   case recon::ConversationManager::MediaDirection_ReceiveOnly:
      return SipConversation::MediaDirection_ReceiveOnly;
   case recon::ConversationManager::MediaDirection_SendOnly:
      return SipConversation::MediaDirection_SendOnly;
   case recon::ConversationManager::MediaDirection_SendReceive:
      return SipConversation::MediaDirection_SendReceive;
   case recon::ConversationManager::MediaDirection_None:
      FALL_THROUGH;
   default:
      return SipConversation::MediaDirection_None;
   }
}

SipConversation::MediaEncryptionMode ReconConversationManagerImpl::convertMediaEncryptionMode(recon::ConversationManager::SecureMediaMode smm)
{
   switch (smm)
   {
   case recon::ConversationManager::Srtp:
      return MediaEncryptionMode_SRTP_SDES_Encrypted;
   case recon::ConversationManager::SrtpDtls:
      return MediaEncryptionMode_SRTP_DTLS_Encrypted;
    case recon::ConversationManager::NoSecureMedia:
      FALL_THROUGH;
    default:
      return MediaEncryptionMode_Unencrypted;
   }
}

SipConversation::MediaCryptoSuite ReconConversationManagerImpl::convertMediaCryptoSuite(recon::ConversationManager::SecureMediaCryptoSuite crypto)
{
   switch (crypto)
   {
      case recon::ConversationManager::SRTP_AES_CM_128_HMAC_SHA1_32:
         return MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32;
      case recon::ConversationManager::SRTP_AES_CM_128_HMAC_SHA1_80:
         return MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80;
      case recon::ConversationManager::SRTP_AES_CM_256_HMAC_SHA1_32:
         return MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32;
      case recon::ConversationManager::SRTP_AES_CM_256_HMAC_SHA1_80:
         return MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80;
      case recon::ConversationManager::SRTP_AES_CM_192_HMAC_SHA1_32:
         return MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32;
      case recon::ConversationManager::SRTP_AES_CM_192_HMAC_SHA1_80:
         return MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80;
      case recon::ConversationManager::SRTP_AEAD_AES_128_GCM_16:
         return MediaCryptoSuite_AEAD_AES_128_GCM;
      case recon::ConversationManager::SRTP_AEAD_AES_256_GCM_16:
         return MediaCryptoSuite_AEAD_AES_256_GCM;
      default:
         return MediaCryptoSuite_None;
   }
}

recon::ConversationManager::SecureMediaCryptoSuite ReconConversationManagerImpl::convertMediaCryptoSuite(SipConversation::MediaCryptoSuite crypto)
{
   switch (crypto)
   {
      case MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32:
         return recon::ConversationManager::SRTP_AES_CM_128_HMAC_SHA1_32;
      case MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80:
         return recon::ConversationManager::SRTP_AES_CM_128_HMAC_SHA1_80;
      case MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32:
         return recon::ConversationManager::SRTP_AES_CM_256_HMAC_SHA1_32;
      case MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80:
         return recon::ConversationManager::SRTP_AES_CM_256_HMAC_SHA1_80;
      case MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32:
         return recon::ConversationManager::SRTP_AES_CM_192_HMAC_SHA1_32;
      case MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80:
         return recon::ConversationManager::SRTP_AES_CM_192_HMAC_SHA1_80;
      case MediaCryptoSuite_AEAD_AES_128_GCM:
         return recon::ConversationManager::SRTP_AEAD_AES_128_GCM_16;
      case MediaCryptoSuite_AEAD_AES_256_GCM:
         return recon::ConversationManager::SRTP_AEAD_AES_256_GCM_16;
      default:
         return recon::ConversationManager::SRTP_CRYPTO_SUITE_INVALID;
   }
}

std::vector<recon::ConversationManager::SecureMediaCryptoSuite> ReconConversationManagerImpl::convertMediaCryptoSuites(cpc::vector<MediaCryptoSuite> cryptos)
{
   std::vector<recon::ConversationManager::SecureMediaCryptoSuite> cryptoSuites;

   for (cpc::vector<MediaCryptoSuite>::iterator i = cryptos.begin(); i != cryptos.end(); i++)
   {
      recon::ConversationManager::SecureMediaCryptoSuite crypto = convertMediaCryptoSuite(*i);
      if (crypto != recon::ConversationManager::SRTP_CRYPTO_SUITE_INVALID)
      {
         cryptoSuites.push_back(crypto);
      }
   }
   
   return cryptoSuites;
}

cpc::vector<MediaCryptoSuite> ReconConversationManagerImpl::convertMediaCryptoSuites(std::vector<recon::ConversationManager::SecureMediaCryptoSuite> cryptos)
{
   cpc::vector<MediaCryptoSuite> cryptoSuites;

   for (std::vector<recon::ConversationManager::SecureMediaCryptoSuite>::iterator i = cryptos.begin(); i != cryptos.end(); i++)
   {
      MediaCryptoSuite crypto = convertMediaCryptoSuite(*i);
      if (crypto != MediaCryptoSuite_None)
      {
         cryptoSuites.push_back(crypto);
      }
   }
      
   return cryptoSuites;
}
   
cpc::vector<MediaInfo> toMediaInfo(const recon::ConversationManager::MediaAttributes& mediaAttribs)
{
   cpc::vector<MediaInfo> miVector;
   if (mediaAttribs.audioDirection != recon::ConversationManager::MediaDirection_None)
   {
      MediaInfo miAudio;
      miAudio.mediaStreamId = 2000;
      miAudio.mediaDirection = ReconConversationManagerImpl::convertMediaDirection(mediaAttribs.audioDirection);
      miAudio.mediaType = MediaType_Audio;
      miAudio.mediaCrypto = ReconConversationManagerImpl::convertMediaCryptoSuite(mediaAttribs.secureAudioCrypto);
      miAudio.mediaEncryptionOptions.secureMediaRequired = mediaAttribs.secureAudioRequired;
      miAudio.mediaEncryptionOptions.mediaEncryptionMode = ReconConversationManagerImpl::convertMediaEncryptionMode(mediaAttribs.secureAudioMode);
      miAudio.mediaEncryptionOptions.mediaCryptoSuites = ReconConversationManagerImpl::convertMediaCryptoSuites(mediaAttribs.secureAudioDefaultCryptoSuites);
      miVector.push_back(miAudio);
   }
   if (mediaAttribs.videoDirection != recon::ConversationManager::MediaDirection_None)
   {
      MediaInfo miVideo;
      miVideo.mediaStreamId = 2001;
      miVideo.mediaDirection = ReconConversationManagerImpl::convertMediaDirection(mediaAttribs.videoDirection);
      miVideo.mediaType = MediaType_Video;
      miVideo.mediaCrypto = ReconConversationManagerImpl::convertMediaCryptoSuite(mediaAttribs.secureVideoCrypto);
      miVideo.mediaEncryptionOptions.secureMediaRequired = mediaAttribs.secureVideoRequired;
      miVideo.mediaEncryptionOptions.mediaEncryptionMode = ReconConversationManagerImpl::convertMediaEncryptionMode(mediaAttribs.secureVideoMode);
      miVideo.mediaEncryptionOptions.mediaCryptoSuites = ReconConversationManagerImpl::convertMediaCryptoSuites(mediaAttribs.secureVideoDefaultCryptoSuites);
      miVector.push_back(miVideo);
   }
   return miVector;
}

SipConversation::AnswerModeSettings ReconConversationManagerImpl::toSdkAnswerMode(const recon::ConversationManager::AnswerModeAttributes& reconAnswerMode)
{
   SipConversation::AnswerModeSettings sdkAnswerMode;
   switch (reconAnswerMode.mode)
   {
      case recon::ConversationManager::AnswerModeAttributes::AnswerMode_Manual: sdkAnswerMode.mode = SipConversation::AnswerMode_Manual; break;
      case recon::ConversationManager::AnswerModeAttributes::AnswerMode_Auto: sdkAnswerMode.mode = SipConversation::AnswerMode_Auto; break;
      default: sdkAnswerMode.mode = SipConversation::AnswerMode_Disabled; break;
   }
   sdkAnswerMode.privileged = reconAnswerMode.privileged;
   sdkAnswerMode.required = reconAnswerMode.required;
   return sdkAnswerMode;
}

recon::ConversationManager::AnswerModeAttributes ReconConversationManagerImpl::toReconAnswerMode(const SipConversation::AnswerModeSettings& sdkAnswerMode)
{
   recon::ConversationManager::AnswerModeAttributes reconAnswerMode;
   switch (sdkAnswerMode.mode)
   {
      case SipConversation::AnswerMode_Manual: reconAnswerMode.mode = recon::ConversationManager::AnswerModeAttributes::AnswerMode_Manual; break;
      case SipConversation::AnswerMode_Auto: reconAnswerMode.mode = recon::ConversationManager::AnswerModeAttributes::AnswerMode_Auto; break;
      default: reconAnswerMode.mode = recon::ConversationManager::AnswerModeAttributes::AnswerMode_Disabled; break;
   }
   reconAnswerMode.privileged = sdkAnswerMode.privileged;
   reconAnswerMode.required = sdkAnswerMode.required;
   return reconAnswerMode;
}

SipConversation::AnswerModeSettings ReconConversationManagerImpl::extractAnswerMode(const resip::SipMessage& msg)
{
   SipConversation::AnswerModeSettings answerMode;

   bool privileged = false;
   bool autoAnswer = false;
   bool required = false;
   cpc::string header = SipHelpers::getAnswerMode(msg, privileged, autoAnswer, required);
   if (!header.empty())
   {
      if (privileged)
      {
         answerMode.privileged = true;
      }

      if (required)
      {
         answerMode.required = true;
      }

      if (autoAnswer)
      {
         answerMode.mode = SipConversation::AnswerMode_Auto;
      }
      else
      {
         answerMode.mode = SipConversation::AnswerMode_Manual;
      }
   }
   return answerMode;
}

int ReconConversationManagerImpl::updateMediaConnections()
{
   SipCallCreationInfoMap::const_iterator it = mCallCreationInfo.begin();
   std::set<recon::ParticipantHandle> handled;
   for (; it != mCallCreationInfo.end(); ++it)
   {
      if (handled.count(it->second->reconRemoteParticipant) == 0)
      {
         InfoLog(<< "updateMediaConnection (send re-INVITE) for recon remote participant " << it->second->reconRemoteParticipant << ", reconConversation " << it->second->reconConversation);

         bool restartAudioLevelMonitor = (mAudioLevelMonitor != nullptr);
         if (restartAudioLevelMonitor)
         {
            stopMonitoringAudioLevels(it->first);
         }

         updateMediaConnection(it->second->reconRemoteParticipant);

         if (restartAudioLevelMonitor)
         {
            startMonitoringAudioLevels(it->first);
         }
         handled.insert(it->second->reconRemoteParticipant);
      }
   }
   return kSuccess;
}

int ReconConversationManagerImpl::startStarcodeHandovers()
{
   SipAVConversationManagerInterface* conMgr = dynamic_cast<SipAVConversationManagerInterface*>(SipConversationManager::getInterface(mAccount.getPhone()));

   // As the starcode network change handling only supports the network change of one active call,
   // all other calls need to be destroyed as they cannot be pulled.
   cpc::vector<SipConversationHandle> endedCalls;
   SipConversationHandle starcodeCall = 0;
   SipConversationHandle activeCall = conMgr->endCallsNotApplicableForStarcodeNetworkChange(endedCalls);

   bool isStarcodeHandlingRequired = conMgr->isStarcodeHandlingRequired();
   SipCallCreationInfo* ci = getCreationInfo(activeCall);
   if (ci && isStarcodeHandlingRequired)
   {
      InfoLog(<< "ReconConversationManagerImpl::startStarcodeHandovers(): send starcode for active call: " << activeCall << " with recon remote participant " << ci->reconRemoteParticipant << ", reconConversation " << ci->reconConversation);

      bool restartAudioLevelMonitor = (mAudioLevelMonitor != nullptr);
      if (restartAudioLevelMonitor)
      {
         stopMonitoringAudioLevels(activeCall);
      }

      starcodeCall = conMgr->networkChangeStarcodeHandover(activeCall);

      if (restartAudioLevelMonitor)
      {
         startMonitoringAudioLevels(starcodeCall);
      }

      DebugLog(<< "ReconConversationManagerImpl::startStarcodeHandovers(): active-call: " << activeCall << " can be restored using starcode handling");

#ifdef CPCAPI2_AUTO_TEST
      ConversationEndedEventFromStarcodeNetworkChange endedEvent;
      endedEvent.originalConversation = activeCall;
      endedEvent.newConversation = starcodeCall;
      endedEvent.endedConversations = endedCalls;
      mCallImpl.fireCallsEndedEventFromStarcodeNetworkChange(starcodeCall, endedEvent);
#endif
   }

   return kSuccess;
}

bool ReconConversationManagerImpl::hasLocalBindingUpdated(const resip::NameAddr& contact)
{
   // Only compare with existing binding, if it had been populated, i.e. it's a registration refresh rather than a new registration
   bool hasUpdated = false;
   if (mLocalContactBinding.uri().getAor().empty() == false)
   {
      if (contact == mLocalContactBinding)
      {
         if (contact.uri().exists(resip::p_rinstance) && mLocalContactBinding.uri().exists(resip::p_rinstance))
         {
            if (contact.uri().param(resip::p_rinstance) != mLocalContactBinding.uri().param(resip::p_rinstance))
            {
               hasUpdated = true;
            }
         }
      }
      else
      {
         hasUpdated = true;
      }
   }

   DebugLog(<< "ReconConversationManagerImpl::hasLocalBindingUpdated(): current-binding: " << mLocalContactBinding.uri().toString() << " new-binding: " << contact.uri().toString() << " has-updated: " << hasUpdated);
   return hasUpdated;
}

void ReconConversationManagerImpl::updateLocalContact(const resip::NameAddr& contact)
{
   InfoLog(<< "updateLocalContact: " << contact);
   mLocalContactBinding = contact;
   SipCallCreationInfoMap::const_iterator it = mCallCreationInfo.begin();
   for (; it != mCallCreationInfo.end(); ++it)
   {
      if (it->second->reconRemoteParticipant != 0)
      {
         RemoteParticipant* remotePart = dynamic_cast<RemoteParticipant*>(getParticipant(it->second->reconRemoteParticipant));
         if (remotePart && remotePart->isDialogValid())
         {
            if (contact.uri().host().empty())
            {
               // In cases where no registrar is configured, the contact will not be initialized (as no 200 OK responses are received).
               // We can empty out the host and port of the current contact, as it will get updated in the transport layer, allowing us
               // to retain the other parameters.
               DebugLog(<< "updateLocalContact: resetting host and port in local contact " << remotePart->getContact() << " for account-handle " << it->second->account);
               remotePart->getContact().uri().host() = "";
               remotePart->getContact().uri().port() = 0;
            }
            else
            {
               DebugLog(<< "updateLocalContact: updating local contact from " << remotePart->getContact() << " to " << contact << " for account-handle " << it->second->account);
               remotePart->getContact() = contact;
            }
         }
      }
   }
}

void ReconConversationManagerImpl::updateLocalInterfaceOverride(const resip::Tuple* serverIp)
{
   if (!mDefaultConvProfile) return;

   StackLog(<< "ReconConversationManagerImpl::updateLocalInterfaceOverride(): mDefaultConvProfile: " << mDefaultConvProfile);

   if (!mOverrideSourceIpSignalling.empty())
   {
      if ((mAcctSettings.enableNat64Support && DnsUtil::isIpV6Address(mDefaultConvProfile->localInterfaceOverride()) && (serverIp != NULL)) || (!mAcctSettings.enableNat64Support))
      {
         SipCallCreationInfoMap::const_iterator it = mCallCreationInfo.begin();
         for (; it != mCallCreationInfo.end(); ++it)
         {
            if (it->second->reconRemoteParticipant != 0)
            {
               ConversationManager::updateLocalInterfaceOverride(it->second->reconRemoteParticipant, mDefaultConvProfile->localInterfaceOverride());
            }
         }
      }
   }
}

void ReconConversationManagerImpl::setOfferTelephoneEvent( const bool& enable )
{
   if( mDefaultConvProfile )
      mDefaultConvProfile->offerTelephoneEvent() = enable;
}

SipCallCreationInfo* ReconConversationManagerImpl::getCreationInfo(const SipConversationHandle& h) const
{
   SipCallCreationInfoMap::const_iterator it = mCallCreationInfo.find(h);
   if (it != mCallCreationInfo.end())
   {
      return it->second;
   }
   return NULL;
}

SipCallCreationInfo* ReconConversationManagerImpl::getCreationInfoForMediaStream(int webrtc_channel, SipConversationHandle& h) const
{
   SipCallCreationInfoMap::const_iterator it = mCallCreationInfo.begin();
   for (; it != mCallCreationInfo.end(); ++it)
   {
      std::vector<std::weak_ptr<webrtc_recon::RtpStreamImpl> > mediaStreams = it->second->rtpStreams;
      std::vector<std::weak_ptr<webrtc_recon::RtpStreamImpl> >::const_iterator itStreams = mediaStreams.begin();
      for (; itStreams != mediaStreams.end(); ++itStreams)
      {
         if (std::shared_ptr<webrtc_recon::RtpStreamImpl> spStream = itStreams->lock())
         {
            if (spStream->channel() == webrtc_channel)
            {
               h = it->first;
               return it->second;
            }
         }
      }
   }
   return NULL;
}

void ReconConversationManagerImpl::addCreationInfo(const SipConversationHandle& h, SipCallCreationInfo* ci)
{
   mCallCreationInfo[h] = ci;
}

void ReconConversationManagerImpl::removeCreationInfo(const SipConversationHandle& h)
{
   SipCallCreationInfoMap::iterator it = mCallCreationInfo.find(h);
   if (it != mCallCreationInfo.end())
   {
      //std::cout << "REMOVING from map item that has rp " << it->second->reconRemoteParticipant << " and orig " << it->second->reconOriginalRemoteParticipant << std::endl;
      mAccount.getAccountInterface()->postToSdkThread(resip::resip_bind(&ReconConversationManagerImpl::removeCreationInfoImpl, this, it->second));
      mCallCreationInfo.erase(it);
   }
}

void ReconConversationManagerImpl::removeCreationInfoImpl(SipCallCreationInfo* ci)
{
   delete ci;
}

SipConversationHandle ReconConversationManagerImpl::findSipConversationHandle(recon::ConversationHandle reconConvHandle) const
{
   SipCallCreationInfoMap::const_iterator it = mCallCreationInfo.begin();
   for (; it != mCallCreationInfo.end(); ++it)
   {
      if (it->second->reconConversation == reconConvHandle)
      {
         return it->first;
      }
   }
   return 0xffffffff;
}

SipConversationHandle ReconConversationManagerImpl::findSipConversationHandleByParticipantHandle(recon::ParticipantHandle reconPartHandle, bool includeOrig) const
{
   //std::cout << "looking for " << reconPartHandle << std::endl;
   SipCallCreationInfoMap::const_iterator it = mCallCreationInfo.begin();
   for (; it != mCallCreationInfo.end(); ++it)
   {
      //std::cout << "map has rp " << it->second->reconRemoteParticipant << " and orig " << it->second->reconOriginalRemoteParticipant << std::endl;
      if (it->second->reconRemoteParticipant == reconPartHandle || 
          (includeOrig && (it->second->reconOriginalRemoteParticipant == reconPartHandle)))
      {
         return it->first;
      }
   }
   return 0xffffffff;
}

void ReconConversationManagerImpl::destroyRelatedConversations(SipConversationHandle h)
{
   SipCallCreationInfoMap callCreationInfoMapCopy = mCallCreationInfo;
   SipCallCreationInfoMap::const_iterator it = callCreationInfoMapCopy.begin();
   for (; it != callCreationInfoMapCopy.end(); it++)
   {
      if (it->second != NULL)
      {
         if (it->first == h || it->second->originalConversation == h)
         {
            // fire an onConversationStatisticsUpdated event now so that the SipConversationState code
            // can be used to obtain "final" values for the stats after the call is over;
            // note this has to be done before destroyParticipant(..) below so that the RTP stream data counters
            // are still valid (they get reset when destroyParticipant(..) calls stopRtpSend(..))
            refreshConversationStatisticsImpl(h, true, true, true, true);

            recon::ParticipantHandle reconPart = it->second->reconRemoteParticipant;
            recon::ConversationHandle reconConv = it->second->reconConversation;

            destroyParticipant(reconPart); // !WARNING! this deletes the SipCallCreationInfo pointed to by it->second
            destroyConversation(reconConv);
            removeParticipant(reconConv, mLocalParticipant);
            finalizeConversation(reconPart, resip::InviteSessionHandler::LocalCancel, NULL);
         }
      }
   }
}

recon::ConversationProfileHandle ReconConversationManagerImpl::getDefaultConversationProfile() const
{
   if (mDefaultConvProfile)
   {
      return mDefaultConvProfile->handle();
   }
   assert(0);
   return 0;   
}

recon::ParticipantHandle ReconConversationManagerImpl::getLocalParticipant() const
{
   return mLocalParticipant;
}

void ReconConversationManagerImpl::createConversationProfiles(resip::DialogUsageManager* dum)
{
   assert(dum != 0);

   // --- AUDIO/VIDEO profile ---
   resip::SharedPtr<recon::ConversationProfile> convProfile(new recon::ConversationProfile(dum->getMasterUserProfile()));
   updateConversationProfile(convProfile);

   // keep this ConversationProfile around, since we'll build the ConversationProfiles that get
   // used on a per-call basis from this one
   mDefaultConvProfile = convProfile;
   if (mDefaultConvProfileHandle != 0)
   {
      destroyConversationProfile(mDefaultConvProfileHandle);
   }
   mDefaultConvProfileHandle = addConversationProfile(convProfile, true);
}

void ReconConversationManagerImpl::updateDefaultConversationProfile(const resip::Tuple* serverIp)
{
   if (mDefaultConvProfile)
   {
      updateConversationProfile(mDefaultConvProfile, serverIp);
   }
}

void ReconConversationManagerImpl::resetLocalInterfaceOverride()
{
   if (mDefaultConvProfile)
   {
      StackLog(<< "ReconConversationManagerImpl::resetLocalInterfaceOverride(): resetting default profile");
      mDefaultConvProfile->localInterfaceOverride() = "";
   }
   
   SipCallCreationInfoMap::const_iterator it = mCallCreationInfo.begin();
   std::set<recon::ParticipantHandle> handled;
   for (; it != mCallCreationInfo.end(); ++it)
   {
      if (it->second->reconRemoteParticipant != 0)
      {
         StackLog(<< "ReconConversationManagerImpl::resetLocalInterfaceOverride(): updating local interface override on RemoteParticipant");
         ConversationManager::updateLocalInterfaceOverride(it->second->reconRemoteParticipant, "");
      }
   }
}

const resip::Data& ReconConversationManagerImpl::overrideSourceIpSignalling() const
{
   return mOverrideSourceIpSignalling;
}

resip::Data& ReconConversationManagerImpl::overrideSourceIpSignalling()
{
   return mOverrideSourceIpSignalling;
}

void ReconConversationManagerImpl::sendInfo( recon::ParticipantHandle partHandle, resip::Contents& content )
{
   recon::RemoteParticipant* remotePart = dynamic_cast<recon::RemoteParticipant*>( getParticipant( partHandle ));
   if( remotePart == NULL )
      return;

   resip::InviteSessionHandle ish( remotePart->getInviteSessionHandle() );
   if( ish.isValid() )
      ish->info( content ); 
}

resip::Data callQualityString(ConversationCallQuality cq)
{
   switch (cq)
   {
   case ConversationCallQuality_Fair:
      return "Fair";
   case ConversationCallQuality_Good:
      return "Good";
   case ConversationCallQuality_Poor:
      return "Poor";
   case ConversationCallQuality_Unknown:
      return "Unknown";
   }
   return "Invalid Value";
}

int ReconConversationManagerImpl::refreshConversationStatisticsImpl(SipConversationHandle conversation, bool includeNetworkStats, bool includeJitterStats, bool includeRemoteStats, bool internalOnly)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL && ci->rtpStreams.size() > 0)
   {
      webrtc_recon::MediaStackImpl* ms = mCallImpl.media_stack();
      if (ms == NULL)
      {
         return -1;
      }

      std::shared_ptr<webrtc_recon::CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<webrtc_recon::CodecFactoryImpl>(getMediaStack()->codecFactory());

      ConversationStatistics conversationStatistics;
      JitterBufferStatistics jitterBufferStatistics;
      uint32_t rtcp_sr_timestamp( 0 );

      std::vector<std::weak_ptr<webrtc_recon::RtpStreamImpl> >::iterator itRtpStreams = ci->rtpStreams.begin();

      conversationStatistics.callQuality = ConversationCallQuality_Unknown;

      for(; itRtpStreams != ci->rtpStreams.end(); itRtpStreams++)
      {
         if(std::shared_ptr<webrtc_recon::RtpStreamImpl> rtpStream = itRtpStreams->lock())
         {
            if(rtpStream->mediaType() == recon::MediaStack::MediaType_Audio)
            {
               webrtc::CodecInst audioCodec;

               if (rtpStream->channelSet() &&
                   ms->audioCodec()->GetSendCodec(rtpStream->channel(), audioCodec) == 0)
               {
                  webrtc::CallStatistics callStatistics;
                  callStatistics.fractionLost = 0;
                  callStatistics.cumulativeLost = 0;
                  callStatistics.extendedMax = 0;
                  callStatistics.jitterSamples = 0;
                  callStatistics.rttMs = 0;
                  callStatistics.bytesSent = 0;
                  callStatistics.packetsSent = 0;
                  callStatistics.bytesReceived = 0;
                  callStatistics.packetsReceived = 0;
                  callStatistics.capture_start_ntp_time_ms_ = 0;
                  if (includeNetworkStats)
                  {
                     ms->voe_rtp_rtcp()->GetRTCPStatistics(rtpStream->channel(), callStatistics);
                  }

                  AudioStatistics audioStatistics;
                  clearStatistics(&audioStatistics);
                  audioStatistics.callStartTimeNTP = rtpStream->getCallStartTime();
                  audioStatistics.endpoint.ipAddress = rtpStream->getLocalTuple().presentationFormat().c_str();
                  audioStatistics.endpoint.port = rtpStream->getLocalTuple().getPort();
                  audioStatistics.encoder.pltype = audioCodec.pltype;
                  strncpy(audioStatistics.encoder.plname, audioCodec.plname, 32);
                  audioStatistics.encoder.plfreq = audioCodec.plfreq;
                  audioStatistics.encoder.pacsize = audioCodec.pacsize;
                  audioStatistics.encoder.channels = audioCodec.channels;
                  audioStatistics.encoder.rate = audioCodec.rate;
                  
                  if (codecFactory)
                  {
                     if (std::shared_ptr<webrtc_recon::CpsiCodec> cpsiCodec = codecFactory->getAudioCodec(audioCodec.plname, audioCodec.plfreq))
                     {
                        audioStatistics.encoder.displayName = cpsiCodec->display_name.c_str();
                     }
                  }

                  webrtc::CodecInst remoteAudioCodec;
                  if (rtpStream->channelSet() &&
                      ms->audioCodec()->GetRecCodec(rtpStream->channel(), remoteAudioCodec) == 0)
                  {
                     audioStatistics.decoder.pltype = remoteAudioCodec.pltype;
                     strncpy(audioStatistics.decoder.plname, remoteAudioCodec.plname, 32);
                     audioStatistics.decoder.plfreq = remoteAudioCodec.plfreq;
                     audioStatistics.decoder.pacsize = remoteAudioCodec.pacsize;
                     audioStatistics.decoder.channels = remoteAudioCodec.channels;
                     audioStatistics.decoder.rate = remoteAudioCodec.rate;
            
                     if (codecFactory)
                     {
                        if (std::shared_ptr<webrtc_recon::CpsiCodec> cpsiCodec = codecFactory->getAudioCodec(remoteAudioCodec.plname, remoteAudioCodec.plfreq))
                        {
                           audioStatistics.decoder.displayName = cpsiCodec->display_name.c_str();
                        }
                     }
                  }

                  audioStatistics.streamStatistics.fractionLost = callStatistics.fractionLost;
                  audioStatistics.streamStatistics.cumulativeLost = callStatistics.cumulativeLost;
                  audioStatistics.streamStatistics.extendedMax = callStatistics.extendedMax;
                  audioStatistics.streamStatistics.jitterSamples = callStatistics.jitterSamples;
                  audioStatistics.streamStatistics.rttMs = callStatistics.rttMs;
                  audioStatistics.streamDataCounters.bytesSent = callStatistics.bytesSent;
                  audioStatistics.streamDataCounters.packetsSent = callStatistics.packetsSent;
                  audioStatistics.streamDataCounters.bytesReceived = callStatistics.bytesReceived;
                  audioStatistics.streamDataCounters.packetsReceived = callStatistics.packetsReceived;
                  if (includeNetworkStats || includeJitterStats)
                  {
                     ms->voe_rtp_rtcp()->GetRTPStatistics(rtpStream->channel(),
                        audioStatistics.averageJitterMs,
                        audioStatistics.maxJitterMs,
                        audioStatistics.discardedPackets);
                  }

                  if (includeNetworkStats)
                  {
                     webrtc::RTCPVoIPMetric localVoipMetric;
                     if (rtpStream->getLocalRTCPVoIPMetric(&localVoipMetric) == 0)
                     {
                        audioStatistics.XRvoipMetrics.burstDensity = localVoipMetric.burstDensity;
                        audioStatistics.XRvoipMetrics.burstDuration = localVoipMetric.burstDuration;
                        audioStatistics.XRvoipMetrics.discardRate = localVoipMetric.discardRate;
                        audioStatistics.XRvoipMetrics.endSystemDelay = localVoipMetric.endSystemDelay;
                        audioStatistics.XRvoipMetrics.extRfactor = localVoipMetric.extRfactor;
                        audioStatistics.XRvoipMetrics.gapDensity = localVoipMetric.gapDensity;
                        audioStatistics.XRvoipMetrics.gapDuration = localVoipMetric.gapDuration;
                        audioStatistics.XRvoipMetrics.Gmin = localVoipMetric.Gmin;
                        audioStatistics.XRvoipMetrics.JBabsMax = localVoipMetric.JBabsMax;
                        audioStatistics.XRvoipMetrics.JBmax = localVoipMetric.JBmax;
                        audioStatistics.XRvoipMetrics.JBnominal = localVoipMetric.JBnominal;
                        audioStatistics.XRvoipMetrics.lossRate = localVoipMetric.lossRate;
                        audioStatistics.XRvoipMetrics.MOSCQ = localVoipMetric.MOSCQ;
                        audioStatistics.XRvoipMetrics.MOSLQ = localVoipMetric.MOSLQ;
                        audioStatistics.XRvoipMetrics.noiseLevel = localVoipMetric.noiseLevel;
                        audioStatistics.XRvoipMetrics.RERL = localVoipMetric.RERL;
                        audioStatistics.XRvoipMetrics.Rfactor = localVoipMetric.Rfactor;
                        audioStatistics.XRvoipMetrics.roundTripDelay = localVoipMetric.roundTripDelay;
                        audioStatistics.XRvoipMetrics.RXconfig = localVoipMetric.RXconfig;
                        audioStatistics.XRvoipMetrics.signalLevel = localVoipMetric.signalLevel;
                     }
                  }

                  if (includeNetworkStats)
                  {
                     webrtc::RTCPXRStatsSummary localStatsSummary;
                     if (rtpStream->getLocalRTCPXRStatsSummary(&localStatsSummary) == 0)
                     {
                        audioStatistics.XRstatisticsSummary.begin_seq = localStatsSummary.begin_seq;
                        audioStatistics.XRstatisticsSummary.dev_jitter = localStatsSummary.dev_jitter;
                        audioStatistics.XRstatisticsSummary.dev_ttl_or_hl = localStatsSummary.dev_ttl_or_hl;
                        audioStatistics.XRstatisticsSummary.dup_packets = localStatsSummary.dup_packets;
                        audioStatistics.XRstatisticsSummary.end_seq = localStatsSummary.end_seq;
                        audioStatistics.XRstatisticsSummary.lost_packets = localStatsSummary.lost_packets;
                        audioStatistics.XRstatisticsSummary.max_jitter = localStatsSummary.max_jitter;
                        audioStatistics.XRstatisticsSummary.max_ttl_or_hl = localStatsSummary.max_ttl_or_hl;
                        audioStatistics.XRstatisticsSummary.mean_jitter = localStatsSummary.mean_jitter;
                        audioStatistics.XRstatisticsSummary.mean_ttl_or_hl = localStatsSummary.mean_ttl_or_hl;
                        audioStatistics.XRstatisticsSummary.min_jitter = localStatsSummary.min_jitter;
                        audioStatistics.XRstatisticsSummary.min_ttl_or_hl = localStatsSummary.min_ttl_or_hl;
                     }
                  }

                  audioStatistics.intervalCallQualityReport = cpc::string(rtpStream->getIntervalCallQualityReport().c_str(), rtpStream->getIntervalCallQualityReport().size());

                  conversationStatistics.audioChannels.push_back(audioStatistics);

                  RemoteAudioStatistics remoteAudioStatistics;
                  clearStatistics(&remoteAudioStatistics);
                  remoteAudioStatistics.endpoint.ipAddress = rtpStream->getRemoteTuple().presentationFormat().c_str();
                  remoteAudioStatistics.endpoint.port = rtpStream->getRemoteTuple().getPort();

                  if (includeRemoteStats)
                  {
                     // This method must be used, because retrieving the value from the
                     // various report(s) results in a timestamp which is not updated while
                     // the call is on hold
                     rtcp_sr_timestamp = (uint32_t)ms->voe_rtp_rtcp()->LastReceived( rtpStream->channel() );
                     remoteAudioStatistics.lastRtcpReceived = rtcp_sr_timestamp;

                     std::vector<webrtc::ReportBlock> report_blocks;
                     ms->voe_rtp_rtcp()->GetRemoteRTCPReportBlocks(rtpStream->channel(), &report_blocks);
                     if (report_blocks.size() > 0)
                     {
                        remoteAudioStatistics.lastSenderReportReceived = rtcp_sr_timestamp; // ?

                        webrtc::ReportBlock firstReportBlock = report_blocks.front();
                        remoteAudioStatistics.sender_SSRC = firstReportBlock.sender_SSRC;
                        remoteAudioStatistics.source_SSRC = firstReportBlock.source_SSRC;
                        remoteAudioStatistics.streamStatistics.fractionLost = firstReportBlock.fraction_lost;
                        remoteAudioStatistics.streamStatistics.cumulativeLost = firstReportBlock.cumulative_num_packets_lost;
                        remoteAudioStatistics.streamStatistics.extendedMax = firstReportBlock.extended_highest_sequence_number;
                        remoteAudioStatistics.streamStatistics.jitterSamples = firstReportBlock.interarrival_jitter;
                        remoteAudioStatistics.streamStatistics.rttMs = firstReportBlock.rttMs;
                     }
                     webrtc::RTCPVoIPMetric xrVoipMetric;
                     memset(&xrVoipMetric, 0, sizeof(webrtc::RTCPVoIPMetric));
                     if (ms->voe_rtp_rtcp()->GetRTCPXRVoipMetric(rtpStream->channel(), &xrVoipMetric) == 0)
                     {
                        remoteAudioStatistics.lastRtcpXrReceived = rtcp_sr_timestamp; // ?

                        remoteAudioStatistics.XRvoipMetrics.burstDensity = xrVoipMetric.burstDensity;
                        remoteAudioStatistics.XRvoipMetrics.burstDuration = xrVoipMetric.burstDuration;
                        remoteAudioStatistics.XRvoipMetrics.discardRate = xrVoipMetric.discardRate;
                        remoteAudioStatistics.XRvoipMetrics.endSystemDelay = xrVoipMetric.endSystemDelay;
                        remoteAudioStatistics.XRvoipMetrics.extRfactor = xrVoipMetric.extRfactor;
                        remoteAudioStatistics.XRvoipMetrics.gapDensity = xrVoipMetric.gapDensity;
                        remoteAudioStatistics.XRvoipMetrics.gapDuration = xrVoipMetric.gapDuration;
                        remoteAudioStatistics.XRvoipMetrics.Gmin = xrVoipMetric.Gmin;
                        remoteAudioStatistics.XRvoipMetrics.JBabsMax = xrVoipMetric.JBabsMax;
                        remoteAudioStatistics.XRvoipMetrics.JBmax = xrVoipMetric.JBmax;
                        remoteAudioStatistics.XRvoipMetrics.JBnominal = xrVoipMetric.JBnominal;
                        remoteAudioStatistics.XRvoipMetrics.lossRate = xrVoipMetric.lossRate;
                        remoteAudioStatistics.XRvoipMetrics.MOSCQ = xrVoipMetric.MOSCQ;
                        remoteAudioStatistics.XRvoipMetrics.MOSLQ = xrVoipMetric.MOSLQ;
                        remoteAudioStatistics.XRvoipMetrics.noiseLevel = xrVoipMetric.noiseLevel;
                        remoteAudioStatistics.XRvoipMetrics.RERL = xrVoipMetric.RERL;
                        remoteAudioStatistics.XRvoipMetrics.Rfactor = xrVoipMetric.Rfactor;
                        remoteAudioStatistics.XRvoipMetrics.roundTripDelay = xrVoipMetric.roundTripDelay;
                        remoteAudioStatistics.XRvoipMetrics.RXconfig = xrVoipMetric.RXconfig;
                        remoteAudioStatistics.XRvoipMetrics.signalLevel = xrVoipMetric.signalLevel;
                     }
                     webrtc::RTCPXRStatsSummary xrStatsSummary;
                     if (ms->voe_rtp_rtcp()->GetRTCPXRStatsSummary(rtpStream->channel(), &xrStatsSummary) == 0)
                     {
                        remoteAudioStatistics.lastRtcpXrReceived = rtcp_sr_timestamp; // ?

                        remoteAudioStatistics.XRstatisticsSummary.begin_seq = xrStatsSummary.begin_seq;
                        remoteAudioStatistics.XRstatisticsSummary.dev_jitter = xrStatsSummary.dev_jitter;
                        remoteAudioStatistics.XRstatisticsSummary.dev_ttl_or_hl = xrStatsSummary.dev_ttl_or_hl;
                        remoteAudioStatistics.XRstatisticsSummary.dup_packets = xrStatsSummary.dup_packets;
                        remoteAudioStatistics.XRstatisticsSummary.end_seq = xrStatsSummary.end_seq;
                        remoteAudioStatistics.XRstatisticsSummary.lost_packets = xrStatsSummary.lost_packets;
                        remoteAudioStatistics.XRstatisticsSummary.max_jitter = xrStatsSummary.max_jitter;
                        remoteAudioStatistics.XRstatisticsSummary.max_ttl_or_hl = xrStatsSummary.max_ttl_or_hl;
                        remoteAudioStatistics.XRstatisticsSummary.mean_jitter = xrStatsSummary.mean_jitter;
                        remoteAudioStatistics.XRstatisticsSummary.mean_ttl_or_hl = xrStatsSummary.mean_ttl_or_hl;
                        remoteAudioStatistics.XRstatisticsSummary.min_jitter = xrStatsSummary.min_jitter;
                        remoteAudioStatistics.XRstatisticsSummary.min_ttl_or_hl = xrStatsSummary.min_ttl_or_hl;
                     }
                  }

                  conversationStatistics.remoteAudioChannels.push_back(remoteAudioStatistics);

                  AudioJitterBufferStatistics audioJitterStats;
                  memset(&audioJitterStats, 0, sizeof(audioJitterStats));
                  if (includeJitterStats)
                  {
                     webrtc::NetworkStatistics networkStats;
                     memset(&networkStats, 0, sizeof(networkStats));
                     if (ms->voe_neteq_stats()->GetNetworkStatistics(rtpStream->channel(), networkStats) == 0)
                     {
                        audioJitterStats.addedSamples = networkStats.addedSamples;
                        audioJitterStats.clockDriftPPM = networkStats.clockDriftPPM;
                        audioJitterStats.currentAccelerateRate = networkStats.currentAccelerateRate;
                        audioJitterStats.currentBufferSizeMs = networkStats.currentBufferSize;
                        audioJitterStats.currentDiscardRate = networkStats.currentDiscardRate;
                        audioJitterStats.currentEffectivePacketLossRate = networkStats.currentPacketLossRate;
                        audioJitterStats.currentSynthesizedAudioInsertRate = networkStats.currentExpandRate;
                        audioJitterStats.currentSynthesizedAudioPreemptiveInsertRate = networkStats.currentPreemptiveRate;
                        audioJitterStats.jitterBurstsFound = networkStats.jitterPeaksFound;
                        audioJitterStats.maxWaitingTimeMs = networkStats.maxWaitingTimeMs;
                        audioJitterStats.meanWaitingTimeMs = networkStats.meanWaitingTimeMs;
                        audioJitterStats.medianWaitingTimeMs = networkStats.medianWaitingTimeMs;
                        audioJitterStats.minWaitingTimeMs = networkStats.minWaitingTimeMs;
                        audioJitterStats.preferredBufferSizeMs = networkStats.preferredBufferSize;
                     }
                     jitterBufferStatistics.audioChannels.push_back(audioJitterStats);
                  }
               }
            }
            else if(rtpStream->mediaType() == recon::MediaStack::MediaType_Video)
            {
               webrtc::VideoCodec videoCodec;
               if (rtpStream->channelSet() &&
                   ms->videoCodec()->GetSendCodec(rtpStream->channel(), videoCodec) == 0)
               {
                  VideoStatistics videoStatistics;
                  clearStatistics(&videoStatistics);
                  videoStatistics.endpoint.ipAddress = rtpStream->getLocalTuple().presentationFormat().c_str();
                  videoStatistics.endpoint.port = rtpStream->getLocalTuple().getPort();
                  strncpy(videoStatistics.encoder.plName, videoCodec.plName, 32);
                  videoStatistics.encoder.plType = videoCodec.plType;
                  videoStatistics.encoder.width = videoCodec.width;
                  videoStatistics.encoder.height = videoCodec.height;
                  videoStatistics.encoder.startBitrate = videoCodec.startBitrate;
                  videoStatistics.encoder.minBitrate = videoCodec.minBitrate;
                  videoStatistics.encoder.maxBitrate = videoCodec.maxBitrate;
                  ms->videoCodec()->GetCodecTargetBitrate(rtpStream->channel(), &videoStatistics.currentTargetBitrate);
                  videoStatistics.encoder.maxFramerate = videoCodec.maxFramerate;
                  videoStatistics.encoder.hadwareAccelerated = false;
                  if (videoCodec.extra_options)
                  {
                    const webrtc_recon::HardwareAccelerationSettings acc = videoCodec.extra_options->Get<webrtc_recon::HardwareAccelerationSettings>();
                    videoStatistics.encoder.hadwareAccelerated = acc.hardware_encode_supported && acc.hardware_encoding_acceleration_enabled;
                  }
                  
                  if (codecFactory)
                  {
                     if (std::shared_ptr<webrtc_recon::CpsiCodec> cpsiCodec = codecFactory->getVideoCodec(videoCodec.plName))
                     {
                        videoStatistics.encoder.displayName = cpsiCodec->display_name.c_str();
                     }
                  }

                  webrtc::VideoCodec remoteVideoCodec;
                  if (ms->videoCodec()->GetReceiveCodec(rtpStream->channel(), remoteVideoCodec) == 0)
                  {
                     strncpy(videoStatistics.decoder.plName, remoteVideoCodec.plName, 32);
                     videoStatistics.decoder.plType = remoteVideoCodec.plType;
                     videoStatistics.decoder.width = remoteVideoCodec.width;
                     videoStatistics.decoder.height = remoteVideoCodec.height;
                     videoStatistics.decoder.startBitrate = remoteVideoCodec.startBitrate;
                     videoStatistics.decoder.minBitrate = remoteVideoCodec.minBitrate;
                     videoStatistics.decoder.maxBitrate = remoteVideoCodec.maxBitrate;
                     videoStatistics.decoder.maxFramerate = remoteVideoCodec.maxFramerate;
                     videoStatistics.decoder.hadwareAccelerated = false;
                     if (remoteVideoCodec.extra_options)
                     {
                       const webrtc_recon::HardwareAccelerationSettings acc = remoteVideoCodec.extra_options->Get<webrtc_recon::HardwareAccelerationSettings>();
                       videoStatistics.decoder.hadwareAccelerated = acc.hardware_decode_supported && acc.hardware_decoding_acceleration_enabled;
                     }
                     
                     if (codecFactory)
                     {
                        if (std::shared_ptr<webrtc_recon::CpsiCodec> cpsiCodec = codecFactory->getVideoCodec(remoteVideoCodec.plName))
                        {
                           videoStatistics.decoder.displayName = cpsiCodec->display_name.c_str();
                        }
                     }
                  }
                  else
                  {
#ifdef CPCAPI2_AUTO_TEST
                     DebugLog(<< "ms->videoCodec()->GetReceiveCodec failed for endpoint " << videoStatistics.endpoint.ipAddress << ":" << videoStatistics.endpoint.port);
#endif
                  }
                  
                  int realWidth = rtpStream->getVideoWidth();
                  int realHeight = rtpStream->getVideoHeight();
                  if (realWidth != 0 )
                  {
                     videoStatistics.decoder.width = realWidth;
                  }else
		            {
                     videoStatistics.decoder.width = 0;
                  }
                  if (realHeight != 0)
                  {
                     videoStatistics.decoder.height = realHeight;
                  }else
                  {
                     videoStatistics.decoder.height = 0;
                  }
                  int realFps = rtpStream->getVideoFramerate();
                  if (realFps != 0)
                  {
                     videoStatistics.decoder.maxFramerate = realFps;
                  }

                  if (includeNetworkStats)
                  {
                     size_t bytesSent, bytesReceived;
                     
                     int ret = ms->vie_rtp_rtcp()->GetRTPStatistics(rtpStream->channel(),
                        bytesSent,
                        videoStatistics.streamDataCounters.packetsSent,
                        bytesReceived,
                        videoStatistics.streamDataCounters.packetsReceived);
                     if (ret == -1)
                     {
                        ErrLog(<< "Failed calling GetRTPStatistics; last error: " << ms->vie_base()->LastError());
                     }
                     
                     videoStatistics.streamDataCounters.bytesSent = (unsigned int)bytesSent;
                     videoStatistics.streamDataCounters.bytesReceived = (unsigned int)bytesReceived;
                     
                     ms->vie_rtp_rtcp()->GetBandwidthUsage(rtpStream->channel(), videoStatistics.totalBitrateSent,
                        videoStatistics.videoBitrateSent,
                        videoStatistics.fecBitrateSent,
                        videoStatistics.nackBitrateSent);
                     ms->vie_rtp_rtcp()->GetReceivedRTCPStatistics(rtpStream->channel(), 
                        videoStatistics.streamStatistics.fractionLost,
                        videoStatistics.streamStatistics.cumulativeLost,
                        videoStatistics.streamStatistics.extendedMax,
                        videoStatistics.streamStatistics.jitterSamples,
                        videoStatistics.streamStatistics.rttMs);
                     videoStatistics.discardedPackets = ms->videoCodec()->GetDiscardedPackets(rtpStream->channel());
                  }

                  conversationStatistics.videoChannels.push_back(videoStatistics);

                  RemoteVideoStatistics remoteVideoStatistics;
                  clearStatistics(&remoteVideoStatistics);
                  remoteVideoStatistics.endpoint.ipAddress = rtpStream->getRemoteTuple().presentationFormat().c_str();
                  remoteVideoStatistics.endpoint.port = rtpStream->getRemoteTuple().getPort();
                  if (includeRemoteStats)
                  {
                     remoteVideoStatistics.lastRtcpReceived = ms->vie_rtp_rtcp()->LastReceived( rtpStream->channel() ); // ?

                     if (ms->vie_rtp_rtcp()->GetSentRTCPStatistics(rtpStream->channel(),
                           remoteVideoStatistics.streamStatistics.fractionLost,
                           remoteVideoStatistics.streamStatistics.cumulativeLost,
                           remoteVideoStatistics.streamStatistics.extendedMax,
                           remoteVideoStatistics.streamStatistics.jitterSamples,
                           remoteVideoStatistics.streamStatistics.rttMs) != 0)
                     {
                        DebugLog(<< "Video GetSentRTCPStatistics not available: " << ms->vie_base()->LastError());
                     }
                     else
                     {
                        remoteVideoStatistics.lastSenderReportReceived = remoteVideoStatistics.lastRtcpReceived; // ?
                     }

                     if (remoteVideoStatistics.streamStatistics.cumulativeLost > 10000000)
                     {
                        WarningLog(<< "remoteVideoStatistics.streamStatistics.cumulativeLost is very large; bug?");
                     }
                  }
                  conversationStatistics.remoteVideoChannels.push_back(remoteVideoStatistics);

                  VideoJitterBufferStatistics videoJitterStats;
                  memset(&videoJitterStats, 0, sizeof(videoJitterStats));
                  if (includeJitterStats)
                  {
                     int receive_side_delay = 0;
                     if (ms->videoCodec()->GetReceiveSideDelay(rtpStream->channel(), &receive_side_delay) == 0)
                     {
                        videoJitterStats.currentBufferSizeMs = receive_side_delay;
                     }

                     videoJitterStats.currentDiscardRate = 0;
                     unsigned int discarded_packets = ms->videoCodec()->GetDiscardedPackets(rtpStream->channel());
                     if (discarded_packets > 0 && videoStatistics.streamDataCounters.packetsReceived > 0)
                     {
                        videoJitterStats.currentDiscardRate = (unsigned short)((uint64_t)((uint64_t)discarded_packets << 28) / (uint64_t)((uint64_t)videoStatistics.streamDataCounters.packetsReceived << 14));
                     }

                     ms->videoCodec()->GetReceiveCodecStastistics(rtpStream->channel(), videoJitterStats.numDecodedKeyFrames, videoJitterStats.numDecodedDeltaFrames);

                     jitterBufferStatistics.videoChannels.push_back(videoJitterStats);
                  }
               }
            }
         }
      }

      if (conversationStatistics.audioChannels.size() > 0 || conversationStatistics.videoChannels.size() > 0)
      {
         std::string audioJitterBufferSizeMs = jitterBufferStatistics.audioChannels.empty() ? "n/a" : std::to_string(jitterBufferStatistics.audioChannels[0].currentBufferSizeMs);
      
         if (conversationStatistics.audioChannels.size() > 0 &&
            conversationStatistics.remoteAudioChannels.size() > 0)
         {
            unsigned int packetsReceived( conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived );

            if( ci->previousPacketsReceived == packetsReceived &&
                ci->previousAudioRTCPSenderReportTimestamp == rtcp_sr_timestamp )
            {
               ci->numIntervalsWithoutUpdate += 1;
            }
            else
            {
               ci->previousPacketsReceived = packetsReceived;
               ci->previousAudioRTCPSenderReportTimestamp = rtcp_sr_timestamp;
               ci->numIntervalsWithoutUpdate = 0;
            }

            conversationStatistics.callQuality = calculateCallQuality(
               conversation,
               conversationStatistics.audioChannels[0].streamStatistics.fractionLost,
               conversationStatistics.remoteAudioChannels[0].streamStatistics.fractionLost,
               packetsReceived,
               conversationStatistics.audioChannels[0].streamDataCounters.packetsSent,
               conversationStatistics.audioChannels[0].streamStatistics.rttMs,
               ci->numIntervalsWithoutUpdate );

            // was MosEstimator::calculateNetworkMos -- our very brief attempt at replacing VQmon
            conversationStatistics.networkMos = -1;

            const int localFractionLost = conversationStatistics.audioChannels[0].streamStatistics.fractionLost;
            const int rttMs = conversationStatistics.audioChannels[0].streamStatistics.rttMs;
            const int packetsSent = conversationStatistics.audioChannels[0].streamDataCounters.packetsSent;
            
            std::stringstream videoStats;
            if (conversationStatistics.videoChannels.size() > 0)
            {
               videoStats << std::endl << "=========== Brief video stats: " <<
                             "incoming fraction lost: (" << conversationStatistics.videoChannels[0].streamStatistics.fractionLost << "/256)" <<
                             " packets received: " << conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived <<
                             " packets sent: " << conversationStatistics.videoChannels[0].streamDataCounters.packetsSent;
            }
            
            InfoLog(<< "=========== Brief audio stats:" <<
               " incoming fraction lost: (" << localFractionLost << "/256), rttMs: " << rttMs <<
               ", currentJbSizeMs: " << audioJitterBufferSizeMs << ", packets received: " << packetsReceived <<
               ", packets sent: " << packetsSent <<
               ", numIntvWithoutUpdate: " << ci->numIntervalsWithoutUpdate <<
               ", call quality: " << callQualityString(conversationStatistics.callQuality) <<
               ", numNonZeroAudio: " << ci->numNonZeroAudioInputLevelsSampled <<
               videoStats.str());
               
         }
         else
         {
            conversationStatistics.callQuality = ConversationCallQuality_Unknown;
            conversationStatistics.networkMos = -1;
         }

         ConversationStatisticsUpdatedEvent args;
         args.conversationStatistics = conversationStatistics;
         args.jitterBufferStatistics = jitterBufferStatistics;
         if (internalOnly)
         {
            mCallImpl.fireEvent(cpcEvent(SipConversationHandlerInternal, onConversationStatisticsUpdated), conversation, args);
         }
         else
         {
            mCallImpl.fireEvent(cpcEvent(SipConversationHandler, onConversationStatisticsUpdated), conversation, args);
         }

         resip::Log::Level logLevel = internalOnly ? resip::Log::Info: resip::Log::Stack;
         int64_t currentNTPtime = webrtc::Clock::GetRealTimeClock()->CurrentNtpInMilliseconds();

         GenericLog(RESIPROCATE_SUBSYSTEM, logLevel, << "=========== CONVERSATION STATISTICS =============");
         if (conversationStatistics.audioChannels.size() > 0)
         {
            GenericLog(RESIPROCATE_SUBSYSTEM, logLevel, << "---------- LOCAL ----------" << std::endl
                    << "callStartTimeNTP:    " << conversationStatistics.audioChannels[0].callStartTimeNTP << std::endl
                    << "currentTimeNTP:      " << currentNTPtime << std::endl
                    << "statsIntervalMs:     " << (currentNTPtime - conversationStatistics.audioChannels[0].callStartTimeNTP) << std::endl
                    << "cumulativeLost:      " << conversationStatistics.audioChannels[0].streamStatistics.cumulativeLost << std::endl
                    << "fractionLost:        " << conversationStatistics.audioChannels[0].streamStatistics.fractionLost << std::endl
                    << "discarded:           " << conversationStatistics.audioChannels[0].discardedPackets << std::endl
                    // jitterSamples doesn't seem to accurately reflect actual jitter; in order check in StreamStatisticianImpl::UpdateCounters
                    // is questionable given RFC 3550 sec 6.4.1 specifies order of arrival should be used, not necessarily in sequence)
                    << "jitterSamples:       " << conversationStatistics.audioChannels[0].streamStatistics.jitterSamples << std::endl
                    << "currentJbSizeMs      " << audioJitterBufferSizeMs << std::endl
                    << "rttMs:               " << conversationStatistics.audioChannels[0].streamStatistics.rttMs << std::endl
                    << "packetsReceived:     " << conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived << std::endl
                    << "packetsSent:         " << conversationStatistics.audioChannels[0].streamDataCounters.packetsSent << std::endl
                    << "bytesReceived:       " << conversationStatistics.audioChannels[0].streamDataCounters.bytesReceived << std::endl
                    << "bytesSent:           " << conversationStatistics.audioChannels[0].streamDataCounters.bytesSent << std::endl
                    << "decoder.plname:      " << conversationStatistics.audioChannels[0].decoder.plname << std::endl
                    << "encoder.plname:      " << conversationStatistics.audioChannels[0].encoder.plname << std::endl
                    << "numNonZeroAudio:     " << (ci != NULL ? ci->numNonZeroAudioInputLevelsSampled : -2) << std::endl
                    << "localEndpoint:       " << conversationStatistics.audioChannels[0].endpoint.ipAddress << ":" << conversationStatistics.audioChannels[0].endpoint.port);
         }
         if (conversationStatistics.videoChannels.size() > 0)
         {
            GenericLog(RESIPROCATE_SUBSYSTEM, logLevel, << "---------- LOCAL (video) ----------" << std::endl
               << "cumulativeLost:       " << conversationStatistics.videoChannels[0].streamStatistics.cumulativeLost << std::endl
               << "fractionLost:         " << conversationStatistics.videoChannels[0].streamStatistics.fractionLost << std::endl
               << "discarded:            " << conversationStatistics.videoChannels[0].discardedPackets << std::endl
               << "jitterSamples:        " << conversationStatistics.videoChannels[0].streamStatistics.jitterSamples << std::endl
               << "rttMs:                " << conversationStatistics.videoChannels[0].streamStatistics.rttMs << std::endl
               << "packetsReceived:      " << conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived << std::endl
               << "packetsSent:          " << conversationStatistics.videoChannels[0].streamDataCounters.packetsSent << std::endl
               << "bytesReceived:        " << conversationStatistics.videoChannels[0].streamDataCounters.bytesReceived << std::endl
               << "bytesSent:            " << conversationStatistics.videoChannels[0].streamDataCounters.bytesSent << std::endl
               << "decoder.plname:       " << conversationStatistics.videoChannels[0].decoder.plName << std::endl
               << "encoder.plname:       " << conversationStatistics.videoChannels[0].encoder.plName << std::endl
               << "currentTargetBitrate: " << conversationStatistics.videoChannels[0].currentTargetBitrate << std::endl
               << "localEndpoint:        " << conversationStatistics.videoChannels[0].endpoint.ipAddress << ":" << conversationStatistics.videoChannels[0].endpoint.port);
         }
         if (conversationStatistics.remoteAudioChannels.size() > 0)
         {
            GenericLog(RESIPROCATE_SUBSYSTEM, logLevel, << "---------- REMOTE ----------" << std::endl
                    << "cumulativeLost:           " << conversationStatistics.remoteAudioChannels[0].streamStatistics.cumulativeLost << std::endl
                    << "fractionLost:             " << conversationStatistics.remoteAudioChannels[0].streamStatistics.fractionLost << std::endl
                    << "jitterSamples:            " << conversationStatistics.remoteAudioChannels[0].streamStatistics.jitterSamples << std::endl
                    << "rttMs:                    " << conversationStatistics.remoteAudioChannels[0].streamStatistics.rttMs << std::endl
                    << "remoteEndpoint:           " << conversationStatistics.remoteAudioChannels[0].endpoint.ipAddress << ":" << conversationStatistics.remoteAudioChannels[0].endpoint.port << std::endl
                    << "lastRtcpreceived:         " << conversationStatistics.remoteAudioChannels[0].lastRtcpReceived << std::endl
                    << "lastRtcpXrReceived:       " << conversationStatistics.remoteAudioChannels[0].lastRtcpXrReceived << std::endl
                    << "lastSenderReportReceived: " << conversationStatistics.remoteAudioChannels[0].lastSenderReportReceived << std::endl );
         }
         if (conversationStatistics.remoteVideoChannels.size() > 0)
         {
            GenericLog(RESIPROCATE_SUBSYSTEM, logLevel, << "---------- REMOTE (video) ----------" << std::endl
               << "cumulativeLost:           " << conversationStatistics.remoteVideoChannels[0].streamStatistics.cumulativeLost << std::endl
               << "fractionLost:             " << conversationStatistics.remoteVideoChannels[0].streamStatistics.fractionLost << std::endl
               << "jitterSamples:            " << conversationStatistics.remoteVideoChannels[0].streamStatistics.jitterSamples << std::endl
               << "rttMs:                    " << conversationStatistics.remoteVideoChannels[0].streamStatistics.rttMs << std::endl
               << "remoteEndpoint:           " << conversationStatistics.remoteVideoChannels[0].endpoint.ipAddress << ":" << conversationStatistics.remoteVideoChannels[0].endpoint.port << std::endl
               << "lastRtcpReceived:         " << conversationStatistics.remoteVideoChannels[0].lastRtcpReceived << std::endl
               << "lastSenderReportReceived: " << conversationStatistics.remoteVideoChannels[0].lastSenderReportReceived );
         }
      }
   }
   else
   {
      // silently fail, was probably called with an invalid (or recently invalidated) handle
   }
   return kSuccess;
}

ConversationCallQuality 
ReconConversationManagerImpl::calculateCallQuality(SipConversationHandle h, unsigned short localFractionLost, unsigned short remoteFractionLost, unsigned int totalReceivedPackets, unsigned int totalSentPackets, int64_t latency, unsigned int numIntervalsWithoutUpdate )
{
   SipCallCreationInfo* ci = getCreationInfo(h);
   if (ci == NULL)
   {
      return ConversationCallQuality_Unknown;
   }

   // Refresh is usually around every 2 seconds. RTCP updates usually come around
   // every 5 seconds. So make the number of intervals > 3 to be safe.
   if( numIntervalsWithoutUpdate > 3 )
   {
      return ConversationCallQuality_Poor;
   }

   int receivedPercentLoss = 0;
   if (localFractionLost > 0)
   {
      receivedPercentLoss = localFractionLost * 100 / 255;
   }

   int sentPercentLoss = 0;
   if (remoteFractionLost > 0)
   {
      sentPercentLoss = remoteFractionLost * 100 / 255;
   }

   if (totalReceivedPackets == 0 && totalSentPackets == 0)
   {
      return ConversationCallQuality_Unknown;
   }
   else if (receivedPercentLoss > 15 || sentPercentLoss > 15 || latency > 750)
   {
      return ConversationCallQuality_Poor;
   }
   else if (receivedPercentLoss < 5 && sentPercentLoss < 5 && latency < 500)
   {
      return ConversationCallQuality_Good;
   }
   else
   {
      return ConversationCallQuality_Fair;
   }

}

void ReconConversationManagerImpl::updateConversationProfile(resip::SharedPtr<recon::ConversationProfile>& convProfile, const resip::Tuple* serverIp) const
{
   const SipConversationSettings& conversationSettings = getConvSettings();
   const SipAccountSettings& accountSettings = mAcctSettings;

   DebugLog(<< "ReconConversationManagerImpl::updateConversationProfile()");
   resip::Data srcIp = resip::Data::Empty;
   
   bool nat64Present = false;
   bool nat64DiscoveryFailure = false;
   if (mAcctSettings.enableNat64Support && (mAcctSettings.ipVersion != IpVersion_V4))
   {
      nat64Present = resip::IpSynthTools::detectNat64(mDum->getSipStack().getDnsStub(), nat64DiscoveryFailure);
      if (nat64DiscoveryFailure)
       {
         // query timeout
         nat64Present = false;
      }
   }

   if (!mOverrideSourceIpTransport.empty())
   {
      DebugLog(<< "srcIp updated from: " << srcIp << " as the override source ip transport is initialized: " << mOverrideSourceIpTransport);
      srcIp = mOverrideSourceIpTransport;
   }
   else if (!mAcctSettings.sourceAddress.empty())
   {
      DebugLog(<< "srcIp updated from: " << srcIp << " as the account settings source address is initialized: " << mAcctSettings.sourceAddress);
      srcIp = mAcctSettings.sourceAddress;
   }
   else
   {
      if ((serverIp != NULL) && DnsUtil::isIpAddress(serverIp->presentationFormat()) && !serverIp->isAnyInterface())
      {
         IpHelpers::getPreferredLocalIpAddress(*serverIp, srcIp);
         DebugLog(<< "getPreferredLocalIpAddress - " << srcIp << " : " << *serverIp);
      }
      else
      {
         // This will grab the IP address passed to onRegistrationSuccess the last time it was invoked;
         // a real mis-nomer, since this is also used in the useRegistrar==false case.
         resip::Tuple serverIpFromLastReg;
         if (mAccount.getSipServerIpFromLastReg(serverIpFromLastReg))
         {
            IpHelpers::getPreferredLocalIpAddress(serverIpFromLastReg, srcIp);
            DebugLog(<< "getPreferredLocalIpAddress (using serverIpFromLastReg) - " << srcIp << " : " << serverIpFromLastReg);
         }
      }
      if (srcIp == resip::Data::Empty || srcIp == "0.0.0.0")
      {
         std::list<std::pair<resip::Data, resip::Data> > v4ifs = resip::DnsUtil::getInterfacesEx(resip::Data::Empty, true, false, false, false);
         // OBELISK-5938: if NAT64 is detected, there is a chance that only IPv6 connectivity will work.
         // As such, we'll first try looking for an IPv6 network interface to use.
         // Note that this is not a robust check; e.g. does not detect if interface is up but does not have
         // proper internet connectivity.
         // This path would typically only be used for incoming calls via SIP push Stretto tunnel.
         if (v4ifs.size() > 0 && !nat64Present)
         {
            resip::Tuple googDns("*******", 53, resip::UDP);
            IpHelpers::getPreferredLocalIpAddress(googDns, srcIp);
            DebugLog(<< "getPreferredLocalIpAddress (using *******) - " << srcIp);
         }
         else
         {
            std::list<std::pair<resip::Data, resip::Data> > v6ifs = resip::DnsUtil::getInterfacesEx(resip::Data::Empty, false, true, false, false);
            if (v6ifs.size() > 0)
            {
               resip::Tuple googDns("2001:4860:4860::8888", 53, resip::UDP);
               IpHelpers::getPreferredLocalIpAddress(googDns, srcIp);
               DebugLog(<< "getPreferredLocalIpAddress (using 2001:4860:4860::8888) - " << srcIp);
               
               if (srcIp == resip::Data::Empty || srcIp == "0.0.0.0")
               {
                  // OBELISK-6070: we as much as possible don't want to fall back to picking a random IPv4 network interface; 
                  // i.e. we only want to invoke determineSrcIp() as a last resort -- and instead here look for an IPv4
                  // interface that the OS indicates might have internet access.
                  // Similiar to above with the OBELISK-5938 this check is by no means robust -- and does not guarantee
                  // actual internet connectivity.
 
                  resip::Tuple googDns("*******", 53, resip::UDP);
                  IpHelpers::getPreferredLocalIpAddress(googDns, srcIp);
                  WarningLog(<< "NAT64 detected but route lookup to IPv6 Google DNS server failed; falling back to IPv4 interface lookup via getPreferredLocalIpAddress (using *******) - " << srcIp);
               }
            }
         }
      }
      if (srcIp == resip::Data::Empty || srcIp == "0.0.0.0")
      {
         srcIp = determineSrcIp();
         DebugLog(<< "srcIp updated to preferred local ip address as it was not initialized: " << srcIp);
      }
      if (srcIp == resip::Data::Empty || srcIp == "0.0.0.0")
      {
         srcIp = "127.0.0.1";
         DebugLog(<< "srcIp updated to localhost as it is currently not initialized: " << srcIp);
      }
      if (!mOverrideSourceIpSignalling.empty())
      {
         if ((mAcctSettings.enableNat64Support && DnsUtil::isIpV6Address(srcIp)) ||
            (!mAcctSettings.enableNat64Support))
         {
            convProfile->localInterfaceOverride() = srcIp;
            DebugLog(<< "srcIp updated from: " << srcIp << " to: " << mOverrideSourceIpSignalling << " as the override source ip signalling address is initialized");
            srcIp = mOverrideSourceIpSignalling;
         }
      }
      else if (mAccount.isUsingStrettoTunnel())
      {
         // todo in future: revise to not special case Stretto
         // tunnel/push here. this is currently required because
         // SipAccountImpl::getOverrideSourceIpForNAT64() returns
         // an empty value when Stretto tunnel is in use.
         if (nat64Present)
         {
            convProfile->localInterfaceOverride() = srcIp;
            // this assumes we are talking to an IPv4 SIP server; as of
            // July 2021, Stretto push server does not support IPv6 SIP
            // servers, so this should be an okay assumption.
            srcIp = mAccount.getOverrideSourceIpForNAT64Unconditional().c_str();
         }
      }
   }

   DebugLog(<< "Found srcIp: " << srcIp);

   convProfile->audioSupported() = true; // this is STATIC -- if we have a mic, and speakers, then it should be true
   convProfile->videoSupported() = true; // this is STATIC -- if we have a monitor (CRT, LCD, LED), then it should be true
   resip::Data sessName(conversationSettings.sessionName.c_str());
   if (sessName == resip::Data::Empty || sessName == "")
   {
      // must be at least the 'space' character according to the RFC
      sessName = " ";
   }
   convProfile->sessionName() = sessName;
   convProfile->encodeAttribsForStaticPLs() = conversationSettings.includeAttribsForStaticPLs;
   buildSessionCapabilities(convProfile, true, true, srcIp, convProfile->sessionCaps(), convProfile->sessionName());

   // Set NAT traversal parameters, if a mode is selected
   convProfile->natTraversalMode() = ConversationProfile::NoNatTraversal;
   if (conversationSettings.natTraversalMode != NatTraversalMode_None)
   {
      // Set NAT traversal server and port, if a source is selected
      bool natTraversalServerSpecified = false;
      if (conversationSettings.natTraversalServerSource != NatTraversalServerSource_None)
      {
         std::string natTraversalServerAddr;
         int natTraversalServerPort = 0;

         // Check that DNS SRV lookup is enabled
         if (conversationSettings.natTraversalServerSource == NatTraversalServerSource_SRV)
         {
            // Get the address of the NAT traversal server and 
            // port through a DNS SRV lookup using the configured domain

            StunDnsSrvRecord dnsSrvRecord = mAccount.getStunDnsSrvRecord();
            if (!dnsSrvRecord.target.empty())
            {
               natTraversalServerAddr = dnsSrvRecord.target.c_str();
               natTraversalServerPort = dnsSrvRecord.port;

               DebugLog(<< "NatTraversalServerSource_SRV discovery success");
            }
            else
            {
               DebugLog(<< "NatTraversalServerSource_SRV discovery no results");
            }
         }

         // Check that no NAT traversal server address has not been found yet
         if (natTraversalServerAddr.empty() && !conversationSettings.natTraversalServer.empty())
         {
            DebugLog(<< "retreiving STUN server from configuration");

            // Retrieve the STUN server (and port) from the configuration
            natTraversalServerAddr = conversationSettings.natTraversalServer.c_str();
            natTraversalServerPort = 3478;
            std::string::size_type last_sq_bracket = natTraversalServerAddr.rfind("]");
            std::string::size_type colon_pos = natTraversalServerAddr.rfind(":");
            if (colon_pos != std::string::npos)
            {
               if (last_sq_bracket == std::string::npos || (last_sq_bracket != std::string::npos && last_sq_bracket < colon_pos))
               {
                  std::string stunPortStr = natTraversalServerAddr.substr(colon_pos+1);
                  if (stunPortStr.size() > 0)
                  {
                     natTraversalServerPort = resip::Data(stunPortStr.c_str()).convertInt();
                  }
                  natTraversalServerAddr = natTraversalServerAddr.substr(0,colon_pos);
               }
            }
            if (last_sq_bracket != std::string::npos)
            {
               // we have to trim away the [ and ] otherwise DnsUtil::isIpAddress(..) does not like
               natTraversalServerAddr = natTraversalServerAddr.substr(1,last_sq_bracket-1);
            }
         }

         DebugLog(<< "natTraversalServerAddr: " << natTraversalServerAddr << ", natTraversalServerPort: " << natTraversalServerPort);

         // Set the NAT traversal parameters if host and port are valid
         if (!natTraversalServerAddr.empty() && natTraversalServerPort > 0)
         {
            convProfile->natTraversalServerHostname() = natTraversalServerAddr.c_str();
            convProfile->natTraversalServerPort() = natTraversalServerPort;
            convProfile->natTraversalServerSupportsTurn() = (conversationSettings.natTraversalServerType == NatTraversalServerType_StunAndTurn ||
               conversationSettings.natTraversalServerType == NatTraversalServerType_TurnOnly);

            //if (natTraversalSettings.m_strUsername.GetLength() > 0)
            //{
            //   convProfile->stunUsername() = natTraversalSettings.m_strUsername;
            //}

            //if (natTraversalSettings.m_strPassword.GetLength() > 0)
            //{
            //   convProfile->stunPassword() = natTraversalSettings.m_strPassword;
            //}

            natTraversalServerSpecified = true;
         }
      }

      DebugLog(<< "ReconConversationManagerImpl::updateConversationProfile(): NAT Traversal Mode: " << conversationSettings.natTraversalMode);

      // Set the NAT traversal mode
      switch (conversationSettings.natTraversalMode)
      {
      case NatTraversalMode_STUN:
         convProfile->natTraversalMode() = ConversationProfile::StunBindDiscovery;
         break;
      case NatTraversalMode_TURN:
         convProfile->natTraversalMode() = ConversationProfile::TurnUdpAllocation;
         break;
      case NatTraversalMode_ICE:
         convProfile->natTraversalMode() = ConversationProfile::Ice;
         break;
      case NatTraversalMode_Auto:
         // Enable ICE only if a NAT traversal server has been specified
         convProfile->natTraversalMode() = natTraversalServerSpecified ? 
            ConversationProfile::Ice : ConversationProfile::NoNatTraversal;
         break;
      default:
         assert(0);
         break;
      }
   }

   convProfile->useRfc2543Hold() = (conversationSettings.holdMode == HoldMode_RFC2543);

   // Not sure if required, the convProfile should already have this from DUM?
   convProfile->setDefaultFrom(mDum->getMasterProfile()->getDefaultFrom());

   //convProfile->signalingQOS() = account->GetAbstractPhone()->GetSettings()->GetString("system", "qos:signaling", "");
   //convProfile->audioDataQOS() = account->GetAbstractPhone()->GetSettings()->GetString("system", "qos:audio", "");
   //convProfile->videoDataQOS() = account->GetAbstractPhone()->GetSettings()->GetString("system", "qos:video", "");

   convProfile->allowAutoAnswer() = (conversationSettings.answerMode.allowAuto || conversationSettings.answerMode.allowManual);
   convProfile->allowPriorityAutoAnswer() = (conversationSettings.answerMode.allowAuto || conversationSettings.answerMode.allowManual) && conversationSettings.answerMode.allowPrivileged;
   convProfile->allowManualMode() = conversationSettings.answerMode.allowManual;
   convProfile->allowAutoMode() = conversationSettings.answerMode.allowAuto;
   convProfile->challengeAutoAnswerRequests() = conversationSettings.answerMode.challenge;

   convProfile->answerModeEnabled() = ((conversationSettings.answerMode.mode != SipConversation::AnswerMode_Disabled) ? true : false);
   convProfile->answerModeManual() = ((conversationSettings.answerMode.mode == SipConversation::AnswerMode_Manual) ? true : false);
   convProfile->answerModePrivileged() = conversationSettings.answerMode.privileged;
   convProfile->answerModeRequired() = conversationSettings.answerMode.required;

   //ReadBandwidthModifiers(convProfile);

   convProfile->stunUsername() = conversationSettings.turnUsername;
   convProfile->stunPassword() = conversationSettings.turnPassword;

   convProfile->includePPreferredIdentity() = conversationSettings.includePPreferredIdentity;
   convProfile->includePAssertedIdentity() = conversationSettings.includePAssertedIdentity;

   // Outbound might get disabled if the registrar doesn't support it
   convProfile->clientOutboundEnabled() = mDum->getMasterProfile()->clientOutboundEnabled();
   convProfile->setInstanceId(mDum->getMasterProfile()->getInstanceId());

   convProfile->nackPliSupported() = mNackPliSupported;
   convProfile->tmmbrSupported() = mTmmbrSupported;
   convProfile->dtlsSupported() = mDtlsSupported;
   convProfile->tlsVersion() = SipAccountImpl::getSSLType(mAcctSettings.sslVersion);
   convProfile->tlsCiphers() = BaseSecurity::CipherList(Data(mAcctSettings.cipherSuite.c_str()));
}

resip::Data ReconConversationManagerImpl::determineSrcIp() const
{
   resip::Data srcIp;
   if (srcIp == resip::Data::Empty)
      srcIp = CPCAPI2::IpHelpers::getPreferredLocalIpAddress();

   return srcIp;
}

///////////////////////////////////////////////////////////////////////
// Conversation Manager Functions /////////////////////////////////////

void 
ReconConversationManagerImpl::alertParticipant(recon::ParticipantHandle partHandle, bool earlyFlag)
{
   ConversationManager::alertParticipant(partHandle, earlyFlag);

   ConversationStateChangedEvent args;
   args.conversationState = ConversationState_LocalRinging;
   SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(partHandle);
   SipCallCreationInfo* ci = getCreationInfo(convHandle);
   if (ci != NULL)
   {
      ci->currentConversationState = ConversationState_LocalRinging;
   }
   AnswerModeSettings answerModeDefault;
   args.answerMode = (ci ? ci->answerMode : answerModeDefault);
   mCallImpl.fireConvStateChangedEvent(convHandle, args);
}

///////////////////////////////////////////////////////////////////////
// Conversation Manager Handlers //////////////////////////////////////

void
ReconConversationManagerImpl::onMediaStreamCreated(recon::ParticipantHandle partHandle, std::shared_ptr<recon::RtpStream> rtpStream)
{
   SipConversationHandle sipConvHandle = findSipConversationHandleByParticipantHandle(partHandle);
   if (sipConvHandle == 0xFFFFFFFF)
   {
      WarningLog(<< "Could not find SipConversationHandle for recon ParticipantHandle " << partHandle);
      return;
   }

   SipCallCreationInfo* ci = getCreationInfo(sipConvHandle);
   if (ci)
   {
      std::shared_ptr<webrtc_recon::RtpStreamImpl> rtpStreamImpl = std::dynamic_pointer_cast<webrtc_recon::RtpStreamImpl>(rtpStream);
      rtpStreamImpl->setSessionId(ci->callId);
      rtpStreamImpl->setErrorCallback(this, (int)sipConvHandle);
      rtpStreamImpl->setKeyFrameRequestCallback(this, (int)partHandle);
      if (rtpStreamImpl->mediaType() == recon::MediaStack::MediaType_Video &&
          ci->incomingVideoRenderSurface != NULL)
      {
         std::shared_ptr<webrtc_recon::MixerImpl> mixerImpl = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(rtpStreamImpl->getMediaStack()->mixer());
         mixerImpl->removeVideoRenderer(rtpStreamImpl->channel(), ci->incomingVideoRenderSurface);
         mixerImpl->addVideoRenderer(rtpStreamImpl->channel(), ci->incomingVideoRenderSurface, ci->incomingVideoRenderSurfaceType);
      }
      ci->rtpStreams.push_back(rtpStreamImpl);

      // refresh the base stats here so that information like current codec
      // is available
      refreshConversationStatisticsImpl(sipConvHandle, false, false, false, true);
   }
   else
   {
      WarningLog(<< "Missing SipCallCreationInfo for SipConversationHandle " << sipConvHandle);
   }
}

CPCAPI2::SipConversation::MediaDirection toCPCAPIMediaDirection(recon::ConversationManager::MediaDirection reconMD)
{
   switch (reconMD)
   {
   case recon::ConversationManager::MediaDirection_SendReceive:
      return MediaDirection_SendReceive;
   case recon::ConversationManager::MediaDirection_SendOnly:
      return MediaDirection_SendOnly;
   case recon::ConversationManager::MediaDirection_ReceiveOnly:
      return MediaDirection_ReceiveOnly;
   case recon::ConversationManager::MediaDirection_Inactive:
      return MediaDirection_Inactive;
   default:
      break;
   }
   return MediaDirection_Inactive;
}

MediaEncryptionMode toCPCAPIMediaEncryptionMode(recon::ConversationManager::SecureMediaMode m)
{
   switch (m)
   {
   case recon::ConversationManager::Srtp:
      return MediaEncryptionMode_SRTP_SDES_Encrypted;
   case recon::ConversationManager::SrtpDtls:
      return MediaEncryptionMode_SRTP_DTLS_Encrypted;
   default:
      break;
   }
   return MediaEncryptionMode_Unencrypted;
}

void ReconConversationManagerImpl::onIncomingParticipant(
   recon::ParticipantHandle partHandle,
   const resip::SipMessage& msg,
   bool autoAnswer,
   bool isCodecsMismatched,
   const MediaSpecificMismatchInfo& mediaMismatchedInfo,
   const ConversationManager::MediaAttributes& requestedMedia)
{
   SipConversationHandle h = findSipConversationHandleByParticipantHandle(partHandle);
   if (h == 0xFFFFFFFF)
   {
      WarningLog(<< "Could not find SipConversationHandle for recon ParticipantHandle " << partHandle);
      return;
   }

   SipCallCreationInfo* ci = getCreationInfo(h);
   if (ci)
   {
      NewConversationEvent args;
      args.conversationState = ConversationState_RemoteOriginated;
      args.account = mAccount.getHandle();
      args.conversationType = ConversationType_Incoming;
      args.conversationToJoin = 0;
      args.conversationToReplace = 0;
      args.relatedConversation = 0;
      args.isCodecsMismatched = isCodecsMismatched;
      args.isAudioCodecsMismatched = mediaMismatchedInfo.isAudioCodecsMismatched;
      args.isVideoCodecsMismatched = mediaMismatchedInfo.isVideocodecsMismatched;
      NameAddr localNameAddr = msg.header(h_To);
      NameAddr remoteNameAddr;
      if (mAccount.getSettings().preferPAssertedIdentity && msg.exists(h_PAssertedIdentities) && !msg.header(h_PAssertedIdentities).empty())
      {
         NameAddrs remoteNameAddrs = msg.header(h_PAssertedIdentities);
         remoteNameAddr = remoteNameAddrs.front();
      }
      else
      {
         remoteNameAddr = msg.header(h_From);
      }
      args.localAddress = Data::from(localNameAddr.uri()).c_str();
      args.localDisplayName =  Data::from(localNameAddr.displayName()).c_str();
      args.remoteAddress = CharEncodingHelper::unEscape(remoteNameAddr.uri()).c_str();
      args.remoteDisplayName = Data::from(remoteNameAddr.displayName()).c_str();
      args.autoAnswer = autoAnswer;
      args.alertInfoHeader = SipHelpers::getFirstAlertInfoOrCallInfo(msg);
      
      resip::ExtensionHeader xh_ResourcePriority("Resource-Priority");
      if (msg.exists(xh_ResourcePriority))
      {
         const resip::StringCategories& rhdrs = msg.header(xh_ResourcePriority);
         for (const resip::StringCategory& rhdr : rhdrs)
         {
            args.resourcePriority.push_back(rhdr.value().c_str());
         }
      }

      SipHelpers::populateHistoryInfos(msg, args.historyInfo);
      SipHelpers::populatePCalledPartyId(msg, args.pCalledPartyIdAddress, args.pCalledPartyIdDisplayname);
      SipHelpers::populateSessionId(msg, args.sessionId);
      SipHelpers::populateReferredBy(msg, args.referredByAddress, args.referredByDisplayname);
      SipHelpers::populateDiversion(msg, args.diversionAddress, args.diversionDisplayname, args.diversionReason);

      std::ostringstream m;
      msg.encode(m);
      args.sipMessage = m.str().c_str();

      // Update from addr in creation info for incoming call
      ci->fromAddress = Data::from(remoteNameAddr.uri()).c_str();

      ci->answerMode = extractAnswerMode(msg);
      args.answerMode = ci->answerMode;

      // check for SDP
      if (msg.exists(h_ContentType) && msg.getContents())
      {
         if (requestedMedia.audioDirection != ConversationManager::MediaDirection_None)
         {
            MediaInfo mi;
            mi.mediaType = MediaType_Audio;
            mi.mediaDirection = toCPCAPIMediaDirection(requestedMedia.audioDirection);
            mi.mediaEncryptionOptions.mediaEncryptionMode = toCPCAPIMediaEncryptionMode(requestedMedia.secureAudioMode);
            mi.mediaEncryptionOptions.secureMediaRequired = requestedMedia.secureAudioRequired;
            mi.mediaEncryptionOptions.mediaCryptoSuites = ReconConversationManagerImpl::convertMediaCryptoSuites(requestedMedia.secureAudioDefaultCryptoSuites);

            args.remoteMediaInfo.push_back(mi);
         }
         if (requestedMedia.videoDirection != ConversationManager::MediaDirection_None)
         {
            MediaInfo mi = MediaInfo(MediaType_Video, toCPCAPIMediaDirection(requestedMedia.videoDirection));
            mi.mediaEncryptionOptions.mediaEncryptionMode = toCPCAPIMediaEncryptionMode(requestedMedia.secureVideoMode);
            mi.mediaEncryptionOptions.secureMediaRequired = requestedMedia.secureVideoRequired;
            mi.mediaEncryptionOptions.mediaCryptoSuites = ReconConversationManagerImpl::convertMediaCryptoSuites(requestedMedia.secureVideoDefaultCryptoSuites);
            args.remoteMediaInfo.push_back(mi);
         }
         ci->remoteMediaInfo = args.remoteMediaInfo;

         resip::SdpContents* sdpContents = dynamic_cast<resip::SdpContents*>(msg.getContents());
         if (sdpContents != NULL)
         {
            args.sessionDescription.sdpString = sdpContents->getBodyData().c_str();
            args.sessionDescription.sdpLen = sdpContents->getBodyData().size();
            args.sessionDescription.sdpType = CPCAPI2::SipConversation::SessionDescription::SessionDescriptionType_Offer;
         }
      }
      else
      {
         InfoLog(<< "onIncomingParticipant: Empty INVITE - no SDP Offer");
      }

      mCallImpl.fireNewConvEvent(h, args);
      ci->lastRemoteAddr = { args.remoteAddress, args.remoteDisplayName };
      mCallImpl.countNewCall();
   }
   else
   {
      WarningLog(<< "Missing SipCallCreationInfo for SipConversationHandle " << h);
   }
}

void 
ReconConversationManagerImpl::onIncomingJoinRequest(ParticipantHandle newPartHandle, ParticipantHandle existingPartHandle, const resip::SipMessage& msg, bool autoAnswer, bool isCodecsMismatched, const MediaSpecificMismatchInfo& mediaMismatchedInfo, const ConversationManager::MediaAttributes& requestedMedia)
{
   /*
   SUA_TRACES_DEBUG("onIncomingJoinRequest: newPartHandle=" << newPartHandle << "; existingPartHandle=" << existingPartHandle << "; From: " << msg.header(h_From));
   
   bool bInHasSdpOffer = false;
   bool bIsMediaSecure = true;
   ICall::EConnectivityMode eAudioMode = ICall::None;
   ICall::EConnectivityMode eVideoMode = ICall::None;

   getMediaParams(msg, bInHasSdpOffer, bIsMediaSecure, eAudioMode, eVideoMode);

   mIncomingCallSink->OnIncomingCall(
      newPartHandle,
      bInHasSdpOffer,
      eAudioMode,
      eVideoMode,
      bIsMediaSecure,
      existingPartHandle,
      0,
      false,
      std::shared_ptr<resip::SipMessage>(XS_NEW resip::SipMessage(msg))
   );
   */
}

void 
ReconConversationManagerImpl::onIncomingTransferRequest(ParticipantHandle newPartHandle, ParticipantHandle existingPartHandle, const resip::SipMessage& msg, bool autoAnswer, bool isCodecsMismatched, const MediaSpecificMismatchInfo& mediaMismatchedInfo, const ConversationManager::MediaAttributes& requestedMedia)
{
   SipConversationHandle h = findSipConversationHandleByParticipantHandle(newPartHandle);
   SipCallCreationInfo* ci = getCreationInfo(h);
   if (!ci)
   {
      WarningLog(<< "Missing SipCallCreationInfo for SipConversationHandle " << h);
      return;
   }

   ci->conversationToReplace = findSipConversationHandleByParticipantHandle(existingPartHandle);
   ci->fromAddress = Data::from(msg.header(h_From).uri()).c_str();

   SipCallCreationInfo* ciToReplace = getCreationInfo(ci->conversationToReplace);
   
   if (ciToReplace)
   {
      ci->callKitMode = ciToReplace->callKitMode;
   }

   if (ci->callKitMode)
   {
      webrtc::AudioDeviceModule* adm = mCallImpl.media_stack()->voe_base()->audio_device();
      adm->ActivatePlayAndRecordMode(true);
   }

   NewConversationEvent args;
   args.conversationState = ConversationState_RemoteOriginated;
   args.account = mAccount.getHandle();
   args.conversationType = ConversationType_IncomingTransferRequest;
   args.conversationToReplace = ci->conversationToReplace;
   args.conversationToJoin = 0;
   args.relatedConversation = 0;
   args.remoteAddress = CharEncodingHelper::unEscape(msg.header(h_From).uri()).c_str();
   args.remoteDisplayName = Data::from(msg.header(h_From).displayName()).c_str();
   args.isCodecsMismatched = isCodecsMismatched;
   args.isAudioCodecsMismatched = mediaMismatchedInfo.isAudioCodecsMismatched;
   args.isVideoCodecsMismatched = mediaMismatchedInfo.isVideocodecsMismatched;
   args.answerMode = ci->answerMode;

   // check for SDP
   if (msg.exists(h_ContentType) && msg.getContents())
   {
      if (requestedMedia.audioDirection != ConversationManager::MediaDirection_None)
      {
         MediaInfo mi = MediaInfo(MediaType_Audio, toCPCAPIMediaDirection(requestedMedia.audioDirection));
         mi.mediaEncryptionOptions.mediaEncryptionMode = toCPCAPIMediaEncryptionMode(requestedMedia.secureAudioMode);
         mi.mediaEncryptionOptions.secureMediaRequired = requestedMedia.secureAudioRequired;
         mi.mediaEncryptionOptions.mediaCryptoSuites = ReconConversationManagerImpl::convertMediaCryptoSuites(requestedMedia.secureAudioDefaultCryptoSuites);
         args.remoteMediaInfo.push_back(mi);
      }
      if (requestedMedia.videoDirection != ConversationManager::MediaDirection_None)
      {
         MediaInfo mi = MediaInfo(MediaType_Video, toCPCAPIMediaDirection(requestedMedia.videoDirection));
         mi.mediaEncryptionOptions.mediaEncryptionMode = toCPCAPIMediaEncryptionMode(requestedMedia.secureVideoMode);
         mi.mediaEncryptionOptions.secureMediaRequired = requestedMedia.secureVideoRequired;
         mi.mediaEncryptionOptions.mediaCryptoSuites = ReconConversationManagerImpl::convertMediaCryptoSuites(requestedMedia.secureVideoDefaultCryptoSuites);
         args.remoteMediaInfo.push_back(mi);
      }
      ci->remoteMediaInfo = args.remoteMediaInfo;
   }
   else
   {
      InfoLog(<< "onIncomingTransferRequest: Empty INVITE - no SDP Offer");
   }

   mCallImpl.fireEvent(cpcEvent(SipConversationHandler, onNewConversation), h, args);
   ci->lastRemoteAddr = { args.remoteAddress, args.remoteDisplayName };
   mCallImpl.countNewCall();
}

void
ReconConversationManagerImpl::onRequestOutgoingParticipant(recon::ParticipantHandle transferTargetHandle, const resip::SipMessage& msg)
{
   /*
   SUA_TRACES_DEBUG("onRequestOutgoingParticipant: transferTargetHandle=" << transferTargetHandle << "; To: " << msg.header(h_To));

   if (mReferSink)
   {
      mReferSink->OnReferRequestForNewOutgoingCall(transferTargetHandle, std::shared_ptr<resip::SipMessage>(new resip::SipMessage(msg)));
   }
   */
}

void
ReconConversationManagerImpl::onRequestOutgoingParticipant(recon::ParticipantHandle transferTargetHandle, recon::ParticipantHandle transfererHandle, const resip::SipMessage& msg)
{
   resip::NameAddr transferTarget = msg.header(h_ReferTo);
   SipConversationHandle h = SipConversationHandleFactory::getNext();
   SipCallCreationInfo* ci = new SipCallCreationInfo();
   ci->reconRemoteParticipant = transferTargetHandle;
   ci->account = mAccount.getHandle();
   ci->targetAddresses.push_back(transferTarget);
   DebugLog(<< "ReconConversationManagerImpl::onRequestOutgoingParticipant(): added new conversation: " << h);
   addCreationInfo(h, ci);

   TransferRequestEvent args;
   args.transferTargetAddress = CharEncodingHelper::unEscape(transferTarget.uri().getAorNoReally()).c_str();
   args.transferTargetDisplayName = transferTarget.displayName().c_str();
   args.transferTargetConversation = h;

   SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(transfererHandle);
   SipCallCreationInfo* ciTransferer = getCreationInfo(convHandle);
   if (ciTransferer != NULL)
   {
      if (ciTransferer->originalConversation != 0)
      {
         // for forked call, the original conv handle needs to be passed to the handler
         convHandle = ciTransferer->originalConversation;
      }
      ci->localMediaInfo = ciTransferer->localMediaInfo; // retain local media preferences
      ci->configuredLocalMediaInfo = ciTransferer->configuredLocalMediaInfo; // retain configured media preferences
      ci->bestEffortMediaEncryption = ciTransferer->bestEffortMediaEncryption; // retain media encyprtion options
      ci->callKitMode = ciTransferer->callKitMode; //retain callKitMode
   }
   mCallImpl.fireEvent(cpcEvent(SipConversationHandler, onIncomingTransferRequest), convHandle, args);
}

void
ReconConversationManagerImpl::onLocalParticipantRedirected(recon::ParticipantHandle partHandle, const resip::SipMessage& msg)
{
   if (!msg.exists(h_Contacts)) return;

   resip::NameAddr target = msg.header(h_Contacts).front();
   SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(partHandle);
   SipCallCreationInfo* ci = getCreationInfo(convHandle);
   if (!ci)
   {
      WarningLog(<< "Missing SipCallCreationInfo for SipConversationHandle " << convHandle);
      return;
   }

   ci->redirectedAddrs.push_back(target);

   RedirectRequestEvent args;
   args.targetAddress = CharEncodingHelper::unEscape(target.uri().getAorNoReally()).c_str();

   if (msg.exists(h_Reasons) && !msg.header(h_Reasons).empty())
   {
      args.reason = msg.header(h_Reasons).front().value().c_str();
   }

   mCallImpl.fireEvent(cpcEvent(SipConversationHandler, onIncomingRedirectRequest), convHandle, args);
}

void
ReconConversationManagerImpl::onNewOutgoingParticipant(recon::ParticipantHandle partHandle, recon::ParticipantHandle origParticipantHandle, const resip::SipMessage& msg)
{
   SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(origParticipantHandle, true);
   SipCallCreationInfo* ci = getCreationInfo(convHandle);
   if (ci != NULL)
   {
      SipConversationHandle existingConvHandle = findSipConversationHandleByParticipantHandle(partHandle);
      if (existingConvHandle == 0xFFFFFFFF)
      {
         //std::cout << "updating reconRemoteParticipant " << ci->reconRemoteParticipant << " to " << partHandle << std::endl;
         ci->reconOriginalRemoteParticipant = ci->reconRemoteParticipant;
         ci->reconRemoteParticipant = partHandle;
      }
      if (!ci->wasRedirected)
      {
         addParticipant(ci->reconConversation, partHandle);
      }
   }
}

void
ReconConversationManagerImpl::onNewOutgoingParticipant(recon::ParticipantHandle partHandle, const resip::SipMessage& msg)
{
}

resip::Data
terminatedReasonToString(resip::InviteSessionHandler::TerminatedReason reason)
{
   switch (reason)
   {
   case InviteSessionHandler::Error:
      return "Error";
   case InviteSessionHandler::Timeout:
      return "Timeout";
   case InviteSessionHandler::Replaced:
      return "Replaced";
   case InviteSessionHandler::LocalBye:
      return "LocalBye";
   case InviteSessionHandler::RemoteBye:
      return "RemoteBye";
   case InviteSessionHandler::LocalCancel:
      return "LocalCancel";
   case InviteSessionHandler::RemoteCancel:
      return "RemoteCancel";
   case InviteSessionHandler::Rejected:
      return "Rejected";
   case InviteSessionHandler::Referred:
      return "Referred";
   }
   return "UnknownReason";
}

void
ReconConversationManagerImpl::finalizeConversation(recon::ParticipantHandle partHandle, resip::InviteSessionHandler::TerminatedReason reason, const resip::SipMessage* msg)
{
   SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(partHandle);
   DebugLog(<< "ReconConversationManagerImpl::finalizeConversation(): retrieved conversation: " << convHandle << " from participant: " << partHandle);
   // std::cout << "    > finalizeConversation(" << StringConv::wstringToUtf8(mAccount.getSettings().username) << ") convHandle=" << convHandle << std::endl;
   if (convHandle != 0xffffffff)
   {
      SipCallCreationInfo* ci = getCreationInfo(convHandle);
      if (!ci)
      {
         WarningLog(<< "Missing SipCallCreationInfo for SipConversationHandle " << convHandle);
         return;
      }
      // std::cout << "    > finalizeConversation" << StringConv::wstringToUtf8(mAccount.getSettings().username) << ) ci=" << ci << " ci->orig=" << ci->originalConversation << std::endl;
      if (reason == resip::InviteSessionHandler::LocalCancel && (!ci->redirectedAddrs.empty() || ci->wasRedirected))
      {
         ci->wasRedirected = false;
         return; // do not remove here; the SipCallCreationInfo will get re-used for the (new) call created as a result of the 302 redirect
      }

      if (ci->bestEffortMediaEncryption)
      {
         // 'Best Effort' media encryption mode is enabled
         if (ci->bestEffortMediaEncryptionState == BestEffortMediaEncryptionState_SAVP_Call)
         {
            // A response for the SAVP call has been received
            if (msg != NULL && msg->isResponse() && msg->header(h_StatusLine).statusCode() == 488)
            {
               // SAVP call was rejected
               ci->bestEffortMediaEncryptionState = BestEffortMediaEncryptionState_SAVP_Call_Rejected;
         
               // Make the AVP call
               SipConversationManager::getInterface(mAccount.getPhone())->start(convHandle);

               // Don't send a termination and do not remove the conversation handle as we are going to re-use it but count as ended
               mCallImpl.countEndCall();

               return;
            }
         }
         else if (ci->bestEffortMediaEncryptionState == BestEffortMediaEncryptionState_SAVP_Call_Rejected)
         {
            // SVAP call rejected

            // Don't send a termination and do not remove the conversation handle as we are going to re-use it
            return;
         }
      }

      resip::Data callQualityReport;
      std::vector<std::weak_ptr<webrtc_recon::RtpStreamImpl> >::iterator itRtpStreams = ci->rtpStreams.begin();
      for (; itRtpStreams != ci->rtpStreams.end(); itRtpStreams++)
      {
         if (std::shared_ptr<webrtc_recon::RtpStreamImpl> rtpStream = itRtpStreams->lock())
         {
            if (callQualityReport.size() == 0)
            {
               // implemented for audio only
               rtpStream->generateCallQualityReport();
               callQualityReport = rtpStream->getCallQualityReport();
            }
         }
      }

      if (ci->originalConversation == 0)
      {
         stopMonitoringAudioLevels(convHandle);

         ConversationEndedEvent args;
         args.conversationState = ConversationState_Ended;
         args.sipResponseCode = 0;
         args.endReason = ConversationEndReason_Unknown;
         args.responseTimeMs = ci->responseTimeMs;

         if (msg != NULL)
         {
            if (msg->isResponse())
            {
               args.sipResponseCode = msg->header(h_StatusLine).statusCode(); // e.g. 480
               if (msg->header(h_StatusLine).reason().size() > 0)
               {
                  args.signallingEndEvent = msg->header(h_StatusLine).reason().c_str(); //e.g. Temporary Unavailable
               }

               // Warning header carries a 3-Digit Code, Hostname and Warning Text
               // e.g. Warning: 307 isi.edu "Session parameter 'foo' not understood"
               if (msg->exists(h_Warnings) && !msg->header(h_Warnings).empty())
               {
                  args.signallingEndWarningCode = msg->header(h_Warnings).front().code();
                  args.signallingEndWarning = msg->header(h_Warnings).front().text().c_str();
               }
            }
            else
            {
               args.signallingEndEvent = resip::getMethodName(msg->header(h_RequestLine).method()).c_str(); // e.g. BYE/CANCEL
            }

            // both requests and responses can have Reason header
            if (msg->exists(h_Reasons) && !msg->header(h_Reasons).empty())
            {
               for (H_Reasons::Type::const_iterator it = msg->header(h_Reasons).begin(); it != msg->header(h_Reasons).end(); ++it)
               {
                  if (it->isWellFormed() && (it->value().find("SIP") != Data::npos || it->value().find("Q.850") != Data::npos))
                  {
                     resip::Data reasonTxt;
                     {
                        resip::DataStream ds(reasonTxt);
                        ds << it->value().c_str();

                        if (it->exists(p_cause))
                        {
                           ds << ";cause=" << it->param(p_cause);
                        }

                        if (it->exists(p_text) && !it->param(p_text).empty())
                        {
                           ds << ";text=\"" << it->param(p_text).c_str() << "\"";
                        }
                     }
                     args.signallingEndReason = reasonTxt.c_str();
                  }

                  // From RFC3326 - the proxy adds SIP;cause=200 to the reason field of CANCEL messages to the endpoints which didn't answer the call
                  if (msg->isRequest() && msg->header(h_RequestLine).method() == MethodTypes::CANCEL && it->exists(p_cause) && it->param(p_cause) == 200)
                  {
                     args.endReason = ConversationEndReason_CallAnsweredElsewhere; // identify forking case
                  }
               }
            }

            if (args.signallingEndReason.empty())
            {
               // limited backwards compatibility with SDK versions <= 1.5.2
               args.signallingEndReason = args.signallingEndEvent;
            }

            SipHelpers::populateSessionId(*msg, args.sessionId);
        }

        if (ConversationEndReason_CallAnsweredElsewhere != args.endReason)
        {
          switch (reason)
          {
            case InviteSessionHandler::Rejected:
            case InviteSessionHandler::LocalBye:
            case InviteSessionHandler::LocalCancel:
               args.endReason = ConversationEndReason_UserTerminatedLocally;
               break;
            case InviteSessionHandler::RemoteBye:
            case InviteSessionHandler::RemoteCancel:
               args.endReason = ConversationEndReason_UserTerminatedRemotely;
               break;
            case InviteSessionHandler::Error:
               if (args.sipResponseCode / 100 == 4)
               {
                  args.endReason = ConversationEndReason_ServerRejected;
               }
               else
               {
                  args.endReason = ConversationEndReason_ServerError;
               }
               break;
            default:
               break;
          }
        }

         args.callQualityReport = cpc::string(callQualityReport.c_str(), callQualityReport.size());

         // Android and iOS specific
         if(ci->callKitMode)
         {
            webrtc::AudioDeviceModule* adm = mCallImpl.media_stack()->voe_base()->audio_device();
            adm->ActivatePlayAndRecordMode(false);
         }

         // only fire event for original conversation leg
         mCallImpl.fireConvEndedEvent(convHandle, args);
         mCallImpl.countEndCall();
      }

      removeCreationInfo(convHandle);
   }
}

void
ReconConversationManagerImpl::onParticipantTerminated(recon::ParticipantHandle partHandle, unsigned int statusCode, resip::InviteSessionHandler::TerminatedReason reason, const resip::SipMessage* msg)
{
   // std::cout << "***** onParticipantTerminated(" << StringConv::wstringToUtf8(mAccount.getSettings().username) << ") part=" << partHandle <>< std::endl;

   SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(partHandle);
   if (convHandle != 0xffffffff)
   {
      SipCallCreationInfo* ci = getCreationInfo(convHandle);
      if (ci != NULL)
      {
         // fire an onConversationStatisticsUpdated event now so that the SipConversationState code
         // can be used to obtain "final" values for the stats after the call is over;
         // note this has to be done before destroyParticipant(..) below so that the RTP stream data counters
         // are still valid (they get reset when destroyParticipant(..) calls stopRtpSend(..))
         refreshConversationStatisticsImpl(convHandle, true, true, true, true);

         Conversation* conv = getConversation(ci->reconConversation);
         if (conv != NULL)
         {
            if (conv->getNumRemoteParticipants() <= 1)
            {
               destroyConversation(ci->reconConversation);
               removeParticipant(ci->reconConversation, mLocalParticipant);
            }
         }
      }
   }

   finalizeConversation(partHandle, reason, msg);
}

void
ReconConversationManagerImpl::onConversationDestroyed(recon::ConversationHandle convHandle)
{
}

void
ReconConversationManagerImpl::onParticipantDestroyed(recon::ParticipantHandle partHandle)
{
   // std::cout << "***** onParticipantDestroyed(" << StringConv::wstringToUtf8(mAccount.getSettings().username) << ") part=" << partHandle << std::endl;
   // .jjg. this is a "last resort" method of signalling to the rest of the SDK that the call
   // has been destroyed -- e.g. we get in here in the case where the Contact in our outgoing INVITE
   // is bogus, and then the user eventually gives up by "hanging up" the Call in the SDK
   //
   finalizeConversation(partHandle, InviteSessionHandler::LocalCancel, NULL);
}

void
ReconConversationManagerImpl::onRelatedConversation(recon::ConversationHandle relatedConvHandle, recon::ParticipantHandle relatedPartHandle,
                                              recon::ConversationHandle origConvHandle, recon::ParticipantHandle origPartHandle)
{
   //std::cout << "related part: " << relatedPartHandle << ", origPart: " << origPartHandle << std::endl;
   SipConversationHandle cpcOrigHandle = findSipConversationHandle(origConvHandle);
   SipCallCreationInfo* ciOrig = getCreationInfo(cpcOrigHandle);
   if (ciOrig != NULL)
   {
      ciOrig->wasForked = true;
   }

   SipConversationHandle cpcForkedHandle = SipConversationHandleFactory::getNext();
   SipCallCreationInfo* ci = new SipCallCreationInfo();
   ci->account = mAccount.getHandle();
   ci->reconConversation = relatedConvHandle;
   ci->reconRemoteParticipant = relatedPartHandle;
   ci->originalConversation = cpcOrigHandle;
   if (ciOrig)
   {
      // OBELISK-6181 When creating a related conversation copy over the media infos to maintain encryption settings.
      ci->localMediaInfo = ciOrig->localMediaInfo;
      ci->remoteMediaInfo = ciOrig->remoteMediaInfo;
      ci->targetAddresses = ciOrig->targetAddresses;
      ci->configuredLocalMediaInfo = ciOrig->configuredLocalMediaInfo;
      ci->lastRemoteAddr = ciOrig->lastRemoteAddr;
      // preserve callKitMode
      ci->callKitMode = ciOrig->callKitMode;
   }
   ci->wasForked = true;
   AnswerModeSettings answerModeDefault;
   ci->answerMode = (ciOrig ? ciOrig->answerMode : answerModeDefault);
   DebugLog(<< "ReconConversationManagerImpl::onRelatedConversation(): added new conversation: " << cpcForkedHandle << " original conversation: " << cpcOrigHandle);
   addCreationInfo(cpcForkedHandle, ci);
}

void
ReconConversationManagerImpl::onParticipantAlerting(recon::ParticipantHandle partHandle, const resip::SipMessage& msg)
{
   // .jjg. TODO: in the case of forking, we should have another field in the ConversationStateChangedEvent
   // which we set to the actual SipConversationHandle that we allocated in onNewSession(..);
   // the app should be able to use this to destroy individual call legs

   SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(partHandle);
   SipCallCreationInfo* ci = getCreationInfo(convHandle);
   if (!ci)
   {
      WarningLog(<< "Missing SipCallCreationInfo for SipConversationHandle " << convHandle);
      return;
   }

   ConversationStateChangedEvent args;
   args.conversationState = ConversationState_RemoteRinging;
   args.extendedStateInfo = ExtendedStateInfo_None;
   args.dialogId = SipHelpers::getDialogId(msg);
   args.alertInfoHeader = SipHelpers::getFirstAlertInfoOrCallInfo(msg);
   args.answerMode = ci->answerMode;
   if (msg.isResponse())
   {
      args.responseCode = msg.header(h_StatusLine).responseCode();
      args.statusText = msg.header(h_StatusLine).reason().c_str();
      args.responseTimeMs = ci->responseTimeMs;
      if (args.responseCode == 181)
      {
         args.extendedStateInfo = EntendedStateInfo_RemoteRinging_CallIsBeingForwarded;
      }
      else if (args.responseCode == 182)
      {
         args.extendedStateInfo = EntendedStateInfo_RemoteRinging_Queued;
      }
      else
      {
         args.extendedStateInfo = EntendedStateInfo_RemoteRinging_Ringing;
      }
   }

   if (mAccount.getSettings().preferPAssertedIdentity && msg.exists(h_PAssertedIdentities) && !msg.header(h_PAssertedIdentities).empty())
   {
      resip::NameAddr remoteNameAddr = msg.header(h_PAssertedIdentities).front();
      args.remoteAddress = CharEncodingHelper::unEscape(remoteNameAddr.uri()).c_str();
      args.remoteDisplayName = Data::from(remoteNameAddr.displayName()).c_str();
   }
   else
   {
      if (ci->targetAddresses.size() > 0)
      {
         args.remoteAddress = CharEncodingHelper::unEscape(ci->targetAddresses[0].uri()).c_str();
      }

      //Connected Line Identification Presentation (COLP)
      if (msg.exists(h_To))
      {
         resip::NameAddr remoteNameAddr = msg.header(h_To);
         if (!remoteNameAddr.displayName().empty())
         {
            args.remoteDisplayName = Data::from(remoteNameAddr.displayName()).c_str();
         }
      }
   }

   SipHelpers::populateHistoryInfos(msg, args.historyInfo);
   SipHelpers::populateSessionId(msg, args.sessionId);

   if (ci->originalConversation != 0)
   {
      // forking has happened; recon represents each forked leg with a new recon::ParticipantHandle;
      // but we want to represent it as just another ringing event for the same (original) SipConversationHandle
      convHandle = ci->originalConversation;
   }

   if (// don't send a ringing response if we've sent one before
       ci->sentRemoteRinging &&
       // except in the case that the provisional response was caused by a redirect
       ci->lastRemoteRingingEvent.contactHeaderField == args.contactHeaderField &&
       ci->lastRemoteRingingEvent.remoteAddress == args.remoteAddress &&
       ci->lastRemoteRingingEvent.remoteDisplayName == args.remoteDisplayName)
   {
      return;
   }

   if (ci->sentRemoteRinging && ci->lastRemoteRingingEvent.extendedStateInfo == EntendedStateInfo_RemoteRinging_CallIsBeingForwarded)
   {
      args.extendedStateInfo = EntendedStateInfo_RemoteRinging_CallIsBeingForwarded;
   }
   DebugLog(<< "Firing onConversationStateChanged remote ringing for " << args.remoteAddress );

   ci->currentConversationState = args.conversationState;
   mCallImpl.fireConvStateChangedEvent(convHandle, args);
   ci->lastRemoteAddr = { args.remoteAddress, args.remoteDisplayName };
   ci->sentRemoteRinging = true;
   ci->lastRemoteRingingEvent = args;
}

void
ReconConversationManagerImpl::onParticipantEarlyMedia(recon::ParticipantHandle partHandle, const resip::SipMessage& msg)
{
   ConversationStateChangedEvent args;
   args.conversationState = ConversationState_Early;
   args.dialogId = SipHelpers::getDialogId(msg);
   args.alertInfoHeader = SipHelpers::getFirstAlertInfoOrCallInfo(msg);

   SipHelpers::populateSessionId(msg, args.sessionId);

   SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(partHandle);
   SipCallCreationInfo* ci = getCreationInfo(convHandle);
   if (!ci)
   {
      WarningLog(<< "Missing SipCallCreationInfo for SipConversationHandle " << convHandle);
      return;
   }

   ci->answerMode = extractAnswerMode(msg);
   args.answerMode = ci->answerMode;
   args.responseTimeMs = ci->responseTimeMs;
   args.responseCode = msg.header(h_StatusLine).responseCode();

   if (mAccount.getSettings().preferPAssertedIdentity && msg.exists(h_PAssertedIdentities) && !msg.header(h_PAssertedIdentities).empty())
   {
      resip::NameAddr remoteNameAddr = msg.header(h_PAssertedIdentities).front();
      args.remoteAddress = CharEncodingHelper::unEscape(remoteNameAddr.uri()).c_str();
      args.remoteDisplayName = Data::from(remoteNameAddr.displayName()).c_str();
   }
   else
   {
      if (ci->targetAddresses.size() > 0)
      {
         args.remoteAddress = CharEncodingHelper::unEscape(ci->targetAddresses[0].uri()).c_str();
      }
   }

   if (ci->originalConversation != 0)
   {
      // forking has happened; recon represents each forked leg with a new recon::ParticipantHandle;
      // but we want to represent it as just another ringing event for the same (original) SipConversationHandle
      convHandle = ci->originalConversation;
   }

   ci->currentConversationState = args.conversationState;
   mCallImpl.fireConvStateChangedEvent(convHandle, args);
   ci->lastRemoteAddr = { args.remoteAddress, args.remoteDisplayName };
}

void
ReconConversationManagerImpl::onParticipantConnected(recon::ParticipantHandle partHandle, const resip::SipMessage& msg)
{
   mConvState = ConversationState_Connected;
   ConversationStateChangedEvent args;
   args.conversationState = ConversationState_Connected;
   args.dialogId = SipHelpers::getDialogId(msg);
   args.alertInfoHeader = SipHelpers::getFirstAlertInfoOrCallInfo(msg);

   SipHelpers::populateSessionId(msg, args.sessionId);

   // Read Contact header field to get focus URI from conference factory in case of server based conference.
   if (msg.exists(h_Contacts))
   {
      args.contactHeaderField = resip::Data::from(msg.header(h_Contacts).front()).c_str();
   }

   SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(partHandle);
   SipCallCreationInfo* ci = getCreationInfo(convHandle);

   if (!ci)
   {
      WarningLog(<< "Missing SipCallCreationInfo for SipConversationHandle " << convHandle);
      return;
   }

   args.answerMode = ci->answerMode;
   args.responseTimeMs = ci->responseTimeMs;
   args.responseCode = msg.header(h_StatusLine).responseCode();

   if (ci->conversationToReplaceWithStarcode != 0 && !ci->lastRemoteAddr.empty())
   {
      // Prefer existing remoteAddr for starcode calls which could have been populated from the original call the starcode pulled.
      // Note: arguably we could adjust this logic to only apply this if P-Asserted-Identity is not present in the starcode call
      args.remoteAddress = ci->lastRemoteAddr.address;
      args.remoteDisplayName = ci->lastRemoteAddr.displayName;
   }
   else if (mAccount.getSettings().preferPAssertedIdentity && msg.exists(h_PAssertedIdentities) && !msg.header(h_PAssertedIdentities).empty())
   {
      resip::NameAddr remoteNameAddr = msg.header(h_PAssertedIdentities).front();
      args.remoteAddress = CharEncodingHelper::unEscape(remoteNameAddr.uri()).c_str();
      args.remoteDisplayName = Data::from(remoteNameAddr.displayName()).c_str();    
   }
   else
   {
      if (ci->targetAddresses.size() > 0)
      {
         args.remoteAddress = CharEncodingHelper::unEscape(ci->targetAddresses[0].uri()).c_str();
      }
   }

   ci->currentConversationState = args.conversationState;

   if (ci->originalConversation != 0)
   {
      // forking happened, we created new SipConversationHandle + SipCallCreationInfo for a forked leg, and now that leg has
      // been answered; we need to make it look like the original SipConversationHandle is the one that is now connected
      SipCallCreationInfo* ciOrig = getCreationInfo(ci->originalConversation);

      if (!ciOrig)
      {
         WarningLog(<< "Missing SipCallCreationInfo for originalConversation handle " << ci->originalConversation);
         return;
      }

      cpc::vector<MediaInfo> configuredLocalMediaInfo = ciOrig->configuredLocalMediaInfo;
      std::vector<std::weak_ptr<webrtc_recon::RtpStreamImpl> > ciOrigRtpStreams = ciOrig->rtpStreams;
      (*ciOrig) = (*ci);
      if (ciOrigRtpStreams.size() > 0 && ciOrig->rtpStreams.size() == 0)
      {
         ciOrig->rtpStreams = ciOrigRtpStreams;
      }
      ciOrig->originalConversation = 0;
      ciOrig->configuredLocalMediaInfo = configuredLocalMediaInfo;
      convHandle = ci->originalConversation;
   }

   mCallImpl.fireConvStateChangedEvent(convHandle, args);
   ci->lastRemoteAddr = { args.remoteAddress, args.remoteDisplayName };

   DebugLog(<< "ReconConversationManagerImpl::onParticipantConnected(): " << this << " account: " << mAccount.getHandle() << " conversation: " << convHandle);
   if (getConvSettings().networkChangeHandoverMode == SipConversation::NetworkChangeHandoverMode_Starcode)
   {
      SipAVConversationManagerInterface* conMgr = dynamic_cast<SipAVConversationManagerInterface*>(SipConversationManager::getInterface(mAccount.getPhone()));

      for (SipCallCreationInfoMap::const_iterator i = mCallCreationInfo.begin(); i != mCallCreationInfo.end(); ++i)
      {
         if (i->second && (i->second->conversationToReplaceWithStarcode != 0))
         {
            conMgr->end(i->second->conversationToReplaceWithStarcode);
            i->second->conversationToReplaceWithStarcode = 0;
         }
      }
   }
}

void buildTransferProgressEvent(TransferProgressEvent& args, const resip::SipMessage* msg)
{
   resip::SipFrag* frag = (msg ? dynamic_cast<SipFrag*>(msg->getContents()) : NULL);
   args.sipResponseCode = 0;
   if (frag)
   {
      if (frag->message().isResponse())
      {
         args.sipResponseCode = frag->message().header(h_StatusLine).responseCode();
      }
   }
   else
   {
      if (msg && msg->isResponse())
      {
         args.sipResponseCode = msg->header(h_StatusLine).responseCode();
      }
   }
   
   switch (args.sipResponseCode)
   {
   case 0:
   case 100:
      args.progressEventType = TransferProgressEventType_Trying;
      break;
   case 180:
   case 183:
      args.progressEventType = TransferProgressEventType_Ringing;
      break;
   case 200:
      args.progressEventType = TransferProgressEventType_Connected;
      break;
   case 300:
   case 301:
   case 302:
      args.progressEventType = TransferProgressEventType_Redirected;
      break;
   default:
      args.progressEventType = TransferProgressEventType_Failed;
   }
}

void
ReconConversationManagerImpl::onParticipantRedirectProgress(recon::ParticipantHandle partHandle, const resip::SipMessage* msg)
{
   TransferProgressEvent args;
   buildTransferProgressEvent(args, msg);
   SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(partHandle);

   mCallImpl.fireEvent(cpcEvent(SipConversationHandler, onTransferProgress), convHandle, args);
}

void
ReconConversationManagerImpl::onParticipantRedirectSuccess(recon::ParticipantHandle partHandle, const resip::SipMessage* msg)
{
   SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(partHandle);
   if (NULL == msg)
   {
      SipCallCreationInfo* ci = getCreationInfo(convHandle);

      // Android iOS specific
      if(ci && ci->callKitMode)
      {
         webrtc::AudioDeviceModule* adm = mCallImpl.media_stack()->voe_base()->audio_device();
         adm->ActivatePlayAndRecordMode(false);
      }
      
      // 302 redirect
      ConversationEndedEvent args;
      args.conversationState = ConversationState_Ended;
      args.sipResponseCode = 0;
      args.endReason = ConversationEndReason_Redirected;

      mCallImpl.fireConvEndedEvent(convHandle, args);
      mCallImpl.countEndCall();

      stopMonitoringAudioLevels(convHandle);

      removeCreationInfo(convHandle);
   }
   else
   {
      TransferProgressEvent args;
      buildTransferProgressEvent(args, msg);

      mCallImpl.fireEvent(cpcEvent(SipConversationHandler, onTransferProgress), convHandle, args);

      SipCallCreationInfo* ci = getCreationInfo(convHandle);
      if (ci != NULL)
      {
         if (!ci->shouldEndOriginalCallAfterAttendedTransferSuccess)
         {
            return;
         }
      }
      destroyRelatedConversations(convHandle);
   }
}

void
ReconConversationManagerImpl::onParticipantRedirectFailure(recon::ParticipantHandle partHandle, unsigned int statusCode, const resip::Data& reasonPhrase, const resip::SipMessage* msg)
{
   TransferProgressEvent args;
   buildTransferProgressEvent(args, msg);

   SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(partHandle);
   mCallImpl.fireEvent(cpcEvent(SipConversationHandler, onTransferProgress), convHandle, args);
}

void
ReconConversationManagerImpl::onDtmfEvent(recon::ParticipantHandle partHandle, int dtmf, int duration, bool up)
{
   /*
   SUA_TRACES_DEBUG("onDtmfEvent: partHandle=" << partHandle << "; dtmf=" << dtmf << "; duration=" << duration << "; up=" << up);
   */
}

ConversationManager::MediaDirection toLocalState(ConversationManager::MediaDirection remoteState)
{
   ConversationManager::MediaDirection localState = ConversationManager::MediaDirection_None;
   switch (remoteState)
   {
   case ConversationManager::MediaDirection_Inactive:
      localState = ConversationManager::MediaDirection_Inactive;
      break;
   case ConversationManager::MediaDirection_ReceiveOnly:
      localState = ConversationManager::MediaDirection_SendOnly;
      break;
   case ConversationManager::MediaDirection_SendOnly:
      localState = ConversationManager::MediaDirection_ReceiveOnly;
      break;
   case ConversationManager::MediaDirection_SendReceive:
      localState = ConversationManager::MediaDirection_SendReceive;
      break;
   default:
      break;
   }
   return localState;
}

MediaDirection 
getMediaDirection(const cpc::vector<MediaInfo>& mi, const MediaType& mediaType)
{
   for(cpc::vector<MediaInfo>::const_iterator it = mi.begin(); it != mi.end(); ++it)
   {
      if(it->mediaType == mediaType)
         return it->mediaDirection;
   }
   return MediaDirection_None;
}

MediaEncryptionMode 
getMediaEncryption(const cpc::vector<MediaInfo>& mi, const MediaType& mediaType)
{
   for(cpc::vector<MediaInfo>::const_iterator it = mi.begin(); it != mi.end(); ++it)
   {
      if(it->mediaType == mediaType)
         return it->mediaEncryptionOptions.mediaEncryptionMode;
   }
   return MediaEncryptionMode_Unencrypted;
}

bool 
getMediaEncryptionRequired(const cpc::vector<MediaInfo>& mi, const MediaType& mediaType)
{
   for(cpc::vector<MediaInfo>::const_iterator it = mi.begin(); it != mi.end(); ++it)
   {
      if(it->mediaType == mediaType)
         return it->mediaEncryptionOptions.secureMediaRequired;
   }
   return false;
}

cpc::vector<MediaCryptoSuite> ReconConversationManagerImpl::getMediaCryptoSuites(const cpc::vector<MediaInfo>& mi, const MediaType& mediaType)
{
   cpc::vector<MediaCryptoSuite> mediaCryptoSuites;
   for (cpc::vector<MediaInfo>::const_iterator it = mi.begin(); it != mi.end(); ++it)
   {
      if (it->mediaType == mediaType)
         return it->mediaEncryptionOptions.mediaCryptoSuites;
   }
   
   // TODO: Should this be invalid, rather than default value
   mediaCryptoSuites.push_back(MediaCryptoSuite_None);
   return mediaCryptoSuites;
}
   
void ReconConversationManagerImpl::onParticipantMediaChangeRequested(
   recon::ParticipantHandle partHandle,
   recon::ConversationManager::MediaDirection remoteAudioState,
   recon::ConversationManager::MediaDirection remoteVideoState,
   const recon::ConversationManager::MediaAttributes& mediaAttribs,
   bool mediaDidChange,
   const resip::SipMessage* msg)
{
   SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(partHandle);
   SipCallCreationInfoMap::iterator itCi = mCallCreationInfo.find(convHandle);
   if (itCi != mCallCreationInfo.end() && itCi->second->originalConversation != 0)
   {
      // forked call - make it look like the original conversation is the one that has media changed request
      convHandle = itCi->second->originalConversation;
      itCi = mCallCreationInfo.find(convHandle);
   }

   ConversationMediaChangeRequestEvent args;
   if (remoteAudioState != recon::ConversationManager::MediaDirection_None)
   {
      MediaInfo mi;
      mi.mediaDirection = toCPCAPIMediaDirection(remoteAudioState);
      
      mi.mediaEncryptionOptions.mediaEncryptionMode = toCPCAPIMediaEncryptionMode(mediaAttribs.secureAudioMode);
      mi.mediaEncryptionOptions.secureMediaRequired = mediaAttribs.secureAudioRequired;
      mi.mediaEncryptionOptions.mediaCryptoSuites = ReconConversationManagerImpl::convertMediaCryptoSuites(mediaAttribs.secureAudioDefaultCryptoSuites);

      mi.mediaType = MediaType_Audio;
      args.remoteMediaInfo.push_back(mi);
   }

   if (remoteVideoState != recon::ConversationManager::MediaDirection_None)
   {
      MediaInfo mi;
      mi.mediaDirection = toCPCAPIMediaDirection(remoteVideoState);
      
      mi.mediaEncryptionOptions.mediaEncryptionMode = toCPCAPIMediaEncryptionMode(mediaAttribs.secureVideoMode);
      mi.mediaEncryptionOptions.secureMediaRequired = mediaAttribs.secureVideoRequired;
      mi.mediaEncryptionOptions.mediaCryptoSuites = ReconConversationManagerImpl::convertMediaCryptoSuites(mediaAttribs.secureVideoDefaultCryptoSuites);
      
      mi.mediaType = MediaType_Video;
      args.remoteMediaInfo.push_back(mi);
   }

   if (itCi != mCallCreationInfo.end())
   {
      if (msg)
      {
         resip::NameAddr remoteNameAddr;
         if (msg->isRequest())
         {
            remoteNameAddr = msg->header(h_From);
         }
         else
         {
            remoteNameAddr = msg->header(h_To);
         }

         if (mAccount.getSettings().preferPAssertedIdentity && msg->exists(h_PAssertedIdentities) && !msg->header(h_PAssertedIdentities).empty())
         {
            remoteNameAddr = msg->header(h_PAssertedIdentities).front();
         }
         itCi->second->potentialTargetAddress.reset(new resip::NameAddr(remoteNameAddr));
      }

      // remains true until the user accepts the request
      itCi->second->hasPendingRequest = true;
   }

   mCallImpl.fireEvent(cpcEvent(SipConversationHandler, onConversationMediaChangeRequest), convHandle, args);
}

void
toCPCAPI2AudioCodec(const webrtc::CodecInst& audioCodec, CPCAPI2::SipConversation::AudioCodec& out)
{
   out.channels = audioCodec.channels;
   out.pacsize = audioCodec.pacsize;
   out.plfreq = audioCodec.plfreq;
   strcpy(out.plname, audioCodec.plname);
   out.pltype = audioCodec.pltype;
   out.rate = audioCodec.rate;
}

void
toCPCAPI2VideoCodec(const webrtc::VideoCodec& videoCodec, CPCAPI2::SipConversation::VideoCodec& out)
{
   out.height = videoCodec.height;
   out.maxBitrate = videoCodec.maxBitrate;
   out.maxFramerate = videoCodec.maxFramerate;
   out.minBitrate = videoCodec.minBitrate;
   strcpy(out.plName, videoCodec.plName);
   out.plType = videoCodec.plType;
   out.startBitrate = videoCodec.startBitrate;
   out.width = videoCodec.width;
}

void ReconConversationManagerImpl::onParticipantMediaChanged(
   recon::ParticipantHandle partHandle,
   recon::ConversationManager::MediaDirection audioState,
   recon::ConversationManager::MediaDirection videoState,
   recon::ConversationManager::MediaDirection remoteAudioState,
   ConversationManager::MediaDirection remoteVideoState,
   recon::ConversationManager::SecureMediaMode remoteAudioCryptoSuiteMode,
   recon::ConversationManager::SecureMediaMode remoteVideoCryptoSuiteMode,
   recon::ConversationManager::SecureMediaCryptoSuite remoteAudioCryptoSuite,
   recon::ConversationManager::SecureMediaCryptoSuite remoteVideoCryptoSuite,
   std::vector<recon::ConversationManager::SecureMediaCryptoSuite>& supportedAudioCryptoSuites,
   std::vector<recon::ConversationManager::SecureMediaCryptoSuite>& supportedVideoCryptoSuites,
   const resip::SipMessage* msg)
{
   ConversationMediaChangedEvent args;
   bool canSendAudio = false;
   bool canSendVideo = false;
   SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(partHandle);
   SipCallCreationInfo* callInfo = getCreationInfo(convHandle);
   if (!callInfo)
   {
      WarningLog(<< "Missing SipCallCreationInfo for SipConversationHandle " << convHandle);
      return;
   }

   InfoLog(<< "onParticipantMediaChanged remoteAudioCryptoSuiteMode = " << remoteAudioCryptoSuiteMode);
   if (audioState != recon::ConversationManager::MediaDirection_None)
   {
      CPCAPI2::SipConversation::MediaDirection direction = toCPCAPIMediaDirection(audioState);
      MediaInfo mi;
      mi.mediaDirection = direction;
      mi.mediaType = MediaType_Audio;
      mi.mediaCrypto = ReconConversationManagerImpl::convertMediaCryptoSuite(remoteAudioCryptoSuite);
      canSendAudio = (direction == CPCAPI2::SipConversation::MediaDirection_SendOnly) || (direction == CPCAPI2::SipConversation::MediaDirection_SendReceive);
      MediaInfo miRemote;
      miRemote.mediaDirection = CPCAPI2::SipConversation::MediaDirection_None;
      miRemote.mediaType = MediaType_Audio;
      miRemote.mediaCrypto = ReconConversationManagerImpl::convertMediaCryptoSuite(remoteAudioCryptoSuite);
      if (callInfo)
      {
         // TODO: Mode returned is original configuration, should it not be the mode as negotiated with remote party, e.g. if remote party had encryption disabled, the local media encyption state is Unencrypted, even though configured Enecrypted
         mi.mediaEncryptionOptions.mediaEncryptionMode = getMediaEncryption(callInfo->localMediaInfo, MediaType_Audio);
         mi.mediaEncryptionOptions.secureMediaRequired = (mi.mediaEncryptionOptions.mediaEncryptionMode != MediaEncryptionMode_Unencrypted);
         mi.mediaEncryptionOptions.mediaCryptoSuites = ReconConversationManagerImpl::getMediaCryptoSuites(callInfo->localMediaInfo, MediaType_Audio);

         miRemote.mediaDirection = toCPCAPIMediaDirection(remoteAudioState);
         miRemote.mediaEncryptionOptions.mediaEncryptionMode = toCPCAPIMediaEncryptionMode(remoteAudioCryptoSuiteMode);
         miRemote.mediaEncryptionOptions.secureMediaRequired = (miRemote.mediaEncryptionOptions.mediaEncryptionMode != MediaEncryptionMode_Unencrypted);
         miRemote.mediaEncryptionOptions.mediaCryptoSuites = ReconConversationManagerImpl::convertMediaCryptoSuites(supportedAudioCryptoSuites);
      }
      else
      {
         mi.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_Unencrypted;
         miRemote.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_Unencrypted;
      }
      args.localMediaInfo.push_back(mi);
      args.remoteMediaInfo.push_back(miRemote);
   }
   if (videoState != recon::ConversationManager::MediaDirection_None)
   {
      CPCAPI2::SipConversation::MediaDirection direction = toCPCAPIMediaDirection(videoState);
      MediaInfo mi;
      mi.mediaDirection = direction;
      mi.mediaType = MediaType_Video;
      mi.mediaCrypto = ReconConversationManagerImpl::convertMediaCryptoSuite(remoteVideoCryptoSuite);
      canSendVideo = (direction == CPCAPI2::SipConversation::MediaDirection_SendOnly) ||
            (direction == CPCAPI2::SipConversation::MediaDirection_SendReceive);
      MediaInfo miRemote;
      miRemote.mediaDirection = CPCAPI2::SipConversation::MediaDirection_None;
      miRemote.mediaType = MediaType_Video;
      miRemote.mediaCrypto = ReconConversationManagerImpl::convertMediaCryptoSuite(remoteVideoCryptoSuite);
      if (callInfo)
      {
         // TODO: Mode returned is original configuration, should it not be the mode as negotiated with remote party, e.g. if remote party had encryption disabled, the local media encyption state is Unencrypted, even though configured Enecrypted
         mi.mediaEncryptionOptions.mediaEncryptionMode = getMediaEncryption(callInfo->localMediaInfo, MediaType_Video);
         mi.mediaEncryptionOptions.secureMediaRequired = (mi.mediaEncryptionOptions.mediaEncryptionMode != MediaEncryptionMode_Unencrypted);
         mi.mediaEncryptionOptions.mediaCryptoSuites = ReconConversationManagerImpl::getMediaCryptoSuites(callInfo->localMediaInfo, MediaType_Video);

         miRemote.mediaDirection = toCPCAPIMediaDirection(remoteVideoState);
         miRemote.mediaEncryptionOptions.mediaEncryptionMode =  toCPCAPIMediaEncryptionMode(remoteVideoCryptoSuiteMode);
         miRemote.mediaEncryptionOptions.secureMediaRequired = (miRemote.mediaEncryptionOptions.mediaEncryptionMode != MediaEncryptionMode_Unencrypted);
         miRemote.mediaEncryptionOptions.mediaCryptoSuites = ReconConversationManagerImpl::convertMediaCryptoSuites(supportedVideoCryptoSuites);
      }
      else
      {
         mi.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_Unencrypted;
         miRemote.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_Unencrypted;
      }
      args.localMediaInfo.push_back(mi);
      args.remoteMediaInfo.push_back(miRemote);
   }

   callInfo->remoteMediaInfo = args.remoteMediaInfo;

   // on hold if we cannot send any media
   args.remoteHold = !canSendAudio && !canSendVideo;

   if (callInfo->originalConversation != 0)
   {
      // make it look like the original SipConversationHandle is the one that has media changed
      convHandle = callInfo->originalConversation;
   }

   recon::Conversation* reconConv = getConversation(callInfo->reconConversation);

   if (reconConv)
   {
      args.localHold = reconConv->getNumRemoteParticipants() == 0 && reconConv->getNumLocalParticipants() == 0;
   }
   else
   {
      DebugLog(<< "ReconConversationManagerImpl::onParticipantMediaChanged: localHold assumed false!");
      // Assumed false if recon conversation not available
      //FIXME why can it be null? Artem reported a stack trace from iOs where it was
      args.localHold = false;
   }

   std::vector<std::weak_ptr<webrtc_recon::RtpStreamImpl> >::iterator itRtpStreams = callInfo->rtpStreams.begin();

   for (; itRtpStreams != callInfo->rtpStreams.end(); itRtpStreams++)
   {
      if (std::shared_ptr<webrtc_recon::RtpStreamImpl> rtpStream = itRtpStreams->lock())
      {
         if (rtpStream->mediaType() == recon::MediaStack::MediaType_Audio)
         {
            webrtc::CodecInst audioCodec;
            cpc::vector<MediaInfo>::iterator itMi = args.localMediaInfo.begin();
            for (; itMi != args.localMediaInfo.end(); ++itMi)
            {
               if (itMi->mediaType == MediaType_Audio)
               {
                  dynamic_cast<webrtc_recon::MediaStackImpl*>(this->getMediaStack())->audioCodec()->GetSendCodec(rtpStream->channel(), audioCodec);
                  toCPCAPI2AudioCodec(audioCodec, itMi->audioCodec);
               }
            }
            itMi = args.remoteMediaInfo.begin();
            for (; itMi != args.remoteMediaInfo.end(); ++itMi)
            {
               if (itMi->mediaType == MediaType_Audio)
               {
                  dynamic_cast<webrtc_recon::MediaStackImpl*>(this->getMediaStack())->audioCodec()->GetRecCodec(rtpStream->channel(), audioCodec);
                  toCPCAPI2AudioCodec(audioCodec, itMi->audioCodec);
                  itMi->mediaStreamId = rtpStream->channel();
               }
            }

            rtpStream->setAudioDeviceCloseDelay(callInfo->audioDeviceCloseDelay);

#if (CPCAPI2_BRAND_RECORDING_MODULE == 1)
            // apply recordings to stream
            std::set<Recording::RecorderHandle>::iterator recIt = callInfo->recordings.begin();
            for(; recIt != callInfo->recordings.end(); recIt++)
            {
               int channel = rtpStream->channel();

               if(channel < 0)
               {
                  ErrLog(<< "didn't add conversation " << convHandle << " to recorder " << *recIt << " due to no channel");
               }
               else
               {
                  webrtc::VoEFile* voeFile = mCallImpl.media_stack()->file();
                  voeFile->FileRecorderAddChannel(*recIt, channel);
               }
            }
#endif //CPCAPI2_BRAND_RECORDING_MODULE
         }
         else if(rtpStream->mediaType() == recon::MediaStack::MediaType_Video)
         {
            bool inactive = false;
            webrtc::VideoCodec videoCodec;
            cpc::vector<MediaInfo>::iterator itMi = args.localMediaInfo.begin();
            for (; itMi != args.localMediaInfo.end(); ++itMi)
            {
               if (itMi->mediaType == MediaType_Video)
               {
                  dynamic_cast<webrtc_recon::MediaStackImpl*>(this->getMediaStack())->videoCodec()->GetSendCodec(rtpStream->channel(), videoCodec);
                  toCPCAPI2VideoCodec(videoCodec, itMi->videoCodec);
                  if (itMi->mediaDirection == CPCAPI2::SipConversation::MediaDirection_Inactive) inactive = true;
               }
            }
            itMi = args.remoteMediaInfo.begin();
            for (; itMi != args.remoteMediaInfo.end(); ++itMi)
            {
               if (itMi->mediaType == MediaType_Video)
               {
                  dynamic_cast<webrtc_recon::MediaStackImpl*>(this->getMediaStack())->videoCodec()->GetReceiveCodec(rtpStream->channel(), videoCodec);
                  toCPCAPI2VideoCodec(videoCodec, itMi->videoCodec);
                  itMi->mediaStreamId = rtpStream->channel();

                  if (rtpStream->isReceivingRtp() && 
                      itMi->mediaDirection != CPCAPI2::SipConversation::MediaDirection_Inactive &&
                      itMi->mediaDirection != CPCAPI2::SipConversation::MediaDirection_ReceiveOnly &&
                      itMi->mediaDirection != CPCAPI2::SipConversation::MediaDirection_None &&
                      !inactive)
                  {
                     onKeyFrameRequest(rtpStream, partHandle);
                  }
               }
            }
         }
      }
   }

   if (msg) {
      cpc::vector<cpc::string> assertedIdentities;
      SipHelpers::populatePAssertedIdentities(*msg, assertedIdentities);
      if (0 < assertedIdentities.size()) {
         args.assertedIdentity = assertedIdentities[0];
      }
   }

   mCallImpl.fireEvent(cpcEvent(SipConversationHandler, onConversationMediaChanged), convHandle, args);

   if (callInfo->potentialTargetAddress.get() != NULL &&
      ((callInfo->targetAddresses.size() == 1 && !(*(callInfo->potentialTargetAddress) == callInfo->targetAddresses[0])) // SDK dialed the call, target now differs
      ||
      (callInfo->targetAddresses.size() == 0))) // SDK received the call
   {
      callInfo->targetAddresses.clear();
      callInfo->targetAddresses.push_back(*(callInfo->potentialTargetAddress));

      ConversationStateChangedEvent convStateChangedArgs;
      convStateChangedArgs.conversationState = callInfo->currentConversationState;
      convStateChangedArgs.remoteAddress = CharEncodingHelper::unEscape(callInfo->potentialTargetAddress->uri()).c_str();
      convStateChangedArgs.remoteDisplayName = Data::from(callInfo->potentialTargetAddress->displayName()).c_str();
      convStateChangedArgs.answerMode = callInfo->answerMode;
      callInfo->potentialTargetAddress.reset();
      mCallImpl.fireConvStateChangedEvent(convHandle, convStateChangedArgs);
   }

   // streams may have changed, restart volume monitoring if it was running
   if (mAudioLevelMonitor != NULL)
   {
      stopMonitoringAudioLevels(convHandle);
      startMonitoringAudioLevels(convHandle);
   }
}

void ReconConversationManagerImpl::onNewSession(resip::ClientInviteSessionHandle h, resip::InviteSession::OfferAnswerType oat, const resip::SipMessage& msg)
{
   ConversationManager::onNewSession(h, oat, msg);

   recon::RemoteParticipant* remotePart = dynamic_cast<recon::RemoteParticipant*>(h->getAppDialog().get());
   if (remotePart)
   {
      recon::ParticipantHandle partHandle = remotePart->getParticipantHandle();
      SipConversationHandle sipConvHandle = findSipConversationHandleByParticipantHandle(partHandle);
      SipCallCreationInfo* ci = getCreationInfo(sipConvHandle);
      if (!ci)
      {
         WarningLog(<< "Missing SipCallCreationInfo for SipConversationHandle " << sipConvHandle);
         return;
      }

      std::vector<std::weak_ptr<webrtc_recon::RtpStreamImpl> >::iterator itRtpStreams = ci->rtpStreams.begin();
      for (; itRtpStreams != ci->rtpStreams.end(); ++itRtpStreams)
      {
         if (std::shared_ptr<webrtc_recon::RtpStreamImpl> rtpStream = itRtpStreams->lock())
         {
            rtpStream->setSessionId(msg.header(h_CallID).value());
         }
      }

      if (!ci->redirectedAddrs.empty())
      {
         const resip::NameAddr target = (!ci->redirectedAddrs.empty() ? ci->redirectedAddrs.front() : msg.header(h_To));
         ci->wasRedirected = true;
         ci->redirectedAddrs.clear();

         // we only fire onNewConversation for the original call, not for new forks
         NewConversationEvent args;
         args.conversationState = ConversationState_LocalOriginated;
         args.account = mAccount.getHandle();
         args.conversationType = ConversationType_Outgoing;
         args.relatedConversation = 0;
         args.conversationToJoin = 0;
         args.conversationToReplace = 0;
         args.localMediaInfo = ci->localMediaInfo;

         args.remoteAddress = CharEncodingHelper::unEscape(target.uri().getAorNoReally()).c_str();
         args.remoteDisplayName = target.displayName().c_str();
         args.answerMode = ci->answerMode;

         mCallImpl.fireNewConvEvent(sipConvHandle, args);
         ci->lastRemoteAddr = { args.remoteAddress, args.remoteDisplayName };
      }
      // store callId for use if media is re-created (OBELISK-3221)
      if (ci->callId.empty())
      {
         ci->callId = msg.header(h_CallID).value();
      }
   }
}

void ReconConversationManagerImpl::onNewSession(resip::ServerInviteSessionHandle h, resip::InviteSession::OfferAnswerType oat, const resip::SipMessage& msg)
{
   ConversationManager::onNewSession(h, oat, msg);

   recon::RemoteParticipant* remotePart = dynamic_cast<recon::RemoteParticipant*>(h->getAppDialog().get());
   if (remotePart)
   {
      recon::ParticipantHandle partHandle = remotePart->getParticipantHandle();
      SipConversationHandle sipConvHandle = findSipConversationHandleByParticipantHandle(partHandle);
      if (sipConvHandle == 0xFFFFFFFF)
      {
         sipConvHandle = SipConversationHandleFactory::getNext();
         SipCallCreationInfo* ci = new SipCallCreationInfo();
         ci->reconRemoteParticipant = partHandle;
         ci->reconOriginalRemoteParticipant = partHandle;
         ci->account = mAccount.getHandle();
         ci->callId = msg.header(h_CallID).value();
         ci->answerMode = extractAnswerMode(msg);
         DebugLog(<< "ReconConversationManagerImpl::onNewSession(): added new conversation: " << sipConvHandle);
         addCreationInfo(sipConvHandle, ci);
      }
   }
}

void ReconConversationManagerImpl::onAnswer(resip::InviteSessionHandle h, const resip::SipMessage& msg, const resip::SdpContents& sdp)
{
   ConversationManager::onAnswer(h, msg, sdp);
   recon::RemoteParticipant* remotePart = dynamic_cast<recon::RemoteParticipant*>(h->getAppDialog().get());
   if (remotePart)
   {
      recon::ParticipantHandle partHandle = remotePart->getParticipantHandle();
      SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(partHandle);
      SipCallCreationInfo* ci = getCreationInfo(convHandle);
      if (ci != NULL)
      {
         resip::Data sdpData(resip::Data::from(sdp));
         SdpOfferAnswerEvent args;
         args.sdp.sdpString = sdpData.c_str();
         args.sdp.sdpLen = sdpData.size();
         args.sdp.sdpType = CPCAPI2::SipConversation::SessionDescription::SessionDescriptionType_Answer;
         args.answerMode = extractAnswerMode(msg);
         mCallImpl.fireEvent(cpcEvent(SipConversationHandlerInternal, onSdpOfferAnswer), convHandle, args);
      }
      
      this->populateLocalRemoteConversationValues(convHandle);
   }
}

void ReconConversationManagerImpl::onOffer(resip::InviteSessionHandle h, const resip::SipMessage& msg, const resip::SdpContents& offer)
{
   ConversationManager::onOffer(h, msg, offer);

   // TODO: remove firing of obsolete event onSdpOfferAnswer and send onRemoteSdpAnswer instead from better spot
   recon::RemoteParticipant* remotePart = dynamic_cast<recon::RemoteParticipant*>(h->getAppDialog().get());
   if (remotePart)
   {
      recon::ParticipantHandle partHandle = remotePart->getParticipantHandle();
      SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(partHandle);
      SipCallCreationInfo* ci = getCreationInfo(convHandle);
      if (ci != NULL)
      {
         resip::Data sdpData(resip::Data::from(offer));
         SdpOfferAnswerEvent args;
         args.sdp.sdpLen = sdpData.size();
         args.sdp.sdpString = sdpData.c_str();
         args.sdp.sdpType = CPCAPI2::SipConversation::SessionDescription::SessionDescriptionType_Offer;
         args.answerMode = extractAnswerMode(msg);
         mCallImpl.fireEvent(cpcEvent(SipConversationHandlerInternal, onSdpOfferAnswer), convHandle, args);
      }
   }
}

void ReconConversationManagerImpl::onOfferRequired(resip::InviteSessionHandle h, const resip::SipMessage& msg)
{
   ConversationManager::onOfferRequired(h, msg);
}

void ReconConversationManagerImpl::onReferResponseReceived(resip::InviteSessionHandle h, const resip::SipMessage& msg)
{
   ConversationManager::onReferResponseReceived(h, msg);
   
   recon::RemoteParticipant* remotePart = dynamic_cast<recon::RemoteParticipant*>(h->getAppDialog().get());
   if (remotePart)
   {
      recon::ParticipantHandle partHandle = remotePart->getParticipantHandle();
      SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(partHandle);
      if (convHandle == 0xFFFFFFFF)
      {
         WarningLog(<< "Could not find SipConversationHandle for recon ParticipantHandle " << partHandle);
      }
      else
      {
         TransferResponseEvent args;
         args.sipResponseCode = msg.header(h_StatusLine).responseCode();
         args.warningHeader = SipHelpers::getFirstWarning(msg);
         mCallImpl.fireEvent(cpcEvent(SipConversationHandler, onTransferResponse), convHandle, args);
      }
   }
}

bool ReconConversationManagerImpl::onLocalOfferRequired(recon::ConversationHandle conversation, recon::ParticipantHandle participant, const recon::ConversationManager::MediaAttributes& mediaAttribs)
{
   SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(participant);
   SipCallCreationInfo* ci = getCreationInfo(convHandle);
   if (ci == NULL)
   {
      convHandle = findSipConversationHandle(conversation);
      ci = getCreationInfo(convHandle);
   }
   if (ci != NULL)
   {
      if (ci->mediaBypassMode)
      {
         LocalSdpOfferEvent args;
         args.localMediaInfo = toMediaInfo(mediaAttribs);
         mCallImpl.fireEvent(cpcEvent(SipConversationHandlerInternal, onLocalSdpOffer), convHandle, args);
         return true;
      }
   }
   return false;
}

bool ReconConversationManagerImpl::onLocalAnswerRequired(recon::ParticipantHandle participant, const resip::SdpContents& sdpOffer, const recon::ConversationManager::MediaAttributes& mediaAttribs)
{
   SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(participant);
   SipCallCreationInfo* ci = getCreationInfo(convHandle);
   if (ci != NULL)
   {
      if (ci->mediaBypassMode)
      {
         LocalSdpAnswerEvent args;
         args.localMediaInfo = toMediaInfo(mediaAttribs);
         resip::Data sdpData(resip::Data::from(sdpOffer));
         args.sdpOffer.sdpLen = sdpData.size();
         args.sdpOffer.sdpString = sdpData.c_str();
         args.sdpOffer.sdpType = CPCAPI2::SipConversation::SessionDescription::SessionDescriptionType_Offer;
         mCallImpl.fireEvent(cpcEvent(SipConversationHandlerInternal, onLocalSdpAnswer), convHandle, args);
         return true;
      }
   }
   return false;
}

void ReconConversationManagerImpl::onConnected(resip::ClientInviteSessionHandle h, const resip::SipMessage& msg)
{
   recon::RemoteParticipantDialogSet* remotePartDialogSet = dynamic_cast<recon::RemoteParticipantDialogSet*>(h->getAppDialogSet().get());
   if (NULL != remotePartDialogSet)
   {
      SipCallCreationInfo* ci = getCreationInfo(findSipConversationHandleByParticipantHandle(remotePartDialogSet->getActiveRemoteParticipantHandle()));
      if (ci != NULL)
      {
         ci->responseTimeMs = dynamic_cast<resip::AppDialogSet*>(h->getAppDialogSet().get())->getResponseTimeMs();
      }
   }

   ConversationManager::onConnected(h, msg);
}

void ReconConversationManagerImpl::onConnected(resip::InviteSessionHandle h, const resip::SipMessage& msg)
{
   ConversationManager::onConnected(h, msg);

   recon::RemoteParticipant* remotePart = dynamic_cast<recon::RemoteParticipant*>(h->getAppDialog().get());
   if (remotePart)
   {
      recon::ParticipantHandle partHandle = remotePart->getParticipantHandle();
      ConversationStateChangedEvent args;
      args.conversationState = ConversationState_Connected;
      args.dialogId = SipHelpers::getDialogId(msg);
      args.alertInfoHeader = SipHelpers::getFirstAlertInfoOrCallInfo(msg);

      SipHelpers::populateSessionId(msg, args.sessionId);
      SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(partHandle);
      SipCallCreationInfo* ci = getCreationInfo(convHandle);
      if (ci != NULL)
      {
         ci->currentConversationState = args.conversationState;
         args.answerMode = ci->answerMode;
      }
      mCallImpl.fireConvStateChangedEvent(convHandle, args);

      this->populateLocalRemoteConversationValues(convHandle);
   }
}

void ReconConversationManagerImpl::onInfo(resip::InviteSessionHandle h, const resip::SipMessage& msg)
{
   if (msg.exists(resip::h_Event))
   {
      const Data& event = msg.header(resip::h_Event).value();
      InfoLog(<< "Received INFO message with Event header (event: " << event << ")");
      h->acceptNIT();

      recon::RemoteParticipant* remotePart = dynamic_cast<recon::RemoteParticipant*>(h->getAppDialog().get());
      SipConversationHandle convHandle = 0;
      if (remotePart)
      {
         recon::ParticipantHandle partHandle = remotePart->getParticipantHandle();
         convHandle = findSipConversationHandleByParticipantHandle(partHandle);
         if (convHandle == 0xFFFFFFFF)
         {
            WarningLog(<< "Could not find SipConversationHandle for recon ParticipantHandle " << partHandle);
            return;
         }
      }
      
      if (resip::isEqualNoCase(event, BroadsoftCallControlMonitor::TalkEvent))
      {
         InfoLog(<< "Recognized Broadsoft talk event, sending to handler");
         BroadsoftTalkEvent args;
         mCallImpl.fireEvent(cpcEvent(SipConversationHandler, onIncomingBroadsoftTalkRequest), convHandle, args);
      }
      else if (resip::isEqualNoCase(event, BroadsoftCallControlMonitor::HoldEvent))
      {
         InfoLog(<< "Recognized Broadsoft hold event, sending to handler");
         BroadsoftHoldEvent args;
         mCallImpl.fireEvent(cpcEvent(SipConversationHandler, onIncomingBroadsoftHoldRequest), convHandle, args);
      }
   }
   else if (msg.exists(resip::h_ContentType) &&
      resip::isEqualNoCase(msg.header(resip::h_ContentType).type(), "text"))
   {
      h->acceptNIT();
      InfoLog(<< "Received text in an INFO request body");
   }
   else if (msg.exists(resip::h_ContentType) && resip::isEqualNoCase(msg.header(resip::h_ContentType).type(), "application") &&
      resip::isEqualNoCase(msg.header(resip::h_ContentType).subType(), "media_control+xml"))
   {
      recon::RemoteParticipant* remotePart = dynamic_cast<recon::RemoteParticipant*>(h->getAppDialog().get());
      if (remotePart)
      {
         recon::ParticipantHandle partHandle = remotePart->getParticipantHandle();
         SipConversationHandle sipConvHandle = findSipConversationHandleByParticipantHandle(partHandle);
         if (sipConvHandle == 0xFFFFFFFF)
         {
            WarningLog(<< "Could not find SipConversationHandle for recon ParticipantHandle " << partHandle);
            return;
         }

         SipCallCreationInfo* ci = getCreationInfo(sipConvHandle);
         if (ci)
         {
            std::vector<std::weak_ptr<webrtc_recon::RtpStreamImpl> >::const_iterator itRtpStreams = ci->rtpStreams.begin();
            for (; itRtpStreams != ci->rtpStreams.end(); ++itRtpStreams)
            {
               std::shared_ptr<webrtc_recon::RtpStreamImpl> rtpStream = itRtpStreams->lock();
               if (rtpStream)
               {
                  if (rtpStream->mediaType() == recon::MediaStack::MediaType_Video)
                  {
                     InfoLog(<< "INFO request triggered a send key video frame");
                     rtpStream->sendKeyVideoFrame();
                  }
               }
            }
         }
         else
         {
            WarningLog(<< "Missing SipCallCreationInfo for SipConversationHandle " << sipConvHandle);
         }
      }

      h->acceptNIT();
      InfoLog(<< "Accepting a media_control+xml INFO");
   }
   else if (msg.exists(resip::h_ContentType) && resip::isEqualNoCase(msg.header(resip::h_ContentType).type(), "application") &&
      resip::isEqualNoCase(msg.header(resip::h_ContentType).subType(), "dtmf-relay"))
   {
      // Don't do anything with this, but just accept it, because the user doesn't really want
      // to hear DTMF digits in any case (but we shouldn't reject it if it's happening).
      h->acceptNIT();
   }
   else if (msg.exists(resip::h_ContentType) && resip::isEqualNoCase(msg.header(resip::h_ContentType).type(), "message") &&
      resip::isEqualNoCase(msg.header(resip::h_ContentType).subType(), "sipfrag"))
   {
      // This is a special case related to handling requests from hard phones, calls that are made from land lines. 
      // Not really defined in RFCs, but not prohibited either. Requested by customer. See OBELISK-3627. 

      SipFrag* frag = dynamic_cast<SipFrag*>(msg.getContents());
      if (frag && frag->message().exists(h_To))
      {
         recon::RemoteParticipant* remotePart = dynamic_cast<recon::RemoteParticipant*>(h->getAppDialog().get());
         if (remotePart)
         {
            recon::ParticipantHandle partHandle = remotePart->getParticipantHandle();
            resip::Data newName = frag->message().header(h_To).getHeaderField().getBuffer();
            ConversationStateChangedEvent args;
            SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(partHandle);
            SipCallCreationInfo* ci = getCreationInfo(convHandle);

            if (ci != NULL)
            {
               args.dialogId = SipHelpers::getDialogId(msg);
               args.conversationState = ci->currentConversationState;
               args.remoteDisplayName = newName.c_str();
               args.answerMode = ci->answerMode;
               mCallImpl.fireConvStateChangedEvent(convHandle, args);
            }
         }
      }

      h->acceptNIT();
      InfoLog(<< "Accepting a message/sipfrag INFO");
   }
   else
   {
      /* 
      @fixed bugz 84575.
      According to the RFC 2976, A 200 OK response MUST be sent by a UAS for an INFO request 
      with no message body if the INFO request was successfully received for an existing call.
      */
      if( msg.header(resip::h_ContentLength).value() == 0)
      {
         h->acceptNIT();
         InfoLog(<< "Response 200 for INFO request without body");
      }
      else
         // with message body for which there is not processing rule, 
         // then 415 (Unsuported Media type) is responsed
      {
         h->rejectNIT(415);
         WarningLog(<< "Unsupported MIME type for INFO request body: " << msg.header(resip::h_ContentType) << ", send 415");
      }
   }
}

void ReconConversationManagerImpl::onInfoFailure(resip::InviteSessionHandle h, const resip::SipMessage& msg)
{
   /* !ds! For now the only INFO messages we send are for DTMF. But if we ever send another type we'll have
      to discriminate them here */

   /* TODO: figure out how to get the original SIP INFO message here that caused the error */

/*
   // Check to ensure that this is a DTMF digit
   unsigned int toneId = 0;
   unsigned long toneDuration = 0;

   if( SipDTMFRelayUtils::ParseDTMFInfo( *msg.getContents(), toneId, toneDuration ))
   {
*/
      recon::RemoteParticipant* remotePart = dynamic_cast<recon::RemoteParticipant*>(h->getAppDialog().get());
      if( remotePart )
      {
         recon::ParticipantHandle partHandle = remotePart->getParticipantHandle();
         SipConversationHandle sipConvHandle = findSipConversationHandleByParticipantHandle( partHandle );
         if( sipConvHandle == 0xFFFFFFFF )
         {
            WarningLog(<< "Could not find SipConversationHandle for recon ParticipantHandle " << partHandle);
            return;
         }

         SipCallCreationInfo* ci = getCreationInfo( sipConvHandle );
         if( ci )
            ci->sipDTMFFailure = true;
      }
/*
   }
*/
}

void ReconConversationManagerImpl::onTerminated(resip::InviteSessionHandle h, resip::InviteSessionHandler::TerminatedReason reason, const resip::SipMessage* msg)
{
   recon::RemoteParticipantDialogSet* remotePartDialogSet = dynamic_cast<recon::RemoteParticipantDialogSet*>(h->getAppDialogSet().get());
   if (NULL != remotePartDialogSet)
   {
      SipCallCreationInfo* ci = getCreationInfo(findSipConversationHandleByParticipantHandle(remotePartDialogSet->getActiveRemoteParticipantHandle()));
      if (ci != NULL)
      {
         ci->responseTimeMs = dynamic_cast<resip::AppDialogSet*>(h->getAppDialogSet().get())->getResponseTimeMs();
      }
   }

   ConversationManager::onTerminated(h, reason, msg);

   recon::RemoteParticipant* remotePart = dynamic_cast<recon::RemoteParticipant*>(h->getAppDialog().get());
   if (NULL != remotePart)
   {
      recon::ParticipantHandle partHandle = remotePart->getParticipantHandle();
      // std::cout << "***** onTerminated(" << StringConv::wstringToUtf8(mAccount.getSettings().username) << ") part=" << partHandle << std::endl;
      finalizeConversation(partHandle, reason, msg);

      recon::RemoteParticipantDialogSet* remotePartDialogSet = dynamic_cast<recon::RemoteParticipantDialogSet*>(h->getAppDialogSet().get());
      if (NULL != remotePartDialogSet)
      {
         if (remotePartDialogSet->isUACRedirected())
         {
            if (findSipConversationHandleByParticipantHandle(partHandle) == 0xffffffff)
            {
               SipCallCreationInfo* ci = getCreationInfo(findSipConversationHandleByParticipantHandle(remotePartDialogSet->getOriginalRedirectedParticipantHandle()));
               if (ci != NULL)
               {
                  ci->redirectedAddrs.clear();
                  finalizeConversation(remotePartDialogSet->getOriginalRedirectedParticipantHandle(), reason, msg);
               }
            }
         }
      }
   }
   if (msg != NULL)
   {
      mAccount.handle5xx(*msg);
   }
}

void 
ReconConversationManagerImpl::onProvisional(resip::ClientInviteSessionHandle h, const resip::SipMessage& msg)
{
   recon::RemoteParticipantDialogSet* remotePartDialogSet = dynamic_cast<recon::RemoteParticipantDialogSet*>(h->getAppDialogSet().get());
   if (NULL != remotePartDialogSet)
   {
      SipCallCreationInfo* ci = getCreationInfo(findSipConversationHandleByParticipantHandle(remotePartDialogSet->getActiveRemoteParticipantHandle()));
      if (ci != NULL)
      {
         ci->responseTimeMs = dynamic_cast<resip::AppDialogSet*>(h->getAppDialogSet().get())->getResponseTimeMs();
      }
   }
   ConversationManager::onProvisional(h, msg);
}

void
ReconConversationManagerImpl::onReadyToSendInvite(recon::ParticipantHandle partHandle, resip::SipMessage& msg)
{
   /*
   resip::Data transportParam;
   switch (mTransport)
   {
   case TProxyServerSettings::ETransport_TCP:
      transportParam = "tcp";
      break;
   case TProxyServerSettings::ETransport_UDP:
      transportParam = "udp";
      break;
   case TProxyServerSettings::ETransport_TLS:
      transportParam = "tls";
      break;
   case TProxyServerSettings::ETransport_Automatic:
      {
         // !jjg! disabling this for now, since there are a plethora of issues:
         // 1) some domains don't accept TCP at all, and we have no fallback mechanism
         // 2) some domains accept TCP, but will still route the request over UDP if the target endpoint
         //    is REGISTERed with UDP

         //// .jjg. if we have ICE enabled and are using both audio and video,
         //// then odds are pretty good that we are going to be beyond our MTU;
         //// force TCP if we are using ICE
         //if (mAccount->GetAccountSettings()->m_tFirewallNatSettings.m_eNatTraversalMode == TFirewallNatSettings::NatTraversalMode_ICE)
         //{
         //   transportParam = "tcp";
         //}
      }
      break;
   default:
      break;
   }

   if (transportParam.size() > 0)
   {
      resip::Uri& targetUri = msg.header(h_RequestLine).uri();
      targetUri.param(p_transport) = transportParam;
      if (msg.exists(h_Contacts))
      {
         msg.header(h_Contacts).front().uri().param(p_transport) = transportParam;
      }
   }

   if (ISuaOutgoingCallSinkPtr callSink = mMapParticipantToOutgoingCallSink[partHandle])
   {
      callSink->OnReadyToSendInvite(partHandle, msg);
   }
   */

   adornMessage(partHandle, msg);
}

/*
 * Handle responses to Out-of-Dialog requests
 */
bool 
ReconConversationManagerImpl::onSuccess(resip::ClientOutOfDialogReqHandle req, const resip::SipMessage& response)
{
   return mAccount.onSuccess(req, response);
}

bool 
ReconConversationManagerImpl::onFailure(resip::ClientOutOfDialogReqHandle req, const resip::SipMessage& response)
{
   return mAccount.onFailure(req, response);
}

////////////////////////////////////////////////////////////////////////////////
// RedirectHandler /////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////

void 
ReconConversationManagerImpl::onRedirectReceived(AppDialogSetHandle h, const SipMessage& msg)
{
   /*
   InfoLog(<< "onRedirectReceived(AppDialogSetHandle): " << msg.brief());
   RemoteParticipantDialogSet *remoteParticipantDialogSet = dynamic_cast<RemoteParticipantDialogSet *>(h.get());
   if(remoteParticipantDialogSet)
   {
      if(remoteParticipantDialogSet->getNumDialogs() == 0)
      {
         remoteParticipantDialogSet->onRedirectReceived(h,msg);
      }
   }
   //else if (msg.header(h_CSeq).method() == SUBSCRIBE)
   //{
   //   // .jjg. prevent us from SUBSCRIBing to the same AOR
   //   // twice if we are redirected
   //   if (msg.header(h_Contacts).size() > 0)
   //   {
   //      NameAddr topContact = msg.header(h_Contacts).front();
   //      FindClientSubscriptionFunctor findFunc(topContact.uri().getAor());
   //      mDum->applyToAllClientSubscriptions(&findFunc);
   //      if (findFunc.wasFound())
   //      {
   //         mPreventRedirectsForTheseAppDialogSets.insert(h.get());
   //      }
   //   }
   //}
   */

   if (RemoteParticipantDialogSet *remoteParticipantDialogSet = dynamic_cast<RemoteParticipantDialogSet *>(h.get()))
   {
      if (remoteParticipantDialogSet->getNumDialogs() == 0)
      {
         remoteParticipantDialogSet->onRedirectReceived(h,msg);
      }
   }
}

bool 
ReconConversationManagerImpl::onTryingNextTarget(AppDialogSetHandle h, const SipMessage& msg)
{
   //InfoLog(<< "onTryingNextTarget(AppDialogSetHandle): " << msg.brief());
   bool allowTryNextTarget = true;
   /*
   if (mPreventRedirectsForTheseAppDialogSets.count(h.get()) > 0)
   {
      allowTryNextTarget = false;
      mPreventRedirectsForTheseAppDialogSets.erase(h.get());
   }
   CClientSubscriptionAppDialogSetWrapper* ads = dynamic_cast<CClientSubscriptionAppDialogSetWrapper*>(h.get());
   if (ads)
   {
      if (msg.isRequest() && msg.header(h_RequestLine).method() == resip::SUBSCRIBE)
      {
         ads->targetUri() = msg.header(h_RequestLine).uri();
      }
   }
   */
   // Always allow redirection otherwise
   return allowTryNextTarget;
}

bool
ReconConversationManagerImpl::isOutOfDialogReferSupported() const
{
   return false;
   //return mAccount->GetAccountSettings()->m_tProxyServerSettings.m_bAllowOodRefer;
}

void ReconConversationManagerImpl::onReadyToSend(resip::ClientSubscriptionHandle h, resip::SipMessage& msg)
{
   if (getConvSettings().adornTransferMessages)
   {
      recon::RemoteParticipant* remotePart = dynamic_cast<recon::RemoteParticipant*>(h->getAppDialog().get());
      if (remotePart)
      {
         recon::ParticipantHandle partHandle = remotePart->getParticipantHandle();
         adornMessage(partHandle, msg);
      }
   }
}

void ReconConversationManagerImpl::onReadyToSend(resip::ServerSubscriptionHandle h, resip::SipMessage& msg)
{
   if (getConvSettings().adornTransferMessages)
   {
      recon::RemoteParticipant* remotePart = dynamic_cast<recon::RemoteParticipant*>(h->getAppDialog().get());
      if (remotePart)
      {
         recon::ParticipantHandle partHandle = remotePart->getParticipantHandle();
         adornMessage(partHandle, msg);
      }
   }
}

void ReconConversationManagerImpl::onReadyToSend(resip::InviteSessionHandle h, resip::SipMessage& msg)
{
   recon::RemoteParticipant* remotePart = dynamic_cast<recon::RemoteParticipant*>(h->getAppDialog().get());
   if (remotePart)
   {
      recon::ParticipantHandle partHandle = remotePart->getParticipantHandle();
      adornMessage(partHandle, msg);
   }

   //Connected Line Identification Presentation (COLP)
   if (msg.isResponse() && msg.header(h_StatusLine).responseCode() == 180)
   {
      if (msg.exists(h_To))
         msg.header(h_To).displayName() = mAcctSettings.displayName;
   }

   if (msg.isResponse() && msg.header(h_StatusLine).responseCode() == 200 && msg.header(h_CSeq).method() == INVITE)
   {
      if (mDefaultConvProfile->includePAssertedIdentity())
      {
         bool idExists = false;
         for (ParserContainer<NameAddr>::iterator i = msg.header(h_PAssertedIdentities).begin(); i != msg.header(h_PAssertedIdentities).end(); ++i)
         {
            if (*i == mDefaultConvProfile->getDefaultFrom())
            {
               idExists = true;
               break;
            }
         }
         if (!idExists || msg.header(h_PAssertedIdentities).size() > 1)
         {
            msg.header(h_PAssertedIdentities).clear();
            msg.header(h_PAssertedIdentities).push_back(mDefaultConvProfile->getDefaultFrom());
         }
      }
   }
}

void ReconConversationManagerImpl::addAdornmentHandler(SipConversationAdornmentInternalHandler* handler)
{
   mAdornmentHandler = handler;
}

void ReconConversationManagerImpl::adornMessage(recon::ParticipantHandle partHandle, resip::SipMessage& msg) const
{   
   SipConversationHandle convHandle = findSipConversationHandleByParticipantHandle(partHandle);
   SipCallCreationInfo* ci = getCreationInfo(convHandle);

   if (!ci) return;

   for (cpc::vector<CPCAPI2::SipHeader>::iterator it = ci->customHeaders.begin(); it != ci->customHeaders.end(); ++it)
   {
      SipHelpers::setHeader(msg, it->header, it->value);
   }

   if (!mAdornmentHandler) return;

   ConversationAdornmentInternalEvent args(msg);

   if (msg.isRequest())
   {
      args.method = msg.methodStr().c_str();
      args.target = msg.header(h_To).uri().getAorNoReally().c_str();
      args.responseCode = 0;
   }

   if (msg.isResponse())
   {
      args.responseCode = msg.header(h_StatusLine).responseCode();
   }
   
   args.cseqMethod = resip::getMethodName(msg.header(h_CSeq).method()).c_str();

   std::ostringstream ss;
   msg.encode(ss);
   args.message = ss.str().c_str();

   if (ci->originalConversation != 0)
   {
      convHandle = ci->originalConversation;
   }

   static unsigned int adornmentMessageId = 1;
   args.account = mAccount.getHandle();
   args.adornmentMessageId = adornmentMessageId++;
   args.conversation = convHandle;

   assert(mAdornmentHandler);

   SipAccountHandle account = mAccount.getHandle();

   mAdornmentHandler->onConversationAdornment(account, args);

   for (cpc::vector<CPCAPI2::SipHeader>::iterator it = args.customHeaders.begin(); it != args.customHeaders.end(); ++it)
   {
      SipHelpers::setHeader(msg, it->header, it->value);
   }
}

const SipConversationSettings ReconConversationManagerImpl::getConvSettings() const
{
   SipConversationSettings ret = mConvSettings.find(CPCAPI2::TransportNone)->second;
   NetworkTransport currentTransport = NetworkChangeManager::getInterface(mAccount.getPhone())->networkTransport();
   std::map<NetworkTransport, SipConversationSettings>::const_iterator it = mConvSettings.find(currentTransport);
   if (it != mConvSettings.end())
   {
      ret = it->second;
   }
   StackLog(<< "ReconConversationManagerImpl::getConvSettings(): NAT Traversal Mode: " << ret.natTraversalMode);
   return ret;
}

int ReconConversationManagerImpl::setReconConvManSettings(const SipConversationSettings& settings, CPCAPI2::NetworkTransport transport)
{
   mConvSettings[transport] = settings;
   StackLog(<< "ReconConversationManagerImpl::setReconConvManSettings(): NAT Traversal Mode: " << settings.natTraversalMode);
   return kSuccess;
}

#define ARRAY_LENGTH(var)   (sizeof(var)/sizeof(var[0]))

void ReconConversationManagerImpl::populateLocalRemoteConversationValues(SipConversationHandle convHandle)
{
   struct webrtc::RTCPLocalRemoteIDs localRemoteIDs;
   struct webrtc::RTCPLocalRemoteGroup localRemoteGroups;

   // use -1 in order to allow for null terminator on truncation
   strncpy(localRemoteIDs.LocalID, cpc::string("sip:") +
      this->mAcctSettings.username + cpc::string("@") + this->mAcctSettings.domain,
      ARRAY_LENGTH(localRemoteIDs.LocalID)-1);
   strncpy(localRemoteGroups.LocalGroup, this->mAcctSettings.localGroup,
      ARRAY_LENGTH(localRemoteGroups.LocalGroup)-1);
   // there is currently no way to get this, but I'm told it must be an empty string otherwise
   strncpy(localRemoteGroups.RemoteGroup, "", ARRAY_LENGTH(localRemoteGroups.RemoteGroup)-1);

   SipCallCreationInfo* ci = getCreationInfo(convHandle);
   if (ci != NULL && (!ci->fromAddress.empty() || ci->targetAddresses.size() > 0))
   {
      if (!ci->fromAddress.empty())
      {
         // incoming call
         
         assert(ci->targetAddresses.size() == 0 ||
            // DRL the "BasicCallTests.BasicCallReInviteHold" unit test results in this scenario which I am not sure is correct
            (ci->targetAddresses.size() == 1 && CharEncodingHelper::unEscape(ci->targetAddresses[0].uri()) == ci->fromAddress));
         
         // use -1 in order to allow for null terminator on truncation
         strncpy(localRemoteIDs.RemoteID, ci->fromAddress.c_str(), ARRAY_LENGTH(localRemoteIDs.RemoteID)-1);
         strncpy(localRemoteIDs.OrigID, ci->fromAddress.c_str(), ARRAY_LENGTH(localRemoteIDs.OrigID)-1);
      }
      else if (ci->targetAddresses.size() > 0)
      {
         // outgoing call
         
         cpc::string addr = CharEncodingHelper::unEscape(ci->targetAddresses[0].uri()).c_str();
         
         // use -1 in order to allow for null terminator on truncation
         strncpy(localRemoteIDs.RemoteID, addr.c_str(), ARRAY_LENGTH(localRemoteIDs.RemoteID)-1);
         strncpy(localRemoteIDs.OrigID, localRemoteIDs.LocalID, ARRAY_LENGTH(localRemoteIDs.OrigID)-1);
      }
      else
         assert(0);

      std::vector<std::weak_ptr<webrtc_recon::RtpStreamImpl> >::const_iterator itRtpStreams = ci->rtpStreams.begin();
      for (; itRtpStreams != ci->rtpStreams.end(); ++itRtpStreams)
      {
         std::shared_ptr<webrtc_recon::RtpStreamImpl> rtpStream = itRtpStreams->lock();
         if (rtpStream)
         {
            rtpStream->setLocalRemoteIDs(localRemoteIDs);
            rtpStream->setLocalRemoteGroup(localRemoteGroups);
         }
      }
   }
}

void ReconConversationManagerImpl::clearStatistics(AudioStatistics* stats)
{
   memset(&(stats->encoder), 0, sizeof(stats->encoder));
   memset(&(stats->decoder), 0, sizeof(stats->decoder));
   memset(&(stats->streamStatistics), 0, sizeof(stats->streamStatistics));
   memset(&(stats->streamDataCounters), 0, sizeof(stats->streamDataCounters));
   stats->maxJitterMs = 0;
   stats->averageJitterMs = 0;
   stats->discardedPackets = 0;
   memset(&(stats->XRvoipMetrics), 0, sizeof(stats->XRvoipMetrics));
   memset(&(stats->XRstatisticsSummary), 0, sizeof(stats->XRstatisticsSummary));
   stats->callStartTimeNTP = 0;
   stats->endpoint.ipAddress = "";
   stats->endpoint.port = 0;
}

void ReconConversationManagerImpl::clearStatistics(RemoteAudioStatistics* stats)
{
   stats->sender_SSRC = 0;
   stats->source_SSRC = 0;
   memset(&(stats->streamStatistics), 0, sizeof(stats->streamStatistics));
   memset(&(stats->XRvoipMetrics), 0, sizeof(stats->XRvoipMetrics));
   memset(&(stats->XRstatisticsSummary), 0, sizeof(stats->XRstatisticsSummary));
   stats->endpoint.ipAddress = "";
   stats->endpoint.port = 0;
   stats->lastRtcpReceived = 0;
   stats->lastRtcpXrReceived = 0;
   stats->lastSenderReportReceived = 0;
}

void ReconConversationManagerImpl::clearStatistics(VideoStatistics* stats)
{
   memset(&(stats->encoder), 0, sizeof(stats->encoder));
   memset(&(stats->decoder), 0, sizeof(stats->decoder));
   memset(&(stats->streamStatistics), 0, sizeof(stats->streamStatistics));
   memset(&(stats->streamDataCounters), 0, sizeof(stats->streamDataCounters));
   stats->totalBitrateSent = 0;
   stats->videoBitrateSent = 0;
   stats->fecBitrateSent = 0;
   stats->nackBitrateSent = 0;
   stats->discardedPackets = 0;
   stats->currentTargetBitrate = 0;
   stats->endpoint.ipAddress = "";
   stats->endpoint.port = 0;
}

void ReconConversationManagerImpl::clearStatistics(RemoteVideoStatistics* stats)
{
   memset(&(stats->streamStatistics), 0, sizeof(stats->streamStatistics));
   stats->endpoint.ipAddress = "";
   stats->endpoint.port = 0;
   stats->lastRtcpReceived = 0;
   stats->lastSenderReportReceived = 0;
}

int ReconConversationManagerImpl::startMonitoringAudioLevels(SipConversationHandle convHandle)
{
   SipCallCreationInfo* ci = getCreationInfo(convHandle);
   if (ci == NULL)
   {
      return kError;
   }

   if (mAudioLevelMonitor == NULL)
   {
      InfoLog(<< "Starting AudioLevelMonitor");
      webrtc_recon::MediaStackImpl* ms = mCallImpl.media_stack();

      CPCAPI2::Media::AudioInterface* audio = NULL;
      CPCAPI2::Media::MediaManager* media = dynamic_cast<CPCAPI2::Media::MediaManager*>(CPCAPI2::Media::MediaManager::getInterface(mAccount.getPhone()));
      if (media != NULL)
      {
        audio = dynamic_cast<CPCAPI2::Media::AudioInterface*>(CPCAPI2::Media::Audio::getInterface(media));
      }

      mAudioLevelMonitor = new CPCAPI2::Media::AudioLevelMonitor(&mAccount.getPhone()->getSdkModuleThread(), ms->volume_control());
      mAudioLevelMonitor->start(this, audio);
      mAudioLevelMonitor->startInputLevelMonitoringImpl(-1); // monitor active device
   }

   std::vector<std::weak_ptr<webrtc_recon::RtpStreamImpl> >::const_iterator iter;
   for (iter = ci->rtpStreams.begin() ; iter != ci->rtpStreams.end() ; ++iter)
   {
      if (std::shared_ptr<webrtc_recon::RtpStreamImpl> pStream = iter->lock())
      {
         // ensure there is a valid channel to monitor. If not, will try later in onParticipantMediaChanged
         if (pStream->mediaType() == recon::MediaStack::MediaType_Audio && pStream->channel() >= 0)
         {
            mAudioLevelMonitor->startOutputLevelMonitoringForChannelImpl(pStream->channel());
         }
      }
   }

   return kSuccess;
}

int ReconConversationManagerImpl::stopMonitoringAudioLevels(SipConversationHandle convHandle)
{
   SipCallCreationInfo* ci = getCreationInfo(convHandle);
   if (ci == NULL)
   {
      return kError;
   }

   if (mAudioLevelMonitor == NULL)
   {
      return kError;
   }

   std::vector<std::weak_ptr<webrtc_recon::RtpStreamImpl> >::const_iterator iter;
   for (iter = ci->rtpStreams.begin() ; iter != ci->rtpStreams.end() ; ++iter)
   {
      if (std::shared_ptr<webrtc_recon::RtpStreamImpl> pStream = iter->lock())
      {
         if (pStream->mediaType() == recon::MediaStack::MediaType_Audio)
         {
            mAudioLevelMonitor->stopOutputLevelMonitoringForChannelImpl(pStream->channel());
         }
      }
   }

   if (!mAudioLevelMonitor->isMonitoringOutput())
   {
      mAudioLevelMonitor->stopInputLevelMonitoringImpl();
      mAudioLevelMonitor->destroy();
      mAudioLevelMonitor = NULL;
   }

   return kSuccess;
}

void ReconConversationManagerImpl::onAudioLevels(int voe_channel, unsigned int inputLevel, unsigned int outputLevel)
{
   SipConversationHandle convHandle = 0;
   SipCallCreationInfo* ci = getCreationInfoForMediaStream(voe_channel, convHandle);
   if (ci != NULL)
   {
      if (ci->numNonZeroAudioInputLevelsSampled < 0)
      {
         ci->numNonZeroAudioInputLevelsSampled = 0;
      }
      if (inputLevel > 0)
      {
         ci->numNonZeroAudioInputLevelsSampled++;
      }

      ConversationAudioDeviceLevelChangeEvent args;
      args.inputDeviceLevel = inputLevel;
      args.outputDeviceLevel = outputLevel;
      mCallImpl.fireEvent(cpcEvent(SipConversationHandler, onAudioDeviceLevelChange), convHandle, args);
   }
}

bool ReconConversationManagerImpl::hasCallWithCallId(const cpc::string& callId) const
{
   SipCallCreationInfoMap::const_iterator it = mCallCreationInfo.begin();
   for (; it != mCallCreationInfo.end(); ++it)
   {
      if (callId == it->second->callId)
      {
         return true;
      }
   }
   return false;
}

int ReconConversationManagerImpl::getRemoteParticipantCount(SipConversationHandle conversation)
{
   int count = 0;
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      Conversation* conv = getConversation(ci->reconConversation);
      if (conv != NULL)
      {
         count += conv->getNumRemoteParticipants();
      }
   }
   return count;
}


}
}

#endif // CPCAPI2_BRAND_CALL_MODULE
