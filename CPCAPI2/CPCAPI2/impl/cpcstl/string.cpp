#include <cpcstl/string.h>

#include <stdlib.h>
#include <vector>
#include <locale>
#include <sstream>

#if (_MSC_VER >= 1700)
#define CPCAPI2_USE_BUILT_IN_STRINGCONV
#elif defined(__linux__) || defined(BB10) || defined(__THREADX)
#define CPCAPI2_USE_UTF8CPP_STRINGCONV
#endif

#if defined(_WIN32) || defined(__APPLE__)
#include <codecvt>
#endif

#ifdef CPCAPI2_USE_UTF8CPP_STRINGCONV
#include "../../../contrib/utfcpp/utf8.h"
#endif

#ifdef __linux__
#include <string.h> // memcpy includes
#endif

#ifdef __APPLE__
#include <TargetConditionals.h>
#endif

namespace cpc {

#ifdef CPCAPI2_USE_BUILT_IN_STRINGCONV
std::wstring utf8ToWstring(const char* inCh)
{
#if defined (__APPLE__) && !defined(TARGET_OS_IPHONE)
   std::wstring_convert<std::codecvt_utf8<wchar_t>, wchar_t> convert;
#else
   std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>, wchar_t> convert;
#endif
   try
   {
      return convert.from_bytes(inCh);
   }
   catch(...)
   {
      return L"";
   }
}

#elif defined(CPCAPI2_USE_UTF8CPP_STRINGCONV)
std::wstring utf8ToWstring(const char* inCh)
{
   const std::string in(inCh);
   std::vector<wchar_t> convertedMessage;
   if (sizeof(wchar_t) == 2)        // For platforms where wchar_t is 16 bits, Windows XP e.g.
   {
      utf8::utf8to16(in.begin(), in.end(), back_inserter(convertedMessage));
   }
   else if (sizeof(wchar_t) == 4)   // For platforms where wchar_t is 32 bits, MAC OS X e.g.
   {
      utf8::utf8to32(in.begin(), in.end(), back_inserter(convertedMessage));
   }
   else                             // A platform with another size for wchar_t, not 16 or 32 bits?
   {
      //assert(false);
   }
   return std::wstring(convertedMessage.begin(), convertedMessage.end());
}
#endif

void* string::toWstring(const char* in, const wchar_t** out, size_t* outSize) const
{
   std::wstring* wstr = NULL;
   if (in[0] != '\0')
   {
      wstr = new std::wstring(utf8ToWstring(in));
   }
   else
   {
      wstr = new std::wstring();
   }

   *out = wstr->c_str();
   *outSize = wstr->size();
   return (void*)wstr;
}

void string::freeWstring(void* h) const
{
   std::wstring* wstrHandle = (std::wstring*)h;
   delete wstrHandle;
}

string::~string() {
	if (m_first != m_buffer)
		TINYSTL_ALLOCATOR::static_deallocate(m_first, m_capacity - m_first);
}

void string::reserve(size_t capacity) {
	if (m_first + capacity + 1 <= m_capacity)
		return;

	const size_t size = (size_t)(m_last - m_first);

	pointer newfirst = (pointer)TINYSTL_ALLOCATOR::static_allocate(capacity + 1);
	for (pointer it = m_first, newit = newfirst, end = m_last; it != end; ++it, ++newit)
		*newit = *it;
	if (m_first != m_buffer)
		TINYSTL_ALLOCATOR::static_deallocate(m_first, m_capacity - m_first);

	m_first = newfirst;
	m_last = newfirst + size;
	m_capacity = m_first + capacity;
}

string string::substr(size_t start, size_t len) const {
   if (len == npos || (start + len > size())) {
      len = size() - start;
   }
   return string(c_str()+start, len);
}

string::operator const char*() const {
   return c_str();
}

string to_string(unsigned int val) {
   std::stringstream ss;
   ss << val;
   return ss.str().c_str();
}

string to_string(int val) {
   std::stringstream ss;
   ss << val;
   return ss.str().c_str();
}

string to_string(float val) {
   std::stringstream ss;
   ss << val;
   return ss.str().c_str();
}

string to_string(long val) {
   std::stringstream ss;
   ss << val;
   return ss.str().c_str();
}

#if LONG_MAX != LLONG_MAX || !defined(__linux__)
string to_string(int64_t val) {
   std::stringstream ss;
   ss << val;
   return ss.str().c_str();
}
#endif

float to_float(const string& str)
{
   return (float)strtod(str.c_str(), NULL);
}

int to_int(const string& str)
{
   return strtol(str.c_str(), NULL, 10);
}

}
