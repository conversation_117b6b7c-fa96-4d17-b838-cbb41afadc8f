#include <nlohmann/json.hpp>
#include "LogLevel.h"

#ifndef JSONRPC_CPCAPI2_PHONE_PHONESETLOGLEVEL_H
#define JSONRPC_CPCAPI2_PHONE_PHONESETLOGLEVEL_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  class PhoneSetLogLevel
  {
  public:
    int32_t handle;
    LogLevel level;

    PhoneSetLogLevel() = default;

    PhoneSetLogLevel(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const PhoneSetLogLevel& o)
    {
      j = nlohmann::json::object();
       j["handle"] = o.handle;
     j["level"] = o.level;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, PhoneSetLogLevel& o)
    {
     o.handle = j.at("handle");
     o.level = j.at("level");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_PHONESETLOGLEVEL_H
