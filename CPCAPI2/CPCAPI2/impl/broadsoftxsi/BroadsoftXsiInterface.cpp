#include <brand_branded.h>

#if (CPCAPI2_BRAND_BROADSOFT_XSI_MODULE == 1)

#include <mutex>
#include <condition_variable>

#include <assert.h>

#include "BroadsoftXsiInterface.h"
#include "BroadsoftXsiImpl.h"
#include "phone/PhoneInterface.h"

using namespace CPCAPI2::BroadsoftXsi;

#define INVALID_HANDLE_STR  "Invalid Handle"
std::atomic< BroadsoftXsiHandle > BroadsoftXsiInterface::s_CurrentHandle( 1 );

BroadsoftXsiInterface::BroadsoftXsiInterface(CPCAPI2::Phone* phone)
   : m_IOService(dynamic_cast<PhoneInterface*>(phone)->getAsioIoService()), m_Shutdown( false ), m_CbHook( NULL ), m_Context( NULL )
{
}

BroadsoftXsiInterface::~BroadsoftXsiInterface()
{
   m_Shutdown = true;
}

// PhoneModule implementation
void BroadsoftXsiInterface::Release()
{
   // Release is actually called in the resip reactor thread already. So no need to
   // delegate into the IO service, do everything inline.
   m_Shutdown = true;
   std::map< BroadsoftXsiHandle, BroadsoftXsiInfo* >::iterator iter = m_InfoMap.begin();
   for( ; iter != m_InfoMap.end() ; ++iter )
   {
      BroadsoftXsiInfo *info( iter->second );
      if( info != NULL )
      {
         delete info->pImpl; info->pImpl = NULL;
      }
   }
   m_InfoMap.clear();

   delete this; // suicide
}

bool BroadsoftXsiInterface::getInfo( const BroadsoftXsiHandle& h, BroadsoftXsiInfo*& pOutInfo )
{
   bool result = false;

   std::map< BroadsoftXsiHandle, BroadsoftXsiInfo* >::iterator iter = m_InfoMap.find( h );
   if( iter == m_InfoMap.end() )
   {
      ErrorEvent evt;
      evt.errNo     = 0;
      evt.errorText = INVALID_HANDLE_STR;
      // TODO
      // info->acctImpl->fireEvent( cpcFunc( BroadsoftXsiHandler::onError ), evt ); // how ?
   }
   else
   {
      pOutInfo = iter->second;
      result = true;
   }

   return result;
}

// Broadsoft XSI Implementation
BroadsoftXsiHandle BroadsoftXsiInterface::create( void )
{
   BroadsoftXsiHandle h = s_CurrentHandle.fetch_add( 1 );
   m_IOService.post( std::bind( &BroadsoftXsiInterface::createImpl, this, h ));
   return h;
}

int BroadsoftXsiInterface::createImpl( const BroadsoftXsiHandle& h )
{
   BroadsoftXsiInfo *info = new BroadsoftXsiInfo;
   info->pImpl = new BroadsoftXsiImpl( h, m_IOService, m_CallbackFifo );
   m_InfoMap[ h ] = info;
   info->pImpl->setCallbackHook(m_CbHook, m_Context);
   return kSuccess;
}

int BroadsoftXsiInterface::configureSettings( const BroadsoftXsiHandle& h, const LocalSettings& settings )
{
   m_IOService.post( std::bind( &BroadsoftXsiInterface::configureSettingsImpl, this, h, settings ));
   return kSuccess;
}

int BroadsoftXsiInterface::configureSettingsImpl( const BroadsoftXsiHandle& h, const LocalSettings& settings )
{
   BroadsoftXsiInfo* info = NULL;
   if( !getInfo( h, info ))
      return kError;

   if( info->pImpl == NULL )
      return kError;

   info->localSettings = settings;

   return info->pImpl->configureConnectionSettings( info, settings );
}

int BroadsoftXsiInterface::applySettings( const BroadsoftXsiHandle& h )
{
   m_IOService.post( std::bind( &BroadsoftXsiInterface::applySettingsImpl, this, h ));
   return kSuccess;
}

int BroadsoftXsiInterface::applySettingsImpl( const BroadsoftXsiHandle& h )
{
   return kSuccess;
}

int BroadsoftXsiInterface::queryServices(const BroadsoftXsiHandle& h)
{
   m_IOService.post(std::bind(&BroadsoftXsiInterface::queryServicesImpl, this, h));
   return kSuccess;
}

int BroadsoftXsiInterface::queryServicesImpl(const BroadsoftXsiHandle& h)
{
   BroadsoftXsiInfo* info = NULL;
   if (!getInfo(h, info))
      return kError;

   return info->pImpl->queryServices();
}

int BroadsoftXsiInterface::queryCallLogs( const BroadsoftXsiHandle& h, const CallLogType& callLogType )
{
   m_IOService.post( std::bind( &BroadsoftXsiInterface::queryCallLogsImpl, this, h, callLogType ));
   return kSuccess;
}

int BroadsoftXsiInterface::queryCallLogsImpl( const BroadsoftXsiHandle& h, const CallLogType& callLogType )
{
   BroadsoftXsiInfo* info = NULL;
   if( !getInfo( h, info ))
      return kError;

   return info->pImpl->queryCallLogs( callLogType );
}

int BroadsoftXsiInterface::deleteCallLogs( const BroadsoftXsiHandle& h, const CallLogType& callLogType )
{
   m_IOService.post( std::bind( &BroadsoftXsiInterface::deleteCallLogsImpl, this, h, callLogType ));
   return kSuccess;
}

int BroadsoftXsiInterface::deleteCallLogsImpl( const BroadsoftXsiHandle& h, const CallLogType& callLogType )
{
   BroadsoftXsiInfo* info = NULL;
   if( !getInfo( h, info ))
      return kError;

   return info->pImpl->deleteCallLogs( callLogType );
}

int BroadsoftXsiInterface::deleteCallLog( const BroadsoftXsiHandle& h, const CallLogType& callLogType, const cpc::string& callLogId )
{
   m_IOService.post( std::bind( &BroadsoftXsiInterface::deleteCallLogImpl, this, h, callLogType, callLogId ));
   return kSuccess;
}

int BroadsoftXsiInterface::deleteCallLogImpl( const BroadsoftXsiHandle& h, const CallLogType& callLogType, const cpc::string& callLogId )
{
   BroadsoftXsiInfo* info = NULL;
   if( !getInfo( h, info ))
      return kError;

   return info->pImpl->deleteCallLog( callLogType, callLogId );
}

int BroadsoftXsiInterface::queryEnterpriseDirectory( const BroadsoftXsiHandle& h, const DirectorySearchParameters& searchParameters )
{
   m_IOService.post( std::bind( &BroadsoftXsiInterface::queryEnterpriseDirectoryImpl, this, h, searchParameters ));
   return kSuccess;
}

int BroadsoftXsiInterface::queryEnterpriseDirectoryImpl( const BroadsoftXsiHandle& h, const DirectorySearchParameters& searchParameters )
{
   BroadsoftXsiInfo* info = NULL;
   if( !getInfo( h, info ))
      return kError;

   return info->pImpl->queryEnterpriseDirectory( searchParameters );
}

int BroadsoftXsiInterface::queryServiceSettings( const BroadsoftXsiHandle& h, const ServiceType t )
{
   m_IOService.post( std::bind( &BroadsoftXsiInterface::queryServiceSettingsImpl, this, h, t ));
   return kSuccess;
}

int BroadsoftXsiInterface::queryServiceSettingsImpl( const BroadsoftXsiHandle& h, const ServiceType t )
{
   BroadsoftXsiInfo* info = NULL;
   if( !getInfo( h, info ))
      return kError;

   return info->pImpl->queryServiceSettings(t);
}

int BroadsoftXsiInterface::setBroadworksAnywhereSettings( const BroadsoftXsiHandle& h, const BroadworksAnywhere& settings )
{
   m_IOService.post( std::bind( &BroadsoftXsiInterface::setBroadworksAnywhereSettingsImpl, this, h, settings ));
   return kSuccess;
}

int BroadsoftXsiInterface::setBroadworksAnywhereSettingsImpl( const BroadsoftXsiHandle& h, const BroadworksAnywhere& settings )
{
   BroadsoftXsiInfo* info = NULL;
   if( !getInfo( h, info ))
      return kError;

   return info->pImpl->setBroadworksAnywhereSettings( settings );
}

int BroadsoftXsiInterface::setCallForwardAlwaysSettings( const BroadsoftXsiHandle& h, const CallForwardAlways& settings )
{
   m_IOService.post( std::bind( &BroadsoftXsiInterface::setCallForwardAlwaysSettingsImpl, this, h, settings ));
   return kSuccess;
}

int BroadsoftXsiInterface::setCallForwardAlwaysSettingsImpl( const BroadsoftXsiHandle& h, const CallForwardAlways& settings )
{
   BroadsoftXsiInfo* info = NULL;
   if( !getInfo( h, info ))
      return kError;

   return info->pImpl->setCallForwardAlwaysSettings( settings );
}

int BroadsoftXsiInterface::setCallForwardBusySettings(const BroadsoftXsiHandle& h, const CallForwardBusy& settings)
{
   m_IOService.post(std::bind(&BroadsoftXsiInterface::setCallForwardBusySettingsImpl, this, h, settings));
   return kSuccess;
}

int BroadsoftXsiInterface::setCallForwardBusySettingsImpl(const BroadsoftXsiHandle& h, const CallForwardBusy& settings)
{
   BroadsoftXsiInfo* info = NULL;
   if (!getInfo(h, info))
      return kError;

   return info->pImpl->setCallForwardBusySettings(settings);
}

int BroadsoftXsiInterface::setCallForwardNoAnswerSettings(const BroadsoftXsiHandle& h, const CallForwardNoAnswer& settings)
{
   m_IOService.post(std::bind(&BroadsoftXsiInterface::setCallForwardNoAnswerSettingsImpl, this, h, settings));
   return kSuccess;
}

int BroadsoftXsiInterface::setCallForwardNoAnswerSettingsImpl(const BroadsoftXsiHandle& h, const CallForwardNoAnswer& settings)
{
   BroadsoftXsiInfo* info = NULL;
   if (!getInfo(h, info))
      return kError;

   return info->pImpl->setCallForwardNoAnswerSettings(settings);
}

int BroadsoftXsiInterface::setDoNotDisturbSettings(const BroadsoftXsiHandle& h, const DoNotDisturb& settings)
{
   m_IOService.post(std::bind(&BroadsoftXsiInterface::setDoNotDisturbSettingsImpl, this, h, settings));
   return kSuccess;
}

int BroadsoftXsiInterface::setDoNotDisturbSettingsImpl(const BroadsoftXsiHandle& h, const DoNotDisturb& settings)
{
   BroadsoftXsiInfo* info = NULL;
   if (!getInfo(h, info))
      return kError;

   return info->pImpl->setDoNotDisturbSettings(settings);
}

int BroadsoftXsiInterface::setRemoteOfficeSettings(const BroadsoftXsiHandle& h, const RemoteOffice& settings)
{
   m_IOService.post(std::bind(&BroadsoftXsiInterface::setRemoteOfficeSettingsImpl, this, h, settings));
   return kSuccess;
}

int BroadsoftXsiInterface::setRemoteOfficeSettingsImpl(const BroadsoftXsiHandle& h, const RemoteOffice& settings)
{
   BroadsoftXsiInfo* info = NULL;
   if (!getInfo(h, info))
      return kError;

   return info->pImpl->setRemoteOfficeSettings(settings);
}

int BroadsoftXsiInterface::setSimultaneousRingSettings(const BroadsoftXsiHandle& h, const SimultaneousRing& settings)
{
   m_IOService.post(std::bind(&BroadsoftXsiInterface::setSimultaneousRingSettingsImpl, this, h, settings));
   return kSuccess;
}

int BroadsoftXsiInterface::setSimultaneousRingSettingsImpl(const BroadsoftXsiHandle& h, const SimultaneousRing& settings)
{
   BroadsoftXsiInfo* info = NULL;
   if (!getInfo(h, info))
      return kError;

   return info->pImpl->setSimultaneousRingSettings(settings);
}

int BroadsoftXsiInterface::createCall(const BroadsoftXsiHandle& h,  const cpc::string& number)
{
   m_IOService.post(std::bind(&BroadsoftXsiInterface::createCallImpl, this, h, number));
   return kSuccess;
}

int BroadsoftXsiInterface::createCallImpl(const BroadsoftXsiHandle& h,  const cpc::string& number)
{
   BroadsoftXsiInfo* info = NULL;
   if (!getInfo(h, info))
      return kError;

   return info->pImpl->createCall(number);
}

int BroadsoftXsiInterface::destroy( const BroadsoftXsiHandle& h )
{
   m_IOService.post( std::bind( &BroadsoftXsiInterface::destroyImpl, this, h ));
   return kSuccess;
}

int BroadsoftXsiInterface::destroyImpl( const BroadsoftXsiHandle& h )
{
   BroadsoftXsiInfo* info = NULL;
   if( !getInfo( h, info ))
      return kError;

   delete info->pImpl;
   delete info;

   m_InfoMap.erase( h );
   return kSuccess;
}

int BroadsoftXsiInterface::setHandler( const BroadsoftXsiHandle& h, BroadsoftXsiHandler* handler)
{
   int result = kError;
   if( handler != NULL )
   {
      m_IOService.post( std::bind( &BroadsoftXsiInterface::setHandlerImpl, this, h, handler ));
      result = kSuccess;
   }
   else // handler == NULL
   {
      // Special case that needs to block the caller until operation is complete.

      std::mutex mutex;
      std::condition_variable cvar;

      // Unfortunately verbose functor class which is here to avoid use of C++ lambdas.
      struct MyFunctor
      {
         MyFunctor( BroadsoftXsiInterface *parent, const BroadsoftXsiHandle& h, BroadsoftXsiHandler*& handler, std::mutex& mutex, std::condition_variable& cvar, int& result )
            : mParent( parent ), mHandle( h ), mHandler( handler ), mMutex( mutex ), mCVar( cvar ), mResult( result ) {}

         void operator()( void )
         {
            std::lock_guard< std::mutex > lock( mMutex );
            mResult = mParent->setHandlerImpl( mHandle, mHandler );
            mCVar.notify_all();
         }

         BroadsoftXsiInterface *mParent;
         const BroadsoftXsiHandle& mHandle;
         BroadsoftXsiHandler*& mHandler;
         std::mutex& mMutex;
         std::condition_variable& mCVar;
         int& mResult;
      };

      {
         // Block which needs to be synchronized
         std::unique_lock< std::mutex > lock( mutex ); // acquires the mutex

         MyFunctor *func = new MyFunctor( this, h, handler, mutex, cvar, result );
         m_IOService.post( std::bind( &MyFunctor::operator(), func ));
         cvar.wait( lock ); // releases the mutex and waits on the condition (blocks caller thread)
         delete func; func = NULL; // Safe to delete functor now.
         lock.unlock(); // lock is reaquired, so .. release the associated mutex
      }

      // Force any events to run as a result of this operation
      process( -1 );
   }
   return result;
}

int BroadsoftXsiInterface::setHandlerImpl( const BroadsoftXsiHandle& h, BroadsoftXsiHandler* handler)
{
   BroadsoftXsiInfo* info = NULL;
   if( !getInfo( h, info ))
      return kError;

   return info->pImpl->setHandler( handler );
}

int BroadsoftXsiInterface::process( unsigned int timeout )
{
   // -1 == no wait
   if( m_Shutdown )
      return -1;

   resip::ReadCallbackBase* fp = m_CallbackFifo.getNext( timeout );
   while( fp )
   {
      (*fp)();
      delete fp;
      if( m_Shutdown )
         return -1;

      fp = m_CallbackFifo.getNext( -1 );
   }

   return kSuccess;
}

void BroadsoftXsiInterface::postToProcessThread( void (*pfun)(void*), void* obj )
{
   // Inner struct just used to stuff a std::bind into the resip Fifo
   struct ReadCallback : resip::ReadCallbackBase
   {
      ReadCallback(std::function<void()> f) : mF(f) {}
      virtual void operator()() { mF(); }
      virtual void* address() { return NULL; }
   private:
      std::function<void()> mF;
   };

   std::function<void()> bfunc = std::bind(pfun, obj);
   ReadCallback* brcb = new ReadCallback( bfunc );
   m_CallbackFifo.add(brcb);
}

void BroadsoftXsiInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
   m_IOService.post( std::bind( &BroadsoftXsiInterface::setCallbackHookImpl, this, cbHook, context ));
}

void BroadsoftXsiInterface::setCallbackHookImpl( void (*cbHook)(void*), void* context )
{
   m_CbHook = cbHook;
   m_Context = context;
   
   std::map< BroadsoftXsiHandle, BroadsoftXsiInfo* >::iterator iter = m_InfoMap.begin();
   while( iter != m_InfoMap.end() )
   {
      if( iter->second != NULL && iter->second->pImpl != NULL )
         iter->second->pImpl->setCallbackHook( cbHook, context );
      
      ++iter;
   }
}


#ifdef CPCAPI2_AUTO_TEST
CPCAPI2::AutoTestReadCallback* BroadsoftXsiInterface::process_test(int timeout)
{
   if( m_Shutdown )
      return NULL;

   resip::ReadCallbackBase* rcb = m_CallbackFifo.getNext( timeout );
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
      return fpCmd;
   if (rcb != NULL)
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   return NULL;
}
#endif

#endif // CPCAPI2_BRAND_BROADSOFT_XSI_MODULE
