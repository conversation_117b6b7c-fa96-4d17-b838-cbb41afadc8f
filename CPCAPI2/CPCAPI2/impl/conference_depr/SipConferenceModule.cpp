#include "brand_branded.h"

#if (CPCAPI2_BRAND_CONFERENCE_MODULE == 1)
#include "SipConferenceManagerImpl.h"
#include "impl/phone/PhoneInterface.h"

namespace CPCAPI2
{
namespace SipConference
{

SipConferenceManager* SipConferenceManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_CONFERENCE_MODULE == 1)
   PhoneInterface* pi = dynamic_cast<PhoneInterface*>(cpcPhone);
   SipConferenceManager* confManager = dynamic_cast<SipConferenceManager*>(pi->getInterfaceByName("SipConferenceManagerImpl"));
   if (confManager == NULL)
   {
      SipConferenceManagerImpl* m_if = new SipConferenceManagerImpl(cpcPhone);
      pi->registerInterface("SipConferenceManagerImpl", m_if);
      confManager = m_if;
   }
   return confManager;
#else
   return NULL;
#endif
}

}
}
#endif
