#include "TimeBombLicense.h"
#include "brand_branded.h"

#include <ctime>
#include <time.h> // for difftime

namespace CPCAPI2
{
namespace Licensing
{

inline
std::time_t makeTimePoint (int year, int mon, int day)
{
   struct std::tm t;
   t.tm_sec = 0;        // second of minute (0 .. 59 and 60 for leap seconds)
   t.tm_min = 0;        // minute of hour (0 .. 59)
   t.tm_hour = 0;      // hour of day (0 .. 23)
   t.tm_mday = day;       // day of month (0 .. 31)
   t.tm_mon = mon-1;      // month of year (0 .. 11)
   t.tm_year = year-1900; // year since 1900
   t.tm_isdst = -1;       // determine whether daylight saving time
   std::time_t tt = std::mktime(&t);
   if (tt == -1) {
       throw "no valid system time";
   }
   return tt;
}

bool TimeBombLicense::isLicenseValid(int expiryYear, int expiryMonth, int expiryDay, double& timeRemainingInSecs)
{
#if !defined(CPCAPI2_BRAND_LICENSE_EXPIRY_YEAR) && !defined(CPCAPI2_BRAND_LICENSE_EXPIRY_PEROID)
   intentionally break the build if this condition is true; you've messed up the branding'
#endif
   std::time_t current_time;
   std::time(&current_time);
   std::time_t expire_time = makeTimePoint(expiryYear, expiryMonth, expiryDay);
   double timediff = difftime(expire_time, current_time);
   if (timediff > 0.0f)
   {
      timeRemainingInSecs = timediff;
      return true;
   }
   else
   {
      timeRemainingInSecs = 0;
      return false;
   }
}

}
}
