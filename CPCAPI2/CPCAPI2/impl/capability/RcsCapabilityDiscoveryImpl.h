#pragma once

#if !defined(CPCAPI2_CAPABILITY_DISCOVERY_IMPL_H)
#define CPCAPI2_CAPABILITY_DISCOVERY_IMPL_H

#include "cpcapi2defs.h"

#include "RcsCapabilityDiscoveryInterface.h"
#include "RcsCapabilityDiscoveryInternal.h"
#include "RcsCapabilityDiscoveryInternalHandler.h"

#include "../account/SipAccountImpl.h"

namespace CPCAPI2
{
namespace RcsCapabilityDiscovery
{
class RcsCapabilityDiscoveryHandler;

class RcsCapabilityDiscoveryImpl :
   public RcsCapabilityDiscoveryInternalHandler
{
public:
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, std::pair<RcsCapabilityDiscoveryHandler*, RcsCapabilityDiscoveryImpl*> > CapabilityDiscoveryMap;
   RcsCapabilityDiscoveryImpl(std::shared_ptr<CapabilityDiscoveryMap> parentMap, SipAccount::SipAccountImpl& account, RcsCapabilityDiscoveryInterface& intf);
   virtual ~RcsCapabilityDiscoveryImpl();

   int initialize();

   int addContact(
      const cpc::string& address, const RcsCapabilityDiscoveryInterface::time_point& lastUpdated);

   int removeContact(
      const cpc::string& address);

   int requestContactCapabilityStatus(
      const cpc::string& address, bool immediate);

   int setMyCapabilities(
      const RcsCapabilitySet& caps);

   int updateSettings(
      const RcsCapabilityDiscoverySettings& settings);

   int onTimer();

private:
   // begin RcsCapabilityDiscoveryInternalHandler
   virtual int onIncomingCapabilityDiscovery(CPCAPI2::SipAccount::SipAccountHandle account, const RcsIncomingCapabilityDiscoveryEvent& args);
   virtual int onOutgoingCapabilityDiscoverySuccess(CPCAPI2::SipAccount::SipAccountHandle account, const RcsOutgoingCapabilityDiscoveryEvent& args);
   virtual int onOutgoingCapabilityDiscoveryFailure(CPCAPI2::SipAccount::SipAccountHandle account, const RcsOutgoingCapabilityDiscoveryEvent& args);
   virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const ErrorEvent& args);
   virtual int onReleased();
   // end begin RcsCapabilityDiscoveryInternalHandler

   void removeFromQueue(const cpc::string& address);

private:
   CPCAPI2::SipAccount::SipAccountImpl& mAccount;
   RcsCapabilityDiscoveryInternal mRcsCapabilityDiscoveryInternal;
   RcsCapabilityDiscoveryInterface& mInterface;
   RcsCapabilitySet mMyCapabilities;
   std::deque<cpc::string> mQueue;
   typedef std::map<cpc::string, unsigned int> TimeoutMap;
   TimeoutMap mTimeout;
   RcsCapabilityDiscoveryInternalHandle mNextOutgoingHandle; // only used in sendCapabilityRequest
   RcsCapabilityDiscoverySettings mSettings;
   std::weak_ptr<CapabilityDiscoveryMap> mParentMap;
};

}
}
#endif // CPCAPI2_CAPABILITY_DISCOVERY_IMPL_H
