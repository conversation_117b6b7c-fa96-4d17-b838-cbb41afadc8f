#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPMWI_MWITYPE_H
#define JSONRPC_CPCAPI2_SIPMWI_MWITYPE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipMwi {
  enum class MwiType
  {
    Voice,
    Fax,
    Pager,
    Multimedia,
    Text,
    None
  };

  inline std::string to_string(MwiType value)
  {
    switch (value)
    {
      case MwiType::Voice:
        return "Voice";
      case MwiType::Fax:
        return "Fax";
      case MwiType::Pager:
        return "Pager";
      case MwiType::Multimedia:
        return "Multimedia";
      case MwiType::Text:
        return "Text";
      case MwiType::None:
        return "None";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, MwiType value)
  {
    switch (value)
    {
      case MwiType::Voice:
        os << "Voice";
        break;
      case MwiType::Fax:
        os << "Fax";
        break;
      case MwiType::Pager:
        os << "Pager";
        break;
      case MwiType::Multimedia:
        os << "Multimedia";
        break;
      case MwiType::Text:
        os << "Text";
        break;
      case MwiType::None:
        os << "None";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const MwiType& e)
  {
    switch (e)
    {
      case MwiType::Voice:
        j = "Voice";
        break;
      case MwiType::Fax:
        j = "Fax";
        break;
      case MwiType::Pager:
        j = "Pager";
        break;
      case MwiType::Multimedia:
        j = "Multimedia";
        break;
      case MwiType::Text:
        j = "Text";
        break;
      case MwiType::None:
        j = "None";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, MwiType& e)
  {
      if (0 == j.get<std::string>().compare("Voice"))
    {
      e = MwiType::Voice;
    }
    else if (0 == j.get<std::string>().compare("Fax"))
    {
      e = MwiType::Fax;
    }
    else if (0 == j.get<std::string>().compare("Pager"))
    {
      e = MwiType::Pager;
    }
    else if (0 == j.get<std::string>().compare("Multimedia"))
    {
      e = MwiType::Multimedia;
    }
    else if (0 == j.get<std::string>().compare("Text"))
    {
      e = MwiType::Text;
    }
    else if (0 == j.get<std::string>().compare("None"))
    {
      e = MwiType::None;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum MwiType"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipMwi
#endif // JSONRPC_CPCAPI2_SIPMWI_MWITYPE_H
