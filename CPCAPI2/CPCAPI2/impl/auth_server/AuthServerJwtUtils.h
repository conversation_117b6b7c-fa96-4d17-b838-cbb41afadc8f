#pragma once

#if !defined(CPCAPI2_AUTH_SERVER_JWT_UTILS)
#define CPCAPI2_AUTH_SERVER_JWT_UTILS

#include <rutil/Data.hxx>
#include <map>
#include <vector>

namespace CPCAPI2
{
namespace AuthServer
{
class JwtUtils
{
public:
   static int GenerateJWT(const resip::Data& p8file, const resip::Data& issuer, const std::map<resip::Data, resip::Data>& publicClaims, int validSeconds, resip::Data& outJwt);
   static int VerifyJWT(const resip::Data& p8file, const resip::Data& jwt, bool& outValid, std::map<resip::Data, resip::Data>* outPublicClaims=NULL, std::vector<resip::Data>* requestedResources=NULL);

   static int Encrypt(const resip::Data& p8file, const resip::Data& inData, resip::Data& encrypted, bool base64encode=false);
   static int Decrypt(const resip::Data& p8file, const resip::Data& encrypted, resip::Data& outData);

private:
   static resip::Data loadCertFromFile(const resip::Data& fileName);
};
}
}

#endif // CPCAPI2_AUTH_SERVER_JWT_UTILS
