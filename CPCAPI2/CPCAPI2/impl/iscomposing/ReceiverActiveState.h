#pragma once

#if !defined(__CPCAPI2_RECEIVER_ACTIVE_STATE_H__)
#define __CPCAPI2_RECEIVER_ACTIVE_STATE_H__

#include "ReceiverBaseState.h"

namespace CPCAPI2
{
namespace IsComposing
{

class ReceiverActiveState : public ReceiverBaseState
{
public:
   virtual void onEnterState(IsComposingManager* manager, IsComposingInfo* info);

   virtual void onIdleStateReceived(IsComposingManager* manager, IsComposingInfo* info);
   virtual void onActiveStateReceived(IsComposingManager* manager, IsComposingInfo* info);
   virtual void onMessageReceived(IsComposingManager* manager, IsComposingInfo* info);
   virtual void onRefreshTimeout(IsComposingManager* manager, IsComposingInfo* info);

   virtual void onLeaveState(IsComposingManager* manager, IsComposingInfo* info);

   static ReceiverActiveState instance;

private:
   void moveToIdleState(IsComposingManager* manager, IsComposingInfo* info);
};

}
}

#endif // __CPCAPI2_RECEIVER_ACTIVE_STATE_H__