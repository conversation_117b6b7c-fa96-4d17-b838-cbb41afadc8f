#ifdef __APPLE__
#include <TargetConditionals.h>
#endif

#if defined(_WIN32) && !defined(WinRT)
#define XS_TARGET_OS_WINDOWS
#elif defined(__APPLE__) && (!defined(TARGET_OS_IPHONE) || !TARGET_OS_IPHONE)
#define XS_TARGET_OS_MAC
#elif defined(__APPLE__) && defined(TARGET_OS_IPHONE) && TARGET_OS_IPHONE
#define XS_TARGET_OS_IPHONE
#elif defined(__linux__)
#define XS_TARGET_OS_LINUX
#endif

#include "Licensing.h"
#include "XmlSignatureVerify.h"
#include "LicensingTypes.h"
#include "HardwareHash.h"
#include "LicensingHandler.h"
#include "../../util/cpc_logger.h"
#include "../../util/HDDiskId.h"
#include "../../util/MachineIdentification.h"
#include "../../util/FileUtils.h"
#include "../../util/RegistryHelpers.h"
#include "../../util/DeviceInfo.h"
#include "../../account/SipAccountInterface.h"
#include "../../account/SipAccountImpl.h"
#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
#include "../../xmpp/XmppAccountInterface.h"
#include "../../xmpp/XmppAccountImpl.h"
#endif

#ifdef XS_TARGET_OS_MAC
#include "../../util/Mac/CMacSecurityAccess.h"
#endif

#include <rutil/ssl/SHA1Stream.hxx>
#include <rutil/Random.hxx>

#include <ctime>
#include <math.h>
#include <sstream>
#include <regex>


#define LICENSE_SERVER_PROTOCOL_VERSION   "8"
#define LICENSE_SERVER_CERTIFICATE_ID     "3"
#define LICENSE_KEY_NEW_LENGTH            25
#define ACC_RUNTIME_CHECK_INTERVAL        300
#define MAX_ACCOUNT_STR_LENGTH            100

#define TIMER_EXPIRY_ID   9898
#define TIMER_RUNTIME_ID  9899

// =================== CPCAPI2 additions =======================
#ifndef CPCAPI2_BRAND_LICENSE_BUILTIN_KEY
#define CPCAPI2_BRAND_LICENSE_BUILTIN_KEY ""
#endif // !CPCAPI2_BRAND_LICENSE_BUILTIN_KEY

#ifndef CPCAPI2_BRAND_FULL_NAME_OVERRIDE
#define CPCAPI2_BRAND_FULL_NAME_OVERRIDE ""
#endif // !CPCAPI2_BRAND_FULL_NAME_OVERRIDE

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::LICENSING

#if DEBUG && defined(ANDROID)
#include <android/log.h>
#define LICENSE_DEBUG(...) __android_log_print(ANDROID_LOG_INFO, "CPCAPI2_LICENSE", __VA_ARGS__);
#else
#define LICENSE_DEBUG(fmt, ...)
#endif

// =============================================================

namespace CPCAPI2
{
namespace Licensing
{
// Utilities
//==============================================================================

static std::string lexical_cast(int64_t val)
{
   std::stringstream ss;
   ss << val;

   return ss.str();
}

static unsigned long stringToTime(std::string in)
{
   return atol(in.c_str());
}

static std::string timeToString(unsigned long in)
{
   char temp[32];
   snprintf(temp, 32, "%d", (int)in);
   std::string result(temp);

   return result;
}

static void resizeStringIfNeeded(std::string& input, size_t maxLen)
{
   if (input.size() > maxLen)
      input.resize(maxLen);
}

static unsigned long calcCheckInterval(bool firstcheck, const time_t now, const time_t expiry, unsigned long checkDelay, unsigned long endPercent, unsigned long minBetween)
{
   if (expiry < now)
   {
      return minBetween;
   }

   const unsigned long remaining = static_cast<long>(expiry - now);
   if (remaining < minBetween)
   {
      return remaining;
   }
   else
   {
      return minBetween;
   }
}

//==============================================================================

Licensing::Licensing(CPCAPI2::PhoneInterface *cpcPhone, LicensingHandler* ipLicensingEventSink) :
   mPhone(cpcPhone),
   mLicensingHandler(ipLicensingEventSink),
   mExpiryTimer(NULL),
   mRTTimer(NULL),
   mLicenseStatus(LicenseStatus_NoLicense),
   mInitialized(false),
   mFirstCheck(true),
   mExpiry(0),
   mLastRunTimeCheckTime(0),
   mStartTime(0),
   mDuration(0),
   mCheckDelay(0),
   mMinInterval(0),
   mEndPercent(0),
   mErrorCode(0),
#if ((CPCAPI2_BRAND_USE_LICENSE_SERVER == 1) || defined(CPCAPI2_AUTO_TEST))
   mReactor(new resip::MultiReactor("CPCAPI2_Licensing")),
   mOwnReactor(true),
#else
   mReactor(&cpcPhone->getSdkModuleThread()),
   mOwnReactor(false),
#endif
   mXmlSigVerify(new XmlSignatureVerify()),
   mHttpClient(new HTTPClient(cpcPhone))
{
   mSipAccountInterface = NULL;
   mXmppAccountInterface = NULL;
#if (CPCAPI2_BRAND_ACCOUNT_MODULE == 1)
   mSipAccountInterface = dynamic_cast<CPCAPI2::SipAccount::SipAccountInterface*>(CPCAPI2::SipAccount::SipAccountManager::getInterface(mPhone));
#endif
#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
   mXmppAccountInterface = dynamic_cast<CPCAPI2::XmppAccount::XmppAccountInterface*>(CPCAPI2::XmppAccount::XmppAccountManager::getInterface(mPhone));
#endif


   if (mOwnReactor)
      mReactor->start();
}

Licensing::~Licensing()
{
   delete mXmlSigVerify;
   delete mHttpClient;
}

void
Licensing::Shutdown()
{
   CancelExpiryTimer();
   CancelRunTimeTimer();

   mInitialized = false;

   if (mOwnReactor)
   {
      mReactor->stop();
      // calls join
      delete mReactor;
      mReactor = NULL;
   }
}

void
Licensing::Initialize(const LicensingClientSettings& licenseSettings, void* context)
{
   if (mReactor)
      mReactor->post(resip::resip_bind(&Licensing::initializeImpl, this, licenseSettings, context));
}

void
Licensing::initializeImpl(const LicensingClientSettings& licenseSettings, void* context)
{
   InfoLog(<< "Licensing initialize.");

   mContext = context;
   mSettings = licenseSettings;

   if (!mSettings.licenseDocumentLocation.empty())
   {
      std::string tmp = std::string(mSettings.licenseDocumentLocation.c_str());
      mSettings.licenseDocumentLocation = tmp.c_str();

      CPCAPI2::FileUtils::CreateDir(tmp.c_str(), true);

      mInitialized = true;
   }

   srand(static_cast<unsigned int>(time(0)));

   SetHarddiskHash();
   SetMacAddressHash();
   SetHardwareID();
   SetHardwareHash();
   SetMachineName();

   LicenseStatus status = LicenseStatus_Error;

   if (mInitialized)
   {
#ifndef CPCAPI2_AUTO_TEST
      cpc::string builtInKey(CPCAPI2_BRAND_LICENSE_BUILTIN_KEY);
      if (!builtInKey.empty())
      {
         InfoLog(<< "Using built-in license key instead of provided key(s)");
         mSettings.licenseKeys.clear();
         mSettings.licenseKeys.push_back(builtInKey);
      }
#endif

#if (CPCAPI2_BRAND_USE_LICENSE_SERVER == 1)
      status = InternalDoRemoteCheck();
#else
#ifdef CPCAPI2_AUTO_TEST
      if(licenseSettings.forceRemoteCheck)
         status = InternalDoRemoteCheck();
      else
#endif
      status = InternalDoLocalCheck();
#endif
   }
   else
   {
      ErrLog(<< "Licensing::SetLicense() - Licensing has not been initialized!");
   }

   InternalSetStatus(status, true);
}

void
Licensing::InternalSetStatus(LicenseStatus status, bool notify)
{
#if (CPCAPI2_BRAND_LICENSE_IGNORE_SERVER_ERRORS == 1)

   if (status == LicenseStatus_ServerAuthFailed ||
      status == LicenseStatus_ServerUnreachable ||
      status == LicenseStatus_ServerBadData ||
      status == LicenseStatus_Expired)
   {
      status = LicenseStatus_Valid;
   }
#endif /* CPCAPI2_BRAND_LICENSE_IGNORE_SERVER_ERRORS */

   if (mLicenseStatus != status)
   {
      mLicenseStatus = status;
      DebugLog(<< "License status: " << status);
   }

   if (notify)
   {
      sendLicenseEvent();
   }
}

void Licensing::sendLicenseEvent() const
{
   if (mLicensingHandler)
   {
      LicenseList licenses = mLicenses;
      if (mLicenseStatus == LicenseStatus_Valid && licenses.empty() && mInvalidLicenses.empty())
      {
         // Handle case of LTS or local license check, where mLicenses is not necessarily populated
         // in case of license validation success, but the app may need the key(s) for other purposes.
         // (see OBELISK 2682, 2750, 2772)
         for (auto it = mSettings.licenseKeys.begin(); it != mSettings.licenseKeys.end(); ++it)
         {
            License licenseInfo;
            licenseInfo.key = *it;
            licenses.push_back(licenseInfo);
         }
      }

      LicenseStateEvent ipEvent(mLicenseStatus, mUserLicenseMessage, mSettings.licenseUrl.c_str(),
         mUserUrlDescription, CalcDaysLeft(), mErrorCode, mHardwareID, licenses, mInvalidLicenses, mContext);

      std::weak_ptr< const Licensing> weakThis = shared_from_this();
      mPhone->getSdkModuleThread().post(resip::resip_static_bind(&Licensing::fireLicenseStateEvent, weakThis, mLicensingHandler, ipEvent));
   }
}


void Licensing::fireLicenseStateEvent(const std::weak_ptr< const Licensing>& weakThis, LicensingHandler* licensingHandler, const LicenseStateEvent& evt)
{
   if (std::shared_ptr<const Licensing> sharedThis = weakThis.lock())
   {
      licensingHandler->OnLicenseState(evt);
   }
}

LicenseStatus
Licensing::InternalDoRemoteCheck()
{
   mUserLicenseMessage = "";
   mUserUrlDescription = "";
   mUserLicenseURL     = "";

   LicenseStatus status = LicenseStatus_Pending;

   const std::string license = ReadFromFile(LFT_License);
   bool requestNow = true;

   if (!license.empty()) // We have a license from file
   {
      const LicenseDocParser parser(license);
      if (parser.type() == LicenseDocParser::Error)
      {
         ErrLog(<< "Licensing::InternalDoRemoteCheck() - Invalid license loaded from file! ");
      }
      else if (parser.certificateId().compare(LICENSE_SERVER_CERTIFICATE_ID) != 0)
      {
         ErrLog(<< "Licensing::InternalDoRemoteCheck() - discarding license document. Certificate ID mismatch ");
      }
      else
      {
         // First verify the signature of the document
         unsigned char* psig = NULL;
         int siglen = 0;
         bool isValid = false;
         isValid = mXmlSigVerify->verifySignature(license, &psig, siglen);

         if (!isValid)
         {
            ErrLog(<< "Licensing::InternalDoRemoteCheck() Bad signature on License Document - Verification fails!");
         }
         else if (!SameSetKeysAsInput(parser.licenses()))
         {
            ErrLog(<< "New key(s) entered by user");
         }
		 else if (!SameBrand(parser.brand()))
		 {
			 ErrLog(<< "Different brand");
		 }
#ifdef XS_TARGET_OS_WINDOWS
         else if ((parser.machineHash() != mHardwareID) && !CPCAPI2::RegistryHelpers::VerifyHardware(mAllHdds, mAllMacs))
         {
            ErrLog(<< "Loaded License Document is not from this PC! ");
         }
#else
         else if (parser.machineHash() != mHardwareID)
         {
            DebugLog(<< "Loaded License Document is not from this PC! ");
         }
#endif
         else if (ReadAccumulatedRunTime() == 0)
         {
            // I.e. there is no art file or it is empty or decrypts to 0
            DebugLog(<< "Licensing::InternalDoRemoteCheck() - No art or art is zero! ");
         }
         else
         {
            SetAllValuesFromParser(parser);

            if (IsLicenseExpired())
            {
               DebugLog(<< "Licensing::InternalDoRemoteCheck() - License expired, request again! ");
            }
            else
            {
               status = (parser.type() == LicenseDocParser::Valid) ? LicenseStatus_Valid : LicenseStatus_Grace;
               if ((status == LicenseStatus_Valid) && (mLicenses.size()==1 && mLicenses[0].type == "Trial"))
               {
                  status = LicenseStatus_TrialMode;
               }

               // valid license; no need to send server request at this time - dependent on branding
               requestNow = (CPCAPI2_BRAND_LICENSE_FORCE_REMOTE_CHECK == 1);
            }
         }
      }
   }
   if (requestNow)
   {
      // Whoa, not so fast, do we even have a key to validate with the server?
      if (mSettings.licenseKeys.empty())
      {
         status = LicenseStatus_NoLicense;
      }
      else
      {
         if (PerformKeySanityCheck())
         {
            // To-Do: Do we need to check for already outstanding request?
            DebugLog(<< "Sending license request.");
            SendLicenseRequest();

            status = mLicenseStatus;
#ifdef XS_TARGET_OS_WINDOWS
            // Write MachineID and hardware primitives to registry for safe keeping on windows.
            if(!CPCAPI2::RegistryHelpers::WriteRegistryStringValue("hid", mHardwareID))
            {
               DebugLog(<< "Licensing::InternalDoRemoteCheck() - Failed writing hardware id to HKLM! ");
            }
#endif
         }
         else
         {
            DebugLog(<< "Licensing::InternalDoRemoteCheck() - License key sanity check fails!");
            status = LicenseStatus_Invalid;
            mErrorCode = 0;
         }
      }
   }
   else if ((status == LicenseStatus_Valid) || (status == LicenseStatus_Grace) || (status == LicenseStatus_TrialMode))
   {
      // License from file appear to be valid, start the timer to check for new license
      if (mFirstCheck)
      {
         DebugLog(<< "Licensing::InternalDoRemoteCheck() - Start license check timer, load checktime! ");
         StartExpiryTimer(mExpiry, true);
      }
      else
      {
         DebugLog(<< "Licensing::InternalDoRemoteCheck() - Start license check timer! ");
         StartExpiryTimer(mExpiry);
      }

      DoAccumulatedRunTimeCheck();
   }

   return status;
}

bool Licensing::SameSetKeysAsInput(const LicenseList& licenses) const
{
   bool sameSet = licenses.size() == mSettings.licenseKeys.size();
   if (sameSet)
   {
      for (auto it = licenses.begin(); it != licenses.end(); ++it)
      {
         if (std::find(mSettings.licenseKeys.begin(), mSettings.licenseKeys.end(), (*it).key.c_str()) == mSettings.licenseKeys.end())
         {
            sameSet = false;
            break;
         }
      }
   }
   return sameSet;
}

bool Licensing::SameBrand(const std::string & brand) const
{
	return brand == mSettings.brand.c_str();
}

bool Licensing::PerformKeySanityCheck()
{
   bool pass = true;
   for (auto it = mSettings.licenseKeys.begin(); pass && it != mSettings.licenseKeys.end(); ++it)
   {
      pass = PerformKeySanityCheck((*it).c_str());
   }
   return pass;
}

bool
Licensing::PerformKeySanityCheck(const std::string& key)
{
   // Check key length and perform BloodBag check
   // (http://www.transfusionguidelines.org.uk/index.asp?Publication=RB&Section=25&pageid=742)

   static const int _bloodBagMapping[256] =
   { -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,   // 0-15
     -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,   // 16-31
     -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1, -1, -1,   // 32-47
      0,  1,  2,  3,  4,  5,  6,  7,  8,  9, -1, -1, -1, -1, -1, -1,   // 48-63
     -1, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24,   // 64-79
     25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, -1, -1, -1, -1, -1,   // 80-95
     -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,   // 96-111
     -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,   // 112-127
     -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,   // 128-143
     -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,   // 144-159
     -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,   // 160-175
     -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,   // 176-191
     -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,   // 192-207
     -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,   // 208-223
     -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,   // 224-239
     -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1 }; // 240-255

   if (key.length() != LICENSE_KEY_NEW_LENGTH)
   {
      return false;
   }

   long weightedSum = 0;

   for (int i=0; i<LICENSE_KEY_NEW_LENGTH-2; i++)
   {
      int value = _bloodBagMapping[key[i]];
      if (value == -1)
      {
         return false;
      }

      weightedSum += value * static_cast<long>(pow((double)2, LICENSE_KEY_NEW_LENGTH-2-i));
   }

   long checkSum = ((38 - (weightedSum % 37)) % 37);

   char result[8];
   snprintf(result, 8, "%02d", (int)checkSum);

   std::string chkPortion = key.substr(LICENSE_KEY_NEW_LENGTH-2,2);

   return (chkPortion.compare(result) == 0);
}

LicenseStatus
Licensing::InternalDoLocalCheck()
{
   DebugLog(<<"Licensing::InternalDoLocalCheck() - Validating license locally...");

#ifdef XS_TARGET_OS_WINDOWS
   // Write MachineID and hardware primitives to registry for safe keeping on windows.
   if(!CPCAPI2::RegistryHelpers::WriteRegistryStringValue("hid", mHardwareID))
   {
      DebugLog(<< "Licensing::InternalDoLocalCheck() - Failed writing hardware id to HKLM! ");
   }
#endif

   LicenseStatus status = LicenseStatus_Error;

   if (mSettings.licenseKeys.empty())
   {
      status = LicenseStatus_NoLicense;
   }
   else
   {
      // verify key format - it's all we can do locally to validate the license
      if (PerformKeySanityCheck())
      {
         status = LicenseStatus_Valid;

#if defined(XS_TARGET_OS_WINDOWS)
         // save hardware primitives so next time we can detect if hardware has changed
         if(!CPCAPI2::RegistryHelpers::WriteHardwareToRegistry("h", mAllHdds))
         {
            DebugLog(<< "Licensing::InternalDoLocalCheck() - Failed writing HDD to HKLM! ");
         }

         if(!CPCAPI2::RegistryHelpers::WriteHardwareToRegistry("m", mAllMacs))
         {
            DebugLog(<< "Licensing::InternalDoLocalCheck() - Failed writing MAC to HKLM! ");
         }
#endif

      }
      else
      {
         status = LicenseStatus_Invalid;
      }
   }

   return status;
}

void
Licensing::SetAllValuesFromParser(const LicenseDocParser& parser)
{
   mExpiry       = parser.expiry();
   mStartTime    = parser.startTime();
   mDuration     = parser.duration();
   mCheckDelay   = parser.checkDelay();
   mMinInterval  = parser.minInterval();
   mEndPercent   = parser.endPercent();
   mErrorCode    = parser.errorCode();
   mLicenses     = parser.licenses();
   mInvalidLicenses = parser.invalidLicenses();

   mUserLicenseMessage = parser.message();
   mUserUrlDescription = parser.urlDescription();
   mUserLicenseURL     = parser.url();
}

void
Licensing::SetErrorValuesFromParser(const LicenseDocParser& parser)
{
   mErrorCode    = parser.errorCode();
   mUserLicenseMessage = parser.message();
   mUserUrlDescription = parser.urlDescription();
   mUserLicenseURL     = parser.url();
}

//--- SERVER REQUESTS ------------------------------
cpc::vector<CPCAPI2::HTTPClient::StringPair> Licensing::CustomHeaders(const std::string& content) const
{
   cpc::vector<CPCAPI2::HTTPClient::StringPair> headers;
   std::time_t timeStamp = std::time(0);
   headers.push_back(TimestampHeader(lexical_cast(timeStamp)));
   headers.push_back(SignatureHeader(lexical_cast(timeStamp), content));
   return headers;
}

CPCAPI2::HTTPClient::StringPair Licensing::TimestampHeader(const std::string& timestamp) const
{
   CPCAPI2::HTTPClient::StringPair header;
   header.first = "X-CP-Timestamp";
   header.second = timestamp.c_str();
   return header;
}

CPCAPI2::HTTPClient::StringPair Licensing::SignatureHeader(const std::string& timestamp, const std::string& content) const
{
   CPCAPI2::HTTPClient::StringPair header;
   header.first = "X-CP-Signature";
   header.second = GenerateSignature(timestamp, content).c_str();
   return header;
}

std::string Licensing::GenerateSignature(const std::string& timestamp, const std::string& content) const
{
   //**********?POST?/servicev2/validatelicense?{content}

   std::ostringstream sigString;
   sigString << timestamp.c_str() << "?" << "POST" << "?" << "/servicev2/validatelicense" << "?" << content.c_str();

   std::string signingStr = sigString.str();
   std::string cert(CPCAPI2_BRAND_LICENSE_EMBEDDED_CERTIFICATE);

   unsigned char key[SHA256_DIGEST_LENGTH];
   // non thread safe if NULL is passed for 3rd param
   SHA256(reinterpret_cast<const unsigned char*>(cert.c_str()), cert.length(), key);


   unsigned char sig[EVP_MAX_MD_SIZE];
   // non thread safe if NULL is passed for 6rd param
   HMAC(EVP_sha256(), key, SHA256_DIGEST_LENGTH, reinterpret_cast<const unsigned char*>(signingStr.c_str()), signingStr.length(), sig, NULL);
   resip::Data encoded = resip::Data(sig, SHA256_DIGEST_LENGTH).base64encode();

   return encoded.c_str();
}

void
Licensing::SendLicenseRequest(bool useSecondaryUrl)
{
   std::string request = CreateRequestXML();
   char *postBlob = NULL;

   std::string uri = useSecondaryUrl ? mSettings.licenseUrlSecondary.c_str() : mSettings.licenseUrl.c_str();

   // for debug only!!!
   //std::string uri = "http://alphacels.counterpath.com/Service/LicensePool.svc/RegisterClientBinding";
   //uri = "https://home.giddens.net:2255/servicev2/ValidateLicense";

   DebugLog(<< "License request uri='" << uri << "', content='" << request << "'");

   unsigned int length = (unsigned int) request.length();
   if (length > 0)
   {
      postBlob = new char[length + sizeof(wchar_t)];
      memcpy(postBlob, request.c_str(), length);

      int errorCode;
      int responseStatus = 0;
      cpc::string result;
      cpc::string contentType;
      HTTPClient::RedirectInfo redirectInfo;
      mHttpClient->HTTPSendMessage(
         HTTPClient::EHTTPVerbPOST,
         uri.c_str(),
         "application/xml", // mime-type
         "",
         "",
         NULL, // client certificate
         NULL, // client certificate password
         postBlob,
         length,
         0,
         false,
         false,
         true,  //ignore all the certificate validation error- only for licensing - FB #52301
         false,  //no cookie support for licensing yet; m_ipSettings->GetBool("system", "http:enable_cookies",false),
         "",     //no cookie support for licensing yet; m_ipSettings->GetString("system", "http:cookie_file",""),
         CustomHeaders(request),
         false,
         false,
         errorCode,
         responseStatus,
         contentType,
         result,
         redirectInfo,
         NULL,
         mSettings.proxyServerAddress
      );
      DebugLog(<< "License response error='" << errorCode << "', status=" << responseStatus << "', content='" << result << "'");

      delete [] postBlob;

      processServerResponse(errorCode, responseStatus, std::string(contentType), std::string(result), useSecondaryUrl);
   }
}

#define XMLCHAR_CAST(x) reinterpret_cast<const xmlChar*>(x)

// replaces any invalid characters that can't be encoded into XML
// assumes input is valid UTF-8
inline std::string MakeValidXml(std::string text)
{
    // The list of valid XML characters were obtained from https://www.w3.org/TR/xml/#charsets
    // and I converted that list to this list of invalid characters and then I remove those (like
    // \x00 and \uD800) that failed compilation or in the regex processing - these are unlikely
    // to appear anyway).
    static std::regex _invalidXMLChars("([\x01-\x08\x0B\x0C\x0E-\x1F\u0001-\u0008\u000B\u000C\u000E-\u001F\uFFFE\uFFFF])");
    std::string res = std::regex_replace(text, _invalidXMLChars, "�");
    return res;
}

inline std::string MakeValidXml(cpc::string text)
{
   return MakeValidXml(std::string(text.c_str()));
}

const std::string
Licensing::CreateRequestXML()
{
   const std::string clientTimeStr = timeToString(static_cast<unsigned long>(GetCurrentTimeUTC()));
   const std::string clientVersion = mSettings.clientVersion.empty() ? std::string(CPCAPI2_BRAND_VERSION_NUMBER) + " " + std::string(CPCAPI2_BRAND_BUILD_STAMP) : mSettings.clientVersion.c_str();

   xmlBufferPtr buffer = xmlBufferCreate();

   try
   {
      xmlTextWriterPtr writer = xmlNewTextWriterMemory(buffer, 0);
      if (!writer)
      {
         ErrLog(<< "Licensing::CreateRequestXML() - XML buffer alloction fails!");
         assert(false);
         return std::string();
      }

      xmlTextWriterStartDocument  (writer, 0, "UTF-8", 0);

      xmlTextWriterStartElement   (writer, XMLCHAR_CAST("validateLicenseRequest"));

      xmlTextWriterWriteAttribute (writer, XMLCHAR_CAST("version"),       XMLCHAR_CAST(LICENSE_SERVER_PROTOCOL_VERSION));
      xmlTextWriterWriteElement   (writer, XMLCHAR_CAST("machineHash"),   XMLCHAR_CAST(mHardwareID.c_str()));
      xmlTextWriterWriteElement   (writer, XMLCHAR_CAST("machineName"),   XMLCHAR_CAST(MakeValidXml(mMachineName).c_str()));
      xmlTextWriterWriteElement  (writer, XMLCHAR_CAST("hardwareId"), XMLCHAR_CAST(mHardwareHash.c_str()));

      if (!mSettings.provisioningId.empty())  xmlTextWriterWriteElement(writer, XMLCHAR_CAST("provisioningUsername"), XMLCHAR_CAST(MakeValidXml(mSettings.provisioningId).c_str()));
      if (!mSettings.keySourceUrl.empty()) xmlTextWriterWriteElement(writer, XMLCHAR_CAST("keySourceUrl"), XMLCHAR_CAST(mSettings.keySourceUrl.c_str()));
      if (!mSettings.osVersion.empty()) xmlTextWriterWriteElement   (writer, XMLCHAR_CAST("osVersion"),    XMLCHAR_CAST(MakeValidXml(mSettings.osVersion).c_str()));

      std::string brandName = CPCAPI2_BRAND_FULL_NAME_OVERRIDE;
      if (brandName.empty())
      {
         brandName = mSettings.brand.c_str();
      }

      xmlTextWriterWriteElement   (writer, XMLCHAR_CAST("brand"),         XMLCHAR_CAST(brandName.c_str()));
      xmlTextWriterWriteElement   (writer, XMLCHAR_CAST("clientTime"),    XMLCHAR_CAST(clientTimeStr.c_str()));

      if (!mHardDiskHash.empty())
      {
         xmlTextWriterWriteElement(writer, XMLCHAR_CAST("harddiskSn"), XMLCHAR_CAST(mHardDiskHash.c_str()));
      }
      xmlTextWriterWriteElement(writer, XMLCHAR_CAST("macAddress"), XMLCHAR_CAST(mMacAddressHash.c_str()));

      xmlTextWriterWriteElement  (writer, XMLCHAR_CAST("certificateId"),   XMLCHAR_CAST(LICENSE_SERVER_CERTIFICATE_ID));

      /* Include optional subscriber limit from settings (can be provisioned) in the license request. Used by Metaswitch. */
      xmlTextWriterWriteElement  (writer, XMLCHAR_CAST("maximumSubscribers"), XMLCHAR_CAST(timeToString(mSettings.maximumSubscribers).c_str()));

      xmlTextWriterWriteElement  (writer, XMLCHAR_CAST("clientVersion"), XMLCHAR_CAST(MakeValidXml(clientVersion).c_str()));

      // licenses
      xmlTextWriterStartElement(writer, XMLCHAR_CAST("licenses"));
      for (auto it = mSettings.licenseKeys.begin(); it != mSettings.licenseKeys.end(); ++it)
      {
         xmlTextWriterStartElement(writer, XMLCHAR_CAST("license")); // <license>
         xmlTextWriterWriteElement(writer, XMLCHAR_CAST("key"), XMLCHAR_CAST((*it).c_str()));
         xmlTextWriterEndElement(writer); // </license>
      }
      xmlTextWriterEndElement(writer); // </licenses>

      // accounts
      xmlTextWriterStartElement     (writer, XMLCHAR_CAST("accounts"));

      if (!mSettings.accounts.empty())
      {
         for (cpc::vector<AccountInfo>::const_iterator it = mSettings.accounts.begin(); it != mSettings.accounts.end(); ++it)
         {
            std::string creds = std::string(it->user);
            resizeStringIfNeeded(creds, MAX_ACCOUNT_STR_LENGTH);
            xmlTextWriterStartElement     (writer, XMLCHAR_CAST("account"));
            xmlTextWriterWriteAttribute   (writer, XMLCHAR_CAST("type"), XMLCHAR_CAST(it->type.c_str()));
            xmlTextWriterWriteAttribute   (writer, XMLCHAR_CAST("default"), XMLCHAR_CAST(it->defaultAccount ? "true" : "false"));
            xmlTextWriterWriteAttribute   (writer, XMLCHAR_CAST("state"), XMLCHAR_CAST(it->enabled ? "Enabled" : "Disabled"));
            xmlTextWriterWriteString      (writer, XMLCHAR_CAST(MakeValidXml(creds).c_str()));
            xmlTextWriterEndElement       (writer);
         }
      }
      else
      {
#if (CPCAPI2_BRAND_ACCOUNT_MODULE == 1)
         // SIP accounts
         bool gotFirstSipAccount = false;
         cpc::vector<CPCAPI2::SipAccount::SipAccountHandle> sipAccountHandles = mSipAccountInterface->getAccountHandles();
         for (cpc::vector<CPCAPI2::SipAccount::SipAccountHandle>::const_iterator sipAccountIter = sipAccountHandles.begin(); sipAccountIter != sipAccountHandles.end(); ++sipAccountIter)
         {
            CPCAPI2::SipAccount::SipAccountHandle sipAccountHandle = *sipAccountIter;
            CPCAPI2::SipAccount::SipAccountImpl* sipAccountImpl = mSipAccountInterface->getAccountImpl(sipAccountHandle);
            if (!sipAccountImpl) {
               WarningLog(<< "sipAccountImpl is NULL for sipAccountHandle=" << sipAccountHandle);
               continue;
            }
            if (mPhone->isShutdown())
            {
               WarningLog(<< "phone is shut down while processing sipAccountHandle=" << sipAccountHandle);
               break;
            }

            std::string creds = "sip:" + std::string(sipAccountImpl->getSettings().username.c_str()) + "@" + std::string(sipAccountImpl->getSettings().domain.c_str());
            resizeStringIfNeeded(creds, MAX_ACCOUNT_STR_LENGTH);
            xmlTextWriterStartElement     (writer, XMLCHAR_CAST("account"));
            xmlTextWriterWriteAttribute   (writer, XMLCHAR_CAST("type"), XMLCHAR_CAST("SIP"));
            if (!gotFirstSipAccount)
            {
               gotFirstSipAccount = true;
               xmlTextWriterWriteAttribute   (writer, XMLCHAR_CAST("default"), XMLCHAR_CAST("true"));
            }
            else
            {
               xmlTextWriterWriteAttribute   (writer, XMLCHAR_CAST("default"), XMLCHAR_CAST("false"));
            }
            xmlTextWriterWriteAttribute   (writer, XMLCHAR_CAST("state"), XMLCHAR_CAST(sipAccountImpl->isEnabled() ? "Enabled" : "Disabled"));
            xmlTextWriterWriteString      (writer, XMLCHAR_CAST(MakeValidXml(creds).c_str()));
            xmlTextWriterEndElement       (writer);
         }
#endif

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
         // XMPP accounts

         cpc::vector<CPCAPI2::XmppAccount::XmppAccountHandle> xmppAccountHandles = mXmppAccountInterface->getAccountHandles();
         for (cpc::vector<CPCAPI2::XmppAccount::XmppAccountHandle>::const_iterator xmppAccountIter = xmppAccountHandles.begin(); xmppAccountIter != xmppAccountHandles.end(); ++xmppAccountIter)
         {
            CPCAPI2::XmppAccount::XmppAccountHandle xmppAccountHandle = *xmppAccountIter;
            CPCAPI2::XmppAccount::XmppAccountImpl* xmppAccountImpl = mXmppAccountInterface->getImpl(xmppAccountHandle).get();
            if (!xmppAccountImpl) {
               WarningLog(<< "xmppAccountImpl is NULL for xmppAccountHandle=" << xmppAccountHandle);
               continue;
            }
            if (mPhone->isShutdown())
            {
               WarningLog(<< "phone is shut down while processing xmppAccountHandle=" << xmppAccountHandle);
               break;
            }

            std::string creds = std::string(xmppAccountImpl->getSettings().username.c_str()) + "@" + std::string(xmppAccountImpl->getSettings().domain.c_str());
            resizeStringIfNeeded(creds, MAX_ACCOUNT_STR_LENGTH);
            xmlTextWriterStartElement     (writer, XMLCHAR_CAST("account"));
            xmlTextWriterWriteAttribute   (writer, XMLCHAR_CAST("type"), XMLCHAR_CAST("XMPP"));
            xmlTextWriterWriteAttribute   (writer, XMLCHAR_CAST("default"), XMLCHAR_CAST("false"));
            xmlTextWriterWriteAttribute   (writer, XMLCHAR_CAST("state"), XMLCHAR_CAST(xmppAccountImpl->isEnabled() ? "Enabled" : "Disabled"));
            xmlTextWriterWriteString      (writer, XMLCHAR_CAST(MakeValidXml(creds).c_str()));
            xmlTextWriterEndElement       (writer);
         }
#endif
      }

      xmlTextWriterEndElement     (writer);
      xmlTextWriterEndElement(writer);
      xmlTextWriterEndDocument(writer);
      xmlFreeTextWriter(writer);
   }
   catch (const std::exception&)
   {
      xmlBufferFree(buffer);

      ErrLog(<< "Licensing::CreateRequestXML() - XML writer fails!");
      assert(false);
      return std::string();
   }

   std::string contents(reinterpret_cast<const char*>(buffer->content));
   xmlBufferFree(buffer);

   return contents;
}

void
Licensing::SetMacAddressHash()
{
   std::string macInfo = CPCAPI2::MachineIdentification::GetLocalMACAddress();

   if (macInfo.empty())
   {
      ErrLog(<< "Licensing: CUtils::GetLocalMACAddress not available");
   }
   else
   {
      resip::SHA1Stream cHash;
      cHash << resip::Data(macInfo.data(), macInfo.size());
      mMacAddressHash = cHash.getHex().c_str();
   }
}

void
Licensing::SetHarddiskHash()
{
#if defined(XS_TARGET_OS_MAC)
   std::string hddInfo = CSecurityAccess::instance()->getSerialNumber().c_str();
#elif defined(XS_TARGET_OS_IPHONE)
   cpc::string uniqueId;
   DeviceInfo::getPlatformUniqueId(uniqueId);
   std::string hddInfo = uniqueId.c_str();
#elif defined(XS_TARGET_OS_LINUX)
   std::string hddInfo;
#else
   std::string hddInfo = CPCAPI2::HDDiskId::GetPrimaryMasterHDDId();
#endif

   if (hddInfo.empty())
   {
      ErrLog(<< "Licensing: CHDDiskId::GetPrimaryMasterHDDId not available");
   }
   else
   {
      resip::SHA1Stream cHash;
      cHash << resip::Data(hddInfo.data(), hddInfo.size());

      mHardDiskHash = cHash.getHex().c_str();
   }
}

void Licensing::SetHardwareHash()
{
   HardwareHash hh;
   mHardwareHash = hh.GenerateHardwareHash();
}

void
Licensing::SetHardwareID()
{
   if (mHardwareID.empty())
   {
#ifdef XS_TARGET_OS_WINDOWS
      mAllMacs = CPCAPI2::MachineIdentification::GetAllMACs_Win32();
      mAllHdds = CPCAPI2::HDDiskId::GetAllHddInfo();
#elif defined(WinRT)
      mAllMacs = CPCAPI2::MachineIdentification::GetAllMACs_Win32();
#else
      mAllMacs.push_back(mMacAddressHash);
      mAllHdds.push_back(mHardDiskHash);
#endif

      DebugLog(<< "Found " << mAllMacs.size() << " MAC, " << mAllHdds.size() << " HD.");
      mHardwareID = CPCAPI2::MachineIdentification::GetHardwareId();
   }
}

void
Licensing::SetMachineName()
{
   if (mMachineName.empty())
   {
      mMachineName = CPCAPI2::MachineIdentification::ComputerName();
   }
}

//--- LICENSE FILE HANDLING ---------------

const cpc::string
Licensing::GetStorageFileName(LicensingFileType whichFile)
{
   std::string p(mSettings.licenseDocumentLocation);

   if (p.back() != '/')
      p += "/";

   switch (whichFile)
   {
      case LFT_License:
         p += "license.bin";
         break;
      case LFT_Auxiliary:
         p += "rtp.bin";
         break;
      default:
         p += "broken.bin";
         break;
   }

   return p.c_str();
}

void
Licensing::WriteToFile(const std::string& data, LicensingFileType whichType)
{
   cpc::string theFile(GetStorageFileName(whichType).c_str());

   CPCAPI2::FileUtils::ResetFileAttributes(theFile);
   bool bResult = CPCAPI2::FileUtils::SaveMemoryToFile(
      theFile.c_str(),
      (unsigned char*) data.data(),
      (unsigned long) data.size(),
      true, // Always encrypt
      CPCAPI2_BRAND_ENCRYPTED_SETTINGS_KEY);

   if (!bResult)
   {
      ErrLog(<< "Licensing::WriteToFile() - Error writing to file " + std::string(GetStorageFileName(whichType).c_str()));
   }
}

const std::string
Licensing::ReadFromFile(LicensingFileType whichType)
{
   std::string result;

   char* pFileContents = NULL;

     bool bLoaded = CPCAPI2::FileUtils::LoadTextFileToMemory(
      GetStorageFileName(whichType).c_str(),
      pFileContents,
      true,   // Always encrypt
      CPCAPI2_BRAND_ENCRYPTED_SETTINGS_KEY);

   if ( !(bLoaded && pFileContents))
   {
      InfoLog(<< "Licensing::ReadFromFile() - Error reading file " + std::string(GetStorageFileName(whichType).c_str()));
   }
   else
   {
      result = pFileContents;
      delete[] pFileContents;
   }

   return result;
}

void
Licensing::LicDeleteFile(LicensingFileType whichType)
{
  const std::string file(GetStorageFileName(whichType));

#if defined(XS_TARGET_OS_WINDOWS) || defined(WinRT)
   if (!DeleteFile(file.c_str()))
   {
      ErrLog(<< "Licensing::DeleteFile() - Deleting file " + std::string(GetStorageFileName(whichType).c_str()) + "fails!");
   }
#else
   if (unlink(file.c_str()) != 0)
   {
       ErrLog(<< "Licensing::DeleteFile() - Deleting file " + std::string(GetStorageFileName(whichType).c_str()) + "fails!");
   }
#endif
}

//--- HTTP RESPONSE HANDLING --------------

void
Licensing::handleHTTPError(LicenseStatus status)
{
   if(status == LicenseStatus_ServerEmbeddedCert)
   {
      // the branded in root certificate is invalid
      InternalSetStatus(status);
      return;
   }

   // Check for expiry of current license, if there is one
   if (IsLicenseExpired())
   {
      // Our local license has expired, we can't continue running
      InternalSetStatus(status);
   }
   else
   {
      //// There is time to try again later
      StartExpiryTimer(mExpiry);
   }
}

bool
Licensing::trySecondaryServer(bool fromSecondaryUrl, std::string message)
{
   if (!mSettings.licenseUrlSecondary.empty() && !fromSecondaryUrl)
   {
      DebugLog(<< "Licensing::trySecondaryServer() - " << message.c_str() << " Retry request using secondary URL.");
      SendLicenseRequest(true);
      return true;
   }
   return false;
}

// IHTTPSink
void
Licensing::processServerResponse(int errorCode, int responseStatus, std::string contentType, std::string responseBody, bool fromSecondaryUrl)
{
   // Did we get a response?
   if (errorCode != 0)
   {
      ErrLog(<< "Licensing::OnHTTPResponse(): Error:" + lexical_cast(errorCode) + " Reason:" + lexical_cast(responseStatus));

      if (responseStatus == CPCAPI2::HTTPClient::EHTTPAuth)
      {
         handleHTTPError(LicenseStatus_ServerAuthFailed);
      }
      else if(responseStatus == CPCAPI2::HTTPClient::EHTTPEmbedded)
      {
         mUserLicenseMessage = "Failure to load the embedded certificate!! License cannot be verified against the license server.";
         handleHTTPError(LicenseStatus_ServerEmbeddedCert);
      }
      else
      {
         if (!trySecondaryServer(fromSecondaryUrl, "Connection to server failed!"))
         {
            handleHTTPError(LicenseStatus_ServerUnreachable);
         }
      }
      return;
   }

   // In case of non-200 response try secondary server immediately
   if (responseStatus != 200 && trySecondaryServer(fromSecondaryUrl, "Invalid response from Server: " + lexical_cast(responseStatus) + " Error:" + lexical_cast(errorCode)))
   {
      return;
   }

   // Is there any data in the response?
   if (responseBody.size() == 0)
   {
      ErrLog(<< "Licensing::OnHTTPResponse() - No data in response!");
      handleHTTPError(LicenseStatus_ServerBadData);
      return;
   }

   // Is the data XML?
   bool isXML = (contentType.find("application/xml") != std::string::npos);

   // Check for various response statuses, only 2xx is an OK response
   if (responseStatus >= 400)
   {
      if (isXML)
      {
         const std::string document(responseBody);
         const LicenseDocParser parser(document);

         if (parser.type() == LicenseDocParser::Error)
         {
            // Server is rejecting the license key, it may have been revoked or something similar?
            LicDeleteFile(LFT_License);  // Remove the current license document
            SetErrorValuesFromParser(parser);

   #if (CPCAPI2_BRAND_LICENSE_IGNORE_SERVER_ERRORS == 0)

            InternalSetStatus(LicenseStatus_ServerReject);

   #else  /* CPCAPI2_BRAND_LICENSE_IGNORE_SERVER_ERRORS */

            // only reject if license is revoked
            unsigned long revokecode = 1205;
            if (parser.errorCode() == revokecode)
            {
               InternalSetStatus(LicenseStatus_ServerReject);
            }
            else
            {
               InternalSetStatus(LicenseStatus_Valid);
            }
   #endif    /* CPCAPI2_BRAND_LICENSE_IGNORE_SERVER_ERRORS */

            return;
         }
      }
      else
      {
         // Not a good response from Server
         ErrLog(<< "Licensing::OnHTTPResponse() - Non-OK response from Server: " + lexical_cast(responseStatus) + " Error:" + lexical_cast(errorCode));
         handleHTTPError(LicenseStatus_ServerBadData);
      }
      return;
   }
   else if (responseStatus >= 300)
   {
      ErrLog(<< "Licensing::OnHTTPResponse() - Got 3xx class response (" + lexical_cast(responseStatus) + ") -> LicenseStatus_ServerBadData");
      handleHTTPError(LicenseStatus_ServerBadData);
      return;
   }
   else if (responseStatus >= 200)
   {
      if (isXML)
      {
         const std::string document(responseBody);
         const LicenseDocParser parser(document);

         if (parser.type() == LicenseDocParser::Error)
         {

   #if (CPCAPI2_BRAND_LICENSE_IGNORE_SERVER_ERRORS == 1)
            // reject if license is revoked
            unsigned long revokecode = 1205;
            if (parser.errorCode() == revokecode)
            {
               InternalSetStatus(LicenseStatus_ServerReject);
               return;
            }
   #endif   /* CPCAPI2_BRAND_LICENSE_IGNORE_SERVER_ERRORS */

            if (mExpiry == 0)         // Never had a license doc?
            {
               SetAllValuesFromParser(parser);
               InternalSetStatus(LicenseStatus_ServerBadData);
               return;
            }

            if (IsLicenseExpired())   // Local license doc expired?
            {
               SetAllValuesFromParser(parser);

               InternalSetStatus(LicenseStatus_Expired);
               return;
            }
            else
            {
               StartExpiryTimer(mExpiry);   // Restart timer, check again later
               return;
            }
         }
         else if ((parser.type() == LicenseDocParser::Valid) || (parser.type() == LicenseDocParser::Grace))
         {
            // First verify the signature of the document
            unsigned char* psig = NULL;
            int siglen = 0;
            bool isValid = false;
            isValid = mXmlSigVerify->verifySignature(document, &psig, siglen);

            if ( !isValid )
            {
               ErrLog(<< "Licensing::OnHTTPResponse() Bad signature on License Document - Verification fails!");
               handleHTTPError(LicenseStatus_ServerBadData);
               return;
            }

            if (parser.expiry() != mExpiry)
            {
               // We got a new lease time from the server
               ResetAccumulatedRunTime();
               mFirstCheck = true;    // First check refers to the first time after getting a new expiry time
            }

            SetAllValuesFromParser(parser);
            WriteToFile(document, LFT_License);
#if defined(XS_TARGET_OS_WINDOWS)
            // save hardware primitives so next time we can detect if hardware has changed
            CPCAPI2::RegistryHelpers::WriteHardwareToRegistry("h", mAllHdds);
            CPCAPI2::RegistryHelpers::WriteHardwareToRegistry("m", mAllMacs);
#endif
            StartExpiryTimer(mExpiry);

            DoAccumulatedRunTimeCheck();

            if (parser.type() == LicenseDocParser::Valid)
            {
               if (parser.version() < 8 && parser.licenses()[0].type == "Trial")
               {
                  InternalSetStatus(LicenseStatus_TrialMode);
               }
               else
               {
                  InternalSetStatus(LicenseStatus_Valid);
               }
            }
            else
            {
               InternalSetStatus(LicenseStatus_Grace);
            }
            return;
         }
      }
      else
      {
         ErrLog(<< "Licensing::OnHTTPResponse() - Got 200 Ok but content type (" + contentType + ")is not application/xml -> LicenseStatus_ServerBadData");
         handleHTTPError(LicenseStatus_ServerBadData);
         return;
      }
   }
   else
   {
      ErrLog(<< "Licensing::OnHTTPResponse() - Got 1xx class response (" + lexical_cast(responseStatus) + ") -> LicenseStatus_ServerBadData");
      handleHTTPError(LicenseStatus_ServerBadData);
      return;
   }
}

// --- TIMER HANDLING -----------------------------------------------
bool
Licensing::IsLicenseExpired()
{
   time_t now = GetCurrentTimeUTC();

   // Simple check to see if our local PC time is within the license validity period
   if ((now >= mExpiry) || (now < mStartTime))
   {
      return true;
   }

   // Next a check to see if we have accumulated too much Run Time on this license, i.e. user setting the clock back to fool us
   time_t art = ReadAccumulatedRunTime();
   if (art >= mDuration)
   {
      return true;
   }
   return false;
}

time_t
Licensing::GetCurrentTimeUTC() const
{
   time_t now = time(0);

#if 0
   struct std::tm local, utc;
   struct std::tm *t;

   t = localtime(&now);
   memcpy(&local, t, sizeof(struct std::tm));

   t = gmtime(&now);
   memcpy(&utc, t, sizeof(struct std::tm));

   // Adjust for local time zone
   struct std::tm *gm;
   gm = gmtime(&now);
   time_t gm_time = mktime(gm);
   double gmt_offset = difftime(now, gm_time);

   now += static_cast<time_t>(gmt_offset);
#endif

   return now;
}

unsigned long
Licensing::CalcDaysLeft() const
{
   time_t now = GetCurrentTimeUTC();

   if (now >= mExpiry)
   {
      return 0;
   }

   unsigned long secondsLeft = static_cast<unsigned long>(mExpiry) - static_cast<unsigned long>(now);
   unsigned long daysleft = secondsLeft / 86400;
   return daysleft;
}

unsigned long
Licensing::CalcMinutesLeft() const
{
   time_t now = GetCurrentTimeUTC();

   if (now >= mExpiry)
   {
      return 0;
   }

   unsigned long secondsLeft = static_cast<unsigned long>(mExpiry) - static_cast<unsigned long>(now);
   unsigned long minutesLeft = secondsLeft / 60;
   return minutesLeft;
}

void
Licensing::CancelExpiryTimer()
{
   if (mExpiryTimer)
   {
      mExpiryTimer->cancel();
      mReactor->safeDelete(mExpiryTimer);
      mExpiryTimer = NULL;
   }
}

void
Licensing::StartExpiryTimer(const time_t expiry, bool loadCheckTime)
{
   time_t now = GetCurrentTimeUTC();

   CancelExpiryTimer();

   unsigned long intv = 0;

   if (loadCheckTime)
   {
      unsigned long ct = static_cast<unsigned long>(ReadCheckTime());
      unsigned long nt = static_cast<unsigned long>(now);

      if (ct > nt)
      {
         intv = ct - nt;
      }
      else
      {
         intv = 2; // Trigger a new check in 2 seconds
      }
   }
   else
   {
      intv = calcCheckInterval(mFirstCheck, now, expiry, mCheckDelay, mEndPercent, mMinInterval);
   }

   WriteCheckTime(now + intv);

   if (mFirstCheck)
   {
      mFirstCheck = false;
   }

   mExpiryTimer = new resip::DeadlineTimer<resip::MultiReactor>(*mReactor);
   mExpiryTimer->expires_from_now(intv * 1000);
   mExpiryTimer->async_wait(this, TIMER_EXPIRY_ID, NULL);
}

time_t
Licensing::ReadCheckTime()
{
   std::string xml(ReadFromFile(LFT_Auxiliary));
   LicenseAuxiliaryDocParser auxParser(xml);

   if (auxParser.CheckTime().empty())
   {
      return 0;
   }

   return static_cast<time_t>(stringToTime(auxParser.CheckTime()));
}

void
Licensing::WriteCheckTime(const time_t checktime)
{
   LicenseAuxiliaryDocCreater creater(ReadAccumulatedRunTime(), checktime);
   WriteToFile(creater.getXml(), LFT_Auxiliary);
}

void
Licensing::CancelRunTimeTimer()
{
   if (mRTTimer)
   {
      mRTTimer->cancel();
      mReactor->safeDelete(mRTTimer);
      mRTTimer = NULL;
   }
}

void
Licensing::StartRunTimeTimer()
{
   CancelRunTimeTimer();

   mRTTimer = new resip::DeadlineTimer<resip::MultiReactor>(*mReactor);
   mRTTimer->expires_from_now(ACC_RUNTIME_CHECK_INTERVAL * 1000);
   mRTTimer->async_wait(this, TIMER_RUNTIME_ID, NULL);
}

time_t
Licensing::ReadAccumulatedRunTime()
{
   std::string xml(ReadFromFile(LFT_Auxiliary));
   LicenseAuxiliaryDocParser auxParser(xml);

   if (auxParser.AccumulatedRuntime().empty())
   {
      return 0;
   }

   return static_cast<time_t>(stringToTime(auxParser.AccumulatedRuntime()));
}

void
Licensing::WriteAccumulatedRunTime(const time_t art)
{
   LicenseAuxiliaryDocCreater creater(art, ReadCheckTime());
   WriteToFile(creater.getXml(), LFT_Auxiliary);
}

void
Licensing::DoAccumulatedRunTimeCheck()
{
   time_t now = GetCurrentTimeUTC();

   if (mLastRunTimeCheckTime == 0)
   {
      StartRunTimeTimer();
      WriteAccumulatedRunTime(1);
   }
   else
   {
      time_t art = ReadAccumulatedRunTime();

      if (now < mLastRunTimeCheckTime)
      {
         // Clock change while running -> check now
         CancelExpiryTimer();
         SendLicenseRequest();
      }
      else
      {
         art += (now - mLastRunTimeCheckTime);
         WriteAccumulatedRunTime(art);

         if (art >= mDuration)
         {
            // Force check based on how much time we have accumulated running so far
            CancelExpiryTimer();
            SendLicenseRequest();
         }
         else
         {
            StartRunTimeTimer();
         }
      }
   }

   mLastRunTimeCheckTime = now;
}

void
Licensing::ResetAccumulatedRunTime()
{
   CancelRunTimeTimer();
   mLastRunTimeCheckTime = 0;
}

void
Licensing::handleExpiryTimer()
{
   CancelRunTimeTimer();
   SendLicenseRequest();
}

void
Licensing::onTimer(unsigned short id, void* appState)
{
   if (id == TIMER_EXPIRY_ID)
   {
      handleExpiryTimer();
   }
   else if (id == TIMER_RUNTIME_ID)
   {
      DoAccumulatedRunTimeCheck();
   }

   // notify client that license was deemed no longer valid
   if (mLicenseStatus == LicenseStatus_Expired || mLicenseStatus == LicenseStatus_ServerReject)
   {
      sendLicenseEvent();
   }
}

}
}
