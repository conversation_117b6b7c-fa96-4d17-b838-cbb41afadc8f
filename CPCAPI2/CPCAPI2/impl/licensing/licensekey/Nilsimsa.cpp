//
//  nilsimsa.c
//  nilsimsa
//
//  Created by <PERSON><PERSON> on 10/28/2013.
//  Copyright (c) 2013 CounterPath. All rights reserved.
//

extern "C" {
#include "Nilsimsa.h"
}

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

namespace CPCAPI2
{
namespace Licensing
{

const unsigned char TRAN[] = {0x02,0xD6,0x9E,0x6F,0xF9,0x1D,0x04,0xAB,0xD0,0x22,0x16,0x1F,0xD8,0x73,0xA1,0xAC, \
                               0x3B,0x70,0x62,0x96,0x1E,0x6E,0x8F,0x39,0x9D,0x05,0x14,0x4A,0xA6,0xBE,0xAE,0x0E, \
                               0xCF,0xB9,0x9C,0x9A,0xC7,0x68,0x13,0xE1,0x2D,0xA4,0x<PERSON>,0x51,0x8D,0x64,0x6B,0x50, \
                               0x23,0x80,0x03,0x41,0xEC,0xBB,0x71,0xCC,0x7A,0x86,0x7F,0x98,0xF2,0x36,0x5E,0xEE, \
                               0x8E,0xCE,0x4F,0xB8,0x32,0xB6,0x5F,0x59,0xDC,0x1B,0x31,0x4C,0x7B,0xF0,0x63,0x01, \
                               0x6C,0xBA,0x07,0xE8,0x12,0x77,0x49,0x3C,0xDA,0x46,0xFE,0x2F,0x79,0x1C,0x9B,0x30, \
                               0xE3,0x00,0x06,0x7E,0x2E,0x0F,0x38,0x33,0x21,0xAD,0xA5,0x54,0xCA,0xA7,0x29,0xFC, \
                               0x5A,0x47,0x69,0x7D,0xC5,0x95,0xB5,0xF4,0x0B,0x90,0xA3,0x81,0x6D,0x25,0x55,0x35, \
                               0xF5,0x75,0x74,0x0A,0x26,0xBF,0x19,0x5C,0x1A,0xC6,0xFF,0x99,0x5D,0x84,0xAA,0x66, \
                               0x3E,0xAF,0x78,0xB3,0x20,0x43,0xC1,0xED,0x24,0xEA,0xE6,0x3F,0x18,0xF3,0xA0,0x42, \
                               0x57,0x08,0x53,0x60,0xC3,0xC0,0x83,0x40,0x82,0xD7,0x09,0xBD,0x44,0x2A,0x67,0xA8, \
                               0x93,0xE0,0xC2,0x56,0x9F,0xD9,0xDD,0x85,0x15,0xB4,0x8A,0x27,0x28,0x92,0x76,0xDE, \
                               0xEF,0xF8,0xB2,0xB7,0xC9,0x3D,0x45,0x94,0x4B,0x11,0x0D,0x65,0xD5,0x34,0x8B,0x91, \
                               0x0C,0xFA,0x87,0xE9,0x7C,0x5B,0xB1,0x4D,0xE5,0xD4,0xCB,0x10,0xA2,0x17,0x89,0xBC, \
                               0xDB,0xB0,0xE2,0x97,0x88,0x52,0xF7,0x48,0xD3,0x61,0x2C,0x3A,0x2B,0xD1,0x8C,0xFB, \
                               0xF1,0xCD,0xE4,0x6A,0xE7,0xA9,0xFD,0xC4,0x37,0xC8,0xD2,0xF6,0xDF,0x58,0x72,0x4E};


const unsigned char hex[] = {'0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f'};

const unsigned char BITS[] = {0x00,0x01,0x01,0x02,0x01,0x02,0x02,0x03,0x01,0x02,0x02,0x03,0x02,0x03,0x03,0x04, \
                               0x01,0x02,0x02,0x03,0x02,0x03,0x03,0x04,0x02,0x03,0x03,0x04,0x03,0x04,0x04,0x05, \
                               0x01,0x02,0x02,0x03,0x02,0x03,0x03,0x04,0x02,0x03,0x03,0x04,0x03,0x04,0x04,0x05, \
                               0x02,0x03,0x03,0x04,0x03,0x04,0x04,0x05,0x03,0x04,0x04,0x05,0x04,0x05,0x05,0x06, \
                               0x01,0x02,0x02,0x03,0x02,0x03,0x03,0x04,0x02,0x03,0x03,0x04,0x03,0x04,0x04,0x05, \
                               0x02,0x03,0x03,0x04,0x03,0x04,0x04,0x05,0x03,0x04,0x04,0x05,0x04,0x05,0x05,0x06, \
                               0x02,0x03,0x03,0x04,0x03,0x04,0x04,0x05,0x03,0x04,0x04,0x05,0x04,0x05,0x05,0x06, \
                               0x03,0x04,0x04,0x05,0x04,0x05,0x05,0x06,0x04,0x05,0x05,0x06,0x05,0x06,0x06,0x07, \
                               0x01,0x02,0x02,0x03,0x02,0x03,0x03,0x04,0x02,0x03,0x03,0x04,0x03,0x04,0x04,0x05, \
                               0x02,0x03,0x03,0x04,0x03,0x04,0x04,0x05,0x03,0x04,0x04,0x05,0x04,0x05,0x05,0x06, \
                               0x02,0x03,0x03,0x04,0x03,0x04,0x04,0x05,0x03,0x04,0x04,0x05,0x04,0x05,0x05,0x06, \
                               0x03,0x04,0x04,0x05,0x04,0x05,0x05,0x06,0x04,0x05,0x05,0x06,0x05,0x06,0x06,0x07, \
                               0x02,0x03,0x03,0x04,0x03,0x04,0x04,0x05,0x03,0x04,0x04,0x05,0x04,0x05,0x05,0x06, \
                               0x03,0x04,0x04,0x05,0x04,0x05,0x05,0x06,0x04,0x05,0x05,0x06,0x05,0x06,0x06,0x07, \
                               0x03,0x04,0x04,0x05,0x04,0x05,0x05,0x06,0x04,0x05,0x05,0x06,0x05,0x06,0x06,0x07, \
                               0x04,0x05,0x05,0x06,0x05,0x06,0x06,0x07,0x05,0x06,0x06,0x07,0x06,0x07,0x07,0x08};

//ns_obj
//ns_create_object()
//{
//   ns_obj o;
//   memset(o.acc, 0, 256*sizeof(unsigned short));
//   memset(o.signature, 0, 65*sizeof(char));
//   o.count = 0;
//   return o;
//}


extern "C" {
unsigned char tran3(const char a, const char b, const char c, const char n)
{
   unsigned char idx = (((TRAN[(a+n)&255]^TRAN[b]*(n+n+1))+TRAN[(c)^TRAN[n]])&255);
   return idx;
}


void
ns_add_data(ns_obj *o, const char* in)
{
   size_t l = strlen(in);
   char *str = (char*)malloc(l>5?l:5);
   memset(str, 0, l>5?l:5);
   memcpy(str, in, l);

   l = l>5?l:5;

   for (unsigned int i=0; i<l-4; i++)
   {

      o->acc[tran3(str[i+0], str[i+1], str[i+2], 0)]++;
      o->acc[tran3(str[i+0], str[i+1], str[i+3], 1)]++;
      o->acc[tran3(str[i+0], str[i+1], str[i+4], 2)]++;
      o->acc[tran3(str[i+0], str[i+2], str[i+3], 3)]++;
      o->acc[tran3(str[i+0], str[i+2], str[i+4], 4)]++;
      o->acc[tran3(str[i+0], str[i+3], str[i+4], 5)]++;
      o->acc[tran3(str[i+1], str[i+2], str[i+3], 6)]++;
      o->acc[tran3(str[i+1], str[i+2], str[i+4], 7)]++;
      o->acc[tran3(str[i+1], str[i+3], str[i+4], 8)]++;
      o->acc[tran3(str[i+2], str[i+3], str[i+4], 9)]++;

      o->count += 10;
   }
   free(str);
}


void
ns_create_signature(ns_obj *o)
{


   unsigned char digest[32];
   memset(digest, 0, 32*sizeof(char));

   unsigned short trs = (unsigned short)(o->count / 256);
   for (int i=0; i<32; i++)
   {
      unsigned char bf = 0x00;
      for (int j=0; j<8; j++)
      {
         if (o->acc[i*8+j] > trs) {
            bf |= (0x01 << (7-j));
         }
      }
      digest[i] = bf;
   }

   for (int i=0; i<32; i++)
   {
      o->signature[i*2] = hex[(digest[i]&0xf0)>>4];
      o->signature[i*2+1] = hex[(digest[i]&0x0f)];
   }
}

//
//unsigned char ns_compare(const char* s1, const char* s2)
//{
//   size_t l = strlen(s1);
//   size_t m = strlen(s2);
//
//   if (l != m || l != 64) {
//      return 0x00;            // if any of the preconditions are not fulfilled then we regard the result as maximum dissimilar
//   }
//
//   unsigned char diff = 0x00;
//
//   for (int i=0; i<64; i++)
//   {
//      diff += BITS[s1[i]^s2[i]];
//   }
//
//   return 0xff - diff;
//}

}

}
}