// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef SipConversation_JSON_HELPER_H
#define SipConversation_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/public/call/SipConversationState.h>
#include <interface/public/call/SipConversationTypes.h>
#include <impl/call/SipConversationHandlerInternal.h>
#include <interface/public/call/SipConversationState.h>
#include <interface/experimental/call/SipConversationManagerExt.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::AudioCodec& v)
    {
      w.StartObject();
      Write(w, "pltype", v.pltype);
      Write(w, "plname", v.plname);
      Write(w, "plfreq", v.plfreq);
      Write(w, "pacsize", v.pacsize);
      Write(w, "channels", v.channels);
      Write(w, "rate", v.rate);
      Write(w, "priority", v.priority);
      Write(w, "displayName", v.displayName);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::AudioCodec& v)
    {
      Read(json, "pltype", v.pltype);
      Read(json, "plname", v.plname);
      Read(json, "plfreq", v.plfreq);
      Read(json, "pacsize", v.pacsize);
      Read(json, "channels", v.channels);
      Read(json, "rate", v.rate);
      Read(json, "priority", v.priority);
      Read(json, "displayName", v.displayName);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::VideoCodec& v)
    {
      w.StartObject();
      Write(w, "plName", v.plName);
      Write(w, "plType", v.plType);
      Write(w, "width", v.width);
      Write(w, "height", v.height);
      Write(w, "startBitrate", v.startBitrate);
      Write(w, "maxBitrate", v.maxBitrate);
      Write(w, "minBitrate", v.minBitrate);
      Write(w, "maxFramerate", v.maxFramerate);
      Write(w, "hadwareAccelerated", v.hadwareAccelerated);
      Write(w, "priority", v.priority);
      Write(w, "displayName", v.displayName);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::VideoCodec& v)
    {
      Read(json, "plName", v.plName);
      Read(json, "plType", v.plType);
      Read(json, "width", v.width);
      Read(json, "height", v.height);
      Read(json, "startBitrate", v.startBitrate);
      Read(json, "maxBitrate", v.maxBitrate);
      Read(json, "minBitrate", v.minBitrate);
      Read(json, "maxFramerate", v.maxFramerate);
      Read(json, "hadwareAccelerated", v.hadwareAccelerated);
      Read(json, "priority", v.priority);
      Read(json, "displayName", v.displayName);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::IPEndpoint& v)
    {
      w.StartObject();
      Write(w, "ipAddress", v.ipAddress);
      Write(w, "port", v.port);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::IPEndpoint& v)
    {
      Read(json, "ipAddress", v.ipAddress);
      Read(json, "port", v.port);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::StreamStatistics& v)
    {
      w.StartObject();
      Write(w, "fractionLost", v.fractionLost);
      Write(w, "cumulativeLost", v.cumulativeLost);
      Write(w, "extendedMax", v.extendedMax);
      Write(w, "jitterSamples", v.jitterSamples);
      Write(w, "rttMs", v.rttMs);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::StreamStatistics& v)
    {
      Read(json, "fractionLost", v.fractionLost);
      Read(json, "cumulativeLost", v.cumulativeLost);
      Read(json, "extendedMax", v.extendedMax);
      Read(json, "jitterSamples", v.jitterSamples);
      Read(json, "rttMs", v.rttMs);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::StreamDataCounters& v)
    {
      w.StartObject();
      Write(w, "bytesSent", v.bytesSent);
      Write(w, "packetsSent", v.packetsSent);
      Write(w, "bytesReceived", v.bytesReceived);
      Write(w, "packetsReceived", v.packetsReceived);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::StreamDataCounters& v)
    {
      Read(json, "bytesSent", v.bytesSent);
      Read(json, "packetsSent", v.packetsSent);
      Read(json, "bytesReceived", v.bytesReceived);
      Read(json, "packetsReceived", v.packetsReceived);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::XRVoipMetrics& v)
    {
      w.StartObject();
      Write(w, "lossRate", v.lossRate);
      Write(w, "discardRate", v.discardRate);
      Write(w, "burstDensity", v.burstDensity);
      Write(w, "gapDensity", v.gapDensity);
      Write(w, "burstDuration", v.burstDuration);
      Write(w, "gapDuration", v.gapDuration);
      Write(w, "roundTripDelay", v.roundTripDelay);
      Write(w, "endSystemDelay", v.endSystemDelay);
      Write(w, "signalLevel", v.signalLevel);
      Write(w, "noiseLevel", v.noiseLevel);
      Write(w, "RERL", v.RERL);
      Write(w, "Gmin", v.Gmin);
      Write(w, "Rfactor", v.Rfactor);
      Write(w, "extRfactor", v.extRfactor);
      Write(w, "MOSLQ", v.MOSLQ);
      Write(w, "MOSCQ", v.MOSCQ);
      Write(w, "RXconfig", v.RXconfig);
      Write(w, "JBnominal", v.JBnominal);
      Write(w, "JBmax", v.JBmax);
      Write(w, "JBabsMax", v.JBabsMax);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::XRVoipMetrics& v)
    {
      Read(json, "lossRate", v.lossRate);
      Read(json, "discardRate", v.discardRate);
      Read(json, "burstDensity", v.burstDensity);
      Read(json, "gapDensity", v.gapDensity);
      Read(json, "burstDuration", v.burstDuration);
      Read(json, "gapDuration", v.gapDuration);
      Read(json, "roundTripDelay", v.roundTripDelay);
      Read(json, "endSystemDelay", v.endSystemDelay);
      Read(json, "signalLevel", v.signalLevel);
      Read(json, "noiseLevel", v.noiseLevel);
      Read(json, "RERL", v.RERL);
      Read(json, "Gmin", v.Gmin);
      Read(json, "Rfactor", v.Rfactor);
      Read(json, "extRfactor", v.extRfactor);
      Read(json, "MOSLQ", v.MOSLQ);
      Read(json, "MOSCQ", v.MOSCQ);
      Read(json, "RXconfig", v.RXconfig);
      Read(json, "JBnominal", v.JBnominal);
      Read(json, "JBmax", v.JBmax);
      Read(json, "JBabsMax", v.JBabsMax);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::XRStatisticsSummary& v)
    {
      w.StartObject();
      Write(w, "begin_seq", v.begin_seq);
      Write(w, "end_seq", v.end_seq);
      Write(w, "lost_packets", v.lost_packets);
      Write(w, "dup_packets", v.dup_packets);
      Write(w, "min_jitter", v.min_jitter);
      Write(w, "max_jitter", v.max_jitter);
      Write(w, "mean_jitter", v.mean_jitter);
      Write(w, "dev_jitter", v.dev_jitter);
      Write(w, "min_ttl_or_hl", v.min_ttl_or_hl);
      Write(w, "max_ttl_or_hl", v.max_ttl_or_hl);
      Write(w, "mean_ttl_or_hl", v.mean_ttl_or_hl);
      Write(w, "dev_ttl_or_hl", v.dev_ttl_or_hl);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::XRStatisticsSummary& v)
    {
      Read(json, "begin_seq", v.begin_seq);
      Read(json, "end_seq", v.end_seq);
      Read(json, "lost_packets", v.lost_packets);
      Read(json, "dup_packets", v.dup_packets);
      Read(json, "min_jitter", v.min_jitter);
      Read(json, "max_jitter", v.max_jitter);
      Read(json, "mean_jitter", v.mean_jitter);
      Read(json, "dev_jitter", v.dev_jitter);
      Read(json, "min_ttl_or_hl", v.min_ttl_or_hl);
      Read(json, "max_ttl_or_hl", v.max_ttl_or_hl);
      Read(json, "mean_ttl_or_hl", v.mean_ttl_or_hl);
      Read(json, "dev_ttl_or_hl", v.dev_ttl_or_hl);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::AudioStatistics& v)
    {
      w.StartObject();
      Write(w, "encoder", v.encoder);
      Write(w, "decoder", v.decoder);
      Write(w, "streamStatistics", v.streamStatistics);
      Write(w, "streamDataCounters", v.streamDataCounters);
      Write(w, "maxJitterMs", v.maxJitterMs);
      Write(w, "averageJitterMs", v.averageJitterMs);
      Write(w, "discardedPackets", v.discardedPackets);
      Write(w, "XRvoipMetrics", v.XRvoipMetrics);
      Write(w, "XRstatisticsSummary", v.XRstatisticsSummary);
      Write(w, "intervalCallQualityReport", v.intervalCallQualityReport);
      Write(w, "callStartTimeNTP", v.callStartTimeNTP);
      Write(w, "endpoint", v.endpoint);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::AudioStatistics& v)
    {
      Read(json, "encoder", v.encoder);
      Read(json, "decoder", v.decoder);
      Read(json, "streamStatistics", v.streamStatistics);
      Read(json, "streamDataCounters", v.streamDataCounters);
      Read(json, "maxJitterMs", v.maxJitterMs);
      Read(json, "averageJitterMs", v.averageJitterMs);
      Read(json, "discardedPackets", v.discardedPackets);
      Read(json, "XRvoipMetrics", v.XRvoipMetrics);
      Read(json, "XRstatisticsSummary", v.XRstatisticsSummary);
      Read(json, "intervalCallQualityReport", v.intervalCallQualityReport);
      Read(json, "callStartTimeNTP", v.callStartTimeNTP);
      Read(json, "endpoint", v.endpoint);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::RemoteAudioStatistics& v)
    {
      w.StartObject();
      Write(w, "sender_SSRC", v.sender_SSRC);
      Write(w, "source_SSRC", v.source_SSRC);
      Write(w, "streamStatistics", v.streamStatistics);
      Write(w, "XRvoipMetrics", v.XRvoipMetrics);
      Write(w, "XRstatisticsSummary", v.XRstatisticsSummary);
      Write(w, "endpoint", v.endpoint);
      Write(w, "lastRtcpReceived", v.lastRtcpReceived);
      Write(w, "lastRtcpXrReceived", v.lastRtcpXrReceived);
      Write(w, "lastSenderReportReceived", v.lastSenderReportReceived);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::RemoteAudioStatistics& v)
    {
      Read(json, "sender_SSRC", v.sender_SSRC);
      Read(json, "source_SSRC", v.source_SSRC);
      Read(json, "streamStatistics", v.streamStatistics);
      Read(json, "XRvoipMetrics", v.XRvoipMetrics);
      Read(json, "XRstatisticsSummary", v.XRstatisticsSummary);
      Read(json, "endpoint", v.endpoint);
      Read(json, "lastRtcpReceived", v.lastRtcpReceived);
      Read(json, "lastRtcpXrReceived", v.lastRtcpXrReceived);
      Read(json, "lastSenderReportReceived", v.lastSenderReportReceived);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::VideoStatistics& v)
    {
      w.StartObject();
      Write(w, "encoder", v.encoder);
      Write(w, "decoder", v.decoder);
      Write(w, "streamStatistics", v.streamStatistics);
      Write(w, "streamDataCounters", v.streamDataCounters);
      Write(w, "totalBitrateSent", v.totalBitrateSent);
      Write(w, "videoBitrateSent", v.videoBitrateSent);
      Write(w, "fecBitrateSent", v.fecBitrateSent);
      Write(w, "nackBitrateSent", v.nackBitrateSent);
      Write(w, "discardedPackets", v.discardedPackets);
      Write(w, "currentTargetBitrate", v.currentTargetBitrate);
      Write(w, "endpoint", v.endpoint);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::VideoStatistics& v)
    {
      Read(json, "encoder", v.encoder);
      Read(json, "decoder", v.decoder);
      Read(json, "streamStatistics", v.streamStatistics);
      Read(json, "streamDataCounters", v.streamDataCounters);
      Read(json, "totalBitrateSent", v.totalBitrateSent);
      Read(json, "videoBitrateSent", v.videoBitrateSent);
      Read(json, "fecBitrateSent", v.fecBitrateSent);
      Read(json, "nackBitrateSent", v.nackBitrateSent);
      Read(json, "discardedPackets", v.discardedPackets);
      Read(json, "currentTargetBitrate", v.currentTargetBitrate);
      Read(json, "endpoint", v.endpoint);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::RemoteVideoStatistics& v)
    {
      w.StartObject();
      Write(w, "streamStatistics", v.streamStatistics);
      Write(w, "endpoint", v.endpoint);
      Write(w, "lastRtcpReceived", v.lastRtcpReceived);
      Write(w, "lastSenderReportReceived", v.lastSenderReportReceived);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::RemoteVideoStatistics& v)
    {
      Read(json, "streamStatistics", v.streamStatistics);
      Read(json, "endpoint", v.endpoint);
      Read(json, "lastRtcpReceived", v.lastRtcpReceived);
      Read(json, "lastSenderReportReceived", v.lastSenderReportReceived);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::AudioJitterBufferStatistics& v)
    {
      w.StartObject();
      Write(w, "currentBufferSizeMs", v.currentBufferSizeMs);
      Write(w, "preferredBufferSizeMs", v.preferredBufferSizeMs);
      Write(w, "jitterBurstsFound", v.jitterBurstsFound);
      Write(w, "currentEffectivePacketLossRate", v.currentEffectivePacketLossRate);
      Write(w, "currentDiscardRate", v.currentDiscardRate);
      Write(w, "currentSynthesizedAudioInsertRate", v.currentSynthesizedAudioInsertRate);
      Write(w, "currentSynthesizedAudioPreemptiveInsertRate", v.currentSynthesizedAudioPreemptiveInsertRate);
      Write(w, "currentAccelerateRate", v.currentAccelerateRate);
      Write(w, "clockDriftPPM", v.clockDriftPPM);
      Write(w, "meanWaitingTimeMs", v.meanWaitingTimeMs);
      Write(w, "medianWaitingTimeMs", v.medianWaitingTimeMs);
      Write(w, "minWaitingTimeMs", v.minWaitingTimeMs);
      Write(w, "maxWaitingTimeMs", v.maxWaitingTimeMs);
      Write(w, "addedSamples", v.addedSamples);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::AudioJitterBufferStatistics& v)
    {
      Read(json, "currentBufferSizeMs", v.currentBufferSizeMs);
      Read(json, "preferredBufferSizeMs", v.preferredBufferSizeMs);
      Read(json, "jitterBurstsFound", v.jitterBurstsFound);
      Read(json, "currentEffectivePacketLossRate", v.currentEffectivePacketLossRate);
      Read(json, "currentDiscardRate", v.currentDiscardRate);
      Read(json, "currentSynthesizedAudioInsertRate", v.currentSynthesizedAudioInsertRate);
      Read(json, "currentSynthesizedAudioPreemptiveInsertRate", v.currentSynthesizedAudioPreemptiveInsertRate);
      Read(json, "currentAccelerateRate", v.currentAccelerateRate);
      Read(json, "clockDriftPPM", v.clockDriftPPM);
      Read(json, "meanWaitingTimeMs", v.meanWaitingTimeMs);
      Read(json, "medianWaitingTimeMs", v.medianWaitingTimeMs);
      Read(json, "minWaitingTimeMs", v.minWaitingTimeMs);
      Read(json, "maxWaitingTimeMs", v.maxWaitingTimeMs);
      Read(json, "addedSamples", v.addedSamples);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::VideoJitterBufferStatistics& v)
    {
      w.StartObject();
      Write(w, "numDecodedKeyFrames", v.numDecodedKeyFrames);
      Write(w, "numDecodedDeltaFrames", v.numDecodedDeltaFrames);
      Write(w, "currentBufferSizeMs", v.currentBufferSizeMs);
      Write(w, "currentDiscardRate", v.currentDiscardRate);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::VideoJitterBufferStatistics& v)
    {
      Read(json, "numDecodedKeyFrames", v.numDecodedKeyFrames);
      Read(json, "numDecodedDeltaFrames", v.numDecodedDeltaFrames);
      Read(json, "currentBufferSizeMs", v.currentBufferSizeMs);
      Read(json, "currentDiscardRate", v.currentDiscardRate);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::MediaEncryptionOptions& v)
    {
      w.StartObject();
      Write(w, "mediaEncryptionMode", v.mediaEncryptionMode);
      Write(w, "secureMediaRequired", v.secureMediaRequired);
      Write(w, "mediaCryptoSuites", v.mediaCryptoSuites);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::MediaEncryptionOptions& v)
    {
      Read(json, "mediaEncryptionMode", v.mediaEncryptionMode);
      Read(json, "secureMediaRequired", v.secureMediaRequired);
      Read(json, "mediaCryptoSuites", v.mediaCryptoSuites);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::MediaInfo& v)
    {
      w.StartObject();
      Write(w, "mediaType", v.mediaType);
      Write(w, "mediaDirection", v.mediaDirection);
      Write(w, "mediaCrypto", v.mediaCrypto);
      Write(w, "mediaEncryptionOptions", v.mediaEncryptionOptions);
      Write(w, "audioCodec", v.audioCodec);
      Write(w, "videoCodec", v.videoCodec);
      Write(w, "conferenceMixContribution", v.conferenceMixContribution);
      Write(w, "isLocallyDisabled", v.isLocallyDisabled);
      Write(w, "conferenceMixId", v.conferenceMixId);
      Write(w, "mediaStreamId", v.mediaStreamId);
      Write(w, "videoCaptureDeviceId", v.videoCaptureDeviceId);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::MediaInfo& v)
    {
      Read(json, "mediaType", v.mediaType);
      Read(json, "mediaDirection", v.mediaDirection);
      Read(json, "mediaCrypto", v.mediaCrypto);
      Read(json, "mediaEncryptionOptions", v.mediaEncryptionOptions);
      Read(json, "audioCodec", v.audioCodec);
      Read(json, "videoCodec", v.videoCodec);
      Read(json, "conferenceMixContribution", v.conferenceMixContribution);
      Read(json, "isLocallyDisabled", v.isLocallyDisabled);
      Read(json, "conferenceMixId", v.conferenceMixId);
      Read(json, "mediaStreamId", v.mediaStreamId);
      Read(json, "videoCaptureDeviceId", v.videoCaptureDeviceId);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::ConversationStatistics& v)
    {
      w.StartObject();
      Write(w, "audioChannels", v.audioChannels);
      Write(w, "remoteAudioChannels", v.remoteAudioChannels);
      Write(w, "videoChannels", v.videoChannels);
      Write(w, "remoteVideoChannels", v.remoteVideoChannels);
      Write(w, "callQuality", v.callQuality);
      Write(w, "networkMos", v.networkMos);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::ConversationStatistics& v)
    {
      Read(json, "audioChannels", v.audioChannels);
      Read(json, "remoteAudioChannels", v.remoteAudioChannels);
      Read(json, "videoChannels", v.videoChannels);
      Read(json, "remoteVideoChannels", v.remoteVideoChannels);
      Read(json, "callQuality", v.callQuality);
      Read(json, "networkMos", v.networkMos);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::JitterBufferStatistics& v)
    {
      w.StartObject();
      Write(w, "audioChannels", v.audioChannels);
      Write(w, "videoChannels", v.videoChannels);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::JitterBufferStatistics& v)
    {
      Read(json, "audioChannels", v.audioChannels);
      Read(json, "videoChannels", v.videoChannels);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::AnswerModeSettings& v)
    {
      w.StartObject();
      Write(w, "mode", v.mode);
      Write(w, "privileged", v.privileged);
      Write(w, "required", v.required);
      Write(w, "challenge", v.challenge);
      Write(w, "allowManual", v.allowManual);
      Write(w, "allowAuto", v.allowAuto);
      Write(w, "allowPrivileged", v.allowPrivileged);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::AnswerModeSettings& v)
    {
      Read(json, "mode", v.mode);
      Read(json, "privileged", v.privileged);
      Read(json, "required", v.required);
      Read(json, "challenge", v.challenge);
      Read(json, "allowManual", v.allowManual);
      Read(json, "allowAuto", v.allowAuto);
      Read(json, "allowPrivileged", v.allowPrivileged);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::SessionDescription& v)
    {
      w.StartObject();
      Write(w, "sdpString", v.sdpString);
      Write(w, "sdpLen", v.sdpLen);
      Write(w, "sdpType", v.sdpType);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::SessionDescription& v)
    {
      Read(json, "sdpString", v.sdpString);
      Read(json, "sdpLen", v.sdpLen);
      Read(json, "sdpType", v.sdpType);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversationSettings& v)
    {
      w.StartObject();
      Write(w, "sessionName", v.sessionName);
      Write(w, "natTraversalMode", v.natTraversalMode);
      Write(w, "natTraversalServerSource", v.natTraversalServerSource);
      Write(w, "natTraversalServer", v.natTraversalServer);
      Write(w, "natTraversalServerType", v.natTraversalServerType);
      Write(w, "holdMode", v.holdMode);
      Write(w, "prackMode", v.prackMode);
      Write(w, "answerMode", v.answerMode);
      Write(w, "networkChangeHandoverMode", v.networkChangeHandoverMode);
      Write(w, "networkChangeHandoverStarcode", v.networkChangeHandoverStarcode);
      Write(w, "minRtpPort", v.minRtpPort);
      Write(w, "maxRtpPort", v.maxRtpPort);
      Write(w, "minRtpPortAudio", v.minRtpPortAudio);
      Write(w, "maxRtpPortAudio", v.maxRtpPortAudio);
      Write(w, "minRtpPortVideo", v.minRtpPortVideo);
      Write(w, "maxRtpPortVideo", v.maxRtpPortVideo);
      Write(w, "turnUsername", v.turnUsername);
      Write(w, "turnPassword", v.turnPassword);
      Write(w, "includePPreferredIdentity", v.includePPreferredIdentity);
      Write(w, "includePAssertedIdentity", v.includePAssertedIdentity);
      Write(w, "includeAttribsForStaticPLs", v.includeAttribsForStaticPLs);
      Write(w, "adornTransferMessages", v.adornTransferMessages);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversationSettings& v)
    {
      Read(json, "sessionName", v.sessionName);
      Read(json, "natTraversalMode", v.natTraversalMode);
      Read(json, "natTraversalServerSource", v.natTraversalServerSource);
      Read(json, "natTraversalServer", v.natTraversalServer);
      Read(json, "natTraversalServerType", v.natTraversalServerType);
      Read(json, "holdMode", v.holdMode);
      Read(json, "prackMode", v.prackMode);
      Read(json, "answerMode", v.answerMode);
      Read(json, "networkChangeHandoverMode", v.networkChangeHandoverMode);
      Read(json, "networkChangeHandoverStarcode", v.networkChangeHandoverStarcode);
      Read(json, "minRtpPort", v.minRtpPort);
      Read(json, "maxRtpPort", v.maxRtpPort);
      Read(json, "minRtpPortAudio", v.minRtpPortAudio);
      Read(json, "maxRtpPortAudio", v.maxRtpPortAudio);
      Read(json, "minRtpPortVideo", v.minRtpPortVideo);
      Read(json, "maxRtpPortVideo", v.maxRtpPortVideo);
      Read(json, "turnUsername", v.turnUsername);
      Read(json, "turnPassword", v.turnPassword);
      Read(json, "includePPreferredIdentity", v.includePPreferredIdentity);
      Read(json, "includePAssertedIdentity", v.includePAssertedIdentity);
      Read(json, "includeAttribsForStaticPLs", v.includeAttribsForStaticPLs);
      Read(json, "adornTransferMessages", v.adornTransferMessages);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::SipConversationState& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      Write(w, "conversation", v.conversation);
      Write(w, "conversationState", v.conversationState);
      Write(w, "conversationType", v.conversationType);
      Write(w, "localAddress", v.localAddress);
      Write(w, "localDisplayName", v.localDisplayName);
      Write(w, "remoteAddress", v.remoteAddress);
      Write(w, "remoteDisplayName", v.remoteDisplayName);
      Write(w, "remoteMediaInfo", v.remoteMediaInfo);
      Write(w, "localMediaInfo", v.localMediaInfo);
      Write(w, "localHold", v.localHold);
      Write(w, "remoteHold", v.remoteHold);
      Write(w, "endReason", v.endReason);
      Write(w, "statistics", v.statistics);
      Write(w, "jitterBufferStatistics", v.jitterBufferStatistics);
      Write(w, "answerMode", v.answerMode);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::SipConversationState& v)
    {
      Read(json, "account", v.account);
      Read(json, "conversation", v.conversation);
      Read(json, "conversationState", v.conversationState);
      Read(json, "conversationType", v.conversationType);
      Read(json, "localAddress", v.localAddress);
      Read(json, "localDisplayName", v.localDisplayName);
      Read(json, "remoteAddress", v.remoteAddress);
      Read(json, "remoteDisplayName", v.remoteDisplayName);
      Read(json, "remoteMediaInfo", v.remoteMediaInfo);
      Read(json, "localMediaInfo", v.localMediaInfo);
      Read(json, "localHold", v.localHold);
      Read(json, "remoteHold", v.remoteHold);
      Read(json, "endReason", v.endReason);
      Read(json, "statistics", v.statistics);
      Read(json, "jitterBufferStatistics", v.jitterBufferStatistics);
      Read(json, "answerMode", v.answerMode);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::SipConversationStateManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::SipConversationStateManager& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::NewConversationEvent& v)
    {
      w.StartObject();
      Write(w, "conversationState", v.conversationState);
      Write(w, "conversationType", v.conversationType);
      Write(w, "localAddress", v.localAddress);
      Write(w, "localDisplayName", v.localDisplayName);
      Write(w, "remoteAddress", v.remoteAddress);
      Write(w, "remoteDisplayName", v.remoteDisplayName);
      Write(w, "localMediaInfo", v.localMediaInfo);
      Write(w, "remoteMediaInfo", v.remoteMediaInfo);
      Write(w, "relatedConversation", v.relatedConversation);
      Write(w, "conversationToReplace", v.conversationToReplace);
      Write(w, "conversationToJoin", v.conversationToJoin);
      Write(w, "account", v.account);
      Write(w, "autoAnswer", v.autoAnswer);
      Write(w, "isCodecsMismatched", v.isCodecsMismatched);
      Write(w, "isAudioCodecsMismatched", v.isAudioCodecsMismatched);
      Write(w, "isVideoCodecsMismatched", v.isVideoCodecsMismatched);
      Write(w, "alertInfoHeader", v.alertInfoHeader);
      Write(w, "answerMode", v.answerMode);
      Write(w, "sipMessage", v.sipMessage);
      Write(w, "sessionDescription", v.sessionDescription);
      Write(w, "resourcePriority", v.resourcePriority);
      Write(w, "historyInfo", v.historyInfo);
      Write(w, "pCalledPartyIdAddress", v.pCalledPartyIdAddress);
      Write(w, "pCalledPartyIdDisplayname", v.pCalledPartyIdDisplayname);
      Write(w, "sessionId", v.sessionId);
      Write(w, "referredByAddress", v.referredByAddress);
      Write(w, "referredByDisplayname", v.referredByDisplayname);
      Write(w, "diversionAddress", v.diversionAddress);
      Write(w, "diversionDisplayname", v.diversionDisplayname);
      Write(w, "diversionReason", v.diversionReason);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::NewConversationEvent& v)
    {
      Read(json, "conversationState", v.conversationState);
      Read(json, "conversationType", v.conversationType);
      Read(json, "localAddress", v.localAddress);
      Read(json, "localDisplayName", v.localDisplayName);
      Read(json, "remoteAddress", v.remoteAddress);
      Read(json, "remoteDisplayName", v.remoteDisplayName);
      Read(json, "localMediaInfo", v.localMediaInfo);
      Read(json, "remoteMediaInfo", v.remoteMediaInfo);
      Read(json, "relatedConversation", v.relatedConversation);
      Read(json, "conversationToReplace", v.conversationToReplace);
      Read(json, "conversationToJoin", v.conversationToJoin);
      Read(json, "account", v.account);
      Read(json, "autoAnswer", v.autoAnswer);
      Read(json, "isCodecsMismatched", v.isCodecsMismatched);
      Read(json, "isAudioCodecsMismatched", v.isAudioCodecsMismatched);
      Read(json, "isVideoCodecsMismatched", v.isVideoCodecsMismatched);
      Read(json, "alertInfoHeader", v.alertInfoHeader);
      Read(json, "answerMode", v.answerMode);
      Read(json, "sipMessage", v.sipMessage);
      Read(json, "sessionDescription", v.sessionDescription);
      Read(json, "resourcePriority", v.resourcePriority);
      Read(json, "historyInfo", v.historyInfo);
      Read(json, "pCalledPartyIdAddress", v.pCalledPartyIdAddress);
      Read(json, "pCalledPartyIdDisplayname", v.pCalledPartyIdDisplayname);
      Read(json, "sessionId", v.sessionId);
      Read(json, "referredByAddress", v.referredByAddress);
      Read(json, "referredByDisplayname", v.referredByDisplayname);
      Read(json, "diversionAddress", v.diversionAddress);
      Read(json, "diversionDisplayname", v.diversionDisplayname);
      Read(json, "diversionReason", v.diversionReason);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::ConversationEndedEvent& v)
    {
      w.StartObject();
      Write(w, "conversationState", v.conversationState);
      Write(w, "endReason", v.endReason);
      Write(w, "sipResponseCode", v.sipResponseCode);
      Write(w, "responseTimeMs", v.responseTimeMs);
      Write(w, "signallingEndEvent", v.signallingEndEvent);
      Write(w, "signallingEndReason", v.signallingEndReason);
      Write(w, "signallingEndWarningCode", v.signallingEndWarningCode);
      Write(w, "signallingEndWarning", v.signallingEndWarning);
      Write(w, "callQualityReport", v.callQualityReport);
      Write(w, "sessionId", v.sessionId);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::ConversationEndedEvent& v)
    {
      Read(json, "conversationState", v.conversationState);
      Read(json, "endReason", v.endReason);
      Read(json, "sipResponseCode", v.sipResponseCode);
      Read(json, "responseTimeMs", v.responseTimeMs);
      Read(json, "signallingEndEvent", v.signallingEndEvent);
      Read(json, "signallingEndReason", v.signallingEndReason);
      Read(json, "signallingEndWarningCode", v.signallingEndWarningCode);
      Read(json, "signallingEndWarning", v.signallingEndWarning);
      Read(json, "callQualityReport", v.callQualityReport);
      Read(json, "sessionId", v.sessionId);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::TransferRequestEvent& v)
    {
      w.StartObject();
      Write(w, "transferTargetAddress", v.transferTargetAddress);
      Write(w, "transferTargetDisplayName", v.transferTargetDisplayName);
      Write(w, "transferTargetConversation", v.transferTargetConversation);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::TransferRequestEvent& v)
    {
      Read(json, "transferTargetAddress", v.transferTargetAddress);
      Read(json, "transferTargetDisplayName", v.transferTargetDisplayName);
      Read(json, "transferTargetConversation", v.transferTargetConversation);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::TransferProgressEvent& v)
    {
      w.StartObject();
      Write(w, "progressEventType", v.progressEventType);
      Write(w, "sipResponseCode", v.sipResponseCode);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::TransferProgressEvent& v)
    {
      Read(json, "progressEventType", v.progressEventType);
      Read(json, "sipResponseCode", v.sipResponseCode);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::TransferResponseEvent& v)
    {
      w.StartObject();
      Write(w, "sipResponseCode", v.sipResponseCode);
      Write(w, "warningHeader", v.warningHeader);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::TransferResponseEvent& v)
    {
      Read(json, "sipResponseCode", v.sipResponseCode);
      Read(json, "warningHeader", v.warningHeader);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::RedirectRequestEvent& v)
    {
      w.StartObject();
      Write(w, "targetAddress", v.targetAddress);
      Write(w, "reason", v.reason);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::RedirectRequestEvent& v)
    {
      Read(json, "targetAddress", v.targetAddress);
      Read(json, "reason", v.reason);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::TargetChangeRequestEvent& v)
    {
      w.StartObject();
      Write(w, "targetAddress", v.targetAddress);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::TargetChangeRequestEvent& v)
    {
      Read(json, "targetAddress", v.targetAddress);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::HangupRequestEvent& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::HangupRequestEvent& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::BroadsoftTalkEvent& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::BroadsoftTalkEvent& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::BroadsoftHoldEvent& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::BroadsoftHoldEvent& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::ConversationStateChangeRequestEvent& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::ConversationStateChangeRequestEvent& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::ConversationStateChangedEvent& v)
    {
      w.StartObject();
      Write(w, "conversationState", v.conversationState);
      Write(w, "dialogId", v.dialogId);
      Write(w, "contactHeaderField", v.contactHeaderField);
      Write(w, "remoteAddress", v.remoteAddress);
      Write(w, "remoteDisplayName", v.remoteDisplayName);
      Write(w, "alertInfoHeader", v.alertInfoHeader);
      Write(w, "answerMode", v.answerMode);
      Write(w, "responseCode", v.responseCode);
      Write(w, "statusText", v.statusText);
      Write(w, "extendedStateInfo", v.extendedStateInfo);
      Write(w, "historyInfo", v.historyInfo);
      Write(w, "sessionId", v.sessionId);
      Write(w, "responseTimeMs", v.responseTimeMs);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::ConversationStateChangedEvent& v)
    {
      Read(json, "conversationState", v.conversationState);
      Read(json, "dialogId", v.dialogId);
      Read(json, "contactHeaderField", v.contactHeaderField);
      Read(json, "remoteAddress", v.remoteAddress);
      Read(json, "remoteDisplayName", v.remoteDisplayName);
      Read(json, "alertInfoHeader", v.alertInfoHeader);
      Read(json, "answerMode", v.answerMode);
      Read(json, "responseCode", v.responseCode);
      Read(json, "statusText", v.statusText);
      Read(json, "extendedStateInfo", v.extendedStateInfo);
      Read(json, "historyInfo", v.historyInfo);
      Read(json, "sessionId", v.sessionId);
      Read(json, "responseTimeMs", v.responseTimeMs);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent& v)
    {
      w.StartObject();
      Write(w, "remoteMediaInfo", v.remoteMediaInfo);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent& v)
    {
      Read(json, "remoteMediaInfo", v.remoteMediaInfo);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::ConversationMediaChangedEvent& v)
    {
      w.StartObject();
      Write(w, "localMediaInfo", v.localMediaInfo);
      Write(w, "remoteMediaInfo", v.remoteMediaInfo);
      Write(w, "localHold", v.localHold);
      Write(w, "remoteHold", v.remoteHold);
      Write(w, "assertedIdentity", v.assertedIdentity);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::ConversationMediaChangedEvent& v)
    {
      Read(json, "localMediaInfo", v.localMediaInfo);
      Read(json, "remoteMediaInfo", v.remoteMediaInfo);
      Read(json, "localHold", v.localHold);
      Read(json, "remoteHold", v.remoteHold);
      Read(json, "assertedIdentity", v.assertedIdentity);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::ErrorEvent& v)
    {
      w.StartObject();
      Write(w, "errorText", v.errorText);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::ErrorEvent& v)
    {
      Read(json, "errorText", v.errorText);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent& v)
    {
      w.StartObject();
      Write(w, "conversationStatistics", v.conversationStatistics);
      Write(w, "jitterBufferStatistics", v.jitterBufferStatistics);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent& v)
    {
      Read(json, "conversationStatistics", v.conversationStatistics);
      Read(json, "jitterBufferStatistics", v.jitterBufferStatistics);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::ConversationAudioDeviceLevelChangeEvent& v)
    {
      w.StartObject();
      Write(w, "inputDeviceLevel", v.inputDeviceLevel);
      Write(w, "outputDeviceLevel", v.outputDeviceLevel);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::ConversationAudioDeviceLevelChangeEvent& v)
    {
      Read(json, "inputDeviceLevel", v.inputDeviceLevel);
      Read(json, "outputDeviceLevel", v.outputDeviceLevel);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::SipConversationHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::SipConversationHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::ConversationAdornmentEvent& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      Write(w, "adornmentMessageId", v.adornmentMessageId);
      Write(w, "target", v.target);
      Write(w, "method", v.method);
      Write(w, "responseCode", v.responseCode);
      Write(w, "message", v.message);
      Write(w, "cseqMethod", v.cseqMethod);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::ConversationAdornmentEvent& v)
    {
      Read(json, "account", v.account);
      Read(json, "adornmentMessageId", v.adornmentMessageId);
      Read(json, "target", v.target);
      Read(json, "method", v.method);
      Read(json, "responseCode", v.responseCode);
      Read(json, "message", v.message);
      Read(json, "cseqMethod", v.cseqMethod);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::SipConversationAdornmentHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::SipConversationAdornmentHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::ConversationInitiatedEvent& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      Write(w, "conversationType", v.conversationType);
      Write(w, "remoteAddress", v.remoteAddress);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::ConversationInitiatedEvent& v)
    {
      Read(json, "account", v.account);
      Read(json, "conversationType", v.conversationType);
      Read(json, "remoteAddress", v.remoteAddress);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::SdpOfferAnswerEvent& v)
    {
      w.StartObject();
      Write(w, "sdp", v.sdp);
      Write(w, "answerMode", v.answerMode);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::SdpOfferAnswerEvent& v)
    {
      Read(json, "sdp", v.sdp);
      Read(json, "answerMode", v.answerMode);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::LocalSdpOfferEvent& v)
    {
      w.StartObject();
      Write(w, "localMediaInfo", v.localMediaInfo);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::LocalSdpOfferEvent& v)
    {
      Read(json, "localMediaInfo", v.localMediaInfo);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::LocalSdpAnswerEvent& v)
    {
      w.StartObject();
      Write(w, "localMediaInfo", v.localMediaInfo);
      Write(w, "sdpOffer", v.sdpOffer);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::LocalSdpAnswerEvent& v)
    {
      Read(json, "localMediaInfo", v.localMediaInfo);
      Read(json, "sdpOffer", v.sdpOffer);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::IntervalReportEvent& v)
    {
      w.StartObject();
      Write(w, "callQualityReport", v.callQualityReport);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::IntervalReportEvent& v)
    {
      Read(json, "callQualityReport", v.callQualityReport);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::ConversationEndedEventFromStarcodeNetworkChange& v)
    {
      w.StartObject();
      Write(w, "endedConversations", v.endedConversations);
      Write(w, "originalConversation", v.originalConversation);
      Write(w, "newConversation", v.newConversation);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::ConversationEndedEventFromStarcodeNetworkChange& v)
    {
      Read(json, "endedConversations", v.endedConversations);
      Read(json, "originalConversation", v.originalConversation);
      Read(json, "newConversation", v.newConversation);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::SipConversationHandlerAdaptor& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::SipConversationHandlerAdaptor& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::SipConversationHandlerInternal& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::SipConversationHandlerInternal& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::NackSettings& v)
    {
      w.StartObject();
      Write(w, "maxNackListSize", v.maxNackListSize);
      Write(w, "maxPacketAgeToNack", v.maxPacketAgeToNack);
      Write(w, "maxIncompleteTimeMs", v.maxIncompleteTimeMs);
      Write(w, "nackHistorySizeSender", v.nackHistorySizeSender);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::NackSettings& v)
    {
      Read(json, "maxNackListSize", v.maxNackListSize);
      Read(json, "maxPacketAgeToNack", v.maxPacketAgeToNack);
      Read(json, "maxIncompleteTimeMs", v.maxIncompleteTimeMs);
      Read(json, "nackHistorySizeSender", v.nackHistorySizeSender);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipConversation::SipConversationManagerExt& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipConversation::SipConversationManagerExt& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // SipConversation_JSON_HELPER_H