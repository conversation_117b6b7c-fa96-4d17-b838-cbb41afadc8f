#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE == 1)
#include "SipConversationCompatibility.h"
#include "interface/public/call/SipConversationManager.h"
#include "interface/public/call/SipConversationTypes.h"
#include "interface/experimental/peerconnection/PeerConnectionManager.h"
#include "jsonrpccxx/common.hpp"
#include "gen/SipConversation/datatypes/SipConversationSettings.h"
#include "gen/SipConversation/datatypes/ConversationState.h"
#include "gen/SipConversation/datatypes/ConversationEndReason.h"
#include "gen/SipConversation/datatypes/ConversationState.h"
#include "gen/SipConversation/datatypes/ConversationType.h"
#include "gen/SipConversation/datatypes/NatTraversalMode.h"
#include "gen/SipConversation/datatypes/NatTraversalServerSourceType.h"
#include "gen/SipConversation/datatypes/NatTraversalServerType.h"
#include "gen/SipConversation/datatypes/HoldMode.h"
#include "gen/SipConversation/datatypes/PrackMode.h"
#include "gen/SipConversation/datatypes/AnswerMode.h"
#include "gen/SipConversation/datatypes/NetworkChangeHandoverMode.h"

#define COPY_FIELD(_from, _to, _field) optionalSet(_from._field, _to._field)
#define COPY_FIELD_CONVERTED(_from, _to, _field) optionalConvertAndSet(_from._field, _to._field)

#define ENUM_SWITCH_CASE_LONG(_enum_name, _enum_item_from, _enum_item_to) {               \
                        case _enum_name::_enum_item_from: \
                           return ::CPCAPI2::SipConversation::_enum_name::_enum_name ## _ ## _enum_item_to; \
                   }

#define ENUM_SWITCH_CASE(_enum_name, _enum_item) ENUM_SWITCH_CASE_LONG(_enum_name, _enum_item, _enum_item)


namespace jsonrpc
{
namespace CPCAPI2
{
namespace SipConversation
{

template <typename P, typename Q>
void optionalConvertAndSet(P &toSet, const std::optional<Q>& opt)
{
   if (opt)
   {
      toSet = convert(*opt);
   }
}

template <typename P, typename Q>
void optionalSet(P &toSet, const std::optional<Q>& opt)
{
   if (opt)
   {
      toSet = *opt;
   }
}

::CPCAPI2::SipConversation::MediaType convert(const MediaType v)
{
    switch (v)
    {
        ENUM_SWITCH_CASE(MediaType, Audio);
        ENUM_SWITCH_CASE(MediaType, Video);
    }
}

::CPCAPI2::SipConversation::DtmfMode convert(const DtmfMode v)
{
    switch (v)
    {
         ENUM_SWITCH_CASE_LONG(DtmfMode, Rfc2833, RFC2833);
         ENUM_SWITCH_CASE(DtmfMode, InBand);
         ENUM_SWITCH_CASE_LONG(DtmfMode, SipInfo, SIP_INFO);
         ENUM_SWITCH_CASE_LONG(DtmfMode, Rfc2833InBand, RFC2833_InBand);
         ENUM_SWITCH_CASE_LONG(DtmfMode, Rfc2833SipInfo, RFC2833_SIP_INFO);
         ENUM_SWITCH_CASE_LONG(DtmfMode, InBandSipInfo, InBand_SIP_INFO);
         ENUM_SWITCH_CASE(DtmfMode, Everything);
    }
}

::CPCAPI2::SipConversation::MediaDirection convert(const MediaDirection v)
{
    switch (v)
    {
        ENUM_SWITCH_CASE(MediaDirection, None);
        ENUM_SWITCH_CASE(MediaDirection, SendReceive);
        ENUM_SWITCH_CASE(MediaDirection, SendOnly);
        ENUM_SWITCH_CASE(MediaDirection, ReceiveOnly);
        ENUM_SWITCH_CASE(MediaDirection, Inactive);
    }
}

::CPCAPI2::SipConversation::NatTraversalMode convert(const NatTraversalMode v)
{
    switch (v)
    {
        ENUM_SWITCH_CASE(NatTraversalMode, None);
        ENUM_SWITCH_CASE(NatTraversalMode, Auto);
        ENUM_SWITCH_CASE_LONG(NatTraversalMode, Stun, STUN);
        ENUM_SWITCH_CASE_LONG(NatTraversalMode, Turn, TURN);
        ENUM_SWITCH_CASE_LONG(NatTraversalMode, Ice, ICE);
    }
}

// Here ENUM_SWITCH is not used, because NatTraversalServerSourceType enum is not typical as others in CPCAPI
// (NatTraversalServerSource_None but not NatTraversalServerSourceType_None)
::CPCAPI2::SipConversation::NatTraversalServerSourceType convert(const NatTraversalServerSourceType v)
{
    switch (v)
    {
      case NatTraversalServerSourceType::None: 
         return ::CPCAPI2::SipConversation::NatTraversalServerSourceType::NatTraversalServerSource_None;
      case NatTraversalServerSourceType::Srv: 
         return ::CPCAPI2::SipConversation::NatTraversalServerSourceType::NatTraversalServerSource_SRV;
      case NatTraversalServerSourceType::Custom: 
         return ::CPCAPI2::SipConversation::NatTraversalServerSourceType::NatTraversalServerSource_Custom;
    }
}

::CPCAPI2::SipConversation::NatTraversalServerType convert(const NatTraversalServerType v)
{
    switch (v)
    {
        ENUM_SWITCH_CASE(NatTraversalServerType, StunAndTurn);
        ENUM_SWITCH_CASE(NatTraversalServerType, StunOnly);
        ENUM_SWITCH_CASE(NatTraversalServerType, TurnOnly);
    }
}

::CPCAPI2::SipConversation::HoldMode convert(const HoldMode v)
{
    switch (v)
    {
        ENUM_SWITCH_CASE_LONG(HoldMode, Rfc3264, RFC3264);
        ENUM_SWITCH_CASE_LONG(HoldMode, Rfc2543, RFC2543);

    }
}

::CPCAPI2::SipConversation::PrackMode convert(const PrackMode v)
{
    switch (v)
    {
        ENUM_SWITCH_CASE(PrackMode, Disabled);
        ENUM_SWITCH_CASE(PrackMode, Supported);
        ENUM_SWITCH_CASE(PrackMode, Required);
        ENUM_SWITCH_CASE(PrackMode, SupportUasAndUac);
    }
}

::CPCAPI2::SipConversation::AnswerMode convert(const AnswerMode v)
{
    switch (v)
    {
        ENUM_SWITCH_CASE(AnswerMode, Disabled);
        ENUM_SWITCH_CASE(AnswerMode, Manual);
        ENUM_SWITCH_CASE(AnswerMode, Auto);
    }
}

::CPCAPI2::SipConversation::NetworkChangeHandoverMode convert(const NetworkChangeHandoverMode v)
{
    switch (v)
    {
        ENUM_SWITCH_CASE(NetworkChangeHandoverMode, Reinvite);
        ENUM_SWITCH_CASE(NetworkChangeHandoverMode, Starcode);
    }
}

::CPCAPI2::SipConversation::MediaEncryptionMode convert(const MediaEncryptionMode v)
{
   switch (v)
    {
      ENUM_SWITCH_CASE(MediaEncryptionMode, Unencrypted);
      ENUM_SWITCH_CASE_LONG(MediaEncryptionMode, SrtpSdesEncrypted, SRTP_SDES_Encrypted);
      ENUM_SWITCH_CASE_LONG(MediaEncryptionMode, SrtpDtlsEncrypted, SRTP_DTLS_Encrypted);
    }
}

::CPCAPI2::SipConversation::MediaCryptoSuite convert(const MediaCryptoSuite v)
{
   switch (v)
    {
      ENUM_SWITCH_CASE(MediaCryptoSuite, None);
      ENUM_SWITCH_CASE_LONG(MediaCryptoSuite, AesCm_128HmacSha1_32, AES_CM_128_HMAC_SHA1_32);
      ENUM_SWITCH_CASE_LONG(MediaCryptoSuite, AesCm_128HmacSha1_80, AES_CM_128_HMAC_SHA1_80);
      ENUM_SWITCH_CASE_LONG(MediaCryptoSuite, AesCm_256HmacSha1_32, AES_CM_256_HMAC_SHA1_32);
      ENUM_SWITCH_CASE_LONG(MediaCryptoSuite, AesCm_256HmacSha1_80, AES_CM_256_HMAC_SHA1_80);
      ENUM_SWITCH_CASE_LONG(MediaCryptoSuite, AesCm_192HmacSha1_32, AES_CM_192_HMAC_SHA1_32);
      ENUM_SWITCH_CASE_LONG(MediaCryptoSuite, AesCm_192HmacSha1_80, AES_CM_192_HMAC_SHA1_80);
      ENUM_SWITCH_CASE_LONG(MediaCryptoSuite, AeadAes_128Gcm, AEAD_AES_128_GCM);
      ENUM_SWITCH_CASE_LONG(MediaCryptoSuite, AeadAes_256Gcm, AEAD_AES_256_GCM);
    }
}

cpc::vector<::CPCAPI2::SipConversation::MediaCryptoSuite> convert(const cpc::vector<MediaCryptoSuite>& item)
{
   cpc::vector<::CPCAPI2::SipConversation::MediaCryptoSuite> result(item.size());
   for (size_t ind = 0; ind < item.size(); ++ind) {
      result[ind] = convert(item[ind]);
   }
   return result;
}

::CPCAPI2::SipConversation::MediaEncryptionOptions convert(const MediaEncryptionOptions item)
{
   ::CPCAPI2::SipConversation::MediaEncryptionOptions result;
   COPY_FIELD_CONVERTED(result, item, mediaEncryptionMode);
   COPY_FIELD(result, item, secureMediaRequired);
   COPY_FIELD_CONVERTED(result, item, mediaCryptoSuites);
   return result;
}

::CPCAPI2::SipConversation::AudioCodec convert(const AudioCodec& item) {
   ::CPCAPI2::SipConversation::AudioCodec result;
   COPY_FIELD(result, item, pltype);
   if (item.plname) {
      strcpy(result.plname, item.plname->c_str());
   }
   COPY_FIELD(result, item, plfreq);
   COPY_FIELD(result, item, pacsize);
   COPY_FIELD(result, item, channels);
   COPY_FIELD(result, item, rate);
   COPY_FIELD(result, item, priority);
   COPY_FIELD(result, item, displayName);
   return result;
}

::CPCAPI2::SipConversation::VideoCodec convert(const VideoCodec& item) {
   ::CPCAPI2::SipConversation::VideoCodec result;
   if (item.plName) {
      strcpy(result.plName, item.plName->c_str());
   }
   COPY_FIELD(result, item, plType);
   COPY_FIELD(result, item, width);
   COPY_FIELD(result, item, height);
   COPY_FIELD(result, item, startBitrate);
   COPY_FIELD(result, item, maxBitrate);
   COPY_FIELD(result, item, minBitrate);
   COPY_FIELD(result, item, maxFramerate);
   COPY_FIELD(result, item, hadwareAccelerated);
   COPY_FIELD(result, item, priority);
   COPY_FIELD(result, item, displayName);
   return result;
}

::CPCAPI2::SipConversation::MediaInfo convert(const MediaInfo& item) {
   ::CPCAPI2::SipConversation::MediaInfo result;
   COPY_FIELD_CONVERTED(result, item, mediaType);
   COPY_FIELD_CONVERTED(result, item, mediaDirection);
   COPY_FIELD_CONVERTED(result, item, mediaCrypto);
   COPY_FIELD_CONVERTED(result, item, mediaEncryptionOptions);
   COPY_FIELD_CONVERTED(result, item, audioCodec);
   COPY_FIELD_CONVERTED(result, item, videoCodec);
   COPY_FIELD(result, item, conferenceMixContribution);
   COPY_FIELD(result, item, isLocallyDisabled);
   COPY_FIELD(result, item, conferenceMixId);
   COPY_FIELD(result, item, mediaStreamId);
   COPY_FIELD(result, item, videoCaptureDeviceId);
   return result;
}

::CPCAPI2::SipConversation::AnswerModeSettings convert(const AnswerModeSettings item)
{
   ::CPCAPI2::SipConversation::AnswerModeSettings result;
   COPY_FIELD_CONVERTED(result, item, mode);
   COPY_FIELD(result, item, privileged);
   COPY_FIELD(result, item, required);
   COPY_FIELD(result, item, challenge);
   COPY_FIELD(result, item, allowManual);
   COPY_FIELD(result, item, allowAuto);
   COPY_FIELD(result, item, allowPrivileged);
   return result;
}

::CPCAPI2::SipConversationSettings convert(const SipConversationSettings item)
{
   ::CPCAPI2::SipConversationSettings result;
   COPY_FIELD(result, item, sessionName);
   COPY_FIELD_CONVERTED(result, item, natTraversalMode);
   COPY_FIELD_CONVERTED(result, item, natTraversalServerSource);
   COPY_FIELD(result, item, natTraversalServer);
   COPY_FIELD_CONVERTED(result, item, natTraversalServerType);
   COPY_FIELD_CONVERTED(result, item, holdMode);
   COPY_FIELD_CONVERTED(result, item, prackMode);
   COPY_FIELD_CONVERTED(result, item, answerMode);
   COPY_FIELD_CONVERTED(result, item, networkChangeHandoverMode);
   COPY_FIELD(result, item, networkChangeHandoverStarcode);
   COPY_FIELD(result, item, minRtpPort);
   COPY_FIELD(result, item, maxRtpPort);
   COPY_FIELD(result, item, minRtpPortAudio);
   COPY_FIELD(result, item, maxRtpPortAudio);
   COPY_FIELD(result, item, minRtpPortVideo);
   COPY_FIELD(result, item, maxRtpPortVideo);
   COPY_FIELD(result, item, turnUsername);
   COPY_FIELD(result, item, turnPassword);
   COPY_FIELD(result, item, includePPreferredIdentity);
   COPY_FIELD(result, item, includePAssertedIdentity);
   COPY_FIELD(result, item, includeAttribsForStaticPLs);
   COPY_FIELD(result, item, adornTransferMessages);
   return result;
}


void SipConversationCompatibility::configureMedia(const int64_t phoneHandle, const int64_t conversationHandle, const MediaInfo& mediaDescriptor) {
   getInstance(phoneHandle)->configureMedia(conversationHandle, convert(mediaDescriptor));
}

void SipConversationCompatibility::setMediaEnabled(const int64_t phoneHandle, const int64_t conversationHandle, const MediaType mediaType, const bool enabled) {
   getInstance(phoneHandle)->setMediaEnabled(conversationHandle, convert(mediaType), enabled);
}

void SipConversationCompatibility::setAnonymousMode(const int64_t phoneHandle, const int64_t conversationHandle, const int64_t anonymousMode) {
   getInstance(phoneHandle)->setAnonymousMode(conversationHandle, anonymousMode);
}

void SipConversationCompatibility::hold(const int64_t phoneHandle, const int64_t conversationHandle) {
   getInstance(phoneHandle)->hold(conversationHandle);
}

void SipConversationCompatibility::unhold(const int64_t phoneHandle, const int64_t conversationHandle) {
   getInstance(phoneHandle)->unhold(conversationHandle);
}

void SipConversationCompatibility::sendMediaChangeRequest(const int64_t phoneHandle, const int64_t conversationHandle) {
   getInstance(phoneHandle)->sendMediaChangeRequest(conversationHandle);
}

void SipConversationCompatibility::redirect(const int64_t phoneHandle, const int64_t conversationHandle, const cpc::string& targetAddress, const cpc::string& reason) {
   getInstance(phoneHandle)->redirect(conversationHandle, targetAddress, reason);
}

void SipConversationCompatibility::sendRingingResponse(const int64_t phoneHandle, const int64_t conversationHandle) {
   getInstance(phoneHandle)->sendRingingResponse(conversationHandle);
}

void SipConversationCompatibility::reject(const int64_t phoneHandle, const int64_t conversationHandle, const int64_t rejectReason) {
   getInstance(phoneHandle)->reject(conversationHandle, rejectReason);
}

void SipConversationCompatibility::accept(const int64_t phoneHandle, const int64_t conversationHandle) {
   getInstance(phoneHandle)->accept(conversationHandle);
}

void SipConversationCompatibility::acceptIncomingTransferRequest(const int64_t phoneHandle, const int64_t conversationHandle) {
   getInstance(phoneHandle)->acceptIncomingTransferRequest(conversationHandle);
}

void SipConversationCompatibility::rejectIncomingTransferRequest(const int64_t phoneHandle, const int64_t conversationHandle) {
   getInstance(phoneHandle)->rejectIncomingTransferRequest(conversationHandle);
}

void SipConversationCompatibility::transfer(const int64_t phoneHandle, const int64_t transferTargetConversation, const int64_t transfereeConversation) {
   getInstance(phoneHandle)->transfer(transferTargetConversation, transfereeConversation);
}

void SipConversationCompatibility::transferWithFlag(const int64_t phoneHandle, const int64_t transferTargetConversation, const int64_t transfereeConversation, const bool endTargetConversationOnSuccess) {
   getInstance(phoneHandle)->transfer(transferTargetConversation, transfereeConversation, endTargetConversationOnSuccess);
}

void SipConversationCompatibility::transferToAddress(const int64_t phoneHandle, const int64_t transfereeConversation, const cpc::string& targetAddress) {
   getInstance(phoneHandle)->transfer(transfereeConversation, targetAddress);
}

void SipConversationCompatibility::setDtmfMode(const int64_t phoneHandle, const int64_t account, const int64_t ordinal, const DtmfMode dtmfMode) {
   getInstance(phoneHandle)->setDtmfMode(account, ordinal, convert(dtmfMode));
}

void SipConversationCompatibility::startDtmfTone(const int64_t phoneHandle, const int64_t conversationHandle, const int64_t toneId, const bool playLocally) {
   getInstance(phoneHandle)->startDtmfTone(conversationHandle, toneId, playLocally);
}

void SipConversationCompatibility::stopDtmfTone(const int64_t phoneHandle) {
   getInstance(phoneHandle)->stopDtmfTone();
}

void SipConversationCompatibility::playSound(const int64_t phoneHandle, const int64_t conversationHandle, const cpc::string& resourceUri, const bool repeat) {
   getInstance(phoneHandle)->playSound(conversationHandle, resourceUri, repeat);
}

void SipConversationCompatibility::stopPlaySound(const int64_t phoneHandle, const int64_t conversationHandle) {
   getInstance(phoneHandle)->stopPlaySound(conversationHandle);
}

int64_t SipConversationCompatibility::getCallCount(const int64_t phoneHandle) {
   return getInstance(phoneHandle)->getCallCount();
}

void SipConversationCompatibility::setCallKitMode(const int64_t phoneHandle, const int64_t conversationHandle) {
   getInstance(phoneHandle)->setCallKitMode(conversationHandle);
}

void SipConversationCompatibility::setTelecomFrameworkMode(const int64_t phoneHandle, const int64_t conversationHandle) {
   getInstance(phoneHandle)->setTelecomFrameworkMode(conversationHandle);
}

SipConversationCompatibility::SipConversationCompatibility(const std::shared_ptr<::CPCAPI2::PhoneInstances> &phones)
{
   mPhones = phones;
}

::CPCAPI2::SipConversation::SipConversationManager* SipConversationCompatibility::getInstance(const int64_t phoneHandle)
{
   return ::CPCAPI2::SipConversation::SipConversationManager::getInterface(mPhones->get(phoneHandle));
}

void SipConversationCompatibility::setDefaultSettings(const int64_t phoneHandle, const int64_t account, const SipConversationSettings& settings)
{
   getInstance(phoneHandle)->setDefaultSettings(account, ::CPCAPI2::SipConversationSettings());
}

int64_t SipConversationCompatibility::createConversation(const int64_t phoneHandle, const int64_t account)
{
   return getInstance(phoneHandle)->createConversation(account);
}

void SipConversationCompatibility::addParticipant(const int64_t phoneHandle, const int64_t conversation, const cpc::string& targetAddress)
{
   getInstance(phoneHandle)->addParticipant(conversation, targetAddress);
}

void SipConversationCompatibility::start(const int64_t phoneHandle, const int64_t conversation)
{
   getInstance(phoneHandle)->start(conversation);
}

void SipConversationCompatibility::end(const int64_t phoneHandle, const int64_t conversation)
{
   getInstance(phoneHandle)->end(conversation);
}


::CPCAPI2::SipConversation::ConversationState convert(const ConversationState v)
{
    switch (v)
    {
         ENUM_SWITCH_CASE(ConversationState, None);
         ENUM_SWITCH_CASE(ConversationState, LocalOriginated);
         ENUM_SWITCH_CASE(ConversationState, RemoteOriginated);
         ENUM_SWITCH_CASE(ConversationState, RemoteRinging);
         ENUM_SWITCH_CASE(ConversationState, LocalRinging);
         ENUM_SWITCH_CASE(ConversationState, Connected);
         ENUM_SWITCH_CASE(ConversationState, Early);
         ENUM_SWITCH_CASE(ConversationState, Ended);
    }
}

::CPCAPI2::SipConversation::ConversationEndReason convert(const ConversationEndReason v)
{
    switch (v)
    {
         ENUM_SWITCH_CASE(ConversationEndReason, Unknown);
         ENUM_SWITCH_CASE(ConversationEndReason, UserTerminatedLocally);
         ENUM_SWITCH_CASE(ConversationEndReason, UserTerminatedRemotely);
         ENUM_SWITCH_CASE(ConversationEndReason, ServerError);
         ENUM_SWITCH_CASE(ConversationEndReason, ServerRejected);
         ENUM_SWITCH_CASE(ConversationEndReason, Redirected);
         ENUM_SWITCH_CASE(ConversationEndReason, CallAnsweredElsewhere);
    }
}

} // namepace SipConversation
}    // namepace CPCAPI2
} // namespace jsonrpc
#endif // CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE