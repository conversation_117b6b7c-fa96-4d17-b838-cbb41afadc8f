#pragma once
#ifndef __CPCAPI2_BIEVENTS_INTERNAL_TYPES_H__
#define __CPCAPI2_BIEVENTS_INTERNAL_TYPES_H__

#include <stdint.h>

#include <bievents/BIEventsTypes.h>
#include <util/strettorpc/RPCIdentifier.h>
#include <util/strettorpc/RPCTimestamp.h>

namespace CPCAPI2
{
   namespace BIEvents
   {
      class BIEventsManagerImpl;

      /**
       * This structure is used by the cache, it wraps the actual event
       * and also adds other crucial meta-information (like the event ID).
       *
       * It represents the information as it is stored (before compression) in
       * the file for each event record.
       *
       * NB: m_SummaryText and m_Variable contain strings which are NOT null terminated!
       *     use std::string::assign to handle these.
       */
      typedef struct BIEventRecord
      {
         // Fixed data at the front of the record
         typedef struct fixed
         {
            char     ID[ 64 ];         // sha256 in hex (no null)
            char     Source[ 5 ];
            char     Type[ 5 ];
            uint32_t Group;
            uint32_t Context;
            int64_t  MillisSinceEpoch; // timestamp
            size_t   SummarySize;
            size_t   LabelSize;
            size_t   VariableSize;
         } fixed_t;

         fixed_t  m_Fixed;       // constant-length data
         uint8_t *m_SummaryText; // use uint8_t to handle UTF easily
         uint8_t *m_Labels;      // several strings concated together with length indicators.
         uint8_t *m_Variable;    // Name value pair list

         BIEventRecord( void );
         BIEventRecord( const BIEventRecord& that );
         BIEventRecord( const CPCAPI2::RPCIdentifier & id, const BIEventHeader& header, const BIEventBody& body );
            
         ~BIEventRecord( void );

         void SetID( const CPCAPI2::RPCIdentifier& id );
         bool Deserialize( BIEventHeader& outHeader, BIEventBody& outBody ) const;

         BIEventRecord& operator=( const BIEventRecord& that );
         // for collection semantics
         bool operator==( const BIEventRecord& that );
         // for collection semantics

      } BIEventRecord;
   }
}

#endif //  __CPCAPI2_BIEVENTS_INTERNAL_TYPES_H__
