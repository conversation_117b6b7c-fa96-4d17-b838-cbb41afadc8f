#include "brand_branded.h"

#if (CPCAPI2_BRAND_CALL_MODULE == 1)
#include "SipConversationStateImpl.h"
#include "SipAVConversationManagerInterface.h"

#ifndef CPCAPI2_REMOTE_CLIENT
#include "../phone/PhoneInterface.h"
#endif

#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_CALL


namespace CPCAPI2
{
namespace SipConversation
{

SipConversationStateImpl::SipConversationStateImpl(SipAVConversationManagerInterface* convManIf)
   : mConvManIf(convManIf)
{
}

SipConversationStateImpl::~SipConversationStateImpl()
{
#ifndef CPCAPI2_REMOTE_CLIENT
   mConvManIf->removeSdkObserver(this);
#endif
}

void SipConversationStateImpl::Release()
{
   delete this;
}

int SipConversationStateImpl::getState(SipConversationHandle h, SipConversationState& conversationState)
{
   resip::Lock locker (mStateLock);

   std::map<SipConversationHandle,SipConversationState>::iterator it = mStateMap.find(h);
   if (it != mStateMap.end())
   {
      conversationState = it->second;
      return kSuccess;
   }
   return kError;
}

int SipConversationStateImpl::getStateAllConversations(cpc::vector<SipConversationState>& conversationState)
{
   resip::Lock locker (mStateLock);

   std::map<SipConversationHandle, SipConversationState>::iterator it = mStateMap.begin();
   for (; it != mStateMap.end(); ++it)
   {
      conversationState.push_back(it->second);
   }
   return kSuccess;
}

int SipConversationStateImpl::getStateAllActiveConversations(cpc::vector<SipConversationState>& conversationState)
{
   resip::Lock locker (mStateLock);

   std::map<SipConversationHandle, SipConversationState>::iterator it = mStateMap.begin();
   for (; it != mStateMap.end(); ++it)
   {
      if (it->second.conversationState != ConversationState_Ended)
      {
         conversationState.push_back(it->second);
      }
   }
   return kSuccess;
}

int SipConversationStateImpl::onNewConversation(SipConversationHandle conversation, const NewConversationEvent& args)
{
   resip::Lock locker (mStateLock);

   mStateMap[conversation] = SipConversationState();
   std::map<SipConversationHandle,SipConversationState>::iterator it = mStateMap.find(conversation);
   if (it != mStateMap.end())
   {
      it->second.account = args.account;
      it->second.conversation = conversation;
      it->second.conversationState = args.conversationState;
      it->second.conversationType = args.conversationType;
      it->second.endReason = ConversationEndReason_Unknown;
      it->second.localHold = false;
      it->second.localMediaInfo = args.localMediaInfo;
      it->second.localAddress = args.localAddress;
      it->second.localDisplayName = args.localDisplayName;
      it->second.remoteAddress = args.remoteAddress;
      it->second.remoteDisplayName = args.remoteDisplayName;
      it->second.remoteHold = false;
      it->second.remoteMediaInfo = args.remoteMediaInfo;
      it->second.answerMode = args.answerMode;
   }
   return kSuccess;
}

int SipConversationStateImpl::onConversationEnded(SipConversationHandle conversation, const ConversationEndedEvent& args)
{
   DebugLog(<< "SipConversationStateImpl::onConversationEnded(): conversation: " << conversation << " state: " << args.conversationState);
   resip::Lock locker (mStateLock);

   std::map<SipConversationHandle,SipConversationState>::iterator it = mStateMap.find(conversation);
   if (it != mStateMap.end())
   {
      //it->second.account = args.account;
      it->second.conversationState = args.conversationState;
      //it->second.conversationType = args.conversationType;
      it->second.endReason = args.endReason;
      it->second.localHold = false;
      it->second.localMediaInfo.clear();
      //it->second.remoteAddress = args.remoteAddress;
      //it->second.remoteDisplayName = args.remoteDisplayName;
      it->second.remoteHold = false;
      it->second.remoteMediaInfo.clear();
      //mConvManIf->phoneInterface()->getSdkModuleThread().postMS(resip::resip_bind(&SipConversationStateImpl::cleanupConv, this, conversation), 5000);
   }
   return kSuccess;
}

int SipConversationStateImpl::onIncomingTransferRequest(SipConversationHandle conversation, const TransferRequestEvent& args)
{
   return kSuccess;
}

int SipConversationStateImpl::onIncomingRedirectRequest(SipConversationHandle conversation, const RedirectRequestEvent& args)
{
   return kSuccess;
}

int SipConversationStateImpl::onIncomingTargetChangeRequest(SipConversationHandle conversation, const TargetChangeRequestEvent& args)
{
   return kSuccess;
}

int SipConversationStateImpl::onIncomingHangupRequest(SipConversationHandle conversation, const HangupRequestEvent& args)
{
   return kSuccess;
}

int SipConversationStateImpl::onIncomingBroadsoftTalkRequest(SipConversationHandle conversation, const BroadsoftTalkEvent& args)
{
   return kSuccess;
}

int SipConversationStateImpl::onIncomingBroadsoftHoldRequest(SipConversationHandle conversation, const BroadsoftHoldEvent& args)
{
   return kSuccess;
}

int SipConversationStateImpl::onTransferProgress(SipConversationHandle conversation, const TransferProgressEvent& args)
{
   return kSuccess;
}

int SipConversationStateImpl::onConversationStateChangeRequest(SipConversationHandle conversation, const ConversationStateChangeRequestEvent& args)
{
   return kSuccess;
}

int SipConversationStateImpl::onConversationStateChanged(SipConversationHandle conversation, const ConversationStateChangedEvent& args)
{
   resip::Lock locker (mStateLock);
   
   std::map<SipConversationHandle,SipConversationState>::iterator it = mStateMap.find(conversation);
   if (it != mStateMap.end())
   {
      //it->second.account = args.account;
      it->second.conversationState = args.conversationState;
      //it->second.conversationType = args.conversationType;
      //it->second.endReason = args.endReason;
      //it->second.localHold = false;
      //it->second.localMediaInfo.clear();
      if (args.remoteAddress.size() > 0)
      {
         it->second.remoteAddress = args.remoteAddress;
      }
      if (args.remoteDisplayName.size() > 0)
      {
         it->second.remoteDisplayName = args.remoteDisplayName;
      }
      //it->second.remoteHold = false;
      //it->second.remoteMediaInfo.clear();
      it->second.answerMode = args.answerMode;
   }
   return kSuccess;
}

int SipConversationStateImpl::onConversationMediaChangeRequest(SipConversationHandle conversation, const ConversationMediaChangeRequestEvent& args)
{
   return kSuccess;
}

int SipConversationStateImpl::onConversationMediaChanged(SipConversationHandle conversation, const ConversationMediaChangedEvent& args)
{
   resip::Lock locker (mStateLock);
   
   std::map<SipConversationHandle,SipConversationState>::iterator it = mStateMap.find(conversation);
   if (it != mStateMap.end())
   {
      //it->second.account = args.account;
      //it->second.conversationState = args.conversationState;
      //it->second.conversationType = args.conversationType;
      //it->second.endReason = args.endReason;
      it->second.localHold = args.localHold;
      it->second.localMediaInfo = args.localMediaInfo;
      //it->second.remoteAddress = args.remoteAddress;
      //it->second.remoteDisplayName = args.remoteDisplayName;
      it->second.remoteHold = args.remoteHold;
      it->second.remoteMediaInfo = args.remoteMediaInfo;
   }
   return kSuccess;
}

int SipConversationStateImpl::onConversationStatisticsUpdated(SipConversationHandle conversation, const ConversationStatisticsUpdatedEvent& args)
{
   resip::Lock locker (mStateLock);
   
   std::map<SipConversationHandle,SipConversationState>::iterator it = mStateMap.find(conversation);
   if (it != mStateMap.end())
   {
      it->second.statistics = args.conversationStatistics;
      it->second.jitterBufferStatistics = args.jitterBufferStatistics;
   }
   return kSuccess;
}

int SipConversationStateImpl::onError(SipConversationHandle subscription, const ErrorEvent& args)
{
   return kSuccess;
}

int SipConversationStateImpl::onConversationInitiated(SipConversationHandle conversation, const ConversationInitiatedEvent& args)
{
   return kSuccess;
}

void SipConversationStateImpl::cleanupConv(SipConversationHandle conversation)
{
   resip::Lock locker (mStateLock);
   
   mStateMap.erase(conversation);
}

}
}

#endif
