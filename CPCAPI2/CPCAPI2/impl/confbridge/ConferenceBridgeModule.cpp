#include "brand_branded.h"

#include "interface/experimental/confbridge/ConferenceBridgeManager.h"
#include "interface/experimental/confbridge/ConferenceRegistrar.h"

#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_MODULE == 1)
#include "../phone/PhoneInterface.h"
#include "ConferenceBridgeManagerInterface.h"
#endif

namespace CPCAPI2
{
namespace ConferenceBridge
{
ConferenceBridgeManager* ConferenceBridgeManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_MODULE == 1)
   ConferenceRegistrar::getInterface(cpcPhone);
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<ConferenceBridgeManagerInterface>(phone, "ConferenceBridgeManagerInterface");
#else
   return NULL;
#endif
}

const cpc::string& ConferenceBridgeManager::getServiceId()
{
   static cpc::string serviceId = "confbridge";
   return serviceId;
}

}
}
