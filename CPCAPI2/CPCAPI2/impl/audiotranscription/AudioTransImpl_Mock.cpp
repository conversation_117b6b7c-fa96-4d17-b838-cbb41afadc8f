#include "brand_branded.h"

#if (CPCAPI2_BRAND_AUDIO_TRANS_MODULE == 1)
#include "AudioTransImpl.h"
#include "AudioTransInterface.h"
#include "media/MediaManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "auth_server/AuthServerJwtUtils.h"

#include <MediaStackImpl.hxx>

// WebRTC
#include <vie_codec.h>
#include <voe_base.h>
#include <vie_base.h>
#include <vie_rtp_rtcp.h>
#include <webrtc/video_engine/vie_encoder.h>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>
#include <sstream>

#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

using namespace CPCAPI2::Media;

namespace CPCAPI2
{
namespace AudioTrans
{
AudioTransImpl::AudioTransImpl(CPCAPI2::PhoneInterface* phone, AudioTransInterface* ccif, AudioTransHandle h)
   : mPhone(phone),
     mMediaManager(dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone))),
     mInterface(ccif),
     mHandle(h),
     mCurrentResultSetId(1),
     mShouldReadTranscribeResults(true)
{
}

AudioTransImpl::~AudioTransImpl()
{
}

void AudioTransImpl::setAudioTransSettings(const AudioTransSettings& settings)
{
   mSettings = settings;
}

class TranscriptionEncodedAudioObserver : public webrtc::VoEMediaProcess
{
public:
   TranscriptionEncodedAudioObserver(AudioTransImpl* audioTransImpl)
      : mAudioTrans(audioTransImpl), mAudioBuffIdx(0), mNumSamplesVadPassive(0) 
   {
   }
   virtual ~TranscriptionEncodedAudioObserver() {}

   void InitGoogleSpeech()
   {
      InfoLog(<< "InitGoogleSpeech");
   }

   virtual void Process(int channel,
      webrtc::ProcessingTypes type,
      webrtc::AudioFrame* audioFrame)
   {
      mResampler.InitializeIfNeeded(audioFrame->sample_rate_hz_, 16000, 1);
      int numSamples = mResampler.Resample(audioFrame->data_, audioFrame->samples_per_channel_*audioFrame->num_channels_, &mAudioBuff[mAudioBuffIdx], webrtc::AudioFrame::kMaxDataSizeSamples) / audioFrame->num_channels_;
      mAudioBuffIdx += numSamples;
      if (mAudioBuffIdx >= 32800)
      {
         //mRequest.set_audio_content(&mAudioBuff[0], 32800 * 2);
         //InfoLog(<< "write streaming recognize request");
         //mStreamer->Write(mRequest);
         //mAudioBuffIdx = 0;

         //if (audioFrame->vad_activity_ == webrtc::AudioFrame::kVadPassive)
         //{
         //   mNumSamplesVadPassive += numSamples;

         //   if (mNumSamplesVadPassive >= 640)
         //   {
         //      mNumSamplesVadPassive = 0;
         //      Reset();
         //      //mAudioTrans->post(resip::resip_bind(&TranscriptionEncodedAudioObserver::Reset, this));
         //   }
         //}
         //else
         //{
         //   mNumSamplesVadPassive = 0;
         //}
      }
   }

   void Reset()
   {
      InfoLog(<< "writes done");
   }

private:
   AudioTransImpl* mAudioTrans;
   webrtc::PushResampler<int16_t> mResampler;
   int16_t mAudioBuff[32800]; // just over 64kb, and evenly divisible by 320 since we get 320 bytes in each frame of audio from webrtc
   int mAudioBuffIdx;
   int mNumSamplesVadPassive;
};

void AudioTransImpl::startTranscription()
{
   if (mSettings.audioRecvChannel >= 0 && mEncodedAudioObs.get() == NULL)
   {
      mEncodedAudioObs.reset(new TranscriptionEncodedAudioObserver(this));
      mEncodedAudioObs->InitGoogleSpeech();

      std::shared_ptr<webrtc_recon::MediaStackImpl> mediaStack = mMediaManager->media_stack_ptr();
      std::shared_ptr<webrtc_recon::MixerImpl> mixerImpl = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mediaStack->mixer());
      mixerImpl->registerDecodedAudioObserver(mSettings.audioRecvChannel, mEncodedAudioObs.get());

   }
}

void AudioTransImpl::stopTranscriptionInternal()
{
   InfoLog(<< "stopTranscriptionInternal()");
   mShouldReadTranscribeResults = false;
}

void AudioTransImpl::stopTranscription()
{
   InfoLog(<< "stopTranscription()");
   mShouldReadTranscribeResults = false;

   std::shared_ptr<webrtc_recon::MediaStackImpl> mediaStack = mMediaManager->media_stack_ptr();
   std::shared_ptr<webrtc_recon::MixerImpl> mixerImpl = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mediaStack->mixer());
   mixerImpl->unregisterDecodedAudioObserver(mSettings.audioRecvChannel, mEncodedAudioObs.get());
}

void AudioTransImpl::restartTranscription()
{
   InfoLog(<< "restartTranscription()");
   mShouldReadTranscribeResults = true;
}

void AudioTransImpl::post(resip::ReadCallbackBase* rcb)
{
   mInterface->post(rcb);
}

void AudioTransImpl::HandleEncodedAudio(uint32_t timeStamp,
   const uint8_t* payloadData,
   size_t payloadSize)
{
}

void AudioTransImpl::transcriptionResultsThread()
{
   InfoLog(<< "transcriptionResultsThread()");
   InfoLog(<< "transcriptionResultsThread() - exit");

}


}
}

#endif // CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE
