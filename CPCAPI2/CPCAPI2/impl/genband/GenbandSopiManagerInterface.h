#pragma once

#if !defined(__CPCAPI2_GENBAND_SOPI_CLIENT_MANAGER_INTERFACE_H__)
#define __CPCAPI2_GENBAND_SOPI_CLIENT_MANAGER_INTERFACE_H__

#include "cpcapi2defs.h"
#include "genband/GenbandSopiManager.h"
#include "genband/GenbandSopiHandler.h"
#include "../phone/PhoneInterface.h"

#include "GenbandSopiAddressBookHandler.h"

#include <rutil/Fifo.hxx>
#include <mutex>


namespace CPCAPI2
{
namespace GenbandSopi
{

class SopiClientImpl;

class GenbandSopiManagerInterface : public CPCAPI2::EventSource2<CPCAPI2::EventHandler<GenbandSopiHandler, GenbandSopiClientHandle> >,
                                    public PhoneModule,
                                    public GenbandSopiManager
{
public:
   GenbandSopiManagerInterface(Phone* phone);
   virtual ~GenbandSopiManagerInterface();

   FORWARD_EVENT_PROCESSOR(GenbandSopiManagerInterface);

   // GenbandSopiClientManager interface
   virtual GenbandSopiClientHandle createClient(const GenbandSopiClientSettings& settings) OVERRIDE;
   virtual int setHandler(GenbandSopiClientHandle handle, GenbandSopiHandler* handler) OVERRIDE;

   virtual int requestAddressBook(GenbandSopiClientHandle handle) OVERRIDE;
   virtual int addAddressBookEntry(GenbandSopiClientHandle handle, const AddressBookEntry& entry) OVERRIDE;
   virtual int updateAddressBookEntry(GenbandSopiClientHandle handle,const cpc::string& entryId, const AddressBookEntry& entry) OVERRIDE;
   virtual int deleteAddressBookEntry(GenbandSopiClientHandle handle, const cpc::string entryName) OVERRIDE;
   virtual int getAddressBookGroups(GenbandSopiClientHandle handle) OVERRIDE;
   virtual int addAddressBookGroup(GenbandSopiClientHandle handle, const cpc::string& groupName) OVERRIDE;
   virtual int updateAddressBookGroup(GenbandSopiClientHandle handle, const cpc::string& oldGroupName, const cpc::string& newGroupName) OVERRIDE;
   virtual int deleteAddressBookGroup(GenbandSopiClientHandle handle, const cpc::string& groupName) OVERRIDE;
   virtual int searchGlobalDirectory(GenbandSopiClientHandle handle, const cpc::string& searchQuery, unsigned int maxCount) OVERRIDE;

   virtual int setSubscriptionHandler(CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;
   virtual int startSubscription(GenbandSopiClientHandle handle,CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;
   virtual int stopSubscription(GenbandSopiClientHandle handle) OVERRIDE;

   virtual int addAuthorizedUser(GenbandSopiClientHandle handle, const cpc::string& userName) OVERRIDE;
   virtual int removeAuthorizedUser(GenbandSopiClientHandle handle, const cpc::string& userName) OVERRIDE;
   virtual int getAuthorizedUsers(GenbandSopiClientHandle handle) OVERRIDE;

   virtual int addBannedUser(GenbandSopiClientHandle handle, const cpc::string& userName) OVERRIDE;
   virtual int removeBannedUser(GenbandSopiClientHandle handle, const cpc::string& userName) OVERRIDE;
   virtual int getBannedUsers(GenbandSopiClientHandle handle) OVERRIDE;

   virtual int addPoliteBlockedUser(GenbandSopiClientHandle handle, const cpc::string& userName) OVERRIDE;
   virtual int removePoliteBlockedUser(GenbandSopiClientHandle handle, const cpc::string& userName) OVERRIDE;
   virtual int getPoliteBlockedUsers(GenbandSopiClientHandle handle) OVERRIDE;

   bool isGlobalSearchPendingRequest();
   void setGlobalSearchPendingRequest();
   void getGlobalSearchPendingRequest(); 

   bool isNetworkAvailable();
   
   virtual int destroy(GenbandSopiClientHandle handle) override;

private:
   // PhoneModuleInterface
   virtual void Release() OVERRIDE;

   void createClientImpl(GenbandSopiClientHandle handle, const GenbandSopiClientSettings& settings);
   void setHandlerImpl(GenbandSopiClientHandle handle, GenbandSopiHandler* handler);
   void requestAddressBookImpl(GenbandSopiClientHandle handle);
   void addAddressBookEntryImpl(GenbandSopiClientHandle handle, const AddressBookEntry& entry);
   void updateAddressBookEntryImpl(GenbandSopiClientHandle handle, const cpc::string& entryId, const AddressBookEntry& entry);
   void deleteAddressBookEntryImpl(GenbandSopiClientHandle handle, const cpc::string entryName);
   void getAddressBookGroupsImpl(GenbandSopiClientHandle handle);
   void addAddressBookGroupImpl(GenbandSopiClientHandle handle, const cpc::string& groupName);
   void updateAddressBookGroupImpl(GenbandSopiClientHandle handle, const cpc::string& oldGroupName, const cpc::string& newGroupName);
   void deleteAddressBookGroupImpl(GenbandSopiClientHandle handle, const cpc::string& groupName);
   void searchGlobalDirectoryImpl(GenbandSopiClientHandle handle, const cpc::string& searchQuery, unsigned int maxCount);
   
   void addAuthorizedUserImpl(GenbandSopiClientHandle handle, const cpc::string& userName);
   void removeAuthorizedUserImpl(GenbandSopiClientHandle handle, const cpc::string& userName);
   void getAuthorizedUsersImpl(GenbandSopiClientHandle handle);  
   
   void addBannedUserImpl(GenbandSopiClientHandle handle, const cpc::string& userName);
   void removeBannedUserImpl(GenbandSopiClientHandle handle, const cpc::string& userName);
   void getBannedUsersImpl(GenbandSopiClientHandle handle);

   void addPoliteBlockedUserImpl(GenbandSopiClientHandle handle, const cpc::string& userName);
   void removePoliteBlockedUserImpl(GenbandSopiClientHandle handle, const cpc::string& userName);
   void getPoliteBlockedUsersImpl(GenbandSopiClientHandle handle);

   
   void setSubscriptionHandlerImpl( CPCAPI2::SipAccount::SipAccountHandle account);
   void startSubscriptionImpl(GenbandSopiClientHandle handle,CPCAPI2::SipAccount::SipAccountHandle account);
   void stopSubscriptionImpl(GenbandSopiClientHandle handle);

   void destroyImpl(GenbandSopiClientHandle handle);

   void fireError(const cpc::string& errorText);

   void destroySopiClients();
   SopiClientImpl* getClient(GenbandSopiClientHandle handle);

private:
   GenbandSopiClientHandle mNextHandle;
   PhoneInterface* mPhone;

   typedef std::map<GenbandSopiClientHandle, SopiClientImpl*> ClientImplMap;
   ClientImplMap mClientImplMap;

   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, CPCAPI2::GenbandSopi::GenbandSopiAddressBookHandler*> AccountMap;
   AccountMap mAccountMap;
   std::map<GenbandSopiClientHandle, GenbandSopiHandler*> mHandlers;

   CPCAPI2::SipEvent::SipEventSubscriptionHandle mSubscriptionHandle;     
   CPCAPI2::SipEvent::SipEventManagerInterface* mSipEventIf;
   CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;

   int mGlobalSearchPendingRequest;
   std::recursive_mutex mMutex;
};

std::ostream& operator<<(std::ostream& os, const AddressBookUpdatedEvent& evt);
std::ostream& operator<<(std::ostream& os, const AddressBookGroupsUpdatedEvent& evt);
std::ostream& operator<<(std::ostream& os, const SearchGlobalAddressBookResultEvent& evt);
std::ostream& operator<<(std::ostream& os, const GetAuthorizedUsersEvent& evt);
std::ostream& operator<<(std::ostream& os, const GetBannedUsersEvent& evt);
std::ostream& operator<<(std::ostream& os, const GetPoliteBlockedUsersEvent& evt);
std::ostream& operator<<(std::ostream& os, const GenbandSopi::ErrorEvent& evt);

}
}
#endif
