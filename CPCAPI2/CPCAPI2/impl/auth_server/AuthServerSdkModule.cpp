#include "brand_branded.h"

#include "interface/experimental/auth_server/AuthServer.h"

#if (CPCAPI2_BRAND_AUTH_SERVER_MODULE == 1)
#include "AuthServerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace AuthServer
   {
      AuthServer* AuthServer::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_AUTH_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<AuthServerInterface>(phone, "AuthServer");
#else
         return NULL;
#endif
      }

   }
}
