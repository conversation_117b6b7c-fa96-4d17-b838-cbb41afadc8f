#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_EVENT_MODULE == 1)
#include "SipEventManagerImpl.h"
#include "SipEventSubscriptionCreationInfo.h"
#include "event/SipEventSubscriptionHandler.h"
#include "SipEventManagerInterface.h"
#include "RLMIContents.h"
#include "cpcapi2utils.h"
#include "../util/buffer.h"
#include "../util/cpc_logger.h"
#include <resip/stack/ExtensionHeader.hxx>
#include <resip/stack/ExtensionParameter.hxx>
#include <resip/stack/Helper.hxx>
#include <resip/stack/MultipartRelatedContents.hxx>
#include <rutil/Log.hxx>
#include <resip/dum/AppDialog.hxx>

using namespace resip;
using namespace CPCAPI2::SipAccount;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_EVENT


namespace CPCAPI2
{
namespace SipEvent
{
SipEventManagerImpl::SipEventManagerImpl(std::shared_ptr<AccountMap> parentMap, SipAccountImpl& account, CPCAPI2::SipEvent::SipEventManagerInterface* sipEventIf)
   : mParentMap(parentMap), mAccount(account), mSipEventIf(sipEventIf), mNextRetryIntervalSec(0)
{
   mAccount.registerAccountAwareFeature(this);
}

SipEventManagerImpl::~SipEventManagerImpl()
{
   mAccount.unregisterAccountAwareFeature(this);
}

void SipEventManagerImpl::release()
{
   if (std::shared_ptr<AccountMap> parentMap = mParentMap.lock())
   {
      parentMap->erase(mAccount.getHandle());
   }
   delete this;
}

void SipEventManagerImpl::addEventType(const std::string& eventType)
{
   if (mEventTypes.end() == mEventTypes.find(eventType))
   {
      mEventTypes.insert(eventType);
      if (NULL != mDum.get())
      {
         mDum->addClientSubscriptionHandler(eventType.c_str(), this);
         mDum->addServerSubscriptionHandler(eventType.c_str(), this);
      }
   }
}

//
// IAccountAware
//
int SipEventManagerImpl::adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile)
{
   if (!profile->isMethodSupported(resip::SUBSCRIBE)) profile->addSupportedMethod(resip::SUBSCRIBE);
   if (!profile->isMethodSupported(resip::NOTIFY)) profile->addSupportedMethod(resip::NOTIFY);
   return kSuccess;
}

int SipEventManagerImpl::addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig)
{
   InfoLog(<< "SipEventManagerImpl::addHandlers(..)");
   // the recon ConversationManager will get its copy of DUM from the UserAgent
   mDum = dum;

   for (auto it = mEventTypes.begin(); mEventTypes.end() != it; ++it)
   {
      mDum->addClientSubscriptionHandler(it->c_str(), this);
      mDum->addServerSubscriptionHandler(it->c_str(), this);
   }

   return kSuccess;
}

int SipEventManagerImpl::onDumBeingDestroyed()
{
   InfoLog(<< "SipEventManagerImpl::onDumBeingDestroyed()");
   if (mDum.get() != NULL) // if account was created but never enabled
   {
      cpc::vector<SipEventSubscriptionHandle> subs = getSubscriptions();
      cpc::vector<SipEventSubscriptionHandle>::iterator it = subs.begin();
      for (; it != subs.end(); ++it)
      {
         SipEventSubscriptionCreationInfo* ci = getCreationInfo(*it);
         if (ci != NULL)
         {
            ci->cleanup();
         }
      }
   }
   mDum.reset();
   mCreationInfo.clear();
   return kSuccess;
}

int SipEventManagerImpl::onRegistrationSuccess(const SipRegistrationSuccessEvent& args)
{
   DebugLog(<< "SipEventManagerImpl::onRegistrationSuccess(): subscription list size: " << mCreationInfo.size()
      << " server: " << args.server << " localContact: " << args.localContact << " overrideSourceIpForNAT64: " << args.overrideSourceIpSignalling);

   std::vector<SipEventSubscriptionHandle> subscriptionList;
   for (SipEventSubscriptionCreationInfoMap::iterator it = mCreationInfo.begin(); it != mCreationInfo.end(); it++)
   {
      subscriptionList.push_back(it->first);
   }

   for (std::vector<SipEventSubscriptionHandle>::iterator it = subscriptionList.begin(); it != subscriptionList.end(); it++)
   {
      SipEventSubscriptionHandle subscription = (*it);
      SipEventSubscriptionCreationInfo* ciOld = getCreationInfo(*it);
      if (!ciOld)
      {
         DebugLog(<< "SipEventManagerImpl::onRegistrationSuccess(): error getting creation info for subscription handle: " << subscription);
         continue;
      }

      CPCAPI2::SipAccount::SipAccountHandle account = ciOld->account;
      cpc::vector<resip::NameAddr> targetAddresses = ciOld->targetAddresses;

      if (!ciOld->dumClientSubscriptionHandle.isValid())
      {
         DebugLog(<< "SipEventManagerImpl::onRegistrationSuccess(): creation info for subscription handle: " << subscription << " for event: " << ciOld->eventType << " has invalid dumClientSubscriptionHandle");
         continue;
      }

      // Note that call to recreateSubscription will change the subscription creation info list
      DebugLog(<< "SipEventManagerImpl::onRegistrationSuccess(): recreate subscription with subscription handle: " << subscription << " (event: " << ciOld->eventType
         << ") as the contact header may have been updated " << args.localContact);
      if (!mSipEventIf->recreateSubscription(account, subscription))
      {
         DebugLog(<< "SipEventManagerImpl::onRegistrationSuccess(): error recreating subscription for subscription handle: " << subscription);
         continue;
      }

      // Call cleanup on the previous subscription, as recreateSubscription will remove the old creation info from the list, but does not delete it.
      ciOld->cleanup();

      SipEventSubscriptionCreationInfo* ci = getCreationInfo(subscription);
      if (!ci)
      {
         DebugLog(<< "SipEventManagerImpl::onRegistrationSuccess(): error getting creation info after subscription reset for subscription handle: " << subscription);
         continue;
      }

      // VERIFY: addParticipant does not need to be called as the target addresses are carried over from the previous creation info, verification required as
      // the SipEventManagerImpl::onTerminated and SipPresenceManagerInterface::restartFailedSubscription add the participant using that interface. If it is
      // not required to be done seperately, then we can move the carry over of the target addresses from the previous subscription info to recreateSubscription.
      ci->targetAddresses = targetAddresses;
      // mSipEventIf->addParticipant(subscription, targetAddress);
      mSipEventIf->start(subscription);
   }

   return kSuccess;
}

class SipEventManagerAppDialogFactoryDelegate : public CPCAPI2::SipAccount::AppDialogFactoryDelegate
{
public:
   SipEventManagerAppDialogFactoryDelegate(SipEventManagerImpl* pParent)
      : mImpl(pParent) {};

   // accept anything (NOTE: implies a priority/order)
   virtual bool isMyMessage(const resip::SipMessage& msg) { return msg.method() == resip::SUBSCRIBE; };
   virtual resip::AppDialogSet* createAppDialogSet(resip::DialogUsageManager& dum, const resip::SipMessage& msg)
   {
      return new SipEventSubscriptionCreationInfo(dum, mImpl);
   }

private:
   SipEventManagerImpl* mImpl;
};

int SipEventManagerImpl::registerSdkDialogSetFactory(CPCAPI2::SipAccount::AppDialogSetFactory& factory)
{
   // this part runs before addHandlers
   std::shared_ptr<SipEventManagerAppDialogFactoryDelegate> pTemp(new SipEventManagerAppDialogFactoryDelegate(this));
   factory.addDelegate(pTemp, true);
   return kSuccess;
}

SipEventSubscriptionCreationInfo* SipEventManagerImpl::getCreationInfo(const SipEventSubscriptionHandle& h) const
{
   SipEventSubscriptionCreationInfoMap::const_iterator it = mCreationInfo.find(h);
   if (it != mCreationInfo.end())
   {
      return it->second;
   }
   return NULL;
}

void SipEventManagerImpl::addCreationInfo(const SipEventSubscriptionHandle& h, SipEventSubscriptionCreationInfo* ci)
{
   mCreationInfo[h] = ci;
}

void SipEventManagerImpl::removeCreationInfo(const SipEventSubscriptionHandle& h)
{
   mCreationInfo.erase(h);
}

cpc::vector<SipEventSubscriptionHandle> SipEventManagerImpl::getSubscriptions() const
{
   cpc::vector<SipEventSubscriptionHandle> subscriptions;

   for (SipEventSubscriptionCreationInfoMap::const_iterator it = mCreationInfo.begin(); it != mCreationInfo.end(); it++)
   {
      subscriptions.push_back(it->first);
   }

   return subscriptions;
}

void SipEventManagerImpl::fireError(const SipEventSubscriptionHandle& subscription, const cpc::string& errorText, const resip::Data& eventPackage)
{
   ErrorEvent evt;
   evt.errorText = errorText;
   mSipEventIf->fireEvent(cpcEvent(SipEventSubscriptionHandler, onError), mAccount.getHandle(), eventPackage.c_str(), subscription, evt);
}

void SipEventManagerImpl::fireSubscriptionStateChanged(SipEventSubscriptionHandle subscription, SubscriptionStateChangedEvent& args, const resip::Data& eventPackage)
{
   mSipEventIf->fireEvent(cpcEvent(SipEventSubscriptionHandler, onSubscriptionStateChanged), mAccount.getHandle(), eventPackage.c_str(), subscription, args);
}

// ClientSubscriptionHandler
//
void SipEventManagerImpl::onUpdatePending(resip::ClientSubscriptionHandle h, const resip::SipMessage& notify, bool outOfOrder)
{
   if (isDumShutdown())
      return;

   h->acceptUpdate();

   SubscriptionStateChangedEvent args;
   args.subscriptionState = SipSubscriptionState_Pending;

   resip::Data eventPackage = notify.header(h_Event).value();
   SipEventSubscriptionHandle sdkh = findSipEventSubscriptionHandleByResipHandle(h);
   mSipEventIf->fireEvent(cpcEvent(SipEventSubscriptionHandler, onSubscriptionStateChanged), mAccount.getHandle(), eventPackage.c_str(), sdkh, args);

   mNextRetryIntervalSec = 0;
}

void SipEventManagerImpl::onUpdateActive(resip::ClientSubscriptionHandle h, const resip::SipMessage& notify, bool outOfOrder)
{
   if (isDumShutdown())
      return;

   h->acceptUpdate();
   SipEventSubscriptionCreationInfo* ci = dynamic_cast<SipEventSubscriptionCreationInfo*>(h->getAppDialogSet().get());
   if (!ci) return;

   resip::Data eventPackage = notify.header(h_Event).value();

   SipEventSubscriptionHandle sdkh = findSipEventSubscriptionHandleByResipHandle(h);

   bool isInitialNotify = false;
   if (ci->subscriptionState != SipSubscriptionState_Active)
   {
      isInitialNotify = true;
      ci->subscriptionState = SipSubscriptionState_Active;

      SubscriptionStateChangedEvent args;
      args.subscriptionState = SipSubscriptionState_Active;
      mSipEventIf->fireEvent(cpcEvent(SipEventSubscriptionHandler, onSubscriptionStateChanged), mAccount.getHandle(), eventPackage.c_str(), sdkh, args);
   }

   resip::Contents* content = (notify.exists(resip::h_ContentType) ? notify.getContents() : NULL);
   int contentLength = (notify.exists(h_ContentLength) ? notify.header(h_ContentLength).value() : 0);

   if (contentLength > 0 || (contentLength == 0 && isInitialNotify))
   {
      // parse multipart NOTIFY
      if (content != NULL && content->getType() == MultipartRelatedContents::getStaticType())
      {
         MultipartRelatedContents* multipartCont = dynamic_cast<MultipartRelatedContents*>(content);
         if (multipartCont)
         {
            IncomingResourceListEvent rlsEventArgs;
            ResourceContentMap mapCidToResourceInstance;
            resip::MultipartRelatedContents::Parts& parts = multipartCont->parts();
            resip::MultipartRelatedContents::Parts::const_iterator itPart = parts.begin();
            // parse list first
            for (; itPart != multipartCont->parts().end(); itPart++)
            {
               resip::Contents* cont = *itPart;
               if (cont->getType() == RLMIContents::getStaticType())
               {
                  RLMIContents* rlmi = dynamic_cast<RLMIContents*>(cont);
                  if (rlmi)
                  {
                     processRlmiContents(*rlmi, rlsEventArgs, mapCidToResourceInstance);
                  }
                  break;
               }
            }

            itPart = parts.begin();
            for (; itPart != multipartCont->parts().end(); itPart++)
            {
               resip::Contents* cont = *itPart;
               // extract Content-Id for this part and match it with its resource instance
               std::string contentIdStr(cont->header(h_ContentID).value().begin() + 1, cont->header(h_ContentID).value().end() - 1);
               cpc::string contentId = contentIdStr.c_str();
               ResourceContentMap::iterator itCid = mapCidToResourceInstance.find(contentId);
               if (itCid != mapCidToResourceInstance.end())
               {
                  SipEventResourceInstance resource = itCid->second;
                  cpc::vector<SipEventResource>::iterator itRes = rlsEventArgs.resources.begin();
                  bool foundContentId = false;
                  for (; itRes != rlsEventArgs.resources.end(); itRes++)
                  {
                     cpc::vector<SipEventResourceInstance>::iterator itInst = itRes->instances.begin();
                     for (; itInst != itRes->instances.end(); itInst++)
                     {
                        if (itInst->id == resource.id)
                        {
                           // populate rls event args
                           if (!cont->getBodyData().empty())
                              itInst->contentUtf8.assign(cont->getBodyData().c_str(), cont->getBodyData().size());
                           if (cont->exists(h_ContentType))
                           {
                              itInst->mimeType = cont->header(h_ContentType).type().c_str();
                              itInst->mimeSubType = cont->header(h_ContentType).subType().c_str();
                           }
                           foundContentId = true;
                           break;
                        }
                     }
                     if (foundContentId)
                        break;
                  }
               }
            }

            mSipEventIf->fireEvent(cpcEvent(SipEventSubscriptionHandler, onIncomingResourceList), mAccount.getHandle(), eventPackage.c_str(), sdkh, rlsEventArgs);
         }
      }
      else
      {
         IncomingEventStateEvent eventStateArgs;
         if (content != NULL)
         {
            eventStateArgs.eventState.contentUTF8.assign(content->getBodyData().c_str(), content->getBodyData().size());
         }
         eventStateArgs.eventState.contentLength = contentLength;
         eventStateArgs.eventState.eventPackage = (ci->eventType.c_str());
         if (notify.exists(h_SubscriptionState) && notify.header(h_SubscriptionState).exists(p_expires))
         {
            eventStateArgs.eventState.expiresTimeMs = notify.header(h_SubscriptionState).param(p_expires);
         }
         if (notify.exists(h_ContentType))
         {
            eventStateArgs.eventState.mimeType = (notify.header(h_ContentType).type().c_str());
            eventStateArgs.eventState.mimeSubType = (notify.header(h_ContentType).subType().c_str());
         }

         mSipEventIf->fireEvent(cpcEvent(SipEventSubscriptionHandler, onIncomingEventState), mAccount.getHandle(), eventPackage.c_str(), sdkh, eventStateArgs);
      }
   }

   mNextRetryIntervalSec = 0;
}

void SipEventManagerImpl::onUpdateExtension(resip::ClientSubscriptionHandle h, const resip::SipMessage& notify, bool outOfOrder)
{
   if (isDumShutdown())
      return;

   h->acceptUpdate();

   // no Subscription State given in NOTIFY; treat as "extension"
   SubscriptionStateChangedEvent args;
   args.subscriptionState = SipSubscriptionState_Active;

   resip::Data eventPackage = notify.header(h_Event).value();

   SipEventSubscriptionHandle sdkh = findSipEventSubscriptionHandleByResipHandle(h);
   mSipEventIf->fireEvent(cpcEvent(SipEventSubscriptionHandler, onSubscriptionStateChanged), mAccount.getHandle(), eventPackage.c_str(), sdkh, args);

   mNextRetryIntervalSec = 0;
}

/// return values: -1 = fail, 0 = retry immediately, N = retry in N seconds
int SipEventManagerImpl::onRequestRetry(resip::ClientSubscriptionHandle, int retrySeconds, const resip::SipMessage& response)
{
   InfoLog(<< "SipEventManagerImpl::onRequestRetry() handling internally generated 408/503: " << response);
   int retVal = -1;
   ExtensionHeader altsAvailHeader("X-Alternatives-Available");
   if (response.exists(altsAvailHeader))
   {
      // .jza. try a different non-greylisted DNS result
      retVal = 5;
   }
   else
   {
      if (response.header(h_StatusLine).responseCode() == 503)
      {
         if ((response.exists(h_Warnings) && response.header(h_Warnings).front().code() == 396) ||
             (response.exists(h_Warnings) && response.header(h_Warnings).front().code() == 397) ||
             (response.exists(h_Warnings) && response.header(h_Warnings).front().code() == 398))
         {
            // we hit this case when we get a transport failure (397) OR if we couldn't reach the DNS servers (398);
            // so we want to try again, because it's likely that our network is down
            retVal = 5;
         }
         else
         {
            // well, it was a 503, and we don't really know why ... fail
            retVal = -1;
         }
      }
      else
      {
         retVal = calcNextRetryInterval(); // 408
      }
   }

   if (retVal >= 0)
   {
      InfoLog(<< "Retry subscription due to internally-generated 408/503 from stack in " << retVal << " seconds");
   }
   else
   {
      InfoLog(<< "Failing the subscription, no retry will be attempted");
   }

   return retVal;
}

void SipEventManagerImpl::onTerminated(resip::ClientSubscriptionHandle h, const resip::SipMessage* msg)
{
   if (isDumShutdown())
      return;

   SipEventSubscriptionCreationInfo* ci = dynamic_cast<SipEventSubscriptionCreationInfo*>(h->getAppDialogSet().get());
   if (!ci) return;

   SubscriptionEndedEvent args;
   args.endReason = SipSubscriptionEndReason_ServerEnded;
   args.subscriptionType = SipSubscriptionType_Outgoing;
   args.retryAfter = 0;
   args.statusCode = 0;
   args.initialSubscribe = true;
   args.isNotifyTerminated = false;
   args.remoteAddress = "";
   args.reason = "";

   if (msg != NULL)
   {
      // Retrieve the retry value from Retry-After header or Subscription-State
      if(msg->exists(h_RetryAfter))
      {
         args.retryAfter = msg->header(h_RetryAfter).value();
      }
      else
      {
         if(msg->exists(h_SubscriptionState))
         {
            if(msg->header(h_SubscriptionState).exists(p_retryAfter))
               args.retryAfter = msg->header(h_SubscriptionState).param(p_retryAfter);         
         }
      }
   
      if(msg->header(h_CSeq).method() == NOTIFY) 
      {
          // Terminated because of NOTIFY
          args.isNotifyTerminated = true;
      }
   
      if (ci->subscriptionState == SipSubscriptionState_Active)
      {
          // Not an initial subscription
          args.initialSubscribe = false;
          if(msg->exists(h_SubscriptionState) && msg->header(h_SubscriptionState).exists(p_reason))
             args.reason = msg->header(h_SubscriptionState).param(p_reason).c_str();
      }
   
      // is response
      if(msg->isResponse())
      {
          args.remoteAddress = (Data::from(msg->header(h_To).uri()).c_str());
          args.statusCode = msg->header(resip::h_StatusLine).statusCode();
      }
      else
      {
         if(args.isNotifyTerminated)
         {
            args.remoteAddress = (Data::from(msg->header(h_From).uri()).c_str());
         }
         else
         {
            args.remoteAddress = (Data::from(msg->header(h_To).uri()).c_str());
         }
      }
   }

   resip::Data eventPackage = h->getEventType();
   ci->dumClientSubscriptionHandle = h;
   SipEventSubscriptionHandle sdkh = findSipEventSubscriptionHandleByResipHandle(h);
   mSipEventIf->fireEvent(cpcEvent(SipEventSubscriptionHandler, onSubscriptionEnded), mAccount.getHandle(), eventPackage.c_str(), sdkh, args);

   if (args.isNotifyTerminated && ci->enableNotifyTerminationRetryHandling && 
      (args.reason == "deactivated" || args.reason == "timeout"))
   {
      // Retry with a new subscription immediately as per RFC 3265 section 3.2.4
      if (mSipEventIf->recreateSubscription(ci->account, sdkh))
      {
         mSipEventIf->addParticipant(sdkh, args.remoteAddress); // Use the same participant as previous subscription
         mSipEventIf->start(sdkh);
      }
   }
   else
   {
      removeCreationInfo(sdkh);
   }

   if (msg != NULL)
   {
      mAccount.handle5xx(*msg);
   }

   mNextRetryIntervalSec = 0;
}

void SipEventManagerImpl::onNewSubscription(resip::ClientSubscriptionHandle h, const resip::SipMessage& notify)
{
   if (isDumShutdown())
      return;

   SipEventSubscriptionCreationInfo* ci = dynamic_cast<SipEventSubscriptionCreationInfo*>(h->getAppDialogSet().get());
   if (!ci) return;

   NewSubscriptionEvent args;
   args.account = mAccount.getHandle();
   args.subscriptionType = SipSubscriptionType_Outgoing;
   args.remoteAddress = (Data::from(notify.header(h_From).uri()).c_str());
   args.remoteDisplayName = (Data::from(notify.header(h_From).displayName()).c_str());

   resip::Data eventPackage = h->getEventType();
   ci->dumClientSubscriptionHandle = h;
   SipEventSubscriptionHandle sdkh = ci->sdkh;
   mSipEventIf->fireEvent(cpcEvent(SipEventSubscriptionHandler, onNewSubscription), mAccount.getHandle(), eventPackage.c_str(), sdkh, args);

   mNextRetryIntervalSec = 0;
}

// ServerSubscriptionHandler
//
void SipEventManagerImpl::onNewSubscription(resip::ServerSubscriptionHandle h, const resip::SipMessage& sub)
{
   if (isDumShutdown())
      return;

   // Extract the parameters from the Event: header
   H_Event::Type eventPackageHeader = sub.header(h_Event);
   std::vector<resip::Data> paramNames = eventPackageHeader.getUnknownParameterNames();
   cpc::vector<Parameter> eventPackageParams;
   for (std::vector<resip::Data>::const_iterator it = paramNames.begin(); it != paramNames.end(); it++)
   {
      Parameter eventPackageParam(it->c_str(), eventPackageHeader.param(resip::ExtensionParameter(*it)).c_str());
      eventPackageParams.push_back(eventPackageParam);
   }

   SipEventSubscriptionHandle sdkh = SipEventSubscriptionHandleFactory::getNext();
   SipEventSubscriptionCreationInfo* ci = dynamic_cast<SipEventSubscriptionCreationInfo*>(h->getAppDialogSet().get());
   if (ci)
   {
      ci->dumServerSubscriptionHandle = h;
      ci->account = mAccount.getHandle();
      ci->eventType = sub.header(h_Event).value();
      ci->expiresSeconds = sub.header(h_Expires).value();
      ci->eventPackageParams = eventPackageParams;
      addCreationInfo(sdkh, ci);

      NewSubscriptionEvent args;
      args.account = mAccount.getHandle();
      args.subscriptionType = SipSubscriptionType_Incoming;
      args.remoteAddress = (Data::from(sub.header(h_From).uri()).c_str());
      args.remoteDisplayName = (Data::from(sub.header(h_From).displayName()).c_str());
      args.eventPackage = (eventPackageHeader.value().c_str());
      args.eventPackageParams = eventPackageParams;
      resip::Mimes acceptedMimeTypes = sub.header(h_Accepts);
      resip::Mimes::iterator itMimes = acceptedMimeTypes.begin();
      for (; itMimes != acceptedMimeTypes.end(); ++itMimes)
      {
         CPCAPI2::MimeType m(itMimes->type().c_str(), itMimes->subType().c_str());
         args.supportedMimeTypes.push_back(m);
      }

      SipEventSubscriptionHandler* appHandler = NULL;
      resip::Data eventPackage = h->getEventType();

      mSipEventIf->fireEvent(cpcEvent(SipEventSubscriptionHandler, onNewSubscription), mAccount.getHandle(), eventPackage.c_str(), sdkh, args);
   }
   else
   {
      WarningLog(<< "onNewSubscription no creation info found for handle " << h.getId());
   }
}

void SipEventManagerImpl::onTerminated(resip::ServerSubscriptionHandle h)
{
   if (isDumShutdown())
      return;

   SubscriptionEndedEvent args;
   args.endReason = SipSubscriptionEndReason_ServerEnded;
   args.subscriptionType = SipSubscriptionType_Incoming;

   std::string eventPackage = h->getEventType().c_str();
   SipEventSubscriptionHandle sdkh = findSipEventSubscriptionHandleByResipHandle(h);
   mSipEventIf->fireEvent(cpcEvent(SipEventSubscriptionHandler, onSubscriptionEnded), mAccount.getHandle(), eventPackage, sdkh, args);

   removeCreationInfo(sdkh);
}

void SipEventManagerImpl::onNotifyRejected(ServerSubscriptionHandle h, const SipMessage& msg)
{
   NotifyFailureEvent args;
   args.sipResponseCode = msg.header(h_StatusLine).responseCode();

   std::string eventPackage = h->getEventType().c_str();

   SipEventSubscriptionHandle sdkh = findSipEventSubscriptionHandleByResipHandle(h);
   mSipEventIf->fireEvent(cpcEvent(SipEventSubscriptionHandler, onNotifyFailure), mAccount.getHandle(), eventPackage, sdkh, args);
}

void SipEventManagerImpl::onRefresh(ServerSubscriptionHandle h, const resip::SipMessage& sub)
{
   DebugLog(<< "received refresh for SipEventManagerImpl, handle " << h.getId());
   ServerSubscriptionHandler::onRefresh(h, sub);
}

SipEventSubscriptionHandle SipEventManagerImpl::findSipEventSubscriptionHandleByResipHandle(resip::ClientSubscriptionHandle h)
{
   SipEventSubscriptionCreationInfoMap::iterator it = mCreationInfo.begin();
   for (; it != mCreationInfo.end(); ++it)
   {
      if (it->second->dumClientSubscriptionHandle == h)
      {
         return it->first;
      }
   }
   return 0xffffffff;
}

SipEventSubscriptionHandle SipEventManagerImpl::findSipEventSubscriptionHandleByResipHandle(resip::ServerSubscriptionHandle h)
{
   SipEventSubscriptionCreationInfoMap::iterator it = mCreationInfo.begin();
   for (; it != mCreationInfo.end(); ++it)
   {
      if (it->second->dumServerSubscriptionHandle == h)
      {
         return it->first;
      }
   }
   return 0xffffffff;
}

void SipEventManagerImpl::processRlmiContents(const RLMIContents& rlmi, IncomingResourceListEvent& args, ResourceContentMap& mapCidToResourceInstance) const
{
   args.fullState = rlmi.stateType() == RLMIContents::full;
   args.uri = rlmi.entity().getAorNoReally().c_str();
   args.version = rlmi.version();
   const RLMIContents::Resources& resources = rlmi.getResources();
   RLMIContents::Resources::const_iterator itRes = resources.begin();
   for (; itRes != resources.end(); itRes++)
   {
      SipEventResource resource;
      resource.uri = itRes->uri().getAorNoReally().c_str();

      const RLMIContents::Names& names = itRes->names();
      RLMIContents::Names::const_iterator itName = names.begin();
      for (; itName != names.end(); itName++)
      {
         resource.names.push_back(itName->second.c_str());
      }

      const RLMIContents::Instances& instances = itRes->instances();
      RLMIContents::Instances::const_iterator itInst = instances.begin();
      for (; itInst != instances.end(); itInst++)
      {
         SipEventResourceInstance destInstance;
         RLMIContents::Resource::Instance srcInstance = *itInst;
         destInstance.id = srcInstance.getId();
         destInstance.subscriptionState = RLMIContents::instanceStateToSdk(srcInstance.getSubscriptionState());
         resource.instances.push_back(destInstance);

         // save content id for later matching with the content body
         mapCidToResourceInstance[srcInstance.getContentId()] = destInstance;
      }

      args.resources.push_back(resource);
   }
}

int SipEventManagerImpl::calcNextRetryInterval() // Adapted from SipAccountImpl::calcNextRegRetryInterval()
{
   static const int minRetryIntervalSeconds = 5; // Not configurable today

   int retVal = minRetryIntervalSeconds;
   if (mNextRetryIntervalSec < 5*minRetryIntervalSeconds)
   {
      retVal = mNextRetryIntervalSec + minRetryIntervalSeconds;
   }
   else if (mNextRetryIntervalSec < 300)
   {
      retVal = mNextRetryIntervalSec + 2*minRetryIntervalSeconds;
   }
   else
   {
      retVal = mNextRetryIntervalSec + 300;
   }
   mNextRetryIntervalSec = std::min<int>(resip::Helper::jitterValue(retVal, 80, 120, minRetryIntervalSeconds+1), 1800);
   return mNextRetryIntervalSec;
}

}
}
#endif // CPCAPI2_BRAND_CALL_MODULE
