#pragma once
#ifndef __CPCAPI2_ANALYTICSMANAGERINTERFACE_H__
#define __CPCAPI2_ANALYTICSMANAGERINTERFACE_H__

#include <analytics1/AnalyticsManagerInt.h>
#include <media/video/VideoHandler.h>
#include <phone/PhoneModule.h>
#include <rutil/MultiReactor.hxx>
#include <rutil/Fifo.hxx>

#ifdef CPCAPI2_AUTO_TEST
#include <util/AutoTestProcessor.h>
#endif

namespace CPCAPI2
{
   namespace SipAccount
   {
      class SipAccountInterface;
   }

   namespace XmppAccount
   {
      class XmppAccountInterface;
   }

   namespace PushToTalk
   {
      class PushToTalkManagerInterface;
   }

   namespace SipConversation
   {
      class SipAVConversationManagerInterface;
      class SipConversationStateManager;
   }

   class PhoneInterface;

   namespace Analytics
   {
      class AnalyticsManagerImpl;

      class AnalyticsManagerInterface :
         public AnalyticsManagerInt,
         public PhoneModule
#ifdef CPCAPI2_AUTO_TEST
       , public AutoTestProcessor
#endif
      {
      public:

         AnalyticsManagerInterface(CPCAPI2::Phone* phone);
         virtual ~AnalyticsManagerInterface();

         void setCallbackHook(void (*cbHook)(void*), void* context);

         // AnalyticsManager
         virtual void setHandler(AnalyticsHandler * notificationHandler) OVERRIDE;
         virtual AnalyticsHandle open(const AnalyticsSettings& settings, const GeneralStats& general, const AnalyticsHandle& serverHandle = 0) OVERRIDE;
         virtual int close(const AnalyticsHandle& serverHandle) OVERRIDE;
         virtual int setPresenceStats(const AnalyticsHandle& serverHandle, const PresenceStats& stats) OVERRIDE;
         virtual int setProvisioningStats(const AnalyticsHandle& serverHandle, const ProvisioningStats& stats) OVERRIDE;
         virtual int setStabilityStats(const AnalyticsHandle& serverHandle, const StabilityStats& stats) OVERRIDE;
         virtual int setSettingsStats(const AnalyticsHandle& serverHandle, const SettingsStats& stats) OVERRIDE;
         virtual int sendReport(const AnalyticsHandle& serverHandle) OVERRIDE;
         virtual int process(unsigned int timeout) OVERRIDE;

         //internal methods to set data passed in from other modules
         virtual int xmppAccountStatusChangeFired(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& args) OVERRIDE;
         virtual int pttServiceStatusChangeFired(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PttServiceStatusChangedEvent& args) OVERRIDE;
         virtual int pttSessionStatusChangeFired(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttSessionStateChangedEvent& args) OVERRIDE;
         virtual int CurrentVideoDeviceUpdatedFired(CPCAPI2::Media::VideoDeviceInfo devInfo) OVERRIDE;
         virtual int instantMessageInfoFired(unsigned int accountHandle, const bool incoming, const bool isSip) OVERRIDE;
         virtual int ConvRecordingStarted(CPCAPI2::SipConversation::SipConversationHandle conversationHandle) OVERRIDE;

         // PhoneModule
         virtual void Release() OVERRIDE;

         // AutoTestProcessor (for unit testing)
#ifdef CPCAPI2_AUTO_TEST
         virtual AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif

      private: // methods

         // AnalyticsManager Impl
         void setCallbackHookImpl(void (*cbHook)(void*), void* context);
         void setHandlerImpl(AnalyticsHandler * notificationHandler);
         AnalyticsHandle openImpl(const AnalyticsSettings& settings, const GeneralStats& general, const AnalyticsHandle& inHandle);
         int closeImpl(const AnalyticsHandle& serverHandle);
         int setPresenceStatsImpl(const AnalyticsHandle& serverHandle, const PresenceStats& stats);
         int setProvisioningStatsImpl(const AnalyticsHandle& serverHandle, const ProvisioningStats& stats);
         int setStabilityStatsImpl(const AnalyticsHandle& serverHandle, const StabilityStats& stats);
         int setSettingsStatsImpl(const AnalyticsHandle& serverHandle, const SettingsStats& stats);
         int sendReportImpl(const AnalyticsHandle& serverHandle);

         int xmppAccountStatusChangeFiredImpl(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& args);
         int pttServiceStatusChangeFiredImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PttServiceStatusChangedEvent& args);
         int pttSessionStatusChangeFiredImpl(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttSessionStateChangedEvent& args);
         int CurrentVideoDeviceUpdatedFiredImpl(CPCAPI2::Media::VideoDeviceInfo devInfo);
         int instantMessageInfoFiredImpl(unsigned int accountHandle, const bool incoming, const bool isSip);
         int ConvRecordingStartedImpl(CPCAPI2::SipConversation::SipConversationHandle conversationHandle);

      private:

         std::shared_ptr<resip::MultiReactor> mReactor;
         AnalyticsManagerImpl *mImpl;
         PhoneInterface* mPhone;

         void (*m_CbHook)(void*);
         void* m_Context;

         // Callback Fifo which should be used to marshal the events (process method should be called)
         bool mShutdown;
         resip::Fifo<resip::ReadCallbackBase> mCallbackFifo;
      };
   }
}

#endif // __CPCAPI2_ANALYTICSMANAGERINTERFACE_H__
