#pragma once

#if !defined(__CPCAPI2_SIP_DIALOG_EVENT_PUBLICATION_MANAGER_INTERFACE_H__)
#define __CPCAPI2_SIP_DIALOG_EVENT_PUBLICATION_MANAGER_INTERFACE_H__

#include "cpcapi2defs.h"
#include "../phone/PhoneModule.h"
#include "../account/SipAccountInterface.h"
#include "../event/SipEventPublicationManagerInterface.h"
#include "dialogevent/SipDialogEventPublicationManager.h"
#include "dialogevent/SipDialogEventPublicationHandler.h"

namespace CPCAPI2
{
class Phone;

namespace SipDialogEvent
{
class SipDialogEventPublicationManagerImpl;

class SipDialogEventPublicationManagerInterface : public CPCAPI2::EventSource2< CPCAPI2::EventHandler<SipDialogEventPublicationHandler, SipAccount::SipAccountHandle> >,
                                                  public SipDialogEventPublicationManager,
                                                  public PhoneModule
{
public:
   SipDialogEventPublicationManagerInterface(Phone* phone);
   virtual ~SipDialogEventPublicationManagerInterface();

   FORWARD_EVENT_PROCESSOR(SipDialogEventPublicationManagerInterface);

   // SipDialogEventPublicationManager interface
   virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipDialogEventPublicationHandler* handler) OVERRIDE;
   virtual SipDialogEventPublicationHandle createPublication(CPCAPI2::SipAccount::SipAccountHandle account, const SipDialogEventPublicationSettings& settings) OVERRIDE;
   virtual int setTarget(SipDialogEventPublicationHandle publication, const cpc::string& targetAddress) OVERRIDE;
   virtual int publish(SipDialogEventPublicationHandle publication, const DialogInfoDocument& dialogInfoDoc) OVERRIDE;
   virtual int end(SipDialogEventPublicationHandle publication) OVERRIDE;
   int setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipDialogEventPublicationHandler* handler);

private:
   CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;
   typedef std::map<SipAccount::SipAccountHandle, SipDialogEventPublicationManagerImpl*> AccountMap;
   std::shared_ptr<AccountMap> mAccountMapPtr;
   AccountMap& mAccountMap;
   std::map<CPCAPI2::SipAccount::SipAccountHandle, SipDialogEventPublicationHandler*> mHandlers;
   CPCAPI2::SipEvent::SipEventPublicationManagerInterface* mSipPublicationIf;
   PhoneInterface* mPhone;

   // PhoneModule interface
   virtual void Release() OVERRIDE;

   int publishImpl(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const DialogInfoDocument& dialogInfoDoc);

   SipDialogEventPublicationManagerImpl* getManagerForAccount(CPCAPI2::SipAccount::SipAccountHandle account);
};

std::ostream& operator<<(std::ostream& os, const DialogEventPublicationSuccessEvent& evt);
std::ostream& operator<<(std::ostream& os, const DialogEventPublicationFailureEvent& evt);
std::ostream& operator<<(std::ostream& os, const DialogEventPublicationRemoveEvent& evt);
std::ostream& operator<<(std::ostream& os, const DialogEventPublicationErrorEvent& evt);

}
}

#endif // __CPCAPI2_SIP_DIALOG_EVENT_PUBLICATION_MANAGER_INTERFACE_H__
