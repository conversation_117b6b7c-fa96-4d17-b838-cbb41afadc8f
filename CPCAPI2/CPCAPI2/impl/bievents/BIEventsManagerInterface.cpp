#include <atomic>
#include <mutex>
#include <condition_variable>

#include <boost/asio/io_service.hpp>

#include <brand_branded.h>

#include <util/strettorpc/RPCIdentifier.h>

#include "BIEventsManagerInterface.h"
#include "BIEventsManagerImpl.h"
#include "BIEventsHelperInterface.h"
#include "phone/PhoneInterface.h"
#include "../util/cpc_logger.h"
#include "../util/LogSubsystems.h"

#if (CPCAPI2_BRAND_BIEVENTS_MODULE == 1)

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::BI_EVENTS

using namespace CPCAPI2::BIEvents;

static std::atomic< BIEventsHandle > s_EventsHandleCounter( 0 );

BIEventsManagerInterface::BIEventsManagerInterface(CPCAPI2::Phone* phone)
   : m_Helper( NULL ), m_IOService(dynamic_cast<PhoneInterface*>(phone)->getAsioIoService()), m_Shutdown( false ), m_CbHook( NULL ), m_Context( NULL )
{
}

BIEventsManagerInterface::~BIEventsManagerInterface()
{
}

void BIEventsManagerInterface::PreRelease()
{
   // Scope the mutex
   {
      std::lock_guard< std::recursive_mutex > lk(m_InfoMutex);

      m_Shutdown = true;

      // Abort all current HTTP connections (if any)
      std::map< BIEventsHandle, std::shared_ptr< BIEventsManagerImpl > >::iterator iter = m_InfoMap.begin();
      while( iter != m_InfoMap.end() )
      {
         if( iter->second != NULL )
            iter->second->abort();

         ++iter;
      }
   }

   // Scope the mutex
   {
      std::lock_guard< std::recursive_mutex > lk(m_InfoMutex);

      for (std::map< BIEventsHandle, std::shared_ptr< BIEventsManagerImpl > >::const_iterator it = m_InfoMap.begin();
           it != m_InfoMap.end(); ++it)
      {
         // hold a weak reference to all the impls we know about; we do this so
         // we can check back to see if they've destructed in PreReleaseCompleted.
         m_PendingShutdownImpls.push_back(it->second);
      }

      // Free everything in the info map (shared_ptr does the work)
      m_InfoMap.clear();

      delete m_Helper;
   }
}

bool BIEventsManagerInterface::PreReleaseCompleted()
{
   for (std::vector< std::weak_ptr< BIEventsManagerImpl > >::const_iterator it = m_PendingShutdownImpls.begin();
      it != m_PendingShutdownImpls.end(); ++it)
   {
      if (it->lock())
      {
         return false;
      }
   }

   return true;
}

// Invoked in DUM thread
void BIEventsManagerInterface::Release()
{
   delete this; // suicide
}

BIEventsHandle BIEventsManagerInterface::create( void )
{
   BIEventsHandle result = s_EventsHandleCounter.fetch_add( 1 );
   m_IOService.post( std::bind( &BIEventsManagerInterface::createImpl, this, result ));
   return result;
}

int BIEventsManagerInterface::createImpl( const BIEventsHandle& hEvents )
{
   std::lock_guard< std::recursive_mutex > lk( m_InfoMutex );

   std::shared_ptr< BIEventsManagerImpl > pImpl( new BIEventsManagerImpl( hEvents, m_IOService, m_CallbackFifo ));
   pImpl->setCallbackHook( m_CbHook, m_Context );
   m_InfoMap[ hEvents ] = pImpl;
   return kSuccess;
}

int BIEventsManagerInterface::configureSettings( const BIEventsHandle& hEvents, const struct BIEventsSettings& settings )
{
   m_IOService.post( std::bind( &BIEventsManagerInterface::configureSettingsImpl, this, hEvents, settings ));
   return kSuccess;
}

int BIEventsManagerInterface::configureSettingsImpl( const BIEventsHandle& hEvents, const struct BIEventsSettings& settings )
{
   std::lock_guard< std::recursive_mutex > lk( m_InfoMutex );

   std::map< BIEventsHandle, std::shared_ptr< BIEventsManagerImpl > >::const_iterator iter = m_InfoMap.find( hEvents );
   if( iter == m_InfoMap.end() )
      return kError;

   return iter->second->configureSettings( settings );
}

int BIEventsManagerInterface::setHandler( const BIEventsHandle& hEvents, BIEventsHandler * notificationHandler )
{
   int result = kError;
   if( notificationHandler != NULL )
   {
      // Just do the normal thing.
      m_IOService.post( std::bind( &BIEventsManagerInterface::setHandlerImpl, this, hEvents, notificationHandler ));
      result = kSuccess;
   }
   else // notificationHandler == NULL
   {
      std::mutex mutex;
      std::condition_variable cvar;

      // Unfortunately verbose functor class which is here to avoid use of C++ lambdas.
      struct MyFunctor
      {
         MyFunctor( BIEventsManagerInterface *parent, const BIEventsHandle& hEvents, BIEventsHandler*& notificationHandler, std::mutex& mutex, std::condition_variable& cvar, int& result )
            : mParent( parent ), mhEvents( hEvents ), mnotificationHandler( notificationHandler ), mMutex( mutex ), mCVar( cvar ), mResult( result ) {}

         void operator()( void )
         {
            std::lock_guard< std::mutex > lock( mMutex );
            mResult = mParent->setHandlerImpl( mhEvents, mnotificationHandler );
            mCVar.notify_all();
         }

         BIEventsManagerInterface *mParent;
         const BIEventsHandle& mhEvents;
         BIEventsHandler*& mnotificationHandler;
         std::mutex& mMutex;
         std::condition_variable& mCVar;
         int& mResult;
      };

      {
         // Block which needs to be synchronized
         std::unique_lock< std::mutex > lock( mutex ); // acquires the mutex

         MyFunctor *func = new MyFunctor( this, hEvents, notificationHandler, mutex, cvar, result );
         m_IOService.post( std::bind( &MyFunctor::operator(), func ));
         cvar.wait( lock ); // releases the mutex and waits on the condition (blocks caller thread)
         delete func; func = NULL; // Safe to delete functor now.
         lock.unlock(); // lock is reaquired, so .. release the associated mutex
      }

      // Force any events to run as a result of this operation
      process( -1 );
   }
   return result;
}

int BIEventsManagerInterface::setHandlerImpl( const BIEventsHandle& hEvents, BIEventsHandler * notificationHandler )
{
   std::lock_guard< std::recursive_mutex > lk( m_InfoMutex );

   std::map< BIEventsHandle, std::shared_ptr< BIEventsManagerImpl > >::const_iterator iter = m_InfoMap.find( hEvents );
   if( iter == m_InfoMap.end() )
      return kError;

   return iter->second->setHandler( notificationHandler );
}

int BIEventsManagerInterface::enable( const BIEventsHandle& hEvents )
{
   m_IOService.post( std::bind( &BIEventsManagerInterface::enableImpl, this, hEvents ));
   return kSuccess;
}

int BIEventsManagerInterface::enableImpl( const BIEventsHandle& hEvents )
{
   std::lock_guard< std::recursive_mutex > lk( m_InfoMutex );

   std::map< BIEventsHandle, std::shared_ptr< BIEventsManagerImpl > >::const_iterator iter = m_InfoMap.find( hEvents );
   if( iter == m_InfoMap.end() )
      return kError;

   return iter->second->enable();
}

int BIEventsManagerInterface::disable( const BIEventsHandle& hEvents )
{
   m_IOService.post( std::bind( &BIEventsManagerInterface::disableImpl, this, hEvents ));
   return kSuccess;
}

int BIEventsManagerInterface::disableImpl( const BIEventsHandle& hEvents )
{
   std::lock_guard< std::recursive_mutex > lk( m_InfoMutex );

   std::map< BIEventsHandle, std::shared_ptr< BIEventsManagerImpl > >::const_iterator iter = m_InfoMap.find( hEvents );
   if( iter == m_InfoMap.end() )
      return kError;

   return iter->second->disable();
}

int BIEventsManagerInterface::destroy( const BIEventsHandle& hEvents )
{
   m_IOService.post( std::bind( &BIEventsManagerInterface::destroyImpl, this, hEvents ));
   return kSuccess;
}

int BIEventsManagerInterface::destroyImpl( const BIEventsHandle & hEvents )
{
   std::lock_guard< std::recursive_mutex > lk( m_InfoMutex );

   std::map< BIEventsHandle, std::shared_ptr< BIEventsManagerImpl > >::iterator iter = m_InfoMap.find( hEvents );
   if( iter == m_InfoMap.end() )
      return kError;

   m_InfoMap.erase( hEvents ); // shared_ptr handles deletion
   return kSuccess;
}

int BIEventsManagerInterface::postEvent( const BIEventsHandle& hEvents, const BIEventHeader& eventHeader, const BIEventBody& eventBody, EventID& outEventID )
{
   RPCIdentifier id; // generate the ID here.
   outEventID = id.c_str();

   // pass args as a copy
   m_IOService.post( std::bind( &BIEventsManagerInterface::postEventImpl, this, hEvents, BIEventHeader( eventHeader ), BIEventBody( eventBody ), id ));
   return kSuccess;
}

int BIEventsManagerInterface::postEventImpl( const BIEventsHandle& hEvents, const BIEventHeader& eventHeader, const BIEventBody& eventBody, const CPCAPI2::RPCIdentifier& inEventID )
{
   std::lock_guard< std::recursive_mutex > lk( m_InfoMutex );

   std::map< BIEventsHandle, std::shared_ptr< BIEventsManagerImpl > >::const_iterator iter = m_InfoMap.find( hEvents );
   if( iter == m_InfoMap.end() )
   {
      ErrLog(<< "Error looking up handle " << hEvents << " in m_InfoMap; not posting BI event (type=" + eventHeader.Type);
      return kError;
   }

   return iter->second->postEvent( eventHeader, eventBody, inEventID );
}

int BIEventsManagerInterface::purgeCache( const BIEventsHandle& hEvents )
{
   m_IOService.post( std::bind( &BIEventsManagerInterface::purgeCacheImpl, this, hEvents ));
   return kSuccess;
}

int BIEventsManagerInterface::purgeCacheImpl( const BIEventsHandle& hEvents )
{
   std::lock_guard< std::recursive_mutex > lk( m_InfoMutex );

   std::map< BIEventsHandle, std::shared_ptr< BIEventsManagerImpl > >::const_iterator iter = m_InfoMap.find( hEvents );
   if (iter == m_InfoMap.end())
   {
      ErrLog(<< "BIEventsManagerInterface::purgeCacheImpl: couldn't find handle " << hEvents);
      return kError;
   }

   return iter->second->purgeCache();
}

BIEventsHelperInterface *BIEventsManagerInterface::getHelper( void )
{
   if( m_Helper == NULL )
      m_Helper = new BIEventsHelperInterface( this );

   return m_Helper;
}

int BIEventsManagerInterface::process(int timeout)
{
   // -1 == no wait
   if( m_Shutdown )
      return -1;

   resip::ReadCallbackBase* fp = m_CallbackFifo.getNext( timeout );
   while( fp )
   {
      (*fp)();
      delete fp;
      if( m_Shutdown )
         return -1;

      fp = m_CallbackFifo.getNext( -1 );
   }

   return kSuccess;
}

void BIEventsManagerInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
    m_IOService.post( std::bind( &BIEventsManagerInterface::setCallbackHookImpl, this, cbHook, context ));
}

void BIEventsManagerInterface::setCallbackHookImpl(void (*cbHook)(void*), void* context)
{
   std::lock_guard< std::recursive_mutex > lk( m_InfoMutex );

   m_CbHook = cbHook;
   m_Context = context;

   std::map< BIEventsHandle, std::shared_ptr< BIEventsManagerImpl > >::iterator iter = m_InfoMap.begin();
   while( iter != m_InfoMap.end() )
   {
      if( iter->second != NULL )
         iter->second->setCallbackHook( cbHook, context );
      ++iter;
   }
}

#ifdef CPCAPI2_AUTO_TEST
CPCAPI2::AutoTestReadCallback* BIEventsManagerInterface::process_test(int timeout)
{
   if( m_Shutdown )
      return NULL;

   resip::ReadCallbackBase* rcb = m_CallbackFifo.getNext( timeout );
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
      return fpCmd;
   if (rcb != NULL)
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   return NULL;
}
#endif
#endif
