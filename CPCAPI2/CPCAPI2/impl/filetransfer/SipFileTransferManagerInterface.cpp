#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1)

#include <algorithm>
#include <sstream>

#include "SipFileTransferManagerInterface.h"
#include "SipFileTransferInfo.h"
#include "SipFileTransferManagerImpl.h"
#include "../util/cpc_logger.h"
#include "../util/ResipConv.h"
#include "../util/IpHelpers.h"
#include "cpcapi2defs.h"
#include "phone/Phone.h"
#include "../account/SipAccountInterface.h"
#include "../phone/PhoneInterface.h"

// resip includes
#include <resip/stack/Mime.hxx>
#include <resip/stack/SdpContents.hxx>
#include <resip/dum/ServerInviteSession.hxx>
#include <resip/dum/ClientInviteSession.hxx>

// MSRP stack includes
#include <utils/msrp_mem.h>
#include <utils/msrp_string.h>
#include <msrp_uri.h>
#include <msrp_stack.h>
#include <msrp_session.h>
#include <msrp_message.h>
#include <msrp_content_type.h>

#include <sstream>
#include <ostream>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_FILETRANSFER

using CPCAPI2::SipAccount::SipAccountInterface;
using CPCAPI2::SipAccount::SipAccountManager;
using CPCAPI2::SipAccount::SipAccountHandle;
using CPCAPI2::SipAccount::SipAccountImpl;

namespace CPCAPI2
{
namespace SipFileTransfer
{

// struct passed through the msrp lib and back to us
struct CallbackInfo
{
   SipFileTransferManagerInterface *pThis;
   SipFileTransferHandle            fileTransfer;
   SipFileTransferItemHandle        fileTransferItem;
   unsigned short                   last_percent;
};

SipFileTransferManagerInterface::SipFileTransferManagerInterface( CPCAPI2::Phone *phone )
   : CPCAPI2::EventSource2<CPCAPI2::EventHandler<SipFileTransferHandler, SipAccount::SipAccountHandle> >(dynamic_cast<PhoneInterface*>( phone )),
     mAccountIf(NULL),
     mPhone( dynamic_cast<PhoneInterface*>( phone )),
     mAccountMapPtr(new AccountMap),
     mAccountMap(*mAccountMapPtr)
{
   mAccountIf = dynamic_cast< SipAccountInterface* >( SipAccountManager::getInterface( phone ));
}

SipFileTransferManagerInterface::~SipFileTransferManagerInterface()
{
   mAccountMap.clear();
}

CPCAPI2::PhoneInterface* SipFileTransferManagerInterface::phoneInterface()
{
   return mPhone;
}

void SipFileTransferManagerInterface::Release()
{
   delete this; // suicide
}

int SipFileTransferManagerInterface::setHandler(
   SipAccountHandle account,
   SipFileTransferHandler* handler )
{
   resip::ReadCallbackBase* setHandlerCmd = resip::resip_bind(&SipFileTransferManagerInterface::setHandlerImpl, this, account, handler);
   
   if (handler == NULL)
   {
      // removing the handler involves two steps:
      // 1. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);

      // 2. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      executeOnSdkThread(setHandlerCmd);
   }
   else
   {
      postToSdkThread(setHandlerCmd);
   }
   
   return kSuccess;
}

int SipFileTransferManagerInterface::setHandlerImpl( SipAccountHandle account, SipFileTransferHandler* handler )
{
   AccountMap::iterator it = mAccountMap.find(account);
   SipFileTransferManagerImpl* ftMan = (it == mAccountMap.end() ? NULL : it->second);
   if (ftMan == NULL)
   {
      // Try to get the invite session handle
      SipAccountImpl* acct = mAccountIf->getAccountImpl(account);

      if (!acct)
      {
         mAccountIf->fireError("Invalid account handle for SipFileTransferManager::setHandler");
         return kError;
      }

      ftMan = new SipFileTransferManagerImpl(this, mAccountMapPtr, mPhone, *acct);
      mPhone->getSdkModuleThread().registerEventHandler(ftMan);
      mAccountMap[account] = ftMan;
   }

   auto it2 = mHandlers.find(account);
   if (mHandlers.end() != it2)
   {
      removeAppHandler(it2->second, account);
   }

   mHandlers[account] = handler;
   if (nullptr != handler)
   {
      addAppHandler(handler, account);
   }

   return kSuccess;
}

SipFileTransferItemHandle SipFileTransferManagerInterface::createFileTransferItem( SipAccountHandle account )
{
   SipFileTransferItemHandle itemHandle = sNextFileTransferItemHandle++;

   // run a Dum command to create the file transfer structure in our collection
   postToSdkThread( resip::resip_bind(&SipFileTransferManagerInterface::createFileTransferItemImpl, this, account, itemHandle ));
   return itemHandle;
}

int SipFileTransferManagerInterface::createFileTransferItemImpl( SipAccountHandle account, SipFileTransferItemHandle itemHandle )
{
   // Find the correct account (and therefore the Impl handle)
   AccountMap::iterator it = mAccountMap.find( account );
   if( it == mAccountMap.end() )
      return kError;

   SipAccountImpl* acct = mAccountIf->getAccountImpl( account );
   if( !acct )
   {
      cpc::string msg = cpc::string("SipFileTransferManagerInterface::createFileTransferItem called with invalid account handle: " + cpc::to_string(account)) + 
                         cpc::string(" SipEventSubscriptionHandle invalid: " + cpc::to_string(itemHandle));
      mAccountIf->fireError((msg).c_str());
      return kError;
   }

   if( !acct->isEnabled() )
   {
      cpc::string msg = cpc::string("SipFileTransferManagerInterface::createFileTransferItem called before account enabled: " + cpc::to_string(account)) + 
                         cpc::string(", SipEventSubscriptionHandle invalid: " + cpc::to_string(itemHandle));
      mAccountIf->fireError((msg).c_str());
      return kError;
   }

   if( !it->second->getDum() )
   {
      cpc::string msg = cpc::string("SipFileTransferManagerInterface::createFileTransferItem called but DUM is not available: " + cpc::to_string(account)) + 
                         cpc::string(", SipEventSubscriptionHandle invalid: " + cpc::to_string(itemHandle));
      mAccountIf->fireError((msg).c_str());
      return kError;
   }

   // By default it is outgoing
   SipFileTransferItemInfoPtr pInfo( new SipFileTransferItemInfo( itemHandle ));
   it->second->setFileTransferItemInfo( itemHandle, pInfo );
   return kSuccess;
}

// create a new, outbound file transfer
SipFileTransferHandle SipFileTransferManagerInterface::createFileTransfer( SipAccountHandle account )
{
   // Create a new SipFileTransferInfo and store it in our collection. Return the handle
   SipFileTransferHandle sessionHandle = sNextFileTransferHandle++;

   postToSdkThread( resip::resip_bind( &SipFileTransferManagerInterface::createFileTransferImpl, this, account, sessionHandle ));
   return sessionHandle;
}

int SipFileTransferManagerInterface::createFileTransferImpl( SipAccountHandle account, SipFileTransferHandle sessionHandle )
{
   AccountMap::iterator it = mAccountMap.find( account );
   if( it == mAccountMap.end() )
      return kError;

   SipAccountImpl* acct = mAccountIf->getAccountImpl( account );
   if( !acct )
   {
      cpc::string msg = cpc::string("SipFileTransferManagerInterface::createFileTransfer called with invalid account handle: " + cpc::to_string(account)) + 
                         cpc::string(" SipEventSubscriptionHandle invalid: " + cpc::to_string(sessionHandle));
      mAccountIf->fireError((msg).c_str());
   }
   else if( !acct->isEnabled() )
   {
      cpc::string msg = cpc::string("SipFileTransferManagerInterface::createFileTransfer called before account enabled: " + cpc::to_string(account)) + 
                         cpc::string(", SipEventSubscriptionHandle invalid: " + cpc::to_string(sessionHandle));
      mAccountIf->fireError((msg).c_str());
   }
   else if( !it->second->getDum() )
   {
      cpc::string msg = cpc::string("SipFileTransferManagerInterface::createFileTransfer called but DUM is not available: " + cpc::to_string(account)) + 
                         cpc::string(", SipEventSubscriptionHandle invalid: " + cpc::to_string(sessionHandle));
      mAccountIf->fireError((msg).c_str());
   }
   else
   {
      SipFileTransferInfo * pInfo( new SipFileTransferInfo( sessionHandle, it->second->getDum(), it->second ));
      pInfo->accountHandle = account;
      pInfo->m_isActive = true; // TODO: need some kind of setting here
      it->second->setFileTransferInfo( sessionHandle, pInfo );
   }
   return kSuccess;
}

int SipFileTransferManagerInterface::addParticipant( SipFileTransferHandle fileTransfer, const cpc::string& targetAddress )
{
   postToSdkThread( resip::resip_bind( &SipFileTransferManagerInterface::addParticipantImpl, this, fileTransfer, targetAddress ));
   return kSuccess;
}

int SipFileTransferManagerInterface::addParticipantImpl( SipFileTransferHandle fileTransfer, const cpc::string& targetAddress )
{
   SipFileTransferInfo * pInfo = getFileTransferInfo( fileTransfer );
   if( pInfo == NULL )
      return kError;

   resip::NameAddr targetNameAddr;
   if (!ResipConv::stringToAddr(targetAddress, targetNameAddr))
   {
      // fire to specific subscription event handler if we can
      cpc::string msg = "Failed to parse participant URI '" + targetAddress + "'";
      AccountMap::iterator it = mAccountMap.find(pInfo->accountHandle);

      SipFileTransferManagerImpl* pMan = (it == mAccountMap.end() ? NULL : it->second);
      if( pMan )
         pMan->fireError( fileTransfer, msg );
      else
         mAccountIf->fireError( msg ); // if no event handler available, fall back on account handler

      return kSuccess;
   }
   pInfo->targetAddress = targetNameAddr;
   return kSuccess;
}

// start an outbound file transfer. Build the SDP offer and send it in an INVITE
int SipFileTransferManagerInterface::start( SipFileTransferHandle fileTransfer )
{
   // the SIP session begins. Note, we won't actually know the remote msrp information until
   // after the 200OK comes back and we can process the SDP.
   postToSdkThread( resip::resip_bind( &SipFileTransferManagerInterface::startImpl, this, fileTransfer ));
   return kSuccess;
}

int SipFileTransferManagerInterface::startImpl( SipFileTransferHandle fileTransfer )
{
   SipFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if( pInfo == NULL )
      return kError;

   AccountMap::iterator itAcct = mAccountMap.find(pInfo->accountHandle);
   if( itAcct == mAccountMap.end() )
      return kError;

   SipFileTransferManagerImpl* acct = itAcct->second;
   if( pInfo->targetAddress.uri().getAor().empty() )
   {
      acct->fireError( fileTransfer, "Cannot start file transfer. No participants have been added" );
      return kSuccess;
   }
   if( pInfo->transferItems.size() < 1 )
   {
      acct->fireError( fileTransfer, "Cannot start file transfer. No files have been added" );
      return kSuccess;
   }

   std::map< SipFileTransferItemHandle, SipFileTransferItemInfoWeakPtr >::iterator iter;
   for( iter = pInfo->transferItems.begin() ; iter != pInfo->transferItems.end() ; ++iter )
   {
      SipFileTransferItemInfoPtr pItem = iter->second.lock();
      if( pItem == NULL )
         continue;

      cpc::string filename = pItem->detail.localfilePath;
      filename.append("/");
      filename.append(pItem->detail.localfileName);
      if (!phoneInterface()->hasFilePermission(Permission_ReadFiles, filename))
      {
         phoneInterface()->requestPermission(0, Permission_ReadFiles);
         mOutgoingPendingPermission.insert(fileTransfer);
         InfoLog(<< "Cannot send file due to missing permission");
         return kError;
      }
   }

   resip::NameAddr targetNameAddr = pInfo->targetAddress;

   const resip::Data& sdpStr = acct->makeFileTransferSdp(
      fileTransfer,
      "sendonly",
      msrp_stack_get_listening_port( acct->getMSRPStack() ),
      targetNameAddr.uri().host() // seems reasonable .. ?
   );

   resip::HeaderFieldValue hfv(sdpStr.c_str(), (unsigned int)sdpStr.size());
   resip::SdpContents sdp(hfv, resip::Mime("application","sdp"));

   // transform address if transformer defined
   AddressTransformer* transformer = mPhone->getAddressTransformer();
   if(transformer != NULL)
   {
      cpc::string transformedAddress = "";
      AddressTransformationContext context;
      context.addressUsageType = AddressUsageType::AddressUsageType_SipConversation;

      SipAccount::SipAccountImpl* acct = mAccountIf->getAccountImpl(pInfo->accountHandle);
      if (!acct)
      {
         mAccountIf->fireError("Invalid account handle for SipFileTransferManager::start");
         return kError;
      }

      context.registrationDomain = acct->getSettings().domain;
      if(transformer->applyTransformation((targetNameAddr.uri().getAOR(false).data()), context, transformedAddress) == kSuccess)
      {
         ResipConv::stringToAddr(transformedAddress, targetNameAddr);
      }
   }
   resip::SharedPtr<resip::SipMessage> invite = acct->getDum()->makeInviteSession( targetNameAddr, &sdp, resip::DialogUsageManager::None, 0, pInfo );
   invite->header( resip::h_ContentType ).type()    = "application";
   invite->header( resip::h_ContentType ).subType() = "sdp";
   acct->getDum()->send(invite);
   return kSuccess;
}

int SipFileTransferManagerInterface::end( SipFileTransferHandle fileTransfer )
{
   // Hangup the SIP call
   postToSdkThread( resip::resip_bind( &SipFileTransferManagerInterface::endImpl, this, fileTransfer ));
   return kSuccess;
}

int SipFileTransferManagerInterface::endImpl( SipFileTransferHandle fileTransfer )
{
   SipFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if( pInfo == NULL )
      return kError;

   AccountMap::iterator itAcct = mAccountMap.find( pInfo->accountHandle );
   if( itAcct != mAccountMap.end() )
   {
      // delegate to the ManagerImpl
      SipFileTransferManagerImpl* acct = itAcct->second;
      if( acct != NULL )
         acct->end( fileTransfer );
   }

   mIncomingPendingPermission.erase( fileTransfer );
   mOutgoingPendingPermission.erase( fileTransfer );

   return kSuccess;
}

// Thunk into the class
static void msrp_message_progress( msrp_message_t *p_message, intptr_t user_data, unsigned short percent )
{
   CallbackInfo *pInfo = ( CallbackInfo * ) user_data;
   if( pInfo == NULL )
      return;

   // Avoid reporting the same percentage more than once
   if( pInfo->last_percent != percent )
   {
      pInfo->last_percent = percent;
      pInfo->pThis->messageProgressCallback( p_message, pInfo->fileTransfer, pInfo->fileTransferItem, percent );
   }
}

// Thunk into the class
static void msrp_message_recv_complete( msrp_message_t * p_message, intptr_t user_data )
{
   CallbackInfo *pInfo = ( CallbackInfo * ) user_data;
   if( pInfo == NULL || p_message == NULL )
      return;

   pInfo->pThis->messageRecvCompletedCallback( p_message, pInfo->fileTransfer, pInfo->fileTransferItem );
}

// Thunk into the class
static void msrp_message_cancelled( msrp_message_t *p_message, intptr_t user_data )
{
   CallbackInfo *pInfo = ( CallbackInfo * ) user_data;
   if( pInfo == NULL )
      return;

   pInfo->pThis->messageCancelledCallback( p_message, pInfo->fileTransfer, pInfo->fileTransferItem );
}

static void msrp_message_destroyed( msrp_message_t *p_message, intptr_t user_data )
{
   CallbackInfo *pInfo = ( CallbackInfo * ) user_data;
   if( pInfo == NULL || p_message == NULL )
      return;

   delete pInfo;
   msrp_message_set_user_data( p_message, 0 );
}

// Thunk into the class
static void msrp_session_message_created( msrp_session_t *p_session, msrp_message_t *p_message, intptr_t user_data )
{
   CallbackInfo *pUserData = ( CallbackInfo * ) user_data;
   if( pUserData == NULL )
      return;

   // register the message callbacks now
   msrp_message_callbacks_t callbacks;
   msrp_memset( &callbacks, 0, sizeof( msrp_message_callbacks_t ));
   callbacks.p_message_progress_func      = msrp_message_progress;
   callbacks.p_message_recv_complete_func = msrp_message_recv_complete;
   callbacks.p_message_cancelled_func     = msrp_message_cancelled;
   callbacks.p_message_destroyed_func     = msrp_message_destroyed;

   msrp_message_install_callbacks( p_message, &callbacks );
   msrp_message_set_user_data( p_message, user_data );

   // Call back into the object
   pUserData->pThis->messageCreatedCallback( p_session, p_message, pUserData->fileTransfer, pUserData->fileTransferItem );
}

int SipFileTransferManagerInterface::provisionalAccept( SipFileTransferHandle fileTransfer )
{
   // Provisionally Accept (180 Ringing) the incoming SIP call
   postToSdkThread( resip::resip_bind( &SipFileTransferManagerInterface::provisionalAcceptImpl, this, fileTransfer ));
   return kSuccess;
}

int SipFileTransferManagerInterface::provisionalAcceptImpl( SipFileTransferHandle fileTransfer )
{
   SipFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if( pInfo == NULL )
      return kError;

   if( pInfo->serverInviteSessionHandle.isValid())
      pInfo->serverInviteSessionHandle->provisional();

   return kSuccess;
}

// This method serves two purposes:
// 1) in the case of outbound file item, adds the items to the file transfer.
// 2) in the case of inbound file item, updates the file transfer item data.
int SipFileTransferManagerInterface::configureFileTransferItems( SipFileTransferHandle fileTransfer, const SipFileTransferItems& fileItems )
{
   postToSdkThread( resip::resip_bind( &SipFileTransferManagerInterface::configureFileTransferItemsImpl, this, fileTransfer, fileItems ));
   return kSuccess;
}

int SipFileTransferManagerInterface::configureFileTransferItemsImpl( SipFileTransferHandle fileTransfer, const SipFileTransferItems& fileItems )
{
   // Update the info we have with the read-write members from the structure
   SipFileTransferInfo *pTransfer = getFileTransferInfo( fileTransfer );
   if( pTransfer == NULL )
      return kError;

   // Fetch the account and therefore the SipFileTransferManagerImpl
   AccountMap::iterator itAcct = mAccountMap.find( pTransfer->accountHandle );
   SipFileTransferManagerImpl *pImpl = itAcct->second;
   if( pImpl == NULL )
      return kError;

   // Loop over the items and update the known info
   for( SipFileTransferItems::const_iterator iter = fileItems.begin() ; iter != fileItems.end() ; ++iter )
   {
      // Item should exist (create... should be called first)
      SipFileTransferItemInfoPtr pItem = pImpl->getFileTransferItemInfo( iter->handle );
      if( pItem == NULL )
         continue;

      if( iter->localfileName.size() > 0 )
         pItem->detail.localfileName = iter->localfileName;

      if( iter->localfilePath.size() > 0 )
         pItem->detail.localfilePath = iter->localfilePath;

      // Outbound item handling
      if( !pItem->detail.isIncoming )
      {
         // Make sure the specified file is good
         if( !FileMap::FileExists( iter->localfileName, iter->localfilePath ) ||
             !FileMap::IsRegularFile( iter->localfileName, iter->localfilePath ))
            continue;

         // Set the remote filename if not already set
         if( pItem->detail.remotefileName.size() == 0 )
         {
            pItem->detail.remotefileName = ( iter->remotefileName.size() == 0 ) ?
               iter->localfileName :
               iter->remotefileName;

            // Ensure that SOMETHING is set.
            if( pItem->detail.remotefileName.size() == 0 )
               pItem->detail.remotefileName = "unknown.bin";
         }

         // Set the file size
         pItem->detail.fileSizeBytes = FileMap::GetFileSize( iter->localfileName, iter->localfilePath );
      }

      // Inbound items: update the accepted state
      if( pItem->detail.isIncoming )
         pItem->detail.acceptedState = iter->acceptedState;

      // Add the item to the containing transfer if previous checks passed
      pTransfer->transferItems[ iter->handle ] = pItem;
   }

   return kSuccess;
}

// method called to accept an incoming file transfer
int SipFileTransferManagerInterface::accept( SipFileTransferHandle fileTransfer )
{
   postToSdkThread( resip::resip_bind( &SipFileTransferManagerInterface::acceptImpl, this, fileTransfer ));
   return kSuccess;
}

int SipFileTransferManagerInterface::acceptImpl( SipFileTransferHandle fileTransfer )
{
   // Accept (200OK) an incoming SIP call
   SipFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if( pInfo == NULL )
      return kError;

   // Check that the account is valid
   AccountMap::iterator itAcct = mAccountMap.find( pInfo->accountHandle );
   if( itAcct == mAccountMap.end() )
      return kError;

   // One can only accept server invite sessions
   SipFileTransferManagerImpl* acct = itAcct->second;
   if( !pInfo->serverInviteSessionHandle.isValid() )
      return kError;

   std::map< SipFileTransferItemHandle, SipFileTransferItemInfoWeakPtr >::iterator iter;
   for( iter = pInfo->transferItems.begin() ; iter != pInfo->transferItems.end() ; ++iter )
   {
      SipFileTransferItemInfoPtr pItem = iter->second.lock();
      if( pItem == NULL )
         continue;

      // Don't accept file transfers which are not incoming
      if( !pItem->detail.isIncoming )
         continue;

      cpc::string filename = pItem->detail.localfilePath;
      filename.append("/");
      filename.append(pItem->detail.localfileName);
      if (!phoneInterface()->hasFilePermission(Permission_WriteFiles, filename))
      {
        phoneInterface()->requestPermission(0, Permission_WriteFiles);
        mIncomingPendingPermission.insert(fileTransfer);
        InfoLog(<< "Cannot accept file due to missing permission");
        return kError;
      }
   }

   // Setup the media (msrp) before sending the message
   msrp_stack_t *p_stack = acct->getMSRPStack();
   for( iter = pInfo->transferItems.begin() ; iter != pInfo->transferItems.end() ; ++iter )
   {
      msrp_session_t *p_session = NULL;
      SipFileTransferItemInfoPtr pItem = iter->second.lock();
      if( pItem == NULL )
         continue;

      // Don't accept file transfers which are not incoming
      if( !pItem->detail.isIncoming )
         continue;

      // Don't accept file transfers which are not marked as such
      if( pItem->detail.acceptedState != ftitem_accepted )
         continue;

      // Create the session (this allows the stack to forward message requets)
      p_session = msrp_stack_session_create( p_stack, pItem->m_pLocal, pItem->m_pRemote, pInfo->m_isActive );
      msrp_list_push( pInfo->m_pSessions, p_session );

      // Register a listener for newly created messages on the session
      msrp_session_callbacks_t session_callbacks;
      msrp_memset( &session_callbacks, 0, sizeof( msrp_session_callbacks_t ));
      session_callbacks.p_message_created_func = msrp_session_message_created;

      // set up the user data for the callback
      CallbackInfo *pUserData     = new CallbackInfo; // deallocated in message_destroyed cbk
      pUserData->pThis            = this;
      pUserData->fileTransfer     = fileTransfer;
      pUserData->fileTransferItem = iter->first;
      pUserData->last_percent     = 0;

      msrp_session_set_user_data( p_session, ( intptr_t ) pUserData );
      msrp_session_install_callbacks( p_session, &session_callbacks );
   }

   // build the SDP response and send
   resip::Data mediaDir = ( pInfo->m_OfferedAsSendRecv ? "sendrecv" : "recvonly" ); // the default, per the file transfer draft
   const resip::Data& sdpStr = acct->makeFileTransferSdp(
      fileTransfer,
      mediaDir,
      msrp_stack_get_listening_port( acct->getMSRPStack() )
   );
   resip::HeaderFieldValue hfv(sdpStr.c_str(), (unsigned int)sdpStr.size());
   SdpContents sdp(hfv, resip::Mime("application","sdp"));
   pInfo->serverInviteSessionHandle->provideAnswer(sdp);
   pInfo->serverInviteSessionHandle->accept();
   return kSuccess;
}

void SipFileTransferManagerInterface::messageProgressCallback(
   msrp_message_t *p_message,
   SipFileTransferHandle fileTransfer,
   SipFileTransferItemHandle fileTransferItem,
   unsigned short percent )
{
   SipFileTransferInfo * pInfo = getFileTransferInfo( fileTransfer );
   if( pInfo == NULL )
      return;

   AccountMap::iterator itAcct = mAccountMap.find( pInfo->accountHandle );
   if( itAcct != mAccountMap.end() )
   {
      SipFileTransferManagerImpl* acct = itAcct->second;
      acct->fireProgress( fileTransfer, fileTransferItem, percent );
   }
}

void SipFileTransferManagerInterface::messageRecvCompletedCallback(
   msrp_message_t *p_message,
   SipFileTransferHandle fileTransfer,
   SipFileTransferItemHandle fileTransferItem )
{
   // This is a callback coming from the msrp lib, which is already in the appropriate thread.
   SipFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if( pInfo == NULL )
      return;

   AccountMap::iterator itAcct = mAccountMap.find( pInfo->accountHandle );
   if( itAcct != mAccountMap.end() )
   {
      SipFileTransferManagerImpl* acct = itAcct->second;
      SipFileTransferItemInfoPtr pItem;

      // erase the item from the list
      SipFileTransferItemInfoMap::iterator it = pInfo->transferItems.find( fileTransferItem );
      if( it != pInfo->transferItems.end() )
      {
         if(( pItem = it->second.lock() ) != NULL )
         {
            pInfo->transferItems.erase( it ); // remove weak_ptr reference
            acct->removeFileTransferItemInfo( pItem->detail.handle ); // remove shared_ptr reference
            acct->fireItemEnded( pItem->detail.handle, fileTransfer, FileTransferItemEndReason_Complete ); // fire
         }
      }
   }

   /* Commented out on receiver side, normally sender should do this

   // Check if there are no more file transfer items, then automatically
   // END the file transfer.
   if( pInfo->transferItems.size() == 0 )
   {
      // Call the non-impl version of "end". This results in the end request being
      // delayed and marshalled again into this thread. This is needed because the
      // recv completed callback comes in the msrp message processing and deleting
      // the session (as the end call does) in the middle of the processing loop can
      // cause problems. So, mimick the Application doing a callback.
      end( fileTransfer );
   }
   */
}

void SipFileTransferManagerInterface::messageCancelledCallback(
   msrp_message_t *p_message,
   SipFileTransferHandle fileTransfer,
   SipFileTransferItemHandle fileTransferItem )
{
   SipFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if( pInfo == NULL )
      return;

   AccountMap::iterator itAcct = mAccountMap.find( pInfo->accountHandle );
   if( itAcct != mAccountMap.end() )
   {
      SipFileTransferManagerImpl* acct = itAcct->second;
      SipFileTransferItemInfoPtr pItem;

      // erase the item from the list
      SipFileTransferItemInfoMap::iterator it = pInfo->transferItems.find( fileTransferItem );
      if( it != pInfo->transferItems.end() )
      {
         if(( pItem = it->second.lock() ) != NULL )
         {
            pInfo->transferItems.erase( it );
            acct->fireItemEnded( pItem->detail.handle, fileTransfer, FileTransferItemEndReason_Interrupted );
            acct->removeFileTransferItemInfo( pItem->detail.handle );
         }
      }
   }
}

void SipFileTransferManagerInterface::messageCreatedCallback(
   msrp_session_t *p_session, msrp_message_t *p_message,
   SipFileTransferHandle fileTransfer, SipFileTransferItemHandle fileTransferItem )
{
   SipFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if( pInfo == NULL )
      return;

   SipFileTransferItemInfoWeakPtr pWeak = pInfo->transferItems[ fileTransferItem ];
   SipFileTransferItemInfoPtr pItem = pWeak.lock();
   if( pItem == NULL )
      return;

   // Create the file and truncate it to the appropriate size. NB: if the file exists,
   // it will be overwritten with new bytes once the transfer begins.
   pItem->m_Map = new FileMap( pItem->detail.localfileName, pItem->detail.localfilePath, pItem->detail.fileSizeBytes );

   // Set the backing store on the message
   if( pItem->m_Map->IsValid() )
      msrp_message_set_backing_store( p_message, ( uint8_t * ) pItem->m_Map->GetData() );

   // remember the message in our structure in case it needs to be cancelled.
   pItem->m_pMessage = p_message;
}


int SipFileTransferManagerInterface::reject( SipFileTransferHandle fileTransfer, unsigned int rejectReason )
{
   postToSdkThread( resip::resip_bind( &SipFileTransferManagerInterface::rejectImpl, this, fileTransfer, rejectReason ));
   return kSuccess;
}

int SipFileTransferManagerInterface::rejectImpl( SipFileTransferHandle fileTransfer, unsigned int rejectReason )
{
   SipFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if( pInfo == NULL )
      return kError;

   AccountMap::iterator itAcct = mAccountMap.find( pInfo->accountHandle );
   if( itAcct != mAccountMap.end() )
   {
      //SipFileTransferManagerImpl* acct = itAcct->second;
      if( pInfo->serverInviteSessionHandle.isValid() )
         pInfo->serverInviteSessionHandle->reject( rejectReason );
   }
   return kSuccess;
}

int SipFileTransferManagerInterface::cancelItem( SipFileTransferHandle fileTransfer,
   SipFileTransferItemHandle fileTransferItem )
{
   postToSdkThread( resip::resip_bind( &SipFileTransferManagerInterface::cancelItemImpl, this, fileTransfer, fileTransferItem ));
   return kSuccess;
}

int SipFileTransferManagerInterface::cancelItemImpl( SipFileTransferHandle fileTransfer,
   SipFileTransferItemHandle fileTransferItem )
{
   SipFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if( pInfo == NULL )
      return kError;

   SipFileTransferItemInfoWeakPtr pWeak = pInfo->transferItems[ fileTransferItem ];
   SipFileTransferItemInfoPtr pItem = pWeak.lock();
   if( pItem != NULL )
   {
      pItem->detail.acceptedState = ftitem_rejected;
      if( pItem->m_pMessage != NULL )
         msrp_message_cancel( pItem->m_pMessage ); // cancel whatever we find
   }

   return kSuccess;
}

SipFileTransferInfo *SipFileTransferManagerInterface::getFileTransferInfo( SipFileTransferHandle h )
{
   SipFileTransferInfo *result = NULL;
   AccountMap::iterator itAcct = mAccountMap.begin();
   for (; itAcct != mAccountMap.end(); ++itAcct)
   {
      // Just return the first one we find.
      SipFileTransferInfo * pInfo = itAcct->second->getFileTransferInfo( h );
      if( pInfo != NULL )
      {
         result = pInfo;
         break;
      }
   }
   return result;
}

void SipFileTransferManagerInterface::onPermissionGranted(int requestCode, CPCAPI2::Permission permission)
{
  if (Permission_ReadFiles == permission)
  {
    InfoLog(<< "Granted read file permission. Sending pending outgoing files.");
    for (PendingTransfersList::iterator it = mOutgoingPendingPermission.begin(); it != mOutgoingPendingPermission.end(); ++it)
    {
      postToSdkThread( resip::resip_bind( &SipFileTransferManagerInterface::startImpl, this, *it ));
    }
    mOutgoingPendingPermission.clear();
  }
  else if (Permission_WriteFiles == permission)
  {
    InfoLog(<< "Granted write file permission. Accepting pending incoming files.");
    for (PendingTransfersList::iterator it = mIncomingPendingPermission.begin(); it != mIncomingPendingPermission.end(); ++it)
    {
      postToSdkThread( resip::resip_bind( &SipFileTransferManagerInterface::acceptImpl, this, *it ));
    }
    mIncomingPendingPermission.clear();
  }
}

std::ostream& operator<<(std::ostream& os, const NewFileTransferEvent& evt)
{
   return os << "NewFileTransferEvent";
}

std::ostream& operator<<(std::ostream& os, const FileTransferConfiguredEvent& evt)

{
   return os << "FileTransferConfiguredEvent";
}

std::ostream& operator<<(std::ostream& os, const FileTransferEndedEvent& evt)
{
   return os << "FileTransferEndedEvent";
}

std::ostream& operator<<(std::ostream& os, const FileTransferItemEndedEvent& evt)
{
   return os << "FileTransferItemEndedEvent";
}

std::ostream& operator<<(std::ostream& os, const FileTransferItemProgressEvent& evt)
{
   return os << "FileTransferItemProgressEvent";
}

std::ostream& operator<<(std::ostream& os, const SipFileTransfer::ErrorEvent& evt)
{
   return os << "SipFileTransfer::ErrorEvent";
}

} // namespace SipFileTransfer
} // namespace CPCAPI2

#endif
