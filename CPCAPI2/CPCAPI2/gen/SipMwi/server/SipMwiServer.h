#include "ISipMwi.h"
#include "impl/jsonrpc/IJsonRpcServerModuleProcessing.h"

#ifndef JSONRPC_CPCAPI2_SIPMWI_SIPMWISERVER_H
#define JSONRPC_CPCAPI2_SIPMWI_SIPMWISERVER_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipMwi {
class SipMwiServer
{
public:
    SipMwiServer(::CPCAPI2::IJsonRpcServerModuleProcessing& transport, ISipMwi& impl) :
        mTransport(transport),
        mImpl(impl)
    {
        mTransport.registerFunction("SipMwi::registerForEventQueue", jsonrpccxx::methodHandle(&ISipMwi::registerForEventQueue, mImpl), {{ "phoneHandle" }, { "accountHandle" }});
	mTransport.registerFunction("SipMwi::unRegisterForEventQueue", jsonrpccxx::methodHandle(&ISipMwi::unRegisterForEventQueue, mImpl), {{ "phoneHandle" }, { "accountHandle" }});
	mTransport.registerFunction("SipMwi::createSubscription", jsonrpccxx::methodHandle(&ISipMwi::createSubscription, mImpl), {{ "phoneHandle" }, { "accountHandle" }});
	mTransport.registerFunction("SipMwi::start", jsonrpccxx::methodHandle(&ISipMwi::start, mImpl), {{ "phoneHandle" }, { "mwiSubscriptionHandle" }});
	mTransport.registerFunction("SipMwi::end", jsonrpccxx::methodHandle(&ISipMwi::end, mImpl), {{ "phoneHandle" }, { "mwiSubscriptionHandle" }});
    }

private:
    ::CPCAPI2::IJsonRpcServerModuleProcessing& mTransport;
    ISipMwi& mImpl;
}; // class SipMwiServer
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipMwi
#endif // JSONRPC_CPCAPI2_SIPMWI_SIPMWISERVER_H
