#include "jsonrpc/OptionalSerialization.h"
#include "jsonrpc/StringSerialization.h"
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONTRANSFERREQUESTEVENT_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONTRANSFERREQUESTEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationTransferRequestEvent
  {
  public:
    std::optional<int32_t> conversationHandle;
    std::optional<cpc::string> transferTargetAddress;
    std::optional<cpc::string> transferTargetDisplayName;
    std::optional<int32_t> transferTargetConversation;

    SipConversationTransferRequestEvent() = default;

    SipConversationTransferRequestEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationTransferRequestEvent& o)
    {
      j = nlohmann::json::object();
       if (o.conversationHandle) { j["conversationHandle"] = *(o.conversationHandle); }
     if (o.transferTargetAddress) { j["transferTargetAddress"] = *(o.transferTargetAddress); }
     if (o.transferTargetDisplayName) { j["transferTargetDisplayName"] = *(o.transferTargetDisplayName); }
     if (o.transferTargetConversation) { j["transferTargetConversation"] = *(o.transferTargetConversation); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationTransferRequestEvent& o)
    {
     if (j.contains("conversationHandle")) { o.conversationHandle = j.at("conversationHandle"); }
     if (j.contains("transferTargetAddress")) { o.transferTargetAddress = j.at("transferTargetAddress"); }
     if (j.contains("transferTargetDisplayName")) { o.transferTargetDisplayName = j.at("transferTargetDisplayName"); }
     if (j.contains("transferTargetConversation")) { o.transferTargetConversation = j.at("transferTargetConversation"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONTRANSFERREQUESTEVENT_H
