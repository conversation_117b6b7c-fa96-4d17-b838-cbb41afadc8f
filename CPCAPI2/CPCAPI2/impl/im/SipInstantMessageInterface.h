#pragma once

#if !defined(CPCAPI2_INSTANT_MESSAGE_INTERFACE_H)
#define CPCAPI2_INSTANT_MESSAGE_INTERFACE_H

#include "cpcapi2defs.h"
#include "im/SipInstantMessage.h"
#include "im/SipInstantMessageHandler.h"
#include "../phone/PhoneModule.h"
#include "../phone/PhoneInterface.h"
#include "SipInstanceMessageManagerInternal.h"

#include <ctime>
#include <map>

namespace CPCAPI2
{

namespace SipAccount
{
class SipAccountInterface;
}

namespace SipInstantMessage
{
class SipInstantMessageImpl;

class SipInstantMessageInterface : public CPCAPI2::EventSource2<CPCAPI2::EventHandler<SipInstantMessageHandler, CPCAPI2::SipAccount::SipAccountHandle> >,
                                   public SipInstantMessageManager,
                                   public SipInstantMessageManagerInternal,
                                   public PhoneModule
{
public:
   SipInstantMessageInterface(Phone* cpcPhone);
   virtual ~SipInstantMessageInterface();

   FORWARD_EVENT_PROCESSOR(SipInstantMessageInterface);

   virtual void Release() OVERRIDE;

   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipInstantMessageHandler* handler) OVERRIDE;
   virtual int acceptMimeType(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const cpc::string& mimeType) OVERRIDE;
   virtual int acceptMimeType(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const SipInstantMessageManager::MimeType mimeType) OVERRIDE;
   virtual int acceptIncoming(
         SipInstantMessageHandle im,
         unsigned int statusCode = 200) OVERRIDE;
   virtual int rejectIncoming(
         SipInstantMessageHandle im,
         unsigned int statusCode,
         const cpc::string& reasonText) OVERRIDE;
   virtual int rejectIncomingMimeType(
         SipInstantMessageHandle im,
         const cpc::vector<cpc::string>& acceptMimeTypes) OVERRIDE;
   virtual SipInstantMessageHandle sendMessage(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const cpc::string& targetAddress,
         const char* content,
         unsigned int contentLength,
         SipInstantMessageManager::MimeType mimeType,
         const cpc::string& messageType = "") OVERRIDE;
   virtual void sendMessage(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipInstantMessageHandle imHandle,
         const cpc::string& targetAddress,
         const char* content,
         unsigned int contentLength,
         SipInstantMessageManager::MimeType mimeType,
         const cpc::string& messageType = "") OVERRIDE;
   virtual SipInstantMessageHandle sendMessage(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const cpc::string& targetAddress,
         const char* content,
         unsigned int contentLength,
         const cpc::string& customMimeType,
         const cpc::string& messageType = "") OVERRIDE;
   virtual void sendMessage(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipInstantMessageHandle imHandle,
         const cpc::string& targetAddress,
         const char* content,
         unsigned int contentLength,
         const cpc::string& customMimeType,
         const cpc::string& messageType = "") OVERRIDE;
   virtual int setIsComposingMessage(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const cpc::string& targetAddress,
         SipInstantMessageManager::MimeType mimeType,
         struct std::tm* datetime = 0,
         int refreshInterval = 90,
         int idleInterval = 15) OVERRIDE;
   virtual int setIsComposingMessage(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const cpc::string& targetAddress,
         const cpc::string& customMimeType,
         struct std::tm* datetime = 0,
         int refreshInterval = 90,
         int idleInterval = 15) OVERRIDE;

private:
   struct SendMessageInfo
   {
      cpc::string content;
      SipInstantMessageManager::MimeType mimeType;
      cpc::string customMimeType;
      cpc::string messageType;
   };
   int setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipInstantMessageHandler* handler);
   SipInstantMessageImpl* getIM(SipInstantMessageHandle h) const;
   int acceptMimeTypeImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& mimeType);
   int acceptMimeTypeEnumImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const SipInstantMessageManager::MimeType mimeType);
   int acceptIncomingImpl(
      SipInstantMessageHandle im,
      unsigned int statusCode);
   int rejectIncomingImpl(
      SipInstantMessageHandle im,
      unsigned int statusCode,
      const cpc::string& reasonText);
   int rejectIncomingMimeTypeImpl(
      SipInstantMessageHandle im,
      const cpc::vector<cpc::string>& acceptMimeTypes);
   int sendMessageImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipInstantMessageHandle im,
      const cpc::string& targetAddress,
      SendMessageInfo mi);
   int sendMessageCustomImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipInstantMessageHandle im,
      const cpc::string& targetAddress,
      SendMessageInfo mi);
   int setIsComposingMessageImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& targetAddress,
      MimeType mimeType,
      struct std::tm datetime,
      int refreshInterval,
      int idleInterval);
   int setIsComposingMessageCustomImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& targetAddress,
      const cpc::string& customMimeType,
      struct std::tm datetime,
      int refreshInterval,
      int idleInterval);

private:
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, SipInstantMessageImpl*> InstantMessageMap;
   std::shared_ptr<InstantMessageMap> mInstantMessageMapPtr;
   InstantMessageMap& mInstantMessageMap;
   std::map<CPCAPI2::SipAccount::SipAccountHandle, SipInstantMessageHandler*> mHandlers;
   CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;
   SipInstantMessageHandle mNextOutgoingHandle;
   PhoneInterface* mPhone;
};

std::ostream& operator<<(std::ostream& os, const SipIncomingInstantMessageEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipOutgoingInstantMessageEvent& evt);
std::ostream& operator<<(std::ostream& os, const IsComposingMessageEvent& evt);
std::ostream& operator<<(std::ostream& os, const SetIsComposingMessageSuccessEvent& evt);
std::ostream& operator<<(std::ostream& os, const SetIsComposingMessageFailureEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipInstantMessage::ErrorEvent& evt);
}
}
#endif // CPCAPI2_INSTANT_MESSAGE_INTERFACE_H
