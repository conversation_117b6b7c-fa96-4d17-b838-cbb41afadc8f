#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "JsonApiServer_HTTP.h"
#include "JsonApiServerInterface.h"
#include "jsonapi/JsonApiServerModule.h"
#include "cpcapi2utils.h"
#include "log/LocalLogger.h"
#include "phone/PhoneInterface.h"
#include "util/cpc_logger.h"
#include "util/FileUtils.h"
#include "util/HttpsServerImpl.h"

#include <stdio.h>
#include <ostream>
#include <fstream>
#include <locale>
//include <codecvt>
#include <iostream>
#include <sys/stat.h>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL
#define CP_LOCAL_LOGGER_VAR mLocalLogger

using namespace resip;

namespace CPCAPI2
{
namespace JsonApi
{
JsonApiServerHTTP::JsonApiServerHTTP(CPCAPI2::Phone* phone)
   : mServerThread(NULL),
     mPhone(phone),
     mUseHttps(false),
     mLocalLogger(static_cast<PhoneInterface*>(phone)->localLogger())
{
}

JsonApiServerHTTP::~JsonApiServerHTTP()
{
}

void JsonApiServerHTTP::startServer(const JsonApiServerConfig& serverConfig, std::function<void()> onPrivilegedAccessCompleted)
{
   if (mServerThread != NULL)
   {
      return; // already started
   }

   mConfig = serverConfig;

   // HTTP
   if (serverConfig.httpsCertificateFilePath.empty() || serverConfig.httpsPrivateKeyFilePath.empty() || 
      (!fileExists(serverConfig.httpsCertificateFilePath)) || (!fileExists(serverConfig.httpsPrivateKeyFilePath)))
   {
      DebugLog(<< "JsonApiServerHTTP::startServer(): starting http server on port: " << serverConfig.httpPort);
      LocalDebugLog("JsonApiServerHTTP::startServer(): starting http server on port: {}", serverConfig.httpPort);
      mWebHttpServer.reset(new CPCAPI2::JsonApi::HttpServer());
      mWebHttpServer->config.port = serverConfig.httpPort;
      mWebHttpServer->config.thread_pool_size = std::min<size_t>(4, std::thread::hardware_concurrency());
      startServerImpl(mWebHttpServer, serverConfig);
      mUseHttps = false;
   }
   else // HTTPS
   {
      DebugLog(<< "JsonApiServerHTTP::startServer(): starting https server on port: " << serverConfig.httpPort);
      LocalDebugLog("JsonApiServerHTTP::startServer(): starting https server on port: {}", serverConfig.httpPort);
      mWebHttpsServer.reset(new CPCAPI2::HttpsServerImpl(SslCipherOptions(), serverConfig.httpsCertificateFilePath.c_str(), serverConfig.httpsPrivateKeyFilePath.c_str(), serverConfig.httpsDiffieHellmanParamsFilePath.c_str()));
      mWebHttpsServer->config.port = serverConfig.httpPort;
      mWebHttpsServer->config.thread_pool_size = std::min<size_t>(4, std::thread::hardware_concurrency());
      startServerImpl(mWebHttpsServer, serverConfig);
      mUseHttps = true;
   }

   if (onPrivilegedAccessCompleted) onPrivilegedAccessCompleted();
}

template <typename T>
void JsonApiServerHTTP::startServerImpl(std::unique_ptr<typename SimpleWeb::Server<T> >& server, const JsonApiServerConfig& serverConfig)
{
   server->resource["^/jsonApi$"]["POST"] = [&](std::shared_ptr<typename SimpleWeb::Server<T>::Response> response, std::shared_ptr<typename SimpleWeb::Server<T>::Request> request) {
      try {
         handleRequest<T>(response, request);
      }
      catch (const std::exception &e) {
         *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n"
            << e.what();
      }
   };

   mServerThread = new std::thread([&, serverConfig]() {
      try
      {
         server->start();
         InfoLog(<< "exiting web server thread");
         LocalInfoLog("exiting web server thread");
      }
      catch (const boost::system::system_error& ex)
      {
         ErrLog(<< "error starting HTTP server: " << ex.what());
      }
   });
}

void JsonApiServerHTTP::stopServer()
{
   if (mWebHttpServer.get() != NULL)
   {
      mWebHttpServer->stop();
   }

   if (mWebHttpsServer.get() != NULL)
   {
      mWebHttpsServer->stop();
   }

   if (mServerThread != NULL)
   {
      mServerThread->join();
      delete mServerThread;
      mServerThread = NULL;
      mWebHttpServer.reset();
      mWebHttpsServer.reset();
   }
}

void JsonApiServerHTTP::addHttpResourceHandler(const std::string& method, const std::string& pathRegex, const std::function<void(const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Response>&, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Request>&)>& handler)
{
   if (mWebHttpServer)
   {
      mWebHttpServer->resource[pathRegex][method] = handler;
      mWebHttpServer->resource[pathRegex]["OPTIONS"] = [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Request>& request) {
         try {
            *response << "HTTP/1.1 200 OK\r\n"
               << "Access-Control-Allow-Origin: *\r\n"
               << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
         }
         catch (const std::exception &e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n"
               << e.what();
         }
      };
   }
}

void JsonApiServerHTTP::removeHttpResourceHandler(const std::string& method, const std::string& pathRegex)
{
   if (mWebHttpServer)
   {
      auto itRes = mWebHttpServer->resource.find(pathRegex);
      if (itRes != mWebHttpServer->resource.end())
      {
         itRes->second.erase(method);
         if (itRes->second.empty())
         {
            mWebHttpServer->resource.erase(itRes);
         }
      }
   }
}

void JsonApiServerHTTP::addHttpsResourceHandler(const std::string& method, const std::string& pathRegex, const std::function<void(const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>&, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>&)>& handler)
{
   if (mWebHttpsServer)
   {
      mWebHttpsServer->resource[pathRegex][method] = handler;
      mWebHttpsServer->resource[pathRegex]["OPTIONS"] = [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>& request) {
         try {
            *response << "HTTP/1.1 200 OK\r\n"
               << "Access-Control-Allow-Origin: *\r\n"
               << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
         }
         catch (const std::exception &e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n"
               << e.what();
         }
      };
   }
}

void JsonApiServerHTTP::removeHttpsResourceHandler(const std::string& method, const std::string& pathRegex)
{
   if (mWebHttpsServer)
   {
      auto itRes = mWebHttpsServer->resource.find(pathRegex);
      if (itRes != mWebHttpsServer->resource.end())
      {
         itRes->second.erase(method);
         if (itRes->second.empty())
         {
            mWebHttpsServer->resource.erase(itRes);
         }
      }
   }
}

// SDK thread of master SDK
void JsonApiServerHTTP::handleRequestInContext(unsigned int requestHandle, JsonApiUserContext* sdkContext)
{
   // StackLog(<< "JsonApiServerHTTP::handleRequestInContext(): request handle: " << requestHandle);
   std::map<unsigned int, std::shared_ptr<BaseRequest>>::iterator it = mUnhandledRequestMap.find(requestHandle);
   if (it != mUnhandledRequestMap.end())
   {
      // StackLog(<< "JsonApiServerHTTP::handleRequestInContext(): request handle: " << requestHandle << " posting to JsonApiServerHTTP::handleRequestInContextImpl");
      PhoneInterface* pi = dynamic_cast<PhoneInterface*>(sdkContext->phone);
      if (pi->getSdkModuleThread().isCurrentThread())
      {
         handleRequestInContextImpl(requestHandle, sdkContext, it->second);
      }
      else
      {
         pi->getSdkModuleThread().post(resip::resip_bind(&JsonApiServerHTTP::handleRequestInContextImpl, this, requestHandle, sdkContext, it->second));
      }
      mUnhandledRequestMap.erase(it);
   }
}

// SDK thread of worker SDK
void JsonApiServerHTTP::handleRequestInContextImpl(unsigned int requestHandle, JsonApiUserContext* sdkContext, const std::shared_ptr<BaseRequest> requestInfo)
{
   // StackLog(<< "JsonApiServerHTTP::handleRequestInContextImpl(): request handle: " << requestHandle);
   std::unique_ptr<JsonApiUserContext> spContext(sdkContext); // take ownership

   {
      std::shared_ptr<CPCAPI2::JsonApi::HttpsServer::Response> httpsResponse = nullptr;
      std::shared_ptr<CPCAPI2::JsonApi::HttpServer::Response> httpResponse = nullptr;
      std::shared_ptr<rapidjson::Document> jsonRequestPtr;

      AuthToken authToken;
      if (mUseHttps) 
      {
         std::shared_ptr<UnhandledRequestInfo<SimpleWeb::HTTPS>> httpsReqInfo = std::static_pointer_cast<UnhandledRequestInfo<SimpleWeb::HTTPS>>(requestInfo);
         jsonRequestPtr = httpsReqInfo->requestJsonDoc;
         httpsResponse = httpsReqInfo->response;
         authToken = httpsReqInfo->authToken;
      }
      else 
      {
         std::shared_ptr<UnhandledRequestInfo<SimpleWeb::HTTP>> httpReqInfo = std::static_pointer_cast<UnhandledRequestInfo<SimpleWeb::HTTP>>(requestInfo);
         jsonRequestPtr = httpReqInfo->requestJsonDoc;
         httpResponse = httpReqInfo->response;
         authToken = httpReqInfo->authToken;
      }

      if (jsonRequestPtr->IsNull())
      {
         return;
      }

      const rapidjson::Document& jsonRequest = *jsonRequestPtr;
      const rapidjson::Value& moduleIdVal = jsonRequest["moduleId"];

      PhoneInterface* pi = dynamic_cast<PhoneInterface*>(sdkContext->phone);
      JsonApiServerModule* pm = dynamic_cast<JsonApiServerModule*>(pi->getInterfaceByName(moduleIdVal.GetString()));
      if (pm == NULL)
      {
         // try again using the master phone
         // StackLog(<< "JsonApiServerHTTP::handleRequestInContextImpl(): request handle: " << requestHandle << " module name: " << moduleIdVal.GetString());
         pi = dynamic_cast<PhoneInterface*>(mPhone);
         pm = dynamic_cast<JsonApiServerModule*>(pi->getInterfaceByName(moduleIdVal.GetString()));
      }

      if ((sdkContext->permissions.size() == 1 && sdkContext->permissions.count("*") > 0) ||
         (sdkContext->permissions.count(moduleIdVal.GetString()) > 0))
      {
         if (pm != NULL)
         {
            if (!jsonRequest.HasMember("functionObject"))
            {
               return;
            }

            const rapidjson::Value& functionObjVal = jsonRequest["functionObject"];
            if (!functionObjVal.IsObject())
            {
               return;
            }

            {
               JsonApiRequestInfo connId;
               if (mUseHttps)
               {
                  connId = JsonApiRequestInfo(httpsResponse);
               }
               else 
               {
                  connId = JsonApiRequestInfo(httpResponse);
               }
               connId.authToken = authToken;

               // StackLog(<< "JsonApiServerHTTP::handleRequestInContextImpl(): request handle: " << requestHandle << " trigger processIncoming");
               pm->processIncoming(connId, jsonRequestPtr);
            }
         }
      }
      else
      {
         DebugLog(<< "no permission to access SDK module " << moduleIdVal.GetString());
         LocalDebugLog("no permission to access SDK module {}", moduleIdVal.GetString());
      }
   }
}

void JsonApiServerHTTP::sendHttp(const CPCAPI2::Json::JsonDataPointer& eventData, const std::shared_ptr<typename SimpleWeb::Server<SimpleWeb::HTTP>::Response>& httpResponse)
{
   send<SimpleWeb::HTTP>(eventData, httpResponse);
}

void JsonApiServerHTTP::sendHttps(const CPCAPI2::Json::JsonDataPointer& eventData, const std::shared_ptr<typename SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& httpResponse)
{
   send<SimpleWeb::HTTPS>(eventData, httpResponse);
}

void JsonApiServerHTTP::sendHttp(const char* msgData, size_t msgLen, const std::shared_ptr<typename SimpleWeb::Server<SimpleWeb::HTTP>::Response>& httpResponse)
{
   send<SimpleWeb::HTTP>(msgData, msgLen, httpResponse);
}

void JsonApiServerHTTP::sendHttps(const char* msgData, size_t msgLen, const std::shared_ptr<typename SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& httpResponse)
{
   send<SimpleWeb::HTTPS>(msgData, msgLen, httpResponse);
}

template <typename T> void JsonApiServerHTTP::send(const CPCAPI2::Json::JsonDataPointer& eventData, const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& httpResponse)
{   
   SimpleWeb::string_view message(eventData->getMessageData(), eventData->getMessageSize());
   httpResponse->write(SimpleWeb::StatusCode::success_ok, message);
}

template <typename T> void JsonApiServerHTTP::send(const char* msgData, size_t msgLen, const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& httpResponse)
{
   SimpleWeb::string_view message(msgData, msgLen);
   httpResponse->write(SimpleWeb::StatusCode::success_ok, message);
}

template <typename T>
std::shared_ptr<typename SimpleWeb::Server<T>::Response> JsonApiServerHTTP::getResponse(unsigned int requestHandle) const
{
   typename std::map<unsigned int, std::shared_ptr<UnhandledRequestInfo<T>>>::const_iterator it = mUnhandledRequestMap.find(requestHandle);
   if (it != mUnhandledRequestMap.end())
   {
      return it->second->response;
   }
   return std::shared_ptr<HttpServer::Response>();
}

template <typename T>
int JsonApiServerHTTP::getAuthTokenFromRequest(const std::shared_ptr<typename SimpleWeb::Server<T>::Request>& request, resip::Data& authToken)
{
   SimpleWeb::CaseInsensitiveMultimap::const_iterator itAuthHdr = request->header.find("authorization");
   if (itAuthHdr != request->header.end())
   {
      resip::Data authHeader(itAuthHdr->second);
      if (authHeader.size() > 32 && resip::isEqualNoCase(authHeader.substr(0, 6), "bearer"))
      {
         authToken = authHeader.substr(7);
         return 0;
      }
   }
   WarningLog(<< "Error processing request: " << request->path << " Failed to get authorization header / failed to parse authorization header: " << (itAuthHdr != request->header.end() ? itAuthHdr->second : "<>"));
   authToken = resip::Data::Empty;
   return -1;
}

template <typename T>
void JsonApiServerHTTP::handleRequest(const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& response, const std::shared_ptr<typename SimpleWeb::Server<T>::Request>& request)
{
   PhoneInterface* pi = dynamic_cast<PhoneInterface*>(mPhone);
   pi->getSdkModuleThread().post(resip::resip_bind(&JsonApiServerHTTP::handleRequestOnSdkThread<T>, this, response, request));
}

template <typename T>
void JsonApiServerHTTP::handleRequestOnSdkThread(const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& response, const std::shared_ptr<typename SimpleWeb::Server<T>::Request>& request)
{
   std::string requestContent(request->content.string());
   // StackLog(<< "JsonApiServerHTTP::handleRequest(): request: " << requestContent);
   resip::Data authTokenStr;
   if (getAuthTokenFromRequest<T>(request, authTokenStr) != 0)
   {
      WarningLog(<< "Authorization header not present or malformed");
      LocalWarningLog("Authorization header not present or malformed");
      response->write(SimpleWeb::StatusCode::client_error_bad_request, "Authorization header not present or malformed");
      return;
   }

   std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
   std::string targetModule;
   if (!requestContent.empty())
   {
      // Example of incoming requests
      // {"moduleId":"OrchestrationServer","functionObject":{"functionName":"setServerTtl","serverInfo":{"region":"POC","uri":"ws://***********:19089","ttlSeconds":"-1","services":["pushnotificationendpoint"]}}}
      // {"moduleId":"OrchestrationServer","functionObject":{"functionName":"setServerInfo","serverInfo":{"region":"POC","uri":"ws://***********:19089","ttlSeconds":"-1","services":["xmppagent"]}}}

      jsonRequest->Parse<0>(requestContent.c_str());

      if (jsonRequest->HasParseError())
      {
         WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
         LocalWarningLog("Invalid request format, parse error occured: {} Aborting decode.", jsonRequest->GetParseError());

         response->write(SimpleWeb::StatusCode::client_error_bad_request, "Invalid request format, parse error occured");
         return;
      }

      if (!jsonRequest->HasMember("moduleId"))
      {
         WarningLog(<< "Missing moduleId. Aborting decode.");
         LocalWarningLog("Missing moduleId. Aborting decode.");
         response->write(SimpleWeb::StatusCode::client_error_bad_request, "Missing moduleId");
         return;
      }

      const rapidjson::Value& moduleIdVal = (*jsonRequest)["moduleId"];
      if (!moduleIdVal.IsString())
      {
         WarningLog(<< "moduleId is not a string. Aborting decode.");
         LocalWarningLog("moduleId is not a string. Aborting decode.");
         response->write(SimpleWeb::StatusCode::client_error_bad_request, "moduleId is not a string");
         return;
      }
      targetModule = moduleIdVal.GetString();
      InfoLog(<< "Handle request for module: " << moduleIdVal.GetString());

      if (!jsonRequest->HasMember("functionObject"))
      {
         WarningLog(<< "Missing functionObject. Aborting decode.");
         LocalWarningLog("Missing functionObject. Aborting decode.");
         response->write(SimpleWeb::StatusCode::client_error_bad_request, "Missing functionObject");
         return;
      }

      const rapidjson::Value& functionObjectVal = (*jsonRequest)["functionObject"];
      if (!functionObjectVal.IsObject())
      {
         WarningLog(<< "functionObject is not an object. Aborting decode.");
         LocalWarningLog("functionObject is not an object. Aborting decode.");
         response->write(SimpleWeb::StatusCode::client_error_bad_request, "functionObject is not an object");
         return;
      }

      if (!functionObjectVal.HasMember("functionName"))
      {
         WarningLog(<< "Missing functionName. Aborting decode.");
         LocalWarningLog("Missing functionName. Aborting decode.");
         response->write(SimpleWeb::StatusCode::client_error_bad_request, "Missing functionName");
         return;
      }

      const rapidjson::Value& functionNameVal = functionObjectVal["functionName"];
      if (!functionNameVal.IsString())
      {
         WarningLog(<< "functionName is not a string. Aborting decode.");
         LocalWarningLog("functionName is not a string. Aborting decode.");
         response->write(SimpleWeb::StatusCode::client_error_bad_request, "functionName is not a string");
         return;
      }

      std::string sFunctionName = functionNameVal.GetString();
      InfoLog(<< "Handle request for function: " << sFunctionName);
      LocalInfoLog("Handle request for function: {}", sFunctionName);
      if (sFunctionName.compare("setServerTtl") == 0)
      {
         // TODO: Create a response for the TTL message, e.g. {"moduleId":"OrchestrationServer","functionObject":{"functionName":"onSetServerTtlResult","success":true}}
         StackLog(<< "JsonApiServerHTTP::handleRequest(): blocking setServerTtl message from triggering a login event.");
         return;
      }
   }

   AuthToken authToken;
   JsonApiServerInterface* jsonApiServerIf = dynamic_cast<JsonApiServerInterface*>(CPCAPI2::JsonApi::JsonApiServer::getInterface(mPhone));
   resip::Data userIdentity, deviceId;
   std::map<resip::Data, resip::Data> publicClaims;
   std::vector<resip::Data> requestedResources;
   if (jsonApiServerIf->validateAuthToken(authTokenStr, userIdentity, deviceId, publicClaims, requestedResources) == 0)
   {
      unsigned int reqId = mNextRequestHandle++;
      authToken.cp_user = userIdentity.c_str();
      authToken.device_uuid = deviceId.c_str();
      authToken.authToken = authTokenStr.c_str();
      std::ostringstream remoteAddress;
      remoteAddress << request->remote_endpoint_address() << ":" << request->remote_endpoint_port();
      authToken.remoteAddress = remoteAddress.str().c_str();

      //PhoneInterface* pi = dynamic_cast<PhoneInterface*>(mPhone);
      //pi->getSdkModuleThread().post(resip::resip_bind(&JsonApiServerHTTP::mapUnhandledRequest<T>, this, reqId, authToken, jsonRequest, request, response));
      mapUnhandledRequest<T>(reqId, authToken, jsonRequest, request, response);

      // StackLog(<< "Handle request for function: " << sFunctionName << " auth token validated, posted to manUnhandledRequest");

      if (jsonApiServerIf->processIncomingHttp(reqId, authToken, request->path.c_str(), targetModule == "OrchestrationServer") != 0)
      {
         WarningLog(<< "processIncomingHttp error");
         LocalWarningLog("processIncomingHttp error");
         response->write(SimpleWeb::StatusCode::client_error_bad_request, "processIncomingHttp error");
         //pi->getSdkModuleThread().post(resip::resip_bind(&JsonApiServerHTTP::clearUnhandledRequest, this, reqId));
         clearUnhandledRequest(reqId);
         return;
      }
   }
   else
   {
      response->write(SimpleWeb::StatusCode::client_error_bad_request, "auth token validation failed");
      WarningLog(<< "error: failed to validate auth token: " << authTokenStr);
      LocalWarningLog("error: failed to validate auth token: {}", authTokenStr);
   }
}

// SDK thread of master SDK
template <typename T>
void JsonApiServerHTTP::mapUnhandledRequest(unsigned int requestHandle, 
                                            const AuthToken& authToken,
                                            const std::shared_ptr<rapidjson::Document>& jsonRequest, 
                                            const std::shared_ptr<typename SimpleWeb::Server<T>::Request>& request,
                                            const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& response)
{
   // StackLog(<< "JsonApiServerHTTP::mapUnhandledRequest(): request handle: " << requestHandle);
   /*
   rapidjson::StringBuffer buffer(0, 1024);
   rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
   jsonRequest->Accept(writer);
   StackLog(<< "JsonApiServerHTTP::mapUnhandledRequest(): request: " << buffer.GetString());
   */

   UnhandledRequestInfo<T> ri;
   std::shared_ptr<UnhandledRequestInfo<T>> riPtr = std::make_shared<UnhandledRequestInfo<T>>(ri);
   riPtr->authToken = authToken;
   riPtr->requestJsonDoc = jsonRequest;
   riPtr->response = response;
   mUnhandledRequestMap[requestHandle] = std::move(riPtr);
}

void JsonApiServerHTTP::clearUnhandledRequest(unsigned int requestHandle)
{
   mUnhandledRequestMap.erase(requestHandle);
}

bool JsonApiServerHTTP::fileExists(const cpc::string& filename)
{
   struct stat buf;
   if (stat(filename.c_str(), &buf) != -1)
   {
      return true;
   }
   return false;
}

}

}

#endif
