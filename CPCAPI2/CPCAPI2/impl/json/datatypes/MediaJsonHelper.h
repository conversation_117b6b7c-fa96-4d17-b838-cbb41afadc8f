// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef Media_JSON_HELPER_H
#define Media_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/public/media/audio/AudioHandler.h>
#include <interface/public/media/video/VideoHandler.h>
#include <interface/experimental/video_ext/VideoExt.h>
#include <interface/experimental/media/Mixer.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::GainConfig& v)
    {
      w.StartObject();
      Write(w, "mode", v.mode);
      Write(w, "targetLeveldB", v.targetLeveldB);
      Write(w, "compressionGaindB", v.compressionGaindB);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::GainConfig& v)
    {
      Read(json, "mode", v.mode);
      Read(json, "targetLeveldB", v.targetLeveldB);
      Read(json, "compressionGaindB", v.compressionGaindB);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::SpeakerGainConfig& v)
    {
      w.StartObject();
      Write(w, "enabled", v.enabled);
      Write(w, "scale", v.scale);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::SpeakerGainConfig& v)
    {
      Read(json, "enabled", v.enabled);
      Read(json, "scale", v.scale);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::GainSettings& v)
    {
      w.StartObject();
      Write(w, "rxConfig", v.rxConfig);
      Write(w, "txConfig", v.txConfig);
      Write(w, "spkConfig", v.spkConfig);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::GainSettings& v)
    {
      Read(json, "rxConfig", v.rxConfig);
      Read(json, "txConfig", v.txConfig);
      Read(json, "spkConfig", v.spkConfig);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::AudioDeviceInfo& v)
    {
      w.StartObject();
      Write(w, "friendlyName", v.friendlyName);
      Write(w, "hid", v.hid);
      Write(w, "id", v.id);
      Write(w, "role", v.role);
      Write(w, "deviceType", v.deviceType);
      Write(w, "inadvisable", v.inadvisable);
      Write(w, "defaultSystemDevice", v.defaultSystemDevice);
      Write(w, "defaultSystemCommDevice", v.defaultSystemCommDevice);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::AudioDeviceInfo& v)
    {
      Read(json, "friendlyName", v.friendlyName);
      Read(json, "hid", v.hid);
      Read(json, "id", v.id);
      Read(json, "role", v.role);
      Read(json, "deviceType", v.deviceType);
      Read(json, "inadvisable", v.inadvisable);
      Read(json, "defaultSystemDevice", v.defaultSystemDevice);
      Read(json, "defaultSystemCommDevice", v.defaultSystemCommDevice);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::AudioDeviceListUpdatedEvent& v)
    {
      w.StartObject();
      Write(w, "deviceInfo", v.deviceInfo);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::AudioDeviceListUpdatedEvent& v)
    {
      Read(json, "deviceInfo", v.deviceInfo);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::AudioCodecInfo& v)
    {
      w.StartObject();
      Write(w, "codecName", v.codecName);
      Write(w, "id", v.id);
      Write(w, "enabled", v.enabled);
      Write(w, "samplingRate", v.samplingRate);
      Write(w, "minBandwidth", v.minBandwidth);
      Write(w, "maxBandwidth", v.maxBandwidth);
      Write(w, "priority", v.priority);
      Write(w, "payloadType", v.payloadType);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::AudioCodecInfo& v)
    {
      Read(json, "codecName", v.codecName);
      Read(json, "id", v.id);
      Read(json, "enabled", v.enabled);
      Read(json, "samplingRate", v.samplingRate);
      Read(json, "minBandwidth", v.minBandwidth);
      Read(json, "maxBandwidth", v.maxBandwidth);
      Read(json, "priority", v.priority);
      Read(json, "payloadType", v.payloadType);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::AudioCodecListUpdatedEvent& v)
    {
      w.StartObject();
      Write(w, "codecInfo", v.codecInfo);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::AudioCodecListUpdatedEvent& v)
    {
      Read(json, "codecInfo", v.codecInfo);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::AudioDeviceVolumeEvent& v)
    {
      w.StartObject();
      Write(w, "micMuted", v.micMuted);
      Write(w, "speakerMuted", v.speakerMuted);
      Write(w, "micVolumeLevel", v.micVolumeLevel);
      Write(w, "speakerVolumeLevel", v.speakerVolumeLevel);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::AudioDeviceVolumeEvent& v)
    {
      Read(json, "micMuted", v.micMuted);
      Read(json, "speakerMuted", v.speakerMuted);
      Read(json, "micVolumeLevel", v.micVolumeLevel);
      Read(json, "speakerVolumeLevel", v.speakerVolumeLevel);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::AudioDeviceLevelChangeEvent& v)
    {
      w.StartObject();
      Write(w, "inputDeviceLevel", v.inputDeviceLevel);
      Write(w, "outputDeviceLevel", v.outputDeviceLevel);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::AudioDeviceLevelChangeEvent& v)
    {
      Read(json, "inputDeviceLevel", v.inputDeviceLevel);
      Read(json, "outputDeviceLevel", v.outputDeviceLevel);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::SystemAudioServiceErrorEvent& v)
    {
      w.StartObject();
      Write(w, "errorLevel", v.errorLevel);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::SystemAudioServiceErrorEvent& v)
    {
      Read(json, "errorLevel", v.errorLevel);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::AudioStreamStartedEvent& v)
    {
      w.StartObject();
      Write(w, "streamId", v.streamId);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::AudioStreamStartedEvent& v)
    {
      Read(json, "streamId", v.streamId);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::AudioStreamStoppedEvent& v)
    {
      w.StartObject();
      Write(w, "streamId", v.streamId);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::AudioStreamStoppedEvent& v)
    {
      Read(json, "streamId", v.streamId);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::AudioHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::AudioHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::VideoDeviceInfo& v)
    {
      w.StartObject();
      Write(w, "friendlyName", v.friendlyName);
      Write(w, "id", v.id);
      Write(w, "orientation", v.orientation);
      Write(w, "cameraIndex", v.cameraIndex);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::VideoDeviceInfo& v)
    {
      Read(json, "friendlyName", v.friendlyName);
      Read(json, "id", v.id);
      Read(json, "orientation", v.orientation);
      Read(json, "cameraIndex", v.cameraIndex);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::VideoDeviceListUpdatedEvent& v)
    {
      w.StartObject();
      Write(w, "deviceInfo", v.deviceInfo);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::VideoDeviceListUpdatedEvent& v)
    {
      Read(json, "deviceInfo", v.deviceInfo);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::VideoCodecInfo& v)
    {
      w.StartObject();
      Write(w, "codecName", v.codecName);
      Write(w, "id", v.id);
      Write(w, "enabled", v.enabled);
      Write(w, "minBandwidth", v.minBandwidth);
      Write(w, "maxBandwidth", v.maxBandwidth);
      Write(w, "priority", v.priority);
      Write(w, "payloadType", v.payloadType);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::VideoCodecInfo& v)
    {
      Read(json, "codecName", v.codecName);
      Read(json, "id", v.id);
      Read(json, "enabled", v.enabled);
      Read(json, "minBandwidth", v.minBandwidth);
      Read(json, "maxBandwidth", v.maxBandwidth);
      Read(json, "priority", v.priority);
      Read(json, "payloadType", v.payloadType);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::VideoCodecListUpdatedEvent& v)
    {
      w.StartObject();
      Write(w, "codecInfo", v.codecInfo);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::VideoCodecListUpdatedEvent& v)
    {
      Read(json, "codecInfo", v.codecInfo);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::VideoCaptureStateChangedEvent& v)
    {
      w.StartObject();
      Write(w, "state", v.state);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::VideoCaptureStateChangedEvent& v)
    {
      Read(json, "state", v.state);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::VideoHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::VideoHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::MediaStackSettings& v)
    {
      w.StartObject();
      Write(w, "audioLayer", v.audioLayer);
      Write(w, "streamType", v.streamType);
      Write(w, "audioSource", v.audioSource);
      Write(w, "audioOutputDisabled", v.audioOutputDisabled);
      Write(w, "numAudioEncoderThreads", v.numAudioEncoderThreads);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::MediaStackSettings& v)
    {
      Read(json, "audioLayer", v.audioLayer);
      Read(json, "streamType", v.streamType);
      Read(json, "audioSource", v.audioSource);
      Read(json, "audioOutputDisabled", v.audioOutputDisabled);
      Read(json, "numAudioEncoderThreads", v.numAudioEncoderThreads);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::MediaManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::MediaManager& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::ScreenshareDeviceInfo& v)
    {
      w.StartObject();
      Write(w, "deviceId", v.deviceId);
      Write(w, "deviceDescription", v.deviceDescription);
      Write(w, "isWindow", v.isWindow);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::ScreenshareDeviceInfo& v)
    {
      Read(json, "deviceId", v.deviceId);
      Read(json, "deviceDescription", v.deviceDescription);
      Read(json, "isWindow", v.isWindow);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::ScreenshareDeviceListEvent& v)
    {
      w.StartObject();
      Write(w, "devices", v.devices);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::ScreenshareDeviceListEvent& v)
    {
      Read(json, "devices", v.devices);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::VideoWebsocketServerStartedEvent& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::VideoWebsocketServerStartedEvent& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::VideoWebsocketServerHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::VideoWebsocketServerHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::VideoSnapshotEvent& v)
    {
      w.StartObject();
      Write(w, "success", v.success);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::VideoSnapshotEvent& v)
    {
      Read(json, "success", v.success);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::VideoSnapshotHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::VideoSnapshotHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::ScreenshareDeviceListHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::ScreenshareDeviceListHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::ScreenShareHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::ScreenShareHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::VideoExt& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::VideoExt& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::Media::MixerSettings& v)
    {
      w.StartObject();
      Write(w, "perChannelAecMode", v.perChannelAecMode);
      Write(w, "perChannelNsMode", v.perChannelNsMode);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::Media::MixerSettings& v)
    {
      Read(json, "perChannelAecMode", v.perChannelAecMode);
      Read(json, "perChannelNsMode", v.perChannelNsMode);
    }
  }
}
#pragma warning(pop)
#endif // Media_JSON_HELPER_H