#pragma once

#if !defined(__CPCAPI2_RECEIVER_BASE_STATE_H__)
#define __CPCAPI2_RECEIVER_BASE_STATE_H__

#include "BaseState.h"

#include <assert.h>

namespace CPCAPI2
{
namespace IsComposing
{

class ReceiverBaseState : public BaseState
{
public:
   virtual void onIdleStateReceived(IsComposingManager* manager, IsComposingInfo* info) { assert(false); }
   virtual void onActiveStateReceived(IsComposingManager* manager, IsComposingInfo* info) { assert(false); }
   virtual void onMessageReceived(IsComposingManager* manager, IsComposingInfo* info) { assert(false); }
   virtual void onRefreshTimeout(IsComposingManager* manager, IsComposingInfo* info) { assert(false); }
};

}
}

#endif // __CPCAPI2_RECEIVER_BASE_STATE_H__