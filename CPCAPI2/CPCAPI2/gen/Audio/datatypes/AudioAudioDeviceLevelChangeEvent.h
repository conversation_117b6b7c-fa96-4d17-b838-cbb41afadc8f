#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIODEVICELEVELCHANGEEVENT_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIODEVICELEVELCHANGEEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
  class AudioAudioDeviceLevelChangeEvent
  {
  public:
    std::optional<int32_t> inputDeviceLevel;
    std::optional<int32_t> outputDeviceLevel;

    AudioAudioDeviceLevelChangeEvent() = default;

    AudioAudioDeviceLevelChangeEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const AudioAudioDeviceLevelChangeEvent& o)
    {
      j = nlohmann::json::object();
       if (o.inputDeviceLevel) { j["inputDeviceLevel"] = *(o.inputDeviceLevel); }
     if (o.outputDeviceLevel) { j["outputDeviceLevel"] = *(o.outputDeviceLevel); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, AudioAudioDeviceLevelChangeEvent& o)
    {
     if (j.contains("inputDeviceLevel")) { o.inputDeviceLevel = j.at("inputDeviceLevel"); }
     if (j.contains("outputDeviceLevel")) { o.outputDeviceLevel = j.at("outputDeviceLevel"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIODEVICELEVELCHANGEEVENT_H
