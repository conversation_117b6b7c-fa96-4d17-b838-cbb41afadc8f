#include "brand_branded.h"

#if (CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE == 1) || (CPCAPI2_BRAND_CONFERENCE_CONNECTOR_MODULE == 1)
#include "CloudServerConnection.h"
#include "CloudConnectorImpl.h"
#include "phone/PhoneInterface.h"
#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"
#include "util/DeviceInfo.h"
#include "auth_server/AuthServerJwtUtils.h"
#include "json/JsonHelper.h"

#include <client_https.hpp>
#include "websocketpp/uri.hpp"

#include "../util/cpc_logger.h"

#include <rutil/MultiReactor.hxx>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER

using namespace CPCAPI2::JsonApi;

namespace CPCAPI2
{

namespace CloudConnector
{

CloudServerConnection::CloudServerConnection(CloudServerConnectionObserver* cl) :
mObs(cl),
mPhone(NULL),
mMasterSdkPhone(NULL),
mJsonApiClient(NULL),
mJsonConnHdl(0),
mStatus(CloudServerConnStatus_Disconnected),
mReleased(false)
{
   StackLog(<< "CloudServerConnection::CloudServerConnection(): " << this << " phone: " << mPhone << " master phone: " << mMasterSdkPhone << " json client: " << mJsonApiClient);
}

CloudServerConnection::~CloudServerConnection()
{
   mReleaseMutex.lock();
   StackLog(<< "CloudServerConnection::~CloudServerConnection(): " << this << " phone: " << mPhone << " master phone: " << mMasterSdkPhone << " json client: " << mJsonApiClient << " status: " << CloudServerConnection::get_debug_string(mStatus) << " phone thread: " << (mPhone ? (dynamic_cast<PhoneInterface*>(mPhone))->getSdkModuleThread().isCurrentThread() : false) << " master phone thread: " << (mMasterSdkPhone ? (dynamic_cast<PhoneInterface*>(mMasterSdkPhone))->getSdkModuleThread().isCurrentThread() : false) << " thread-id: " << resip::ThreadIf::selfId());

   if (mJsonApiClient)
   {
      dynamic_cast<CPCAPI2::JsonApi::JsonApiClientInterface*>(mJsonApiClient)->removeSdkObserver(this);
      //dynamic_cast<CPCAPI2::JsonApi::JsonApiClientInterface*>(mJsonApiClient)->Release();
   }
   mJsonApiClient = NULL;

   if ((mStatus == CloudServerConnStatus_Connecting) || (mStatus == CloudServerConnStatus_Connected) || (mStatus == CloudServerConnStatus_Authenticating))
   {
      disconnect();
   }

   if (mPhone)
   {
      (dynamic_cast<PhoneInterface*>(mPhone))->appReleaseImpl();
      (dynamic_cast<PhoneInterface*>(mPhone))->releaseImpl();
   }
   mPhone = NULL;

   mServices.clear();

   mReleaseMutex.unlock();
}

struct CloudConnectorCallbackStruct
{
   std::weak_ptr<CloudServerConnection> weak_connector;
   CloudConnectorCallbackStruct(std::weak_ptr<CloudServerConnection> weak_this) : weak_connector(weak_this) {}
};

void sdkCallbackHook(void* context)
{
   CloudServerConnection* ctx = (CloudServerConnection*)context;
   if (ctx)
      ctx->handleSdkCallback();
}

void sdkCallOnDestructHook(void* context)
{
   // TODO: validate that context is valid before dereferencing it, e.g. CloudImpl may have already destroyed the CloudConnection from sdk thread
   StackLog(<< "CloudServerConnection::sdkCallOnDestructHook(): " << context << ": cloud phone being destroyed");
   CloudConnectorCallbackStruct* cb = (CloudConnectorCallbackStruct*)context;
   if (cb)
   {
      CloudServerConnection::cloudPhoneDestroyed(cb->weak_connector);
   }
   delete cb;
   cb = NULL;
}

void sdkCallOnAppReleaseHook(void* context)
{
   StackLog(<< "CloudServerConnection::sdkCallOnAppReleaseHook(): " << context << ": cloud phone being released");
   CloudConnectorCallbackStruct* cb = (CloudConnectorCallbackStruct*)context;
   if (cb)
   {
      CloudServerConnection::cloudPhoneReleased(cb->weak_connector);
   }
   delete cb;
   cb = NULL;
}

int CloudServerConnection::initialize(PhoneInternal* masterSdkPhone)
{
   mMasterSdkPhone = masterSdkPhone;

   mPhone = CPCAPI2::PhoneInterface::create(masterSdkPhone, &((dynamic_cast<PhoneInterface*>(mMasterSdkPhone))->getSdkLoggerThread()));

   DebugLog(<< "CloudServerConnection::initialize(): " << this << " master-sdk phone: " << mMasterSdkPhone << " created internal phone: " << mPhone);
   (dynamic_cast<PhoneInterface*>(mPhone))->addRefImpl();
   mPhone->setCallbackHook(sdkCallbackHook, this);
   mPhone->setCallOnDestructFn(sdkCallOnDestructHook, new CloudConnectorCallbackStruct(shared_from_this()));
   mPhone->setCallOnAppReleaseFn(sdkCallOnAppReleaseHook, new CloudConnectorCallbackStruct(shared_from_this()));

   LicenseInfo licenseInfo;
   mPhone->initialize(licenseInfo, this, false);

   mJsonApiClient = CPCAPI2::JsonApi::JsonApiClient::getInterface(mPhone);
   dynamic_cast<CPCAPI2::JsonApi::JsonApiClientInterface*>(mJsonApiClient)->addSdkObserver(this);

   return 0;
}

int CloudServerConnection::connect(const cpc::string& serverUrl, const cpc::string& authToken, const cpc::string& realm, bool ignoreCertVerification)
{
   DebugLog(<< "CloudServerConnection::connect(): serverUrl: " << serverUrl << " authToken: " << authToken << " phone thread: " << (dynamic_cast<PhoneInterface*>(mPhone))->getSdkModuleThread().isCurrentThread() << " master phone thread: " << (dynamic_cast<PhoneInterface*>(mMasterSdkPhone))->getSdkModuleThread().isCurrentThread() << " thread-id: " << resip::ThreadIf::selfId());

   if (serverUrl.empty())
   {
      DebugLog(<< "CloudServerConnection::connect(): " << this << " phone: " << mPhone << " server url not initialized");
      return kError;
   }

   mAuthToken = authToken;
   mRealm = realm;
   mUrl = serverUrl;
   if (mJsonApiClient)
   {
      changeState(CloudServerConnStatus_Connecting);
      JsonApiClientSettings settings;
      settings.ignoreCertVerification = ignoreCertVerification;
      settings.serverUri = serverUrl;
      mJsonApiClient->configureDefaultSettings(settings);
      mJsonApiClient->enable();
      return kSuccess;
   }

   DebugLog(<< "CloudServerConnection::connect(): " << this << " phone: " << mPhone << " json client not initialized");
   return kError;
}

int CloudServerConnection::disconnect()
{
   StackLog(<< "CloudServerConnection::disconnect(): cloud connection status: " << mStatus << " phone thread: " << (mPhone ? (dynamic_cast<PhoneInterface*>(mPhone))->getSdkModuleThread().isCurrentThread() : false) << " master phone thread: " << (mMasterSdkPhone ? (dynamic_cast<PhoneInterface*>(mMasterSdkPhone))->getSdkModuleThread().isCurrentThread() : false) << " thread-id: " << resip::ThreadIf::selfId());

   changeState(CloudServerConnStatus_Disconnecting);

   if (mJsonApiClient)
   {
      StackLog(<< "CloudServerConnection::disconnect(): cloud connection status: " << CloudServerConnection::get_debug_string(mStatus) << " disabling json client: " << mJsonApiClient);
      mJsonApiClient->disable();
      return kSuccess;
   }

   DebugLog(<< "CloudServerConnection::disconnect(): " << this << " phone: " << mPhone << " json client not initialized");
   return kError;
}

int CloudServerConnection::logout()
{
   StackLog(<< "CloudServerConnection::logout(): cloud connection status: " << mStatus << " phone thread: " << (mPhone ? (dynamic_cast<PhoneInterface*>(mPhone))->getSdkModuleThread().isCurrentThread() : false) << " master phone thread: " << (mMasterSdkPhone ? (dynamic_cast<PhoneInterface*>(mMasterSdkPhone))->getSdkModuleThread().isCurrentThread() : false) << " thread-id: " << resip::ThreadIf::selfId());

   if (mJsonApiClient)
   {
      StackLog(<< "CloudServerConnection::logout(): cloud connection status: " << CloudServerConnection::get_debug_string(mStatus) << " initiating logout on json client: " << mJsonApiClient);
      return (mJsonApiClient->logout());
   }

   DebugLog(<< "CloudServerConnection::logout(): " << this << " phone: " << mPhone << " json client not initialized");
   return kError;
}

void CloudServerConnection::release()
{
   StackLog(<< "CloudServerConnection::release(): " << this << " phone: " << mPhone << " master phone: " << mMasterSdkPhone << " json client: " << mJsonApiClient);

   mReleaseMutex.lock();
   if (mJsonApiClient)
   {
      CPCAPI2::JsonApi::JsonApiClientInterface* jci = dynamic_cast<CPCAPI2::JsonApi::JsonApiClientInterface*>(mJsonApiClient);
      mJsonApiClient = NULL;
      jci->removeSdkObserver(this);
   }
   mJsonApiClient = NULL;

   if (mPhone != NULL)
   {
      PhoneInterface* pi = dynamic_cast<PhoneInterface*>(mPhone);
      mPhone = NULL;
      pi->appReleaseImpl();
      pi->releaseImpl();
   }
   mReleaseMutex.unlock();
}

int CloudServerConnection::dropIncomingJsonMessages(bool enable)
{
   StackLog(<< "CloudServerConnection::dropIncomingJsonMessages(): cloud connection status: " << mStatus << " drop message enable: " << enable << " phone thread: " << (mPhone ? (dynamic_cast<PhoneInterface*>(mPhone))->getSdkModuleThread().isCurrentThread() : false) << " master phone thread: " << (mMasterSdkPhone ? (dynamic_cast<PhoneInterface*>(mMasterSdkPhone))->getSdkModuleThread().isCurrentThread() : false) << " thread-id: " << resip::ThreadIf::selfId());

   if (mJsonApiClient)
   {
      StackLog(<< "CloudServerConnection::dropIncomingJsonMessages(): cloud connection status: " << CloudServerConnection::get_debug_string(mStatus) << " json client: " << mJsonApiClient << " drop message enable: " << enable);
      CPCAPI2::JsonApi::JsonApiClientInterface* jsonApiClientIf = dynamic_cast<CPCAPI2::JsonApi::JsonApiClientInterface*>(mJsonApiClient);
      if (jsonApiClientIf)
      {
         jsonApiClientIf->dropIncomingJsonMessages(enable);
      }
      return kSuccess;
   }

   return kError;
}

void CloudServerConnection::cloudPhoneDestroyed(std::weak_ptr<CloudServerConnection> weakSelf)
{
   DebugLog(<< "CloudServerConnection::cloudPhoneDestroyed(): reset cloud phone and json client, as destruction may have been triggered by application releasing the phone context, thread-id: " << resip::ThreadIf::selfId());

   if (std::shared_ptr<CloudServerConnection> self = weakSelf.lock())
   {
      self->release();
   }
   else
   {
      DebugLog(<< "CloudServerConnection::cloudPhoneDestroyed(): invalid connector");
   }
}

void CloudServerConnection::cloudPhoneReleased(std::weak_ptr<CloudServerConnection> weakSelf)
{
   DebugLog(<< "CloudServerConnection::cloudPhoneReleased(): reset cloud phone and json client, as released may have been triggered by application releasing the phone context, thread-id: " << resip::ThreadIf::selfId());

   if (std::shared_ptr<CloudServerConnection> self = weakSelf.lock())
   {
      self->release();
   }
   else
   {
      DebugLog(<< "CloudServerConnection::cloudPhoneReleased(): invalid connector");
   }
}

void CloudServerConnection::jsonClientDestroyed(std::weak_ptr<CloudServerConnection> weakSelf)
{
   DebugLog(<< "CloudServerConnection::jsonClientDestroyed(): reset cloud phone and json client, as destruction may have been triggered by application releasing the phone context, thread-id: " << resip::ThreadIf::selfId());

   if (std::shared_ptr<CloudServerConnection> self = weakSelf.lock())
   {
      self->release();
   }
   else
   {
      DebugLog(<< "CloudServerConnection::jsonClientDestroyed(): invalid connector");
   }
}

void CloudServerConnection::addService(const ServiceDesc& service)
{
   mServices.insert(service);
}

const std::set<ServiceDesc>& CloudServerConnection::getServices() const
{
   return mServices;
}

const cpc::string& CloudServerConnection::getUrl() const
{
   return mUrl;
}

CPCAPI2::PhoneInternal* CloudServerConnection::getPhone() const
{
   return mPhone;
}

const cpc::string& CloudServerConnection::getAuthToken() const
{
   return mAuthToken;
}

class AuthRequestor
{
public:

   AuthRequestor(resip::MultiReactor& requestServiceThread, resip::MultiReactor& callbackThread, bool useCurl, const resip::Data& authServerUrl, const resip::Data& username, const resip::Data& password_hash, const std::vector<resip::Data>& requestedResources, const resip::Data& encryptionKey, bool ignoreCertVerification, const SslCipherOptions tlsSettings, const int authTimeoutSecs, const std::function<void(int, const resip::Data&, const cpc::string&)>& resultCb) :
   mReqServiceThread(requestServiceThread),
   mCallbackThread(callbackThread),
   mUseCurl(useCurl),
   mAuthServerUrl(authServerUrl),
   mUsername(username),
   mPass(password_hash),
   mRequestedResources(requestedResources),
   mEncryptionKey(encryptionKey),
   mResultDetails(""),
   mResultCode(-1),
   mIgnoreCertVerification(ignoreCertVerification),
   mTlsSettings(tlsSettings),
   mRetriesPending(1),
   mAuthTimeoutSecs(authTimeoutSecs),
   mResultCb(resultCb)
   {
   }

   virtual ~AuthRequestor()
   {
   }

   void start()
   {
      mReqServiceThread.post(resip::resip_bind(&AuthRequestor::doAuth, this));
   }

private:

   void doAuth()
   {
      std::unique_ptr<AuthRequestor> thisDeleter(this);
      std::string url(mAuthServerUrl.c_str(), mAuthServerUrl.size());
      std::string responseBodyStr("");
      int response = 0;
      std::stringstream messageBody;
      resip::Data messageBodyEncrypted;

      cpc::string device_uuid;
      DeviceInfo::getInstanceId(device_uuid);
      if (device_uuid.empty())
      {
         device_uuid = "00000000-0000-0000-0000-000000000000";
      }

      /* first hit the auth server for a token */
      messageBody << "{ "
         << "\"username\": \"" << mUsername.c_str() << "\", "
         << "\"password\": \"" << mPass.c_str() << "\", "
         << "\"device_uuid\": \"" << device_uuid.c_str() << "\", "
         << "\"requested_resources\": [";

      for (size_t i=0; i<mRequestedResources.size(); i++)
      {
         resip::Data reqRes = mRequestedResources[i];
         messageBody << "\"" << reqRes.c_str();
         if (i < mRequestedResources.size() - 1)
         {
            messageBody << "\", ";
         }
         else
         {
            messageBody << "\" ";
         }
      }
      messageBody << "] ";
      messageBody << "}"; // memory needs to hang around until after perform

      if (mEncryptionKey.size() == 0)
      {
         messageBodyEncrypted = resip::Data(messageBody.str().c_str());
      }
      else
      {
         if (CPCAPI2::AuthServer::JwtUtils::Encrypt(
            resip::Data(mEncryptionKey.c_str(), mEncryptionKey.size()),
            resip::Data(messageBody.str().c_str(), messageBody.str().size()),
            messageBodyEncrypted,
            true) != 0)
         {
            mResultDetails = "failed to encrypt add user request body";
            ErrLog(<< "CloudServerConnection::AuthRequestor::doAuth(): " << mResultDetails);
            postFailureCallback();
            return;
         }
      }

      bool retry = true;
      while (retry)
      {
         retry = postAuthRequest(url, responseBodyStr, response, messageBody, messageBodyEncrypted);
      }
   }

   bool postAuthRequest(std::string& url, std::string& responseBodyStr, int& response, std::stringstream& messageBody, resip::Data& messageBodyEncrypted)
   {
      try
      {
         if (mUseCurl)
         {
            CurlPPHelper helper;
            std::shared_ptr<curlpp::Easy> request(new curlpp::Easy());
            helper.setDefaultOptions(*request, url, "POST", messageBodyEncrypted.size());
            helper.setTimeoutOption(*request, mAuthTimeoutSecs);
            DebugLog(<< "CloudServerConnection::AuthRequestor::postAuthRequest(): auth server url: " << url << " request: " << messageBody.str());
            request->setOpt(new curlpp::options::PostFields(std::string(messageBodyEncrypted.c_str(), messageBodyEncrypted.size())));

            int acceptableFailures(0);
            if (mIgnoreCertVerification)
               acceptableFailures = (int)CurlPPSSL::E_CERT_WHATEVER_ERROR;

            CurlPPSSL cssl(mTlsSettings, acceptableFailures);
            request->setOpt(new curlpp::options::SslCtxFunction(cssl));

            std::stringstream responseBody;
            helper.setDebugLoggingOptions(*request, true);
            request->setOpt(new curlpp::options::WriteStream(&responseBody));
            request->perform();
            responseBodyStr = responseBody.str();

            response = curlpp::infos::ResponseCode::get(*request);
         }
         else
         {
            websocketpp::uri urlParsed(url);
            SimpleWeb::Client<SimpleWeb::HTTPS> swClient(urlParsed.get_host_port(), false);

            auto swResp = swClient.request("POST", urlParsed.get_resource(), messageBodyEncrypted.c_str());
            resip::Data respCodeStr = swResp->status_code.c_str();
            response = respCodeStr.convertInt();
            responseBodyStr = swResp->content.string();
         }

         // DebugLog(<< "CloudServerConnection::AuthRequestor::doAuth(): response: " << responseBody.str());
         if (response == 200)
         {
            std::unique_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
            jsonRequest->Parse<0>(responseBodyStr.c_str());
            if (jsonRequest->HasParseError())
            {
               DebugLog(<< "CloudServerConnection::AuthRequestor::postAuthRequest(): response parse error");
               postFailureCallback();
               return false;
            }
            if (jsonRequest->HasMember("token"))
            {
               const rapidjson::Value& tokenVal = (*jsonRequest)["token"];
               assert(tokenVal.IsString());
               assert(tokenVal.GetStringLength() > 0);
               mResultCode = 0;
               resip::Data authToken(tokenVal.GetString(), tokenVal.GetStringLength());
               resip::ReadCallbackBase* cb = resip::resip_static_bind(mResultCb, mResultCode, authToken, mResultDetails);
               mCallbackThread.post(cb);
            }
            else
            {
               DebugLog(<< "CloudServerConnection::AuthRequestor::postAuthRequest(): response missing token");
               mResultDetails = "incomplete response from authentication server, missing \"token\"";
               postFailureCallback();
            }
         }
         else
         {
            DebugLog(<< "CloudServerConnection::AuthRequestor::postAuthRequest(): failure response: " << response);
            mResultDetails = "response: ";
            mResultDetails.append(resip::Data(response).c_str());
            mResultDetails.append(": ");
            mResultDetails.append(responseBodyStr.c_str());
            postFailureCallback();
         }
      }
      catch (curlpp::RuntimeError& e)
      {
         std::cerr << "Runtime Error: " << e.what();
         DebugLog(<< "CloudServerConnection::AuthRequestor::postAuthRequest(): curlpp exception: " << e.what());
         if (mRetriesPending > 0)
         {
            mRetriesPending--;
            DebugLog(<< "CloudServerConnection::AuthRequestor::postAuthRequest(): curlpp attempt authentication retry");
            return true;
         }

         mResultDetails = "exception: ";
         mResultDetails.append(e.what());
         mResultCode = (-2); // Treated as connection-error
         postFailureCallback();
      }
      catch (const SimpleWeb::system_error& e)
      {
         std::cerr << "Runtime Error: " << e.what();
         DebugLog(<< "CloudServerConnection::AuthRequestor::postAuthRequest(): simple-web exception: " << e.what());

         if (mRetriesPending > 0)
         {
            mRetriesPending--;
            DebugLog(<< "CloudServerConnection::AuthRequestor::postAuthRequest(): simple-web attempt authentication retry");
            return true;
         }

         mResultDetails = "exception: ";
         mResultDetails.append(e.what());
         mResultCode = (-2); // Treated as connection-error
         postFailureCallback();
      }

      return false;
   }

   void postFailureCallback()
   {
      resip::ReadCallbackBase* cb = resip::resip_static_bind(mResultCb, mResultCode, resip::Data::Empty, mResultDetails);
      mCallbackThread.post(cb);
   }

private:
   resip::MultiReactor& mReqServiceThread;
   resip::MultiReactor& mCallbackThread;
   bool mUseCurl;
   resip::Data mAuthServerUrl;
   resip::Data mUsername;
   resip::Data mPass;
   std::vector<resip::Data> mRequestedResources;
   resip::Data mEncryptionKey;
   cpc::string mResultDetails;
   int mResultCode;
   bool mIgnoreCertVerification;
   const SslCipherOptions mTlsSettings;
   int mRetriesPending;
   int mAuthTimeoutSecs;
   std::function<void(int, const resip::Data&, const cpc::string&)> mResultCb;

};

int CloudServerConnection::doAuthFlow(resip::MultiReactor& requestServiceThread, resip::MultiReactor& callbackThread, const resip::Data& authServerUrl, const resip::Data& username, const resip::Data& password_hash, const std::vector<resip::Data>& requestedResources, const resip::Data& encryptionKey, bool ignoreCertVerification, const SslCipherOptions tlsSettings, const int authTimeoutSecs, const std::function<void(int, const resip::Data&, const cpc::string&)>& resultCb)
{
   AuthRequestor* authRequest = new AuthRequestor(requestServiceThread, callbackThread, true, authServerUrl, username, password_hash, requestedResources, encryptionKey, ignoreCertVerification, tlsSettings, authTimeoutSecs, resultCb);
   authRequest->start();
   return kSuccess;
}

int CloudServerConnection::doAuthFlowWithSimpleWebClient(resip::MultiReactor& requestServiceThread, resip::MultiReactor& callbackThread, const resip::Data& authServerUrl, const resip::Data& username, const resip::Data& password_hash, const std::vector<resip::Data>& requestedResources, const resip::Data& encryptionKey, bool ignoreCertVerification, const SslCipherOptions tlsSettings, const int authTimeoutSecs, const std::function<void(int, const resip::Data&, const cpc::string&)>& resultCb)
{
   AuthRequestor* authRequest = new AuthRequestor(requestServiceThread, callbackThread, false, authServerUrl, username, password_hash, requestedResources, encryptionKey, ignoreCertVerification, tlsSettings, authTimeoutSecs, resultCb);
   authRequest->start();
   return kSuccess;
}

class OrchRequestor
{

public:

   OrchRequestor(resip::MultiReactor& requestServiceThread, resip::MultiReactor& callbackThread, const std::vector<ServiceDesc>& serviceRequests, const resip::Data& orchServerUrl, const resip::Data& authToken, bool ignoreCertVerification, const SslCipherOptions tlsSettings, const int orchTimeoutSecs, const std::function<void(int, const std::vector<ServiceDescUrl>&, const cpc::string&)>& resultCb)
      : mReactor(requestServiceThread),
        mCallbackThread(callbackThread),
        mServiceRequests(serviceRequests),
      mOrchServerUrl(orchServerUrl),
      mAuthToken(authToken),
      mResultDetails(""),
      mIgnoreCertVerification(ignoreCertVerification),
      mTlsSettings(tlsSettings),
      mOrchTimeoutSecs(orchTimeoutSecs),
      mResultCb(resultCb)
   {
   }

   ~OrchRequestor()
   {
   }

   void start()
   {
      mReactor.post(resip::resip_bind(&OrchRequestor::doOrchestrationFlow, this));
   }

private:

   void doOrchestrationFlow()
   {
      std::unique_ptr<OrchRequestor> thisDeleter(this);
      std::vector<ServiceDescUrl> serviceUrls;
      try
      {
         /* now hit orchestration server */
         CurlPPHelper helper;
         curlpp::Easy orchRequest;
         //std::string orchMessageBody = "{"
         //   "\"moduleId\":\"OrchestrationServer\","
         //   "\"functionObject\" : {"
         //   "\"functionName\":\"requestService\","
         //   "   \"serviceRequests\" : [{"
         //   "      \"service\" : \"xmppagent\","
         //   "      \"region\" : \"NA\""
         //   "}]"
         //   "}"
         //   "}";

         rapidjson::Document jsonReq;
         jsonReq.SetObject();
         jsonReq.AddMember("moduleId", "OrchestrationServer", jsonReq.GetAllocator());

         rapidjson::Value functionObject(rapidjson::kObjectType);
         functionObject.AddMember("functionName", "requestService", jsonReq.GetAllocator());
         rapidjson::Value serviceReqsVal(rapidjson::kArrayType);

         auto itServReq = mServiceRequests.begin();
         for (; itServReq != mServiceRequests.end(); ++itServReq)
         {
            rapidjson::Value serviceMapItem(rapidjson::kObjectType);
            rapidjson::Value serviceVal(itServReq->service.c_str(), jsonReq.GetAllocator());
            rapidjson::Value regionVal(itServReq->region.c_str(), jsonReq.GetAllocator());
            serviceMapItem.AddMember("service", serviceVal, jsonReq.GetAllocator());
            serviceMapItem.AddMember("region", regionVal, jsonReq.GetAllocator());
            serviceReqsVal.PushBack(serviceMapItem, jsonReq.GetAllocator());
         }
         functionObject.AddMember("serviceRequests", serviceReqsVal, jsonReq.GetAllocator());
         jsonReq.AddMember("functionObject", functionObject, jsonReq.GetAllocator());

         rapidjson::StringBuffer buffer(0, 1024);
         rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
         jsonReq.Accept(writer);

         std::string orchServerUrl(mOrchServerUrl.c_str(), mOrchServerUrl.size());

         helper.setDefaultOptions(orchRequest, orchServerUrl, "POST", buffer.GetSize());
         helper.setTimeoutOption(orchRequest, mOrchTimeoutSecs);
         StackLog(<< "CloudServerConnection::OrchRequestor::doOrchestrationFlow(): orch server url: " << orchServerUrl << " request: " << buffer.GetString());
         orchRequest.setOpt(new curlpp::options::PostFields(buffer.GetString()));

         std::list<std::string> header;
         header.push_back(std::string("Authorization: bearer ") + mAuthToken.c_str());
         orchRequest.setOpt(new curlpp::options::HttpHeader(header));

         int acceptableFailures( 0 );
         if( mIgnoreCertVerification )
            acceptableFailures = ( int ) CurlPPSSL::E_CERT_WHATEVER_ERROR;

         CurlPPSSL cssl( mTlsSettings, acceptableFailures );
         orchRequest.setOpt(new curlpp::options::SslCtxFunction(cssl));

         std::stringstream responseBody;
         orchRequest.setOpt(new curlpp::options::WriteStream(&responseBody));
         orchRequest.perform();

         int response = curlpp::infos::ResponseCode::get(orchRequest);

         StackLog(<< "CloudServerConnection::OrchRequestor::doOrchestrationFlow(): response: " << responseBody.str());
         if (response == 200)
         {
            mResultDetails = "";

            std::unique_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
            jsonRequest->Parse<0>(responseBody.str().c_str());
            if (jsonRequest->HasParseError())
            {
               mResultDetails = "response parse error";
               postFailureCallback();
               return;
            }

            if (!jsonRequest->HasMember("moduleId"))
            {
               mResultDetails = "incomplete response, missing \"moduleId\"";
               postFailureCallback();
               return;
            }

            const rapidjson::Value& moduleIdVal = (*jsonRequest)["moduleId"];
            if (!moduleIdVal.IsString())
            {
               mResultDetails = "invalid response, \"moduleId\" does not match required data type";
               postFailureCallback();
               return;
            }

            if (std::strncmp(moduleIdVal.GetString(), "OrchestrationServer", 19) != 0)
            {
               mResultDetails = "incomplete response, missing \"OrchestrationServer\"";
               postFailureCallback();
               return;
            }

            if (!jsonRequest->HasMember("functionObject"))
            {
               mResultDetails = "incomplete response, missing \"functionObject\"";
               postFailureCallback();
               return;
            }

            const rapidjson::Value& funcObjVal = (*jsonRequest)["functionObject"];
            if (!funcObjVal.IsObject())
            {
               mResultDetails = "invalid response, \"functionObject\" does not match required data type";
               postFailureCallback();
               return;
            }

            if (!funcObjVal.HasMember("functionName"))
            {
               mResultDetails = "incomplete response, missing \"functionName\"";
               postFailureCallback();
               return;
            }

            const rapidjson::Value& funcNameVal = funcObjVal["functionName"];
            if (!funcNameVal.IsString())
            {
               mResultDetails = "invalid response, \"functionName\" does not match required data type";
               postFailureCallback();
               return;
            }

            if (std::strncmp(funcNameVal.GetString(), "onRequestServiceResult", 22) != 0)
            {
               mResultDetails = "invalid response, expected \"onRequestServiceResult\"";
               postFailureCallback();
               return;
            }

            if (!funcObjVal.HasMember("serviceMappings"))
            {
               mResultDetails = "incomplete response, missing \"serviceMappings\"";
               postFailureCallback();
               return;
            }

            const rapidjson::Value& serviceMappingsVal = funcObjVal["serviceMappings"];
            if (!serviceMappingsVal.IsArray())
            {
               mResultDetails = "invalid response, \"moduleId\" does not match required data type";
               postFailureCallback();
               return;
            }

            for (rapidjson::Value::ConstValueIterator it = serviceMappingsVal.Begin(); it != serviceMappingsVal.End(); ++it)
            {
               if (it->IsObject())
               {
                  ServiceDescUrl serviceUrl;
                  CPCAPI2::Json::Read(*it, "service", serviceUrl.serviceDesc.service);
                  CPCAPI2::Json::Read(*it, "region", serviceUrl.serviceDesc.region);
                  CPCAPI2::Json::Read(*it, "uri", serviceUrl.url);
                  /*
                  // For testing only, required when the cloud service such as the xmpp agent is being tested on a particular machine,
                  // different from what is configured on the orchestrator database.
                  std::map<ServiceDesc, cpc::string>::iterator it2 = mMapServiceToLoopbackUri.find(serviceUrl.serviceDesc);
                  if (it2 != mMapServiceToLoopbackUri.end())
                  {
                     serviceUrl.url = it2->second;
                  }
                  */

                  serviceUrls.push_back(serviceUrl);
               }
            }

            for (std::vector<ServiceDesc>::iterator i = mServiceRequests.begin(); i != mServiceRequests.end(); ++i)
            {
               if (!serviceServerFound((*i), serviceUrls))
               {
                  mResultDetails = "no servers available for \"";
                  mResultDetails += (*i).service;
                  mResultDetails += "\" service in \"";
                  mResultDetails += (*i).region;
                  mResultDetails += "\" region";
                  postFailureCallback();
                  return;
               }
            }

            resip::ReadCallbackBase* cb = resip::resip_static_bind(mResultCb, 0, serviceUrls, mResultDetails);
            mCallbackThread.post(cb);
         }
         else
         {
            mResultDetails = "response: ";
            mResultDetails.append(resip::Data(response).c_str());
            mResultDetails.append(": ");
            mResultDetails.append(responseBody.str().c_str());
            postFailureCallback();
         }
      }
      catch (curlpp::LibcurlRuntimeError& lre)
      {
         if (lre.whatCode() == CURLcode::CURLE_OPERATION_TIMEDOUT)
         {
            thisDeleter.release();
            mReactor.postMS(resip::resip_bind(&OrchRequestor::doOrchestrationFlow, this), 1000);
         }
         else
         {
            mResultDetails = "exception: ";
            mResultDetails.append(lre.what());
            postFailureCallback();
         }
      }
      catch (curlpp::RuntimeError& e)
      {
         mResultDetails = "exception: ";
         mResultDetails.append(e.what());
         postFailureCallback();
      }
   }

   void postFailureCallback()
   {
      std::vector<ServiceDescUrl> urls;
      resip::ReadCallbackBase* cb = resip::resip_static_bind(mResultCb, -1, urls, mResultDetails);
      mCallbackThread.post(cb);
   }

   bool serviceServerFound(ServiceDesc service, std::vector<ServiceDescUrl>& servers)
   {
      for (std::vector<ServiceDescUrl>::iterator i = servers.begin(); i != servers.end(); ++i)
      {
         if ((service.region == (*i).serviceDesc.region) && (service.service == (*i).serviceDesc.service))
         {
            return true;
         }
      }

      return false;
   }

private:

   resip::MultiReactor& mReactor;
   resip::MultiReactor& mCallbackThread;
   resip::Data mOrchServerUrl;
   resip::Data mAuthToken;
   std::vector<ServiceDesc> mServiceRequests;
   cpc::string mResultDetails;
   bool mIgnoreCertVerification;
   const SslCipherOptions mTlsSettings;
   int mOrchTimeoutSecs;
   std::function<void(int, const std::vector<ServiceDescUrl>&, const cpc::string&)> mResultCb;

};

int CloudServerConnection::doOrchestrationFlow(resip::MultiReactor& requestServiceThread, resip::MultiReactor& callbackThread, const std::vector<ServiceDesc>& serviceRequests, const resip::Data& orchServerUrl, const resip::Data& authToken, bool ignoreCertVerification, const SslCipherOptions tlsSettings, const int orchTimeoutSecs, const std::function<void(int, const std::vector<ServiceDescUrl>&, const cpc::string&)>& resultCb)
{
   OrchRequestor* orchRequest = new OrchRequestor(requestServiceThread, callbackThread, serviceRequests, orchServerUrl, authToken, ignoreCertVerification, tlsSettings, orchTimeoutSecs, resultCb);
   orchRequest->start();
   return kSuccess;
}

class AddUserRequestor
{

public:

   AddUserRequestor(resip::MultiReactor& requestServiceThread, resip::MultiReactor& callbackThread, const resip::Data& username, const resip::Data& password, const resip::Data& authServerUrl, const resip::Data& encryptionKey, bool ignoreCertVerification, const SslCipherOptions tlsSettings, const int authTimeoutSecs, const std::function<void(int, const cpc::string&)>& resultCb)
      : mReactor(requestServiceThread),
      mCallbackThread(callbackThread),
      mUsername(username),
      mPassword(password),
      mAuthServerUrl(authServerUrl),
      mEncryptionKey(encryptionKey),
      mResultDetails(""),
      mIgnoreCertVerification(ignoreCertVerification),
      mTlsSettings(tlsSettings),
      mAuthTimeoutSecs(authTimeoutSecs),
      mResultCb(resultCb)
   {
   }

   ~AddUserRequestor()
   {
   }

   void start()
   {
      mReactor.post(resip::resip_bind(&AddUserRequestor::doAddUserFlow, this));
   }

private:

   void doAddUserFlow()
   {
      std::unique_ptr<AddUserRequestor> thisDeleter(this);
      try
      {
         CurlPPHelper helper;
         curlpp::Easy request;
         std::stringstream messageBody;
         messageBody << "{ "
            << "\"username\": \""
            << mUsername.c_str()
            << "\", " << "\"password\": \""
            << mPassword.c_str()
            << "\" "
            << "}"; // memory needs to hang around until after perform

         resip::Data messageBodyEncrypted;
         if (CPCAPI2::AuthServer::JwtUtils::Encrypt(
            resip::Data(mEncryptionKey.c_str(), mEncryptionKey.size()),
            resip::Data(messageBody.str().c_str(), messageBody.str().size()),
            messageBodyEncrypted,
            true) != 0)
         {
            mResultDetails = "failed to encrypt add user request body";
            ErrLog(<< "CloudConnectorImpl::addUser(): " << mResultDetails);
            postFailureCallback();
            return;
         }

         std::string url(mAuthServerUrl.c_str(), mAuthServerUrl.size());

         StackLog(<< "CloudConnectorImpl::addUser(): auth server url: " << url << " request: " << messageBody.str());

         helper.setDefaultOptions(request, url, "POST", messageBodyEncrypted.size());
         helper.setTimeoutOption(request, mAuthTimeoutSecs);

         request.setOpt(new curlpp::options::PostFields(std::string(messageBodyEncrypted.c_str(), messageBodyEncrypted.size())));

         int acceptableFailures( 0 );
         if( mIgnoreCertVerification )
            acceptableFailures = ( int ) CurlPPSSL::E_CERT_WHATEVER_ERROR;

         CurlPPSSL cssl( mTlsSettings, acceptableFailures );
         request.setOpt(new curlpp::options::SslCtxFunction(cssl));

         std::stringstream responseBody;
         request.setOpt(new curlpp::options::WriteStream(&responseBody));
         request.perform();

         int response = curlpp::infos::ResponseCode::get(request);

         StackLog(<< "CloudConnectorImpl::addUser(): response-code: " << response << " response: " << responseBody.str());
         if (response == 201)
         {
            mResultDetails = "";
            resip::ReadCallbackBase* cb = resip::resip_static_bind(mResultCb, 0, mResultDetails);
            mCallbackThread.post(cb);
         }
         else
         {
            mResultDetails = "response: ";
            mResultDetails.append(resip::Data(response).c_str());
            mResultDetails.append(": ");
            mResultDetails.append(responseBody.str().c_str());
            postFailureCallback();
         }
      }
      catch (curlpp::RuntimeError& e)
      {
         mResultDetails = "exception: ";
         mResultDetails.append(e.what());
         postFailureCallback();
      }
   }

   void postFailureCallback()
   {
      resip::ReadCallbackBase* cb = resip::resip_static_bind(mResultCb, -1, mResultDetails);
      mCallbackThread.post(cb);
   }

private:

   resip::MultiReactor& mReactor;
   resip::MultiReactor& mCallbackThread;
   resip::Data mUsername;
   resip::Data mPassword;
   resip::Data mAuthServerUrl;
   resip::Data mEncryptionKey;
   cpc::string mResultDetails;
   bool mIgnoreCertVerification;
   const SslCipherOptions mTlsSettings;
   int mAuthTimeoutSecs;
   std::function<void(int, const cpc::string&)> mResultCb;

};

int CloudServerConnection::doAddUserFlow(resip::MultiReactor& requestServiceThread, resip::MultiReactor& callbackThread, const resip::Data& username, const resip::Data& password, const resip::Data& authServerUrl, const resip::Data& encryptionKey, bool ignoreCertVerification, const SslCipherOptions tlsSettings, const int authTimeoutSecs, const std::function<void(int, const cpc::string& details)>& resultCb)
{
   AddUserRequestor* addUserRequestor = new AddUserRequestor(requestServiceThread, callbackThread, username, password, authServerUrl, encryptionKey, ignoreCertVerification, tlsSettings, authTimeoutSecs, resultCb);
   addUserRequestor->start();
   return kSuccess;
}

void CloudServerConnection::handleSdkCallback()
{
   mPhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
}

void CloudServerConnection::changeState(CloudServerConnStatus newStatus)
{
   StackLog(<< "CloudServerConnection::changeState(): " << this << " phone: " << mPhone << " master phone: " << mMasterSdkPhone << " json client: " << mJsonApiClient << " current status: " << CloudServerConnection::get_debug_string(mStatus) << " new status: " << CloudServerConnection::get_debug_string(newStatus));
   if ((mStatus == CloudServerConnStatus_Disconnected) && (newStatus == CloudServerConnStatus_Disconnecting))
   {
      StackLog(<< "CloudServerConnection::changeState(): " << this << " ignoring change state to: " << CloudServerConnection::get_debug_string(newStatus) << " as connection is already in: " << CloudServerConnection::get_debug_string(mStatus) << " state");
   }
   else if ((mStatus == CloudServerConnStatus_Disconnected) && (newStatus == CloudServerConnStatus_LoggedOut))
   {
      StackLog(<< "CloudServerConnection::changeState(): " << this << " ignoring change state to: " << CloudServerConnection::get_debug_string(newStatus) << " as connection is already in: " << CloudServerConnection::get_debug_string(mStatus) << " state");
   }
   else
   {
      mStatus = newStatus;
   }

   CloudServerConnStatusEvent args;
   args.status = mStatus;
   mObs->handleCloudServerConnectionStatusChanged(this, args);
}

int CloudServerConnection::onError(const cpc::string& sourceModule, const PhoneErrorEvent& args)
{
   return 0;
}

int CloudServerConnection::onLicensingError(const LicensingErrorEvent& args)
{
   return 0;
}

int CloudServerConnection::onLicensingSuccess()
{
   return 0;
}

// JsonApiClientHandler

int CloudServerConnection::onStatusChanged(CPCAPI2::JsonApi::JsonApiLoginHandle h, const CPCAPI2::JsonApi::StatusChangedEvent& args)
{
   StackLog(<< "CloudServerConnection::onStatusChanged(): " << this << " phone: " << mPhone << " master phone: " << mMasterSdkPhone << " json client: " << mJsonApiClient << " handle: " << h << " status: " << CPCAPI2::JsonApi::StatusChangedEvent::get_debug_string(args.status) << " phone thread: " << (dynamic_cast<PhoneInterface*>(mPhone))->getSdkModuleThread().isCurrentThread() << " master phone thread: " << (dynamic_cast<PhoneInterface*>(mMasterSdkPhone))->getSdkModuleThread().isCurrentThread() << " thread-id: " << resip::ThreadIf::selfId());

   if (args.status == StatusChangedEvent::Status_Connected)
   {
      changeState(CloudServerConnStatus_Authenticating);
      mJsonApiClient->login(mAuthToken, mRealm);
   }
   else if (args.status == StatusChangedEvent::Status_Disconnected)
   {
      changeState(CloudServerConnStatus_Disconnected);
   }
   else if (args.status == StatusChangedEvent::Status_Failed)
   {
      changeState(CloudServerConnStatus_ConnFailure);
   }
   return 0;
}

int CloudServerConnection::onLoginResult(CPCAPI2::JsonApi::JsonApiLoginHandle h, const CPCAPI2::JsonApi::LoginResultEvent& args)
{
   StackLog(<< "CloudServerConnection::onLoginResult(): " << this << " phone: " << mPhone << " handle: " << h << " login result: " << args.success);

   if (args.success)
   {
      changeState(CloudServerConnStatus_Connected);
   }
   else
   {
      changeState(CloudServerConnStatus_AuthFailure);
   }
   return 0;
}

int CloudServerConnection::onLogoutResult(CPCAPI2::JsonApi::JsonApiLoginHandle h, const CPCAPI2::JsonApi::LogoutResultEvent& args)
{
   StackLog(<< "CloudServerConnection::onLogoutResult(): " << this << " phone: " << mPhone << " handle: " << h << " logout result: " << args.success);
   if (args.success)
   {
      changeState(CloudServerConnStatus_LoggedOut);
   }
   else
   {
      changeState(CloudServerConnStatus_LogoutFailure);
   }
   return 0;
}

cpc::string CloudServerConnection::get_debug_string(const CloudServerConnStatus& status)
{
   switch (status)
   {
      case CloudServerConnStatus_Disconnecting: return "disconnecting";
      case CloudServerConnStatus_Disconnected: return "disconnected";
      case CloudServerConnStatus_LoggedOut: return "loggedout";
      case CloudServerConnStatus_Connecting: return "connecting";
      case CloudServerConnStatus_Authenticating: return "authenticating";
      case CloudServerConnStatus_Connected: return "connected";
      case CloudServerConnStatus_ConnFailure: return "connfailure";
      case CloudServerConnStatus_AuthFailure: return "authfailure";
      case CloudServerConnStatus_LogoutFailure: return "logoutfailure";
      default: return "invalid";
   }
   return "invalid";
}

}

}

#endif // CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE
