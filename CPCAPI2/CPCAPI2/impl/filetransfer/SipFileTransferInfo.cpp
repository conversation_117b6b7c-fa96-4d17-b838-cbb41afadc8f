#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1)

#include <msrp_uri.h>
#include <msrp_stack.h>
#include <msrp_session.h>

#include "SipFileTransferInfo.h"
#include "SipFileTransferManagerImpl.h"

using namespace CPCAPI2::SipFileTransfer;

CPCAPI2::SipFileTransfer::SipFileTransferHandle sNextFileTransferHandle = 1;
CPCAPI2::SipFileTransfer::SipFileTransferItemHandle sNextFileTransferItemHandle = 1;

SipFileTransferItemInfo::SipFileTransferItemInfo( SipFileTransferItemHandle newHandle )
: m_pLocal( NULL ),
  m_pRemote( NULL ),
  m_pMessage( NULL ),
  m_Map( NULL )
{
   // Initialize the basic type members of details
   detail.handle          = newHandle;
   detail.fileSizeBytes   = 0;
   detail.isIncoming      = false;
   detail.acceptedState   = ftitem_notprocessed;
   detail.percentComplete = 0;
}

SipFileTransferItemInfo::~SipFileTransferItemInfo()
{
   msrp_uri_destroy( m_pLocal );
   m_pLocal = NULL;

   msrp_uri_destroy( m_pRemote );
   m_pRemote = NULL;

   delete m_Map;
   m_Map = NULL;
   m_pMessage = NULL;
}

SipFileTransferInfo::SipFileTransferInfo( SipFileTransferHandle newHandle, resip::SharedPtr<resip::DialogUsageManager> dum, resip::InviteSessionHandler* handler )
: CPInviteHandlerSelectorDialogSet( *dum, handler ),
  handle( newHandle ),
  m_OfferedAsSendRecv( false )
{
   accountHandle = 0;
   m_pSessions = msrp_list_create();
}

SipFileTransferInfo::~SipFileTransferInfo()
{
   transferItems.clear();

   // Sometimes there could be an item in the list. This happens when DUM is forcefully
   // shut-down before an ongoing file transfer has a chance to terminate naturally.
   //
   // In this case we should delete the session now (and avoid re-deleting it later)
   if( msrp_list_size( m_pSessions ) > 0 )
   {
      SipFileTransferManagerImpl *pMgr = dynamic_cast< SipFileTransferManagerImpl * >( getHandler() );
      msrp_session_t *p_session = NULL;
      msrp_list_enum_t *p_enum  = NULL;
      msrp_stack_t *p_stack     = NULL;

      if( pMgr != NULL )
         p_stack = pMgr->getMSRPStack();

      p_enum = msrp_list_enum_create( m_pSessions );
      while( msrp_list_enum_next( p_enum, ( void ** ) &p_session ))
      {
         msrp_stack_session_destroy( p_stack, p_session );
      }
      msrp_list_enum_destroy( p_enum );
      msrp_list_empty( m_pSessions );
   }

   msrp_list_destroy( m_pSessions );
   m_pSessions = NULL;
}

#endif