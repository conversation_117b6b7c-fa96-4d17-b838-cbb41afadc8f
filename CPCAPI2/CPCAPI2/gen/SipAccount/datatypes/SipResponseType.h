#include "jsonrpc/StringSerialization.h"
#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_SIPRESPONSETYPE_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_SIPRESPONSETYPE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  class SipResponseType
  {
  public:
    std::optional<cpc::string> method;
    std::optional<int32_t> responseCode;

    SipResponseType() = default;

    SipResponseType(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipResponseType& o)
    {
      j = nlohmann::json::object();
       if (o.method) { j["method"] = *(o.method); }
     if (o.responseCode) { j["responseCode"] = *(o.responseCode); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipResponseType& o)
    {
     if (j.contains("method")) { o.method = j.at("method"); }
     if (j.contains("responseCode")) { o.responseCode = j.at("responseCode"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_SIPRESPONSETYPE_H
