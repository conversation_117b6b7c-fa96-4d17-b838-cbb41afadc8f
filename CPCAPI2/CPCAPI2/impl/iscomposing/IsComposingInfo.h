#pragma once

#if !defined(__CPCAPI2_IS_COMPOSING_INFO_H__)
#define __CPCAPI2_IS_COMPOSING_INFO_H__

#include "cpm/CpmTypes.h"
#include "ComposerBaseState.h"
#include "ReceiverBaseState.h"

#include <resip/stack/Mime.hxx>
#include <rutil/MultiReactor.hxx>
#include <rutil/DeadlineTimer.hxx>

namespace CPCAPI2
{
namespace IsComposing
{

struct IsComposingInfo
{
   IsComposingInfo();
   virtual ~IsComposingInfo();

   ComposerBaseState* composerState;
   resip::Mime composerContentType;
   int composerRefreshInterval;
   int composerIdleInterval;
   tm composerLastActive;
   resip::DeadlineTimer<resip::MultiReactor>* composerIdleTimer;
   resip::DeadlineTimer<resip::MultiReactor>* composerRefreshTimer;
   ReceiverBaseState* receiverState;
   int receiverRefreshInterval;
   resip::Mime receiverContentType;
   resip::DeadlineTimer<resip::MultiReactor>* receiverRefreshTimer;
   tm receiverLastActive;
};

}
}

#endif // __CPCAPI2_IS_COMPOSING_INFO_H__