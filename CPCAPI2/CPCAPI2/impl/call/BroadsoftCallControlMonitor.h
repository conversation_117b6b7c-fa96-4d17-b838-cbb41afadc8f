#if !defined(BROADSOFT_CALL_CONTROL_MONITOR_H)
#define BROADSOFT_CALL_CONTROL_MONITOR_H

#include <resip/dum/DumFeature.hxx>
#include <rutil/Data.hxx>

#include <set>


namespace resip
{
class DialogUsageManager;
class DumFeature;
class Message;
class SipMessage;
}

namespace CPCAPI2
{
namespace SipConversation
{
class BroadsoftCallControlMonitor
{
public:
   static const resip::Data TalkEvent;
   static const resip::Data HoldEvent;
   
   BroadsoftCallControlMonitor(resip::DialogUsageManager& dum);
   virtual ~BroadsoftCallControlMonitor() { }
   
   void watch(const resip::SipMessage& msg);
   bool claim(const resip::SipMessage& msg);
   
private:
   BroadsoftCallControlMonitor();

   typedef std::set<resip::Data> WatchedTransactions;
   WatchedTransactions mWatching;
   
   
   // Private classes that do the processing on inbound and outbound messages
   class Incoming : public resip::DumFeature
   {
   public:
      Incoming(BroadsoftCallControlMonitor& monitor, resip::DialogUsageManager& dum);
      virtual ~Incoming() { }
      virtual DumFeature::ProcessingResult process(resip::Message* msg);
      
   private:
      Incoming();
      BroadsoftCallControlMonitor& mMonitor;
   };
   
   class Outgoing : public resip::DumFeature
   {
   public:
      Outgoing(BroadsoftCallControlMonitor& monitor, resip::DialogUsageManager& dum);
      virtual ~Outgoing() { }
      virtual DumFeature::ProcessingResult process(resip::Message* msg);
      
   private:
      Outgoing();
      BroadsoftCallControlMonitor& mMonitor;
   };
};
}
}

#endif // BROADSOFT_CALL_CONTROL_MONITOR_H
