#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_PHONE_LICENSINGERRORREASON_H
#define JSONRPC_CPCAPI2_PHONE_LICENSINGERRORREASON_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  enum class LicensingErrorReason
  {
    LicensingErrorReasonInvalid,
    LicensingErrorReasonExpired,
    LicensingErrorReasonRejected,
    LicensingErrorReasonServerError,
    LicensingErrorReasonUnknown
  };

  inline std::string to_string(LicensingErrorReason value)
  {
    switch (value)
    {
      case LicensingErrorReason::LicensingErrorReasonInvalid:
        return "LicensingErrorReason_Invalid";
      case LicensingErrorReason::LicensingErrorReasonExpired:
        return "LicensingErrorReason_Expired";
      case LicensingErrorReason::LicensingErrorReasonRejected:
        return "LicensingErrorReason_Rejected";
      case LicensingErrorReason::LicensingErrorReasonServerError:
        return "LicensingErrorReason_ServerError";
      case LicensingErrorReason::LicensingErrorReasonUnknown:
        return "LicensingErrorReason_Unknown";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, LicensingErrorReason value)
  {
    switch (value)
    {
      case LicensingErrorReason::LicensingErrorReasonInvalid:
        os << "LicensingErrorReason_Invalid";
        break;
      case LicensingErrorReason::LicensingErrorReasonExpired:
        os << "LicensingErrorReason_Expired";
        break;
      case LicensingErrorReason::LicensingErrorReasonRejected:
        os << "LicensingErrorReason_Rejected";
        break;
      case LicensingErrorReason::LicensingErrorReasonServerError:
        os << "LicensingErrorReason_ServerError";
        break;
      case LicensingErrorReason::LicensingErrorReasonUnknown:
        os << "LicensingErrorReason_Unknown";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const LicensingErrorReason& e)
  {
    switch (e)
    {
      case LicensingErrorReason::LicensingErrorReasonInvalid:
        j = "LicensingErrorReason_Invalid";
        break;
      case LicensingErrorReason::LicensingErrorReasonExpired:
        j = "LicensingErrorReason_Expired";
        break;
      case LicensingErrorReason::LicensingErrorReasonRejected:
        j = "LicensingErrorReason_Rejected";
        break;
      case LicensingErrorReason::LicensingErrorReasonServerError:
        j = "LicensingErrorReason_ServerError";
        break;
      case LicensingErrorReason::LicensingErrorReasonUnknown:
        j = "LicensingErrorReason_Unknown";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, LicensingErrorReason& e)
  {
      if (0 == j.get<std::string>().compare("LicensingErrorReason_Invalid"))
    {
      e = LicensingErrorReason::LicensingErrorReasonInvalid;
    }
    else if (0 == j.get<std::string>().compare("LicensingErrorReason_Expired"))
    {
      e = LicensingErrorReason::LicensingErrorReasonExpired;
    }
    else if (0 == j.get<std::string>().compare("LicensingErrorReason_Rejected"))
    {
      e = LicensingErrorReason::LicensingErrorReasonRejected;
    }
    else if (0 == j.get<std::string>().compare("LicensingErrorReason_ServerError"))
    {
      e = LicensingErrorReason::LicensingErrorReasonServerError;
    }
    else if (0 == j.get<std::string>().compare("LicensingErrorReason_Unknown"))
    {
      e = LicensingErrorReason::LicensingErrorReasonUnknown;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum LicensingErrorReason"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_LICENSINGERRORREASON_H
