#pragma once

#ifndef __CPCAPI2_CP_INVITE_HANDLER_SELECTOR_DIALOG_SET_H__
#define __CPCAPI2_CP_INVITE_HANDLER_SELECTOR_DIALOG_SET_H__

#include <resip/dum/AppDialogSet.hxx>
#include <resip/dum/InviteSession.hxx>

namespace CPCAPI2
{
   namespace SipAccount
   {
      struct CPInviteHandlerSelectorDialogSet : public resip::AppDialogSet
      {
      public:
         CPInviteHandlerSelectorDialogSet(resip::DialogUsageManager& dum, resip::InviteSessionHandler* handler);
         ~CPInviteHandlerSelectorDialogSet();

         resip::InviteSessionHandler* getHandler() const { return handler; }

      private:
         resip::InviteSessionHandler* handler;
      };
   }
}

#endif // __CPCAPI2_CP_INVITE_HANDLER_SELECTOR_DIALOG_SET_H__