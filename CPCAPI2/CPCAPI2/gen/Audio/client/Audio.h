#include "../datatypes/AudioAudioDeviceRole.h"

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
class Audio
{
public:
    Audio(ITransport& transport)
    {
        this.transport = transport;
    }

    void queryDeviceList(int64_t phoneHandle);

	void setCaptureDevice(int64_t phoneHandle, int64_t deviceId, AudioAudioDeviceRole role);

	void setRenderDevice(int64_t phoneHandle, int64_t deviceId, AudioAudioDeviceRole role);

	int64_t playSound(int64_t phoneHandle, AudioAudioDeviceRole role, cpc::string resourceUri, bool repeat);

	void stopPlaySound(int64_t phoneHandle, int64_t sound);

	void queryCodecList(int64_t phoneHandle);

	void setCodecEnabled(int64_t phoneHandle, int64_t codecId, bool enabled);

	void setCodecPriority(int64_t phoneHandle, int64_t codecId, int64_t priority);

	void setCodecPayloadType(int64_t phoneHandle, int64_t codecId, int64_t payloadType);

	void setTelephoneEventPayloadType(int64_t phoneHandle, int64_t payloadType);

	void setMicMute(int64_t phoneHandle, bool enabled);

	void setSpeakerMute(int64_t phoneHandle, bool enabled);

	void setMicVolume(int64_t phoneHandle, int64_t level);

	void setSpeakerVolume(int64_t phoneHandle, int64_t level);

	void queryDeviceVolume(int64_t phoneHandle);

	void startMonitoringCaptureDeviceLevels(int64_t phoneHandle, int64_t deviceId);

	void stopMonitoringRenderDeviceLevels(int64_t phoneHandle);

private:
    ITransport* transport;
}; // class Audio
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
