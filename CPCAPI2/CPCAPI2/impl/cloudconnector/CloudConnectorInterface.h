#pragma once

#if !defined(CPCAPI2_CLOUD_CONNECTOR_INTERFACE_H)
#define CPCAPI2_CLOUD_CONNECTOR_INTERFACE_H

#include "cpcapi2defs.h"
#include "cloudconnector/CloudConnector.h"
#include "xmpp_agent/XmppAgentHandler.h"
#include "phone/Cpcapi2EventSource.h"
#include "phone/PhoneModule.h"
#include "phone/NetworkChangeManagerImpl.h"
#include "cloudconnector/ServiceDesc.h"


namespace CPCAPI2
{

class PhoneInterface;

namespace CloudConnector
{

struct ServiceConnectionStatusEvent;
struct AddUserResponse;
struct LogoutResult;
class CloudConnectorImpl;
class CloudConnectorSyncHandler {};

class CloudConnectorInterface : public CPCAPI2::EventSource<CloudConnectorHandle, CloudConnectorHandler, CloudConnectorSyncHandler>,
                                public PhoneModule,
                                public CloudConnectorManager,
                                // public XmppAgent::XmppAgent<PERSON>andler,
                                public CPCAPI2::EventSyncHandler<NetworkChangeHandler>
{

public:

   CloudConnectorInterface(Phone* phone);
   virtual ~CloudConnectorInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // NetworkChangeHandler
   virtual int onNetworkChange(const NetworkChangeEvent& event) OVERRIDE;

   // XmppAgentHandler
   /*
   virtual int onPushRegistrationSuccess(XmppAgent::XmppPushRegistrationHandle pushRegistration, const XmppAgent::XmppPushRegistrationSuccessEvent& args) OVERRIDE;
   virtual int onPushRegistrationFailure(XmppAgent::XmppPushRegistrationHandle pushRegistration, const XmppAgent::XmppPushRegistrationFailureEvent& args) OVERRIDE;
   virtual int onEventHistory(XmppAgent::XmppPushRegistrationHandle pushRegistration, const XmppAgent::XmppEventHistory& args) OVERRIDE;
   virtual int onRemoteSyncRegisterResult(XmppAgent::XmppPushRegistrationHandle pushRegistration, const XmppAgent::XmppAgentRemoteSyncRegisterResult& args) OVERRIDE;
   virtual int onLogout(XmppAgent::XmppPushRegistrationHandle pushRegistration, const XmppAgent::LogoutResult& args) OVERRIDE;
   */

   // CloudConnectorManager
   virtual int process(unsigned int timeout) OVERRIDE {
      return CPCAPI2::EventSource<CloudConnectorHandle, CloudConnectorHandler, CloudConnectorSyncHandler>::process(timeout);
   }
   virtual void interruptProcess() OVERRIDE {
      CPCAPI2::EventSource<CloudConnectorHandle, CloudConnectorHandler, CloudConnectorSyncHandler>::interruptProcess();
   }
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE {
      CPCAPI2::EventSource<CloudConnectorHandle, CloudConnectorHandler, CloudConnectorSyncHandler>::setCallbackHook(cbHook, context);
   }
   virtual CloudConnectorHandle createCloudConnector() OVERRIDE;
   virtual int setHandler(CloudConnectorHandle conn, CloudConnectorHandler* handler) OVERRIDE;
   virtual int setConnectionSettings(CloudConnectorHandle conn, const CloudConnectorSettings& settings) OVERRIDE;
   virtual int requestService(CloudConnectorHandle conn, const ServiceDescription& serviceDescriptor) OVERRIDE;
   virtual int connectToServices(CloudConnectorHandle conn) OVERRIDE;
   virtual CPCAPI2::Phone* getPhone(CloudConnectorHandle conn, const ServiceDescription& serviceDescriptor) OVERRIDE;
   virtual int disconnectService(CloudConnectorHandle conn, const ServiceDescription& serviceDescriptor) OVERRIDE;
   virtual int addUser(CloudConnectorHandle conn, const AddUserRequest& req) OVERRIDE;
   virtual int destroy(CloudConnectorHandle conn) OVERRIDE;
   virtual int logout(CloudConnectorHandle conn) OVERRIDE;

   PhoneInterface* getThisSdkPhone() const {
      return mPhone;
   }

   void fireConnStatusEvent(CloudConnectorHandle conn, const ServiceConnectionStatusEvent& evt);
   void fireAddUserResponse(CloudConnectorHandle conn, const AddUserResponse& resp);
   void fireLogoutResult(CloudConnectorHandle conn, const LogoutResult& result);
   void addServiceToPhoneMapping(const ServiceDesc& desc, CPCAPI2::Phone* p);
   void removeServiceFromPhoneMapping(const ServiceDesc& desc);
   int setNetworkRestriction(CloudConnectorHandle conn, NetworkTransport transport, bool restricted);

private:

   void createCloudConnectorImpl(CloudConnectorHandle conn);
   void setConnectionSettingsImpl(CloudConnectorHandle conn, const CloudConnectorSettings& settings);
   void requestServiceImpl(CloudConnectorHandle conn, const ServiceDescription& serviceDescriptor);
   void connectToServicesImpl(CloudConnectorHandle conn);
   void disconnectServiceImpl(CloudConnectorHandle conn, const ServiceDescription& serviceDescriptor);
   void addUserImpl(CloudConnectorHandle conn, const AddUserRequest& req);
   void destroyImpl(CloudConnectorHandle conn);
   void logoutImpl(CloudConnectorHandle conn);
   void addServiceToPhoneMappingImpl(const ServiceDesc& desc, CPCAPI2::Phone* p);
   void removeServiceFromPhoneMappingImpl(const ServiceDesc& desc);

   void onNetworkChangeImpl(const NetworkChangeEvent& event);
   void setNetworkRestrictionImpl(CloudConnectorHandle conn, NetworkTransport transport, bool restricted);

private:

   PhoneInterface* mPhone;

   typedef std::map<CloudConnectorHandle, std::shared_ptr<CloudConnectorImpl>> InstanceMap;
   InstanceMap mInstMap;

   typedef std::map<ServiceDesc, Phone*> PhoneMap;
   PhoneMap mPhoneMap;

   ServiceConnectionStatus mLastServiceConnectionStatus;

};

std::ostream& operator<<(std::ostream& os, const ServiceConnectionStatusEvent& evt);


class CloudConnectorHandleFactory
{

public:

   static CloudConnectorHandle getNext() { return sNextHandle++; }

private:

   static CloudConnectorHandle sNextHandle;

};

}

}

#endif // CPCAPI2_CLOUD_CONNECTOR_INTERFACE_H

