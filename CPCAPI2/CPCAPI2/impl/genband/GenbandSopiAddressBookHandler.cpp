// Copyright: (c) 2015 by CounterPath Corporation, all rights reserved.

#include "brand_branded.h"

#if (CPCAPI2_BRAND_GENBAND_SOPI_MODULE == 1)

#include "cpcapi2utils.h"
#include "GenbandSopiAddressBookHandler.h"
#include "genband/GenbandSopiHandler.h"
#include "GenbandSopiManagerInterface.h"

#ifdef ANDROID
#include <android/log.h>
#endif

using namespace CPCAPI2::SipEvent;
using namespace resip;

namespace CPCAPI2
{
namespace GenbandSopi
{	

GenbandSopiAddressBookHandler::GenbandSopiAddressBookHandler(GenbandSopiManagerInterface* itf)
   : mClientHandle(NULL),
     mInterface(itf)
{
}

GenbandSopiAddressBookHandler::~GenbandSopiAddressBookHandler()
{
}

void GenbandSopiAddressBookHandler::setSubscriptionHandler(GenbandSopiClientHandle handle)
{
	mClientHandle = handle;
}
	
int GenbandSopiAddressBookHandler::onIncomingEventState(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingEventStateEvent& args)
{
	#ifdef ANDROID
		__android_log_print(ANDROID_LOG_WARN, "GENBANDSOPI", "onIncomingEventState client = %d",mClientHandle);
	#endif
	
	if(mClientHandle)
	{
		mInterface->requestAddressBook(mClientHandle);
	}
	
    return 1;
}


} //Genband

}//CPCAPI2

#endif //CPCAPI2_BRAND_GENBAND_SOPI_MODULE