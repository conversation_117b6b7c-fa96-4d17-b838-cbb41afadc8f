#include "brand_branded.h"

#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "ConferenceBridgeJsonServerInterface.h"
#include "confbridge/ConferenceBridgeManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "jsonapi/JsonApiServerSendTransportInternal.h"
#include "json/JsonHelper.h"
#include "util/LogSubsystems.h"

#include <rutil/Logger.hxx>
#include <rutil/Random.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#include <ostream>
#include <fstream>

using CPCAPI2::PeerConnection::SessionDescription;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_CALL
#define JSON_MODULE "ConferenceBridgeManagerJsonProxy"

namespace CPCAPI2
{
namespace ConferenceBridge
{
ConferenceBridgeJsonServerInterface::ConferenceBridgeJsonServerInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mConfBridgeMgr(CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(phone))
{
   ConferenceBridgeManagerInterface* confBridgeMgrIf = dynamic_cast<ConferenceBridgeManagerInterface*>(mConfBridgeMgr);
   confBridgeMgrIf->addSdkObserver(this);

   mFunctionMap["setHandler"] = std::bind(&ConferenceBridgeJsonServerInterface::handleSetHandler, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["createConference"] = std::bind(&ConferenceBridgeJsonServerInterface::handleCreateConference, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["addSipEndpoint"] = std::bind(&ConferenceBridgeJsonServerInterface::handleAddSipEndpoint, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["queryConferenceDetails"] = std::bind(&ConferenceBridgeJsonServerInterface::handleQueryConferenceDetails, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["queryConferenceDetails2"] = std::bind(&ConferenceBridgeJsonServerInterface::handleQueryConferenceDetails2, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["queryConferenceList"] = std::bind(&ConferenceBridgeJsonServerInterface::handleQueryConferenceList, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["queryParticipantList"] = std::bind(&ConferenceBridgeJsonServerInterface::handleQueryParticipantList, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["createWebParticipant"] = std::bind(&ConferenceBridgeJsonServerInterface::handleCreateWebParticipant, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["destroyWebParticipant"] = std::bind(&ConferenceBridgeJsonServerInterface::handleDestroyWebParticipant, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["setWebParticipantIdentity"] = std::bind(&ConferenceBridgeJsonServerInterface::handleSetWebParticipantIdentity, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["sendPeerConnectionOffer"] = std::bind(&ConferenceBridgeJsonServerInterface::handleSendPeerConnectionOffer, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["sendPeerConnectionAnswer"] = std::bind(&ConferenceBridgeJsonServerInterface::handleSendPeerConnectionAnswer, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["takeParticipantSnapshot"] = std::bind(&ConferenceBridgeJsonServerInterface::handleTakeParticipantSnapshot, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["setParticipantPhoto"] = std::bind(&ConferenceBridgeJsonServerInterface::handleSetParticipantPhoto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["addAssociatedConference"] = std::bind(&ConferenceBridgeJsonServerInterface::handleAddAssociatedConference, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["destroyConference"] = std::bind(&ConferenceBridgeJsonServerInterface::handleDestroyConference, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["addToFloor"] = std::bind(&ConferenceBridgeJsonServerInterface::handleAddToFloor, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["removeFromFloor"] = std::bind(&ConferenceBridgeJsonServerInterface::handleRemoveFromFloor, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["requestFloor"] = std::bind(&ConferenceBridgeJsonServerInterface::handleRequestFloor, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["setStreamingEnabled"] = std::bind(&ConferenceBridgeJsonServerInterface::handleSetStreamingEnabled, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["setRecordingEnabled"] = std::bind(&ConferenceBridgeJsonServerInterface::handleSetRecordingEnabled, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["setTranscriptionEnabled"] = std::bind(&ConferenceBridgeJsonServerInterface::handleSetTranscriptionEnabled, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
   mFunctionMap["setParticipantPermissions"] = std::bind(&ConferenceBridgeJsonServerInterface::handleSetParticipantPermissions, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);

   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
}

ConferenceBridgeJsonServerInterface::~ConferenceBridgeJsonServerInterface()
{
   dynamic_cast<ConferenceBridgeManagerInterface*>(mConfBridgeMgr)->removeSdkObserver(this);
}

void ConferenceBridgeJsonServerInterface::Release()
{
}

void ConferenceBridgeJsonServerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void ConferenceBridgeJsonServerInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int ConferenceBridgeJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request, const std::shared_ptr<resip::Data>& binaryData)
{
   post(resip::resip_bind(&ConferenceBridgeJsonServerInterface::processIncomingImpl, this, conn, request, binaryData));
   return kSuccess;
}

void ConferenceBridgeJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request, const std::shared_ptr<resip::Data>& binaryData)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(conn, functionObjectVal, binaryData);
   }
}

int ConferenceBridgeJsonServerInterface::handleConnectionClosed(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn)
{
   post(resip::resip_bind(&ConferenceBridgeJsonServerInterface::handleConnectionClosedImpl, this, conn));
   return kSuccess;
}

void ConferenceBridgeJsonServerInterface::handleConnectionClosedImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn)
{
   DebugLog(<< "ConferenceBridgeJsonServerInterface::handleConnectionClosedImpl: mMapConnToWebPart.size(): " << mMapConnToWebPart.size());
   MapConnToWebParticipants::iterator it = mMapConnToWebPart.find(conn);
   if (it != mMapConnToWebPart.end())
   {
      DebugLog(<< "ConferenceBridgeJsonServerInterface::handleConnectionClosedImpl: it->second.size(): " << it->second.size());
      std::vector<ConferenceParticipantHandle>::iterator itPart = it->second.begin();
      for (; itPart != it->second.end(); ++itPart)
      {
         dynamic_cast<ConferenceBridgeManagerInterface*>(mConfBridgeMgr)->destroyWebParticipant(*itPart);
      }
      mMapConnToWebPart.erase(it);
   }

   dynamic_cast<ConferenceBridgeManagerInterface*>(mConfBridgeMgr)->destroyEmptyConferences();
}

int ConferenceBridgeJsonServerInterface::handleSetHandler(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::handleCreateConference(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceHandle conference = 0;
   ConferenceSettings conferenceSettings;

   JsonDeserialize(functionObjectVal, JSON_VALUE(conference), JSON_VALUE(conferenceSettings));

   dynamic_cast<ConferenceBridgeManagerInterface*>(mConfBridgeMgr)->createConference(conference, conferenceSettings);
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::handleAddSipEndpoint(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceHandle conference = 0;
   SipAccount::SipAccountHandle acctHandle = 0;

   JsonDeserialize(functionObjectVal, JSON_VALUE(conference), "sipAccount", acctHandle);

   mConfBridgeMgr->addSipEndpoint(conference, acctHandle);
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::handleDestroyConference(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceHandle conference = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conference));
   mConfBridgeMgr->destroyConference(conference);
   return kSuccess;
}

class ConferenceDetailsHandlerProxy : public CPCAPI2::ConferenceBridge::ConferenceDetailsHandler,
                                      public CPCAPI2::ConferenceBridge::ConferenceBridgeSyncHandler
{
public:
   ConferenceDetailsHandlerProxy(CPCAPI2::JsonApi::JsonApiRequestInfo connId, CPCAPI2::JsonApi::JsonApiServerSendTransport* transport)
      : mConnId(connId), mTransport(transport)
   {
   }
   virtual ~ConferenceDetailsHandlerProxy() {}
   virtual int onConferenceDetails(ConferenceHandle conference, const ConferenceDetailsResult& args) OVERRIDE
   {
      {
         CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, true, JSON_MODULE, "onConferenceDetails");
         serializer.addValue("conference", conference);
         serializer.addValue("conferenceToken", args.conferenceToken);
         serializer.addValue("conferenceInfo", args.conferenceInfo);
         serializer.addValue("persistent", args.persistent);
         serializer.finalize();

         dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInternal*>(mTransport)->send(mConnId, json);
      }
      delete this;
      return kSuccess;
   }
   virtual int onConferenceNotFound(ConferenceHandle conference, const ConferenceNotFoundResult& args) OVERRIDE
   {
      {
         CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, true, JSON_MODULE, "onConferenceNotFound");
         serializer.addValue("conference", conference);
         serializer.finalize();

         dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInternal*>(mTransport)->send(mConnId, json);
      }
      delete this;
      return kSuccess;
   }
private:
   CPCAPI2::JsonApi::JsonApiRequestInfo mConnId;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;
};

int ConferenceBridgeJsonServerInterface::handleQueryConferenceDetails(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceHandle conference = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conference));
   mConfBridgeMgr->queryConferenceDetails(conference, new ConferenceDetailsHandlerProxy(connId, mTransport));
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::handleQueryConferenceDetails2(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   cpc::string conferenceToken;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conferenceToken));
   mConfBridgeMgr->queryConferenceDetails(conferenceToken, new ConferenceDetailsHandlerProxy(connId, mTransport));
   return kSuccess;
}

class ConferenceListHandlerProxy : public CPCAPI2::ConferenceBridge::ConferenceListHandler,
                                   public CPCAPI2::ConferenceBridge::ConferenceBridgeSyncHandler
{
public:
   ConferenceListHandlerProxy(CPCAPI2::JsonApi::JsonApiRequestInfo connId, CPCAPI2::JsonApi::JsonApiServerSendTransport* transport)
      : mConnId(connId), mTransport(transport)
   {
   }
   virtual ~ConferenceListHandlerProxy() {}
   virtual int onConferenceList(const ConferenceListResult& args) OVERRIDE {
      //JsonFunctionCall(mTransport, "onConferenceList", "conferences", args.conferences);
      {
         CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, true, JSON_MODULE, "onConferenceList");
         serializer.addValue("conferences", args.conferences);
         serializer.finalize();

         dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInternal*>(mTransport)->send(mConnId, json);
      }
      delete this;
      
      return kSuccess;
   }

private:
   CPCAPI2::JsonApi::JsonApiRequestInfo mConnId;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;
};

int ConferenceBridgeJsonServerInterface::handleQueryConferenceList(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   if (connId.authToken.requestedResources.size() == 1)
   {
      dynamic_cast<ConferenceBridgeManagerInterface*>(mConfBridgeMgr)->queryConferenceList(connId.authToken.requestedResources[0], new ConferenceListHandlerProxy(connId, mTransport));
   }
   else
   {
      dynamic_cast<ConferenceBridgeManagerInterface*>(mConfBridgeMgr)->queryConferenceList(new ConferenceListHandlerProxy(connId, mTransport));
   }
   return kSuccess;
}

class ParticipantListHandlerProxy : public CPCAPI2::ConferenceBridge::ParticipantListHandler,
   public CPCAPI2::ConferenceBridge::ConferenceBridgeSyncHandler
{
public:
   ParticipantListHandlerProxy(CPCAPI2::JsonApi::JsonApiRequestInfo connId, CPCAPI2::JsonApi::JsonApiServerSendTransport* transport)
      : mConnId(connId), mTransport(transport)
   {
   }
   virtual ~ParticipantListHandlerProxy() {}
   virtual int onParticipantListState(ConferenceHandle conference, const ParticipantListState& args) OVERRIDE
   {
      // JsonFunctionCall(mTransport, "onParticipantListState", JSON_VALUE(conference), "participantsArray", args.participants, "addedParticipantsArray", args.addedParticipants,
      //    "updatedParticipantsArray", args.updatedParticipants, "removedParticipantsArray", args.removedParticipants);

      {
         CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, true, JSON_MODULE, "onParticipantListState");
         serializer.addValue("conference", conference);
         serializer.addValue("participantsArray", args.participants);
         serializer.addValue("addedParticipantsArray", args.addedParticipants);
         serializer.addValue("updatedParticipantsArray", args.updatedParticipants);
         serializer.addValue("removedParticipantsArray", args.removedParticipants);
         serializer.finalize();

         dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInternal*>(mTransport)->send(mConnId, json);
      }
      delete this;
      return kSuccess;
   }
private:
   CPCAPI2::JsonApi::JsonApiRequestInfo mConnId;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;
};

int ConferenceBridgeJsonServerInterface::handleQueryParticipantList(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceHandle conference = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conference));
   mConfBridgeMgr->queryParticipantList(conference, new ParticipantListHandlerProxy(connId, mTransport));
   return kSuccess;
}

class CreateWebParticipantHandlerProxy : public CPCAPI2::ConferenceBridge::CreateWebParticipantHandler,
   public CPCAPI2::ConferenceBridge::ConferenceBridgeSyncHandler
{
public:
   CreateWebParticipantHandlerProxy(CPCAPI2::JsonApi::JsonApiRequestInfo connId, CPCAPI2::JsonApi::JsonApiServerSendTransport* transport)
      : mConnId(connId), mTransport(transport)
   {
   }
   virtual ~CreateWebParticipantHandlerProxy() {}
   virtual int onWebParticipantCreated(ConferenceHandle conference, const WebParticipantCreatedEvent& args) OVERRIDE
   {
      //    JsonFunctionCall(mTransport, "onPeerConnectionAnswer", JSON_VALUE(conference), "participant", args.participant, "sdpAnswer", args.sdpAnswer.sdpString);
      {
         CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, true, JSON_MODULE, "onWebParticipantCreated");
         serializer.addValue("conference", conference);
         serializer.addValue("participant", args.participant);
         serializer.addValue("success", args.success);
         serializer.addValue("hasFloor", args.hasFloor);
         serializer.finalize();

         dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInternal*>(mTransport)->send(mConnId, json);
      }
      delete this;
      return kSuccess;
   }
private:
   CPCAPI2::JsonApi::JsonApiRequestInfo mConnId;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;
};

bool isContextOwnerFromConnId(const CPCAPI2::JsonApi::JsonApiRequestInfo& connId)
{
   bool isContextOwner = false;
   std::string contextStr = connId.authToken.cp_user.c_str();
   if (connId.websocketUrlResource.size() > 1) // might be empty or '/'
   {
      contextStr = connId.websocketUrlResource.c_str();
      if (contextStr.at(0) == '/')
      {
         contextStr = contextStr.substr(1);
      }
      size_t trailingSlash = contextStr.find('/');
      if (trailingSlash != std::string::npos)
      {
         contextStr = contextStr.substr(0, trailingSlash);
      }
   }

   if (contextStr.compare(CPCAPI2::JsonApi::JsonApiServerInterface::doEncrypt(connId.authToken.cp_user).c_str()) == 0)
   {
      isContextOwner = true;
   }
   return isContextOwner;
}

int ConferenceBridgeJsonServerInterface::handleCreateWebParticipant(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceHandle conference = 0;
   ConferenceParticipantHandle participant = 0;
   WebParticipantIdentity identityInfo;
   bool addToFloor = false;

   JsonDeserialize(functionObjectVal, JSON_VALUE(conference), JSON_VALUE(participant), JSON_VALUE(identityInfo), JSON_VALUE(addToFloor));

   bool isContextOwner = isContextOwnerFromConnId(connId);

   if (identityInfo.displayName.empty())
   {
      identityInfo.displayName = connId.authToken.cp_user;
   }

   dynamic_cast<ConferenceBridgeManagerInterface*>(mConfBridgeMgr)->createWebParticipant(participant, conference, isContextOwner, identityInfo, addToFloor, new CreateWebParticipantHandlerProxy(connId, mTransport));
   mMapConnToWebPart[connId].push_back(participant);
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::handleDestroyWebParticipant(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceParticipantHandle webParticipant = 0;

   JsonDeserialize(functionObjectVal, JSON_VALUE(webParticipant));

   DebugLog(<< "handleDestroyWebParticpant: webParticipant: " << webParticipant);

   dynamic_cast<ConferenceBridgeManagerInterface*>(mConfBridgeMgr)->destroyWebParticipant(webParticipant);

   MapConnToWebParticipants::iterator it = mMapConnToWebPart.find(connId);
   if (it != mMapConnToWebPart.end())
   {
      std::vector<ConferenceParticipantHandle>::iterator itPart = it->second.begin();
      for (; itPart != it->second.end(); ++itPart)
      {
         if (*itPart == webParticipant)
         {
            it->second.erase(itPart);
            break;
         }
      }
      if (it->second.size() == 0)
      {
         mMapConnToWebPart.erase(connId);
      }
   }
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::handleSetWebParticipantIdentity(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceParticipantHandle webParticipant = 0;
   CPCAPI2::ConferenceBridge::WebParticipantIdentity wpi;
   cpc::string addressReq;
   cpc::string displayNameReq;
   JsonDeserialize(functionObjectVal, JSON_VALUE(webParticipant), "address", addressReq, "displayName", displayNameReq, "owner", wpi.owner);

   if (addressReq.empty())
   {
      wpi.address = connId.authToken.cp_user;
   }
   else
   {
      wpi.address = addressReq;
   }
   if (displayNameReq.empty())
   {
      wpi.displayName = connId.authToken.cp_user;
   }
   else
   {
      wpi.displayName = displayNameReq;
   }

   bool isContextOwner = isContextOwnerFromConnId(connId);

   dynamic_cast<ConferenceBridgeManagerInterface*>(mConfBridgeMgr)->setWebParticipantIdentity(webParticipant, wpi, isContextOwner);
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::handleSetParticipantPermissions(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceParticipantHandle participant = 0;
   CPCAPI2::ConferenceBridge::ParticipantPermissions permissions;

   cpc::string displayNameReq;
   JsonDeserialize(functionObjectVal, JSON_VALUE(participant), JSON_VALUE(permissions));

   mConfBridgeMgr->setParticipantPermissions(participant, permissions);
   return kSuccess;
}

class PeerConnectionAnswerHandlerProxy : public CPCAPI2::ConferenceBridge::PeerConnectionAnswerHandler,
                                         public CPCAPI2::ConferenceBridge::ConferenceBridgeSyncHandler
{
public:
   PeerConnectionAnswerHandlerProxy(CPCAPI2::JsonApi::JsonApiRequestInfo connId, CPCAPI2::JsonApi::JsonApiServerSendTransport* transport)
      : mConnId(connId), mTransport(transport)
   {
   }
   virtual ~PeerConnectionAnswerHandlerProxy() {}
   virtual int onPeerConnectionAnswer(ConferenceHandle conference, const PeerConnectionAnswerEvent& args) OVERRIDE
   {
      //    JsonFunctionCall(mTransport, "onPeerConnectionAnswer", JSON_VALUE(conference), "participant", args.participant, "sdpAnswer", args.sdpAnswer.sdpString);
      {
         CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, true, JSON_MODULE, "onPeerConnectionAnswer");
         serializer.addValue("conference", conference);
         serializer.addValue("participant", args.participant);
         serializer.addValue("sdpAnswer", args.sdpAnswer.sdpString);
         serializer.finalize();

         dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInternal*>(mTransport)->send(mConnId, json);
      }
      delete this;
      return kSuccess;
   }
private:
   CPCAPI2::JsonApi::JsonApiRequestInfo mConnId;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;
};

int ConferenceBridgeJsonServerInterface::handleSendPeerConnectionOffer(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceParticipantHandle webParticipant = 0;
   cpc::string sdpStr;

   JsonDeserialize(functionObjectVal, JSON_VALUE(webParticipant), "sdpOffer", sdpStr);

   CPCAPI2::PeerConnection::SessionDescription sdp;
   sdp.sdpString = sdpStr;
   sdp.sdpLen = sdpStr.size();
   sdp.sdpType = CPCAPI2::PeerConnection::SessionDescription::SessionDescriptionType_Offer;
   mConfBridgeMgr->sendPeerConnectionOffer(webParticipant, sdp, new PeerConnectionAnswerHandlerProxy(connId, mTransport));
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::handleSendPeerConnectionAnswer(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceParticipantHandle webParticipant = 0;
   cpc::string sdpStr;

   JsonDeserialize(functionObjectVal, JSON_VALUE(webParticipant), "sdpOffer", sdpStr);

   CPCAPI2::PeerConnection::SessionDescription sdp;
   sdp.sdpString = sdpStr;
   sdp.sdpLen = sdpStr.size();
   sdp.sdpType = CPCAPI2::PeerConnection::SessionDescription::SessionDescriptionType_Answer;
   mConfBridgeMgr->sendPeerConnectionAnswer(webParticipant, sdp);
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::handleTakeParticipantSnapshot(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceParticipantHandle participant = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(participant));
   mConfBridgeMgr->takeParticipantSnapshot(participant);
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::handleSetParticipantPhoto(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceParticipantHandle participant = 0;
   cpc::string photoUri;

   JsonDeserialize(functionObjectVal, JSON_VALUE(participant), JSON_VALUE(photoUri));

   if (photoUri.find("blob") != cpc::string::npos)
   {
      resip::Data tempFileName = resip::Random::getCryptoRandom(4).base64encode(true).substr(0, 4).uppercase() + ".png";
      std::ofstream outstr(tempFileName.c_str(), std::ios_base::out | std::ios_base::binary | std::ios_base::trunc);
      if (outstr.is_open())
      {
         outstr << *binaryData << std::flush;
         outstr.close();
         photoUri = tempFileName.c_str();
      }
   }
   mConfBridgeMgr->setParticipantPhoto(participant, photoUri);
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::handleAddAssociatedConference(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceHandle conference = 0;
   ConferenceHandle associatedConference = 0;

   JsonDeserialize(functionObjectVal, JSON_VALUE(conference), JSON_VALUE(associatedConference));

   mConfBridgeMgr->addAssociatedConference(conference, associatedConference);
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::handleAddToFloor(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceParticipantHandle participant = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(participant));
   mConfBridgeMgr->addToFloor(participant);
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::handleRemoveFromFloor(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceParticipantHandle participant = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(participant));
   mConfBridgeMgr->removeFromFloor(participant);
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::handleRequestFloor(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceParticipantHandle participant = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(participant));
   mConfBridgeMgr->requestFloor(participant);
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::handleSetStreamingEnabled(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceHandle conference = 0;
   bool enabled = false;

   JsonDeserialize(functionObjectVal, JSON_VALUE(conference), JSON_VALUE(enabled));

   mConfBridgeMgr->setStreamingEnabled(conference, enabled);
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::handleSetRecordingEnabled(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceHandle conference = 0;
   bool enabled = false;

   JsonDeserialize(functionObjectVal, JSON_VALUE(conference), JSON_VALUE(enabled));

   mConfBridgeMgr->setRecordingEnabled(conference, enabled);
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::handleSetTranscriptionEnabled(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData)
{
   ConferenceHandle conference = 0;
   bool enabled = false;

   JsonDeserialize(functionObjectVal, JSON_VALUE(conference), JSON_VALUE(enabled));

   mConfBridgeMgr->setTranscriptionEnabled(conference, enabled);
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::onConferenceDetails(ConferenceHandle conference, const ConferenceDetailsResult& args)
{
   JsonFunctionCall(mTransport, "onConferenceDetails", JSON_VALUE(conference), "conferenceToken", args.conferenceToken, "conferenceInfo", args.conferenceInfo, "persistent", args.persistent);
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::onConferenceEnded(ConferenceHandle conference, const ConferenceEndedEvent& args)
{
   JsonFunctionCall(mTransport, "onConferenceEnded", JSON_VALUE(conference));
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::onConferenceList(const ConferenceListResult& args)
{
   JsonFunctionCall(mTransport, "onConferenceList", "conferences", args.conferences);
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::onConferenceNotFound(ConferenceHandle conference, const ConferenceNotFoundResult& args)
{
   JsonFunctionCall(mTransport, "onConferenceNotFound", JSON_VALUE(conference));
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::onParticipantListState(ConferenceHandle conference, const ParticipantListState& args)
{
   JsonFunctionCall(mTransport, "onParticipantListState", JSON_VALUE(conference), "isPartialUpdate", args.isPartialUpdate, "isLastChunk", args.isLastChunk, "participantsArray", args.participants, "addedParticipantsArray", args.addedParticipants,
      "updatedParticipantsArray", args.updatedParticipants, "removedParticipantsArray", args.removedParticipants);
   return kSuccess;
}

int ConferenceBridgeJsonServerInterface::onConferenceTranscriptionResult(ConferenceHandle conference, const ConferenceTranscriptionEvent& args)
{
   JsonFunctionCall(mTransport, "onConferenceNotFound", JSON_VALUE(conference), "participant", args.participant, "transcriptionResult", args.transcriptionResult, "resultSetId", args.resultSetId);
   return kSuccess;
}



}
}
#endif
