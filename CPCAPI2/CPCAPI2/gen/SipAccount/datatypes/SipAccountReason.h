#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTREASON_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTREASON_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  enum class SipAccountReason
  {
    None,
    NoNetwork,
    RestrictedNetwork,
    NewNetwork,
    ServerResponse,
    LocalTimeout,
    NetworkDeregistered,
    TunnelFailure,
    DnsLookup,
    TransportProtocolMismatch,
    NoRouteToHost,
    TlsCipherMismatch,
    DomainLocked
  };

  inline std::string to_string(SipAccountReason value)
  {
    switch (value)
    {
      case SipAccountReason::None:
        return "None";
      case SipAccountReason::NoNetwork:
        return "NoNetwork";
      case SipAccountReason::RestrictedNetwork:
        return "RestrictedNetwork";
      case SipAccountReason::NewNetwork:
        return "NewNetwork";
      case SipAccountReason::ServerResponse:
        return "ServerResponse";
      case SipAccountReason::LocalTimeout:
        return "LocalTimeout";
      case SipAccountReason::NetworkDeregistered:
        return "NetworkDeregistered";
      case SipAccountReason::TunnelFailure:
        return "TunnelFailure";
      case SipAccountReason::DnsLookup:
        return "DnsLookup";
      case SipAccountReason::TransportProtocolMismatch:
        return "TransportProtocolMismatch";
      case SipAccountReason::NoRouteToHost:
        return "NoRouteToHost";
      case SipAccountReason::TlsCipherMismatch:
        return "TLSCipherMismatch";
      case SipAccountReason::DomainLocked:
        return "DomainLocked";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, SipAccountReason value)
  {
    switch (value)
    {
      case SipAccountReason::None:
        os << "None";
        break;
      case SipAccountReason::NoNetwork:
        os << "NoNetwork";
        break;
      case SipAccountReason::RestrictedNetwork:
        os << "RestrictedNetwork";
        break;
      case SipAccountReason::NewNetwork:
        os << "NewNetwork";
        break;
      case SipAccountReason::ServerResponse:
        os << "ServerResponse";
        break;
      case SipAccountReason::LocalTimeout:
        os << "LocalTimeout";
        break;
      case SipAccountReason::NetworkDeregistered:
        os << "NetworkDeregistered";
        break;
      case SipAccountReason::TunnelFailure:
        os << "TunnelFailure";
        break;
      case SipAccountReason::DnsLookup:
        os << "DnsLookup";
        break;
      case SipAccountReason::TransportProtocolMismatch:
        os << "TransportProtocolMismatch";
        break;
      case SipAccountReason::NoRouteToHost:
        os << "NoRouteToHost";
        break;
      case SipAccountReason::TlsCipherMismatch:
        os << "TLSCipherMismatch";
        break;
      case SipAccountReason::DomainLocked:
        os << "DomainLocked";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const SipAccountReason& e)
  {
    switch (e)
    {
      case SipAccountReason::None:
        j = "None";
        break;
      case SipAccountReason::NoNetwork:
        j = "NoNetwork";
        break;
      case SipAccountReason::RestrictedNetwork:
        j = "RestrictedNetwork";
        break;
      case SipAccountReason::NewNetwork:
        j = "NewNetwork";
        break;
      case SipAccountReason::ServerResponse:
        j = "ServerResponse";
        break;
      case SipAccountReason::LocalTimeout:
        j = "LocalTimeout";
        break;
      case SipAccountReason::NetworkDeregistered:
        j = "NetworkDeregistered";
        break;
      case SipAccountReason::TunnelFailure:
        j = "TunnelFailure";
        break;
      case SipAccountReason::DnsLookup:
        j = "DnsLookup";
        break;
      case SipAccountReason::TransportProtocolMismatch:
        j = "TransportProtocolMismatch";
        break;
      case SipAccountReason::NoRouteToHost:
        j = "NoRouteToHost";
        break;
      case SipAccountReason::TlsCipherMismatch:
        j = "TLSCipherMismatch";
        break;
      case SipAccountReason::DomainLocked:
        j = "DomainLocked";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, SipAccountReason& e)
  {
      if (0 == j.get<std::string>().compare("None"))
    {
      e = SipAccountReason::None;
    }
    else if (0 == j.get<std::string>().compare("NoNetwork"))
    {
      e = SipAccountReason::NoNetwork;
    }
    else if (0 == j.get<std::string>().compare("RestrictedNetwork"))
    {
      e = SipAccountReason::RestrictedNetwork;
    }
    else if (0 == j.get<std::string>().compare("NewNetwork"))
    {
      e = SipAccountReason::NewNetwork;
    }
    else if (0 == j.get<std::string>().compare("ServerResponse"))
    {
      e = SipAccountReason::ServerResponse;
    }
    else if (0 == j.get<std::string>().compare("LocalTimeout"))
    {
      e = SipAccountReason::LocalTimeout;
    }
    else if (0 == j.get<std::string>().compare("NetworkDeregistered"))
    {
      e = SipAccountReason::NetworkDeregistered;
    }
    else if (0 == j.get<std::string>().compare("TunnelFailure"))
    {
      e = SipAccountReason::TunnelFailure;
    }
    else if (0 == j.get<std::string>().compare("DnsLookup"))
    {
      e = SipAccountReason::DnsLookup;
    }
    else if (0 == j.get<std::string>().compare("TransportProtocolMismatch"))
    {
      e = SipAccountReason::TransportProtocolMismatch;
    }
    else if (0 == j.get<std::string>().compare("NoRouteToHost"))
    {
      e = SipAccountReason::NoRouteToHost;
    }
    else if (0 == j.get<std::string>().compare("TLSCipherMismatch"))
    {
      e = SipAccountReason::TlsCipherMismatch;
    }
    else if (0 == j.get<std::string>().compare("DomainLocked"))
    {
      e = SipAccountReason::DomainLocked;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum SipAccountReason"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTREASON_H
