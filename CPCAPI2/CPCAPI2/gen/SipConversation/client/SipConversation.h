#include "../datatypes/SipConversationSettings.h"
#include "../datatypes/MediaInfo.h"
#include "../datatypes/MediaType.h"
#include "../datatypes/DtmfMode.h"

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
class SipConversation
{
public:
    SipConversation(ITransport& transport)
    {
        this.transport = transport;
    }

    void setDefaultSettings(int64_t phoneHandle, int64_t accountHandle, SipConversationSettings settings);

	int64_t createConversation(int64_t phoneHandle, int64_t accountHandle);

	void addParticipant(int64_t phoneHandle, int64_t conversationHandle, cpc::string targetAddress);

	void configureMedia(int64_t phoneHandle, int64_t conversationHandle, MediaInfo mediaDescriptor);

	void setMediaEnabled(int64_t phoneHandle, int64_t conversationHandle, MediaType mediaType, bool enabled);

	void setAnonymousMode(int64_t phoneHandle, int64_t conversationHandle, int64_t anonymousMode);

	void start(int64_t phoneHandle, int64_t conversationHandle);

	void hold(int64_t phoneHandle, int64_t conversationHandle);

	void unhold(int64_t phoneHandle, int64_t conversationHandle);

	void sendMediaChangeRequest(int64_t phoneHandle, int64_t conversationHandle);

	void end(int64_t phoneHandle, int64_t conversationHandle);

	void redirect(int64_t phoneHandle, int64_t conversationHandle, cpc::string targetAddress, cpc::string reason);

	void sendRingingResponse(int64_t phoneHandle, int64_t conversationHandle);

	void reject(int64_t phoneHandle, int64_t conversationHandle, int64_t rejectReason);

	void accept(int64_t phoneHandle, int64_t conversationHandle);

	void acceptIncomingTransferRequest(int64_t phoneHandle, int64_t conversationHandle);

	void rejectIncomingTransferRequest(int64_t phoneHandle, int64_t conversationHandle);

	void transfer(int64_t phoneHandle, int64_t transferTargetConversation, int64_t transfereeConversation);

	void transferWithFlag(int64_t phoneHandle, int64_t transferTargetConversation, int64_t transfereeConversation, bool endTargetConversationOnSuccess);

	void transferToAddress(int64_t phoneHandle, int64_t transfereeConversation, cpc::string targetAddress);

	void setDtmfMode(int64_t phoneHandle, int64_t account, int64_t ordinal, DtmfMode dtmfMode);

	void startDtmfTone(int64_t phoneHandle, int64_t conversationHandle, int64_t toneId, bool playLocally);

	void stopDtmfTone(int64_t phoneHandle);

	void playSound(int64_t phoneHandle, int64_t conversationHandle, cpc::string resourceUri, bool repeat);

	void stopPlaySound(int64_t phoneHandle, int64_t conversationHandle);

	int64_t getCallCount(int64_t phoneHandle);

	void setCallKitMode(int64_t phoneHandle, int64_t conversationHandle);

	void setTelecomFrameworkMode(int64_t phoneHandle, int64_t conversationHandle);

private:
    ITransport* transport;
}; // class SipConversation
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
