#include <assert.h>
#include <sstream>
#include <string.h>
#include <brand_branded.h>

#include "RegistryUtils.h"
#include <utils/msrp_tree.h>
#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::UEM

#define UEM_LOG_ENABLED                                  0
#if (UEM_LOG_ENABLED == 1)
#define UemLog(args) InfoLog(args)
#else
#define UemLog(args)
#endif

using namespace CPCAPI2::Analytics;

#define ATTRIBUTE_PREFIX "_"
#define LIST_SUFFIX "_list"

static bool parseListItem( const char *listRoot, std::string& outItemName );

// TODO: I'm not sure we need the length encoded in the string here.
bool RegistryUtils::getString( msrp_tree* registry, const char *path, std::string& outValue )
{
   assert( registry != NULL && path != NULL );
   if( registry == NULL || path == NULL )
      return false;

   RegistryNode *node = ( RegistryNode * ) msrp_tree_get( registry, path );
   if( node == NULL )
      return false;

   assert( node->type == RegistryNodeType_string );
   if( node->type != RegistryNodeType_string )
      return false;

   // Try to read off the size of the string
   assert( node->bytes.size() >= sizeof( size_t ));
   if( node->bytes.size() < sizeof( size_t ))
      return false;

   size_t *length = ( size_t * )&( node->bytes[ 0 ] );
   if( length == NULL )
      return false;

   // Validate the size
   assert( node->bytes.size() >= ( sizeof( size_t ) + *length + 1 ));
   char *data = ( char * )&( node->bytes[ sizeof( size_t ) ] ); // NULL terminated
   outValue = data;
   return true;
}

bool RegistryUtils::setString( msrp_tree* registry, const char *path, const std::string& value )
{
   assert( registry != NULL && path != NULL );
   if( registry == NULL || path == NULL )
      return false;

   // Don't bother setting empty strings as values
   if( value.size() == 0 )
      return false;

   RegistryNode *newNode = new RegistryNode;
   if( newNode == NULL )
      return false;

   // Create a new node and set the value. For strings, the length will be
   // encoded as the first bytes in size_t form.
   newNode->type = RegistryNodeType_string;
   newNode->bytes.resize( value.size() + 1 + sizeof( size_t )); // ensure capacity

   // Set the pointers to the right locations
   size_t *length = ( size_t * )&( newNode->bytes[ 0 ] );
   char *data     = ( char * )&( newNode->bytes[ sizeof( size_t ) ] );

   // Copy the data into position
   *length = value.size();
   strncpy( data, value.c_str(), value.size() );
   data[ *length ] = '\0'; // ensure NULL terminated

   // Insert the new node at the path location
   RegistryNode *oldNode = NULL;
   if( msrp_tree_insert( registry, path, ( void * ) newNode, ( void ** ) &oldNode, MSRP_TRUE ) != MSRP_TRUE )
   {
      delete newNode; // fail to insert
      return false;
   }

   // Release any old data that might have been there.
   delete oldNode;
   return true;
}

bool RegistryUtils::getBool( msrp_tree* registry, const char *path, bool& outValue )
{
   assert( registry != NULL && path != NULL );
   if( registry == NULL || path == NULL )
      return false;

   RegistryNode *node = ( RegistryNode * ) msrp_tree_get( registry, path );
   if( node == NULL )
      return false;

   assert( node->type == RegistryNodeType_bool );
   if( node->type != RegistryNodeType_bool )
      return false;

   assert( node->bytes.size() >= sizeof( bool ));
   if( node->bytes.size() < sizeof( bool ))
      return false;

   // Take the address of the first byte in the array, cast it to a pointer
   // to a bool. Dereference this to get our content.
   bool *result = ( bool* )&( node->bytes[ 0 ] );
   outValue = *result;
   return true;
}

bool RegistryUtils::setBool( msrp_tree* registry, const char *path, const bool& value )
{
   assert( registry != NULL && path != NULL );
   if( registry == NULL || path == NULL )
      return false;

   RegistryNode *newNode = new RegistryNode;
   if( newNode == NULL )
      return false;

   // Create a new node and set the value
   newNode->type = RegistryNodeType_bool;
   newNode->bytes.resize( sizeof( bool ));
   bool *store = ( bool * ) &(newNode->bytes[ 0 ]);
   *store = value;

   // Insert the new node at the path location
   RegistryNode *oldNode = NULL;
   if( msrp_tree_insert( registry, path, ( void * ) newNode, ( void ** ) &oldNode, MSRP_TRUE ) != MSRP_TRUE )
   {
      delete newNode; // fail to insert
      return false;
   }

   // Release any old data that might have been there.
   delete oldNode;
   return true;
}

bool RegistryUtils::getInt( msrp_tree* registry, const char *path, int& outValue )
{
   assert( registry != NULL && path != NULL );
   if( registry == NULL || path == NULL )
      return false;

   RegistryNode *node = ( RegistryNode * ) msrp_tree_get( registry, path );
   if( node == NULL )
      return false;

   assert( node->type == RegistryNodeType_int );
   if( node->type != RegistryNodeType_int )
      return false;

   assert( node->bytes.size() >= sizeof( int ));
   if( node->bytes.size() < sizeof( int ))
      return false;

   // Take the address of the first byte in the array, cast it to a pointer
   // to an int. Dereference this to get our content.
   int *result = ( int* )&( node->bytes[ 0 ] );
   outValue = *result;
   return true;
}

bool RegistryUtils::setInt( msrp_tree* registry, const char *path, const int& value )
{
   assert( registry != NULL && path != NULL );
   if( registry == NULL || path == NULL )
      return false;

   RegistryNode *newNode = new RegistryNode;
   if( newNode == NULL )
      return false;

   // Create a new node and set the value
   newNode->type = RegistryNodeType_int;
   newNode->bytes.resize( sizeof( int ));
   int *store = ( int * ) &(newNode->bytes[ 0 ]);
   *store = value;

   // Insert the new node at the path location
   RegistryNode *oldNode = NULL;
   if( msrp_tree_insert( registry, path, ( void * ) newNode, ( void ** ) &oldNode, MSRP_TRUE ) != MSRP_TRUE )
   {
      delete newNode; // fail to insert
      return false;
   }

   // Release any old data that might have been there.
   delete oldNode;
   return true;
}

bool RegistryUtils::getInt64( msrp_tree* registry, const char *path, int64_t& outValue )
{
   assert( registry != NULL && path != NULL );
   if( registry == NULL || path == NULL )
      return false;

   RegistryNode *node = ( RegistryNode * ) msrp_tree_get( registry, path );
   if( node == NULL )
      return false;

   //assert( node->type == RegistryNodeType_int64 );
   if( node->type != RegistryNodeType_int64 )
      return false;

   //assert( node->bytes.size() >= sizeof( int64_t ));
   if( node->bytes.size() < sizeof( int64_t ))
      return false;

   // Take the address of the first byte in the array, cast it to a pointer
   // to an int. Dereference this to get our content.
   int64_t *result = ( int64_t* )&( node->bytes[ 0 ] );
   outValue = *result;
   return true;
}

bool RegistryUtils::setInt64( msrp_tree* registry, const char *path, const int64_t& value )
{
   assert( registry != NULL && path != NULL );
   if( registry == NULL || path == NULL )
      return false;

   RegistryNode *newNode = new RegistryNode;
   if( newNode == NULL )
      return false;

   // Create a new node and set the value
   newNode->type = RegistryNodeType_int64;
   newNode->bytes.resize( sizeof( int64_t ));
   int64_t *store = ( int64_t * ) &(newNode->bytes[ 0 ]);
   *store = value;

   // Insert the new node at the path location
   RegistryNode *oldNode = NULL;
   if( msrp_tree_insert( registry, path, ( void * ) newNode, ( void ** ) &oldNode, MSRP_TRUE ) != MSRP_TRUE )
   {
      delete newNode; // fail to insert
      return false;
   }

   // Release any old data that might have been there.
   delete oldNode;
   return true;
}

bool RegistryUtils::getDouble( msrp_tree* registry, const char *path, double& outValue )
{
   assert( registry != NULL && path != NULL );
   if( registry == NULL || path == NULL )
      return false;

   RegistryNode *node = ( RegistryNode * ) msrp_tree_get( registry, path );
   if( node == NULL )
      return false;

   assert( node->type == RegistryNodeType_double );
   if( node->type != RegistryNodeType_double )
      return false;

   assert( node->bytes.size() >= sizeof( double ));
   if( node->bytes.size() < sizeof( double ))
      return false;

   // Take the address of the first byte in the array, cast it to a pointer
   // to a double. Dereference this to get our content.
   double *result = ( double* )&( node->bytes[ 0 ] );
   outValue = *result;
   return true;
}

bool RegistryUtils::setDouble( msrp_tree* registry, const char *path, const double& value )
{
   assert( registry != NULL && path != NULL );
   if( registry == NULL || path == NULL )
      return false;

   RegistryNode *newNode = new RegistryNode;
   if( newNode == NULL )
      return false;

   // Create a new node and set the value
   newNode->type = RegistryNodeType_double;
   newNode->bytes.resize( sizeof( double ));
   double *store = ( double * ) &(newNode->bytes[ 0 ]);
   *store = value;

   // Insert the new node at the path location
   RegistryNode *oldNode = NULL;
   if( msrp_tree_insert( registry, path, ( void * ) newNode, ( void ** ) &oldNode, MSRP_TRUE ) != MSRP_TRUE )
   {
      delete newNode; // fail to insert
      return false;
   }

   // Release any old data that might have been there.
   delete oldNode;
   return true;
}

bool RegistryUtils::hasData( msrp_tree* registry, const char *path )
{
   assert( registry != NULL && path != NULL );
   if( registry == NULL || path == NULL )
      return false;

   RegistryNode *node = ( RegistryNode * ) msrp_tree_get( registry, path );
   return( node != NULL );
}

bool RegistryUtils::exists( msrp_tree *registry, const char *path )
{
   assert( registry != NULL && path != NULL );
   if( registry == NULL || path == NULL )
      return false;

   return ( msrp_tree_path_exists( registry, path ) == MSRP_TRUE );
}

bool RegistryUtils::erase( msrp_tree *registry, const char *path )
{
   msrp_tree_enum_t *enumm = NULL;
   RegistryNode *curNode = NULL;

   assert( registry != NULL && path != NULL );
   if( registry == NULL || path == NULL )
      return false;

   // First clear all the user data below (and including) this point
   bool found = false;
   enumm = msrp_tree_enum_create_path( registry, path );
   while( msrp_tree_enum_next( enumm, ( void ** ) &curNode ) == MSRP_TRUE )
   {
      found = true;
      delete curNode;
   }
   msrp_tree_enum_destroy( enumm );

   // Clear everything below and including the enumeration point
   msrp_tree_destroy_path( registry, path );

   if (found)
   {
      std::string parentPath(path);
      parentPath = parentPath.substr(0, parentPath.find_last_of(SEP));

      RegistryNode *node = (RegistryNode*)msrp_tree_get(registry, parentPath.c_str());
      if (node && node->type == RegistryNodeType_list)
      {
         assert(node->bytes.size() >= sizeof(size_t));
         if (node->bytes.size() < sizeof(size_t))
         {
            UemLog(<< "RegistryUtils::getListSize(): mismatch in node bytes for registry: " << registry << " list-root: " << listRoot);
            return 0;
         }

         // Take the address of the first byte in the array, cast it to a pointer
         // to a double. Dereference this to get our list size. Decrement it.
         size_t listSize = --(*(size_t*)&(node->bytes[0]));

         int iRemoved = atoi(std::string(path).substr(parentPath.length()+1).c_str());
         // indices are 0-based and the above returns 0 on error so let's check the result matches the original
         assert((parentPath + SEP + std::to_string(iRemoved)).compare(path) == 0);

         for (int i = iRemoved+1; i <= listSize; i++)
         {
            // rename node i as node i-1 to move things down to take up the space of the removed index
            std::string srcPath = parentPath + SEP + std::to_string(i);
            if (!msrp_tree_rename(registry, srcPath.c_str(), std::to_string(i-1).c_str()))
            {
               UemLog(<< "Can't find node to rename -1 with path: " << srcPath << " registry: " << registry << " list-root: " << listRoot);
            }
         }
      }
   }

   return true;
}

// Return the list item name by parsing the last part of the list root
static bool parseListItem( const char *listRoot, std::string& outItemName )
{
   if( listRoot == NULL )
      return false;

   const char *item_end = strstr( listRoot, LIST_SUFFIX );
   if( item_end == NULL )
      return false;

   // count backward from item_end until we hit a "SEP"
   const char *cur = item_end;
   while( cur != listRoot )
   {
      if( strncmp( cur, SEP, sizeof( SEP ) - 1 ) == 0 )
         break;

      --cur;
   }

   // There is an edge case where the first character could also be
   // the separator. One more comparison.
   if( strncmp( cur, SEP, sizeof( SEP ) - 1 ) == 0 )
   {
      cur += 1; // skip over SEP

      // Assign all the chars from cur to item_end into outItemName
      outItemName.assign( cur, item_end - cur );
      return true;
   }

   // Nothing found
   return false;
}

bool RegistryUtils::listExists(msrp_tree* registry, const char* parentPath, const char* itemName, std::string& outListRoot)
{
   // [parent_path]/[item_name]_list/[item_index]/[item_name]
   assert(registry != NULL && parentPath != NULL && itemName != NULL);
   if (registry == NULL || parentPath == NULL || itemName == NULL)
      return false;

   std::string listRoot(parentPath);
   listRoot += SEP;
   listRoot += itemName;
   listRoot += LIST_SUFFIX;

   // Test for existence
   if (!exists(registry, listRoot.c_str()))
      return false;

   // Make sure it's a list
   RegistryNode *node = (RegistryNode*)msrp_tree_get(registry, listRoot.c_str());
   if (node == NULL)
      return false;

   if (node->type != RegistryNodeType_list)
      return false;

   outListRoot = listRoot;
   return true;
}

bool RegistryUtils::createList(msrp_tree* registry, const char* parentPath, const char* itemName, std::string& outListRoot)
{
   // [parent_path]/[item_name]_list/[item_index]/[item_name]
   assert(registry != NULL && parentPath != NULL && itemName != NULL);
   if (registry == NULL || parentPath == NULL || itemName == NULL)
   {
      UemLog(<< "RegistryUtils::createList(): null arguments: registry: " << registry << " path: " << parentPath << " item: " << itemName);
      return false;
   }

   RegistryNode* newNode = new RegistryNode;
   if (newNode == NULL)
   {
      UemLog(<< "RegistryUtils::createList(): error creating new registry-node for path: " << parentPath << " item: " << itemName);
      return false;
   }

   // Set the node type to list and initialize the size to zero
   newNode->type = RegistryNodeType_list;
   newNode->bytes.resize(sizeof(size_t));
   size_t* store = (size_t*) &(newNode->bytes[0]);
   *store = 0;

   std::string listRoot(parentPath);
   listRoot += SEP;
   listRoot += itemName;
   listRoot += LIST_SUFFIX;

   // Insert the new node at the path location
   RegistryNode* oldNode = NULL;
   if (msrp_tree_insert(registry, listRoot.c_str(), (void*)newNode, (void**)&oldNode, MSRP_TRUE) != MSRP_TRUE)
   {
      UemLog(<< "RegistryUtils::createList(): failed to insert node for path: " << parentPath << " item: " << itemName);
      delete newNode; // fail to insert
      return false;
   }

   // Release any old data that might have been there.
   delete oldNode;
   outListRoot = listRoot;
   return true;
}

size_t RegistryUtils::getListSize(msrp_tree* registry, const char* listRoot)
{
   // [parent_path]/[item_name]_list/[item_index]/[item_name]
   assert(registry != NULL && listRoot != NULL);
   if (registry == NULL || listRoot == NULL)
   {
      UemLog(<< "RegistryUtils::getListSize(): null arguments: registry: " << registry << " list-root: " << listRoot);
      return 0;
   }

   RegistryNode* node = (RegistryNode*)msrp_tree_get(registry, listRoot);
   if (node == NULL)
   {
      UemLog(<< "RegistryUtils::getListSize(): error retrieving registry-node for registry: " << registry << " list-root: " << listRoot);
      return 0;
   }

   assert(node->type == RegistryNodeType_list);
   if (node->type != RegistryNodeType_list)
   {
      UemLog(<< "RegistryUtils::getListSize(): node-type is not a list for registry: " << registry << " list-root: " << listRoot);
      return 0;
   }

   assert(node->bytes.size() >= sizeof(size_t));
   if (node->bytes.size() < sizeof(size_t))
   {
      UemLog(<< "RegistryUtils::getListSize(): mismatch in node bytes for registry: " << registry << " list-root: " << listRoot);
      return 0;
   }

   // Take the address of the first byte in the array, cast it to a pointer
   // to a double. Dereference this to get our content.
   size_t* result = (size_t*)&(node->bytes[0]);
   return *result;
}

bool RegistryUtils::addListItem(msrp_tree* registry, const char* listRoot, std::string& outItemPath)
{
   // [parent_path]/[item_name]_list/[item_index]/[item_name]
   assert(registry != NULL && listRoot != NULL);
   if (registry == NULL || listRoot == NULL)
   {
      UemLog(<< "RegistryUtils::addListItem(): null arguments: registry: " << registry << " list-root: " << listRoot);
      return false;
   }

   std::string itemName;
   if (!parseListItem(listRoot, itemName))
   {
      UemLog(<< "RegistryUtils::addListItem(): error parsing list-root for registry: " << registry << " list-root: " << listRoot);
      return false;
   }

   RegistryNode* node = (RegistryNode*)msrp_tree_get(registry, listRoot);
   if (node == NULL)
   {
      UemLog(<< "RegistryUtils::addListItem(): error retrieving registry-node for registry: " << registry << " list-root: " << listRoot);
      return false;
   }

   assert(node->type == RegistryNodeType_list);
   if (node->type != RegistryNodeType_list)
   {
      UemLog(<< "RegistryUtils::addListItem(): node-type is not a list for registry: " << registry << " list-root: " << listRoot);
      return false;
   }

   assert(node->bytes.size() >= sizeof(size_t));
   if (node->bytes.size() < sizeof(size_t))
   {
      UemLog(<< "RegistryUtils::addListItem(): mismatch in node bytes for registry: " << registry << " list-root: " << listRoot);
      return false;
   }

   // Get the current size
   size_t* size = (size_t*)&(node->bytes[0]);

   // construct the new item path
   std::string itemPath(listRoot);
   itemPath += SEP;

   // index == size before incrementing
   std::ostringstream strs;
   strs << *size;
   itemPath += strs.str();

   itemPath += SEP;
   itemPath += itemName;

   // Increment size by one
   *size += 1;

   outItemPath = itemPath;
   return true;
}

bool RegistryUtils::getListItem(msrp_tree* registry, const char* listRoot, const unsigned int& index, std::string& outItemPath)
{
   // [parent_path]/[item_name]_list/[item_index]/[item_name]
   assert(registry != NULL && listRoot != NULL);
   if (registry == NULL || listRoot == NULL)
   {
      UemLog(<< "RegistryUtils::getListItem(): null arguments: registry: " << registry << " list-root: " << listRoot);
      return false;
   }

   // Make sure the size includes the index
   RegistryNode* node = (RegistryNode*)msrp_tree_get(registry, listRoot);
   if (node == NULL)
   {
      UemLog(<< "RegistryUtils::getListItem(): error retrieving register-node for registry: " << registry << " list-root: " << listRoot << " index: " << index);
      return false;
   }

   assert(node->type == RegistryNodeType_list);
   if (node->type != RegistryNodeType_list)
   {
      UemLog(<< "RegistryUtils::getListItem(): node-type is not a list for registry: " << registry << " list-root: " << listRoot << " index: " << index);
      return false;
   }

   assert(node->bytes.size() >= sizeof(size_t));
   if (node->bytes.size() < sizeof(size_t))
   {
      UemLog(<< "RegistryUtils::getListItem(): mismatch in node bytes for registry: " << registry << " list-root: " << listRoot << " index: " << index);
      return false;
   }

   // Test the size
   size_t* size = (size_t*)&(node->bytes[0]);
   if (index >= *size)
   {
      UemLog(<< "RegistryUtils::addListItem(): invalid index: " << index << " for list size: " << *size << " for registry: " << registry << " list-root: " << listRoot);
      return false;
   }

   std::string itemName;
   if (!parseListItem(listRoot, itemName))
   {
      UemLog(<< "RegistryUtils::getListItem(): error parsing list-item for index: " << index << " for registry: " << registry << " list-root: " << listRoot);
      return false;
   }

   std::string itemPath(listRoot);
   itemPath += SEP;

   std::ostringstream strs;
   strs << index;
   itemPath += strs.str();

   itemPath += SEP;
   itemPath += itemName;

   outItemPath = itemPath;
   return true;
}

bool RegistryUtils::getAttribute( msrp_tree* registry, const char* path, const char *attrName, std::string& outAttrValue )
{
   assert( registry != NULL && path != NULL && attrName != NULL );
   if( registry == NULL || path == NULL || attrName == NULL )
      return false;

   std::string attributePath( path );
   attributePath += SEP;
   attributePath += ATTRIBUTE_PREFIX;
   attributePath += attrName;

   return getString( registry, attributePath.c_str(), outAttrValue );
}

bool RegistryUtils::setAttribute(msrp_tree* registry, const char* path, const char* attrName, const std::string& attrValue)
{
   assert(registry != NULL && path != NULL && attrName != NULL);
   if (registry == NULL || path == NULL || attrName == NULL)
      return false;

   std::string attributePath(path);
   attributePath += SEP;
   attributePath += ATTRIBUTE_PREFIX;
   attributePath += attrName;

   return setString(registry, attributePath.c_str(), attrValue);
}
