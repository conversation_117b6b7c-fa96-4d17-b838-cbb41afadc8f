#include <string>
#include <stdlib.h>

#include <brand_branded.h>

#if (CPCAPI2_BRAND_BROADSOFT_XSI_MODULE == 1)

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::BROADSOFT_XSI

#include <sstream>

#include <boost/asio.hpp>

#include "util/LogSubsystems.h"
#include <rutil/Logger.hxx>

#include "cpcapi2defs.h"
#include "util/DumFpCommand.h"

#include <broadsoftxsi/BroadsoftXsi.h>
#include <broadsoftxsi/BroadsoftXsiTypes.h>
#include <broadsoftxsi/BroadsoftXsiHandler.h>
#include "BroadsoftXsiTypesInternal.h"
#include "BroadsoftXsiImpl.h"

// Write/parse XML
#include <libxml/xmlreader.h>
#include <libxml/xmlstring.h>
#include "util/libXmlHelper.h"
// Write/parse JSON
//#include <util/DeviceInfo.h>
//#include <document.h>

// This is here just for the percent encoding functions of the URL
#include <utils/msrp_string.h>
#include <utils/msrp_mem.h>

#include <curlpp/cURLpp.hpp>

/* delims and sub-delims according to rfc 3986. NOTE: I've added SPACE
   to this list to reflect common usage. */
#define RESERVED_CHARS ":/?#[]@!$&'()*+,;= "

#define FIRE_ERROR_AND_RETURN( ERRTEXT ) \
{ \
   ErrorEvent evt; \
   evt.errorText = ( ERRTEXT ); \
   evt.errNo = 0; \
   fireEvent( cpcFunc( BroadsoftXsiHandler::onError ),evt ); \
   return kError; \
}
#define INVALID_HANDLER_STR "Broadsoft XSI Handler Already Set"

#define BROADSOFT_XSI_BRIDGEID "bridgeId"
#define BROADSOFT_XSI_CALLLOGID "callLogId"
#define BROADSOFT_XSI_DEPARTMENT "department"
#define BROADSOFT_XSI_EMAILADDRESS "emailAddress"
#define BROADSOFT_XSI_EXTENSION "extension"
#define BROADSOFT_XSI_FIRSTNAME "firstName"
#define BROADSOFT_XSI_GROUPID "groupId"
#define BROADSOFT_XSI_IMPID "impId"
#define BROADSOFT_XSI_LASTNAME "lastName"
#define BROADSOFT_XSI_LOCATION_NUMBER "locationNumber"

#ifdef CPCAPI2_BRAND_BROADSOFT_XSI_MOBILE_ATTR_NAME
#  define BROADSOFT_XSI_MOBILENO CPCAPI2_BRAND_BROADSOFT_XSI_MOBILE_ATTR_NAME
#else
#  define BROADSOFT_XSI_MOBILENO "mobileNo"
#endif

#define BROADSOFT_XSI_NUMBER "number"
#define BROADSOFT_XSI_RESULTS "results"
#define BROADSOFT_XSI_ROOMID "roomId"
#define BROADSOFT_XSI_SEARCHCRITERIAMODEOR "searchCriteriaModeOr"
#define BROADSOFT_XSI_SORTCOLUMN "sortColumn"
#define BROADSOFT_XSI_USERID "userId"
#define BROADSOFT_XSI_YAHOOID "yahooId"
#define BROADSOFT_XSI_ADDRESS "address"

using namespace CPCAPI2::BroadsoftXsi;
using CPCAPI2::WebSocketCommand;

BroadsoftXsiImpl::BroadsoftXsiImpl(
   BroadsoftXsiHandle h,
   boost::asio::io_service& ioService,
   resip::Fifo< resip::ReadCallbackBase >& callbackFifo )
   : m_IOService(ioService), m_CallbackFifo(callbackFifo), m_HttpClient(new HTTPClient), m_Handler(NULL), m_CbHook(NULL), m_StartNewConnection(true), m_Handle(h), m_PreferEnhancedLogs(true)
{
}

BroadsoftXsiImpl::~BroadsoftXsiImpl()
{
   m_HttpClient->Abort();
   delete m_HttpClient;
   m_Handler = NULL;
}

// Broadsoft XSI Implementation
int BroadsoftXsiImpl::configureConnectionSettings( BroadsoftXsiInfo* info, const LocalSettings& settings )
{
   // Always update the settings
   m_Settings = settings;

   // Assume configuration change requires new http connection
   m_StartNewConnection = true;

   // Assume configuration change could result in enhanced call logs being available
   // Temporarily disabled until we're given accounts with this service to verify against
   m_PreferEnhancedLogs = true;

   return kSuccess;
}

int BroadsoftXsiImpl::applySettings()
{
   return kSuccess;
}

int BroadsoftXsiImpl::queryServices()
{
   cpc::vector<XsiParameter> params;
   cpc::string body;
   request(XsiAction_Services, params, body);
   return kSuccess;
}

int BroadsoftXsiImpl::queryCallLogs(const CallLogType& callLogType)
{
   bool tryAgain = true;
   cpc::vector<XsiParameter> params;
   cpc::string body;
   while (tryAgain)
   {
      bool preferEnhancedLogs = m_PreferEnhancedLogs;
      switch (callLogType)
      {
      case CallLogType_Missed:
         tryAgain = !request(XsiAction_CallLogsMissed, params, body);
         break;
      case CallLogType_Placed:
         tryAgain = !request(XsiAction_CallLogsPlaced, params, body);
         break;
      case CallLogType_Received:
         tryAgain = !request(XsiAction_CallLogsReceived, params, body);
         break;
      case CallLogType_All:
         tryAgain = !request(XsiAction_CallLogs, params, body);
         break;
      case CallLogType_Unknown:
      default:
         ErrLog(<< "BroadsoftXsiImpl::queryCallLogs(): Invalid CallLogType: " << callLogType);
         BroadsoftXsiFailureEvent e;
         fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryCallLogsFailure), e);
         return kError;
      }
      tryAgain &= preferEnhancedLogs && !m_PreferEnhancedLogs;
   }
   return kSuccess;
}

int BroadsoftXsiImpl::deleteCallLogs(const CallLogType& callLogType)
{
   bool tryAgain = true;
   cpc::vector<XsiParameter> params;
   cpc::string body;
   while (tryAgain)
   {
      bool preferEnhancedLogs = m_PreferEnhancedLogs;
      switch (callLogType)
      {
      case CallLogType_Missed:
         tryAgain = !request(XsiAction_DeleteCallLogsMissed, params, body);
         break;
      case CallLogType_Placed:
         tryAgain = !request(XsiAction_DeleteCallLogsPlaced, params, body);
         break;
      case CallLogType_Received:
         tryAgain = !request(XsiAction_DeleteCallLogsReceived, params, body);
         break;
      case CallLogType_All:
         tryAgain = !request(XsiAction_DeleteCallLogs, params, body);
         break;
      case CallLogType_Unknown:
      default:
         ErrLog(<< "BroadsoftXsiImpl::deleteCallLogs(): Invalid CallLogType: " << callLogType);
         BroadsoftXsiFailureEvent e;
         fireEvent(cpcFunc(BroadsoftXsiHandler::onDeleteCallLogsFailure), e);
         return kError;
      }
      tryAgain &= preferEnhancedLogs && !m_PreferEnhancedLogs;
   }
   return kSuccess;
}

int BroadsoftXsiImpl::deleteCallLog(const CallLogType& callLogType, const cpc::string& callLogId)
{
   if (callLogId.empty())
   {
      ErrLog(<< "BroadsoftXsiImpl::deleteCallLog() - Call log id provided is empty. Not sending request.");
      BroadsoftXsiFailureEvent e;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onDeleteCallLogFailure), e);
      return kError;
   }

   bool tryAgain = true;
   cpc::vector<XsiParameter> params;
   cpc::string body;
   XsiParameter pCallLogId = {BROADSOFT_XSI_CALLLOGID, callLogId};
   params.push_back(pCallLogId);
   while (tryAgain)
   {
      bool preferEnhancedLogs = m_PreferEnhancedLogs;
      switch (callLogType)
      {
      case CallLogType_Missed:
         tryAgain = !request(XsiAction_DeleteCallLogMissed, params, body);
         break;
      case CallLogType_Placed:
         tryAgain = !request(XsiAction_DeleteCallLogPlaced, params, body);
         break;
      case CallLogType_Received:
         tryAgain = !request(XsiAction_DeleteCallLogReceived, params, body);
         break;
      default:
      {
         ErrLog(<< "BroadsoftXsiImpl::deleteCallLog() - Invalid call log type provided. Not sending request.");
         BroadsoftXsiFailureEvent e;
         fireEvent(cpcFunc(BroadsoftXsiHandler::onDeleteCallLogFailure), e);
         return kError;
      }
      }
      tryAgain &= preferEnhancedLogs && !m_PreferEnhancedLogs;
   }
   return kSuccess;
}

int BroadsoftXsiImpl::queryEnterpriseDirectory(const DirectorySearchParameters& searchParameters)
{
   cpc::vector<XsiParameter> params;
   if (!searchParameters.bridgeId.empty())
   {
      XsiParameter bridgeId = { BROADSOFT_XSI_BRIDGEID, ("*" + searchParameters.bridgeId + "*/i") };
      params.push_back(bridgeId);
   }
   if (!searchParameters.department.empty())
   {
      XsiParameter department = { BROADSOFT_XSI_DEPARTMENT, ("*" + searchParameters.department + "*/i") };
      params.push_back(department);
   }
   if (!searchParameters.emailAddress.empty())
   {
      XsiParameter emailAddress = { BROADSOFT_XSI_EMAILADDRESS, ("*" + searchParameters.emailAddress + "*/i") };
      params.push_back(emailAddress);
   }
   if (!searchParameters.extension.empty())
   {
      XsiParameter extension = { BROADSOFT_XSI_EXTENSION, ("*" + searchParameters.extension + "*/i") };
      params.push_back(extension);
   }
   if (!searchParameters.firstName.empty())
   {
      XsiParameter firstName = { BROADSOFT_XSI_FIRSTNAME, ("*" + searchParameters.firstName + "*/i") };
      params.push_back(firstName);
   }
   if (!searchParameters.groupId.empty())
   {
      XsiParameter groupId = { BROADSOFT_XSI_GROUPID, ("*" + searchParameters.groupId + "*/i") };
      params.push_back(groupId);
   }
   if (!searchParameters.impId.empty())
   {
      XsiParameter impId = { BROADSOFT_XSI_IMPID, ("*" + searchParameters.impId + "*/i") };
      params.push_back(impId);
   }
   if (!searchParameters.lastName.empty())
   {
      XsiParameter lastName = { BROADSOFT_XSI_LASTNAME, ("*" + searchParameters.lastName + "*/i") };
      params.push_back(lastName);
   }
   if (!searchParameters.mobileNo.empty())
   {
      XsiParameter mobileNo = { BROADSOFT_XSI_MOBILENO, ("*" + searchParameters.mobileNo + "*/i") };
      params.push_back(mobileNo);
   }
   if (!searchParameters.number.empty())
   {
      XsiParameter number = { BROADSOFT_XSI_NUMBER, ("*" + searchParameters.number + "*/i") };
      params.push_back(number);
   }
   if (!searchParameters.roomId.empty())
   {
      XsiParameter roomId = { BROADSOFT_XSI_ROOMID, ("*" + searchParameters.roomId + "*/i") };
      params.push_back(roomId);
   }
   if (!searchParameters.userId.empty())
   {
      XsiParameter userId = { BROADSOFT_XSI_USERID, ("*" + searchParameters.userId + "*/i") };
      params.push_back(userId);
   }
   if (!searchParameters.yahooId.empty())
   {
      XsiParameter yahooId = { BROADSOFT_XSI_YAHOOID, ("*" + searchParameters.yahooId + "*/i") };
      params.push_back(yahooId);
   }

   bool orMode = searchParameters.searchCriteriaModeOr && !params.empty(); // Server returns 400 failure if using OR mode with no parameters
   XsiParameter searchCriteriaModeOr = { BROADSOFT_XSI_SEARCHCRITERIAMODEOR, (orMode ? "True" : "False") };
   params.push_back(searchCriteriaModeOr);

   XsiParameter sortColumn;
   switch (searchParameters.sortColumn)
   {
   case SortColumn_Department:
      sortColumn = { BROADSOFT_XSI_SORTCOLUMN, BROADSOFT_XSI_DEPARTMENT };
      break;
   case SortColumn_FirstName:
      sortColumn = { BROADSOFT_XSI_SORTCOLUMN, BROADSOFT_XSI_FIRSTNAME };
      break;
   case SortColumn_LastName:
   default:
      sortColumn = { BROADSOFT_XSI_SORTCOLUMN, BROADSOFT_XSI_LASTNAME };
      break;
   }
   params.push_back(sortColumn);

   unsigned int results = searchParameters.results;
   if (results == 0 || results > 1000) results = 1000;
   char sResults[5];
   snprintf(sResults, 5, "%u", results);
   XsiParameter pResults = { BROADSOFT_XSI_RESULTS, sResults };
   params.push_back(pResults);

   cpc::string body;
   request(XsiAction_EnterpriseDirectory, params, body);
   return kSuccess;
}

int BroadsoftXsiImpl::queryServiceSettings(const ServiceType serviceType)
{
   cpc::vector<XsiParameter> params;
   cpc::string body;
   switch (serviceType)
   {
   case ServiceType_BroadworksAnywhere:
      request(XsiAction_QueryBroadworksAnywhereSettings, params, body);
      break;
   case ServiceType_CallForwardAlways:
      request(XsiAction_QueryCallForwardingAlwaysSettings, params, body);
      break;
   case ServiceType_CallForwardBusy:
      request(XsiAction_QueryCallForwardingBusySettings, params, body);
      break;
   case ServiceType_CallForwardNoAnswer:
      request(XsiAction_QueryCallForwardingNoAnswerSettings, params, body);
      break;
   case ServiceType_DoNotDisturb:
      request(XsiAction_QueryDoNotDisturbSettings, params, body);
      break;
   case ServiceType_RemoteOffice:
      request(XsiAction_QueryRemoteOfficeSettings, params, body);
      break;
   case ServiceType_SimultaneousRing:
      request(XsiAction_QuerySimultaneousRingSettings, params, body);
      break;
   }
   return kSuccess;
}

int BroadsoftXsiImpl::setBroadworksAnywhereSettings(const BroadworksAnywhere& settings)
{
   cpc::vector<XsiParameter> params;
   xmlChar* xmlBody;
   int xmlBodySize;
   bool requestSuccess = true;

   // Compose XML body for Broadworks Anywhere request
   xmlDocPtr doc = xmlNewDoc((const xmlChar *) "1.0");
   xmlNodePtr root_node = xmlNewNode(NULL, (const xmlChar *) "BroadWorksAnywhere");
   xmlDocSetRootElement(doc, root_node);
   xmlNewProp(root_node, (const xmlChar *) "xmlns", (const xmlChar *) "http://schema.broadsoft.com/xsi");
   xmlNewChild(root_node, NULL, (const xmlChar *) "alertAllLocationsForClickToDialCalls", (const xmlChar *)(settings.alertAllLocationsForClickToDialCalls ? "true" : "false"));
   xmlNewChild(root_node, NULL, (const xmlChar *) "alertAllLocationsForGroupPagingCalls", (const xmlChar *)(m_BroadworksAnywhere.alertAllLocationsForGroupPagingCalls ? "true" : "false"));
   xmlDocDumpMemory(doc, &xmlBody, &xmlBodySize);
   xmlFreeDoc(doc);

   cpc::string body((char*)xmlBody);
   requestSuccess = request(XsiAction_SetBroadworksAnywhereSettings, params, body);
   xmlFree(xmlBody);
   //if (!requestSuccess) return kSuccess;

   bool foundMatch;
   for (auto it = settings.locations.begin(); it != settings.locations.end(); ++it)
   {
      foundMatch = false;
      for (auto it2 = m_BroadworksAnywhere.locations.begin(); it2 != m_BroadworksAnywhere.locations.end(); ++it2)
      {
         if (it->phoneNumber == it2->phoneNumber)
         {
            foundMatch = true;
            if (it->active != it2->active) // no description check since our client doesn't change it
            {
               // Compose XML body for Broadworks Anywhere Location edit
               doc = xmlNewDoc((const xmlChar *) "1.0");
               root_node = xmlNewNode(NULL, (const xmlChar *) "BroadWorksAnywhereLocation");
               xmlDocSetRootElement(doc, root_node);
               xmlNewProp(root_node, (const xmlChar *) "xmlns", (const xmlChar *) "http://schema.broadsoft.com/xsi");
               xmlNewChild(root_node, NULL, (const xmlChar *) "phoneNumber", (const xmlChar *)(it->phoneNumber.c_str()));
			   if (!it2->description.empty()) xmlNewChild(root_node, NULL, (const xmlChar *) "description", (const xmlChar *)(it2->description.c_str()));
               xmlNewChild(root_node, NULL, (const xmlChar *) "active", (const xmlChar *)(it->active ? "true" : "false"));
               xmlDocDumpMemory(doc, &xmlBody, &xmlBodySize);
               xmlFreeDoc(doc);

               cpc::string body((char*)xmlBody);
               params.clear();
               XsiParameter pLocationNumber = { BROADSOFT_XSI_LOCATION_NUMBER, it->phoneNumber };
               params.push_back(pLocationNumber);
               requestSuccess = request(XsiAction_UpdateBroadworksAnywhereLocation, params, body);
               xmlFree(xmlBody);
               //if (!requestSuccess) return kSuccess;
            }
            // else Skip location
            break;
         }
      }
      if (!foundMatch)
      {
         // Compose XML body for Broadworks Anywhere Location add
         doc = xmlNewDoc((const xmlChar *) "1.0");
         root_node = xmlNewNode(NULL, (const xmlChar *) "BroadWorksAnywhereLocation");
         xmlDocSetRootElement(doc, root_node);
         xmlNewProp(root_node, (const xmlChar *) "xmlns", (const xmlChar *) "http://schema.broadsoft.com/xsi");
         xmlNewChild(root_node, NULL, (const xmlChar *) "phoneNumber", (const xmlChar *)(it->phoneNumber.c_str()));
		 //xmlNewChild(root_node, NULL, (const xmlChar *) "description", (const xmlChar *)"");
         xmlNewChild(root_node, NULL, (const xmlChar *) "active", (const xmlChar *)(it->active ? "true" : "false"));
         xmlDocDumpMemory(doc, &xmlBody, &xmlBodySize);
         xmlFreeDoc(doc);

         cpc::string body((char*)xmlBody);
         requestSuccess = request(XsiAction_AddBroadworksAnywhereLocation, params, body);
         xmlFree(xmlBody);
         //if (!requestSuccess) return kSuccess;
      }
   }

   for (auto it = m_BroadworksAnywhere.locations.begin(); it != m_BroadworksAnywhere.locations.end(); ++it)
   {
      foundMatch = false;
      for (auto it2 = settings.locations.begin(); it2 != settings.locations.end(); ++it2)
      {
		  if (it->phoneNumber == it2->phoneNumber)
		  {
			  foundMatch = true;
			  break;
		  }
      }
      if (!foundMatch)
      {
         // Compose XML body for Broadworks Anywhere Location delete
         body = "";
         params.clear();
         XsiParameter pLocationNumber = { BROADSOFT_XSI_LOCATION_NUMBER, it->phoneNumber };
         params.push_back(pLocationNumber);
         requestSuccess = request(XsiAction_DeleteBroadworksAnywhereLocation, params, body);
         //if (!requestSuccess) return kSuccess;
      }
   }

   (void)requestSuccess;
   
   m_BroadworksAnywhere = settings;

   SetServiceSettingsSuccessEvent evt;
   evt.serviceType = ServiceType_BroadworksAnywhere;
   fireEvent(cpcFunc(BroadsoftXsiHandler::onSetServiceSettings), evt);

   return kSuccess;
}

int BroadsoftXsiImpl::setCallForwardAlwaysSettings(const CallForwardAlways& settings)
{
   cpc::vector<XsiParameter> params;
   xmlChar* xmlBody;
   int xmlBodySize;

   // Compose XML body for Call Forward Always request
   xmlDocPtr doc = xmlNewDoc((const xmlChar *) "1.0");
   xmlNodePtr root_node = xmlNewNode(NULL, (const xmlChar *) "CallForwardingAlways");
   xmlDocSetRootElement(doc, root_node);
   xmlNewProp(root_node, (const xmlChar *) "xmlns", (const xmlChar *) "http://schema.broadsoft.com/xsi");
   xmlNewChild(root_node, NULL, (const xmlChar *) "active", (const xmlChar *)(settings.enabled ? "true" : "false"));
   xmlNewChild(root_node, NULL, (const xmlChar *) "forwardToPhoneNumber", (const xmlChar *)settings.phoneNumber.c_str());
   xmlNewChild(root_node, NULL, (const xmlChar *) "ringSplash", (const xmlChar *)(settings.ringSplash ? "true" : "false"));
   xmlDocDumpMemory(doc, &xmlBody, &xmlBodySize);
   xmlFreeDoc(doc);

   cpc::string body((char*)xmlBody);
   request(XsiAction_SetCallForwardingAlwaysSettings, params, body);
   xmlFree(xmlBody);

   return kSuccess;
}

int BroadsoftXsiImpl::setCallForwardBusySettings(const CallForwardBusy& settings)
{
   cpc::vector<XsiParameter> params;
   xmlChar* xmlBody;
   int xmlBodySize;

   // Compose XML body for Call Forward Busy request
   xmlDocPtr doc = xmlNewDoc((const xmlChar *) "1.0");
   xmlNodePtr root_node = xmlNewNode(NULL, (const xmlChar *) "CallForwardingBusy");
   xmlDocSetRootElement(doc, root_node);
   xmlNewProp(root_node, (const xmlChar *) "xmlns", (const xmlChar *) "http://schema.broadsoft.com/xsi");
   xmlNewChild(root_node, NULL, (const xmlChar *) "active", (const xmlChar *)(settings.enabled ? "true" : "false"));
   xmlNewChild(root_node, NULL, (const xmlChar *) "forwardToPhoneNumber", (const xmlChar *)settings.phoneNumber.c_str());
   xmlDocDumpMemory(doc, &xmlBody, &xmlBodySize);
   xmlFreeDoc(doc);

   cpc::string body((char*)xmlBody);
   request(XsiAction_SetCallForwardingBusySettings, params, body);
   xmlFree(xmlBody);

   return kSuccess;
}

int BroadsoftXsiImpl::setCallForwardNoAnswerSettings(const CallForwardNoAnswer& settings)
{
   cpc::vector<XsiParameter> params;
   xmlChar* xmlBody;
   int xmlBodySize;

   // Compose XML body for Call Forward No Answer request
   xmlDocPtr doc = xmlNewDoc((const xmlChar *) "1.0");
   xmlNodePtr root_node = xmlNewNode(NULL, (const xmlChar *) "CallForwardingNoAnswer");
   xmlDocSetRootElement(doc, root_node);
   xmlNewProp(root_node, (const xmlChar *) "xmlns", (const xmlChar *) "http://schema.broadsoft.com/xsi");
   xmlNewChild(root_node, NULL, (const xmlChar *) "active", (const xmlChar *)(settings.enabled ? "true" : "false"));
   xmlNewChild(root_node, NULL, (const xmlChar *) "forwardToPhoneNumber", (const xmlChar *)settings.phoneNumber.c_str());
   char sRings[4];
   snprintf(sRings, 4, "%u", settings.numberOfRings);
   xmlNewChild(root_node, NULL, (const xmlChar *) "numberOfRings", (const xmlChar *)sRings);
   xmlDocDumpMemory(doc, &xmlBody, &xmlBodySize);
   xmlFreeDoc(doc);

   cpc::string body((char*)xmlBody);
   request(XsiAction_SetCallForwardingNoAnswerSettings, params, body);
   xmlFree(xmlBody);

   return kSuccess;
}

int BroadsoftXsiImpl::setDoNotDisturbSettings(const DoNotDisturb& settings)
{
   cpc::vector<XsiParameter> params;
   xmlChar* xmlBody;
   int xmlBodySize;

   // Compose XML body for Do Not Disturb request
   xmlDocPtr doc = xmlNewDoc((const xmlChar *) "1.0");
   xmlNodePtr root_node = xmlNewNode(NULL, (const xmlChar *) "DoNotDisturb");
   xmlDocSetRootElement(doc, root_node);
   xmlNewProp(root_node, (const xmlChar *) "xmlns", (const xmlChar *) "http://schema.broadsoft.com/xsi");
   xmlNewChild(root_node, NULL, (const xmlChar *) "active", (const xmlChar *)(settings.enabled ? "true" : "false"));
   xmlNewChild(root_node, NULL, (const xmlChar *) "ringSplash", (const xmlChar *)(settings.ringSplash ? "true" : "false"));
   xmlDocDumpMemory(doc, &xmlBody, &xmlBodySize);
   xmlFreeDoc(doc);

   cpc::string body((char*)xmlBody);
   request(XsiAction_SetDoNotDisturbSettings, params, body);
   xmlFree(xmlBody);

   return kSuccess;
}

int BroadsoftXsiImpl::setRemoteOfficeSettings(const RemoteOffice& settings)
{
   cpc::vector<XsiParameter> params;
   xmlChar* xmlBody;
   int xmlBodySize;

   // Compose XML body for Remote Office request
   xmlDocPtr doc = xmlNewDoc((const xmlChar *) "1.0");
   xmlNodePtr root_node = xmlNewNode(NULL, (const xmlChar *) "RemoteOffice");
   xmlDocSetRootElement(doc, root_node);
   xmlNewProp(root_node, (const xmlChar *) "xmlns", (const xmlChar *) "http://schema.broadsoft.com/xsi");
   xmlNewChild(root_node, NULL, (const xmlChar *) "active", (const xmlChar *)(settings.enabled ? "true" : "false"));
   xmlNewChild(root_node, NULL, (const xmlChar *) "remoteOfficeNumber", (const xmlChar *)settings.phoneNumber.c_str());
   xmlDocDumpMemory(doc, &xmlBody, &xmlBodySize);
   xmlFreeDoc(doc);

   cpc::string body((char*)xmlBody);
   request(XsiAction_SetRemoteOfficeSettings, params, body);
   xmlFree(xmlBody);

   return kSuccess;
}

int BroadsoftXsiImpl::setSimultaneousRingSettings(const SimultaneousRing& settings)
{
   cpc::vector<XsiParameter> params;
   xmlChar* xmlBody;
   int xmlBodySize;

   // Compose XML body for Simultaneous Ring request
   xmlDocPtr doc = xmlNewDoc((const xmlChar *) "1.0");
   xmlNodePtr root_node = xmlNewNode(NULL, (const xmlChar *) "SimultaneousRingPersonal");
   xmlDocSetRootElement(doc, root_node);
   xmlNewProp(root_node, (const xmlChar *) "xmlns", (const xmlChar *) "http://schema.broadsoft.com/xsi");
   xmlNewChild(root_node, NULL, (const xmlChar *) "active", (const xmlChar *)(settings.enabled ? "true" : "false"));
   xmlNewChild(root_node, NULL, (const xmlChar *) "incomingCalls", (const xmlChar *)(settings.dontRingWhenOnCall ? "Do not Ring if on a Call" : "Ring for all Incoming Calls"));
   if (!settings.locations.empty())
   {
      xmlNodePtr node = xmlNewChild(root_node, NULL, (const xmlChar *) "simRingLocations", NULL);
      for (auto it = settings.locations.begin(); it != settings.locations.end(); ++it)
      {
         xmlNodePtr node2 = xmlNewChild(node, NULL, (const xmlChar *) "simRingLocation", NULL);
         xmlNewChild(node2, NULL, (const xmlChar *) "address", (const xmlChar *)it->phoneNumber.c_str());
         xmlNewChild(node2, NULL, (const xmlChar *) "answerConfirmationRequired", (const xmlChar *)(it->answerConfirmationRequired ? "true" : "false"));
      }
   }
   xmlDocDumpMemory(doc, &xmlBody, &xmlBodySize);
   xmlFreeDoc(doc);

   cpc::string body((char*)xmlBody);
   request(XsiAction_SetSimultaneousRingSettings, params, body);
   xmlFree(xmlBody);

   return kSuccess;
}

int BroadsoftXsiImpl::createCall(const cpc::string& number )
{
   cpc::vector<XsiParameter> params;
   
   XsiParameter pCallAddress = {BROADSOFT_XSI_ADDRESS, number};
   params.push_back(pCallAddress);

   cpc::string body;
   request(XsiAction_CallStart, params, body);
   
   return kSuccess;
}

bool BroadsoftXsiImpl::request(const XsiAction action, const cpc::vector<XsiParameter>& parameters, cpc::string& body)
{
   // Set HTTP verb
   HTTPClient::EHTTPVerb httpVerb = HTTPClient::EHTTPVerbInvalid;
   switch (action)
   {
   case XsiAction_Services:
   case XsiAction_CallLogs:
   case XsiAction_CallLogsMissed:
   case XsiAction_CallLogsPlaced:
   case XsiAction_CallLogsReceived:
   case XsiAction_EnterpriseDirectory:
   case XsiAction_QueryBroadworksAnywhereSettings:
   case XsiAction_QueryCallForwardingAlwaysSettings:
   case XsiAction_QueryCallForwardingBusySettings:
   case XsiAction_QueryCallForwardingNoAnswerSettings:
   case XsiAction_QueryDoNotDisturbSettings:
   case XsiAction_QueryRemoteOfficeSettings:
   case XsiAction_QuerySimultaneousRingSettings:
      httpVerb = HTTPClient::EHTTPVerbGET;
      break;
   case XsiAction_DeleteCallLogs:
   case XsiAction_DeleteCallLogsMissed:
   case XsiAction_DeleteCallLogsPlaced:
   case XsiAction_DeleteCallLogsReceived:
   case XsiAction_DeleteCallLogMissed:
   case XsiAction_DeleteCallLogPlaced:
   case XsiAction_DeleteCallLogReceived:
   case XsiAction_DeleteBroadworksAnywhereLocation:
      httpVerb = HTTPClient::EHTTPVerbDELETE;
      break;
   case XsiAction_SetBroadworksAnywhereSettings:
   case XsiAction_UpdateBroadworksAnywhereLocation:
   case XsiAction_SetCallForwardingAlwaysSettings:
   case XsiAction_SetCallForwardingBusySettings:
   case XsiAction_SetCallForwardingNoAnswerSettings:
   case XsiAction_SetDoNotDisturbSettings:
   case XsiAction_SetRemoteOfficeSettings:
   case XsiAction_SetSimultaneousRingSettings:
      httpVerb = HTTPClient::EHTTPVerbPUT;
      break;
   case XsiAction_AddBroadworksAnywhereLocation:
   case XsiAction_CallStart:
      httpVerb = HTTPClient::EHTTPVerbPOST;
      break;
   default:
      ErrLog(<< "BroadsoftXsiImpl::request() - Unable to choose HTTP verb for action: " << action);
      BroadsoftXsiFailureEvent e;
      fireEvent(action, e);
      return false;
   }

   // Create URL
   std::string url;
   {
      std::stringstream ss;
      std::string url_encoded;
      bool parametersStarted = false;

      // Initiate URL
      ss << "http" << (m_Settings.useHttps ? "s" : "") << "://" << m_Settings.domain << "/com.broadsoft.xsi-actions/v2.0/";

      auto pathUsername = curlpp::escape(m_Settings.userName.c_str());
      // Append action command
      switch (action)
      {
      case XsiAction_Services:
         ss << "user/" << pathUsername << "/services";
         break;
      case XsiAction_CallLogs:
      case XsiAction_DeleteCallLogs:
         ss << "user/" << pathUsername << "/directories/" << (m_PreferEnhancedLogs ? "Enhanced" : "") << "CallLogs";
         break;
      case XsiAction_CallLogsMissed:
      case XsiAction_DeleteCallLogsMissed:
      case XsiAction_DeleteCallLogMissed:
         ss << "user/" << pathUsername << "/directories/" << (m_PreferEnhancedLogs ? "Enhanced" : "") << "CallLogs/Missed";
         break;
      case XsiAction_CallLogsPlaced:
      case XsiAction_DeleteCallLogsPlaced:
      case XsiAction_DeleteCallLogPlaced:
         ss << "user/" << pathUsername << "/directories/" << (m_PreferEnhancedLogs ? "Enhanced" : "") << "CallLogs/Placed";
         break;
      case XsiAction_CallLogsReceived:
      case XsiAction_DeleteCallLogsReceived:
      case XsiAction_DeleteCallLogReceived:
         ss << "user/" << pathUsername << "/directories/" << (m_PreferEnhancedLogs ? "Enhanced" : "") << "CallLogs/Received";
         break;
      case XsiAction_EnterpriseDirectory:
         ss << "user/" << pathUsername << "/directories/Enterprise";
         break;
      case XsiAction_QueryBroadworksAnywhereSettings:
      case XsiAction_SetBroadworksAnywhereSettings:
         ss << "user/" << pathUsername << "/services/BroadWorksAnywhere";
         break;
      case XsiAction_AddBroadworksAnywhereLocation:
      case XsiAction_DeleteBroadworksAnywhereLocation:
      case XsiAction_UpdateBroadworksAnywhereLocation:
         ss << "user/" << pathUsername << "/services/BroadWorksAnywhere/Location";
         break;
      case XsiAction_QueryCallForwardingAlwaysSettings:
      case XsiAction_SetCallForwardingAlwaysSettings:
         ss << "user/" << pathUsername << "/services/CallForwardingAlways";
         break;
      case XsiAction_QueryCallForwardingBusySettings:
      case XsiAction_SetCallForwardingBusySettings:
         ss << "user/" << pathUsername << "/services/CallForwardingBusy";
         break;
      case XsiAction_QueryCallForwardingNoAnswerSettings:
      case XsiAction_SetCallForwardingNoAnswerSettings:
         ss << "user/" << pathUsername << "/services/CallForwardingNoAnswer";
         break;
      case XsiAction_QueryDoNotDisturbSettings:
      case XsiAction_SetDoNotDisturbSettings:
         ss << "user/" << pathUsername << "/services/DoNotDisturb";
         break;
      case XsiAction_QueryRemoteOfficeSettings:
      case XsiAction_SetRemoteOfficeSettings:
         ss << "user/" << pathUsername << "/services/RemoteOffice";
         break;
      case XsiAction_QuerySimultaneousRingSettings:
      case XsiAction_SetSimultaneousRingSettings:
         ss << "user/" << pathUsername << "/services/SimultaneousRingPersonal";
         break;
      case XsiAction_CallStart:
         ss << "user/" << pathUsername << "/calls/new";
         break;
      default:
         ErrLog(<< "BroadsoftXsiImpl::request() - Unable to create URL for action: " << action);
         BroadsoftXsiFailureEvent e;
         fireEvent(action, e);
         return false;
      }

      // Append parameters
      switch (action)
      {
      case XsiAction_DeleteCallLogMissed:
      case XsiAction_DeleteCallLogPlaced:
      case XsiAction_DeleteCallLogReceived:
      {
         bool found = false;
         for (auto it = parameters.begin(); it != parameters.end(); ++it)
         {
            if (it->name.find(BROADSOFT_XSI_CALLLOGID) != cpc::string::npos)
            {
               found = true;
               url_encoded = curlpp::escape(it->value.c_str());
               if (!url_encoded.empty())
               {
                  ss << "/" << url_encoded; // Not actually a URL param, just an additional subdir of the URL
               }
               else
               {
                  ErrLog(<< "BroadsoftXsiImpl::request() - Failed to encode call log id: " << it->value);
                  BroadsoftXsiFailureEvent e;
                  fireEvent(cpcFunc(BroadsoftXsiHandler::onDeleteCallLogFailure), e);
                  return false;
               }
               break;
            }
         }
         if (!found)
         {
            ErrLog(<< "BroadsoftXsiImpl::request() - DeleteCallLog with no call log id. Not sending request.");
            BroadsoftXsiFailureEvent e;
            fireEvent(cpcFunc(BroadsoftXsiHandler::onDeleteCallLogFailure), e);
            return false;
         }
         break;
      }
      case XsiAction_EnterpriseDirectory:
      {
         for (auto it = parameters.begin(); it != parameters.end(); ++it)
         {
            url_encoded = curlpp::escape(it->value.c_str());
            if (!url_encoded.empty())
            {
               if (parametersStarted)
               {
                  ss << "&";
               }
               else
               {
                  ss << "?";
                  parametersStarted = true;
               }
               ss << it->name << "=" << url_encoded;
            }
            else
            {
               ErrLog(<< "BroadsoftXsiImpl::request() - Failed to encode enterprise directory parameter: " << it->name << "=" << it->value);
            }
         }
         break;
      }
      case XsiAction_CallStart:
      {
         if(parameters.size() == 1 && parameters[0].name.find(BROADSOFT_XSI_ADDRESS) != cpc::string::npos)
         {
            url_encoded = curlpp::escape(parameters[0].value.c_str());
            if (!url_encoded.empty())
            {
               ss << "?" << parameters[0].name << "=" << url_encoded;
            }
            else
            {
               ErrLog(<< "BroadsoftXsiImpl::request() - Failed to encode call start parameter: " << parameters[0].name << "=" << parameters[0].value);
            }
         }
         else
         {
            ErrLog(<< "BroadsoftXsiImpl::request() - Failed to encode call start parameter");
         }
         break;
      }
      case XsiAction_DeleteBroadworksAnywhereLocation:
      case XsiAction_UpdateBroadworksAnywhereLocation:
      {
         bool found = false;
         for (auto it = parameters.begin(); it != parameters.end(); ++it)
         {
            if (it->name.find(BROADSOFT_XSI_LOCATION_NUMBER) != cpc::string::npos)
            {
               found = true;
			      url_encoded = curlpp::escape(it->value.c_str());
			      if (!url_encoded.empty())
			      {
				      ss << "/" << url_encoded;
			      }
               break;
            }
         }
         if (!found)
         {
            ErrLog(<< "BroadsoftXsiImpl::request() - Editing/Deleting Broadworks Anywhere location with no phone number. Not sending request.");
            BroadsoftXsiFailureEvent e;
            e.serviceType = ServiceType_BroadworksAnywhere;
            fireEvent(cpcFunc(BroadsoftXsiHandler::onSetServiceSettingsFailure), e);
            return false;
         }
         break;
      }
      default:
         // No parameters needed
         break;
      }

      url = ss.str();
   }

   if (m_Settings.logXMLPayload && !body.empty())
   {
      InfoLog(<< "BroadsoftXsiImpl::request() - Logging XML request:\n" << body);
   }

   cpc::string contentType( "application/xml; charset=UTF-8" );
   HTTPClient::RedirectInfo redirectInfo;
   cpc::vector< HTTPClient::StringPair > customHeaders;
   //SCORE-1514:
   if (action == XsiAction_EnterpriseDirectory)
   {
      HTTPClient::StringPair hVersion = {"X-BroadWorks-Protocol-Version", "23.9"};
      customHeaders.push_back(hVersion);
   }
   // HTTPClient::StringPair hAccept = {"Accept", "application/xml; charset=UTF-8"};
   // customHeaders.push_back(hAccept); // Seems like it gets implicitly set to "*/*"

   CPCAPI2::HTTPClient::ResponseResult httpResult;

   CPCAPI2::HTTPClient::RequestConfig httpConfig;
   httpConfig.verb = httpVerb;
   httpConfig.mimeType = contentType.c_str();
   httpConfig.username = m_Settings.userName.c_str();
   httpConfig.password = m_Settings.password.c_str();
   httpConfig.clientCertificate = "";
   httpConfig.clientCertificatePasswd = "";
   httpConfig.useEmbeddedCert1 = false;
   httpConfig.useEmbeddedCert2 = false;
   httpConfig.ignoreCertErrors = false;
   httpConfig.enableCookies = !m_Settings.cookieFile.empty();
   httpConfig.cookieFile = m_Settings.cookieFile.c_str();
   httpConfig.verboseLogging = true;
   httpConfig.userAgent = "CounterPath-HTTP";
   httpConfig.useHttp2 = true;
   httpConfig.messageBody = body.c_str();
   httpConfig.messageLengthInBytes = body.size();
   httpConfig.customHeaders = customHeaders;

   if (m_StartNewConnection)
   {
      m_StartNewConnection = false;

      m_HttpClient->StartHTTPSession(url.c_str(), httpConfig, httpResult); 
   }
   else
   {
      m_HttpClient->DoSessionRequest(url.c_str(), httpConfig, httpResult);
   }

   bool success = processServerResponse(action, parameters, httpResult.errorCode, httpResult.status, std::string(contentType.c_str()), std::string(httpResult.messageBody.c_str()));

   body.swap(httpResult.messageBody);

   return success;
}

bool BroadsoftXsiImpl::processServerResponse(const XsiAction action, const cpc::vector<XsiParameter>& parameters, int errorCode, int responseStatus, std::string contentType, std::string responseBody)
{
   // Did we get a response?
   if (errorCode != 0)
   {
      ErrLog(<< "BroadsoftXsiImpl::processServerResponse(): Error:" << errorCode << " Reason:" << responseStatus);
      BroadsoftXsiFailureEvent e;

      if (responseStatus == CPCAPI2::HTTPClient::EHTTPAuth)
         e.failureReason = BroadsoftXsiFailureReason_Auth;
      else if (responseStatus == CPCAPI2::HTTPClient::EHTTPEmbedded)
         e.failureReason = BroadsoftXsiFailureReason_Cert;
      else if (responseStatus == CPCAPI2::HTTPClient::EHTTPLookup)
         e.failureReason = BroadsoftXsiFailureReason_Resolve;
      else
         e.failureReason = BroadsoftXsiFailureReason_Connect;

      fireEvent( action, e );
      return false;
   }

   // Check for various response statuses, only 2xx is an OK response
   if( responseStatus >= 500 )
   {
      ErrLog(<< "BroadsoftXsiImpl::processServerResponse() - Got 5xx class response (" << responseStatus << ")");
      BroadsoftXsiFailureEvent evt;
      evt.failureReason = BroadsoftXsiFailureReason_HTTP;
      evt.httpResponseCode = responseStatus;
      fireEvent( action, evt );
      return false;
   }
   else if( responseStatus >= 400 )
   {
      BroadsoftXsiFailureEvent evt;
      cpc::string serverErrCode;
      evt.failureReason = BroadsoftXsiFailureReason_HTTP;
      evt.httpResponseCode = responseStatus;
      // Look for error info in response
      if (responseBody.size() != 0 && contentType.find("application/xml") != std::string::npos)
      {
         xmlDocPtr doc = xmlParseMemory(responseBody.data(), (int)responseBody.size());
         if (doc != NULL)
         {
            xmlNodePtr cur = doc->children;
            if (!xmlStrcmp(cur->name, (const xmlChar *) "ErrorInfo"))
            {
               cur = cur->children;
               while (cur != NULL) {
                  if (!xmlStrcmp(cur->name, (const xmlChar *) "summary"))
                  {
                     evt.failureReason = BroadsoftXsiFailureReason_Server;
                     evt.serverErrorMessage = (const char*)(cur->children->content);
                  }
                  else if (!xmlStrcmp(cur->name, (const xmlChar *) "errorCode") &&
                     m_PreferEnhancedLogs &&
                     (action == XsiAction_CallLogs || action == XsiAction_CallLogsMissed ||
                     action == XsiAction_CallLogsPlaced || action == XsiAction_CallLogsReceived))
                  {
                     // Tried to query enhanced logs, but response indicates account doesn't have this service
                     m_PreferEnhancedLogs = false;
                     xmlFreeDoc(doc);
                     return false;
                  }
                  else if (!xmlStrcmp(cur->name, (const xmlChar *) "errorCode")) {
                     serverErrCode = (const char*)(cur->children->content);
                  }
                  cur = cur->next;
               }
            }
            xmlFreeDoc(doc);
         }
      }
      if (evt.serverErrorMessage.empty())
      {
         ErrLog(<< "BroadsoftXsiImpl::processServerResponse() - Got 4xx class response (" << responseStatus << ")");
      }
      else
      {
         ErrLog(<< "BroadsoftXsiImpl::processServerResponse() - Got 4xx class response (" << responseStatus << ") with server error: " << evt.serverErrorMessage);
         evt.errorMessageText = serverErrCode + ": " + evt.serverErrorMessage;
      }
      fireEvent( action, evt );
      return false;
   }
   else if( responseStatus >= 300 )
   {
      // A 301 or 302 response indicates we are being redirected to another server. But according to
      // Line 892 in CurlHttp.cxx, the lib should automatically follow redirections (which is great,
      // otherwise we have to write code to check for redirection cycles). So just throw an error
      // here since we shouldn't get those results in any case.
      ErrLog(<< "BroadsoftXsiImpl::processServerResponse() - Got 3xx class response (" << responseStatus << ")");
      BroadsoftXsiFailureEvent evt;
      evt.failureReason = BroadsoftXsiFailureReason_HTTP;
      evt.httpResponseCode = responseStatus;
      fireEvent( action, evt );
      return false;
   }
   else if( responseStatus >= 200 )
   {
      // Is there any data in the response?
      if (responseBody.size() == 0)
      {
         switch (action)
         {
         case XsiAction_Services:
         case XsiAction_CallLogs:
         case XsiAction_CallLogsMissed:
         case XsiAction_CallLogsPlaced:
         case XsiAction_CallLogsReceived:
         case XsiAction_EnterpriseDirectory:
         case XsiAction_QueryBroadworksAnywhereSettings:
         case XsiAction_QueryCallForwardingAlwaysSettings:
         case XsiAction_QueryCallForwardingBusySettings:
         case XsiAction_QueryCallForwardingNoAnswerSettings:
         case XsiAction_QueryDoNotDisturbSettings:
         case XsiAction_QueryRemoteOfficeSettings:
         case XsiAction_QuerySimultaneousRingSettings:
         case XsiAction_CallStart:
         {
            ErrLog(<< "BroadsoftXsiImpl::processServerResponse() - No data in response!");
            BroadsoftXsiFailureEvent e;
            e.failureReason = BroadsoftXsiFailureReason_Malformed;
            fireEvent(action, e);
            return false;
         }
         case XsiAction_DeleteCallLogs:
         case XsiAction_DeleteCallLogsMissed:
         case XsiAction_DeleteCallLogsPlaced:
         case XsiAction_DeleteCallLogsReceived:
         case XsiAction_DeleteCallLogMissed:
         case XsiAction_DeleteCallLogPlaced:
         case XsiAction_DeleteCallLogReceived:
         case XsiAction_SetBroadworksAnywhereSettings:
         case XsiAction_AddBroadworksAnywhereLocation:
         case XsiAction_DeleteBroadworksAnywhereLocation:
         case XsiAction_UpdateBroadworksAnywhereLocation:
         case XsiAction_SetCallForwardingAlwaysSettings:
         case XsiAction_SetCallForwardingBusySettings:
         case XsiAction_SetCallForwardingNoAnswerSettings:
         case XsiAction_SetDoNotDisturbSettings:
         case XsiAction_SetRemoteOfficeSettings:
         case XsiAction_SetSimultaneousRingSettings:
         default:
            // We weren't expecting any data in the response, continue
            break;
         }
      }
      else if (m_Settings.logXMLPayload && contentType.find("application/xml") != std::string::npos)
      {
         InfoLog(<< "BroadsoftXsiImpl::processServerResponse() - Logging XML response:\n" << responseBody);
      }

      // TODO: Verify requested content type is what was received
      //ErrLog(<< "BroadsoftXsiImpl::processServerResponse() - Got 200 Ok but content type (" << contentType << ") does not match requested type");

      // Check for error info in response
      if (contentType.find("application/xml") != std::string::npos)
      {
         xmlDocPtr doc = xmlParseMemory(responseBody.data(), (int)responseBody.size());
         if (doc != NULL)
         {
            xmlNodePtr cur = doc->children;
            if (!xmlStrcmp(cur->name, (const xmlChar *) "ErrorInfo"))
            {
               // Found error
               BroadsoftXsiFailureEvent evt;
               cpc::string serverErrCode;
               evt.failureReason = BroadsoftXsiFailureReason_Server;
               cur = cur->children;
               while (cur != NULL) {
                  if (!xmlStrcmp(cur->name, (const xmlChar *) "summary"))
                  {
                     evt.serverErrorMessage = (const char*)(cur->children->content);
                  }
                  else if (!xmlStrcmp(cur->name, (const xmlChar *) "errorCode") &&
                           m_PreferEnhancedLogs &&
                           (action == XsiAction_CallLogs || action == XsiAction_CallLogsMissed ||
                            action == XsiAction_CallLogsPlaced || action == XsiAction_CallLogsReceived) &&
                           !xmlStrcmp(cur->children->content, (const xmlChar *) "4410"))
                  {
                     // Tried to query enhanced logs, but response indicates account doesn't have this service
                     m_PreferEnhancedLogs = false;
                     xmlFreeDoc(doc);
                     return false;
                  }
                  else if (!xmlStrcmp(cur->name, (const xmlChar *) "errorCode")) {
                     serverErrCode = (const char*)(cur->children->content);
                  }
                  cur = cur->next;
               }
               ErrLog(<< "BroadsoftXsiImpl::processServerResponse() - Got error info from server. " << evt.serverErrorMessage);
               evt.errorMessageText = serverErrCode + ": " + evt.serverErrorMessage;
               fireEvent(action, evt);
               xmlFreeDoc(doc);
               return false;
            }
            xmlFreeDoc(doc);
         }
      }

      switch (action)
      {
      case XsiAction_DeleteCallLogs:
      case XsiAction_DeleteCallLogsMissed:
      case XsiAction_DeleteCallLogsPlaced:
      case XsiAction_DeleteCallLogsReceived:
      {
         DeleteCallLogsSuccessEvent evt;
         fireEvent(cpcFunc(BroadsoftXsiHandler::onDeleteCallLogsSuccess), evt);
         return true;
      }
      case XsiAction_DeleteCallLogMissed:
      case XsiAction_DeleteCallLogPlaced:
      case XsiAction_DeleteCallLogReceived:
      {
         DeleteCallLogSuccessEvent evt;
         for (auto it = parameters.begin(); it != parameters.end(); ++it)
         {
            if (it->name.find(BROADSOFT_XSI_CALLLOGID) != cpc::string::npos)
            {
               evt.callLogId = it->value;
               break;
            }
         }
         fireEvent(cpcFunc(BroadsoftXsiHandler::onDeleteCallLogSuccess), evt);
         return true;
      }
      case XsiAction_Services:
         return parseServices(contentType, responseBody);
         break;
      case XsiAction_CallLogs:
         return parseCallLogs(contentType, responseBody, CallLogType_All);
         break;
      case XsiAction_CallLogsMissed:
         return parseCallLogs(contentType, responseBody, CallLogType_Missed);
         break;
      case XsiAction_CallLogsPlaced:
         return parseCallLogs(contentType, responseBody, CallLogType_Placed);
         break;
      case XsiAction_CallLogsReceived:
         return parseCallLogs(contentType, responseBody, CallLogType_Received);
         break;
      case XsiAction_EnterpriseDirectory:
         return parseEnterpriseDirectory(contentType, responseBody);
         break;
      case XsiAction_QueryBroadworksAnywhereSettings:
         return parseBroadworksAnywhere(contentType, responseBody);
         break;
      case XsiAction_QueryCallForwardingAlwaysSettings:
         return parseCallForwardAlways(contentType, responseBody);
         break;
      case XsiAction_QueryCallForwardingBusySettings:
         return parseCallForwardBusy(contentType, responseBody);
         break;
      case XsiAction_QueryCallForwardingNoAnswerSettings:
         return parseCallForwardNoAnswer(contentType, responseBody);
         break;
      case XsiAction_QueryDoNotDisturbSettings:
         return parseDoNotDisturb(contentType, responseBody);
         break;
      case XsiAction_QueryRemoteOfficeSettings:
         return parseRemoteOffice(contentType, responseBody);
         break;
      case XsiAction_QuerySimultaneousRingSettings:
         return parseSimultaneousRing(contentType, responseBody);
         break;
      case XsiAction_SetBroadworksAnywhereSettings:
      case XsiAction_AddBroadworksAnywhereLocation:
      case XsiAction_DeleteBroadworksAnywhereLocation:
      case XsiAction_UpdateBroadworksAnywhereLocation:
      {
         // Allow the caller to send the event in case they had to do separate requests to modify locations
         return true;
      }
      case XsiAction_SetCallForwardingAlwaysSettings:
      {
         SetServiceSettingsSuccessEvent evt;
         evt.serviceType = ServiceType_CallForwardAlways;
         fireEvent(cpcFunc(BroadsoftXsiHandler::onSetServiceSettings), evt);
         return true;
      }
      case XsiAction_SetCallForwardingBusySettings:
      {
         SetServiceSettingsSuccessEvent evt;
         evt.serviceType = ServiceType_CallForwardBusy;
         fireEvent(cpcFunc(BroadsoftXsiHandler::onSetServiceSettings), evt);
         return true;
      }
      case XsiAction_SetCallForwardingNoAnswerSettings:
      {
         SetServiceSettingsSuccessEvent evt;
         evt.serviceType = ServiceType_CallForwardNoAnswer;
         fireEvent(cpcFunc(BroadsoftXsiHandler::onSetServiceSettings), evt);
         return true;
      }
      case XsiAction_SetDoNotDisturbSettings:
      {
         SetServiceSettingsSuccessEvent evt;
         evt.serviceType = ServiceType_DoNotDisturb;
         fireEvent(cpcFunc(BroadsoftXsiHandler::onSetServiceSettings), evt);
         return true;
      }
      case XsiAction_SetRemoteOfficeSettings:
      {
         SetServiceSettingsSuccessEvent evt;
         evt.serviceType = ServiceType_RemoteOffice;
         fireEvent(cpcFunc(BroadsoftXsiHandler::onSetServiceSettings), evt);
         return true;
      }
      case XsiAction_SetSimultaneousRingSettings:
      {
         SetServiceSettingsSuccessEvent evt;
         evt.serviceType = ServiceType_SimultaneousRing;
         fireEvent(cpcFunc(BroadsoftXsiHandler::onSetServiceSettings), evt);
         return true;
      }
      case XsiAction_CallStart:
      {
         return parseCreateCallResponse(contentType, responseBody);
      }
      default:
      {
         ErrLog(<< "BroadsoftXsiImpl::processServerResponse() - Processing response for unknown action: " << action);
         BroadsoftXsiFailureEvent evt;
         fireEvent(action, evt);
         return false;
      }
      }
   }
   else // if( responseStatus >= 100 )
   {
      ErrLog(<< "BroadsoftXsiImpl::processServerResponse() - Got 1xx class response (" << responseStatus << ")");
      BroadsoftXsiFailureEvent evt;
      evt.failureReason = BroadsoftXsiFailureReason_HTTP;
      evt.httpResponseCode = responseStatus;
      fireEvent( action, evt );
      return false;
   }
   return true;
}

bool BroadsoftXsiImpl::parseCallLogs(std::string contentType, std::string responseBody, CallLogType callLogType)
{
   tzset(); // IMPALA-4632: Ensure timezone variable is set correctly in case user changed system timezone
   bool success = true;
   bool enhancedLogs = false;
   cpc::vector<CallLog> callLogs;
   if (contentType.find("application/xml") != std::string::npos)
   {
      // Parse XML
      xmlDocPtr doc = xmlParseMemory(responseBody.data(), (int)responseBody.size());

      if (doc == NULL)
      {
         ErrLog(<< "BroadsoftXsiImpl::parseCallLogs() - Parsing XML failed.");
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_CallLogs, evt);
         return false;
      }
      xmlNodePtr cur = doc->children;
      if (callLogType == CallLogType_All)
      {
         // Verify CallLogs entity
         const char* strCallLogs = (m_PreferEnhancedLogs ? "EnhancedCallLogs" : "CallLogs");
         if (xmlStrcmp(cur->name, (xmlChar*)strCallLogs))
         {
            ErrLog(<< "BroadsoftXsiImpl::parseCallLogs() - Parsing XML failed. Expected \"" << strCallLogs << "\". Actual: " << (const char*)cur->name);
            BroadsoftXsiFailureEvent evt;
            evt.failureReason = BroadsoftXsiFailureReason_Malformed;
            fireEvent(XsiAction_CallLogs, evt);
            success = false;
         }
         else
         {
            if (m_PreferEnhancedLogs)
            {
               enhancedLogs = true;
            }
            cur = cur->children;
            while (cur != NULL) {
               if (!parseCallLogType(cur, callLogType, callLogs))
               {
                  // Failed to parse call logs. Don't need to log error or callback, parseCallLogType should have.
                  success = false;
                  cur = NULL;
               }
               else
               {
                  cur = cur->next;
               }
            }
         }
      }
      else if (!parseCallLogType(cur, callLogType, callLogs))
      {
         // Failed to parse call logs. Don't need to log error or callback, parseCallLogType should have.
         success = false;
      }

      xmlFreeDoc(doc);
   }
   else
   {
      ErrLog(<< "BroadsoftXsiImpl::parseCallLogs() - Parsing content type (" << contentType << ") unsupported.");
      BroadsoftXsiFailureEvent evt;
      evt.failureReason = BroadsoftXsiFailureReason_Malformed;
      fireEvent(XsiAction_CallLogs, evt);
   }

   if (success)
   {
      if (m_Settings.logParsedResponse)
      {
         DebugLog(<< "BroadsoftXsiImpl::parseCallLogs() - Parsed call logs!");
         for (auto it = callLogs.begin(); it != callLogs.end(); ++it)
         {
            time_t callTime = it->time;
            DebugLog(<< " Call log ID: " << it->callLogId << ", Type: " << callLogTypeToString(it->callLogType) << ", Name: " << it->name << ", Number: " << it->phoneNumber << ", Time: " << std::ctime(&callTime));
         }
      }

      QueryCallLogsSuccessEvent evt;
      evt.enhanced = enhancedLogs;
      evt.callLogs = callLogs;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryCallLogs), evt);
   }
   return success;
}

time_t extractDateTime(const cpc::string& dateTimeValue)
{
   // Stolen from CpmHelper
   int year, month, day, hour, minute, second, millisecond, offsetHour, offsetMin;
   char offsetPrefix;
   sscanf(dateTimeValue.c_str(), "%4d-%2d-%2dT%2d:%2d:%2d.%3d%1c%2d:%2d", &year, &month, &day, &hour, &minute, &second, &millisecond, &offsetPrefix, &offsetHour, &offsetMin);

   // Fill the time structure
   tm rawDateTime;
   rawDateTime.tm_year = year - 1900;
   rawDateTime.tm_mon = month - 1;
   rawDateTime.tm_mday = day;
   rawDateTime.tm_hour = hour;
   rawDateTime.tm_min = minute;
   rawDateTime.tm_sec = second;
   rawDateTime.tm_wday = 0;
   rawDateTime.tm_yday = 0;
   rawDateTime.tm_isdst = 0;

   // Adjust the retrieved time to the local timezone
   time_t rawtime = mktime(&rawDateTime);
   long offset = 0;
   // TODO: timezone on ThreadX
#if _MSC_VER >= 1900
   long timezone = 0;
   _get_timezone(&timezone);
#endif
#ifndef __THREADX
   if (offsetPrefix == 'Z')
   {
      offset = -timezone;
   }
   else if (offsetPrefix == '-')
   {
      offset = -timezone + ((offsetHour * 60 + offsetMin) * 60);
   }
   else if (offsetPrefix == '+')
   {
      offset = -timezone - ((offsetHour * 60 + offsetMin) * 60);
   }
#endif
   time_t adjustedTime = rawtime + offset;

   return adjustedTime;
}

bool BroadsoftXsiImpl::parseCallLogType(xmlNodePtr cur, CallLogType callLogType, cpc::vector<CallLog>& callLogs)
{
   CallLogType currentLogType;
   // Verify CallLogType
   if (!xmlStrcmp(cur->name, (const xmlChar *) "missed") && (callLogType == CallLogType_All || callLogType == CallLogType_Missed))
   {
      currentLogType = CallLogType_Missed;
   }
   else if (!xmlStrcmp(cur->name, (const xmlChar *) "placed") && (callLogType == CallLogType_All || callLogType == CallLogType_Placed))
   {
      currentLogType = CallLogType_Placed;
   }
   else if (!xmlStrcmp(cur->name, (const xmlChar *) "received") && (callLogType == CallLogType_All || callLogType == CallLogType_Received))
   {
      currentLogType = CallLogType_Received;
   }
   else
   {
      return true;
   }

   xmlNodePtr callLogsEntry = cur->children;
   xmlNodePtr callLogDetail;
   CallLog callLog;
   while (callLogsEntry != NULL) {
      // Verify callLogsEntry
      const char* strCallLogsEntry = (m_PreferEnhancedLogs ? "enhancedCallLogsExtendedEntry" : "callLogsEntry");
      if (xmlStrcmp(callLogsEntry->name, (xmlChar*)strCallLogsEntry))
      {
         ErrLog(<< "BroadsoftXsiImpl::parseCallLogType() - Parsing XML failed. Expected \"" << strCallLogsEntry << "\". Actual: " << (const char*)callLogsEntry->name);
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_CallLogs, evt);
         return false;
      }

      callLog.reset();
      callLog.callLogType = currentLogType;
      time_t durationTime = 0;
      callLogDetail = callLogsEntry->children;
      while (callLogDetail != NULL) {
         if (!xmlStrcmp(callLogDetail->name, (const xmlChar *) "callLogId"))
         { // Both
            if (callLogDetail->children != NULL && callLogDetail->children->type == XML_TEXT_NODE)
            {
               callLog.callLogId = (const char*)(callLogDetail->children->content);
            }
         }
         else if (!xmlStrcmp(callLogDetail->name, (const xmlChar *) "name"))
         { // Basic
            if (callLogDetail->children != NULL && callLogDetail->children->type == XML_TEXT_NODE)
            {
               callLog.name = (const char*)(callLogDetail->children->content);
            }
         }
         else if (!xmlStrcmp(callLogDetail->name, (const xmlChar *) "phoneNumber"))
         { // Basic
            if (callLogDetail->children != NULL && callLogDetail->children->type == XML_TEXT_NODE)
            {
               callLog.phoneNumber = (const char*)(callLogDetail->children->content);
            }
         }
         else if (!xmlStrcmp(callLogDetail->name, (const xmlChar *) "countryCode"))
         { // Both
            if (callLogDetail->children != NULL && callLogDetail->children->type == XML_TEXT_NODE)
            {
               callLog.countryCode = (const char*)(callLogDetail->children->content);
            }
         }
         else if (!xmlStrcmp(callLogDetail->name, (const xmlChar *) "time"))
         { // Both
            if (callLogDetail->children != NULL && callLogDetail->children->type == XML_TEXT_NODE)
            {
               callLog.time = extractDateTime((const char*)(callLogDetail->children->content));
            }
         }
         else if (m_PreferEnhancedLogs)
         {
            if (!xmlStrcmp(callLogDetail->name, (const xmlChar *) "answerTime"))
            {
               if (callLogDetail->children != NULL && callLogDetail->children->type == XML_TEXT_NODE)
               {
                  if (durationTime == 0)
                  {
                     durationTime = atoll((const char*)(callLogDetail->children->content));
                  }
                  else
                  {
                     callLog.duration = (durationTime - atoll((const char*)(callLogDetail->children->content))) / 1000; // ms to s
                  }
               }
            }
            else if (!xmlStrcmp(callLogDetail->name, (const xmlChar *) "releaseTime"))
            {
               if (callLogDetail->children != NULL && callLogDetail->children->type == XML_TEXT_NODE)
               {
                  if (durationTime == 0)
                  {
                     durationTime = atoll((const char*)(callLogDetail->children->content));
                  }
                  else
                  {
                     callLog.duration = (atoll((const char*)(callLogDetail->children->content)) - durationTime) / 1000; // ms to s
                  }
               }
            }
            else if (!xmlStrcmp(callLogDetail->name, (const xmlChar *) "connectedNumber") && currentLogType == CallLogType_Placed)
            { // Unsure if correct field to use for all types.
               if (callLogDetail->children != NULL && callLogDetail->children->type == XML_TEXT_NODE)
               {
                  callLog.phoneNumber = (const char*)(callLogDetail->children->content);
                  if (callLog.name == "") //in case name isn't present default to show the number
                  {
                     callLog.name = (const char*)(callLogDetail->children->content);
                  }
               }
            }
            else if (!xmlStrcmp(callLogDetail->name, (const xmlChar *) "connectedName") && currentLogType == CallLogType_Placed)
            { // Unsure if correct field to use for all types. Missing from placed
               if (callLogDetail->children != NULL && callLogDetail->children->type == XML_TEXT_NODE)
               {
                  callLog.name = (const char*)(callLogDetail->children->content);
               }
            }
            else if (!xmlStrcmp(callLogDetail->name, (const xmlChar *) "calledDirectoryName") && currentLogType == CallLogType_Placed)
            {
               if (callLogDetail->children != NULL && callLogDetail->children->type == XML_TEXT_NODE)
               {
                  callLog.name = (const char*)(callLogDetail->children->content);
               }
            }
			else if (!xmlStrcmp(callLogDetail->name, (const xmlChar *) "callingPresentationNumber") && currentLogType != CallLogType_Placed)
			{
				if (callLogDetail->children != NULL && callLogDetail->children->type == XML_TEXT_NODE)
				{
					callLog.phoneNumber = (const char*)(callLogDetail->children->content);
				}
			}
			else if (!xmlStrcmp(callLogDetail->name, (const xmlChar *) "callingPresentationName") && currentLogType != CallLogType_Placed)
			{
				if (callLogDetail->children != NULL && callLogDetail->children->type == XML_TEXT_NODE)
				{
					callLog.name = (const char*)(callLogDetail->children->content);
				}
			}
            else
            {
               // Unused enhanced call log entry field.
            }
         }
         else
         {
            WarningLog(<< "BroadsoftXsiImpl::parseCallLogType() - Unknown call log entry field: " << (const char*)callLogDetail->name);
         }

         callLogDetail = callLogDetail->next;
      }

      auto it = callLogs.begin();
      for (; it != callLogs.end(); ++it)
      {
         if (it->time > callLog.time)
            continue;
         else
            break;
      }
      callLogs.insert(it, callLog);

      callLogsEntry = callLogsEntry->next;
   }

   return true;
}

const char* BroadsoftXsiImpl::callLogTypeToString(CallLogType lType)
{
   if (lType == CallLogType_All)
      return "All";
   else if (lType == CallLogType_Missed)
      return "Missed";
   else if (lType == CallLogType_Placed)
      return "Placed";
   else if (lType == CallLogType_Received)
      return "Received";
   else
      return "Unknown";
}

bool BroadsoftXsiImpl::parseEnterpriseDirectory(std::string contentType, std::string responseBody)
{
   bool success = true;
   bool moreResultsAvailable = false;
   cpc::vector<DirectoryDetail> dirDetails;
   if (contentType.find("application/xml") != std::string::npos)
   {
      // Parse XML
      xmlDocPtr doc = xmlParseMemory(responseBody.data(), (int)responseBody.size());

      if (doc == NULL)
      {
         ErrLog(<< "BroadsoftXsiImpl::parseEnterpriseDirectory() - Parsing XML failed.");
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_EnterpriseDirectory, evt);
         return false;
      }
      xmlNodePtr cur = doc->children;
      // Verify Enterprise entity
      if (xmlStrcmp(cur->name, (const xmlChar *) "Enterprise"))
      {
         ErrLog(<< "BroadsoftXsiImpl::parseCallLogs() - Parsing XML failed. Expected \"Enterprise\". Actual: " << (const char*)cur->name);
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_EnterpriseDirectory, evt);
         success = false;
      }
      else
      {
         int startIndex = -1;
         int numberOfRecords = -1;
         int totalAvailableRecords = -1;
         cur = cur->children;
         while (cur != NULL) {
            if (!xmlStrcmp(cur->name, (const xmlChar *) "startIndex"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  startIndex = atoi((const char*)(cur->children->content));
               }
            }
            else if (!xmlStrcmp(cur->name, (const xmlChar *) "numberOfRecords"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  numberOfRecords = atoi((const char*)(cur->children->content));
               }
            }
            else if (!xmlStrcmp(cur->name, (const xmlChar *) "totalAvailableRecords"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  totalAvailableRecords = atoi((const char*)(cur->children->content));
               }
            }
            else if (!xmlStrcmp(cur->name, (const xmlChar *) "enterpriseDirectory"))
            {
               if (!parseDirectoryDetails(cur, dirDetails))
               {
                  // Failed to parse directory details. Don't need to log error or callback, parseDirectoryDetails should have.
                  success = false;
                  cur = NULL;
                  continue;
               }
            }
            cur = cur->next;
         }
         if (startIndex != -1 && numberOfRecords != -1 && totalAvailableRecords != -1)
         {
            if (startIndex + numberOfRecords - 1 < totalAvailableRecords)
            {
               moreResultsAvailable = true;
               ErrLog(<< "BroadsoftXsiImpl::parseEnterpriseDirectory() - Received paging response. Need to request more records!");
            }
         }
      }

      xmlFreeDoc(doc);
   }
   else
   {
      ErrLog(<< "BroadsoftXsiImpl::parseEnterpriseDirectory() - Parsing content type (" << contentType << ") unsupported.");
      BroadsoftXsiFailureEvent evt;
      evt.failureReason = BroadsoftXsiFailureReason_Malformed;
      fireEvent(XsiAction_EnterpriseDirectory, evt);
   }

   if (success)
   {
      if (m_Settings.logParsedResponse)
      {
         DebugLog(<< "BroadsoftXsiImpl::parseEnterpriseDirectory() - Parsed enterprise directory!");
         for (auto it = dirDetails.begin(); it != dirDetails.end(); ++it)
         {
            cpc::string detailLog(" ");
            bool addComma = false;
            if (!it->bridgeId.empty())
            {
               detailLog.append("Bridge id: ");
               detailLog.append(it->bridgeId);
               addComma = true;
            }
            if (!it->department.empty())
            {
               if (addComma)
                  detailLog.append(", ");
               else
                  addComma = true;
               detailLog.append("Department: ");
               detailLog.append(it->department);
            }
            if (!it->emailAddress.empty())
            {
               if (addComma)
                  detailLog.append(", ");
               else
                  addComma = true;
               detailLog.append("Email address: ");
               detailLog.append(it->emailAddress);
            }
            if (!it->extension.empty())
            {
               if (addComma)
                  detailLog.append(", ");
               else
                  addComma = true;
               detailLog.append("Extension: ");
               detailLog.append(it->extension);
            }
            if (!it->firstName.empty())
            {
               if (addComma)
                  detailLog.append(", ");
               else
                  addComma = true;
               detailLog.append("First name: ");
               detailLog.append(it->firstName);
            }
            if (!it->groupId.empty())
            {
               if (addComma)
                  detailLog.append(", ");
               else
                  addComma = true;
               detailLog.append("Group id: ");
               detailLog.append(it->groupId);
            }
            if (!it->impId.empty())
            {
               if (addComma)
                  detailLog.append(", ");
               else
                  addComma = true;
               detailLog.append("Imp id: ");
               detailLog.append(it->impId);
            }
            if (!it->lastName.empty())
            {
               if (addComma)
                  detailLog.append(", ");
               else
                  addComma = true;
               detailLog.append("Last name: ");
               detailLog.append(it->lastName);
            }
            if (!it->mobileNo.empty())
            {
               if (addComma)
                  detailLog.append(", ");
               else
                  addComma = true;
               detailLog.append("Mobile #: ");
               detailLog.append(it->mobileNo);
            }
            if (!it->number.empty())
            {
               if (addComma)
                  detailLog.append(", ");
               else
                  addComma = true;
               detailLog.append("Number: ");
               detailLog.append(it->number);
            }
            if (!it->roomId.empty())
            {
               if (addComma)
                  detailLog.append(", ");
               else
                  addComma = true;
               detailLog.append("Room id: ");
               detailLog.append(it->roomId);
            }
            if (!it->userId.empty())
            {
               if (addComma)
                  detailLog.append(", ");
               else
                  addComma = true;
               detailLog.append("User id: ");
               detailLog.append(it->userId);
            }
            if (!it->yahooId.empty())
            {
               if (addComma)
                  detailLog.append(", ");
               else
                  addComma = true;
               detailLog.append("Yahoo id: ");
               detailLog.append(it->yahooId);
            }
            if (!it->additionalDetails.empty())
            {
               if (addComma)
                  detailLog.append(", ");
               addComma = false;
               detailLog.append("Additional Details: {");
               for (auto it2 = it->additionalDetails.begin(); it2 != it->additionalDetails.end(); ++it2)
               {
                  if (addComma)
                     detailLog.append(", ");
                  else
                     addComma = true;
                  detailLog.append(it2->name);
                  detailLog.append(":");
                  detailLog.append(it2->value);
               }
               detailLog.append("}");
               addComma = true;
               
               (void)addComma;
            }
            DebugLog(<< detailLog);
         }
      }

      QueryEnterpriseDirectorySuccessEvent evt;
      evt.details = dirDetails;
      evt.moreResultsAvailable = moreResultsAvailable;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryEnterpriseDirectory), evt);
   }
   return success;
}

bool BroadsoftXsiImpl::parseDirectoryDetails(xmlNodePtr cur, cpc::vector<DirectoryDetail>& dirDetails)
{
   xmlNodePtr detailsNode = cur->children;
   xmlNodePtr detailNode;
   DirectoryDetail dirDetail;
   while (detailsNode != NULL) {
      // Verify directoryDetails
      if (xmlStrcmp(detailsNode->name, (const xmlChar *) "directoryDetails"))
      {
         ErrLog(<< "BroadsoftXsiImpl::parseDirectoryDetails() - Parsing XML failed. Expected \"directoryDetails\". Actual: " << (const char*)detailsNode->name);
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_EnterpriseDirectory, evt);
         return false;
      }

      bool additionalDetails = false;
      dirDetail.reset();
      detailNode = detailsNode->children;
      while (detailNode != NULL) {
         if (!xmlStrcmp(detailNode->name, (const xmlChar *) "bridgeId"))
         {
            if (detailNode->children != NULL && detailNode->children->type == XML_TEXT_NODE)
            {
               dirDetail.bridgeId = (const char*)(detailNode->children->content);
            }
         }
         else if (!xmlStrcmp(detailNode->name, (const xmlChar *) "department"))
         {
            if (detailNode->children != NULL && detailNode->children->type == XML_TEXT_NODE)
            {
               dirDetail.department = (const char*)(detailNode->children->content);
            }
         }
         else if (!xmlStrcmp(detailNode->name, (const xmlChar *) "emailAddress"))
         {
            if (detailNode->children != NULL && detailNode->children->type == XML_TEXT_NODE)
            {
               dirDetail.emailAddress = (const char*)(detailNode->children->content);
            }
         }
         else if (!xmlStrcmp(detailNode->name, (const xmlChar *) "extension"))
         {
            if (detailNode->children != NULL && detailNode->children->type == XML_TEXT_NODE)
            {
               dirDetail.extension = (const char*)(detailNode->children->content);
            }
         }
         else if (!xmlStrcmp(detailNode->name, (const xmlChar *) "firstName"))
         {
            if (detailNode->children != NULL && detailNode->children->type == XML_TEXT_NODE)
            {
               dirDetail.firstName = (const char*)(detailNode->children->content);
            }
         }
         else if (!xmlStrcmp(detailNode->name, (const xmlChar *) "groupId"))
         {
            if (detailNode->children != NULL && detailNode->children->type == XML_TEXT_NODE)
            {
               dirDetail.groupId = (const char*)(detailNode->children->content);
            }
         }
         else if (!xmlStrcmp(detailNode->name, (const xmlChar *) "hiranganaFirstName"))
         {
            // Unsupported
         }
         else if (!xmlStrcmp(detailNode->name, (const xmlChar *) "hiranganaLastName"))
         {
            // Unsupported
         }
         else if (!xmlStrcmp(detailNode->name, (const xmlChar *) "impId"))
         {
            if (detailNode->children != NULL && detailNode->children->type == XML_TEXT_NODE)
            {
               dirDetail.impId = (const char*)(detailNode->children->content);
            }
         }
         else if (!xmlStrcmp(detailNode->name, (const xmlChar *) "lastName"))
         {
            if (detailNode->children != NULL && detailNode->children->type == XML_TEXT_NODE)
            {
               dirDetail.lastName = (const char*)(detailNode->children->content);
            }
         }
         else if (!xmlStrcmp(detailNode->name, (const xmlChar *) "mobileNo"))
         {
            if (detailNode->children != NULL && detailNode->children->type == XML_TEXT_NODE)
            {
               dirDetail.mobileNo = (const char*)(detailNode->children->content);
            }
         }
         else if (!xmlStrcmp(detailNode->name, (const xmlChar *) "number"))
         {
            if (detailNode->children != NULL && detailNode->children->type == XML_TEXT_NODE)
            {
               dirDetail.number = (const char*)(detailNode->children->content);
            }
         }
         else if (!xmlStrcmp(detailNode->name, (const xmlChar *) "roomId"))
         {
            if (detailNode->children != NULL && detailNode->children->type == XML_TEXT_NODE)
            {
               dirDetail.roomId = (const char*)(detailNode->children->content);
            }
         }
         else if (!xmlStrcmp(detailNode->name, (const xmlChar *) "firstNameUnicode"))
         {
            // Unsupported
         }
         else if (!xmlStrcmp(detailNode->name, (const xmlChar *) "lastNameUnicode"))
         {
            // Unsupported
         }
         else if (!xmlStrcmp(detailNode->name, (const xmlChar *) "userId"))
         {
            if (detailNode->children != NULL && detailNode->children->type == XML_TEXT_NODE)
            {
               dirDetail.userId = (const char*)(detailNode->children->content);
            }
         }
         else if (!xmlStrcmp(detailNode->name, (const xmlChar *) "yahooId"))
         {
            if (detailNode->children != NULL && detailNode->children->type == XML_TEXT_NODE)
            {
               dirDetail.yahooId = (const char*)(detailNode->children->content);
            }
         }
         else if (!xmlStrcmp(detailNode->name, (const xmlChar *) "additionalDetails"))
         {
            if (detailNode->children != NULL)
            {
               // Jump into additional details
               additionalDetails = true;
               detailNode = detailNode->children;
               continue;
            }
         }
         else if (additionalDetails)
         {
            WarningLog(<< "BroadsoftXsiImpl::parseDirectoryDetails() - Unknown directory additional detail field: " << (const char*)detailNode->name);
            if (detailNode->children != NULL && detailNode->children->type == XML_TEXT_NODE)
            {
               DirectoryAdditionalDetail additionalDetail = { (const char*)(detailNode->name), (const char*)(detailNode->children->content) };
               dirDetail.additionalDetails.push_back(additionalDetail);
            }
         }
         else
         {
            WarningLog(<< "BroadsoftXsiImpl::parseDirectoryDetails() - Unknown directory detail field: " << (const char*)detailNode->name);
         }

         if (additionalDetails && detailNode->next == NULL)
         {
            // Reached end of additional details, jump out
            detailNode = detailNode->parent;
            additionalDetails = false;
         }
         detailNode = detailNode->next;
      }

      dirDetails.push_back(dirDetail);

      detailsNode = detailsNode->next;
   }

   return true;
}

bool BroadsoftXsiImpl::parseBroadworksAnywhere(std::string contentType, std::string responseBody)
{
   bool success = true;
   BroadworksAnywhere settings;
   if (contentType.find("application/xml") != std::string::npos)
   {
      // Parse XML
      xmlDocPtr doc = xmlParseMemory(responseBody.data(), (int)responseBody.size());

      if (doc == NULL)
      {
         ErrLog(<< "BroadsoftXsiImpl::parseBroadworksAnywhere() - Parsing XML failed.");
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_QueryBroadworksAnywhereSettings, evt);
         return false;
      }
      xmlNodePtr cur = doc->children;
      // Verify BroadWorksAnywhere entity
      if (xmlStrcmp(cur->name, (const xmlChar *) "BroadWorksAnywhere"))
      {
         ErrLog(<< "BroadsoftXsiImpl::parseBroadworksAnywhere() - Parsing XML failed. Expected \"BroadWorksAnywhere\". Actual: " << (const char*)cur->name);
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_QueryBroadworksAnywhereSettings, evt);
         success = false;
      }
      else
      {
         cur = cur->children;
         while (cur != NULL) {
            if (!xmlStrcmp(cur->name, (const xmlChar *) "alertAllLocationsForClickToDialCalls"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  settings.alertAllLocationsForClickToDialCalls = !xmlStrcmp(cur->children->content, (const xmlChar *) "true");
               }
            }
            else if (!xmlStrcmp(cur->name, (const xmlChar *) "alertAllLocationsForGroupPagingCalls"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  settings.alertAllLocationsForGroupPagingCalls = !xmlStrcmp(cur->children->content, (const xmlChar *) "true");
               }
            }
            else if (!xmlStrcmp(cur->name, (const xmlChar *) "locations"))
            {
               xmlNodePtr locationsNode = cur->children;
               xmlNodePtr locationNode;
               BroadworksAnywhereLocation location;
               while (locationsNode != NULL) {
                  // Verify Broadworks Anywhere locations
                  if (xmlStrcmp(locationsNode->name, (const xmlChar *) "location"))
                  {
                     ErrLog(<< "BroadsoftXsiImpl::parseBroadworksAnywhere() - Parsing XML failed. Expected \"location\". Actual: " << (const char*)locationsNode->name);
                     BroadsoftXsiFailureEvent evt;
                     evt.failureReason = BroadsoftXsiFailureReason_Malformed;
                     fireEvent(XsiAction_QueryBroadworksAnywhereSettings, evt);
                     return false;
                  }

                  location.reset();
                  locationNode = locationsNode->children;
                  while (locationNode != NULL) {
                     if (!xmlStrcmp(locationNode->name, (const xmlChar *) "active"))
                     {
                        if (locationNode->children != NULL && locationNode->children->type == XML_TEXT_NODE)
                        {
                           location.active = !xmlStrcmp(locationNode->children->content, (const xmlChar *) "true");
                        }
                     }
                     else if (!xmlStrcmp(locationNode->name, (const xmlChar *) "description"))
                     {
                        if (locationNode->children != NULL && locationNode->children->type == XML_TEXT_NODE)
                        {
                           location.description = (const char*)(locationNode->children->content);
                        }
                     }
                     else if (!xmlStrcmp(locationNode->name, (const xmlChar *) "phoneNumber"))
                     {
                        if (locationNode->children != NULL && locationNode->children->type == XML_TEXT_NODE)
                        {
                           location.phoneNumber = (const char*)(locationNode->children->content);
                        }
                     }
                     else
                     {
                        WarningLog(<< "BroadsoftXsiImpl::parseBroadworksAnywhere() - Unknown location field: " << (const char*)locationNode->name);
                     }

                     locationNode = locationNode->next;
                  }

                  settings.locations.push_back(location);

                  locationsNode = locationsNode->next;
               }
            }
            else
            {
               WarningLog(<< "BroadsoftXsiImpl::parseBroadworksAnywhere() - Received unknown setting: " << (const char*)cur->name);
            }
            cur = cur->next;
         }
      }

      xmlFreeDoc(doc);
   }
   else
   {
      ErrLog(<< "BroadsoftXsiImpl::parseBroadworksAnywhere() - Parsing content type (" << contentType << ") unsupported.");
      BroadsoftXsiFailureEvent evt;
      evt.failureReason = BroadsoftXsiFailureReason_Malformed;
      fireEvent(XsiAction_QueryBroadworksAnywhereSettings, evt);
   }

   if (success)
   {
      if (m_Settings.logParsedResponse)
      {
         cpc::string detailLog("BroadsoftXsiImpl::parseBroadworksAnywhere() - ");
         detailLog.append("alertAllLocationsForClickToDialCalls: ");
         detailLog.append(settings.alertAllLocationsForClickToDialCalls ? "true" : "false");
         detailLog.append(", alertAllLocationsForGroupPagingCalls: ");
         detailLog.append(settings.alertAllLocationsForGroupPagingCalls ? "true" : "false");
         if (!settings.locations.empty())
         {
            detailLog.append(", locations: [");
            bool addComma = false;
            for (auto it = settings.locations.begin(); it != settings.locations.end(); ++it)
            {
               if (addComma)
                  detailLog.append(",");
               else
                  addComma = true;
               detailLog.append("{number: ");
               detailLog.append(it->phoneNumber);
               detailLog.append(", enabled: ");
               detailLog.append(it->active ? "true" : "false");
               detailLog.append(", description: ");
               detailLog.append(it->description);
               detailLog.append("}");
            }
            detailLog.append("]");
         }
         DebugLog(<< detailLog);
      }

      // Cache latest response
      m_BroadworksAnywhere = settings;

      QueryBroadworksAnywhereSettingsSuccessEvent evt;
      evt.settings = settings;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryBroadworksAnywhereSettings), evt);
   }
   return success;
}

bool BroadsoftXsiImpl::parseCallForwardAlways(std::string contentType, std::string responseBody)
{
   bool success = true;
   CallForwardAlways settings;
   if (contentType.find("application/xml") != std::string::npos)
   {
      // Parse XML
      xmlDocPtr doc = xmlParseMemory(responseBody.data(), (int)responseBody.size());

      if (doc == NULL)
      {
         ErrLog(<< "BroadsoftXsiImpl::parseCallForwardAlways() - Parsing XML failed.");
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_QueryCallForwardingAlwaysSettings, evt);
         return false;
      }
      xmlNodePtr cur = doc->children;
      // Verify CallForwardingAlways entity
      if (xmlStrcmp(cur->name, (const xmlChar *) "CallForwardingAlways"))
      {
         ErrLog(<< "BroadsoftXsiImpl::parseCallForwardAlways() - Parsing XML failed. Expected \"CallForwardingAlways\". Actual: " << (const char*)cur->name);
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_QueryCallForwardingAlwaysSettings, evt);
         success = false;
      }
      else
      {
         cur = cur->children;
         while (cur != NULL) {
            if (!xmlStrcmp(cur->name, (const xmlChar *) "active"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  settings.enabled = !xmlStrcmp(cur->children->content, (const xmlChar *) "true");
               }
            }
            else if (!xmlStrcmp(cur->name, (const xmlChar *) "forwardToPhoneNumber"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  settings.phoneNumber = (const char*)(cur->children->content);
               }
            }
            else if (!xmlStrcmp(cur->name, (const xmlChar *) "ringSplash"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  settings.ringSplash = !xmlStrcmp(cur->children->content, (const xmlChar *) "true");
               }
            }
            else
            {
               WarningLog(<< "BroadsoftXsiImpl::parseCallForwardAlways() - Received unknown setting: " << (const char*)cur->name);
            }
            cur = cur->next;
         }
      }

      xmlFreeDoc(doc);
   }
   else
   {
      ErrLog(<< "BroadsoftXsiImpl::parseCallForwardAlways() - Parsing content type (" << contentType << ") unsupported.");
      BroadsoftXsiFailureEvent evt;
      evt.failureReason = BroadsoftXsiFailureReason_Malformed;
      fireEvent(XsiAction_QueryCallForwardingAlwaysSettings, evt);
   }

   if (success)
   {
      if (m_Settings.logParsedResponse)
      {
         cpc::string detailLog("BroadsoftXsiImpl::parseCallForwardAlways() - ");
         detailLog.append("enabled: ");
         detailLog.append(settings.enabled ? "true" : "false");
         detailLog.append(", phoneNumber: ");
         detailLog.append(settings.phoneNumber);
         detailLog.append(", ringSplash: ");
         detailLog.append(settings.ringSplash ? "true" : "false");
         DebugLog(<< detailLog);
      }

      QueryCallForwardAlwaysSettingsSuccessEvent evt;
      evt.settings = settings;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryCallForwardAlwaysSettings), evt);
   }
   return success;
}

bool BroadsoftXsiImpl::parseCallForwardBusy(std::string contentType, std::string responseBody)
{
   bool success = true;
   CallForwardBusy settings;
   if (contentType.find("application/xml") != std::string::npos)
   {
      // Parse XML
      xmlDocPtr doc = xmlParseMemory(responseBody.data(), (int)responseBody.size());

      if (doc == NULL)
      {
         ErrLog(<< "BroadsoftXsiImpl::parseCallForwardBusy() - Parsing XML failed.");
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_QueryCallForwardingBusySettings, evt);
         return false;
      }
      xmlNodePtr cur = doc->children;
      // Verify CallForwardingBusy entity
      if (xmlStrcmp(cur->name, (const xmlChar *) "CallForwardingBusy"))
      {
         ErrLog(<< "BroadsoftXsiImpl::parseCallForwardBusy() - Parsing XML failed. Expected \"CallForwardingBusy\". Actual: " << (const char*)cur->name);
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_QueryCallForwardingBusySettings, evt);
         success = false;
      }
      else
      {
         cur = cur->children;
         while (cur != NULL) {
            if (!xmlStrcmp(cur->name, (const xmlChar *) "active"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  settings.enabled = !xmlStrcmp(cur->children->content, (const xmlChar *) "true");
               }
            }
            else if (!xmlStrcmp(cur->name, (const xmlChar *) "forwardToPhoneNumber"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  settings.phoneNumber = (const char*)(cur->children->content);
               }
            }
            else
            {
               WarningLog(<< "BroadsoftXsiImpl::parseCallForwardBusy() - Received unknown setting: " << (const char*)cur->name);
            }
            cur = cur->next;
         }
      }

      xmlFreeDoc(doc);
   }
   else
   {
      ErrLog(<< "BroadsoftXsiImpl::parseCallForwardBusy() - Parsing content type (" << contentType << ") unsupported.");
      BroadsoftXsiFailureEvent evt;
      evt.failureReason = BroadsoftXsiFailureReason_Malformed;
      fireEvent(XsiAction_QueryCallForwardingBusySettings, evt);
   }

   if (success)
   {
      if (m_Settings.logParsedResponse)
      {
         cpc::string detailLog("BroadsoftXsiImpl::parseCallForwardBusy() - ");
         detailLog.append("enabled: ");
         detailLog.append(settings.enabled ? "true" : "false");
         detailLog.append(", phoneNumber: ");
         detailLog.append(settings.phoneNumber);
         DebugLog(<< detailLog);
      }

      QueryCallForwardBusySettingsSuccessEvent evt;
      evt.settings = settings;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryCallForwardBusySettings), evt);
   }
   return success;
}

bool BroadsoftXsiImpl::parseCallForwardNoAnswer(std::string contentType, std::string responseBody)
{
   bool success = true;
   CallForwardNoAnswer settings;
   if (contentType.find("application/xml") != std::string::npos)
   {
      // Parse XML
      xmlDocPtr doc = xmlParseMemory(responseBody.data(), (int)responseBody.size());

      if (doc == NULL)
      {
         ErrLog(<< "BroadsoftXsiImpl::parseCallForwardNoAnswer() - Parsing XML failed.");
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_QueryCallForwardingNoAnswerSettings, evt);
         return false;
      }
      xmlNodePtr cur = doc->children;
      // Verify CallForwardingNoAnswer entity
      if (xmlStrcmp(cur->name, (const xmlChar *) "CallForwardingNoAnswer"))
      {
         ErrLog(<< "BroadsoftXsiImpl::parseCallForwardNoAnswer() - Parsing XML failed. Expected \"CallForwardingNoAnswer\". Actual: " << (const char*)cur->name);
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_QueryCallForwardingNoAnswerSettings, evt);
         success = false;
      }
      else
      {
         cur = cur->children;
         while (cur != NULL) {
            if (!xmlStrcmp(cur->name, (const xmlChar *) "active"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  settings.enabled = !xmlStrcmp(cur->children->content, (const xmlChar *) "true");
               }
            }
            else if (!xmlStrcmp(cur->name, (const xmlChar *) "forwardToPhoneNumber"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  settings.phoneNumber = (const char*)(cur->children->content);
               }
            }
            else if (!xmlStrcmp(cur->name, (const xmlChar *) "numberOfRings"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  settings.numberOfRings = atoi((const char*)(cur->children->content));
               }
            }
            else
            {
               WarningLog(<< "BroadsoftXsiImpl::parseCallForwardNoAnswer() - Received unknown setting: " << (const char*)cur->name);
            }
            cur = cur->next;
         }
      }

      xmlFreeDoc(doc);
   }
   else
   {
      ErrLog(<< "BroadsoftXsiImpl::parseCallForwardNoAnswer() - Parsing content type (" << contentType << ") unsupported.");
      BroadsoftXsiFailureEvent evt;
      evt.failureReason = BroadsoftXsiFailureReason_Malformed;
      fireEvent(XsiAction_QueryCallForwardingNoAnswerSettings, evt);
   }

   if (success)
   {
      if (m_Settings.logParsedResponse)
      {
         cpc::string detailLog("BroadsoftXsiImpl::parseCallForwardNoAnswer() - ");
         detailLog.append("enabled: ");
         detailLog.append(settings.enabled ? "true" : "false");
         detailLog.append(", phoneNumber: ");
         detailLog.append(settings.phoneNumber);
         detailLog.append(", numberOfRings: ");
         char numberOfRings[4];
         snprintf(numberOfRings, 4, "%u", settings.numberOfRings);
         detailLog.append(numberOfRings);
         DebugLog(<< detailLog);
      }

      QueryCallForwardNoAnswerSettingsSuccessEvent evt;
      evt.settings = settings;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryCallForwardNoAnswerSettings), evt);
   }
   return success;
}

bool BroadsoftXsiImpl::parseDoNotDisturb(std::string contentType, std::string responseBody)
{
   bool success = true;
   DoNotDisturb settings;
   if (contentType.find("application/xml") != std::string::npos)
   {
      // Parse XML
      xmlDocPtr doc = xmlParseMemory(responseBody.data(), (int)responseBody.size());

      if (doc == NULL)
      {
         ErrLog(<< "BroadsoftXsiImpl::parseDoNotDisturb() - Parsing XML failed.");
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_QueryDoNotDisturbSettings, evt);
         return false;
      }
      xmlNodePtr cur = doc->children;
      // Verify DoNotDisturb entity
      if (xmlStrcmp(cur->name, (const xmlChar *) "DoNotDisturb"))
      {
         ErrLog(<< "BroadsoftXsiImpl::parseDoNotDisturb() - Parsing XML failed. Expected \"DoNotDisturb\". Actual: " << (const char*)cur->name);
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_QueryDoNotDisturbSettings, evt);
         success = false;
      }
      else
      {
         cur = cur->children;
         while (cur != NULL) {
            if (!xmlStrcmp(cur->name, (const xmlChar *) "active"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  settings.enabled = !xmlStrcmp(cur->children->content, (const xmlChar *) "true");
               }
            }
            else if (!xmlStrcmp(cur->name, (const xmlChar *) "ringSplash"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  settings.ringSplash = !xmlStrcmp(cur->children->content, (const xmlChar *) "true");
               }
            }
            else
            {
               WarningLog(<< "BroadsoftXsiImpl::parseDoNotDisturb() - Received unknown setting: " << (const char*)cur->name);
            }
            cur = cur->next;
         }
      }

      xmlFreeDoc(doc);
   }
   else
   {
      ErrLog(<< "BroadsoftXsiImpl::parseDoNotDisturb() - Parsing content type (" << contentType << ") unsupported.");
      BroadsoftXsiFailureEvent evt;
      evt.failureReason = BroadsoftXsiFailureReason_Malformed;
      fireEvent(XsiAction_QueryDoNotDisturbSettings, evt);
   }

   if (success)
   {
      if (m_Settings.logParsedResponse)
      {
         cpc::string detailLog("BroadsoftXsiImpl::parseDoNotDisturb() - ");
         detailLog.append("enabled: ");
         detailLog.append(settings.enabled ? "true" : "false");
         detailLog.append(", ringSplash: ");
         detailLog.append(settings.ringSplash ? "true" : "false");
         DebugLog(<< detailLog);
      }

      QueryDoNotDisturbSettingsSuccessEvent evt;
      evt.settings = settings;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryDoNotDisturbSettings), evt);
   }
   return success;
}

bool BroadsoftXsiImpl::parseRemoteOffice(std::string contentType, std::string responseBody)
{
   bool success = true;
   RemoteOffice settings;
   if (contentType.find("application/xml") != std::string::npos)
   {
      // Parse XML
      xmlDocPtr doc = xmlParseMemory(responseBody.data(), (int)responseBody.size());

      if (doc == NULL)
      {
         ErrLog(<< "BroadsoftXsiImpl::parseRemoteOffice() - Parsing XML failed.");
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_QueryRemoteOfficeSettings, evt);
         return false;
      }
      xmlNodePtr cur = doc->children;
      // Verify RemoteOffice entity
      if (xmlStrcmp(cur->name, (const xmlChar *) "RemoteOffice"))
      {
         ErrLog(<< "BroadsoftXsiImpl::parseRemoteOffice() - Parsing XML failed. Expected \"RemoteOffice\". Actual: " << (const char*)cur->name);
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_QueryRemoteOfficeSettings, evt);
         success = false;
      }
      else
      {
         cur = cur->children;
         while (cur != NULL) {
            if (!xmlStrcmp(cur->name, (const xmlChar *) "active"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  settings.enabled = !xmlStrcmp(cur->children->content, (const xmlChar *) "true");
               }
            }
            else if (!xmlStrcmp(cur->name, (const xmlChar *) "remoteOfficeNumber"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  settings.phoneNumber = (const char*)(cur->children->content);
               }
            }
            else
            {
               WarningLog(<< "BroadsoftXsiImpl::parseRemoteOffice() - Received unknown setting: " << (const char*)cur->name);
            }
            cur = cur->next;
         }
      }

      xmlFreeDoc(doc);
   }
   else
   {
      ErrLog(<< "BroadsoftXsiImpl::parseRemoteOffice() - Parsing content type (" << contentType << ") unsupported.");
      BroadsoftXsiFailureEvent evt;
      evt.failureReason = BroadsoftXsiFailureReason_Malformed;
      fireEvent(XsiAction_QueryRemoteOfficeSettings, evt);
   }

   if (success)
   {
      if (m_Settings.logParsedResponse)
      {
         cpc::string detailLog("BroadsoftXsiImpl::parseRemoteOffice() - ");
         detailLog.append("enabled: ");
         detailLog.append(settings.enabled ? "true" : "false");
         detailLog.append(", remoteOfficeNumber: ");
         detailLog.append(settings.phoneNumber);
         DebugLog(<< detailLog);
      }

      QueryRemoteOfficeSettingsSuccessEvent evt;
      evt.settings = settings;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryRemoteOfficeSettings), evt);
   }
   return success;
}

bool BroadsoftXsiImpl::parseSimultaneousRing(std::string contentType, std::string responseBody)
{
   bool success = true;
   SimultaneousRing settings;
   if (contentType.find("application/xml") != std::string::npos)
   {
      // Parse XML
      xmlDocPtr doc = xmlParseMemory(responseBody.data(), (int)responseBody.size());

      if (doc == NULL)
      {
         ErrLog(<< "BroadsoftXsiImpl::parseSimultaneousRing() - Parsing XML failed.");
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_QuerySimultaneousRingSettings, evt);
         return false;
      }
      xmlNodePtr cur = doc->children;
      // Verify SimultaneousRingPersonal entity
      if (xmlStrcmp(cur->name, (const xmlChar *) "SimultaneousRingPersonal"))
      {
         ErrLog(<< "BroadsoftXsiImpl::parseSimultaneousRing() - Parsing XML failed. Expected \"SimultaneousRingPersonal\". Actual: " << (const char*)cur->name);
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_QuerySimultaneousRingSettings, evt);
         success = false;
      }
      else
      {
         cur = cur->children;
         while (cur != NULL) {
            if (!xmlStrcmp(cur->name, (const xmlChar *) "active"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  settings.enabled = !xmlStrcmp(cur->children->content, (const xmlChar *) "true");
               }
            }
            else if (!xmlStrcmp(cur->name, (const xmlChar *) "incomingCalls"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  settings.dontRingWhenOnCall = !xmlStrcmp(cur->children->content, (const xmlChar *) "Do not Ring if on a Call");
               }
            }
            else if (!xmlStrcmp(cur->name, (const xmlChar *) "simRingLocations"))
            {
               xmlNodePtr locationsNode = cur->children;
               xmlNodePtr locationNode;
               SimultaneousRingLocation location;
               while (locationsNode != NULL) {
                  // Verify Simultaneous Ring locations
                  if (xmlStrcmp(locationsNode->name, (const xmlChar *) "simRingLocation"))
                  {
                     ErrLog(<< "BroadsoftXsiImpl::parseSimultaneousRing() - Parsing XML failed. Expected \"simRingLocation\". Actual: " << (const char*)locationsNode->name);
                     BroadsoftXsiFailureEvent evt;
                     evt.failureReason = BroadsoftXsiFailureReason_Malformed;
                     fireEvent(XsiAction_QuerySimultaneousRingSettings, evt);
                     return false;
                  }

                  location.reset();
                  locationNode = locationsNode->children;
                  while (locationNode != NULL) {
                     if (!xmlStrcmp(locationNode->name, (const xmlChar *) "answerConfirmationRequired"))
                     {
                        if (locationNode->children != NULL && locationNode->children->type == XML_TEXT_NODE)
                        {
                           location.answerConfirmationRequired = !xmlStrcmp(locationNode->children->content, (const xmlChar *) "true");
                        }
                     }
                     else if (!xmlStrcmp(locationNode->name, (const xmlChar *) "address"))
                     {
                        if (locationNode->children != NULL && locationNode->children->type == XML_TEXT_NODE)
                        {
                           location.phoneNumber = (const char*)(locationNode->children->content);
                        }
                     }
                     else
                     {
                        WarningLog(<< "BroadsoftXsiImpl::parseSimultaneousRing() - Unknown location field: " << (const char*)locationNode->name);
                     }

                     locationNode = locationNode->next;
                  }

                  settings.locations.push_back(location);

                  locationsNode = locationsNode->next;
               }
            }
            else if (!xmlStrcmp(cur->name, (const xmlChar *) "criteriaActivationList"))
            {
               InfoLog(<< "BroadsoftXsiImpl::parseSimultaneousRing() - criteriaActivationList unsupported.");
            }
            else
            {
               WarningLog(<< "BroadsoftXsiImpl::parseSimultaneousRing() - Received unknown setting: " << (const char*)cur->name);
            }
            cur = cur->next;
         }
      }

      xmlFreeDoc(doc);
   }
   else
   {
      ErrLog(<< "BroadsoftXsiImpl::parseSimultaneousRing() - Parsing content type (" << contentType << ") unsupported.");
      BroadsoftXsiFailureEvent evt;
      evt.failureReason = BroadsoftXsiFailureReason_Malformed;
      fireEvent(XsiAction_QuerySimultaneousRingSettings, evt);
   }

   if (success)
   {
      if (m_Settings.logParsedResponse)
      {
         cpc::string detailLog("BroadsoftXsiImpl::parseSimultaneousRing() - ");
         detailLog.append("enabled: ");
         detailLog.append(settings.enabled ? "true" : "false");
         detailLog.append(", dontRingWhenOnCall: ");
         detailLog.append(settings.dontRingWhenOnCall ? "true" : "false");
         if (!settings.locations.empty())
         {
            detailLog.append(", locations: [");
            bool addComma = false;
            for (auto it = settings.locations.begin(); it != settings.locations.end(); ++it)
            {
               if (addComma)
                  detailLog.append(",");
               else
                  addComma = true;
               detailLog.append("{number: ");
               detailLog.append(it->phoneNumber);
               detailLog.append(", answerConfirmationRequired: ");
               detailLog.append(it->answerConfirmationRequired ? "true" : "false");
               detailLog.append("}");
            }
            detailLog.append("]");
         }
         DebugLog(<< detailLog);
      }

      QuerySimultaneousRingSettingsSuccessEvent evt;
      evt.settings = settings;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQuerySimultaneousRingSettings), evt);
   }
   return success;
}

bool BroadsoftXsiImpl::parseServices(std::string contentType, std::string responseBody)
{
   bool success = true;
   cpc::vector<Service> services;
   if (contentType.find("application/xml") != std::string::npos)
   {
      // Parse XML
      xmlDocPtr doc = xmlParseMemory(responseBody.data(), (int)responseBody.size());

      if (doc == NULL)
      {
         ErrLog(<< "BroadsoftXsiImpl::parseService() - Parsing XML failed.");
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_Services, evt);
         return false;
      }
      xmlNodePtr cur = doc->children;

      while (cur != NULL)
      {
         if (!xmlStrcmp(cur->name, (const xmlChar *) "Services"))
         {
            xmlNodePtr servicesNode = cur->children;
            xmlNodePtr serviceNode;
            while (servicesNode != NULL)
            {
               // Verify Broadworks Anywhere locations
               if (xmlStrcmp(servicesNode->name, (const xmlChar *) "service"))
               {
                  ErrLog(<< "BroadsoftXsiImpl::parseServices() - Parsing XML failed. Expected \"service\". Actual: " << (const char*)servicesNode->name);
                  BroadsoftXsiFailureEvent evt;
                  evt.failureReason = BroadsoftXsiFailureReason_Malformed;
                  fireEvent(XsiAction_Services, evt);
                  return false;
               }

               Service service;
               serviceNode = servicesNode->children;
               while (serviceNode != NULL) {
                  if (!xmlStrcmp(serviceNode->name, (const xmlChar *) "name"))
                  {
                     if (serviceNode->children != NULL && serviceNode->children->type == XML_TEXT_NODE)
                     {
                        service.name = (const char*)(serviceNode->children->content);
                     }
                  }
                  else if (!xmlStrcmp(serviceNode->name, (const xmlChar *) "uri"))
                  {
                     if (serviceNode->children != NULL && serviceNode->children->type == XML_TEXT_NODE)
                     {
                        service.url = (const char*)(serviceNode->children->content);
                     }
                  }
                  serviceNode = serviceNode->next;
               }

               services.push_back(service);

               servicesNode = servicesNode->next;
            }
         }
         else
         {
            WarningLog(<< "BroadsoftXsiImpl::parseServices() - Received unknown setting: " << (const char*)cur->name);
         }
         cur = cur->next;
      }

      xmlFreeDoc(doc);
   }
   else
   {
      ErrLog(<< "BroadsoftXsiImpl::parseSerivces() - Parsing content type (" << contentType << ") unsupported.");
      BroadsoftXsiFailureEvent evt;
      evt.failureReason = BroadsoftXsiFailureReason_Malformed;
      fireEvent(XsiAction_Services, evt);
   }

   if (success)
   {
      if (m_Settings.logParsedResponse)
      {
         DebugLog(<< "BroadsoftXsiImpl::parseServices() - Parsed !");
         for (auto it = services.begin(); it != services.end(); ++it)
         {
            DebugLog(<< " Service Name: " << it->name);
         }
      }

      QueryServicesSuccessEvent evt;
      evt.services = services;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryServices), evt);
   }
   return success;
}

bool BroadsoftXsiImpl::parseCreateCallResponse(std::string contentType, std::string responseBody)
{
   bool success = true;
   cpc::string callId;
   cpc::string trackingId;

   if (contentType.find("application/xml") != std::string::npos)
   {
      // Parse XML
      xmlDocPtr doc = xmlParseMemory(responseBody.data(), (int)responseBody.size());

      if (doc == NULL)
      {
         ErrLog(<< "BroadsoftXsiImpl::parseCreateCallResponse() - Parsing XML failed.");
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_CallStart, evt);
         return false;
      }
      xmlNodePtr cur = doc->children;
      // Verify CallForwardingBusy entity
      if (xmlStrcmp(cur->name, (const xmlChar *) "CallStartInfo"))
      {
         ErrLog(<< "BroadsoftXsiImpl::parseCreateCallResponse() - Parsing XML failed. Expected \"CallStartInfo\". Actual: " << (const char*)cur->name);
         BroadsoftXsiFailureEvent evt;
         evt.failureReason = BroadsoftXsiFailureReason_Malformed;
         fireEvent(XsiAction_CallStart, evt);
         success = false;
      }
      else
      {
         cur = cur->children;
         while (cur != NULL) {
            if (!xmlStrcmp(cur->name, (const xmlChar *) "callId"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  callId = (const char*)(cur->children->content);
               }
            }
            else if (!xmlStrcmp(cur->name, (const xmlChar *) "externalTrackingId"))
            {
               if (cur->children != NULL && cur->children->type == XML_TEXT_NODE)
               {
                  trackingId = (const char*)(cur->children->content);
               }
            }
            else
            {
               WarningLog(<< "BroadsoftXsiImpl::parseCreateCallResponse() - Received unknown setting: " << (const char*)cur->name);
            }
            cur = cur->next;
         }
      }

      xmlFreeDoc(doc);
   }
   else
   {
      ErrLog(<< "BroadsoftXsiImpl::parseCreateCallResponse() - Parsing content type (" << contentType << ") unsupported.");
      BroadsoftXsiFailureEvent evt;
      evt.failureReason = BroadsoftXsiFailureReason_Malformed;
      fireEvent(XsiAction_CallStart, evt);
   }

   if (success)
   {
      if (m_Settings.logParsedResponse)
      {
         cpc::string detailLog("BroadsoftXsiImpl::parseCreateCallResponse() - ");
         detailLog.append("callId: ");
         detailLog.append(callId);
         detailLog.append(", externalTrackingId: ");
         detailLog.append(trackingId);
         DebugLog(<< detailLog);
      }

      CreateCallSuccessEvent evt;
      evt.callId = callId;
      evt.externalTrackingId = trackingId;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onCreateCallSuccess), evt);
   }
   return success;
}

void BroadsoftXsiImpl::fireEvent(XsiAction action, BroadsoftXsiFailureEvent& e)
{
   switch (action)
   {
   case XsiAction_CallLogs:
   case XsiAction_CallLogsMissed:
   case XsiAction_CallLogsPlaced:
   case XsiAction_CallLogsReceived:
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryCallLogsFailure), e);
      break;
   case XsiAction_DeleteCallLogs:
   case XsiAction_DeleteCallLogsMissed:
   case XsiAction_DeleteCallLogsPlaced:
   case XsiAction_DeleteCallLogsReceived:
      fireEvent(cpcFunc(BroadsoftXsiHandler::onDeleteCallLogsFailure), e);
      break;
   case XsiAction_DeleteCallLogMissed:
   case XsiAction_DeleteCallLogPlaced:
   case XsiAction_DeleteCallLogReceived:
      fireEvent(cpcFunc(BroadsoftXsiHandler::onDeleteCallLogFailure), e);
      break;
   case XsiAction_EnterpriseDirectory:
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryEnterpriseDirectoryFailure), e);
      break;
   case XsiAction_QueryBroadworksAnywhereSettings:
      e.serviceType = ServiceType_BroadworksAnywhere;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryServiceSettingsFailure), e);
      break;
   case XsiAction_QueryCallForwardingAlwaysSettings:
      e.serviceType = ServiceType_CallForwardAlways;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryServiceSettingsFailure), e);
      break;
   case XsiAction_QueryCallForwardingBusySettings:
      e.serviceType = ServiceType_CallForwardBusy;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryServiceSettingsFailure), e);
      break;
   case XsiAction_QueryCallForwardingNoAnswerSettings:
      e.serviceType = ServiceType_CallForwardNoAnswer;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryServiceSettingsFailure), e);
      break;
   case XsiAction_QueryDoNotDisturbSettings:
      e.serviceType = ServiceType_DoNotDisturb;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryServiceSettingsFailure), e);
      break;
   case XsiAction_QueryRemoteOfficeSettings:
      e.serviceType = ServiceType_RemoteOffice;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryServiceSettingsFailure), e);
      break;
   case XsiAction_QuerySimultaneousRingSettings:
      e.serviceType = ServiceType_SimultaneousRing;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onQueryServiceSettingsFailure), e);
      break;
   case XsiAction_SetBroadworksAnywhereSettings:
   case XsiAction_AddBroadworksAnywhereLocation:
   case XsiAction_DeleteBroadworksAnywhereLocation:
   case XsiAction_UpdateBroadworksAnywhereLocation:
      e.serviceType = ServiceType_BroadworksAnywhere;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onSetServiceSettingsFailure), e);
      break;
   case XsiAction_SetCallForwardingAlwaysSettings:
      e.serviceType = ServiceType_CallForwardAlways;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onSetServiceSettingsFailure), e);
      break;
   case XsiAction_SetCallForwardingBusySettings:
      e.serviceType = ServiceType_CallForwardBusy;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onSetServiceSettingsFailure), e);
      break;
   case XsiAction_SetCallForwardingNoAnswerSettings:
      e.serviceType = ServiceType_CallForwardNoAnswer;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onSetServiceSettingsFailure), e);
      break;
   case XsiAction_SetDoNotDisturbSettings:
      e.serviceType = ServiceType_DoNotDisturb;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onSetServiceSettingsFailure), e);
      break;
   case XsiAction_SetRemoteOfficeSettings:
      e.serviceType = ServiceType_RemoteOffice;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onSetServiceSettingsFailure), e);
      break;
   case XsiAction_SetSimultaneousRingSettings:
      e.serviceType = ServiceType_SimultaneousRing;
      fireEvent(cpcFunc(BroadsoftXsiHandler::onSetServiceSettingsFailure), e);
      break;
   case XsiAction_CallStart:
      fireEvent(cpcFunc(BroadsoftXsiHandler::onCreateCallFailure), e);
      break;
   default:
      ErrLog(<< "BroadsoftXsiImpl::fireEvent() - Invalid action provided: " << action);
      break;;
   }
}

void BroadsoftXsiImpl::setCallbackHook(void (*cbHook)(void*), void* context)
{
   if( cbHook != NULL && context != NULL )
      m_CbHook = std::bind( cbHook, context );
}

int BroadsoftXsiImpl::setHandler( BroadsoftXsiHandler* handler)
{
   // Ensure handler isn't already set.
   if( m_Handler != NULL && handler != NULL )
      FIRE_ERROR_AND_RETURN( INVALID_HANDLER_STR );

   m_Handler = handler;
   return kSuccess;
}

#endif // #if (CPCAPI2_BRAND_BROADSOFT_XSI_MODULE == 1)
