#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_NETWORKCHANGEMANAGER_NETWORKCHANGEMANAGEREVENTS_H
#define JSONRPC_CPCAPI2_NETWORKCHANGEMANAGER_NETWORKCHANGEMANAGEREVENTS_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace NetworkChangeManager {
  enum class NetworkChangeManagerEvents
  {
    NetworkChangeManagerDotOnNetworkChange
  };

  inline std::string to_string(NetworkChangeManagerEvents value)
  {
    switch (value)
    {
      case NetworkChangeManagerEvents::NetworkChangeManagerDotOnNetworkChange:
        return "NetworkChangeManager.onNetworkChange";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, NetworkChangeManagerEvents value)
  {
    switch (value)
    {
      case NetworkChangeManagerEvents::NetworkChangeManagerDotOnNetworkChange:
        os << "NetworkChangeManager.onNetworkChange";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const NetworkChangeManagerEvents& e)
  {
    switch (e)
    {
      case NetworkChangeManagerEvents::NetworkChangeManagerDotOnNetworkChange:
        j = "NetworkChangeManager.onNetworkChange";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, NetworkChangeManagerEvents& e)
  {
      if (0 == j.get<std::string>().compare("NetworkChangeManager.onNetworkChange"))
    {
      e = NetworkChangeManagerEvents::NetworkChangeManagerDotOnNetworkChange;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum NetworkChangeManagerEvents"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace NetworkChangeManager
#endif // JSONRPC_CPCAPI2_NETWORKCHANGEMANAGER_NETWORKCHANGEMANAGEREVENTS_H
