#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_PHONE_TLSVERSION_H
#define JSONRPC_CPCAPI2_PHONE_TLSVERSION_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  enum class TlsVersion
  {
    Default,
    None,
    V1_0,
    V1_1,
    V1_2,
    V1_3,
    Highest,
    NonDeprecated
  };

  inline std::string to_string(TlsVersion value)
  {
    switch (value)
    {
      case TlsVersion::Default:
        return "Default";
      case TlsVersion::None:
        return "None";
      case TlsVersion::V1_0:
        return "V1_0";
      case TlsVersion::V1_1:
        return "V1_1";
      case TlsVersion::V1_2:
        return "V1_2";
      case TlsVersion::V1_3:
        return "V1_3";
      case TlsVersion::Highest:
        return "HIGHEST";
      case TlsVersion::NonDeprecated:
        return "NON_DEPRECATED";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, TlsVersion value)
  {
    switch (value)
    {
      case TlsVersion::Default:
        os << "Default";
        break;
      case TlsVersion::None:
        os << "None";
        break;
      case TlsVersion::V1_0:
        os << "V1_0";
        break;
      case TlsVersion::V1_1:
        os << "V1_1";
        break;
      case TlsVersion::V1_2:
        os << "V1_2";
        break;
      case TlsVersion::V1_3:
        os << "V1_3";
        break;
      case TlsVersion::Highest:
        os << "HIGHEST";
        break;
      case TlsVersion::NonDeprecated:
        os << "NON_DEPRECATED";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const TlsVersion& e)
  {
    switch (e)
    {
      case TlsVersion::Default:
        j = "Default";
        break;
      case TlsVersion::None:
        j = "None";
        break;
      case TlsVersion::V1_0:
        j = "V1_0";
        break;
      case TlsVersion::V1_1:
        j = "V1_1";
        break;
      case TlsVersion::V1_2:
        j = "V1_2";
        break;
      case TlsVersion::V1_3:
        j = "V1_3";
        break;
      case TlsVersion::Highest:
        j = "HIGHEST";
        break;
      case TlsVersion::NonDeprecated:
        j = "NON_DEPRECATED";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, TlsVersion& e)
  {
      if (0 == j.get<std::string>().compare("Default"))
    {
      e = TlsVersion::Default;
    }
    else if (0 == j.get<std::string>().compare("None"))
    {
      e = TlsVersion::None;
    }
    else if (0 == j.get<std::string>().compare("V1_0"))
    {
      e = TlsVersion::V1_0;
    }
    else if (0 == j.get<std::string>().compare("V1_1"))
    {
      e = TlsVersion::V1_1;
    }
    else if (0 == j.get<std::string>().compare("V1_2"))
    {
      e = TlsVersion::V1_2;
    }
    else if (0 == j.get<std::string>().compare("V1_3"))
    {
      e = TlsVersion::V1_3;
    }
    else if (0 == j.get<std::string>().compare("HIGHEST"))
    {
      e = TlsVersion::Highest;
    }
    else if (0 == j.get<std::string>().compare("NON_DEPRECATED"))
    {
      e = TlsVersion::NonDeprecated;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum TlsVersion"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_TLSVERSION_H
