#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACCOUNT_MODULE == 1)
#include "CPOptionsPingManager.h"

#include "../util/cpc_logger.h"
#include "../util/SipHelpers.h"
#include <resip/stack/ExtensionHeader.hxx>

#include "SipAccountInterface.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_ACCOUNT

namespace CPCAPI2
{
namespace SipAccount
{
CPOptionsPingManager::CPOptionsPingManager(CPCAPI2::Phone* cpcPhone, CPCAPI2::SipAccount::SipAccountImpl* account) :
    mReactor(dynamic_cast<PhoneInterface*>(cpcPhone)->getSdkModuleThread()),
    mAccount(account),
    mAccountHandle(account->getHandle()),
    mTimer(mReactor),
    mPingEnabled(false),
    mRegistered(false),
    mInterval(0),
    mInFlightCallID("")
{
}

CPOptionsPingManager::~CPOptionsPingManager()
{
  mTimer.cancel();
}

////////////////////////////////////////////////////////////////////////////////
// Start SipAccountHandlerInternal
////////////////////////////////////////////////////////////////////////////////

int CPOptionsPingManager::onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args)
{
  if (mPingEnabled && mInterval > 0 && SipAccountStatusChangedEvent::Status_Registered == args.accountStatus)
  {
    //InfoLog(<< "CPOptionsPingManager::onAccountStatusChanged registered account " << account);
    mRegistered = true;

    if (mAccount->getSipServerIpFromLastReg(mTarget))
    {
       DebugLog(<< "CPOptionsPingManager::enableAccount " << account << " last server was " << mTarget.presentationFormat());
    }
    else
    {
      ErrLog(<< "CPOptionsPingManager::enableAccount " << account << " could not get the IP address of last registration");
      return kError;
    }

    mTimer.cancel();
    mTimer.expires_from_now(rand() % mInterval + 1);
    mTimer.async_wait(this, 0, NULL);
  }
  else
  {
    InfoLog(<< "CPOptionsPingManager::onAccountStatusChanged disabled account " << account);
    mRegistered = SipAccountStatusChangedEvent::Status_Registered == args.accountStatus;
    mTimer.cancel();
  }
  return kSuccess;
}

int CPOptionsPingManager::onAccountConfigured(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountConfiguredEvent& args)
{
  //InfoLog(<< "CPOptionsPingManager::onAccountConfigured");
  // Use the current transport, or "None" if there is no setting
  // for the current transport.
  const CPCAPI2::SipAccount::SipAccountSettings& settings(
      args.settings.size() > ( size_t )( args.curTransport ) ?
        args.settings.at( args.curTransport ) :
        args.settings.at( CPCAPI2::NetworkTransport::TransportNone )
  );

  if (settings.useOptionsPing)
  {
    InfoLog(<< "CPOptionsPingManager::onAccountConfigured adding account " << account << " with interval " << settings.optionsPingInterval);
    mInterval = settings.optionsPingInterval;
    mPingEnabled = true;
  }
  else
  {
    InfoLog(<< "CPOptionsPingManager::onAccountConfigured removing account " << account);
    mInterval = 0;
    mPingEnabled = false;
    mTimer.cancel();
  }
  return kSuccess;
}

////////////////////////////////////////////////////////////////////////////////
// End SipAccountHandlerInternal
////////////////////////////////////////////////////////////////////////////////

////////////////////////////////////////////////////////////////////////////////
// Start resip::DeadlineTimerHandler
////////////////////////////////////////////////////////////////////////////////

void CPOptionsPingManager::onTimer(unsigned short timerId, void* appState)
{
  mTimer.cancel();
  //InfoLog(<< "CPOptionsPingManager::onTimer " << mInterval);

  sendPing();

  if (0 < mInterval)
  {
    mTimer.expires_from_now(mInterval);
    mTimer.async_wait(this, timerId, NULL);
  }
}

////////////////////////////////////////////////////////////////////////////////
// End resip::DeadlineTimerHandler
////////////////////////////////////////////////////////////////////////////////

////////////////////////////////////////////////////////////////////////////////
// Start OutOfDialogHandler
////////////////////////////////////////////////////////////////////////////////

bool CPOptionsPingManager::onSuccess(resip::ClientOutOfDialogReqHandle h, const resip::SipMessage& successResponse)
{
   std::string callID = successResponse.header(resip::h_CallId).value().c_str();

   if (mInFlightCallID == callID)
   {
      mInFlightCallID= "";
      InfoLog(<< "CPOptionsPingManager::onSuccess " << mAccountHandle << "   " << callID);
      return true;
   }

   return false;
}

bool CPOptionsPingManager::onFailure(resip::ClientOutOfDialogReqHandle h, const resip::SipMessage& errorResponse)
{
  std::string callID = errorResponse.header(resip::h_CallId).value().c_str();
  if (mInFlightCallID == callID)
  {
    mInFlightCallID = "";

    InfoLog(<< "CPOptionsPingManager::onFailure " << mAccountHandle << "   " << callID);
    unsigned int respCode = errorResponse.header(resip::h_StatusLine).responseCode();
    // OpenSIPS can return 483 Too Many Hops when addressed to the IP address. Ignore 483s as well as 484/485
    // since they indicate that the target has problems processing messages addressed to it's IP address
    if (respCode < 483 || respCode > 485)
    {
      //SCORE 1807:
      if (!mAccount->isDoingRportReReg())
      {
        mAccount->requestRegistrationRefresh(0);
      }
    }
    return true;
  }

  return false;
}

bool CPOptionsPingManager::onReceivedRequest(resip::ServerOutOfDialogReqHandle h, const resip::SipMessage& request)
{
  return false;
}

////////////////////////////////////////////////////////////////////////////////
// End OutOfDialogHandler
////////////////////////////////////////////////////////////////////////////////

bool CPOptionsPingManager::sendPing()
{
  if (!mInFlightCallID.empty())
  {
    InfoLog(<< "CPOptionsPingManager::sendPing " << mAccountHandle << " to " << mTarget << " there is currently a ping in flight");
    return false;
  }

  if (mDum)
  {
    // TODO: This triggers an exception if the target is a V6 address, i.e. ::1 as that results in 3 ':' in a row
    resip::Data target = resip::Data("sip:") + mTarget.presentationFormat();
    resip::SharedPtr<resip::SipMessage> msg = mDum->makeOutOfDialogRequest(resip::NameAddr(target), resip::OPTIONS);
    msg->header(resip::h_MaxForwards).value() = 1;
    resip::ExtensionHeader h_XConnectivityProbe("X-Connectivity-Probe-Ping");
    msg->header(h_XConnectivityProbe).push_back(resip::StringCategory("0"));
    mDum->send(msg);

    if (msg && msg->exists(resip::h_CallId))
    {
       std::string callID = msg->header(resip::h_CallId).value().c_str();
       mInFlightCallID = callID;
       InfoLog(<< "CPOptionsPingManager::sendPing " << mAccountHandle << " to " << target << " with call-id " << callID);
    }
    else
    {
      InfoLog(<< "CPOptionsPingManager::sendPing " << mAccountHandle << " to " << target << " failed");
      return false;
    }

    return true;
  }
  else
  {
    InfoLog(<< "CPOptionsPingManager::sendPing " << mAccountHandle << " dum is not set");
  }
  return false;
}

////////////////////////////////////////////////////////////////////////////////
// Start SipAccountAwareFeature
////////////////////////////////////////////////////////////////////////////////

int CPOptionsPingManager::adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile)
{
   if (!profile->isMethodSupported(resip::OPTIONS))
   {
      profile->addSupportedMethod(resip::OPTIONS);
   }

   return kSuccess;
}

int CPOptionsPingManager::addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig)
{
   dum->addOutOfDialogHandler(resip::OPTIONS, this, false);
   mDum = dum;

   return kSuccess;
}

int CPOptionsPingManager::onDumBeingDestroyed()
{
   mDum.reset();
   return kSuccess;
}

void CPOptionsPingManager::release()
{
  mTimer.cancel();
}

////////////////////////////////////////////////////////////////////////////////
// End SipAccountAwareFeature
////////////////////////////////////////////////////////////////////////////////


}
}
#endif
