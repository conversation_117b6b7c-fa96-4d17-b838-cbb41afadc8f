#pragma once

#if !defined(CPCAPI2_WEBSOCKET_CLIENT_MANAGER_H)
#define CPCAPI2_WEBSOCKET_CLIENT_MANAGER_H

#include "cpcapi2defs.h"
//#include "WebSocketConnection.h"
#include "websocketpp/common/thread.hpp"
#include "websocketpp/config/asio_no_tls_client.hpp"
#include "websocketpp/client.hpp"
#include "GenbandRestAPIEventHandler.h"

namespace CPCAPI2
{
namespace Genband
{

typedef websocketpp::client<websocketpp::config::asio_client> client;
typedef websocketpp::lib::thread thread_type;
typedef websocketpp::lib::shared_ptr<thread_type> thread_ptr;
//typedef websocketpp::lib::shared_ptr<WebSocketConnection> connection_ptr;
typedef websocketpp::config::asio_client::message_type::ptr message_ptr;


class WebSocketClientManager
{
public:
    WebSocketClientManager();
	
	int connect(std::string const & uri, unsigned int subscriptionHdl, CPCAPI2::Genband::GenbandRestAPIEventHandler* msgHandler);
    void close(unsigned int subscriptionHdl);
    void shutdown();
	///////
	void on_message(websocketpp::connection_hdl, message_ptr msg);
    void on_fail(websocketpp::connection_hdl);
    void on_open(websocketpp::connection_hdl);
    void on_close(websocketpp::connection_hdl);
    websocketpp::connection_hdl get_hdl();
	///////
private:    
    size_t nextId;
    std::map<unsigned int, websocketpp::connection_hdl> mConnections;
    client mEndpoint;
    thread_ptr mThread;
	///////////
	websocketpp::lib::mutex m_mutex;
    size_t m_id;
    std::queue<std::string> m_messages;
    websocketpp::connection_hdl m_hdl;
	unsigned int subscriptionHdl;

	GenbandRestAPIEventHandler* msgHandler;
	///////////
};

}
}
#endif