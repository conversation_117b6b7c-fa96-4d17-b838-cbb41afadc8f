#include "../datatypes/SipMwiRegisterForEventQueue.h"
#include "../datatypes/SipMwiUnRegisterForEventQueue.h"
#include "../datatypes/SipMwiCreateSubscription.h"
#include "../datatypes/SipMwiStart.h"
#include "../datatypes/SipMwiEnd.h"

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipMwi {
void SipMwi::registerForEventQueue(int64_t phoneHandle, int64_t accountHandle)
{
    SipMwiRegisterForEventQueue params = new SipMwiRegisterForEventQueue(phoneHandle, accountHandle);
    nlohmann::json j = this.transport.request("registerForEventQueue", params.marshal());
    
};

void SipMwi::unRegisterForEventQueue(int64_t phoneHandle, int64_t accountHandle)
{
    SipMwiUnRegisterForEventQueue params = new SipMwiUnRegisterForEventQueue(phoneHandle, accountHandle);
    nlohmann::json j = this.transport.request("unRegisterForEventQueue", params.marshal());
    
};

int64_t SipMwi::createSubscription(int64_t phoneHandle, int64_t accountHandle)
{
    SipMwiCreateSubscription params = new SipMwiCreateSubscription(phoneHandle, accountHandle);
    nlohmann::json j = this.transport.request("createSubscription", params.marshal());
    return j.get<int64_t>();
};

void SipMwi::start(int64_t phoneHandle, int64_t mwiSubscriptionHandle)
{
    SipMwiStart params = new SipMwiStart(phoneHandle, mwiSubscriptionHandle);
    nlohmann::json j = this.transport.request("start", params.marshal());
    
};

void SipMwi::end(int64_t phoneHandle, int64_t mwiSubscriptionHandle)
{
    SipMwiEnd params = new SipMwiEnd(phoneHandle, mwiSubscriptionHandle);
    nlohmann::json j = this.transport.request("end", params.marshal());
    
};
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipMwi