#if !defined(CPCAPI2_SIP_DIALOG_INFO_DOCUMENT_HELPER)
#define CPCAPI2_SIP_DIALOG_INFO_DOCUMENT_HELPER 

#include "dialogevent/SipDialogEventModel.h"
#include "event/SipEventState.h"

#include <libxml/xmlreader.h>
#include <resip/dum/DialogId.hxx>
#include <resip/dum/DialogEventStateManager.hxx>

namespace CPCAPI2
{
namespace SipDialogEvent
{

class DialogInfoDocumentHelper
{
public:
   static DialogInfoDocument createEmptyDocument(int version, const cpc::string& entity);
   static CPCAPI2::SipEvent::SipEventState createEventState(const DialogInfoDocument& dialogInfoDoc, bool includeSessionDescription, bool sharedAppearanceDialogExtensionsEnabled);
   static cpc::string toXml(DialogInfoDocument doc, bool includeSessionDescription, bool sharedAppearanceDialogExtensionsEnabled);
   static bool fromXml(const cpc::string& xml, DialogInfoDocument &doc, bool sharedAppearanceDialogExtensionsEnabled);
   static void fromDialogEventInfo(int version, const cpc::string& entity, DialogInfoDocumentState documentState, resip::DialogEventStateManager::DialogEventInfos dialogEventInfos, DialogInfoDocument &doc);
   static void setDialogStateReasonAndCode(DialogInfoDocument& doc, const resip::TerminatedDialogEvent& dialogEvent);
   static void setDialogStateReasonAndCode(DialogInfoDocument& doc, const resip::MultipleEventDialogEvent& dialogEvent);

   static const cpc::string EVENT_PACKAGE_NAME;
   static const MimeType EVENT_MIME_TYPE;
   static const cpc::string INCLUDE_SDP_PARAM_NAME;
   static const cpc::string SHARED_APPEARANCE_DIALOG_EXTENSIONS_PARAM_NAME;

private:
   DialogInfoDocumentHelper() {}

   static cpc::string getIdBasedOnDialogId(const DialogId& dialogId);
   static std::ostream& encode(std::ostream& ostream, const DialogInfoDocument& doc, bool includeSessionDescription, bool sharedAppearanceDialogExtensionsEnabled);
   static std::ostream& encode(std::ostream& ostream, const DialogStateInfo& dialogStateInfo);
   static std::ostream& encode(std::ostream& ostream, const DialogInfo& dialog, bool includeSessionDescription, bool sharedAppearanceDialogExtensionsEnabled);
   static std::ostream& encode(std::ostream& ostream, const ParticipantInfo& participant, bool includeSessionDescription);
   static bool parse(xmlTextReaderPtr reader, bool *parsingError, DialogInfoDocument& doc, bool sharedAppearanceDialogExtensionsEnabled);
   static void parse(xmlTextReaderPtr reader, DialogStateInfo& dialogStateInfo);
   static void parse(xmlTextReaderPtr reader, DialogInfo& dialog, bool sharedAppearanceDialogExtensionsEnabled);
   static void parse(xmlTextReaderPtr reader, const cpc::string& xmlElementName, ParticipantInfo& participant);
   static void PopulateIdentityInfoFromNameAddress(const NameAddress& na, IdentityInfo& idInfo);
   static cpc::string dialogInfoDocumentStateToString(const DialogInfoDocumentState& state);
   static cpc::string dialogStateToString(const DialogState& dialogState);
   static cpc::string dialogStateReasonToString(const DialogStateReason& reason);
   static cpc::string dialogDirectionToString(const DialogDirection& direction);
   static cpc::string dialogExclusiveToString(bool exclusive);
   static DialogInfoDocumentState stringToDialogInfoDocumentState(const cpc::string& stateStr);
   static DialogState stringToDialogState(const cpc::string& stateStr);
   static DialogStateReason stringToDialogStateReason(const cpc::string& reasonStr);
   static DialogDirection stringToDialogDirection(const cpc::string& directionStr);
   static bool stringToDialogExclusive(const cpc::string& exclusiveStr);
   static void fromDialogEventInfo(const resip::DialogEventInfo& dialogEventInfo, DialogInfo& dialogInfo);
   static DialogDirection dialogDirectionToSdk(const resip::DialogEventInfo::Direction& resipDirection);
   static DialogState dialogStateToSdk(const resip::DialogEventInfo::State& state);
   static NameAddress nameAddressToSdk(const resip::NameAddr& nameAddr);
   static TargetInfo targetToSdk(const resip::Uri& target);
   static void sessionDescriptionToSdk(const resip::Contents &contents, ParticipantInfo& participant);
   static DialogStateReason dialogStateToSdk(const resip::InviteSessionHandler::TerminatedReason& reason);
};

}
}

#endif // CPCAPI2_SIP_DIALOG_INFO_DOCUMENT_HELPER

