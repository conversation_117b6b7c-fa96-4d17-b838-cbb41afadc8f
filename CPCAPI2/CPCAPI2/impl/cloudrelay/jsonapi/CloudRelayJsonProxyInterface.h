#pragma once

#if !defined(CPCAPI2_CLOUD_RELAY_JSON_PROXY_INTERFACE_H)
#define CPCAPI2_CLOUD_RELAY_JSON_PROXY_INTERFACE_H

#include "cloudrelay/CloudRelayTypes.h"
#include "cloudrelay/CloudRelayHandler.h"
#include "cloudrelay/CloudRelayJsonProxy.h"
#include "cloudrelay/CloudRelaySyncHandler.h"
#include "jsonapi/JsonApiClientModule.h"
#include "phone/PhoneModule.h"
#include "phone/Cpcapi2EventSource.h"

#include <rutil/Reactor.hxx>
#include <rutil/Fifo.hxx>

#include <mutex>
#include <condition_variable>

namespace CPCAPI2
{
class PhoneInterface;
namespace CloudRelay
{
class CloudRelayJsonProxyInterface : public CPCAPI2::EventSource<CloudRelayHandle, CloudRelayHandler, CloudRelaySyncHandler>, 
                                          public CPCAPI2::CloudRelay::CloudRelayManagerJsonProxy,
                                          public CPCAPI2::JsonApi::JsonApiClientModule,
                                          public CPCAPI2::PhoneModule,
                                          public resip::ReactorBinded
{
public:
   CloudRelayJsonProxyInterface(CPCAPI2::Phone* phone);
   virtual ~CloudRelayJsonProxyInterface();

   void clearQueryConferenceListHandler();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiClientModule
   virtual void setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport) OVERRIDE;
   virtual int processIncoming(const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // Inherited via CloudRelayManager
   virtual int process(unsigned int timeout) OVERRIDE {
      return kSuccess;
   }
   virtual void interruptProcess() OVERRIDE {}
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE {}
   virtual int start() OVERRIDE;
   virtual int shutdown() OVERRIDE {
      return kSuccess;
   }
   virtual CloudRelayHandle getRelay() OVERRIDE;
   virtual int setHandler(CloudRelayHandle conference, CloudRelayHandler* handler) OVERRIDE;
   virtual int setHandler(CloudRelayHandler* handler) OVERRIDE;
   virtual int broadcast(CloudRelayHandle relay, const cpc::string& msg) OVERRIDE;
   virtual int sendTo(CloudRelayHandle relay, CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const cpc::string& msg) OVERRIDE;

private:
   // ReactorBinded
   virtual void release() OVERRIDE;

   void processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request, const std::weak_ptr<int>& processCheck);
   int startImpl();
   int getRelayImpl(CloudRelayHandle relay);
   int broadcastImpl(CloudRelayHandle relay, const cpc::string& msg);
   int sendToImpl(CloudRelayHandle relay, CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const cpc::string& msg);

   int handleBroadcast(const rapidjson::Value& functionObjectVal);
   int handleMessage(const rapidjson::Value& functionObjectVal);

private:
   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiTransport* mTransport;
   int mHandlePrefix;
   CloudRelayHandle mNextCloudRelayHandle;
   std::shared_ptr<int> mProcessCheck;
};
}
}
#endif // CPCAPI2_CLOUD_RELAY_JSON_PROXY_INTERFACE_H
