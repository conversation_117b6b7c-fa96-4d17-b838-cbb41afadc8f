#if defined(_WIN32)
#include <intrin.h>
#include <boost/preprocessor/stringize.hpp>
#elif defined(__APPLE__)
#include "TargetConditionals.h"

#if !defined(TARGET_OS_IPHONE) || !(TARGET_OS_IPHONE)
#include "../../util/Mac/CMacSecurityAccess.h"
#include <sys/types.h>
#include <sys/sysctl.h>
#include <uuid/uuid.h>
#endif

#endif

#include "../../util/HDDiskId.h"
//#include <helper/CUtils.h>
#include "HardwareHash.h"
//#include <helper/Nilsimsa.h>
#include "Nilsimsa.h"
#include <sstream>
#include <string.h>
#include <algorithm>
#include "../../util/MachineIdentification.h"

namespace CPCAPI2
{
namespace Licensing
{

static std::string lexical_cast(int64_t val)
{
   std::stringstream ss;
   ss << val;

   return ss.str();
}

std::string HardwareHash::GenerateHardwareHash()
{
   static std::string s_HardwareHash;
   if (!s_HardwareHash.empty())
      return s_HardwareHash;

#if defined(_WIN32)
   auto macAddresses = GetMacAddresses();
   auto hddIds = GetHddIds();
   auto serial = GetSerialNumber();
   auto ram = GetRamSize();
   ns_obj ns;
   memset(&ns, 0, sizeof(ns));
   if (!macAddresses.empty())
   {
      if (macAddresses.size() == 1)
      {
         ns_add_data(&ns, macAddresses[0].c_str());
         ns_add_data(&ns, macAddresses[0].c_str());
      }
      else
      {
         std::sort(macAddresses.begin(), macAddresses.end());
         ns_add_data(&ns, macAddresses[0].c_str());
         ns_add_data(&ns, macAddresses[1].c_str());
      }
   }
   if (!hddIds.empty())
   {
      std::sort(hddIds.begin(), hddIds.end());
      ns_add_data(&ns, hddIds[0].c_str());
   }
   std::string rs = lexical_cast(ram);
   Align(rs, 11);
   ns_add_data(&ns, rs.c_str());
   Align(serial, 16);
   ns_add_data(&ns, serial.c_str());
   ns_create_signature(&ns);
   s_HardwareHash = ns.signature;
#elif defined(TARGET_OS_IPHONE) && !(TARGET_OS_IPHONE)
   uuid_t uuid;
   struct timespec timeout;
   timeout.tv_nsec = 0;
   timeout.tv_sec = 0;
   ns_obj ns;
   memset(&ns, 0, sizeof(ns));
   if (gethostuuid(uuid, &timeout) == 0)
   {
      uuid_string_t uuid_string;
      uuid_unparse_lower(uuid, uuid_string);
      ns_add_data(&ns, uuid_string);
   }
   else
   {
      CPCAPI2::CSecurityAccess* sec = CPCAPI2::CSecurityAccess::instance();

      ns_add_data(&ns, sec->getSerialNumber().c_str());
      ns_add_data(&ns, sec->getMachineType().c_str());
      ns_add_data(&ns, sec->getPrimaryMAC().c_str());
      ns_add_data(&ns, sec->getSerialNumber().c_str());
   }
   ns_create_signature(&ns);
   s_HardwareHash = ns.signature;
#else
   ns_obj ns;
   memset(&ns, 0, sizeof(ns));
   ns_add_data(&ns, "unknow platform - results in same HW string");
   ns_create_signature(&ns);
   s_HardwareHash = ns.signature;
#endif
   return s_HardwareHash;
}

std::vector<std::string> HardwareHash::GetMacAddresses()
{
#if defined(_WIN32)
   auto macs = CPCAPI2::MachineIdentification::GetAllMACs_Win32(false);
   std::vector<std::string> ret;
   for (auto it = macs.begin(); it != macs.end(); ++it)
   {
      ret.push_back(*it);
   }
   return ret;
#else
   // TODO
   return std::vector<std::string>();
#endif
}

std::vector<std::string> HardwareHash::GetHddIds()
{
#if _WIN32
   auto ids = CPCAPI2::HDDiskId::GetAllHddInfo();
   std::vector<std::string> ret;
   for (auto it = ids.begin(); it != ids.end(); ++it)
   {
      ret.push_back(*it);
   }
   return ret;
#else
   // TODO
   return std::vector<std::string>();
#endif
}

std::string HardwareHash::GetSerialNumber()
{
#ifdef _WIN32
   int cpuInfo[4] = {-1};
   __cpuid(cpuInfo, 1);
   std::vector<std::string> ids;
   for (int i = 0; i <=3; ++i)
   {
      std::stringstream stream;
      stream << std::hex << cpuInfo[i];
      ids.push_back(stream.str());
   }
   std::string modelInfo = ids[0];
   std::string featureInfo = ids[3];;

   if (modelInfo.length() < 8)
   {
      modelInfo.insert(0, 8-modelInfo.length(), '0');
   }
   if (featureInfo.length() < 8)
   {
      featureInfo.insert(0, 8-featureInfo.length(), '0');
   }
   return featureInfo + modelInfo;
#elif defined(TARGET_OS_IPHONE) && !(TARGET_OS_IPHONE)
   return CPCAPI2::CSecurityAccess::instance()->getSerialNumber();
#else
   return "";
#endif
}

int64_t HardwareHash::GetRamSize()
{
#ifdef _WIN32
   //OSVERSIONINFO info;
   //ZeroMemory(&info, sizeof(OSVERSIONINFO));
   //info.dwOSVersionInfoSize = sizeof(OSVERSIONINFO);
   //::GetVersionEx(&info);
   //if (info.dwMajorVersion >= 6)
   {
#if (_WIN32_WINNT >= 0x0601)
      static int bytesInKB = 1024;
      ULONGLONG sizeInKiloBytes = 0;
      ::GetPhysicallyInstalledSystemMemory(&sizeInKiloBytes);
      return sizeInKiloBytes * bytesInKB;
#else
      return 8589934592; //(8 * 1024 * 1024 * 1024)
#endif
   }
   //else
   //{
      /*MEMORYSTATUSEX ms;
      ms.dwLength = sizeof(MEMORYSTATUSEX);
      ::GlobalMemoryStatusEx(&ms);
      return ms.ullTotalPhys;*/
   //   return 8589934592; //(8 * 1024 * 1024 * 1024)
   //}
#elif defined (__APPLE__) && !(TARGET_OS_IPHONE)
   int mib[2];
   int64_t physicalMemoryInBytes;
   size_t length;
   // Get the Physical memory size
   mib[0] = CTL_HW;
   mib[1] = HW_MEMSIZE;
   length = sizeof(int64_t);
   sysctl(mib, 2, &physicalMemoryInBytes, &length, NULL, 0);
   return physicalMemoryInBytes;
#else
   return 0;
#endif
}

void HardwareHash::Align(std::string& in, int len)
{
   if ((int) in.size() < len)
   {
      in.append(len - in.size(), '0');
   }
   else if ((int) in.size() > len)
   {
      in = in.substr(0, len);
   }
}

}
}
