#include "brand_branded.h"

#include "bievents/BIEventsManager.h"
#include "bievents/BIEventsHelper.h"

#if (CPCAPI2_BRAND_BIEVENTS_MODULE == 1)
#include "BIEventsManagerInterface.h"
#include "BIEventsHelperInterface.h"
#endif

namespace CPCAPI2
{
   namespace BIEvents
   {
      BIEventsHelper* BIEventsHelper::getInterface( BIEventsManager* pManager )
      {
         if( pManager == NULL )
            return NULL;

#if (CPCAPI2_BRAND_BIEVENTS_MODULE == 1)
         BIEventsManagerInterface *pInterface = dynamic_cast< BIEventsManagerInterface * >( pManager );
         if( pInterface == NULL )
            return NULL;

         return pInterface->getHelper();
#else
         return NULL;
#endif
      }

   }
}
