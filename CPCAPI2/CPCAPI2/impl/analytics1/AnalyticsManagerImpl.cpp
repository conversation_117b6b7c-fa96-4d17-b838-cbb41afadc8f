#include <assert.h>
#include <sstream>
#include <iostream>
#include <brand_branded.h>

#if (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)

#include <account/SipAccount.h>
#include <account/SipAccountInterface.h>
#include <call/SipAVConversationManagerInterface.h>
#include <xmpp/XmppAccountInterface.h>
#include <xmpp/XmppAccountHandlerInternal.h>
#include <xmpp/XmppAccountSettings.h>
#include <ptt/PushToTalkManagerInternal.h>
#include <analytics1/AnalyticsHandler.h>
#include <analytics1/AnalyticsManagerInt.h>
#include <boost/algorithm/string.hpp>
#include <media/MediaManager.h>
#include <media/AudioInterface.h>
#include <webrtc/modules/audio_device/include/audio_device.h>
#include <phone/NetworkChangeManagerInterface.h>
#include <MediaStackImpl.hxx>
#include <voe_base.h>

#include <utils/msrp_tree.h>
#include "AnalyticsManagerImpl.h"
#include "RegistryUtils.h"
#include "DocumentBuilder.h"
#include "../account/SipAccountImpl.h"

#include "../util/IpHelpers.h"
#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::UEM

using namespace CPCAPI2::Analytics;
using namespace CPCAPI2::PushToTalk;
using CPCAPI2::SipAccount::SipAccountManager;
using CPCAPI2::SipAccount::SipAccountInterface;
using CPCAPI2::XmppAccount::XmppAccountInterface;
using CPCAPI2::PushToTalk::PushToTalkManager;
using CPCAPI2::PushToTalk::PushToTalkManagerInterface;
using CPCAPI2::SipConversation::SipConversationManager;
using CPCAPI2::SipConversation::SipAVConversationManagerInterface;
using CPCAPI2::SipConversation::SipConversationStateManager;
using CPCAPI2::SipConversation::MediaEncryptionMode;
using CPCAPI2::Media::AudioDeviceInfo;
using CPCAPI2::Media::AudioInterface;

#define XML_TEMPLATE_VER "1.8"

// indented to indicate relationships
#define ROOT              "cpc_usage_report"
#define GENERAL              "general"
#define SETTINGS_DATA        "settings_data"
#define ACCOUNT_LIST            "account_list"
#define PTT_IDENTITY_LIST          "identity_list"
#define PTT_CHANNEL_LIST           "channel_list"
#define PTT_RANGE_LIST             "lanIpAddressRange_list"
#define ACTIVITY_DATA        "activity_data"
#define PRESENCE                "presence"
#define PROVISIONING            "provisioning"
#define STABILITY               "stability"

// Attribute names
#define SETTINGS_ACCOUNT_ID_ATTR "id"
#define ACTIVITY_ACCOUNT_ID_ATTR "accountid"
#define SESSION_ID_ATTR "sessionid"

/**
* Return the number of seconds since the UNIX epoch (Jan 1st, 1970).
* NB: Win32 epoch is not the same.
*/
static int64_t secondsSinceEpoch()
{
   int64_t result(0);
#ifdef _WIN32
   FILETIME ft;

   // A file time is a 64-bit value that represents the number of
   // *100-nanosecond intervals* that have elapsed since 12:00 A.M.
   // January 1, 1601 Coordinated Universal Time (UTC).
   GetSystemTimeAsFileTime(&ft);

   LARGE_INTEGER date, adjust;
   date.HighPart = ft.dwHighDateTime;
   date.LowPart  = ft.dwLowDateTime;

   // 100-nanoseconds = milliseconds * 10000
   adjust.QuadPart = ************** * 10000;

   // removes the diff between 1970 and 1601
   date.QuadPart -= adjust.QuadPart;

   // result in seconds, not nano-intervals
   result = date.QuadPart / ********;
#else
   struct timeval now;
   gettimeofday(&now, NULL);
   result = (int64_t)now.tv_sec; // just take seconds part
#endif
   return result;
}

unsigned int GetNumberOfDigits( unsigned int i )
{
    return (int) log10 ((double) i) + 1;
}

AnalyticsManagerImpl::AnalyticsManagerImpl(
   resip::Fifo<resip::ReadCallbackBase>& callbacks,
   SipAccountInterface *acctIf,
   XmppAccountInterface *xmppAcctIf,
   PushToTalkManagerInterface* pttIf,
   SipAVConversationManagerInterface* convIf,
   SipConversationStateManager* stateIf,
   CPCAPI2::Phone* phone,
   const std::shared_ptr<resip::MultiReactor>& reactor) :
mCallbacks(callbacks),
mNetworkChangeManagerIf(dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(phone))),
mAccountInterface(acctIf),
#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
mXmppAccountInterface(xmppAcctIf),
#endif
#if (CPCAPI2_BRAND_PTT_MODULE == 1)
mPttManagerInterface(pttIf),
#endif
mConversationManager(convIf),
mPhone(dynamic_cast<PhoneInterface*>(phone)),
mAbortFutureHttpClients(false),
mLastIPChangeTime(0),
mReactor(reactor)
{
    mMainSdkThreadCheck.test();

    if (mAccountInterface != NULL)
    {
        mAccountInterface->addSdkObserver(this);
    }
#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
    if (mXmppAccountInterface != NULL)
    {
        mXmppAccountInterface->addSdkObserver(this);
    }
#endif

// TODO: Limit ptt to auto-test only until the schema has been verified
#if (CPCAPI2_BRAND_PTT_MODULE == 1)
   if (mPttManagerInterface != NULL)
   {
       mPttManagerInterface->addSdkObserver(this);
   }
#endif
   if (mConversationManager != NULL)
   {
      mConversationManager->addSdkObserver(this);
   }

   ((NetworkChangeManagerInterface*)mNetworkChangeManagerIf)->addSdkObserver(this);
}

void AnalyticsManagerImpl::Release()
{
   mMainSdkThreadCheck.test();

   if (mAccountInterface != NULL)
   {
      mAccountInterface->removeSdkObserver(this);
   }

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
   if (mXmppAccountInterface != NULL)
   {
      mXmppAccountInterface->removeSdkObserver(this);
   }
#endif

// TODO: Limit ptt to auto-test only until the schema has been verified
#if (CPCAPI2_BRAND_PTT_MODULE == 1)
   if (mPttManagerInterface != NULL)
   {
      mPttManagerInterface->removeSdkObserver(this);
   }
#endif

   if (mConversationManager != NULL)
   {
      mConversationManager->removeSdkObserver(this);
   }

   ((NetworkChangeManagerInterface*)mNetworkChangeManagerIf)->removeSdkObserver(this);

   reactorSafeReleaseAfter(mReactor.get());
}

AnalyticsManagerImpl::~AnalyticsManagerImpl()
{
   mAnalyticsThreadCheck.test();

   AnalyticsMap::const_iterator iter = mInfoMap.begin();
   for( ; iter != mInfoMap.end() ; ++iter )
   {
      // Erase all user-data objects in the registry
      RegistryNode *data = NULL;
      msrp_tree_enum *enumm = msrp_tree_enum_create(iter->second->registry);
      while (msrp_tree_enum_next(enumm, (void **)&data) == MSRP_TRUE)
         delete data;
      msrp_tree_enum_destroy(enumm);

      // Erase the registry itself
      msrp_tree_destroy(iter->second->registry);
      iter->second->registry = NULL;

      // Cleanup CURL
      delete iter->second->httpClient;

      // delete the map info object
      delete iter->second;
   }

   mInfoMap.clear();

   // Clear the different call-maps
   mActiveConvoList.clear();
   mConvoRecordedMap.clear();
   mConvoTransferredMap.clear();
   mConvoHeldMap.clear();
   mConvoStateMap.clear();
   mCurrentConvoMap.clear();
   mPttSessionActiveList.clear();
   mPttServiceStateMap.clear();
   mPttSessionStateMap.clear();
   mPttSessionCurrentMap.clear();
   mPttSessionIncomingMap.clear();

   mReactor->stop();
}

void AnalyticsManagerImpl::setCallbackHook( void (*cbHook)(void*), void* context )
{
   if( cbHook != NULL && context != NULL )
   {
      m_CbHook = std::bind( cbHook, context );
   }
}

void AnalyticsManagerImpl::setHandler(AnalyticsHandler* handler)
{
   DebugLog(<< "AnalyticsManagerImpl::setHandler(): " << this << " setting handler: " << handler);
   mHandler = handler;
}

int AnalyticsManagerImpl::open(const AnalyticsSettings& settings, const GeneralStats& stats, const AnalyticsHandle& inHandle)
{
   mAnalyticsThreadCheck.test();

   AnalyticsInfo *info = NULL;

   AnalyticsMap::const_iterator iter = mInfoMap.find(inHandle);
   if (iter == mInfoMap.end())
   {
      // Create a new Analytics Info object (complete with registry)
      info = new AnalyticsInfo;
      if (info == NULL)
      {
         InfoLog(<< "AnalyticsManagerImpl::open(): " << this << " could not allocate analytics info");
         return 0;
      }

      info->handle = inHandle;
      info->registry = msrp_tree_create();
      info->httpClient = new HTTPClient(dynamic_cast<PhoneInterface*>(mPhone));
   }
   else
   {
      info = iter->second;
      if (info == NULL)
      {
         DebugLog(<< "AnalyticsManagerImpl::open(): " << this << " analytics info not populated");
         return 0;
      }
   }

   std::string temp;
   std::string parameters; // with extra parameters appended that stretto requires

   parameters = "?user=";
   temp = resip::Data(settings.strettoUserName.c_str()).urlEncoded().c_str();
   boost::replace_all(temp, "@", "%40");  // this is not required for HTTP and therefore not done by the above, but it was here so I'm leaving it just in case
   parameters += temp;

   parameters += "&device=";
   temp = resip::Data(stats.deviceUUID.c_str()).urlEncoded().c_str();
   boost::replace_all(temp, "@", "%40");  // this is not required for HTTP and therefore not done by the above, but it was here so I'm leaving it just in case
   parameters += temp;

   std::string completeURL;
   if (!settings.serverURL.empty())
   {
      completeURL = settings.serverURL.c_str();
      completeURL += parameters;
      info->completeURL = completeURL.c_str();
      info->httpUsername = settings.httpUserName.c_str();
      info->httpPassword = settings.httpPassword.c_str();
   }

   // Validate the installation date
   unsigned int installDate( stats.installationDate );
   if (installDate > 0)
   {
      RegistryUtils::setInt64(info->registry, ROOT SEP GENERAL SEP "installationDate", installDate);
   }
   else
   {
      std::stringstream strValue;
      strValue << stats.launchTime.c_str();

      unsigned int intValue;
      strValue >> intValue;
      RegistryUtils::setInt64(info->registry, ROOT SEP GENERAL SEP "installationDate", intValue);
   }

   // Required section
   RegistryUtils::setString(info->registry, ROOT SEP GENERAL SEP "deviceUUID",            stats.deviceUUID.c_str());
   RegistryUtils::setString(info->registry, ROOT SEP GENERAL SEP "clientVersion",         stats.clientVersion.c_str());
   RegistryUtils::setString(info->registry, ROOT SEP GENERAL SEP "osType",                stats.osType.c_str());
   RegistryUtils::setString(info->registry, ROOT SEP GENERAL SEP "osVersion",             stats.osVersion.c_str());
   RegistryUtils::setString(info->registry, ROOT SEP GENERAL SEP "hardwareModel",         stats.hardwareModel.c_str());
   RegistryUtils::setString(info->registry, ROOT SEP GENERAL SEP "clientPublicIpAddress", stats.publicIPAddress.c_str());
   RegistryUtils::setString(info->registry, ROOT SEP GENERAL SEP "clientLaunchTime",      stats.launchTime.c_str());
   RegistryUtils::setString(info->registry, ROOT SEP GENERAL SEP "xmlTemplateVersion",    XML_TEMPLATE_VER);
   RegistryUtils::setString(info->registry, ROOT SEP GENERAL SEP "language",              stats.language.c_str());
   RegistryUtils::setString(info->registry, ROOT SEP GENERAL SEP "timezone",              stats.timezone.c_str());
   RegistryUtils::setString(info->registry, ROOT SEP GENERAL SEP "serialNumber",          stats.serialNumber.c_str());

   // Set the start of the new recording interval
   const int64_t curTime(secondsSinceEpoch());
   std::ostringstream strs;
   strs << curTime;
   std::string attrValue = strs.str();
   RegistryUtils::setAttribute(info->registry, ROOT, "start", attrValue.c_str());

   mInfoMap[info->handle] = info;

   DebugLog(<< "AnalyticsManagerImpl::open(): UEM: Handle Opened: " << info->handle << " with server-url: " << completeURL);
   return kSuccess;
}

int AnalyticsManagerImpl::close(const AnalyticsHandle& serverHandle)
{
    mAnalyticsThreadCheck.test();

    AnalyticsMap::const_iterator iter = mInfoMap.find(serverHandle);
    if (iter == mInfoMap.end())
        return kError;

    // Erase all user-data objects in the registry
    RegistryNode *data = NULL;
    msrp_tree_enum *enumm = msrp_tree_enum_create(iter->second->registry);
    while (msrp_tree_enum_next(enumm, (void **)&data) == MSRP_TRUE)
        delete data;
    msrp_tree_enum_destroy(enumm);

    // Erase the registry itself
    msrp_tree_destroy(iter->second->registry);
    iter->second->registry = NULL;

    // Cleanup CURL
    delete iter->second->httpClient;

    // delete the map info object
    delete iter->second;

    // Remove the entry from the map
    mInfoMap.erase(iter);

    DebugLog(<< "UEM: Handle Closed:  " << serverHandle);

    return kSuccess;
}

int AnalyticsManagerImpl::setPresenceStats( const AnalyticsHandle& serverHandle, const PresenceStats& stats )
{
   mAnalyticsThreadCheck.test();

   AnalyticsMap::const_iterator iter = mInfoMap.find( serverHandle );
   if( iter == mInfoMap.end() )
      return kError;

   // set the presence state on the registry
   RegistryUtils::setInt( iter->second->registry, ROOT SEP ACTIVITY_DATA SEP PRESENCE SEP "numContacts", stats.numContacts );
   RegistryUtils::setInt( iter->second->registry, ROOT SEP ACTIVITY_DATA SEP PRESENCE SEP "numContactsWithPresence", stats.numContactsWithPresence );

   DebugLog(<< "UEM: setPresenceStats called with handle:  " << serverHandle);
   return kSuccess;
}

int AnalyticsManagerImpl::setProvisioningStats( const AnalyticsHandle& serverHandle, const ProvisioningStats& stats )
{
   mAnalyticsThreadCheck.test();

   AnalyticsMap::const_iterator iter = mInfoMap.find( serverHandle );
   if( iter == mInfoMap.end() )
      return kError;

   // set the provisioning state on the registry
   RegistryUtils::setInt( iter->second->registry, ROOT SEP ACTIVITY_DATA SEP PROVISIONING SEP "successfulProvisionAttempts", stats.successfulProvisionAttempts );
   RegistryUtils::setInt( iter->second->registry, ROOT SEP ACTIVITY_DATA SEP PROVISIONING SEP "failedProvisionAttempts", stats.failedProvisionAttempts );

   DebugLog(<< "UEM: setProvisioningStats called with handle:  " << serverHandle);
   return kSuccess;
}

int AnalyticsManagerImpl::setStabilityStats(const AnalyticsHandle& serverHandle, const StabilityStats& stats)
{
   mAnalyticsThreadCheck.test();

   AnalyticsMap::const_iterator iter = mInfoMap.find(serverHandle);
   if (iter == mInfoMap.end())
      return kError;

   // set the stability value on the registry
   RegistryUtils::setInt(iter->second->registry, ROOT SEP ACTIVITY_DATA SEP STABILITY SEP "numCrashes", stats.numCrashes);

   DebugLog(<< "UEM: setStabilityStats called with handle:  " << serverHandle);
   return kSuccess;
}

int AnalyticsManagerImpl::setSettingsStats(const AnalyticsHandle& serverHandle, const SettingsStats& stats)
{
   mAnalyticsThreadCheck.test();

   AnalyticsMap::const_iterator iter = mInfoMap.find(serverHandle);
   if (iter == mInfoMap.end())
      return kError;

   std::string path = ROOT SEP SETTINGS_DATA SEP;
   SettingsStats::const_iterator iter2 = stats.begin();
   for (; iter2 != stats.end(); ++iter2)
   {
      // path passed in looks something like "cpc_usage_report/settings_data/historyDefaultAccount" so this is not account specific
      RegistryUtils::setString(iter->second->registry, (path + iter2->first.c_str()).c_str(), iter2->second.c_str());
   }

   DebugLog(<< "UEM: setSettingsStats called with handle:  " << serverHandle);
   return kSuccess;
}

int AnalyticsManagerImpl::onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args)
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onAccountStatusChangedImpl, this, account, args ));
   return kSuccess;
}

void AnalyticsManagerImpl::setAccountSettingsTransport(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipAccountTransportType transportType)
{
   SipSettingsMap::iterator itSettings = mSipSettingsMap.find(account);
   if (itSettings != mSipSettingsMap.end())
   {
      std::map<NetworkTransport, SipAccount::SipAccountSettings>::iterator itTransportSettings = itSettings->second.begin();
      for (; itTransportSettings != itSettings->second.end(); itTransportSettings++)
         itTransportSettings->second.sipTransportType = transportType;
   }
}

int AnalyticsManagerImpl::onAccountStatusChangedImpl(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args)
{
    mAnalyticsThreadCheck.test();

    // Loop over all the analytics handles and populate the information
    AnalyticsMap::const_iterator iter = mInfoMap.begin();
    for (; iter != mInfoMap.end(); ++iter)
    {
        const AnalyticsHandle& serverHandle(iter->first);
        msrp_tree* registry(iter->second->registry);
        std::string activityRoot;

        const char *itemName = "account";
        if (!RegistryUtils::listExists(registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot))
        {
            if (!RegistryUtils::createList(registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot))
                return kError;
        }

        // Check to see if there is an account list entry with the right account handle
        bool matchFound(false);
        std::string idString("SIP.");
        std::ostringstream strs;
        strs << account;
        idString += strs.str();

        std::string itemPath; // Set to point to correct item

        size_t listSize = RegistryUtils::getListSize(registry, activityRoot.c_str());
        for (size_t i = 0; i < listSize; ++i)
        {
            RegistryUtils::getListItem(registry, activityRoot.c_str(), i, itemPath);

            std::string attrValue;
            if (RegistryUtils::getAttribute(registry, itemPath.c_str(), ACTIVITY_ACCOUNT_ID_ATTR, attrValue))
            {
                if (attrValue == idString)
                {
                    matchFound = true;
                    break;
                }
            }
        }

        // If there was no match, create an entry for this account.
        if (!matchFound)
        {
            if (!RegistryUtils::addListItem(registry, activityRoot.c_str(), itemPath))
                return kError;

            // Save the Account ID attribute
            if (!RegistryUtils::setAttribute(registry, itemPath.c_str(), ACTIVITY_ACCOUNT_ID_ATTR, idString))
                return kError;

            mAccumulatedSipAccountList.insert(account);
        }

        // Temp var for building paths
        std::string tempPath;

        // Save the configuration (whatever we can)
        tempPath = itemPath; tempPath += SEP; tempPath += "failedRegistrations";

        int failedCount = 0;
        bool enabled = mEnabledSipAccountList.find(account) != mEnabledSipAccountList.end();
        // don't count a disabled and unregistered account as a failure
        if ((enabled && args.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistered) || 
            args.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_WaitingToRegister) {
            if (!RegistryUtils::getInt(registry, tempPath.c_str(), failedCount)) {
                //set to zero
                failedCount = 0;
            }
            //get and increment the current count
            RegistryUtils::setInt(registry, tempPath.c_str(), ++failedCount);
        }
        if (args.accountStatus == CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Registered) {
            //get and if not exist then set to zero as initial value
            if (!RegistryUtils::getInt(registry, tempPath.c_str(), failedCount)) {
                RegistryUtils::setInt(registry, tempPath.c_str(), 0);
            }
            
            //if the account is successfully registered, then get the final transport, in case it was initially set to AUTO mode
            if (args.transportType != CPCAPI2::SipAccount::SipAccountTransportType::SipAccountTransport_Auto &&
               args.transportType != CPCAPI2::SipAccount::SipAccountTransportType::SipAccountTransport_Unknown)
            {
               this->setAccountSettingsTransport(account, args.transportType);
            }
        }

        if (!matchFound && failedCount > 0)    // we keep track of accounts having statistics so we can send them even after they are removed, but 
        {                                      // we don't count a failed count of 0 as activity needing to be provided after the account is removed
            mSipAccountWithStatsList.insert(account);
        }

        DebugLog(<< "UEM: onAccountStatusChanged called with handle:  " << serverHandle);
    }

    return kSuccess;
}

int AnalyticsManagerImpl::xmppAccountStatusChangeFired(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& args)
{
#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
    mAnalyticsThreadCheck.test();

    const char *itemName = "account";

    // Loop over all the analytics handles and populate the information
    AnalyticsMap::const_iterator iter = mInfoMap.begin();
    for (; iter != mInfoMap.end(); ++iter)
    {
        const AnalyticsHandle& serverHandle(iter->first);
        msrp_tree* registry(iter->second->registry);
        std::string activityRoot;

        if (!RegistryUtils::listExists(registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot))
        {
            if (!RegistryUtils::createList(registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot))
                return kError;
        }

        // Check to see if there is an account list entry with the right account handle
        bool matchFound(false);
        std::string idString("XMPP.");
        std::ostringstream strs;
        strs << account;
        idString += strs.str();

        std::string itemPath; // Set to point to correct item

        size_t listSize = RegistryUtils::getListSize(registry, activityRoot.c_str());
        for (size_t i = 0; i < listSize; ++i)
        {
            RegistryUtils::getListItem(registry, activityRoot.c_str(), i, itemPath);

            std::string attrValue;
            if (RegistryUtils::getAttribute(registry, itemPath.c_str(), ACTIVITY_ACCOUNT_ID_ATTR, attrValue))
            {
                if (attrValue == idString)
                {
                    matchFound = true;
                    break;
                }
            }
        }

        // If there was no match, create an entry for this account.
        if (!matchFound)
        {
            if (!RegistryUtils::addListItem(registry, activityRoot.c_str(), itemPath))
                return kError;

            // Save the Account ID attribute
            if (!RegistryUtils::setAttribute(registry, itemPath.c_str(), ACTIVITY_ACCOUNT_ID_ATTR, idString))
                return kError;

            mAccumulatedXmppAccountList.insert(account);
        }

        // Temp var for building paths
        std::string tempPath;

        // Save the configuration (whatever we can)
        tempPath = itemPath; tempPath += SEP; tempPath += "failedRegistrations";

        int failedCount = 0;
        bool enabled = mEnabledXmppAccountList.find(account) != mEnabledXmppAccountList.end();
        // don't count a disabled and unregistered account as a failure
        if ((enabled && args.accountStatus == CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Disconnected) || 
            args.accountStatus == CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Failure)
        {
            //account register failed, so increase counter and set the new value to registry
            if (args.errorCode != CPCAPI2::XmppAccount::Error::Error_None)
            {
                if (!RegistryUtils::getInt(registry, tempPath.c_str(), failedCount)) {
                    //set to zero
                    failedCount = 0;
                }
                //get and increment the current count
                RegistryUtils::setInt(registry, tempPath.c_str(), ++failedCount);
            }
        }
        if (args.accountStatus == CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connected) {
            //get and if not exist then set to zero as initial value
            if (!RegistryUtils::getInt(registry, tempPath.c_str(), failedCount)) {
                RegistryUtils::setInt(registry, tempPath.c_str(), 0);
            }
        }

        if (!matchFound && failedCount > 0)    // we keep track of accounts having statistics so we can send them even after they are removed, but 
        {                                      // we don't count a failed count of 0 as activity needing to be provided after the account is removed
            mXmppAccountWithStatsList.insert(account);
        }

        DebugLog(<< "UEM: xmppAccountStatusChangeFired called with handle:  " << serverHandle);
    }
#endif // #if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
    return kSuccess;
}

int AnalyticsManagerImpl::pttServiceStatusChangeFired(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PttServiceStatusChangedEvent& args)
{
   return kSuccess;
}

int AnalyticsManagerImpl::pttSessionStatusChangeFired(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttSessionStateChangedEvent& args)
{
   return kSuccess;
}

int AnalyticsManagerImpl::CurrentVideoDeviceUpdatedFired(CPCAPI2::Media::VideoDeviceInfo devInfo)
{
    mAnalyticsThreadCheck.test();

    mCurrentVideoDeviceName = devInfo.friendlyName;
    return kSuccess;
}

int AnalyticsManagerImpl::onAccountConfigured(
   CPCAPI2::SipAccount::SipAccountHandle account,
   const CPCAPI2::SipAccount::SipAccountConfiguredEvent& args )
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onAccountConfiguredSipImpl, this, account, args ));
   return kSuccess;
}

int AnalyticsManagerImpl::onAccountConfiguredSipImpl(
   CPCAPI2::SipAccount::SipAccountHandle account,
   const CPCAPI2::SipAccount::SipAccountConfiguredEvent& args )
{
   mAnalyticsThreadCheck.test();

   mSipSettingsMap[account] = args.settings;

   mAccumulatedSipAccountList.insert(account);

   AnalyticsMap::const_iterator iter = mInfoMap.begin();
   for( ; iter != mInfoMap.end() ; ++iter )
   {
      const AnalyticsHandle& serverHandle( iter->first );
      DebugLog(<< "UEM: onAccountConfigured SIP called with handle:  " << serverHandle);
   }

   return kSuccess;
}

int AnalyticsManagerImpl::onAccountEnabled( CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountEnabledEvent& args )
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onAccountEnabledSipImpl, this, account, args ));
   return kSuccess;
}

int AnalyticsManagerImpl::onAccountEnabledSipImpl( CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountEnabledEvent& args )
{
   mAnalyticsThreadCheck.test();

   mEnabledSipAccountList.insert(account);

   return kSuccess;
}

int AnalyticsManagerImpl::onAccountDisabled( CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountDisabledEvent& args )
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onAccountDisabledSipImpl, this, account, args ));
   return kSuccess;
}

int AnalyticsManagerImpl::onAccountDisabledSipImpl( CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountDisabledEvent& args )
{
   mAnalyticsThreadCheck.test();

   mEnabledSipAccountList.erase(account);
   return kSuccess;
}

int AnalyticsManagerImpl::onAccountConfigured(
   CPCAPI2::XmppAccount::XmppAccountHandle account,
   const CPCAPI2::XmppAccount::XmppAccountConfiguredEvent& args )
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onAccountConfiguredXmppImpl, this, account, args ));
   return kSuccess;
}

int AnalyticsManagerImpl::onAccountConfiguredXmppImpl(
   CPCAPI2::XmppAccount::XmppAccountHandle account,
   const CPCAPI2::XmppAccount::XmppAccountConfiguredEvent& args )
{
   mAnalyticsThreadCheck.test();

   mXmppSettingsMap[account] = args.settings;
   
   mAccumulatedXmppAccountList.insert(account);

   AnalyticsMap::const_iterator iter = mInfoMap.begin();
   for( ; iter != mInfoMap.end() ; ++iter )
   {
      const AnalyticsHandle& serverHandle( iter->first );
      DebugLog(<< "UEM: onAccountConfigured XMPP called with handle:  " << serverHandle);
   }

   return kSuccess;
}

int AnalyticsManagerImpl::onAccountEnabled( CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountEnabledEvent& args )
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onAccountEnabledXmppImpl, this, account, args ));
   return kSuccess;
}

int AnalyticsManagerImpl::onAccountEnabledXmppImpl( CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountEnabledEvent& args )
{
    mAnalyticsThreadCheck.test();

   mEnabledXmppAccountList.insert(account);

   return kSuccess;
}

int AnalyticsManagerImpl::onAccountDisabled( CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountDisabledEvent& args )
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onAccountDisabledXmppImpl, this, account, args ));
   return kSuccess;
}

int AnalyticsManagerImpl::onAccountDisabledXmppImpl( CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountDisabledEvent& args )
{
    mAnalyticsThreadCheck.test();

   mEnabledXmppAccountList.erase(account);
   return kSuccess;
}

int AnalyticsManagerImpl::onPttServiceStatusChanged(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PttServiceStatusChangedEvent& args)
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onPttServiceStatusChangedImpl, this, service, args));
   return kSuccess;
}

int AnalyticsManagerImpl::onPttServiceStatusChangedImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PttServiceStatusChangedEvent& args)
{
   mAnalyticsThreadCheck.test();

   // Verify the service handle is valid
   bool serviceFound = (mPttServiceStateMap.find(service) != mPttServiceStateMap.end());
   if (!serviceFound)
   {
      InfoLog(<< "UEM: onPttServiceStatusChanged invalid ptt service handle: " << service);
      return kError;
   }

   mPttServiceStateMap[service] = args.status;

   // Only interested in service ready status
   if (args.status != PushToTalk::PttServiceStatusChangedEvent::Status_Ready)
   {
      return kSuccess;
   }

   bool settingsFound = (mPttSettingsMap.find(service) != mPttSettingsMap.end());
   if (!settingsFound)
   {
      InfoLog(<< "UEM: onPttServiceStatusChanged settings not found for ptt service handle: " << service);
      return kError;
   }

   PushToTalk::PushToTalkServiceSettings settings = mPttSettingsMap[service];

   const char* itemName = "account";

   // Loop over all the analytics handles and populate the information
   for (AnalyticsMap::const_iterator iter = mInfoMap.begin(); iter != mInfoMap.end() ; ++iter)
   {
      const AnalyticsHandle& serverHandle(iter->first);
      msrp_tree* registry(iter->second->registry);
      std::string settingsRoot;

      if (!RegistryUtils::listExists(registry, ROOT SEP SETTINGS_DATA, itemName, settingsRoot))
      {
         if (!RegistryUtils::createList(registry, ROOT SEP SETTINGS_DATA, itemName, settingsRoot))
         {
            return kError;
         }
      }

      // Check to see if there is an account list entry with the right account handle
      bool matchFound(false);
      std::string idString("PTT.");
      std::ostringstream strs;
      strs << service;
      idString += strs.str();

      std::string itemPath; // Set to point to correct item

      size_t listSize = RegistryUtils::getListSize(registry, settingsRoot.c_str());
      for (size_t i = 0 ; i < listSize ; ++i)
      {
         RegistryUtils::getListItem(registry, settingsRoot.c_str(), i, itemPath);

         std::string attrValue;
         if (RegistryUtils::getAttribute(registry, itemPath.c_str(), SETTINGS_ACCOUNT_ID_ATTR, attrValue))
         {
            if (attrValue == idString)
            {
               matchFound = true;
               break;
            }
         }
      }

      // If there was no match, create an entry for this account.
      if (!matchFound)
      {
         InfoLog(<< "UEM: onPttServiceStatusChanged account-id: " << idString << " not found in data tree for ptt service handle: " << service);
         return kError;
      }

      // Start creating configuration items under the itemPath
      // <account id=\"#6:SipAccountInfo_SettingsData\">
      //    <data name="..." value="..." />
      //     etc...
      // </account>"

      // Temp var for building paths
      std::string tempPath = itemPath; tempPath += SEP; tempPath += "enabled";
      RegistryUtils::setBool(registry, tempPath.c_str(), true); // account is now Status_Ready

      DebugLog(<< "UEM: onPttServiceStatusChanged called with ptt service handle: " << service);
   }

   return kSuccess;
}

int AnalyticsManagerImpl::onPttEndpointList(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PttEndpointListEvent& args)
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onPttEndpointListImpl, this, service, args));
   return kSuccess;
}

int AnalyticsManagerImpl::onPttEndpointListImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PttEndpointListEvent& args)
{
   return kSuccess;
}

int AnalyticsManagerImpl::onPttServiceConfigured(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PttServiceConfiguredEvent& args)
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onPttServiceConfiguredImpl, this, service, args));
   return kSuccess;
}

int AnalyticsManagerImpl::onPttServiceConfiguredImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PttServiceConfiguredEvent& args)
{
#if (CPCAPI2_BRAND_PTT_MODULE == 1)
   mAnalyticsThreadCheck.test();

   const CPCAPI2::PushToTalk::PushToTalkServiceSettings& settings(args.settings);
   mPttSettingsMap[service] = args.settings;
   bool serviceFound = (mPttServiceStateMap.find(service) != mPttServiceStateMap.end());
   if (!serviceFound)
   {
      mPttServiceStateMap[service] = PttServiceStatusChangedEvent::Status_Disabled;
   }

   const char* itemName = "account";

   // Loop over all the analytics handles and populate the information
   for (AnalyticsMap::const_iterator iter = mInfoMap.begin(); iter != mInfoMap.end() ; ++iter)
   {
      const AnalyticsHandle& serverHandle(iter->first);
      msrp_tree* registry(iter->second->registry);
      std::string settingsRoot;

      if (!RegistryUtils::listExists(registry, ROOT SEP SETTINGS_DATA, itemName, settingsRoot))
      {
         if (!RegistryUtils::createList(registry, ROOT SEP SETTINGS_DATA, itemName, settingsRoot))
         {
            return kError;
         }
      }

      // Check to see if there is an account list entry with the right account handle
      bool matchFound(false);
      std::string idString("PTT.");
      std::ostringstream strs;
      strs << service;
      idString += strs.str();

      std::string itemPath; // Set to point to correct item

      size_t listSize = RegistryUtils::getListSize(registry, settingsRoot.c_str());
      for (size_t i = 0 ; i < listSize ; ++i)
      {
         RegistryUtils::getListItem(registry, settingsRoot.c_str(), i, itemPath);

         std::string attrValue;
         if (RegistryUtils::getAttribute(registry, itemPath.c_str(), SETTINGS_ACCOUNT_ID_ATTR, attrValue))
         {
            if (attrValue == idString)
            {
               matchFound = true;
               break;
            }
         }
      }

      std::string domain("");
      std::string primaryIdentity = settings.getPrimaryLocalIdentity().c_str();
      size_t pos = primaryIdentity.find("@");
      if (pos == std::string::npos)
      {
         domain = mCurrentIP.c_str();
      }
      else
      {
         domain = primaryIdentity.substr(pos + 1, std::string::npos);
      }

      // If there was no match, create an entry for this account.
      if (!matchFound)
      {
         // Do not create any new account tags if domain is null
         if (domain.c_str() == NULL)
         {
            return kError;
         }

         if (!RegistryUtils::addListItem(registry, settingsRoot.c_str(), itemPath))
         {
            return kError;
         }

         // Save the Account ID attribute
         if (!RegistryUtils::setAttribute(registry, itemPath.c_str(), SETTINGS_ACCOUNT_ID_ATTR, idString))
         {
            return kError;
         }
      }

      // Start creating configuration items under the itemPath
      // <account id=\"#6:SipAccountInfo_SettingsData\">
      //    <data name="..." value="..." />
      //     etc...
      // </account>"

      // Temp var for building paths
      std::string tempPath;

      // Save the configuration (whatever we can)
      if (domain.c_str() != NULL)
      {
         tempPath = itemPath; tempPath += SEP; tempPath += "domain";
         RegistryUtils::setString(registry, tempPath.c_str(), domain.c_str());
      }

      tempPath = itemPath; tempPath += SEP; tempPath += "username";
      RegistryUtils::setString(registry, tempPath.c_str(), primaryIdentity.c_str());

      tempPath = itemPath; tempPath += SEP; tempPath += "protocol";
      RegistryUtils::setString(registry, tempPath.c_str(), "PTT");

      std::string serviceType("");
      if (mPttSettingsMap[service].serviceType == PushToTalk::PttServiceType_LAN)
      {
         serviceType = "LAN";
      }
      else if (mPttSettingsMap[service].serviceType == PushToTalk::PttServiceType_WAN)
      {
         serviceType = "WAN";
      }
      tempPath = itemPath; tempPath += SEP; tempPath += "serviceType";
      RegistryUtils::setString(registry, tempPath.c_str(), serviceType.c_str());

      tempPath = itemPath; tempPath += SEP; tempPath += "enabled";
      RegistryUtils::setBool(registry, tempPath.c_str(), false); // Wait for service status connected before setting flag to connected

      tempPath = itemPath; tempPath += SEP; tempPath += "signalingTransport";
      switch (settings.serviceType)
      {
         case CPCAPI2::PushToTalk::PttServiceType_LAN:
            RegistryUtils::setString(registry, tempPath.c_str(), "UDP");
            break;
         case CPCAPI2::PushToTalk::PttServiceType_WAN:
            RegistryUtils::setString(registry, tempPath.c_str(), "TCP");
            break;
      }

      tempPath = itemPath; tempPath += SEP; tempPath += "mediaInactivityIntervalSeconds";
      RegistryUtils::setInt(registry, tempPath.c_str(), mPttSettingsMap[service].mediaInactivityIntervalSeconds);

      tempPath = itemPath; tempPath += SEP; tempPath += "signalingDscp";
      RegistryUtils::setInt(registry, tempPath.c_str(), mPttSettingsMap[service].signallingDscp);

      tempPath = itemPath; tempPath += SEP; tempPath += "mediaDscp";
      RegistryUtils::setInt(registry, tempPath.c_str(), mPttSettingsMap[service].mediaDscp);

      tempPath = itemPath; tempPath += SEP; tempPath += "lanUnicastBindAddress";
      RegistryUtils::setString(registry, tempPath.c_str(), mPttSettingsMap[service].unicastBindAddress.c_str());

      tempPath = itemPath; tempPath += SEP; tempPath += "lanUnicastPort";
      RegistryUtils::setInt(registry, tempPath.c_str(), mPttSettingsMap[service].unicastPort);

      tempPath = itemPath; tempPath += SEP; tempPath += "lanUnicastDiscoveryEnabled";
      RegistryUtils::setBool(registry, tempPath.c_str(), mPttSettingsMap[service].unicastDiscoveryEnabled);

      tempPath = itemPath; tempPath += SEP; tempPath += "lanKeepAliveEnabled";
      RegistryUtils::setBool(registry, tempPath.c_str(), mPttSettingsMap[service].keepAliveEnabled);

      tempPath = itemPath; tempPath += SEP; tempPath += "lanEndpointListAutoUpdateEnabled";
      RegistryUtils::setBool(registry, tempPath.c_str(), mPttSettingsMap[service].endpointListAutoUpdateEnabled);

      tempPath = itemPath; tempPath += SEP; tempPath += "lanEndpointListFetchLimit";
      RegistryUtils::setInt(registry, tempPath.c_str(), mPttSettingsMap[service].endpointListFetchLimit);

      tempPath = itemPath; tempPath += SEP; tempPath += "wanAuthServiceAddress";
      RegistryUtils::setString(registry, tempPath.c_str(), mPttSettingsMap[service].authServiceAddress.c_str());

      tempPath = itemPath; tempPath += SEP; tempPath += "wanConfServiceAddress";
      RegistryUtils::setString(registry, tempPath.c_str(), mPttSettingsMap[service].confServiceAddress.c_str());

      const char* identityItemName = "identity";
      tempPath = itemPath;
      if (!RegistryUtils::listExists(registry, itemPath.c_str(), identityItemName, settingsRoot))
      {
         if (!RegistryUtils::createList(registry, tempPath.c_str(), identityItemName, settingsRoot))
         {
            DebugLog(<< "AnalyticsManagerImpl::onPttServiceConfiguredImpl(): ptt service: " << service << " failed to create identity list");
            return kError;
         }
      }

      for (auto i = mPttSettingsMap[service].localIdentities.begin(); i != mPttSettingsMap[service].localIdentities.end(); ++i)
      {
         std::string listItemPath = itemPath;
         if (!RegistryUtils::addListItem(registry, settingsRoot.c_str(), listItemPath))
         {
            DebugLog(<< "AnalyticsManagerImpl::onPttServiceConfiguredImpl(): ptt service: " << service << " failed to add item to identity list");
            return kError;
         }

         // Save the Identity ID attribute
         if (!RegistryUtils::setAttribute(registry, listItemPath.c_str(), SETTINGS_ACCOUNT_ID_ATTR, i->getIdentityString().c_str()))
         {
            DebugLog(<< "AnalyticsManagerImpl::onPttServiceConfiguredImpl(): ptt service: " << service << " failed to set attribute in identity list");
            return kError;
         }
      }

      const char* channelItemName = "channel";
      tempPath = itemPath;
      if (!RegistryUtils::listExists(registry, itemPath.c_str(), channelItemName, settingsRoot))
      {
         if (!RegistryUtils::createList(registry, tempPath.c_str(), channelItemName, settingsRoot))
         {
            DebugLog(<< "AnalyticsManagerImpl::onPttServiceConfiguredImpl(): ptt service: " << service << " failed to create channel list");
            return kError;
         }
      }

      for (auto i = mPttSettingsMap[service].subscribedChannels.begin(); i != mPttSettingsMap[service].subscribedChannels.end(); ++i)
      {
         std::string listItemPath = itemPath;
         if (!RegistryUtils::addListItem(registry, settingsRoot.c_str(), listItemPath))
         {
            DebugLog(<< "AnalyticsManagerImpl::onPttServiceConfiguredImpl(): ptt service: " << service << " failed to add item to channel list");
            return kError;
         }

         // Save the Identity ID attribute
         if (!RegistryUtils::setAttribute(registry, listItemPath.c_str(), SETTINGS_ACCOUNT_ID_ATTR, (*i).c_str()))
         {
            DebugLog(<< "AnalyticsManagerImpl::onPttServiceConfiguredImpl(): ptt service: " << service << " failed to set attribute in channel list");
            return kError;
         }
      }

      const char* rangeItemName = "lanIpAddressRange";
      tempPath = itemPath;
      if (!RegistryUtils::listExists(registry, itemPath.c_str(), rangeItemName, settingsRoot))
      {
         if (!RegistryUtils::createList(registry, tempPath.c_str(), rangeItemName, settingsRoot))
         {
            DebugLog(<< "AnalyticsManagerImpl::onPttServiceConfiguredImpl(): ptt service: " << service << " failed to create range list");
            return kError;
         }
      }

      for (auto i = mPttSettingsMap[service].unicastIpRanges.begin(); i != mPttSettingsMap[service].unicastIpRanges.end(); ++i)
      {
         std::string listItemPath = itemPath;
         if (!RegistryUtils::addListItem(registry, settingsRoot.c_str(), listItemPath))
         {
            DebugLog(<< "AnalyticsManagerImpl::onPttServiceConfiguredImpl(): ptt service: " << service << " failed to add item to range list");
            return kError;
         }

         // Save the start and end ip range attributes

         if (!RegistryUtils::setAttribute(registry, listItemPath.c_str(), "ipAddrStart", i->ipAddrStart.c_str()))
         {
            DebugLog(<< "AnalyticsManagerImpl::onPttServiceConfiguredImpl(): ptt service: " << service << " failed to set start attribute in range list");
            return kError;
         }

         if (!RegistryUtils::setAttribute(registry, listItemPath.c_str(), "ipAddrEnd", i->ipAddrEnd.c_str()))
         {
            DebugLog(<< "AnalyticsManagerImpl::onPttServiceConfiguredImpl(): ptt service: " << service << " failed to set end attribute in range list");
            return kError;
         }
      }

      DebugLog(<< "UEM: onPttServiceConfigured called with ptt service handle: " << service);
   }
#endif

   return kSuccess;
}

int AnalyticsManagerImpl::onPttSessionStateChanged(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttSessionStateChangedEvent& args)
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onPttSessionStateChangedImpl, this, session, args));
   return kSuccess;
}

int AnalyticsManagerImpl::onPttSessionStateChangedImpl(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttSessionStateChangedEvent& args)
{
   mAnalyticsThreadCheck.test();

   // Verify the service handle is valid
   bool serviceFound = (mPttServiceStateMap.find(args.service) != mPttServiceStateMap.end());
   if (!serviceFound)
   {
      DebugLog(<< "AnalyticsManagerImpl::onPttSessionStateChangedImpl(): session: " << session << " service: " << args.service <<  " not found");
      return kError;
   }

   bool sessionFound = (mPttSessionStateMap.find(session) != mPttSessionStateMap.end());

   if ((args.currentState == CPCAPI2::PushToTalk::PttSessionState_Idle) && sessionFound)
   {
      handlePttSessionEnd(session, args);
   }
   else
   {
      handlePttSessionUpdate(session, args);
   }
   return kSuccess;
}

int AnalyticsManagerImpl::onPttIncomingCall(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttIncomingCallEvent& args)
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onPttIncomingCallImpl, this, session, args));
   return kSuccess;
}

int AnalyticsManagerImpl::onPttIncomingCallImpl(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttIncomingCallEvent& args)
{
   // Verify the service handle is valid
   bool serviceFound = (mPttServiceStateMap.find(args.service) != mPttServiceStateMap.end());
   if (!serviceFound)
   {
      DebugLog(<< "AnalyticsManagerImpl::onPttIncomingCallImpl(): session: " << session << " service: " << args.service <<  " not found");
      return kError;
   }

   bool found = (mPttSessionIncomingMap.find(session) != mPttSessionIncomingMap.end());
   DebugLog(<< "AnalyticsManagerImpl::onPttIncomingCallImpl(): session: " << session << " service: " << args.service <<  " current-state: " << args.currentState << " session-found: " << found);
   if (!found)
   {
      mPttSessionIncomingMap[session] = args.callerIdentity;
   }
   return kSuccess;
}

int AnalyticsManagerImpl::onPttSessionError(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttSessionErrorEvent& args)
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onPttSessionErrorImpl, this, session, args));
   return kSuccess;
}

int AnalyticsManagerImpl::onPttSessionErrorImpl(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttSessionErrorEvent& args)
{
   // Verify the service handle is valid
   bool serviceFound = (mPttServiceStateMap.find(args.service) != mPttServiceStateMap.end());
   if (!serviceFound)
   {
      DebugLog(<< "AnalyticsManagerImpl::onPttSessionErrorImpl(): session: " << session << " service: " << args.service <<  " not found");
      return kError;
   }

   DebugLog(<< "AnalyticsManagerImpl::onPttSessionErrorImpl(): session: " << session << " service: " << args.service << " session-error: " << args.errorCode);
   std::map<CPCAPI2::PushToTalk::PushToTalkSessionHandle, PttSessionState>::iterator it = mPttSessionStateMap.find(session);
   if (it == mPttSessionStateMap.end())
   {
      DebugLog(<< "AnalyticsManagerImpl::onPttSessionErrorImpl(): session: " << session << " service: " << args.service <<  " session not found");
   }
   else
   {
      it->second.sessionError = args.errorCode;
   }
   return kSuccess;
}

int AnalyticsManagerImpl::onPttReceiverDisconnected(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttReceiverDisconnectedEvent& args)
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onPttReceiverDisconnectedImpl, this, session, args));
   return kSuccess;
}

int AnalyticsManagerImpl::onPttReceiverDisconnectedImpl(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttReceiverDisconnectedEvent& args)
{
   return kSuccess;
}

int AnalyticsManagerImpl::handlePttSessionUpdate(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttSessionStateChangedEvent& args)
{
   mAnalyticsThreadCheck.test();

   bool sessionFound = (mPttSessionStateMap.find(session) != mPttSessionStateMap.end());
   if (sessionFound)
   {
      DebugLog(<< "AnalyticsManagerImpl::handlePttSessionUpdate(): session: " << session << " service: " << args.service <<  " updating state from: " << mPttSessionStateMap[session].currentState << " to: " << args.currentState);
      mPttSessionStateMap[session].currentState = args.currentState;
      mPttSessionStateMap[session].responsesReceived = args.mediaInfoReceived;
   }
   else
   {
      auto i = mPttSessionIncomingMap.find(session);

      PttSessionState state;
      state.session = session;
      state.service = args.service;
      state.currentState = args.currentState;
      state.channelId = args.channelId;
      state.responsesReceived = args.mediaInfoReceived;
      if (i != mPttSessionIncomingMap.end())
      {
         state.remoteIdentity = i->second;
      }
      else
      {
         // TODO: SDK actually supports multiple recipients, is that a supported requirement
         PushToTalk::PttIdentity channelId;
         channelId.userName = args.channelId;
         state.remoteIdentity = ((args.recipients.size() > 0) ? args.recipients[0] : channelId);
      }
      mPttSessionStateMap[session] = state;

      DebugLog(<< "AnalyticsManagerImpl::handlePttSessionUpdate(): session: " << session << " service: " << args.service <<  " adding session to session-state map with current state: " << args.currentState);
   }

   const char* itemName = "call";

   // Only interested in active state
   /// if ((args.currentState != CPCAPI2::PushToTalk::PttSessionState_Active) && (args.currentState != CPCAPI2::PushToTalk::PttSessionState_Talking))
   /*
   if (args.currentState != CPCAPI2::PushToTalk::PttSessionState_Active)
   {
      return kSuccess;
   }
   */

   PttSessionState state;
   getPttState(session, state);

   // Save the current state of the connected call if its not added already (in some cases the SDK sends the connected state event more than once)
   bool alreadyExists = (std::find(mPttSessionActiveList.begin(), mPttSessionActiveList.end(), session) != mPttSessionActiveList.end());
   if (!alreadyExists)
   {
      mPttSessionActiveList.push_back(session);
   }

   // Loop over all the analytics handles and populate the information
   for (AnalyticsMap::const_iterator iter = mInfoMap.begin(); iter != mInfoMap.end() ; ++iter)
   {
      msrp_tree* registry(iter->second->registry);
      std::string activityRoot;

      // Create the call list if it doesn't exist
      StackLog(<< "AnalyticsManagerImpl::handlePttSessionUpdate(): itemName: " << itemName);
      if (!RegistryUtils::listExists(registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot))
      {
         StackLog(<< "AnalyticsManagerImpl::handlePttSessionUpdate(): itemName: " << itemName << " activityRoot: " << activityRoot);
         if (!RegistryUtils::createList(registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot))
         {
            InfoLog(<< "AnalyticsManagerImpl::handlePttSessionUpdate(): error creating list for itemName: " << itemName);
            return kError;
         }
      }

      // Check to see if there is an account list entry with the right account handle
      bool sessionMatchFound(false);
      std::string idString("PTT.");
      std::ostringstream ssac;
      ssac << state.service;
      idString += ssac.str();
      std::stringstream ssid;
      ssid << session;
      std::string sessionId(ssid.str());

      std::string itemPath; // Set to point to correct item

      size_t listSize = RegistryUtils::getListSize(registry, activityRoot.c_str());
      StackLog(<< "AnalyticsManagerImpl::handlePttSessionUpdate(): list size: " << listSize << " for activityRoot: " << activityRoot);
      for (size_t i = 0 ; i < listSize; ++i)
      {
         RegistryUtils::getListItem(registry, activityRoot.c_str(), i, itemPath);
         StackLog(<< "AnalyticsManagerImpl::handlePttSessionUpdate(): itemPath: " << itemPath << " activityRoot: " << activityRoot << " index: " << i);

         std::string attrValue;
         if (RegistryUtils::getAttribute(registry, itemPath.c_str(), ACTIVITY_ACCOUNT_ID_ATTR, attrValue))
         {
            StackLog(<< "AnalyticsManagerImpl::handlePttSessionUpdate(): itemPath: " << itemPath << " attribute: " << ACTIVITY_ACCOUNT_ID_ATTR << " attrValue: " << attrValue << " index: " << i);
            if (attrValue == idString)
            {
               if (RegistryUtils::getAttribute(registry, itemPath.c_str(), SESSION_ID_ATTR, attrValue))
               {
                  StackLog(<< "AnalyticsManagerImpl::handlePttSessionUpdate(): itemPath: " << itemPath << " attribute: " << SESSION_ID_ATTR << " attrValue: " << attrValue << " index: " << i);
                  if (attrValue == sessionId)
                  {
                     sessionMatchFound = true;
                     break;
                  }
               }
            }
         }
      }

      // If there was no match, create an entry for this account, if it is a ptt account
      if (!sessionMatchFound)
      {
         StackLog(<< "AnalyticsManagerImpl::handlePttSessionUpdate(): match not found for id-string: " << idString);

         if (!RegistryUtils::addListItem(registry, activityRoot.c_str(), itemPath))
         {
            InfoLog(<< "AnalyticsManagerImpl::handlePttSessionUpdate(): error adding item to list with activityRoot: " << activityRoot);
            return kError;
         }

         // Save the Account ID attribute
         if (!RegistryUtils::setAttribute(registry, itemPath.c_str(), ACTIVITY_ACCOUNT_ID_ATTR, idString.c_str()))
         {
            InfoLog(<< "AnalyticsManagerImpl::handlePttSessionUpdate(): error setting attribute: " << ACTIVITY_ACCOUNT_ID_ATTR << " with attrValue: " << idString << " on itemPath: " << itemPath);
            return kError;
         }

         // Save the Session ID attribute
         if (!RegistryUtils::setAttribute(registry, itemPath.c_str(), SESSION_ID_ATTR, sessionId.c_str()))
         {
            InfoLog(<< "AnalyticsManagerImpl::handlePttSessionUpdate(): error setting attribute: " << SESSION_ID_ATTR << " with attrValue: " << sessionId << " on itemPath: " << itemPath);
            return kError;
         }
      }

      // If we get here then the ptt session is connected and data will be written to report for it,
      // it is now considered the current / latest call for this report because each report has only
      // one set of call info per PTT service
      mPttSessionCurrentMap[idString] = session;

      // Record the starting time for the call in seconds since the epoch.
      std::string tmp = "";
      if ((args.currentState == CPCAPI2::PushToTalk::PttSessionState_Initiated) || ((args.currentState == CPCAPI2::PushToTalk::PttSessionState_Idle) && (args.previousState != CPCAPI2::PushToTalk::PttSessionState_Idle)))
      {
         tmp = itemPath; tmp += SEP; tmp += "callStart";
         RegistryUtils::setInt64(registry, tmp.c_str(), secondsSinceEpoch());
      }
      else if (args.currentState == CPCAPI2::PushToTalk::PttSessionState_Active)
      {
         mPttSessionStateMap[session].successful = true;
      }

      // Add USB Device info to report
      CPCAPI2::Media::MediaManager* media = CPCAPI2::Media::MediaManager::getInterface(mPhone);
      CPCAPI2::Media::Audio* audio = CPCAPI2::Media::Audio::getInterface(media);
      webrtc_recon::MediaStackImpl* mStack = (static_cast<AudioInterface*>(audio))->media_stack();
      webrtc::AudioDeviceModule* adm = mStack->voe_base()->audio_device();

      char playoutName[128];
      char playoutGuid[128];
      char recordingName[128];
      char recordingGuid[128];
      uint16_t indexPlayOut = 8000;
      uint16_t indexRecording = 8000;
      if (adm->GetPlayoutDeviceIndex(&indexPlayOut) == 0 && adm->GetRecordingDeviceIndex(&indexRecording) == 0)
      {
          if (adm->PlayoutDeviceName(indexPlayOut, playoutName, playoutGuid) == 0 && adm->RecordingDeviceName(indexRecording, recordingName, recordingGuid) == 0)
          {
              // Compare the playout and recording device names to see is they are the same,
              // only need to only compare the part inside the parenthesis
              std::string outDevice = playoutName;
              std::string inDevice = recordingName;

              size_t splitIdx = outDevice.find("(");
              if (splitIdx != std::string::npos)
              {
                  outDevice = outDevice.substr((splitIdx + 1), outDevice.length());
              }
              splitIdx = inDevice.find("(");
              if (splitIdx != std::string::npos)
              {
                  inDevice = inDevice.substr((splitIdx + 1), inDevice.length());
              }

              // Also remove extra ) at the end of string
              splitIdx = outDevice.find(")");
              if (splitIdx != std::string::npos)
              {
                  outDevice = outDevice.substr(0, splitIdx);
              }
              splitIdx = inDevice.find(")");
              if (splitIdx != std::string::npos)
              {
                  inDevice = inDevice.substr(0, splitIdx);
              }

              // Compare final device names
              std::string finalReportValue = "";
              if (strcmp(outDevice.c_str(), inDevice.c_str()) == 0)
              {
                  finalReportValue = outDevice;
              }
              else
              {
                  finalReportValue = outDevice + ", " + inDevice;
              }

              tmp = itemPath; tmp += SEP; tmp += "usbDevice";
              RegistryUtils::setString(registry, tmp.c_str(), finalReportValue);
          }
      }

      DebugLog(<< "AnalyticsManagerImpl::handlePttSessionUpdate(): ptt call with session handle: " << session);
   }

   if ((args.currentState == CPCAPI2::PushToTalk::PttSessionState_Idle) && (args.previousState == CPCAPI2::PushToTalk::PttSessionState_Idle))
   {
      handlePttSessionEnd(session, args);
   }

   return kSuccess;
}

int AnalyticsManagerImpl::handlePttSessionEnd(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttSessionStateChangedEvent& args)
{
#if (CPCAPI2_BRAND_PTT_MODULE == 1)
   mAnalyticsThreadCheck.test();

   bool sessionFound = (mPttSessionStateMap.find(session) != mPttSessionStateMap.end());
   if (sessionFound)
   {
      DebugLog(<< "AnalyticsManagerImpl::handlePttSessionEnd(): session: " << session << " service: " << args.service <<  " updating state from: " << mPttSessionStateMap[session].currentState << " to: " << args.currentState);
      mPttSessionStateMap[session].currentState = args.currentState;
   }
   else
   {
      InfoLog(<< "AnalyticsManagerImpl::handlePttSessionEnd(): session: " << session << " service: " << args.service <<  " session not found");
      return kError;
   }

   // When the conversation ends, finalize the statistics.
   // Whatever we can get from there, put into the document.
   PttSessionState state;
   const char* itemName = "call";

   getPttState(session, state);

   // Delete the current state of the ended call
   mPttSessionActiveList.remove(session);

   // Loop over all the analytics handles and populate the information
   for (AnalyticsMap::const_iterator iter = mInfoMap.begin(); iter != mInfoMap.end(); ++iter)
   {
      const AnalyticsHandle& serverHandle(iter->first);
      msrp_tree* registry(iter->second->registry);
      std::string activityRoot;

      // Create the call list if it doesn't exist
      StackLog(<< "AnalyticsManagerImpl::handlePttSessionEnd(): itemName: " << itemName);
      if (!RegistryUtils::listExists(registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot))
      {
         StackLog(<< "AnalyticsManagerImpl::handlePttSessionEnd(): itemName: " << itemName << " activityRoot: " << activityRoot);
         if (!RegistryUtils::createList(registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot))
         {
            InfoLog(<< "AnalyticsManagerImpl::handlePttSessionEnd(): error creating list for itemName: " << itemName);
            return kError;
         }
      }

      // Check to see if there is an account list entry with the right account handle
      bool sessionMatchFound(false);
      std::string idString("PTT.");
      std::ostringstream ssac;
      ssac << state.service;
      idString += ssac.str();
      std::stringstream ssid;
      ssid << session;
      std::string sessionId(ssid.str());

      // Skip adding any data if end call data has no matching active call data AND its not a failed call,
      // if it is a failed call, then we expect no matching active call data and we continue to write to report
      if ((mPttSessionCurrentMap[idString] == 0) && (state.sessionError != CPCAPI2::PushToTalk::PttSessionError_None))
      {
         return kSuccess;
      }
      // Skip adding any data if the end-call event is not for the current call in the report per account
      else if ((mPttSessionCurrentMap[idString] != 0) && mPttSessionCurrentMap[idString] != session)
      {
         return kSuccess;
      }

      std::string itemPath; // Set to point to correct item
      size_t listSize = RegistryUtils::getListSize(registry, activityRoot.c_str());
      StackLog(<< "AnalyticsManagerImpl::handlePttSessionEnd(): list size: " << listSize << " for activityRoot: " << activityRoot);
      for (size_t i = 0; i < listSize; ++i)
      {
         RegistryUtils::getListItem(registry, activityRoot.c_str(), i, itemPath);
         StackLog(<< "AnalyticsManagerImpl::handlePttSessionEnd(): itemPath: " << itemPath << " activityRoot: " << activityRoot << " index: " << i);

         std::string attrValue;
         if (RegistryUtils::getAttribute(registry, itemPath.c_str(), ACTIVITY_ACCOUNT_ID_ATTR, attrValue))
         {
            StackLog(<< "AnalyticsManagerImpl::handlePttSessionEnd(): itemPath: " << itemPath << " attribute: " << ACTIVITY_ACCOUNT_ID_ATTR << " attrValue: " << attrValue << " index: " << i);
            if (attrValue == idString)
            {
               if (RegistryUtils::getAttribute(registry, itemPath.c_str(), SESSION_ID_ATTR, attrValue))
               {
                  StackLog(<< "AnalyticsManagerImpl::handlePttSessionUpdate(): itemPath: " << itemPath << " attribute: " << SESSION_ID_ATTR << " attrValue: " << attrValue << " index: " << i);
                  if (attrValue == sessionId)
                  {
                     sessionMatchFound = true;
                     break;
                  }
               }
            }
         }
      }

      // If there was no match, create an entry for this account.
      if (!sessionMatchFound)
      {
         DebugLog(<< "AnalyticsManagerImpl::handlePttSessionEnd(): match not found for id-string: " << idString);

         if (!RegistryUtils::addListItem(registry, activityRoot.c_str(), itemPath))
         {
            InfoLog(<< "AnalyticsManagerImpl::handlePttSessionEnd(): error adding item to list with activityRoot: " << activityRoot);
            return kError;
         }

         // Save the Account ID attribute
         if (!RegistryUtils::setAttribute(registry, itemPath.c_str(), ACTIVITY_ACCOUNT_ID_ATTR, idString.c_str()))
         {
            InfoLog(<< "AnalyticsManagerImpl::handlePttSessionEnd(): error setting attribute: " << ACTIVITY_ACCOUNT_ID_ATTR << " with attrValue: " << idString << " on itemPath: " << itemPath);
            return kError;
         }

         // Save the Session ID attribute
         if (!RegistryUtils::setAttribute(registry, itemPath.c_str(), SESSION_ID_ATTR, sessionId.c_str()))
         {
            InfoLog(<< "AnalyticsManagerImpl::handlePttSessionUpdate(): error setting attribute: " << SESSION_ID_ATTR << " with attrValue: " << sessionId << " on itemPath: " << itemPath);
            return kError;
         }
      }

      // Temp var used for building paths
      std::string tmp;

      std::string channel = state.channelId;
      tmp = itemPath; tmp += SEP; tmp += "channel";
      RegistryUtils::setString(registry, tmp.c_str(), channel.c_str());

      tmp = itemPath; tmp += SEP; tmp += "remoteIdentity";
      RegistryUtils::setString(registry, tmp.c_str(), state.remoteIdentity.getIdentityString().c_str());

      std::string serviceType("");
      std::string mediaEncrypted("");
      if (mPttSettingsMap[args.service].serviceType == PushToTalk::PttServiceType_LAN)
      {
         serviceType = "LAN";
         mediaEncrypted = "None";
      }
      else if (mPttSettingsMap[args.service].serviceType == PushToTalk::PttServiceType_WAN)
      {
         serviceType = "WAN";
         mediaEncrypted = "SRTP";
      }
      tmp = itemPath; tmp += SEP; tmp += "serviceType";
      RegistryUtils::setString(registry, tmp.c_str(), serviceType.c_str());

      // Calculate the call duration (the value needs to be in seconds)
      int64_t currentSecs(secondsSinceEpoch());
      int64_t callStartSecs = 0;
      tmp = itemPath; tmp += SEP; tmp += "callStart";
      RegistryUtils::getInt64(registry, tmp.c_str(), callStartSecs);

      // If the call start time is 0 / not found, then it is a failed call
      // so call start and end should be the same (current time) with duration of zero
      if (callStartSecs == 0)
      {
         callStartSecs = currentSecs;
         // Set call start to current because of failed call
         tmp = itemPath; tmp += SEP; tmp += "callStart";
         RegistryUtils::setInt(registry, tmp.c_str(), callStartSecs);

         // Set call success to false because of failed call
         tmp = itemPath; tmp += SEP; tmp += "callSuccessful";
         RegistryUtils::setBool(registry, tmp.c_str(), false);
      }
      else if (state.sessionError != CPCAPI2::PushToTalk::PttSessionError_None)
      {
         // Set call success to false because of failed call
         tmp = itemPath; tmp += SEP; tmp += "callSuccessful";
         RegistryUtils::setBool(registry, tmp.c_str(), false);
      }
      else
      {
         tmp = itemPath; tmp += SEP; tmp += "callSuccessful";
         RegistryUtils::setBool(registry, tmp.c_str(), mPttSessionStateMap[session].successful);
      }

      bool isIncoming = (mPttSessionIncomingMap.find(session) != mPttSessionIncomingMap.end());
      tmp = itemPath; tmp += SEP; tmp += "incoming";
      RegistryUtils::setBool(registry, tmp.c_str(), isIncoming);

      int durationSeconds = (int)((currentSecs - callStartSecs));
      tmp = itemPath; tmp += SEP; tmp += "callDuration";
      RegistryUtils::setInt(registry, tmp.c_str(), durationSeconds);

      // TODO: Currently the ptt media info is hard-coded and not negotiated, using opus for plname and 48000 for plfreq
      std::string codecString("opus/48000");
      tmp = itemPath; tmp += SEP; tmp += "audioInCodec";
      RegistryUtils::setString(registry, tmp.c_str(), codecString.c_str());
      tmp = itemPath; tmp += SEP; tmp += "audioOutCodec";
      RegistryUtils::setString(registry, tmp.c_str(), codecString.c_str());

      // TODO: Need to determine the media encryption dynamically, setting it based on service-type for now
      tmp = itemPath; tmp += SEP; tmp += "mediaEncryption";
      RegistryUtils::setString(registry, tmp.c_str(), mediaEncrypted);

      tmp = itemPath; tmp += SEP; tmp += "oneWayAudio";
      RegistryUtils::setBool(registry, tmp.c_str(), true);

      // TODO: get the call quality
      // tmp = itemPath; tmp += SEP; tmp += "poorNetworkQualityIndicated";
      // RegistryUtils::setBool(registry, tmp.c_str(), false);

      tmp = itemPath; tmp += SEP; tmp += "dataNetworkType";
      const NetworkTransport currentNetTransport = mNetworkChangeManagerIf->networkTransport();
      if (currentNetTransport == TransportWiFi)
      {
         RegistryUtils::setString(registry, tmp.c_str(), "WIFI");
      }
      else if (currentNetTransport == TransportWWAN)
      {
#if TARGET_OS_IPHONE
         RegistryUtils::setString(registry, tmp.c_str(), mNetworkChangeManagerIf->wwanType().c_str());
#else
         RegistryUtils::setString(registry, tmp.c_str(), "4G");
#endif
      }
      else if (currentNetTransport == TransportNone)
      {
         // TODO: persist last used network so we can use it here
         RegistryUtils::setString(registry, tmp.c_str(), "WIFI");
      }

      tmp = itemPath; tmp += SEP; tmp += "networkIpChange";
      RegistryUtils::setBool(registry, tmp.c_str(), mLastIPChangeTime >= callStartSecs);

      std::string failedCallReason = "";
      if (state.sessionError != PushToTalk::PttSessionError_None)
      {
         failedCallReason = PushToTalk::getSessionErrorCode(state.sessionError);
      }
      tmp = itemPath; tmp += SEP; tmp += "failureReason";
      RegistryUtils::setString(registry, tmp.c_str(), failedCallReason);

      tmp = itemPath; tmp += SEP; tmp += "responsesReceived";
      RegistryUtils::setInt(registry, tmp.c_str(), state.responsesReceived);
   }
#endif
   DebugLog(<< "AnalyticsManagerImpl::handlePttSessionEnd(): ptt call with session handle: " << session);
   return kSuccess;
}

int AnalyticsManagerImpl::getPttState(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, PttSessionState& outState) const
{
   std::map<CPCAPI2::PushToTalk::PushToTalkSessionHandle, PttSessionState>::const_iterator it = mPttSessionStateMap.find(session);
   if (it != mPttSessionStateMap.end())
   {
      outState = it->second;
      return kSuccess;
   }

   return kError;
}

int AnalyticsManagerImpl::getState(CPCAPI2::SipConversation::SipConversationHandle handle, SipConversationState& outState) const
{
   std::map<CPCAPI2::SipConversation::SipConversationHandle, SipConversationState>::const_iterator it = mConvoStateMap.find(handle);
   if (it != mConvoStateMap.end())
   {
      outState = it->second;
      return kSuccess;
   }

   return kError;
}

int AnalyticsManagerImpl::onConversationMediaChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangedEvent& args)
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onConversationMediaChangedImpl, this, conversation, args ));
   return kSuccess;
}

void AnalyticsManagerImpl::updateCallOrigin(CPCAPI2::SipConversation::SipConversationHandle account, CPCAPI2::SipConversation::SipConversationHandle conversation, msrp_tree* registry, std::string itemPath)
{
   bool settingsFound = false;
   SipConversationState state;
   if (kSuccess == getState(conversation, state))
   {
      SipSettingsMap::const_iterator itSettings = mSipSettingsMap.find(account);
      if (itSettings != mSipSettingsMap.end())
      {
         settingsFound = true;

         const NetworkTransport currentNetTransport = mNetworkChangeManagerIf->networkTransport();

         // Use the current transport, or "None" if there is no setting
         // for the current transport.
         const CPCAPI2::SipAccount::SipAccountSettings& settings(
            itSettings->second.size() > ( size_t )currentNetTransport ?
            itSettings->second.at( currentNetTransport ) :
            itSettings->second.at( CPCAPI2::NetworkTransport::TransportNone )
         );

         cpc::string addr = (state.conversationType == CPCAPI2::SipConversation::ConversationType_Outgoing)
            // DRL It looks like the localAddress is empty for an outgoing call so get it from the account settings.
            ? "sip:" + settings.username + "@" + settings.domain : state.remoteAddress;
         std::string tmp = itemPath; tmp += SEP; tmp += "callOrigin";
         RegistryUtils::setString(registry, tmp.c_str(), addr.c_str());
      }
   }

   if (!settingsFound)
   {
      ErrLog(<< "UEM: onConversationStateChanged called but couldn't get SIP settings for conversation handle " << conversation);
   }
}

int AnalyticsManagerImpl::onConversationMediaChangedImpl(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangedEvent& args)
{
   mAnalyticsThreadCheck.test();

   mConvoStateMap[conversation].localHold = args.localHold;
   mConvoStateMap[conversation].remoteHold = args.remoteHold;
   mConvoStateMap[conversation].localMediaInfo = args.localMediaInfo;
   mConvoStateMap[conversation].remoteMediaInfo = args.remoteMediaInfo;

   if (args.localHold)
      mConvoHeldMap[conversation] = true;

    // Only interested in unheld state
    if (args.localHold || args.remoteHold)
        return kSuccess;

    // Grab the payload from the local video codec
    bool isVideoCall = false;
    int height = 0;
    int width = 0;
    std::string codecString = "";
    cpc::vector<CPCAPI2::SipConversation::MediaInfo>::const_iterator listIter;
    for (listIter = args.localMediaInfo.begin(); listIter != args.localMediaInfo.end(); ++listIter)
    {
        // Just take the first video codec
        if (listIter->mediaType == CPCAPI2::SipConversation::MediaType_Video)
        {
            isVideoCall = true;
            // Append the bitrate on the end after a slash.
            codecString = listIter->videoCodec.plName;
            codecString += "/";

            std::ostringstream strs;
            strs << listIter->videoCodec.startBitrate;
            codecString += strs.str();
            height = listIter->videoCodec.height;
            width = listIter->videoCodec.width;
        }
    }

    int remHeight = 0;
    int remWidth = 0;
    std::string remCodecString = "";
    for (listIter = args.remoteMediaInfo.begin(); listIter != args.remoteMediaInfo.end(); ++listIter)
    {
        // Just take the first video codec
        if (listIter->mediaType == CPCAPI2::SipConversation::MediaType_Video)
        {
            // Append the bitrate on the end after a slash.
            remCodecString = listIter->videoCodec.plName;
            remCodecString += "/";

            std::ostringstream strs2;
            strs2 << listIter->videoCodec.startBitrate;
            remCodecString += strs2.str();
            remHeight = listIter->videoCodec.height;
            remWidth = listIter->videoCodec.width;
        }
    }

    //try to add the info for local conferences based on the list of active calls and whether they are on hold
    int unHeldConvoCount = 0;
    for (std::list<CPCAPI2::SipConversation::SipConversationHandle>::iterator it = mActiveConvoList.begin(); it != mActiveConvoList.end(); ++it) {

        SipConversationState stateTemp;
        getState(conversation, stateTemp);

        SipConversationState stateOther;
        getState(*it, stateOther);

        //if a conversation is not the current one, and it is not on hold
        if (*it != conversation && !stateTemp.localHold && !stateTemp.remoteHold && !stateOther.remoteHold && !stateOther.localHold)
        {
            //if the convo is also in a connected state
            if (stateTemp.conversationState == SipConversation::ConversationState_Connected)
            {
                //add to the counter because this call must be unheld at same time as the current unheld call, so we are on a local conference
                unHeldConvoCount++;
            }
        }
    }

    const char *itemName = "call";
    SipConversationState state;
    getState(conversation, state);

    // Loop over all the analytics handles and populate the information
    AnalyticsMap::const_iterator iter = mInfoMap.begin();
    for (; iter != mInfoMap.end(); ++iter)
    {
        msrp_tree* registry(iter->second->registry);
        std::string activityRoot;

        // the call list if it doesn't exist so bail
        if (!RegistryUtils::listExists(registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot))
        {
            return kError;
        }

        // Check to see if there is an account list entry with the right account handle
        bool matchFound(false);
        std::string idString("SIP.");
        std::ostringstream strs;
        strs << state.account;
        idString += strs.str();

        std::string itemPath; // Set to point to correct item

        size_t listSize = RegistryUtils::getListSize(registry, activityRoot.c_str());
        for (size_t i = 0; i < listSize; ++i)
        {
            RegistryUtils::getListItem(registry, activityRoot.c_str(), i, itemPath);

            std::string attrValue;
            if (RegistryUtils::getAttribute(registry, itemPath.c_str(), ACTIVITY_ACCOUNT_ID_ATTR, attrValue))
            {
                if (attrValue == idString)
                {
                    matchFound = true;
                    break;
                }
            }
        }

        // If there was no match, bail
        if (!matchFound)
        {
            return kError;
        }

        //add the conference fields to the report
        std::string tmp = itemPath;
        if (unHeldConvoCount > 0)
        {
            tmp = itemPath; tmp += SEP; tmp += "localConference";
            RegistryUtils::setBool(registry, tmp.c_str(), true);
            tmp = itemPath; tmp += SEP; tmp += "maxConferenceParticipants";
            RegistryUtils::setInt(registry, tmp.c_str(), (++unHeldConvoCount+1)); //increment before setting because need to include the current call too & add 1 at end to include myself as well
            tmp = itemPath; tmp += SEP; tmp += "videoConference";
            RegistryUtils::setBool(registry, tmp.c_str(), isVideoCall);
        }

        if (isVideoCall) {
            tmp = itemPath; tmp += SEP; tmp += "videoIn"; tmp += SEP; tmp += "codec";
            RegistryUtils::setString(registry, tmp.c_str(), remCodecString.c_str());

            tmp = itemPath; tmp += SEP; tmp += "videoIn"; tmp += SEP; tmp += "height";
            RegistryUtils::setInt(registry, tmp.c_str(), remHeight);

            tmp = itemPath; tmp += SEP; tmp += "videoIn"; tmp += SEP; tmp += "width";
            RegistryUtils::setInt(registry, tmp.c_str(), remWidth);

            tmp = itemPath; tmp += SEP; tmp += "videoOut"; tmp += SEP; tmp += "codec";
            RegistryUtils::setString(registry, tmp.c_str(), codecString.c_str());

            tmp = itemPath; tmp += SEP; tmp += "videoOut"; tmp += SEP; tmp += "height";
            RegistryUtils::setInt(registry, tmp.c_str(), height);

            tmp = itemPath; tmp += SEP; tmp += "videoOut"; tmp += SEP; tmp += "width";
            RegistryUtils::setInt(registry, tmp.c_str(), width);

            //set the video device that is currently in use
            tmp = itemPath; tmp += SEP; tmp += "videoOut"; tmp += SEP; tmp += "device";
            RegistryUtils::setString(registry, tmp.c_str(), mCurrentVideoDeviceName);
        }

        DebugLog(<< "UEM: onConversationMediaChanged called with ConvoHandle:  " << conversation);
        DebugLog(<< "UEM: onConversationMediaChanged called with unHeldConvoCount:  " << unHeldConvoCount);
        DebugLog(<< "UEM: onConversationMediaChanged called with isVideoCall:  " << isVideoCall);
    }

    return kSuccess;
}

int AnalyticsManagerImpl::onConversationStatisticsUpdated(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent& args)
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onConversationStatisticsUpdatedImpl, this, conversation, args ));
   return kSuccess;
}

int AnalyticsManagerImpl::onConversationStatisticsUpdatedImpl(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent& args)
{
   mConvoStateMap[conversation].statistics = args.conversationStatistics;
   return kSuccess;
}

int AnalyticsManagerImpl::onConversationStateChanged(
   CPCAPI2::SipConversation::SipConversationHandle conversation,
   const CPCAPI2::SipConversation::ConversationStateChangedEvent& args)
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onConversationStateChangedImpl, this, conversation, args ));
   return kSuccess;
}

// Override this method just so we can calculate the call start and call duration.
int AnalyticsManagerImpl::onConversationStateChangedImpl(
   CPCAPI2::SipConversation::SipConversationHandle conversation,
   const CPCAPI2::SipConversation::ConversationStateChangedEvent& args)
{
   mAnalyticsThreadCheck.test();

   mConvoStateMap[conversation].conversationState = args.conversationState;
   if (args.remoteAddress.size() > 0)
   {
      mConvoStateMap[conversation].remoteAddress = args.remoteAddress;
   }


   const char *itemName = "call";

   // Only interested in connected state
   if( args.conversationState != CPCAPI2::SipConversation::ConversationState::ConversationState_Connected )
      return kSuccess;

   SipConversationState state;
   getState(conversation, state);

   //save the current state of the connected call if its not added already (in some cases the SDK sends the connected state event more than once)
   bool alreadyExists = (std::find(mActiveConvoList.begin(), mActiveConvoList.end(), conversation) != mActiveConvoList.end());
   if (!alreadyExists) {
       mActiveConvoList.push_back(conversation);
   }

   // Loop over all the analytics handles and populate the information
   AnalyticsMap::const_iterator iter = mInfoMap.begin();
   for( ; iter != mInfoMap.end() ; ++iter )
   {
      msrp_tree* registry( iter->second->registry );
      std::string activityRoot;

      // Create the call list if it doesn't exist
      if( !RegistryUtils::listExists( registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot ))
      {
         if( !RegistryUtils::createList( registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot ))
            return kError;
      }

      // Check to see if there is an account list entry with the right account handle
      bool matchFound( false );
      std::string idString( "SIP." );
      std::ostringstream strs;
      strs << state.account;
      idString += strs.str();

      //if we get here then the convo is connected and data will be written to report for it
      //it is now considered the current / latest call for this report because each report has only one set of call info per SIP account
      mCurrentConvoMap[idString] = conversation;

      std::string itemPath; // Set to point to correct item

      size_t listSize = RegistryUtils::getListSize( registry, activityRoot.c_str() );
      for( size_t i = 0 ; i < listSize ; ++i )
      {
         RegistryUtils::getListItem( registry, activityRoot.c_str(), i, itemPath );

         std::string attrValue;
         if( RegistryUtils::getAttribute( registry, itemPath.c_str(), ACTIVITY_ACCOUNT_ID_ATTR, attrValue ))
         {
            if( attrValue == idString )
            {
               matchFound = true;
               break;
            }
         }
      }

      // If there was no match, create an entry for this account.
      if( !matchFound )
      {
         if( !RegistryUtils::addListItem( registry, activityRoot.c_str(), itemPath ))
            return kError;

         // Save the Account ID attribute
         if( !RegistryUtils::setAttribute( registry, itemPath.c_str(), ACTIVITY_ACCOUNT_ID_ATTR, idString ))
            return kError;
      }

      // Record the starting time for the call in seconds since the epoch.
      std::string tmp = itemPath; tmp += SEP; tmp += "callStart";
      RegistryUtils::setInt64( registry, tmp.c_str(), secondsSinceEpoch() );

      tmp = itemPath; tmp += SEP; tmp += "callSuccessful";
      RegistryUtils::setBool(registry, tmp.c_str(), true);

      this->updateCallOrigin(state.account, conversation, registry, itemPath);

      // Grab the payload name from the remote audio codec
      cpc::vector<CPCAPI2::SipConversation::MediaInfo>::const_iterator iter;
      for (iter = state.remoteMediaInfo.begin(); iter != state.remoteMediaInfo.end(); ++iter)
      {
          // Just take the first audio codec
          if (iter->mediaType == CPCAPI2::SipConversation::MediaType_Audio)
          {
              // Append the bitrate on the end after a slash.
              std::string codecString(iter->audioCodec.plname);
              codecString += "/";

              std::ostringstream strs;
              strs << iter->audioCodec.plfreq;
              codecString += strs.str();

              tmp = itemPath; tmp += SEP; tmp += "audioInCodec";
              RegistryUtils::setString(registry, tmp.c_str(), codecString.c_str());
          }
          else if (iter->mediaType == CPCAPI2::SipConversation::MediaType_Video)
          {
              std::string codecString(iter->videoCodec.plName);
              codecString += "/";

              std::ostringstream strs;
              strs << iter->videoCodec.startBitrate;
              codecString += strs.str();

              tmp = itemPath; tmp += SEP; tmp += "videoIn"; tmp += SEP; tmp += "codec";
              RegistryUtils::setString(registry, tmp.c_str(), codecString.c_str());

              tmp = itemPath; tmp += SEP; tmp += "videoIn"; tmp += SEP; tmp += "height";
              RegistryUtils::setInt(registry, tmp.c_str(), iter->videoCodec.height);

              tmp = itemPath; tmp += SEP; tmp += "videoIn"; tmp += SEP; tmp += "width";
              RegistryUtils::setInt(registry, tmp.c_str(), iter->videoCodec.width);
          }
      }

      // Grab the payload name from the local audio codec
      for (iter = state.localMediaInfo.begin(); iter != state.localMediaInfo.end(); ++iter)
      {
          //get media encryption value from local media
          std::string encrypted = "None";
          if (iter->mediaEncryptionOptions.mediaEncryptionMode != MediaEncryptionMode::MediaEncryptionMode_Unencrypted) {
              encrypted = "SRTP";
              tmp = itemPath; tmp += SEP; tmp += "mediaEncryption";
              RegistryUtils::setString(registry, tmp.c_str(), encrypted);
          }

          // Just take the first audio codec
          if (iter->mediaType == CPCAPI2::SipConversation::MediaType_Audio)
          {
              // Append the bitrate on the end after a slash.
              std::string codecString(iter->audioCodec.plname);
              codecString += "/";

              std::ostringstream strs;
              strs << iter->audioCodec.plfreq;
              codecString += strs.str();

              tmp = itemPath; tmp += SEP; tmp += "audioOutCodec";
              RegistryUtils::setString(registry, tmp.c_str(), codecString.c_str());
          }
          else if (iter->mediaType == CPCAPI2::SipConversation::MediaType_Video)
          {
              //set the video device that is currently in use
              tmp = itemPath; tmp += SEP; tmp += "videoOut"; tmp += SEP; tmp += "device";
              RegistryUtils::setString(registry, tmp.c_str(), mCurrentVideoDeviceName);

              std::string codecString(iter->videoCodec.plName);
              codecString += "/";

              std::ostringstream strs;
              strs << iter->videoCodec.startBitrate;
              codecString += strs.str();

              tmp = itemPath; tmp += SEP; tmp += "videoOut"; tmp += SEP; tmp += "codec";
              RegistryUtils::setString(registry, tmp.c_str(), codecString.c_str());

              tmp = itemPath; tmp += SEP; tmp += "videoOut"; tmp += SEP; tmp += "height";
              RegistryUtils::setInt(registry, tmp.c_str(), iter->videoCodec.height);

              tmp = itemPath; tmp += SEP; tmp += "videoOut"; tmp += SEP; tmp += "width";
              RegistryUtils::setInt(registry, tmp.c_str(), iter->videoCodec.width);
          }
      }

      //add USB Device info to report
      CPCAPI2::Media::MediaManager* media = CPCAPI2::Media::MediaManager::getInterface(mPhone);
      CPCAPI2::Media::Audio* audio = CPCAPI2::Media::Audio::getInterface(media);
      webrtc_recon::MediaStackImpl* mStack = (static_cast<AudioInterface*>(audio))->media_stack();
      webrtc::AudioDeviceModule* adm = mStack->voe_base()->audio_device();

      char playoutName[128];
      char playoutGuid[128];
      char recordingName[128];
      char recordingGuid[128];
      uint16_t indexPlayOut = 8000;
      uint16_t indexRecording = 8000;
      if (adm->GetPlayoutDeviceIndex(&indexPlayOut) == 0 && adm->GetRecordingDeviceIndex(&indexRecording) == 0)
      {
          if (adm->PlayoutDeviceName(indexPlayOut, playoutName, playoutGuid) == 0 && adm->RecordingDeviceName(indexRecording, recordingName, recordingGuid) == 0)
          {
              //compare the playout and recording device names to see is they are the same
              //need to only compare the part inside the parenthesis
              std::string outDevice = playoutName;
              std::string inDevice = recordingName;

              size_t splitIdx = outDevice.find("(");
              if (splitIdx != std::string::npos)
              {
                  outDevice = outDevice.substr((splitIdx + 1), outDevice.length());
              }
              splitIdx = inDevice.find("(");
              if (splitIdx != std::string::npos)
              {
                  inDevice = inDevice.substr((splitIdx + 1), inDevice.length());
              }

              //also remove extra ) at the end of string
              splitIdx = outDevice.find(")");
              if (splitIdx != std::string::npos)
              {
                  outDevice = outDevice.substr(0, splitIdx);
              }
              splitIdx = inDevice.find(")");
              if (splitIdx != std::string::npos)
              {
                  inDevice = inDevice.substr(0, splitIdx);
              }


              //compare final device names
              std::string finalReportValue = "";
              if (strcmp(outDevice.c_str(), inDevice.c_str()) == 0) {
                  finalReportValue = outDevice;
              }
              else {
                  finalReportValue = outDevice + ", " + inDevice;
              }

              tmp = itemPath; tmp += SEP; tmp += "usbDevice";
              RegistryUtils::setString(registry, tmp.c_str(), finalReportValue);
          }
      }

      DebugLog(<< "UEM: onConversationStateChanged called with Connected status and ConvoHandle:  " << conversation);
   }

   return kSuccess;
}

int AnalyticsManagerImpl::onNewConversation(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::NewConversationEvent& args)
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onNewConversationImpl, this, conversation, args ));
   return kSuccess;
}

int AnalyticsManagerImpl::onNewConversationImpl(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::NewConversationEvent& args)
{
   mConvoStateMap[conversation].account = args.account;
   mConvoStateMap[conversation].conversationType = args.conversationType;
   mConvoStateMap[conversation].remoteAddress = args.remoteAddress;

   return kSuccess;
}

int AnalyticsManagerImpl::onConversationEnded(
   CPCAPI2::SipConversation::SipConversationHandle conversation,
   const CPCAPI2::SipConversation::ConversationEndedEvent& args)
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onConversationEndedImpl, this, conversation, args ));
   return kSuccess;
}

int AnalyticsManagerImpl::onConversationEndedImpl(
   CPCAPI2::SipConversation::SipConversationHandle conversation,
   const CPCAPI2::SipConversation::ConversationEndedEvent& args)
{
   mAnalyticsThreadCheck.test();

   // When the conversation ends, collect statistics from the SipConversationStateManager.
   // Whatever we can get from there, put into the document.
   SipConversationState state;
   const char *itemName = "call";

   getState(conversation, state);

   // make sure we keep track of the account (even if it gets removed) associated with the ended call until after we send the activity report
   mSipAccountWithStatsList.insert(state.account);

   //try to add the info for local conferences based on the list of active calls and whether they are on hold, this is the second attempt
   //to do this in case the first attempt in onConversationMediaChanged did not work. Sometimes the timing of the events break things.
   int unHeldConvoCount = 0;
   for (std::list<CPCAPI2::SipConversation::SipConversationHandle>::iterator it = mActiveConvoList.begin(); it != mActiveConvoList.end(); ++it) {

       SipConversationState stateTemp;
       getState(conversation, stateTemp);

       SipConversationState stateOther;
       getState(*it, stateOther);

       //if a conversation is not the current one, and it is not on hold
       if (*it != conversation && !stateTemp.localHold && !stateTemp.remoteHold && !stateOther.remoteHold && !stateOther.localHold)
       {
            //add to the counter because this call must be unheld at same time as the current unheld call, so we are on a local conference
            unHeldConvoCount++;
       }
   }

   //delete the current state of the ended call
   mActiveConvoList.remove(conversation);

   // Loop over all the analytics handles and populate the information
   AnalyticsMap::const_iterator iter = mInfoMap.begin();
   for (; iter != mInfoMap.end(); ++iter)
   {
       const AnalyticsHandle& serverHandle(iter->first);
       msrp_tree* registry(iter->second->registry);
       std::string activityRoot;

       // Create the call list if it doesn't exist
       if (!RegistryUtils::listExists(registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot))
       {
           if (!RegistryUtils::createList(registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot))
               return kError;
       }

       // Check to see if there is an account list entry with the right account handle
       bool matchFound(false);
       std::string idString("SIP.");
       std::ostringstream strs;
       strs << state.account;
       idString += strs.str();

       //skip adding any data if end call data has no matching active call data AND its not a failed call
       //if it is a failed call, then we expect no matching active call data and we continue to write to report
       if (mCurrentConvoMap[idString] == 0 && args.sipResponseCode < 300 && args.endReason != CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedLocally && args.endReason != CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedRemotely) return kSuccess;

       //skip adding any data if the endCall event is not for the current call in the report per account
       else if (mCurrentConvoMap[idString] != 0 && mCurrentConvoMap[idString] != conversation) return kSuccess;

       std::string itemPath; // Set to point to correct item

       size_t listSize = RegistryUtils::getListSize(registry, activityRoot.c_str());
       for (size_t i = 0; i < listSize; ++i)
       {
           RegistryUtils::getListItem(registry, activityRoot.c_str(), i, itemPath);

           std::string attrValue;
           if (RegistryUtils::getAttribute(registry, itemPath.c_str(), ACTIVITY_ACCOUNT_ID_ATTR, attrValue))
           {
               if (attrValue == idString)
               {
                   matchFound = true;
                   break;
               }
           }
       }

       // If there was no match, create an entry for this account.
       if (!matchFound)
       {
           if (!RegistryUtils::addListItem(registry, activityRoot.c_str(), itemPath))
               return kError;

           // Save the Account ID attribute
           if (!RegistryUtils::setAttribute(registry, itemPath.c_str(), ACTIVITY_ACCOUNT_ID_ATTR, idString))
               return kError;
       }

       // temp var used for building paths
       std::string tmp;

       // Calculate the call duration (the value needs to be in seconds)
       int64_t currentSecs(secondsSinceEpoch());
       int64_t callStartSecs = 0;
       tmp = itemPath; tmp += SEP; tmp += "callStart";
       RegistryUtils::getInt64(registry, tmp.c_str(), callStartSecs);

       //if the call start time is 0 / not found, then it is a failed call
       //so call start and end should be the same(current time) with duration of zero
       if (callStartSecs == 0) {
           callStartSecs = currentSecs;
           //set call start to current because of failed call
           RegistryUtils::setInt(registry, tmp.c_str(), callStartSecs);

           //set call success to false because of failed call
           tmp = itemPath; tmp += SEP; tmp += "callSuccessful";
           RegistryUtils::setBool(registry, tmp.c_str(), false);
       }

       int durationSeconds = (int)((currentSecs - callStartSecs));
       tmp = itemPath; tmp += SEP; tmp += "callDuration";
       RegistryUtils::setInt(registry, tmp.c_str(), durationSeconds);

       // Set the call quality report (arguably the most important bit)
       if (args.callQualityReport.size() > 0) {
           tmp = itemPath; tmp += SEP; tmp += "vqm_report";
           RegistryUtils::setString(registry, tmp.c_str(), args.callQualityReport.c_str());
       }

      tmp = itemPath; tmp += SEP; tmp += "incoming";
      RegistryUtils::setBool( registry, tmp.c_str(),
         state.conversationType == CPCAPI2::SipConversation::ConversationType_Incoming ||
         state.conversationType == CPCAPI2::SipConversation::ConversationType_IncomingJoinRequest ||
         state.conversationType == CPCAPI2::SipConversation::ConversationType_IncomingTransferRequest );

      bool held = mConvoHeldMap.find(conversation) != mConvoHeldMap.end();

      tmp = itemPath; tmp += SEP; tmp += "callHoldUsed";
      RegistryUtils::setBool(registry, tmp.c_str(), held);

      tmp = itemPath; tmp += SEP; tmp += "networkIpChange";
      RegistryUtils::setBool(registry, tmp.c_str(), mLastIPChangeTime >= callStartSecs);

      //set number of real digits dialed for outgoing call
      if (state.conversationType == CPCAPI2::SipConversation::ConversationType_Outgoing) {
          //split to take out the domain which we dont need
          std::string number = state.remoteAddress.c_str();
          const size_t splitIdx = state.remoteAddress.find("@");
          if (splitIdx != std::string::npos)
          {
              number = state.remoteAddress.substr(0, splitIdx);
          }

          //count the number of digits in the hostname / phone number / remote address dialed
          int count = 0;
          for (unsigned int i = 0; i < number.size(); i++) {
              if (isdigit(number[i])) count++;
          }

          tmp = itemPath; tmp += SEP; tmp += "numDigitsDialed";
          RegistryUtils::setInt(registry, tmp.c_str(), count);
      }

      tmp = itemPath; tmp += SEP; tmp += "callTransfer";
      bool transferred = false;
      if (mConvoTransferredMap[conversation] != 0 && mConvoTransferredMap[conversation])
      {
          transferred = true;
      }
      RegistryUtils::setBool( registry, tmp.c_str(), transferred);

      std::string failedCallReason = "";
      if (args.sipResponseCode >= 300) {
          failedCallReason = cpc::to_string(args.sipResponseCode);
      }
      else if (args.sipResponseCode == 0 && args.endReason == CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedLocally && mCurrentConvoMap[idString] == 0) {
          failedCallReason = "Locally Terminated";
      }
      else if (args.sipResponseCode == 0 && args.endReason == CPCAPI2::SipConversation::ConversationEndReason_UserTerminatedRemotely && mCurrentConvoMap[idString] == 0) {
          failedCallReason = "Remotely Terminated";
      }
      tmp = itemPath; tmp += SEP; tmp += "failedDialedCallReason";
      RegistryUtils::setString(registry, tmp.c_str(), failedCallReason);

      // get the one way audio info
      // based on if the stats are available and if there was any packets recieved.
      if (args.sipResponseCode < 300 && durationSeconds > 0) {
         tmp = itemPath; tmp += SEP; tmp += "oneWayAudio";
         if(state.statistics.audioChannels.size() > 0) {
            bool packetsReceived = state.statistics.audioChannels[0].streamDataCounters.packetsReceived > 0 ? true : false;
            RegistryUtils::setBool(registry, tmp.c_str(), !packetsReceived);
         } else {
            RegistryUtils::setBool(registry, tmp.c_str(), true);
         }
      }

      //get the poor audio quality flag
      tmp = itemPath; tmp += SEP; tmp += "poorNetworkQualityIndicated";
      if (state.statistics.callQuality == CPCAPI2::SipConversation::ConversationCallQuality_Poor) {
          RegistryUtils::setBool(registry, tmp.c_str(), true);
      }
      else {
          RegistryUtils::setBool(registry, tmp.c_str(), false);
      }

      tmp = itemPath; tmp += SEP; tmp += "dataNetworkType";

      const NetworkTransport currentNetTransport = mNetworkChangeManagerIf->networkTransport();
      if (currentNetTransport == TransportWiFi)
      {
         RegistryUtils::setString(registry, tmp.c_str(), "WIFI");
      }
      else if (currentNetTransport == TransportWWAN)
      {
#if TARGET_OS_IPHONE
         RegistryUtils::setString(registry, tmp.c_str(), mNetworkChangeManagerIf->wwanType().c_str());
#else
         RegistryUtils::setString(registry, tmp.c_str(), "4G");
#endif
      }
      else if (currentNetTransport == TransportNone)
      {
         // TODO: persist last used network so we can use it here
         RegistryUtils::setString(registry, tmp.c_str(), "WIFI");
      }

      //set whether a call is at least partially recorded or not, this is based on recording being started on the call handle in question
      tmp = itemPath; tmp += SEP; tmp += "recordedCall";
      if ((0 != mConvoRecordedMap.count(conversation)) && (true == mConvoRecordedMap[conversation])) {
          RegistryUtils::setBool(registry, tmp.c_str(), true);
      }
      else {
          RegistryUtils::setBool(registry, tmp.c_str(), false);
      }

      int out = 0;
      tmp = itemPath; tmp += SEP; tmp += "maxConferenceParticipants";
      RegistryUtils::getInt(registry, tmp.c_str(), out);
      //add the conference fields to the report if they havent been added already
      if (unHeldConvoCount > 0 && out == 0)
      {
          DebugLog(<< "UEM: onConversationEnded called and local conf data altered with participant count:  " << (unHeldConvoCount+2));
          tmp = itemPath; tmp += SEP; tmp += "localConference";
          RegistryUtils::setBool(registry, tmp.c_str(), true);

          tmp = itemPath; tmp += SEP; tmp += "maxConferenceParticipants";
          RegistryUtils::setInt(registry, tmp.c_str(), (++unHeldConvoCount + 1)); //increment before setting because need to include the current call too & add 1 at end to include myself as well

          std::string temp = "";
          tmp = itemPath; tmp += SEP; tmp += "videoOut"; tmp += SEP; tmp += "codec";
          if (RegistryUtils::getString(registry, tmp.c_str(), temp)) {
              tmp = itemPath; tmp += SEP; tmp += "videoConference";
              RegistryUtils::setBool(registry, tmp.c_str(), true);
          }
          else {
              tmp = itemPath; tmp += SEP; tmp += "videoConference";
              RegistryUtils::setBool(registry, tmp.c_str(), false);
          }
      }

      //reset the remote video info in case we didnt already catch it...sometimes remote video is delayed so we dont get accurate info
      std::string temp = "";
      //if we have videoOut / codec tag with a valid value, that means current call is a video call
      tmp = itemPath; tmp += SEP; tmp += "videoOut"; tmp += SEP; tmp += "codec";
      if (RegistryUtils::getString(registry, tmp.c_str(), temp) && state.statistics.videoChannels.size() > 0) {
         //set its width/height info for remote one last time with latest values
         tmp = itemPath; tmp += SEP; tmp += "videoIn"; tmp += SEP; tmp += "height";
         RegistryUtils::setInt(registry, tmp.c_str(), state.statistics.videoChannels[0].decoder.height);

         tmp = itemPath; tmp += SEP; tmp += "videoIn"; tmp += SEP; tmp += "width";
         RegistryUtils::setInt(registry, tmp.c_str(), state.statistics.videoChannels[0].decoder.width);
      }
   }

   DebugLog(<< "UEM: onConversationEnded called matching current convo and with ConvoHandle:  " << conversation);
   return kSuccess;
}

int AnalyticsManagerImpl::onTransferProgress(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferProgressEvent& args)
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onTransferProgressImpl, this, conversation, args ));
   return kSuccess;
}

//collect stats for transferring of calls
int AnalyticsManagerImpl::onTransferProgressImpl(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferProgressEvent& args) {
    mAnalyticsThreadCheck.test();

    DebugLog(<< "UEM: onTransferProgress called with ConvoHandle:  " << conversation);

    // if connected then transfer was successful and we should note that in report
    if (args.progressEventType == CPCAPI2::SipConversation::TransferProgressEventType_Connected) {
        mConvoTransferredMap[conversation] = true;
        DebugLog(<< "UEM: onTransferProgress called with Connected status and ConvoHandle:  " << conversation);
    }

    return kSuccess;
}

int AnalyticsManagerImpl::onIncomingTransferRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferRequestEvent& args)
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onIncomingTransferRequestImpl, this, conversation, args ));
   return kSuccess;
}

int AnalyticsManagerImpl::onIncomingTransferRequestImpl(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferRequestEvent& args) {
    mAnalyticsThreadCheck.test();

    mConvoTransferredMap[args.transferTargetConversation] = true;
    DebugLog(<< "UEM: onIncomingTransferRequest called with transferTargetConversation:  " << args.transferTargetConversation);
    return kSuccess;
}

int AnalyticsManagerImpl::onNetworkChange(const CPCAPI2::NetworkChangeEvent& params)
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::onNetworkChangeImpl, this, params ));
   return kSuccess;
}

int AnalyticsManagerImpl::onNetworkChangeImpl(const CPCAPI2::NetworkChangeEvent& params)
{
   mAnalyticsThreadCheck.test();

   NetworkTransport transport = params.networkTransport;
   DebugLog(<< "handleNetworkChangeEvent transport = " << transport);

   // Check if the IP has changed...
   resip::Tuple googDns("*******", 53, resip::UDP);
   resip::Data srcIP;
   IpHelpers::getPreferredLocalIpAddress(googDns, srcIP);
   if (mCurrentIP != srcIP)
   {
      mCurrentIP = srcIP;
      mLastIPChangeTime = secondsSinceEpoch();
   }

   return kSuccess;
}

// Sip / Xmpp InstantMessage handling
int AnalyticsManagerImpl::instantMessageInfoFired(unsigned int accountHandle, const bool incoming, const bool isSip) {

    mAnalyticsThreadCheck.test();

    const char *itemName = "account";

    // Loop over all the analytics handles and populate the information
    AnalyticsMap::const_iterator iter = mInfoMap.begin();
    for (; iter != mInfoMap.end(); ++iter)
    {
        const AnalyticsHandle& serverHandle(iter->first);
        msrp_tree* registry(iter->second->registry);
        std::string activityRoot;

        if (!RegistryUtils::listExists(registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot))
        {
            if (!RegistryUtils::createList(registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot))
                return kError;
        }

        // Check to see if there is an account list entry with the right account handle
        bool matchFound(false);
        std::string idString("SIP.");
        if (!isSip) idString = "XMPP.";

        std::ostringstream strs;
        strs << accountHandle;
        idString += strs.str();

        std::string itemPath; // Set to point to correct item

        size_t listSize = RegistryUtils::getListSize(registry, activityRoot.c_str());
        for (size_t i = 0; i < listSize; ++i)
        {
            RegistryUtils::getListItem(registry, activityRoot.c_str(), i, itemPath);

            std::string attrValue;
            if (RegistryUtils::getAttribute(registry, itemPath.c_str(), ACTIVITY_ACCOUNT_ID_ATTR, attrValue))
            {
                if (attrValue == idString)
                {
                    matchFound = true;
                    break;
                }
            }
        }

        // If there was no match, create an entry for this account.
        if (!matchFound)
        {
            if (!RegistryUtils::addListItem(registry, activityRoot.c_str(), itemPath))
                return kError;

            // Save the Account ID attribute
            if (!RegistryUtils::setAttribute(registry, itemPath.c_str(), ACTIVITY_ACCOUNT_ID_ATTR, idString))
                return kError;
        }

        // Temp var for building paths
        std::string tempPath;

        // Save the configuration (whatever we can)
        tempPath = itemPath; tempPath += SEP;
        if (incoming) {
            tempPath += "incomingIms";
        }
        else {
            tempPath += "outgoingIms";
        }

        int imCount;
        if (!RegistryUtils::getInt(registry, tempPath.c_str(), imCount)) imCount = 0;

        //get and increment the current count
        RegistryUtils::setInt(registry, tempPath.c_str(), ++imCount);

        DebugLog(<< "UEM: instantMessageInfoFired called with ServerHandle:  " << serverHandle);
        DebugLog(<< "UEM: instantMessageInfoFired called with isSip:  " << isSip);
        DebugLog(<< "UEM: instantMessageInfoFired called with incoming:  " << incoming);
        DebugLog(<< "UEM: instantMessageInfoFired called with IM Count:  " << imCount);
    }
    return CPCAPI2::kSuccess;
}

int AnalyticsManagerImpl::ConvRecordingStarted(CPCAPI2::SipConversation::SipConversationHandle conversationHandle)
{
    mAnalyticsThreadCheck.test();

    mConvoRecordedMap[conversationHandle] = true;
    DebugLog(<< "UEM: ConvRecordingStarted called and recording set to true with ConvoHandle:  " << conversationHandle);
    return CPCAPI2::kSuccess;
}

cpc::vector<CPCAPI2::XmppAccount::XmppAccountHandle> AnalyticsManagerImpl::getXmppAccountHandles() const
{
   cpc::vector<XmppAccount::XmppAccountHandle> currentXmppAccountHandles;

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
   currentXmppAccountHandles = mXmppAccountInterface->getAccountHandles();
#endif

   return currentXmppAccountHandles;
}

int AnalyticsManagerImpl::removeUnnecessaryAccounts(
   std::set<CPCAPI2::SipAccount::SipAccountHandle> &removedSipAccountHandles,
   std::set<CPCAPI2::XmppAccount::XmppAccountHandle>& removedXmppAccountHandles)
{   
   if (mAccountInterface == NULL)
      return CPCAPI2::kSuccess;

   mAnalyticsThreadCheck.test();

   cpc::vector<SipAccount::SipAccountHandle> currentSipAccountHandles = mAccountInterface->getAccountHandles();
   cpc::vector<XmppAccount::XmppAccountHandle> currentXmppAccountHandles = getXmppAccountHandles();

   removedSipAccountHandles.clear();
   removedXmppAccountHandles.clear();

   // Loop over all the analytics handles and populate the information
   AnalyticsMap::const_iterator iter = mInfoMap.begin();
   for (; iter != mInfoMap.end(); ++iter)
   {
      const AnalyticsHandle &serverHandle(iter->first);
      msrp_tree *registry(iter->second->registry);

      const char *itemName = "account";

      std::string activityRoot;
      if (!RegistryUtils::listExists(registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot))
      {
         if (!RegistryUtils::createList(registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot))
            return kError;
      }

      std::string settingsRoot;
      if (!RegistryUtils::listExists(registry, ROOT SEP SETTINGS_DATA, itemName, settingsRoot))
      {
         if (!RegistryUtils::createList(registry, ROOT SEP SETTINGS_DATA, itemName, settingsRoot))
            return kError;
      }

      // -------------------------------------------------
      // remove no longer existing accounts unless they are needed for the stats

      for (std::set<SipAccount::SipAccountHandle>::const_iterator sipIter = mAccumulatedSipAccountList.begin(); 
         sipIter != mAccumulatedSipAccountList.end(); ++sipIter)
      {
         SipAccount::SipAccountHandle account = *sipIter;

         if (std::find(currentSipAccountHandles.begin(), currentSipAccountHandles.end(), account) != currentSipAccountHandles.end())
            continue;   // account still exists

         if (mSipAccountWithStatsList.find(account) != mSipAccountWithStatsList.end())
            continue;   // account is needed for stats

         // Check to see if there is an account list entry with a matching account handle
         std::string idString("SIP.");
         std::ostringstream strs;
         strs << account;
         idString += strs.str();

         std::string itemPath; // Set to point to correct item

         size_t listSize = RegistryUtils::getListSize(registry, activityRoot.c_str());
         for (size_t i = 0; i < listSize; ++i)
         {
            RegistryUtils::getListItem(registry, activityRoot.c_str(), i, itemPath);

            std::string attrValue;
            if (RegistryUtils::getAttribute(registry, itemPath.c_str(), ACTIVITY_ACCOUNT_ID_ATTR, attrValue) && attrValue == idString)
            {
               // at this point the path looks like "cpc_usage_report/activity_data/account_list/3/account" and if we want to 
               // remove the account without leaving an empty "<account/>" item in the report we must truncate the path at the item index
               itemPath = itemPath.substr(0, itemPath.length() - 8);
               RegistryUtils::erase(registry, itemPath.c_str());
               i--; listSize--;
            }
         }

         listSize = RegistryUtils::getListSize(registry, settingsRoot.c_str());
         for (size_t i = 0; i < listSize; ++i)
         {
            RegistryUtils::getListItem(registry, settingsRoot.c_str(), i, itemPath);

            std::string attrValue;
            if (RegistryUtils::getAttribute(registry, itemPath.c_str(), SETTINGS_ACCOUNT_ID_ATTR, attrValue) && attrValue == idString)
            {
               // at this point the path looks like "cpc_usage_report/settings_data/account_list/3/account" and if we want to 
               // remove the account without leaving an empty "<account/>" item in the report we must truncate the path at the item index
               itemPath = itemPath.substr(0, itemPath.length() - 8);
               RegistryUtils::erase(registry, itemPath.c_str());
               i--; listSize--;
            }
         }

         // we have removed the account from the report
         removedSipAccountHandles.insert(account);
      }

      for (std::set<XmppAccount::XmppAccountHandle>::const_iterator xmppIter = mAccumulatedXmppAccountList.begin();
         xmppIter != mAccumulatedXmppAccountList.end(); ++xmppIter)
      {
         XmppAccount::XmppAccountHandle account = *xmppIter;

         if (std::find(currentXmppAccountHandles.begin(), currentXmppAccountHandles.end(), account) != currentXmppAccountHandles.end())
            continue;   // account still exists

         if (mXmppAccountWithStatsList.find(account) != mXmppAccountWithStatsList.end())
            continue;   // account is needed for stats

         // Check to see if there is an account list entry with a matching account handle
         std::string idString("XMPP.");
         std::ostringstream strs;
         strs << account;
         idString += strs.str();

         std::string itemPath; // Set to point to correct item

         size_t listSize = RegistryUtils::getListSize(registry, activityRoot.c_str());
         for (size_t i = 0; i < listSize; ++i)
         {
            RegistryUtils::getListItem(registry, activityRoot.c_str(), i, itemPath);

            std::string attrValue;
            if (RegistryUtils::getAttribute(registry, itemPath.c_str(), ACTIVITY_ACCOUNT_ID_ATTR, attrValue) && attrValue == idString)
            {
               // at this point the path looks like "cpc_usage_report/activity_data/account_list/3/account" and if we want to 
               // remove the account without leaving an empty "<account/>" item in the report we must truncate the path at the item index
               itemPath = itemPath.substr(0, itemPath.length() - 8);
               RegistryUtils::erase(registry, itemPath.c_str());
               i--; listSize--;
            }
         }

         listSize = RegistryUtils::getListSize(registry, settingsRoot.c_str());
         for (size_t i = 0; i < listSize; ++i)
         {
            RegistryUtils::getListItem(registry, settingsRoot.c_str(), i, itemPath);

            std::string attrValue;
            if (RegistryUtils::getAttribute(registry, itemPath.c_str(), SETTINGS_ACCOUNT_ID_ATTR, attrValue) && attrValue == idString)
            {
               // at this point the path looks like "cpc_usage_report/settings_data/account_list/3/account" and if we want to 
               // remove the account without leaving an empty "<account/>" item in the report we must truncate the path at the item index
               itemPath = itemPath.substr(0, itemPath.length() - 8);
               RegistryUtils::erase(registry, itemPath.c_str());
               i--; listSize--;
            }
         }

         // we have removed the account from the report
         removedXmppAccountHandles.insert(account);
      }
   }

   return kSuccess;
}

int AnalyticsManagerImpl::populateAccounts()
{   
   if (mAccountInterface == NULL)
      return CPCAPI2::kSuccess;

   mAnalyticsThreadCheck.test();

   cpc::vector<SipAccount::SipAccountHandle> currentSipAccountHandles = mAccountInterface->getAccountHandles();
   cpc::vector<XmppAccount::XmppAccountHandle> currentXmppAccountHandles = getXmppAccountHandles();
   std::set<CPCAPI2::SipAccount::SipAccountHandle> removedSipAccountHandles;
   std::set<CPCAPI2::XmppAccount::XmppAccountHandle> removedXmppAccountHandles;

   if (this->removeUnnecessaryAccounts(removedSipAccountHandles, removedXmppAccountHandles) != kSuccess)
      return kError;

   // Loop over all the analytics handles and populate the information
   AnalyticsMap::const_iterator iter = mInfoMap.begin();
   for (; iter != mInfoMap.end(); ++iter)
   {
      const AnalyticsHandle &serverHandle(iter->first);
      msrp_tree *registry(iter->second->registry);

      const char *itemName = "account";

      std::string activityRoot;
      if (!RegistryUtils::listExists(registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot))
      {
         if (!RegistryUtils::createList(registry, ROOT SEP ACTIVITY_DATA, itemName, activityRoot))
            return kError;
      }

      std::string settingsRoot;
      if (!RegistryUtils::listExists(registry, ROOT SEP SETTINGS_DATA, itemName, settingsRoot))
      {
         if (!RegistryUtils::createList(registry, ROOT SEP SETTINGS_DATA, itemName, settingsRoot))
            return kError;
      }

      // -------------------------------------------------
      // add/update accounts

      for (std::set<SipAccount::SipAccountHandle>::const_iterator sipIter = mAccumulatedSipAccountList.begin(); 
         sipIter != mAccumulatedSipAccountList.end(); ++sipIter)
      {
         SipAccount::SipAccountHandle account = *sipIter;

         if (removedSipAccountHandles.find(account) != removedSipAccountHandles.end())
            continue;   // account has been removed as not needed, don't add it back

         // Check to see if there is an account list entry with the right account handle
         std::string idString("SIP.");
         std::ostringstream strs;
         strs << account;
         idString += strs.str();

         std::string itemPath; // Set to point to correct item

         // Temp var for building paths
         std::string tempPath;

         // Check to see if there is an account list entry with the right account handle
         bool matchFound(false);

         size_t listSize = RegistryUtils::getListSize(registry, settingsRoot.c_str());
         for (size_t i = 0; i < listSize; ++i)
         {
            RegistryUtils::getListItem(registry, settingsRoot.c_str(), i, itemPath);

            std::string attrValue;
            if (RegistryUtils::getAttribute(registry, itemPath.c_str(), SETTINGS_ACCOUNT_ID_ATTR, attrValue) && attrValue == idString)
            {
               matchFound = true;
               break;
            }
         }

         // If there was no match, create an entry for this account.
         if (!matchFound)
         {
            if (!RegistryUtils::addListItem(registry, settingsRoot.c_str(), itemPath))
               return kError;

            // Save the Account ID attribute
            if (!RegistryUtils::setAttribute(registry, itemPath.c_str(), SETTINGS_ACCOUNT_ID_ATTR, idString))
               return kError;
         }

         // Update the account entries
         SipSettingsMap::const_iterator itSettings = mSipSettingsMap.find(account);
         if (itSettings != mSipSettingsMap.end())
         {
            const NetworkTransport currentNetTransport = mNetworkChangeManagerIf->networkTransport();

            // Use the current transport, or "None" if there is no setting
            // for the current transport.
            const CPCAPI2::SipAccount::SipAccountSettings& settings(
               itSettings->second.size() > ( size_t )currentNetTransport ?
               itSettings->second.at( currentNetTransport ) :
               itSettings->second.at( CPCAPI2::NetworkTransport::TransportNone )
            );

            // Save the configuration (whatever we can)
            if (settings.domain.c_str() != NULL)
            {
               tempPath = itemPath; tempPath += SEP; tempPath += "domain";
               RegistryUtils::setString(registry, tempPath.c_str(), settings.domain.c_str());
            }
            else
            {
               tempPath = itemPath; tempPath += SEP; tempPath += "domain";
               RegistryUtils::setString(registry, tempPath.c_str(), "missing_domain");
            }

            tempPath = itemPath; tempPath += SEP; tempPath += "username";
            RegistryUtils::setString(registry, tempPath.c_str(), settings.username.c_str());

            // Only include the outbound proxy if it's set.
            if (!settings.outboundProxy.empty())
            {
               tempPath = itemPath; tempPath += SEP; tempPath += "outboundProxy";
               RegistryUtils::setString(registry, tempPath.c_str(), settings.outboundProxy.c_str());
            }

            tempPath = itemPath; tempPath += SEP; tempPath += "protocol";
            RegistryUtils::setString(registry, tempPath.c_str(), "SIP");

            tempPath = itemPath; tempPath += SEP; tempPath += "enabled";
            RegistryUtils::setBool(registry, tempPath.c_str(),
               mEnabledSipAccountList.find(account) != mEnabledSipAccountList.end());

            if (itSettings->second.find(TransportWiFi) != itSettings->second.end())
            {
               tempPath = itemPath; tempPath += SEP; tempPath += "sipRefreshWifiInterval";
               RegistryUtils::setInt(registry, tempPath.c_str(), itSettings->second.at(TransportWiFi).registrationIntervalSeconds);
            }
            if (itSettings->second.find(TransportWWAN) != itSettings->second.end())
            {
               tempPath = itemPath; tempPath += SEP; tempPath += "sipRefreshCellInterval";
               RegistryUtils::setInt(registry, tempPath.c_str(), itSettings->second.at(TransportWWAN).registrationIntervalSeconds);
            }

            tempPath = itemPath; tempPath += SEP; tempPath += "sipSimpleSupported";
            RegistryUtils::setBool(registry, tempPath.c_str(), true);

            tempPath = itemPath; tempPath += SEP; tempPath += "signalingTransport";
            switch (settings.sipTransportType)
            {
            case CPCAPI2::SipAccount::SipAccountTransportType::SipAccountTransport_UDP:
               RegistryUtils::setString(registry, tempPath.c_str(), "UDP");
               break;
            case CPCAPI2::SipAccount::SipAccountTransportType::SipAccountTransport_TCP:
               RegistryUtils::setString(registry, tempPath.c_str(), "TCP");
               break;
            case CPCAPI2::SipAccount::SipAccountTransportType::SipAccountTransport_TLS:
               RegistryUtils::setString(registry, tempPath.c_str(), "TLS");
               break;
            }
         }
         else
         {
            ErrLog(<< "Unable to retrieve SIP settings for account handle " << account);
         }
      }

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
      for (std::set<XmppAccount::XmppAccountHandle>::const_iterator xmppIter = mAccumulatedXmppAccountList.begin();
         xmppIter != mAccumulatedXmppAccountList.end(); ++xmppIter)
      {
         XmppAccount::XmppAccountHandle account = *xmppIter;

         if (removedXmppAccountHandles.find(account) != removedXmppAccountHandles.end())
            continue;   // account has been removed as not needed, don't add it back

         // Check to see if there is an account list entry with the right account handle
         std::string idString("XMPP.");
         std::ostringstream strs;
         strs << account;
         idString += strs.str();

         std::string itemPath; // Set to point to correct item

         // Temp var for building paths
         std::string tempPath;

         // Check to see if there is an account list entry with the right account handle
         bool matchFound(false);

         size_t listSize = RegistryUtils::getListSize(registry, settingsRoot.c_str());
         for (size_t i = 0; i < listSize; ++i)
         {
            RegistryUtils::getListItem(registry, settingsRoot.c_str(), i, itemPath);

            std::string attrValue;
            if (RegistryUtils::getAttribute(registry, itemPath.c_str(), SETTINGS_ACCOUNT_ID_ATTR, attrValue) && attrValue == idString)
            {
               matchFound = true;
               break;
            }
         }

         // If there was no match, create an entry for this account.
         if (!matchFound)
         {
            if (!RegistryUtils::addListItem(registry, settingsRoot.c_str(), itemPath))
               return kError;

            // Save the Account ID attribute
            if (!RegistryUtils::setAttribute(registry, itemPath.c_str(), SETTINGS_ACCOUNT_ID_ATTR, idString))
               return kError;
         }

         XmppSettingsMap::const_iterator it = mXmppSettingsMap.find(account);
         if (it != mXmppSettingsMap.end())
         {
            CPCAPI2::XmppAccount::XmppAccountSettings settings = it->second;

            // Save the configuration (whatever we can)
            if (settings.domain.c_str() != NULL)
            {
               tempPath = itemPath; tempPath += SEP; tempPath += "domain";
               RegistryUtils::setString(registry, tempPath.c_str(), settings.domain.c_str());
            }
            else
            {
               tempPath = itemPath; tempPath += SEP; tempPath += "domain";
               RegistryUtils::setString(registry, tempPath.c_str(), "missing_domain");
            }

            tempPath = itemPath; tempPath += SEP; tempPath += "username";
            RegistryUtils::setString(registry, tempPath.c_str(), settings.username.c_str());

            tempPath = itemPath; tempPath += SEP; tempPath += "protocol";
            RegistryUtils::setString(registry, tempPath.c_str(), "XMPP");

            tempPath = itemPath; tempPath += SEP; tempPath += "enabled";
            RegistryUtils::setBool(registry, tempPath.c_str(), 
               mEnabledXmppAccountList.find(account) != mEnabledXmppAccountList.end());

            // DRL FIXIT? Does this belong in the XMPP report?
            tempPath = itemPath; tempPath += SEP; tempPath += "sipSimpleSupported";
            RegistryUtils::setBool(registry, tempPath.c_str(), false);

            tempPath = itemPath; tempPath += SEP; tempPath += "signalingTransport";
            RegistryUtils::setString(registry, tempPath.c_str(), "TCP"); // TLS is negotiated using STARTTLS (not sure what that means here)
         }
         else
         {
            ErrLog(<< "Unable to retrieve XMPP settings for account handle " << account);
         }
      }
#endif
   }

   // -------------------------------------------------
   // now that we've update the reports we can cut back our accumulated accounts so our list doesn't keep growing

   for (std::set<CPCAPI2::SipAccount::SipAccountHandle>::const_iterator sipIter = removedSipAccountHandles.begin(); 
      sipIter != removedSipAccountHandles.end(); sipIter++)
   {
      assert(mAccumulatedSipAccountList.find(*sipIter) != mAccumulatedSipAccountList.end());
      mAccumulatedSipAccountList.erase(*sipIter);

      mSipSettingsMap.erase(*sipIter);
   }

   for (std::set<CPCAPI2::XmppAccount::XmppAccountHandle>::const_iterator xmppIter = removedXmppAccountHandles.begin(); 
      xmppIter != removedXmppAccountHandles.end(); xmppIter++)
   {
      assert(mAccumulatedXmppAccountList.find(*xmppIter) != mAccumulatedXmppAccountList.end());
      mAccumulatedXmppAccountList.erase(*xmppIter);

      mXmppSettingsMap.erase(*xmppIter);
   }

   mSipAccountWithStatsList.clear();
   mXmppAccountWithStatsList.clear();

   return kSuccess;
}

int AnalyticsManagerImpl::sendReport(const AnalyticsHandle& serverHandle)
{
   mAnalyticsThreadCheck.test();

   AnalyticsMap::const_iterator iter = mInfoMap.find(serverHandle);
   if (iter == mInfoMap.end())
   {
      DebugLog(<< "AnalyticsManagerImpl::sendReport(): UEM: sendReport failed for ServerHandle: " << serverHandle << " as no analytics info found");
      return kError;
   }

   if (iter->second->httpClient == NULL)
   {
      DebugLog(<< "AnalyticsManagerImpl::sendReport(): UEM: sendReport failed for ServerHandle: " << serverHandle << " as http client is not initialized");
      return kError;
   }

   DebugLog(<< "AnalyticsManagerImpl::sendReport(): UEM: sendReport called with ServerHandle: " << serverHandle);

   if (populateAccounts())
   {
      DebugLog(<< "AnalyticsManagerImpl::sendReport(): UEM: sendReport failed for ServerHandle: " << serverHandle << " as http client is not initialized");
      return kError;
   }

   // Before generating the document, update the reporting period in the registry
   // on the root document attribute to include the end time.
   const int64_t curTime(secondsSinceEpoch());
   std::ostringstream strs;
   strs << curTime;
   std::string attrValue = strs.str();
   RegistryUtils::setAttribute(iter->second->registry, ROOT, "end", attrValue.c_str());

   std::string document;
   DocumentBuilder db(iter->second->registry);
   if (db.toDocument(document))
   {
      cpc::vector<CPCAPI2::HTTPClient::StringPair> headers;
      CPCAPI2::HTTPClient::StringPair header;
      header.first = "Expect";
      header.second = "100-continue";
      headers.push_back(header);

      char *postBlob = NULL;
      unsigned int length = (unsigned int)document.length();
      if (length > 0)
      {
         postBlob = new char[length + sizeof(wchar_t)];
         memcpy(postBlob, document.c_str(), length);
         postBlob[length] = 0;

         int errorCode;
         int responseStatus;

         cpc::string result;

         if (!iter->second->completeURL.empty())
         {
            pushActiveHttpClient(iter->second->httpClient);

            if (mAbortFutureHttpClients)
            {
               InfoLog(<< "AnalyticsManagerImpl::sendReport(): UEM: mAbortActiveHttpClients is set; aborting request");
               errorCode = 0;
               responseStatus = 408;
            }
            else
            {
               // StackLog(<< "AnalyticsManagerImpl::sendReport(): UEM: http username: " << iter->second->httpUsername.c_str() << " password: " << iter->second->httpPassword.c_str());
               DebugLog(<< "AnalyticsManagerImpl::sendReport(): UEM: submitting analytics report to: " << iter->second->completeURL.c_str() << " xml doc: " << postBlob);
               cpc::string contentType;
               HTTPClient::RedirectInfo redirectInfo;
               iter->second->httpClient->HTTPSendMessage(
                  HTTPClient::EHTTPVerbPOST,
                  iter->second->completeURL.c_str(),
                  "text/xml", // mime-type
                  iter->second->httpUsername.c_str(),
                  iter->second->httpPassword.c_str(),
                  NULL, // client certificate
                  NULL, // client certificate password
                  postBlob,
                  length,
                  0,
                  false,
                  false,
                  false, // do not ignore cert errors
                  true,  // enable cookies
                  "",    // no cookie file needed
                  headers,
                  false,  // verbose logging
                  false, // suppress logging
                  errorCode,
                  responseStatus,
                  contentType,
                  result,
                  redirectInfo,
                  NULL,
                  "");
            }

            popActiveHttpClient();
         }
         else
         {
#ifdef CPCAPI2_AUTO_TEST
            // allow the unit tests to avoid posting to a server by specifying an empty URL
            errorCode = 0;
            responseStatus = 200;
#else
            errorCode = 0;
            responseStatus = 404;
#endif // CPCAPI2_AUTO_TEST
         }

         delete[] postBlob;

         std::string errorText;
         if (errorCode != 0)
         {
            // Fire connection failed event
            OnConnectionFailedEvent evt;
            evt.errNo = 0;
            evt.errNo = errorCode;
            errorText = "Connection failed with code: " + cpc::to_string(responseStatus);
            errorText += " and result: " + result;

            evt.errorValue = cpc::string(errorText.c_str(), errorText.size());
            mCallbacks.add(makeFpCommand(AnalyticsHandler::onConnectionFailed, mHandler, serverHandle, evt));
            if (m_CbHook) { m_CbHook(); }
            DebugLog(<< "UEM: sendReport called with OnConnectionFailedEvent with status:  " << errorText);
            return kError;
         }

         // Fire the response code (if available)
         OnReportResponseEvent evt;
         evt.responseCode = responseStatus;
         errorText = "curl responded with code: " + cpc::to_string(responseStatus);
         evt.errorValue = cpc::string(errorText.c_str(), errorText.size());
         mCallbacks.add(makeFpCommand(AnalyticsHandler::onReportResponse, mHandler, serverHandle, evt));
         if (m_CbHook) { m_CbHook(); }
         DebugLog(<< "UEM: sendReport called with OnReportResponseEvent with status:  " << responseStatus);
         if (responseStatus != 200)
         {
            return kError;
         }
      }

      // If sending the document was successful, erase the portion of the registry
      // under activity_data, this will need to be reconstructed each time before
      // sending.
      RegistryUtils::erase(iter->second->registry, ROOT SEP ACTIVITY_DATA);

      // Set the start of the new recording interval
      RegistryUtils::setAttribute(iter->second->registry, ROOT, "start", attrValue.c_str());

      // Clear the different call-maps
      clearSipCalls();
      clearPttCalls();
   }

#ifdef CPCAPI2_AUTO_TEST
   //try exposing the document xml string
   OnReportCreatedSuccessEvent evt;
   evt.content = cpc::string(document.c_str(), document.size());;

   AnalyticsHandlerInt* hInt;
   if (mHandler == (void*) 0xDEADBEEF)
   {
      hInt = reinterpret_cast<AnalyticsHandlerInt*>(0xDEADBEEF);
   }
   else
   {
      hInt = dynamic_cast<AnalyticsHandlerInt*>(mHandler);
   }

   mCallbacks.add(makeFpCommand(AnalyticsHandlerInt::onReportCreatedSuccess, hInt, serverHandle, evt));
   if (m_CbHook) { m_CbHook(); }
#endif

   return kSuccess;
}

void AnalyticsManagerImpl::clearSipCalls()
{
   // mActiveConvoList.clear() // Calls are removed from list when call is ended, i.e. onConversationEnded
   {
      std::map<CPCAPI2::SipConversation::SipConversationHandle, bool>::iterator i = mConvoRecordedMap.begin();
      while (i != mConvoRecordedMap.end())
      {
         if (std::find(mActiveConvoList.begin(), mActiveConvoList.end(), i->first) == mActiveConvoList.end())
         {
            i = mConvoRecordedMap.erase(i);
         }
         else
         {
            i++;
         }
      }
   }
   {
      std::map<CPCAPI2::SipConversation::SipConversationHandle, bool>::iterator i = mConvoTransferredMap.begin();
      while (i != mConvoTransferredMap.end())
      {
         if (std::find(mActiveConvoList.begin(), mActiveConvoList.end(), i->first) == mActiveConvoList.end())
         {
            i = mConvoTransferredMap.erase(i);
         }
         else
         {
            i++;
         }
      }
   }
   {
      std::map<CPCAPI2::SipConversation::SipConversationHandle, bool>::iterator i = mConvoHeldMap.begin();
      while (i != mConvoHeldMap.end())
      {
         if (std::find(mActiveConvoList.begin(), mActiveConvoList.end(), i->first) == mActiveConvoList.end())
         {
            i = mConvoHeldMap.erase(i);
         }
         else
         {
            i++;
         }
      }
   }
   {
      std::map<CPCAPI2::SipConversation::SipConversationHandle, SipConversationState>::iterator i = mConvoStateMap.begin();
      while (i != mConvoStateMap.end())
      {
         if (std::find(mActiveConvoList.begin(), mActiveConvoList.end(), i->first) == mActiveConvoList.end())
         {
            i = mConvoStateMap.erase(i);
         }
         else
         {
            i++;
         }
      }
   }
   {
      std::map<std::string, CPCAPI2::SipConversation::SipConversationHandle>::iterator i = mCurrentConvoMap.begin();
      while (i != mCurrentConvoMap.end())
      {
         if (std::find(mActiveConvoList.begin(), mActiveConvoList.end(), i->second) == mActiveConvoList.end())
         {
            i = mCurrentConvoMap.erase(i);
         }
         else
         {
            i++;
         }
      }
   }
}

void AnalyticsManagerImpl::clearPttCalls()
{
   // mPttSessionActiveList.clear() // Calls are removed from teh list when the call is ended, i.e. handlePttSessionEnd
   std::vector<CPCAPI2::PushToTalk::PushToTalkServiceHandle> activeServices;
   for (std::map<CPCAPI2::PushToTalk::PushToTalkSessionHandle, PttSessionState>::iterator i = mPttSessionStateMap.begin(); i != mPttSessionStateMap.end(); ++i)
   {
      activeServices.push_back(i->second.service);
   }
   {
      if (mPttServiceStateMap.size() > 1)
      {
         std::map<CPCAPI2::PushToTalk::PushToTalkServiceHandle, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status>::iterator i = mPttServiceStateMap.begin();
         while (i != mPttServiceStateMap.end())
         {
            if (std::find(activeServices.begin(), activeServices.end(), i->first) == activeServices.end())
            {
               i = mPttServiceStateMap.erase(i);
            }
            else
            {
               i++;
            }
         }
      }
   }
   {
      std::map<CPCAPI2::PushToTalk::PushToTalkSessionHandle, PttSessionState>::iterator i = mPttSessionStateMap.begin();
      while (i != mPttSessionStateMap.end())
      {
         if (std::find(mPttSessionActiveList.begin(), mPttSessionActiveList.end(), i->first) == mPttSessionActiveList.end())
         {
            i = mPttSessionStateMap.erase(i);
         }
         else
         {
            i++;
         }
      }
   }
   {
      std::map<std::string, CPCAPI2::PushToTalk::PushToTalkSessionHandle>::iterator i = mPttSessionCurrentMap.begin();
      while (i != mPttSessionCurrentMap.end())
      {
         if (std::find(mPttSessionActiveList.begin(), mPttSessionActiveList.end(), i->second) == mPttSessionActiveList.end())
         {
            i = mPttSessionCurrentMap.erase(i);
         }
         else
         {
            i++;
         }
      }
   }
   {
      std::map<CPCAPI2::PushToTalk::PushToTalkSessionHandle, CPCAPI2::PushToTalk::PttIdentity>::iterator i = mPttSessionIncomingMap.begin();
      while (i != mPttSessionIncomingMap.end())
      {
         if (std::find(mPttSessionActiveList.begin(), mPttSessionActiveList.end(), i->first) == mPttSessionActiveList.end())
         {
            i = mPttSessionIncomingMap.erase(i);
         }
         else
         {
            i++;
         }
      }
   }
}

int AnalyticsManagerImpl::pushActiveHttpClient(CPCAPI2::HTTPClient* httpClient)
{
   resip::Lock lock(mActiveHttpRequestClientsMutex);

   mActiveHttpRequestClients.push_front(httpClient);

   return kSuccess;
}

int AnalyticsManagerImpl::popActiveHttpClient()
{
   resip::Lock lock(mActiveHttpRequestClientsMutex);

   mActiveHttpRequestClients.pop_front();

   return kSuccess;
}

int AnalyticsManagerImpl::abortActiveHttpClients()
{
   resip::Lock lock(mActiveHttpRequestClientsMutex);

   for (std::deque<CPCAPI2::HTTPClient*>::iterator it = mActiveHttpRequestClients.begin(); it != mActiveHttpRequestClients.end(); ++it)
   {
      (*it)->Abort();
   }

   return kSuccess;
}

int AnalyticsManagerImpl::abortFutureHttpClients()
{
   mAbortFutureHttpClients = true;

   return kSuccess;
}

#endif
