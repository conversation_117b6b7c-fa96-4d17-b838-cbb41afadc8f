#pragma once

#ifndef __CPCAPI2_SIPCONVERSATIONHANDLER_INTERNAL_H__
#define __CPCAPI2_SIPCONVERSATIONHANDLER_INTERNAL_H__

#include <call/SipConversationTypes.h>
#include <call/SipConversationHandler.h>

namespace CPCAPI2
{
   namespace SipConversation
   {
      struct ConversationInitiatedEvent
      {
         CPCAPI2::SipAccount::SipAccountHandle account;
         ConversationType conversationType;
         cpc::string      remoteAddress;
      };

      struct SdpOfferAnswerEvent
      {
         CPCAPI2::SipConversation::SessionDescription sdp;
         CPCAPI2::SipConversation::AnswerModeSettings answerMode;
         SdpOfferAnswerEvent() {}
         SdpOfferAnswerEvent(CPCAPI2::SipConversation::SessionDescription sdp_, CPCAPI2::SipConversation::AnswerModeSettings answerMode_) : sdp(sdp_), answerMode(answerMode_) {}
      };

      struct LocalSdpOfferEvent
      {
         cpc::vector<CPCAPI2::SipConversation::MediaInfo> localMediaInfo;
      };

      struct LocalSdpAnswerEvent
      {
         cpc::vector<CPCAPI2::SipConversation::MediaInfo> localMediaInfo;
         CPCAPI2::SipConversation::SessionDescription sdpOffer;
      };

      struct IntervalReportEvent
      {
         cpc::string callQualityReport;
      };

      struct ConversationEndedEventFromStarcodeNetworkChange
      {
         cpc::vector<SipConversationHandle> endedConversations;
         SipConversationHandle originalConversation; // Conversation when network change was initiated
         SipConversationHandle newConversation; // Conversation created for starcode network change handling
      };

      class SipConversationHandlerAdaptor : public SipConversationHandler
      {
      public:
         virtual int onNewConversation(SipConversationHandle conversation, const NewConversationEvent& args) OVERRIDE { return CPCAPI2::kSuccess; }
         virtual int onConversationEnded(SipConversationHandle conversation, const ConversationEndedEvent& args) OVERRIDE { return CPCAPI2::kSuccess; }
         virtual int onIncomingTransferRequest(SipConversationHandle conversation, const TransferRequestEvent& args) OVERRIDE { return CPCAPI2::kSuccess; }
         virtual int onIncomingRedirectRequest(SipConversationHandle conversation, const RedirectRequestEvent& args) OVERRIDE { return CPCAPI2::kSuccess; }
         virtual int onIncomingTargetChangeRequest(SipConversationHandle conversation, const TargetChangeRequestEvent& args) OVERRIDE { return CPCAPI2::kSuccess; }
         virtual int onIncomingHangupRequest(SipConversationHandle conversation, const HangupRequestEvent& args) OVERRIDE { return CPCAPI2::kSuccess; }
         virtual int onIncomingBroadsoftTalkRequest(SipConversationHandle conversation, const BroadsoftTalkEvent& args) OVERRIDE { return CPCAPI2::kSuccess; }
         virtual int onIncomingBroadsoftHoldRequest(SipConversationHandle conversation, const BroadsoftHoldEvent& args) OVERRIDE { return CPCAPI2::kSuccess; }
         virtual int onTransferProgress(SipConversationHandle conversation, const TransferProgressEvent& args) OVERRIDE { return CPCAPI2::kSuccess; }
         virtual int onTransferResponse(SipConversationHandle conversation, const TransferResponseEvent& args) OVERRIDE { return CPCAPI2::kSuccess; }
         virtual int onConversationStateChangeRequest(SipConversationHandle conversation, const ConversationStateChangeRequestEvent& args) OVERRIDE { return CPCAPI2::kSuccess; }
         virtual int onConversationStateChanged(SipConversationHandle conversation, const ConversationStateChangedEvent& args) OVERRIDE { return CPCAPI2::kSuccess; }
         virtual int onConversationMediaChangeRequest(SipConversationHandle conversation, const ConversationMediaChangeRequestEvent& args) OVERRIDE { return CPCAPI2::kSuccess; }
         virtual int onConversationMediaChanged(SipConversationHandle conversation, const ConversationMediaChangedEvent& args) OVERRIDE { return CPCAPI2::kSuccess; }
         virtual int onConversationStatisticsUpdated(SipConversationHandle conversation, const ConversationStatisticsUpdatedEvent& args) OVERRIDE { return CPCAPI2::kSuccess; }
         virtual int onAudioDeviceLevelChange(SipConversationHandle conversation, const ConversationAudioDeviceLevelChangeEvent& args) OVERRIDE { return CPCAPI2::kSuccess; }
         virtual int onError(SipConversationHandle conversation, const ErrorEvent& args) OVERRIDE { return CPCAPI2::kSuccess; }
      };

      /**
       * Private interface for internal methods on the SDK Observers.
       */
      class SipConversationHandlerInternal : public SipConversationHandlerAdaptor
      {
      public:
         /**
          * Event is fired before the completion of the call, i.e. just before a new call is sent
          * on the wire (outbound), and just when an incoming call lands into the app (inbound).
          */
         virtual int onConversationInitiated(SipConversationHandle conversation, const ConversationInitiatedEvent& args) { return -1; }
         virtual int onSdpOfferAnswer(SipConversationHandle conversation, const SdpOfferAnswerEvent& args) { return -1; }
         virtual int onLocalSdpOffer(SipConversationHandle conversation, const LocalSdpOfferEvent& args) { return -1; }
         virtual int onLocalSdpAnswer(SipConversationHandle conversation, const LocalSdpAnswerEvent& args) { return -1; }
         virtual int onIntervalReport(SipConversationHandle conversation, const IntervalReportEvent& args) { return -1; }

         /**
          * Event is fired when calls are destroyed due to starcode network change handling, calls are destroyed when
          * there is an active call and there are calls in the background or when a local conference is in progress.
         */
         virtual int onConversationEndedFromStarcodeNetworkChange(SipConversationHandle conversation, const ConversationEndedEventFromStarcodeNetworkChange& args) { return -1; }
      };
   }
}

#endif /* __CPCAPI2_SIPCONVERSATIONHANDLER_INTERNAL_H__ */
