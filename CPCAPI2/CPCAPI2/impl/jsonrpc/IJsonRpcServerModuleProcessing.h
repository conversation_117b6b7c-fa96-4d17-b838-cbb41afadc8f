#pragma once

#if !defined(CPCAPI2_ISERVERTRANSPORT_H)
#define CPCAPI2_ISERVERTRANSPORT_H

#include <string>

namespace CPCAPI2
{
class IJsonRpcServerModuleProcessing
{
public:
    virtual bool registerFunction(const std::string& functionName, jsonrpccxx::MethodHandle callback, const jsonrpccxx::NamedParamMapping& mapping = jsonrpccxx::NAMED_PARAM_MAPPING) = 0;
}; // class IServerTransport
} // namespace CPCPAI2

#endif // !defined(CPCAPI2_ISERVERTRANSPORT_H)