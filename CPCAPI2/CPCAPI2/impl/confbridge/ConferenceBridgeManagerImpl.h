#pragma once

#if !defined(CPCAPI2_ConferenceBridge_MANAGER_IMPL_H)
#define CPCAPI2_ConferenceBridge_MANAGER_IMPL_H

#include "cpcapi2defs.h"
#include "ConferenceBridgeManagerInterface.h"
#include "ConferenceBridgeSyncHandler.h"
#include "ConferenceBridgeHandlerInternal.h"
#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"
#include "../util/AutoTestProcessor.h"
#include "call/SipConversationManager.h"
#include "call/SipConversationHandlerInternal.h"
#include "call/SipAVConversationManagerInterface.h"
#include "phone/EventSyncHandler.h"
#include "videostreaming/VideoStreaming.h"
#include "mp4recording/Mp4Recording.h"
#include "audiotranscription/AudioTrans.h"
#include "audiotranscription/AudioTransSyncHandler.h"

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>

#include <map>

namespace CPCAPI2
{
namespace ConferenceBridge
{
class ConferenceWebParticipantImpl;
class ConferenceBridgeManagerImpl : public CPCAPI2::EventSyncHandler<CPCAPI2::SipConversation::SipConversationHandlerInternal>,
                                    public CPCAPI2::VideoStreaming::VideoStreamHandler,
                                    public CPCAPI2::Mp4Recording::Mp4RecordingHandler,
                                    public CPCAPI2::AudioTrans::AudioTransHandler,
                                    public CPCAPI2::AudioTrans::AudioTransSyncHandler
{
public:
   ConferenceBridgeManagerImpl(ConferenceHandle h, CPCAPI2::Phone* phone, ConferenceBridgeManagerInterface* confBridgeMgrIf);
   virtual ~ConferenceBridgeManagerImpl();

   void setSdkObservers(const std::set<ConferenceBridgeHandler*>* observers);

   cpc::string getConferenceToken() const {
      return mConferenceToken;
   }

   ConferenceHandle getConferenceHandle() const {
      return mHandle;
   }

   const ConferenceSettings& getConferenceSettings() const {
      return mSettings;
   }

   const ConferenceInfo& getConferenceInfo() const {
      return mInfo;
   }

   const std::vector<cpc::string> getParticipantInfoSummary() const;

   ConferenceHandle getParentConference() const {
      return mParentConference;
   }

   int getNumParticipants() const;
   int getNumVideoStreams() const;
   std::chrono::system_clock::time_point getCreateTime() const;
   void setSFUSenderChannelAudio(int channel);
   int getSFUSenderChannelAudio() const;
   void setSFUSenderChannelVideo(int channel);
   int getSFUSenderChannelVideo() const;
   bool doesFloorOwnerExist() const;
   bool isFloorOwner(ConferenceParticipantHandle participant) const;
   std::vector<int>& getSFUParticipantChannelsAudio();
   std::vector<std::weak_ptr<ConferenceWebParticipantImpl> >& getSFUParticipantChannelsVideo();
   void addWebParticipantMediaStatistics(ConferenceParticipantHandle participant, cpc::vector<ConferenceParticipantMediaStatistics>& stats);

   // ConferenceBridgeManager
   int setHandler(ConferenceBridgeHandler* handler);
   int setConferenceSettings(const ConferenceSettings& conferenceSettings);
   int destroyConference();
   int addSipEndpoint(CPCAPI2::SipAccount::SipAccountHandle sipAccount);
   int getConferenceDetails(ConferenceDetailsResult& confDetails) const;
   int queryConferenceDetails(ConferenceDetailsHandler* handler = NULL);
   int createWebParticipant(ConferenceParticipantHandle webParticipant, bool isContextOwner, const WebParticipantIdentity& identityInfo, bool addToFloor);
   int destroyWebParticipant(ConferenceParticipantHandle webParticipant);
   int setParticipantPermissions(ConferenceParticipantHandle participant, const ParticipantPermissions& permissionsInfo);
   int setWebParticipantIdentity(ConferenceParticipantHandle webParticipant, const WebParticipantIdentity& identityInfo, bool isContextOwner);
   int sendPeerConnectionOffer(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpOffer, PeerConnectionAnswerHandler* handler);
   int sendPeerConnectionAnswer(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpAnswer);
   int generateLocalOffer(ConferenceParticipantHandle webParticipant, PeerConnectionAnswerHandler* handler);
   int takeParticipantSnapshot(ConferenceParticipantHandle participant);
   int setParticipantPhoto(ConferenceParticipantHandle participant, const cpc::string& photoFileNameUtf8);
   int addAssociatedConference(ConferenceHandle conference);
   int removeAssociatedConference(ConferenceHandle conference);
   int setParentConference(ConferenceHandle conference);
   int addToFloor(ConferenceParticipantHandle participant);
   int removeFromFloor(ConferenceParticipantHandle participant);
   int setVideoLayout(VideoLayout layout);
   int setStreamingEnabled(bool enabled);
   int setRecordingEnabled(bool enabled);
   int setTranscriptionEnabled(bool enabled);
   int queryMediaStatistics();

   // SipConversationHandlerInternal
   virtual int onConversationStateChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangedEvent& args) OVERRIDE;
   virtual int onConversationEnded(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationEndedEvent& args) OVERRIDE;
   virtual int onNewConversation(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::NewConversationEvent& args) OVERRIDE;
   virtual int onIncomingTransferRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferRequestEvent& args) OVERRIDE;
   virtual int onIncomingRedirectRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::RedirectRequestEvent& args) OVERRIDE;
   virtual int onIncomingTargetChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TargetChangeRequestEvent& args) OVERRIDE;
   virtual int onIncomingHangupRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::HangupRequestEvent& args) OVERRIDE;
   virtual int onIncomingBroadsoftTalkRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftTalkEvent& args) OVERRIDE;
   virtual int onIncomingBroadsoftHoldRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftHoldEvent& args) OVERRIDE;
   virtual int onTransferProgress(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferProgressEvent& args) OVERRIDE;
   virtual int onConversationStateChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangeRequestEvent& args) OVERRIDE;
   virtual int onConversationMediaChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent& args) OVERRIDE;
   virtual int onConversationMediaChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangedEvent& args) OVERRIDE;
   virtual int onConversationStatisticsUpdated(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent& args) OVERRIDE;
   virtual int onError(CPCAPI2::SipConversation::SipConversationHandle subscription, const CPCAPI2::SipConversation::ErrorEvent& args) OVERRIDE;
   virtual int onConversationInitiated(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationInitiatedEvent& args) OVERRIDE { return kSuccess; }

   // CPCAPI2::VideoStreaming::VideoStreamHandler

   // CPCAPI2::AudioTrans::AudioTransHandler
   virtual int onTranscriptionResult(CPCAPI2::AudioTrans::AudioTransHandle audioTransSession, const CPCAPI2::AudioTrans::AudioTranscriptionResult& evt) OVERRIDE;

   void fireParticipantListState(const cpc::vector<ParticipantInfo>& added, const cpc::vector<ParticipantInfo>& updated, const cpc::vector<ParticipantInfo>& removed);

   template<typename TFn, typename TEvt> void fireEvent(const char* funcName, TFn func, const TEvt& args, bool internalOnly = false, bool logging = true)
   {
      for (std::set<ConferenceBridgeHandler*>::iterator itHandler = mSdkObservers->begin(); itHandler != mSdkObservers->end(); ++itHandler)
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, *itHandler, mHandle, args);
         if (dynamic_cast<ConferenceBridgeSyncHandler*>(*itHandler) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }

      //if (logging)
      //   logEvent(funcName, h);

      if (!internalOnly)
      {
         resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, mAppHandler, mHandle, args);
         if (mAppHandler != (void*)0xDEADBEEF && dynamic_cast<ConferenceBridgeSyncHandler*>(mAppHandler) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }
   }

   template<typename TFn, typename TEvt> void fireInternalEvent(const char* funcName, TFn func, ConferenceBridge::ConferenceHandle conferenceHandle, const TEvt& args)
   {
#if defined(CPCAPI2_AUTO_TEST)
      ConferenceBridgeHandlerInternal* internalHandler = NULL;
      if (mAppHandler)
      {
         if (mAppHandler == (void*)0xDEADBEEF)
         {
            internalHandler = static_cast<ConferenceBridgeHandlerInternal*>(mAppHandler);
         }
         else
         {
            internalHandler = dynamic_cast<ConferenceBridgeHandlerInternal*>(mAppHandler);
         }
      }

      if (internalHandler != NULL)
      {
         resip::ReadCallbackBase* rcb = makeFpCommandNew(funcName, func, (ConferenceBridgeHandlerInternal*)0xDEADBEEF, conferenceHandle, args);
         postCallback(rcb);
      }
#endif
   }

   template<typename TFn, typename TEvt> void fireConfDetailsEvent(ConferenceDetailsHandler* appHandler, const char* funcName, TFn func, int h, const TEvt& args, bool internalOnly = false, bool logging = true)
   {
      resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, appHandler, h, args);
      if (dynamic_cast<ConferenceBridgeSyncHandler*>(appHandler) != NULL)
      {
         (*cb)();
         delete cb;
      }
      else
      {
         postCallback(cb);
      }
   }

   template<typename TFn, typename TEvt> void firePeerConnectionAnswerEvent(PeerConnectionAnswerHandler* appHandler, const char* funcName, TFn func, int h, const TEvt& args, bool internalOnly = false, bool logging = true)
   {
      resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, appHandler, h, args);
      if (dynamic_cast<ConferenceBridgeSyncHandler*>(appHandler) != NULL)
      {
         (*cb)();
         delete cb;
      }
      else
      {
         postCallback(cb);
      }
   }

private:
   void postCallback(resip::ReadCallbackBase* rcb);
   void onSnapshotComplete(CPCAPI2::SipConversation::SipConversationHandle conversation, const cpc::string& filenameUtf8);
   int setParticipantTranscriptionEnabled(ConferenceParticipantHandle participant, bool enabled);
   static void fireParticipantListStateThrottled(ConferenceBridgeManagerInterface* confBridgeMgrIf, ConferenceHandle conf);

private:
   ConferenceHandle mHandle;
   ConferenceSettings mSettings;
   CPCAPI2::Phone* mPhone;
   CPCAPI2::SipConversation::SipAVConversationManagerInterface* mConvMgr;
   CPCAPI2::Media::MediaManagerInterface* mMediaMgr;
   ConferenceBridgeManagerInterface* mConfBridgeMgrIf;
   CPCAPI2::VideoStreaming::VideoStreamingManager* mVideoStreamMgr;
   CPCAPI2::Mp4Recording::Mp4RecordingManager* mMp4RecordingMgr;
   CPCAPI2::AudioTrans::AudioTransManager* mAudioTransMgr;
   bool mShutdown;
   cpc::string mConferenceToken;
   const std::set<ConferenceBridgeHandler*>* mSdkObservers;
   ConferenceBridgeHandler* mAppHandler;
   std::set<CPCAPI2::SipAccount::SipAccountHandle> mMonitoredSipAccounts;

   std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> > mWebParticipants;

   struct ConferenceSipParticipantImpl
   {
      ParticipantInfo participantInfo;
      cpc::vector<CPCAPI2::SipConversation::MediaInfo> remoteMediaInfo;
      CPCAPI2::AudioTrans::AudioTransHandle audioTransHandle = (CPCAPI2::AudioTrans::AudioTransHandle)-1;
      bool transcriptionEnabled = false;
   };
   std::map<CPCAPI2::SipConversation::SipConversationHandle, ConferenceSipParticipantImpl*> mSipParticipantInfo;

   ConferenceInfo mInfo;
   std::set<ConferenceHandle> mAssociatedConferences;
   ConferenceHandle mParentConference;
   int mSfuMixSenderChannelVideo;
   std::vector<std::weak_ptr<ConferenceWebParticipantImpl> > mSfuMixParticipantChannelsVideo;
   int mSfuMixSenderChannelAudio;
   std::vector<int> mSfuMixParticipantChannelsAudio;
   CPCAPI2::VideoStreaming::VideoStreamHandle mVideoStreamHandle;
   bool mStreamingEnabled;
   bool mStreamingStarted;
   CPCAPI2::Mp4Recording::Mp4RecordingHandle mRecordingHandle;
   bool mRecordingEnabled;

   ParticipantListState mPrevParticipantListEvent;
   std::vector<ParticipantListState> mNextParticipantListEvents;
   bool mParticipantListEventPending = false;
   cpc::vector<ParticipantInfo> mLastParticipantFloorSet;
   bool mUseLiteParticipantListEvents = false;

   std::map<ConferenceParticipantHandle, ConferenceBridgeMediaStatistics> mParticipantMediaStats;
   std::set<ConferenceParticipantHandle> mParticipantMediaQueries;

   bool mDidAddRefCamera = false;
   std::chrono::system_clock::time_point mCreateTime;
};



}
}

#endif // CPCAPI2_ConferenceBridge_MANAGER_IMPL_H
