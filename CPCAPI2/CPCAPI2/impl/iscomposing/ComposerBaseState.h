#pragma once

#if !defined(__CPCAPI2_COMPOSER_BASE_STATE_H__)
#define __CPCAPI2_COMPOSER_BASE_STATE_H__

#include "BaseState.h"

#include <assert.h>

namespace CPCAPI2
{
namespace IsComposing
{

class ComposerBaseState : public BaseState
{
public:
   virtual void onIsComposingMessage(IsComposingManager* manager, IsComposingInfo* info) { assert(false); }
   virtual void onMessageSent(IsComposingManager* manager, IsComposingInfo* info) { assert(false); }
   virtual void onIdleTimeout(IsComposingManager* manager, IsComposingInfo* info) { assert(false); }
   virtual void onRefreshTimeout(IsComposingManager* manager, IsComposingInfo* info) { assert(false); }
};

}
}

#endif // __CPCAPI2_COMPOSER_BASE_STATE_H__