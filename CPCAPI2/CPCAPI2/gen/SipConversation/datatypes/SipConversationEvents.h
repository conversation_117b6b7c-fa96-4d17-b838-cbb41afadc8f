#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONEVENTS_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONEVENTS_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  enum class SipConversationEvents
  {
    SipConversationDotOnConversationStateChanged,
    SipConversationDotOnNewConversation,
    SipConversationDotOnConversationEnded,
    SipConversationDotOnIncomingTransferRequest,
    SipConversationDotOnTransferProgress,
    SipConversationDotOnTransferResponse,
    SipConversationDotOnConversationStateChangeRequest,
    SipConversationDotOnConversationMediaChangeRequest,
    SipConversationDotOnConversationMediaChanged,
    SipConversationDotOnError
  };

  inline std::string to_string(SipConversationEvents value)
  {
    switch (value)
    {
      case SipConversationEvents::SipConversationDotOnConversationStateChanged:
        return "SipConversation.onConversationStateChanged";
      case SipConversationEvents::SipConversationDotOnNewConversation:
        return "SipConversation.onNewConversation";
      case SipConversationEvents::SipConversationDotOnConversationEnded:
        return "SipConversation.onConversationEnded";
      case SipConversationEvents::SipConversationDotOnIncomingTransferRequest:
        return "SipConversation.onIncomingTransferRequest";
      case SipConversationEvents::SipConversationDotOnTransferProgress:
        return "SipConversation.onTransferProgress";
      case SipConversationEvents::SipConversationDotOnTransferResponse:
        return "SipConversation.onTransferResponse";
      case SipConversationEvents::SipConversationDotOnConversationStateChangeRequest:
        return "SipConversation.onConversationStateChangeRequest";
      case SipConversationEvents::SipConversationDotOnConversationMediaChangeRequest:
        return "SipConversation.onConversationMediaChangeRequest";
      case SipConversationEvents::SipConversationDotOnConversationMediaChanged:
        return "SipConversation.onConversationMediaChanged";
      case SipConversationEvents::SipConversationDotOnError:
        return "SipConversation.onError";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, SipConversationEvents value)
  {
    switch (value)
    {
      case SipConversationEvents::SipConversationDotOnConversationStateChanged:
        os << "SipConversation.onConversationStateChanged";
        break;
      case SipConversationEvents::SipConversationDotOnNewConversation:
        os << "SipConversation.onNewConversation";
        break;
      case SipConversationEvents::SipConversationDotOnConversationEnded:
        os << "SipConversation.onConversationEnded";
        break;
      case SipConversationEvents::SipConversationDotOnIncomingTransferRequest:
        os << "SipConversation.onIncomingTransferRequest";
        break;
      case SipConversationEvents::SipConversationDotOnTransferProgress:
        os << "SipConversation.onTransferProgress";
        break;
      case SipConversationEvents::SipConversationDotOnTransferResponse:
        os << "SipConversation.onTransferResponse";
        break;
      case SipConversationEvents::SipConversationDotOnConversationStateChangeRequest:
        os << "SipConversation.onConversationStateChangeRequest";
        break;
      case SipConversationEvents::SipConversationDotOnConversationMediaChangeRequest:
        os << "SipConversation.onConversationMediaChangeRequest";
        break;
      case SipConversationEvents::SipConversationDotOnConversationMediaChanged:
        os << "SipConversation.onConversationMediaChanged";
        break;
      case SipConversationEvents::SipConversationDotOnError:
        os << "SipConversation.onError";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const SipConversationEvents& e)
  {
    switch (e)
    {
      case SipConversationEvents::SipConversationDotOnConversationStateChanged:
        j = "SipConversation.onConversationStateChanged";
        break;
      case SipConversationEvents::SipConversationDotOnNewConversation:
        j = "SipConversation.onNewConversation";
        break;
      case SipConversationEvents::SipConversationDotOnConversationEnded:
        j = "SipConversation.onConversationEnded";
        break;
      case SipConversationEvents::SipConversationDotOnIncomingTransferRequest:
        j = "SipConversation.onIncomingTransferRequest";
        break;
      case SipConversationEvents::SipConversationDotOnTransferProgress:
        j = "SipConversation.onTransferProgress";
        break;
      case SipConversationEvents::SipConversationDotOnTransferResponse:
        j = "SipConversation.onTransferResponse";
        break;
      case SipConversationEvents::SipConversationDotOnConversationStateChangeRequest:
        j = "SipConversation.onConversationStateChangeRequest";
        break;
      case SipConversationEvents::SipConversationDotOnConversationMediaChangeRequest:
        j = "SipConversation.onConversationMediaChangeRequest";
        break;
      case SipConversationEvents::SipConversationDotOnConversationMediaChanged:
        j = "SipConversation.onConversationMediaChanged";
        break;
      case SipConversationEvents::SipConversationDotOnError:
        j = "SipConversation.onError";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, SipConversationEvents& e)
  {
      if (0 == j.get<std::string>().compare("SipConversation.onConversationStateChanged"))
    {
      e = SipConversationEvents::SipConversationDotOnConversationStateChanged;
    }
    else if (0 == j.get<std::string>().compare("SipConversation.onNewConversation"))
    {
      e = SipConversationEvents::SipConversationDotOnNewConversation;
    }
    else if (0 == j.get<std::string>().compare("SipConversation.onConversationEnded"))
    {
      e = SipConversationEvents::SipConversationDotOnConversationEnded;
    }
    else if (0 == j.get<std::string>().compare("SipConversation.onIncomingTransferRequest"))
    {
      e = SipConversationEvents::SipConversationDotOnIncomingTransferRequest;
    }
    else if (0 == j.get<std::string>().compare("SipConversation.onTransferProgress"))
    {
      e = SipConversationEvents::SipConversationDotOnTransferProgress;
    }
    else if (0 == j.get<std::string>().compare("SipConversation.onTransferResponse"))
    {
      e = SipConversationEvents::SipConversationDotOnTransferResponse;
    }
    else if (0 == j.get<std::string>().compare("SipConversation.onConversationStateChangeRequest"))
    {
      e = SipConversationEvents::SipConversationDotOnConversationStateChangeRequest;
    }
    else if (0 == j.get<std::string>().compare("SipConversation.onConversationMediaChangeRequest"))
    {
      e = SipConversationEvents::SipConversationDotOnConversationMediaChangeRequest;
    }
    else if (0 == j.get<std::string>().compare("SipConversation.onConversationMediaChanged"))
    {
      e = SipConversationEvents::SipConversationDotOnConversationMediaChanged;
    }
    else if (0 == j.get<std::string>().compare("SipConversation.onError"))
    {
      e = SipConversationEvents::SipConversationDotOnError;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum SipConversationEvents"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONEVENTS_H
