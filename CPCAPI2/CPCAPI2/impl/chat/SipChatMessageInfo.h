#pragma once

#if !defined(__CPCAPI2_SIP_CHAT_MESSAGE_INFO_H__)
#define __CPCAPI2_SIP_CHAT_MESSAGE_INFO_H__

#include "cpcapi2defs.h"
#include "chat/SipChatHandler.h"
#include "../cpm/CpmHelper.h"

#include <map>

using namespace CPCAPI2::CPM;

namespace CPCAPI2
{
namespace SipChat
{

struct SipChatMessageInfo
{
   SipChatMessageHandle handle;
   MessageType type;
   bool outgoing; // true, if it's an outgoing message, false if it's incoming
   cpc::string datetimeString;
   SipChatMessageHandle origHandle; // For IMDN notifications only: handle of the message being reported upon
   IsComposingMessageState isComposingState; // For isComposing notifications only
};
typedef std::map<SipChatMessageHandle, SipChatMessageInfo*> SipChatMessageInfoMap;

}
}

#endif // __CPCAPI2_SIP_CHAT_MESSAGE_INFO_H__
