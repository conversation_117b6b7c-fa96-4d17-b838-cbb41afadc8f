#include <brand_branded.h>

#if (CPCAPI2_BRAND_BIEVENTS_MODULE == 1)

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::BI_EVENTS
#include "util/LogSubsystems.h"
#include "rutil/Logger.hxx"

#ifdef _WIN32
#include <io.h>
#else
#include <unistd.h>
#endif
#include <fcntl.h>
#include <sys/types.h>
#include <sys/stat.h>

#include "BIEventCache.h"

#include <assert.h>
#include <stdio.h>

using namespace CPCAPI2::BIEvents;
#if _WIN32
using CPCAPI2::BIEvents::BIEventCache;
#endif

// Some open compat macros
#ifndef O_BINARY
#define O_BINARY 0
#endif

// Define some things on windows to make the system calls more portable
#ifdef _WIN32
#ifndef S_IRUSR
#define S_IRUSR _S_IREAD
#endif
#ifndef S_IWUSR
#define S_IWUSR _S_IWRITE
#endif
#endif

// Path separator used for open
#ifdef _WIN32
#define PATH_SEP "\\"
#else
#define PATH_SEP "/"
#endif

#define MAGIC_NUMBER_CONST 0x1337CACE
#define VERSION_NUM_CONST 2

typedef struct BICacheHeader
{
   int32_t MagicNumber; // Identifies the file type
   int16_t VersionNumber;
   off_t   ReadIndex;
} BICacheHeader_t;

BIEventCache::BIEventCache(
   const std::string & workingDir,
   const std::string & baseName,
   const int & numFiles,
   const off_t & fileSizeLimit ) :
   m_WorkingDir( workingDir ),
   m_BaseName( baseName ),
   m_NumFiles( numFiles ),
   m_FileSizeLimit( fileSizeLimit ),
   m_CheckPoint( 0 ),
   m_IsRollbackSet( false ),
   m_CurReadFD( -1 ),
   m_CurWriteFD( -1 ),
   m_DeflateStreamInitialized( false ),
   m_InflateStreamInitialized( false )
{
   StackLog(<< "Creating a new BIEventCache");

   init();

   memset( m_DeflateBuf, 0, CHUNK );
   memset( m_InflateBuf, 0, CHUNK );
}

BIEventCache::~BIEventCache()
{
   StackLog(<< "Destroying BIEventCache");

   CloseAllFiles();
}

bool BIEventCache::init( void )
{
   if( !initWriteFiles() )
      return false;

   if( !initReadFiles() )
      return false;

   return true;
}

bool BIEventCache::initWriteFiles( void )
{
   StackLog(<< "Initializing Write Files" );

   BICacheHeader_t writeFileHeader = { 0 };

   // - Ensure the working directory exists and is writable/writable
   struct stat dirStat;
   if( stat( m_WorkingDir.c_str(), &dirStat ) < 0 )
      return false;

   // Check if it's a directory
   if(( dirStat.st_mode & S_IFDIR ) == 0 )
      return false;

   // Make sure it's readable
   if(( dirStat.st_mode & S_IREAD ) == 0 )
      return false;

   // Make sure it's writable
   if(( dirStat.st_mode & S_IWRITE ) == 0 )
      return false;

   std::string fullPath = m_WorkingDir;
   fullPath += PATH_SEP;
   fullPath += m_BaseName;

   assert( m_CurWriteFD == -1 );

   // Open the base filename for reading/writing. Create the file
   // if it's not already present. This is where new events will be
   // added.
   bool retry( false );
   do
   {
      // Try to open the file for writing.
      if(( m_CurWriteFD = open( fullPath.c_str(), O_RDWR | O_BINARY | O_APPEND )) < 0 )
      {
         if( errno == ENOENT )
         {
            // File didn't exist, this is a normal case we have to handle specifically.
            if(( m_CurWriteFD = open( fullPath.c_str(), O_RDWR | O_BINARY | O_APPEND | O_CREAT, S_IRUSR | S_IWUSR )) < 0 )
               goto fail;

            // Since the file was opened, create the header and write it first.
            writeFileHeader.MagicNumber   = MAGIC_NUMBER_CONST;
            writeFileHeader.VersionNumber = VERSION_NUM_CONST;
            writeFileHeader.ReadIndex     = sizeof( BICacheHeader_t ); // positioned just after the header
         
            if( WriteDataToFile(( uint8_t * ) &writeFileHeader, sizeof( BICacheHeader_t ), m_CurWriteFD ) < sizeof( BICacheHeader_t ))
               goto fail;
         }
         else
         {
            // Some other unexpected failure. Just abort.
            goto fail;
         }
      }
      else
      {
         // Successfully opened: read the file header
         if( ReadDataFromFile(( uint8_t * ) &writeFileHeader, sizeof( BICacheHeader_t ), m_CurWriteFD ) < sizeof( BICacheHeader_t ))
            goto fail;
      }

      // Check the magic and version numbers.
      if( writeFileHeader.MagicNumber != MAGIC_NUMBER_CONST )
      {
         // This is not our file! abort.
         goto fail;
      }
      else if( writeFileHeader.VersionNumber != VERSION_NUM_CONST )
      {
         // This IS our file, but a wrong version. Take ownership of the problem.
         // Delete this file because it's probably hanging around from an upgrade.
         // Recreate a new version which is compatible.
         // TODO: maybe in the future we can have some backward compatibility.
         close( m_CurWriteFD );
         m_CurWriteFD = -1;
         unlink( fullPath.c_str() );
         retry = true; // do it all over again
      }
      else
      {
         // Everything looks good, continue;
         m_CurWriteFileName = fullPath;
         retry = false;
      }
   }
   while( retry );

   assert( m_CurWriteFD >= 0 );
   if( m_CurWriteFD != -1 )
   {
      // Initialize the zlib routines to handle compression/decompression of the events
      memset( &m_DeflateStream, 0, sizeof( z_stream ));
      m_DeflateStream.data_type = Z_TEXT; // data will be mostly ascii with some binary
      if( deflateInit( &m_DeflateStream, Z_DEFAULT_COMPRESSION ) != Z_OK )
         goto fail;

      m_DeflateStreamInitialized = true;
      memset( m_DeflateBuf, 0, CHUNK );
   }

   StackLog(<< "Initializing Write Files: success" );
   return true;

fail:

   // Cleanup code here
   CloseWriteFiles();
   return false;
}

bool BIEventCache::initReadFiles( bool resetInflate )
{
   StackLog(<< "Initializing Read Files" );
   BICacheHeader_t readFileHeader = { 0 };

   // - Ensure the working directory exists and is writable/writable
   struct stat dirStat;
   if (stat(m_WorkingDir.c_str(), &dirStat) < 0)
   {
      ErrLog(<< "BIEventCache::initReadFiles stat failed on " << m_WorkingDir);
      return false;
   }

   // Check if it's a directory
   if ((dirStat.st_mode & S_IFDIR) == 0)
   {
      ErrLog(<< "BIEventCache::initReadFiles " << m_WorkingDir << " is not a directory");
      return false;
   }

   // Make sure it's readable
   if ((dirStat.st_mode & S_IREAD) == 0)
   {
      ErrLog(<< "BIEventCache::initReadFiles " << m_WorkingDir << " is not readable");
      return false;
   }

   // Make sure it's writable
   if ((dirStat.st_mode & S_IWRITE) == 0)
   {
      ErrLog(<< "BIEventCache::initReadFiles " << m_WorkingDir << " is not writable");
      return false;
   }

   std::string fullPath = m_WorkingDir;
   fullPath += PATH_SEP;
   fullPath += m_BaseName;

   assert( m_CurReadFD == -1 );

   // Also open the oldest filename for reading/writing. This is where
   // old events will be removed.
   for( int i = ( m_NumFiles - 1 ) ; i >= 0 ; --i )
   {
      std::string fileName( fullPath );
      char buf[ 64 ];

      if( i > 0 )
      {
         snprintf(buf, 64, "%d", i);
         fileName += ".";
         fileName += buf;
      }

      // Try to open the file for read/write.
      if(( m_CurReadFD = open( fileName.c_str(), O_RDWR | O_BINARY )) < 0 )
      {
         if( errno == ENOENT )
            continue; // This is expected, just try another file.
         else
         {
            StackLog(<< "errno " << errno << " opening " << fileName);
            goto fail; // Some other failure, abort
         }
      }

      // Read the file header.
      BICacheHeader_t header = { 0 };
      if( ReadDataFromFile(( uint8_t * ) &header, sizeof( BICacheHeader_t ), m_CurReadFD ) < sizeof( BICacheHeader_t ))
      {
         StackLog(<< "ReadDataFromFile failed");
         goto fail;
      }

      // Check the magic number and version.
      if( header.MagicNumber != MAGIC_NUMBER_CONST )
      {
         StackLog(<< "Magic number wrong");
         // This is not even our file, abort.
         goto fail;
      }
      else if( header.VersionNumber != VERSION_NUM_CONST )
      {
         // This is our file, but the wrong version. Just clear it out
         // for now and pretend it didn't exist. Try the next one.
         close( m_CurReadFD );
         m_CurReadFD = -1;
         unlink( fileName.c_str() );
         continue;
      }
      else
      {
         // Everything is fine. Seek to the new offset and prepare
         // for future reads.
         if( lseek( m_CurReadFD, header.ReadIndex, SEEK_SET ) < 0 )
         {
            StackLog(<< "lseek failure");
            goto fail;
         }

         m_CurReadFileName = fileName;
         m_CheckPoint = header.ReadIndex;
         break; // stop, we found our file.
      }
   }

   assert( m_CurReadFD >= 0 );
   if( m_CurReadFD != -1 && resetInflate )
   {
      // call inflateInit
      memset( &m_InflateStream, 0, sizeof( z_stream ));
      m_InflateStream.data_type = Z_TEXT; // data will be mostly ascii with some binary
      if( inflateInit( &m_InflateStream ) != Z_OK )
      {
         StackLog(<< "inflateInit failure");
         goto fail;
      }

      m_InflateStreamInitialized = true;
      memset( m_InflateBuf, 0, CHUNK );
   }

   StackLog(<< "Initializing Read Files: success" );
   return true;

fail:

   // Cleanup code here
   ErrLog(<< "BIEventCache::initReadFiles other failure");
   CloseReadFiles();
   return false;
}

void BIEventCache::CloseWriteFiles( void )
{
   if( m_DeflateStreamInitialized )
   {
      deflateEnd( &m_DeflateStream );
      m_DeflateStreamInitialized = false;
      m_IsRollbackSet = false;
   }

   if( m_CurWriteFD != -1 )
   {
      StackLog(<< "Closing Write Files" );

      m_CurWriteFileName = "";
      close( m_CurWriteFD );
      m_CurWriteFD = -1;
      m_IsRollbackSet = false;
   }
}

void BIEventCache::CloseReadFiles( bool resetInflate )
{
   if( m_InflateStreamInitialized && resetInflate )
   {
      inflateEnd( &m_InflateStream );
      m_InflateStreamInitialized = false;
   }

   if( m_CurReadFD != -1 )
   {
      StackLog(<< "Closing Read Files" );

      // Get the current position within the read file. seek to the front of the
      // file and update the index within the header so we can pick up where we left
      // off.
      BICacheHeader_t header = { 0 };
      header.MagicNumber     = MAGIC_NUMBER_CONST;
      header.VersionNumber   = VERSION_NUM_CONST;
      header.ReadIndex       = lseek( m_CurReadFD, 0, SEEK_CUR );

      lseek( m_CurReadFD, 0, SEEK_SET );
      WriteDataToFile(( uint8_t * ) &header, sizeof( BICacheHeader_t ), m_CurReadFD );

      m_CurReadFileName = "";

      close( m_CurReadFD );
      m_CurReadFD = -1;
      m_CheckPoint = 0;
   }
}

void BIEventCache::CloseAllFiles( void )
{
   CloseReadFiles();
   CloseWriteFiles();
}

void BIEventCache::RollbackRead( void )
{
   if( m_CurReadFD == -1 || m_CheckPoint == 0 )
      return;

   StackLog(<< "Rolling back a read operation" );
   m_IsRollbackSet = true;
}

bool BIEventCache::Purge( void )
{
   bool isReadOpen( m_CurReadFD != -1 );
   bool isWriteOpen( m_CurWriteFD != -1 );
   bool result = true;

   CloseAllFiles();

   // Iterate over the existing files, deleting the oldest and renaming the rest.
   for( int i = ( m_NumFiles - 1 ) ; i >= 0 ; --i )
   {
      std::string name;
      char buf[ 64 ];
      
      // Initialize names to full path
      name = m_WorkingDir;
      name += PATH_SEP;
      name += m_BaseName;

      // The first name has no suffix
      if( i > 0 )
      {
         snprintf(buf, 64, "%d", i);
         name += ".";
         name += buf;
      }

      if( unlink( name.c_str() ) < 0 )
      {
         if( errno == ENOENT ) // this is fine
            continue;

         result = false;
      }
   }

   if( isWriteOpen )
      initWriteFiles();

   if( isReadOpen )
      initReadFiles();

   return result;
}

bool BIEventCache::RotateFiles( void )
{
   // If m_FilesizeLimit is equal to (say) 4, it means there are 4 files, so
   // the names would be [m_BaseName], [m_BaseName].1, [m_BaseName].2, [m_BaseName].3
   //
   StackLog(<< "Rotating cache files" );

   // Don't do anything if the limit is just one file
   if( m_NumFiles <= 1 )
      return true;

   // First, close any open files
   CloseReadFiles( false );
   CloseWriteFiles();

   // Iterate over the existing files, deleting the oldest and renaming the rest.
   for( int i = ( m_NumFiles - 1 ) ; i >= 0 ; --i )
   {
      std::string newName;
      std::string oldName;
      char buf[ 64 ];
      
      // Initialize names to full path
      newName = m_WorkingDir;
      newName += PATH_SEP;
      newName += m_BaseName;
      oldName = newName;

      // The first name has no suffix
      if( i > 0 )
      {
         snprintf(buf, 64, "%d", i);
         oldName += ".";
         oldName += buf;
      }

      snprintf(buf, 64, "%d", i + 1);
      newName += ".";
      newName += buf;

      if( i == ( m_NumFiles - 1 ))
      {
         // delete the last file. NB: if the file is open for reading, the file
         // should get removed AFTER it has been closed.
         if( unlink( oldName.c_str() ) < 0 )
         {
            //  This is acceptable.
            if( errno != ENOENT )
               return false;
         }
         continue; // no need to try renaming
      }

      // Ensure oldName file exists, not a failure if the file doesn't exist
      // (maybe we just didn't get there in the rotation, yet).
      //
      // NB: rename will fail on Win32 platforms unless *ALL* handles to the
      // file are closed. Even though the name of the file has absolutely
      // nothing to do with a file descriptor... :-P
      if( rename( oldName.c_str(), newName.c_str() ) < 0 )
      {
         if( errno != ENOENT )
            return false; // not OK
      }
   }

   // If you get here, file renaming was successful, reopen
   // the files using init().
   if( !initWriteFiles() )
      return false;

   if( !initReadFiles( false ))
      return false;

   return true;
}

ssize_t BIEventCache::WriteDataToFile( uint8_t * pData, size_t bufsize, int fd )
{
   if( pData == NULL || bufsize == 0 )
      return -1;

   // Append compressed data to the file
   size_t writeCount = 0;
   int result = 0;
   do
   {
      result = write( fd, pData + writeCount, bufsize - writeCount );
      if( result < 0 )
         return -1;

      // write succeeded
      writeCount += result;
   }
   while(( result > 0 ) && ( writeCount < bufsize ));
   return writeCount;
}

ssize_t BIEventCache::ReadDataFromFile( uint8_t *buf, size_t bufsize, int fd )
{
   if( buf == NULL || bufsize == 0 )
      return -1;

   size_t readCount = 0;
   int result = 0;
   do
   {
      // Read a full "chunk" of info
      result = read( fd, buf + readCount, bufsize - readCount );
      if( result < 0 )
         return -1;

      readCount += result;
   }
   while(( result > 0 ) && ( readCount < bufsize ));
   return readCount;
}

int BIEventCache::deflateStuff( uint8_t (&buf)[ CHUNK ], Bytef* input, size_t inputSize, bool lastBlock )
{
   int zResult = Z_OK;

   if( inputSize == 0 || input == NULL )
      return zResult; // considered ok

   m_DeflateStream.avail_in  = inputSize;
   m_DeflateStream.next_in   = input;
   m_DeflateStream.avail_out = CHUNK;
   m_DeflateStream.next_out  = ( Bytef * ) &buf;

   while( m_DeflateStream.avail_in > 0 )
   {
      // Z_PARTIAL_FLUSH is absolutely needed here for the last block, unless
      // you want to deal with pasting some final bits from the end of one
      // block onto the front of the next (yuck). So just pad out the block to
      // be byte aligned.
      assert( m_DeflateStream.avail_out != 0 );
      zResult |= deflate( &m_DeflateStream, lastBlock ? Z_PARTIAL_FLUSH : Z_BLOCK );
      if( zResult == Z_OK )
      {
         WriteDataToFile( buf, CHUNK - m_DeflateStream.avail_out, m_CurWriteFD );

         // Chunk processed, advance to next chunk
         m_DeflateStream.avail_out = CHUNK;
         m_DeflateStream.next_out  = ( Bytef * ) &buf;
      }
   }
   return zResult;
}

int BIEventCache::inflateStuff( uint8_t (&buf)[ CHUNK ], Bytef* output, size_t outputSize )
{
   m_InflateStream.next_out  = output;
   m_InflateStream.avail_out = outputSize;
   ssize_t readCount = 0;
   int zResult = Z_OK;

   // Begin deflation of the record.
   while( m_InflateStream.avail_out > 0 && zResult == Z_OK )
   {
      if( m_InflateStream.avail_in == 0 )
      {
         // Read data if there's nothing currently available
         readCount = ReadDataFromFile( buf, CHUNK, m_CurReadFD );
         m_InflateStream.next_in  = ( Bytef* ) buf;
         m_InflateStream.avail_in = readCount; // could be zero
      }

      zResult |= inflate( &m_InflateStream, Z_BLOCK );
   }
   return zResult;
}

bool BIEventCache::PushEvent( const BIEventRecord& eventContent )
{
   // Skip the event if the file is not open for writing
   if( m_CurWriteFD == -1 )
   {
      ErrLog(<< "Error in PushEvent -- file not open for writing");
      return false;
   }

   // Compress the event record header
   if( deflateStuff( m_DeflateBuf, ( Bytef * ) &eventContent.m_Fixed, sizeof( BIEventRecord::fixed_t )) != Z_OK )
   {
      ErrLog(<< "Error in PushEvent -- deflate1 failed");
      return false;
   }

   // Compress the summary text
   if( deflateStuff( m_DeflateBuf, ( Bytef * ) eventContent.m_SummaryText, eventContent.m_Fixed.SummarySize ) != Z_OK )
   {
      ErrLog(<< "Error in PushEvent -- deflate2 failed");
      return false;
   }

   // Compress the labels
   if( deflateStuff( m_DeflateBuf, ( Bytef * ) eventContent.m_Labels, eventContent.m_Fixed.LabelSize ) != Z_OK )
   {
      ErrLog(<< "Error in PushEvent -- deflate3 failed");
      return false;
   }

   // Compress the variable data at the end
   if( deflateStuff( m_DeflateBuf, ( Bytef * ) eventContent.m_Variable, eventContent.m_Fixed.VariableSize, true ) != Z_OK )
   {
      ErrLog(<< "Error in PushEvent -- deflate4 failed");
      return false;
   }

   // If the size is bigger than the limit, rotate files
   struct stat fileStat;
   if( stat( m_CurWriteFileName.c_str(), &fileStat ) >= 0 )
   {
      if( fileStat.st_size >= m_FileSizeLimit )
      {
         if (RotateFiles())
         {
            return true;
         }
         else
         {
            ErrLog(<< "Error in PushEvent -- rotate files failed");
            return false;
         }
      }
   }

   return true;
}

// Read as many events as we can, within the limit.
void BIEventCache::PopEvents( std::list< BIEventRecord >& outEvents, int limit )
{
   if( m_CurReadFD == -1 )
   {
      StackLog(<< "BIEventCache::PopEvents returning -- mCurReadFD is -1" );
      return;
   }

   BIEventRecord readRecord;
   int zResult = Z_OK;

   if( m_IsRollbackSet )
   {
      memset( m_InflateBuf, 0, CHUNK );
      lseek( m_CurReadFD, m_CheckPoint, SEEK_SET );
      m_InflateStream.avail_in = 0;
      inflateReset( &m_InflateStream );
      m_IsRollbackSet = false;
   }

   // Assumption: the read position has already been 'lseeked' to the right
   // position after opening.

   // Read [ 0 .. limit ] events (at most). Could read less, that's OK.
   for( int i = 0 ; ( i < limit && zResult == Z_OK ) ; ++i )
   {
      // Release anything stored from a previous loop
      memset( &readRecord.m_Fixed, 0, sizeof( BIEventRecord::fixed_t ));
      delete [] readRecord.m_SummaryText;
      readRecord.m_SummaryText = NULL;
      delete [] readRecord.m_Variable;
      readRecord.m_Variable = NULL;

      // If the buffer is empty and we're at the start of a record, remember this
      // point as a checkpoint.
      if( m_InflateStream.avail_in == 0 )
         m_CheckPoint = lseek( m_CurReadFD, 0, SEEK_CUR );

      // Use zlib to decompress from that chunk, unless or until a zlib "block"
      // has been completely decompressed.
      zResult |= inflateStuff( m_InflateBuf, ( Bytef* )( &readRecord.m_Fixed ), sizeof( BIEventRecord::fixed_t ));

      // NB: It is possible that the record was successfully decompressed, but
      // the information in it is junk. In particular the SummarySize might be
      // pure fiction. So we need to protect ourselves from this and catch
      // std::bad_alloc
      try
      {
         // If decompression was successful, continue with next step.
         if(( zResult == Z_OK ) && ( readRecord.m_Fixed.SummarySize > 0 ))
         {
            readRecord.m_SummaryText = new uint8_t[ readRecord.m_Fixed.SummarySize + 1 ];
            zResult |= inflateStuff( m_InflateBuf, ( Bytef* ) readRecord.m_SummaryText, readRecord.m_Fixed.SummarySize );
            readRecord.m_SummaryText[ readRecord.m_Fixed.SummarySize ] = '\0';
         }

         // If decompression was successful, continue with next step.
         if(( zResult == Z_OK ) && ( readRecord.m_Fixed.LabelSize > 0 ))
         {
            readRecord.m_Labels = new uint8_t[ readRecord.m_Fixed.LabelSize ];
            zResult |= inflateStuff( m_InflateBuf, ( Bytef* ) readRecord.m_Labels, readRecord.m_Fixed.LabelSize );
         }

         // If decompression was successful, continue with next step.
         if(( zResult == Z_OK ) && ( readRecord.m_Fixed.VariableSize > 0 ))
         {
            readRecord.m_Variable = new uint8_t[ readRecord.m_Fixed.VariableSize ];
            zResult |= inflateStuff( m_InflateBuf, ( Bytef* ) readRecord.m_Variable, readRecord.m_Fixed.VariableSize );
         }
      }
      catch( std::bad_alloc )
      {
         // Some weird data in the records. Treat this as the end of file. But
         // write a warning log about this as we've lost data.
         StackLog( << "Decompression error in cache file " << m_CurReadFileName << ", skipping remainder of file." );
         lseek( m_CurReadFD, 0, SEEK_END );
         zResult = Z_BUF_ERROR;
      }

      // Check for error conditions and perform handling in each
      if( zResult == Z_OK )
      {
         // If we actually successfully decompressed something, put it in the
         // out param.
         outEvents.push_back( readRecord );
      }
      else if( zResult == Z_BUF_ERROR )
      {
         // If the result is Z_BUF_ERROR, it means we have hit the end of the file.
         // But, that does not mean we are finished with processing events. We
         // should attempt to move to the next file for processing, and otherwise
         // continue, unless there are no remaining files (in which case we are
         // done).
         struct stat fileStat = { 0 };
         if( stat( m_CurReadFileName.c_str(), &fileStat ) >= 0 )
         {
            // Double check to see if we're at the end.
            off_t currentPos = lseek( m_CurReadFD, 0, SEEK_CUR );
            if( currentPos >= fileStat.st_size )
            {
               // We are! So check to make sure there is in fact another
               // file to process.
               if( m_CurReadFileName != m_CurWriteFileName )
               {
                  // There is! Close the current file and remove it, as it is
                  // finished. Restart processing with the next file.
                  std::string readFileName( m_CurReadFileName ); // save this
                  CloseReadFiles(); // resets m_CurReadFileName
                  unlink( readFileName.c_str() );
                  initReadFiles();
                  zResult = Z_OK; // reset error to continue;
               }
            }
         }
      }
      else
      {
         StackLog(<< "BIEventCache::PopEvents some other error from zlib" );
      
         // Some other error from zlib. This is possibly not recoverable, so just
         // exit the loop for now. File rotation may need to occur to purge this
         // data from the queue.
         break;
      }
   }
}

#endif
