#pragma once

#if !defined(GENBAND_SOPI_ADDRESS_BOOK_HANDLER_H)
#define GENBAND_SOPI_ADDRESS_BOOK_HANDLER_H
#endif

#include "cpcapi2defs.h"

#include "event/SipEventState.h"
#include "event/SipEventManager.h"

#include "../event/SipEventManagerInterface.h"
#include "../account/SipAccountInterface.h"
#include "../account/SipAccountImpl.h"
#include "../util/DumFpCommand.h"

#include "genband/GenbandSopiManager.h"

namespace CPCAPI2
{

namespace GenbandSopi
{
class GenbandSopiManagerInterface;


class GenbandSopiAddressBookHandler : public CPCAPI2::SipEvent::SipEventSubscriptionHandler
{
public:
    GenbandSopiAddressBookHandler(GenbandSopiManagerInterface* itf);  
    ~GenbandSopiAddressBookHandler();
	
    void setSubscriptionHandler(GenbandSopiClientHandle handle);

    virtual int onNewSubscription(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NewSubscriptionEvent& args){return kSuccess;};
    virtual int onSubscriptionEnded(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionEndedEvent& args){return kSuccess;};
    int onIncomingEventState(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingEventStateEvent& args);
    virtual int onIncomingResourceList(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingResourceListEvent& args) { return kSuccess; }
    virtual int onSubscriptionStateChanged(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionStateChangedEvent& args){return kSuccess;};

    virtual int onNotifySuccess(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifySuccessEvent& args){return kSuccess;};
    virtual int onNotifyFailure(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifyFailureEvent& args){return kSuccess;};

    virtual int onError(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::ErrorEvent& args){return kSuccess;};
private:
    GenbandSopiClientHandle mClientHandle;
	
    GenbandSopiManagerInterface* mInterface;
};

}
}

//GENBAND_SOPI_ADDRESS_BOOK_HANDLER_H
