#pragma once

#include "cpcapi2defs.h"
#include "event/SipPublicationManager.h"
#include "event/SipEventPublicationHandler.h"
#include <resip/dum/AppDialogSet.hxx>
#include <resip/dum/ClientPublication.hxx>
#include <resip/stack/PlainContents.hxx>
#include <map>

namespace CPCAPI2
{
namespace SipEvent
{
class SipEventPublicationManagerImpl;

   enum SipPublicationState
   {
      SipPublicationState_NotStarted  = 1500,
      SipPublicationState_Active      = 1510,
      SipPublicationState_Terminated  = 1520
   };

   class SipEventPublicationCreationInfo : public resip::AppDialogSet
   {
   public:
      CPCAPI2::SipAccount::SipAccountHandle account;
      std::vector<resip::NameAddr>          targetAddresses;
      resip::ClientPublicationHandle        dumClientPublicationHandle;
      resip::ServerPublicationHandle        dumServerPublicationHandle;
      resip::Data                           eventType;
      cpc::vector<Parameter>                eventPackageParams;
      SipEventPublicationHandle             sdkh;
      SipPublicationState                   publicationState;
      unsigned int                          expiresSeconds;
      std::vector<CPCAPI2::MimeType>        supportedMimeTypes;
      std::unique_ptr<resip::PlainContents>   queuedContents;
      bool                                  displaced;

      SipEventPublicationCreationInfo(resip::SharedPtr<resip::DialogUsageManager> dum, CPCAPI2::SipEvent::SipEventPublicationManagerImpl* evtPubMgr);
      virtual ~SipEventPublicationCreationInfo();

      void cleanup();

   private:
      CPCAPI2::SipEvent::SipEventPublicationManagerImpl* eventPublicationManagerImpl;
   };

   typedef std::map<SipEventPublicationHandle, SipEventPublicationCreationInfo*> SipEventPublicationCreationInfoMap;

   class SipEventPublicationHandleFactory
   {
   public:
      static SipEventPublicationHandle getNext() { return sNextHandle++; }
   private:
      static SipEventPublicationHandle sNextHandle;
   };
}
}
