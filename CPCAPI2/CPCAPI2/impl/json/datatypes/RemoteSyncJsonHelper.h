// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef RemoteSync_JSON_HELPER_H
#define RemoteSync_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/experimental/remotesync/RemoteSyncHandler.h>
#include <interface/experimental/remotesync/RemoteSyncConversationThreadItem.h>
#include <interface/experimental/remotesync/RemoteSyncManager.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::RemoteSyncCallHistory& v)
    {
      w.StartObject();
      Write(w, "remoteName", v.remoteName);
      Write(w, "associatedUri", v.associatedUri);
      Write(w, "associatedNumber", v.associatedNumber);
      Write(w, "callDuration", v.callDuration);
      Write(w, "primaryDeviceHash", v.primaryDeviceHash);
      Write(w, "primaryDevicePlatform", v.primaryDevicePlatform);
      Write(w, "primaryDeviceName", v.primaryDeviceName);
      Write(w, "statusCode", v.statusCode);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::RemoteSyncCallHistory& v)
    {
      Read(json, "remoteName", v.remoteName);
      Read(json, "associatedUri", v.associatedUri);
      Read(json, "associatedNumber", v.associatedNumber);
      Read(json, "callDuration", v.callDuration);
      Read(json, "primaryDeviceHash", v.primaryDeviceHash);
      Read(json, "primaryDevicePlatform", v.primaryDevicePlatform);
      Read(json, "primaryDeviceName", v.primaryDeviceName);
      Read(json, "statusCode", v.statusCode);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::RemoteSyncReaction& v)
    {
      w.StartObject();
      Write(w, "created_time", v.created_time);
      Write(w, "rev", v.rev);
      Write(w, "address", v.address);
      Write(w, "server_id", v.server_id);
      Write(w, "value", v.value);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::RemoteSyncReaction& v)
    {
      Read(json, "created_time", v.created_time);
      Read(json, "rev", v.rev);
      Read(json, "address", v.address);
      Read(json, "server_id", v.server_id);
      Read(json, "value", v.value);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::RemoteSyncGroupChatItem& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::RemoteSyncGroupChatItem& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::RemoteSyncConversationThreadItem& v)
    {
      w.StartObject();
      Write(w, "latestChatInfo", v.latestChatInfo);
      Write(w, "latestMessage", v.latestMessage);
      Write(w, "unreadMessages", v.unreadMessages);
      Write(w, "totalMessages", v.totalMessages);
      Write(w, "hasLatestMessage", v.hasLatestMessage);
      Write(w, "hasLatestChatInfo", v.hasLatestChatInfo);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::RemoteSyncConversationThreadItem& v)
    {
      Read(json, "latestChatInfo", v.latestChatInfo);
      Read(json, "latestMessage", v.latestMessage);
      Read(json, "unreadMessages", v.unreadMessages);
      Read(json, "totalMessages", v.totalMessages);
      Read(json, "hasLatestMessage", v.hasLatestMessage);
      Read(json, "hasLatestChatInfo", v.hasLatestChatInfo);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::SetAccountsEvent& v)
    {
      w.StartObject();
      Write(w, "requestID", v.requestID);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::SetAccountsEvent& v)
    {
      Read(json, "requestID", v.requestID);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::NotificationUpdateEvent& v)
    {
      w.StartObject();
      Write(w, "rev", v.rev);
      Write(w, "items", v.items);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::NotificationUpdateEvent& v)
    {
      Read(json, "rev", v.rev);
      Read(json, "items", v.items);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::MessageReactionsEvent& v)
    {
      w.StartObject();
      Write(w, "requestID", v.requestID);
      Write(w, "rev", v.rev);
      Write(w, "created_time", v.created_time);
      Write(w, "server_id", v.server_id);
      Write(w, "address", v.address);
      Write(w, "value", v.value);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::MessageReactionsEvent& v)
    {
      Read(json, "requestID", v.requestID);
      Read(json, "rev", v.rev);
      Read(json, "created_time", v.created_time);
      Read(json, "server_id", v.server_id);
      Read(json, "address", v.address);
      Read(json, "value", v.value);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::FetchMessagesReactionsCompleteEvent& v)
    {
      w.StartObject();
      Write(w, "requestID", v.requestID);
      Write(w, "rev", v.rev);
      Write(w, "reactions", v.reactions);
      Write(w, "request_offset", v.request_offset);
      Write(w, "request_maxcount", v.request_maxcount);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::FetchMessagesReactionsCompleteEvent& v)
    {
      Read(json, "requestID", v.requestID);
      Read(json, "rev", v.rev);
      Read(json, "reactions", v.reactions);
      Read(json, "request_offset", v.request_offset);
      Read(json, "request_maxcount", v.request_maxcount);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::SyncItemsCompleteEvent& v)
    {
      w.StartObject();
      Write(w, "requestID", v.requestID);
      Write(w, "rev", v.rev);
      Write(w, "items", v.items);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::SyncItemsCompleteEvent& v)
    {
      Read(json, "requestID", v.requestID);
      Read(json, "rev", v.rev);
      Read(json, "items", v.items);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::UpdateItemCompleteEvent& v)
    {
      w.StartObject();
      Write(w, "requestID", v.requestID);
      Write(w, "rev", v.rev);
      Write(w, "delta", v.delta);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::UpdateItemCompleteEvent& v)
    {
      Read(json, "requestID", v.requestID);
      Read(json, "rev", v.rev);
      Read(json, "delta", v.delta);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::FetchRangeCompleteEvent& v)
    {
      w.StartObject();
      Write(w, "requestID", v.requestID);
      Write(w, "items", v.items);
      Write(w, "request_offset", v.request_offset);
      Write(w, "request_count", v.request_count);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::FetchRangeCompleteEvent& v)
    {
      Read(json, "requestID", v.requestID);
      Read(json, "items", v.items);
      Read(json, "request_offset", v.request_offset);
      Read(json, "request_count", v.request_count);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::FetchConversationsCompleteEvent& v)
    {
      w.StartObject();
      Write(w, "requestID", v.requestID);
      Write(w, "items", v.items);
      Write(w, "request_offset", v.request_offset);
      Write(w, "request_count", v.request_count);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::FetchConversationsCompleteEvent& v)
    {
      Read(json, "requestID", v.requestID);
      Read(json, "items", v.items);
      Read(json, "request_offset", v.request_offset);
      Read(json, "request_count", v.request_count);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::ConversationUpdatedEvent& v)
    {
      w.StartObject();
      Write(w, "requestID", v.requestID);
      Write(w, "rev", v.rev);
      Write(w, "conversationID", v.conversationID);
      Write(w, "highestClientCreatedTime", v.highestClientCreatedTime);
      Write(w, "setItemsRead", v.setItemsRead);
      Write(w, "setItemsDeleted", v.setItemsDeleted);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::ConversationUpdatedEvent& v)
    {
      Read(json, "requestID", v.requestID);
      Read(json, "rev", v.rev);
      Read(json, "conversationID", v.conversationID);
      Read(json, "highestClientCreatedTime", v.highestClientCreatedTime);
      Read(json, "setItemsRead", v.setItemsRead);
      Read(json, "setItemsDeleted", v.setItemsDeleted);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::ItemsUpdatedEvent& v)
    {
      w.StartObject();
      Write(w, "isRead", v.isRead);
      Write(w, "isDeleted", v.isDeleted);
      Write(w, "isEdited", v.isEdited);
      Write(w, "accounts", v.accounts);
      Write(w, "itemTypes", v.itemTypes);
      Write(w, "conversationIDs", v.conversationIDs);
      Write(w, "serverIDs", v.serverIDs);
      Write(w, "readTimestamp", v.readTimestamp);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::ItemsUpdatedEvent& v)
    {
      Read(json, "isRead", v.isRead);
      Read(json, "isDeleted", v.isDeleted);
      Read(json, "isEdited", v.isEdited);
      Read(json, "accounts", v.accounts);
      Read(json, "itemTypes", v.itemTypes);
      Read(json, "conversationIDs", v.conversationIDs);
      Read(json, "serverIDs", v.serverIDs);
      Read(json, "readTimestamp", v.readTimestamp);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::UpdateItemsCompleteEvent& v)
    {
      w.StartObject();
      Write(w, "requestID", v.requestID);
      Write(w, "isRead", v.isRead);
      Write(w, "isDeleted", v.isDeleted);
      Write(w, "isEdited", v.isEdited);
      Write(w, "accounts", v.accounts);
      Write(w, "itemTypes", v.itemTypes);
      Write(w, "conversationIDs", v.conversationIDs);
      Write(w, "serverIDs", v.serverIDs);
      Write(w, "readTimestamp", v.readTimestamp);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::UpdateItemsCompleteEvent& v)
    {
      Read(json, "requestID", v.requestID);
      Read(json, "isRead", v.isRead);
      Read(json, "isDeleted", v.isDeleted);
      Read(json, "isEdited", v.isEdited);
      Read(json, "accounts", v.accounts);
      Read(json, "itemTypes", v.itemTypes);
      Read(json, "conversationIDs", v.conversationIDs);
      Read(json, "serverIDs", v.serverIDs);
      Read(json, "readTimestamp", v.readTimestamp);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::MessageCountEvent& v)
    {
      w.StartObject();
      Write(w, "requestID", v.requestID);
      Write(w, "unread", v.unread);
      Write(w, "total", v.total);
      Write(w, "unreadConversations", v.unreadConversations);
      Write(w, "totalConversations", v.totalConversations);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::MessageCountEvent& v)
    {
      Read(json, "requestID", v.requestID);
      Read(json, "unread", v.unread);
      Read(json, "total", v.total);
      Read(json, "unreadConversations", v.unreadConversations);
      Read(json, "totalConversations", v.totalConversations);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::OnErrorEvent& v)
    {
      w.StartObject();
      Write(w, "requestID", v.requestID);
      Write(w, "errorCode", v.errorCode);
      Write(w, "errorMessage", v.errorMessage);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::OnErrorEvent& v)
    {
      Read(json, "requestID", v.requestID);
      Read(json, "errorCode", v.errorCode);
      Read(json, "errorMessage", v.errorMessage);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::OnConnectionStateEvent& v)
    {
      w.StartObject();
      Write(w, "previousState", v.previousState);
      Write(w, "currentState", v.currentState);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::OnConnectionStateEvent& v)
    {
      Read(json, "previousState", v.previousState);
      Read(json, "currentState", v.currentState);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::OnTimestampDeltaEvent& v)
    {
      w.StartObject();
      Write(w, "timestampDelta", v.timestampDelta);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::OnTimestampDeltaEvent& v)
    {
      Read(json, "timestampDelta", v.timestampDelta);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::RemoteSyncHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::RemoteSyncHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::ClientDeviceInfo& v)
    {
      w.StartObject();
      Write(w, "clientDeviceHash", v.clientDeviceHash);
      Write(w, "clientDevicePlatform", v.clientDevicePlatform);
      Write(w, "clientDeviceName", v.clientDeviceName);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::ClientDeviceInfo& v)
    {
      Read(json, "clientDeviceHash", v.clientDeviceHash);
      Read(json, "clientDevicePlatform", v.clientDevicePlatform);
      Read(json, "clientDeviceName", v.clientDeviceName);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::RemoteSyncSettings& v)
    {
      w.StartObject();
      Write(w, "wsSettings", v.wsSettings);
      Write(w, "password", v.password);
      Write(w, "accounts", v.accounts);
      Write(w, "clientDeviceInfo", v.clientDeviceInfo);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::RemoteSyncSettings& v)
    {
      Read(json, "wsSettings", v.wsSettings);
      Read(json, "password", v.password);
      Read(json, "accounts", v.accounts);
      Read(json, "clientDeviceInfo", v.clientDeviceInfo);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::RemoteSyncManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::RemoteSyncManager& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // RemoteSync_JSON_HELPER_H