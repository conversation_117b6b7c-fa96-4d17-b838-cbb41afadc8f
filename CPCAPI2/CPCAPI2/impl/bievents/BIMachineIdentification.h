#pragma once

#if !defined(CPCAPI2__BIEVENTS_MACHINE_IDENTIFICATION)
#define CPCAPI2_BIEVENTS_MACHINE_IDENTIFICATION

#include "cpcapi2defs.h"

#include <string>
#include <vector>

namespace CPCAPI2
{
   namespace BIEvents
   {
      /**
       * This class was cloned from CPCAPI2::MachineIdentification, the purpose
       * of the cloning action was to allow modifications to the method for
       * the purpose of BI, but without impacting Licensing. So, this was a
       * compromise.
       */
      class MachineIdentification
      {
      public:
         // returns a list of name/value pairs
         static bool GetHardwareIds( std::vector< std::pair< std::string, std::string > >& idList );
      #if _WIN32
         static std::vector<std::string> GetAllMACs_Win32( void );
      #endif
      #if defined(__linux__) && !defined(ANDROID)
          static std::vector<std::string> GetAllLinuxMACs();
      #endif
      };
   }
}
#endif // CPCAPI2__BIEVENTS_MACHINE_IDENTIFICATION
