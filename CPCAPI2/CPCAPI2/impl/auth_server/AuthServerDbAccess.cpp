#include "brand_branded.h"

#if (CPCAPI2_BRAND_AUTH_SERVER_MODULE == 1)

#include "AuthServerDbAccess.h"

#include <soci/sqlite3/soci-sqlite3.h>
#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

namespace CPCAPI2
{
namespace AuthServer
{
DbAccess::DbAccess(const size_t num_threads) : mNumThreads(num_threads),
                                               mDbPool(num_threads)
{
}

DbAccess::~DbAccess()
{
   for (size_t i = 0; i != mNumThreads; ++i)
   {
      mDbPool.at(i).close();
   }
}

int DbAccess::initialize(const cpc::string& dbfile)
{

   for (size_t i = 0; i != mNumThreads; ++i)
   {
      try
      {
         if (mDbPool.at(i).is_connected())
         {
            mDbPool.at(i).close();
         }
         mDbPool.at(i).open(soci::sqlite3, (std::string)dbfile);
      }
      catch (soci::soci_error&)
      {
         return -1;
      }
   }

   try
   {
      soci::session db(mDbPool);
      {
         std::unique_lock lock(mLock);
         db <<
            "CREATE TABLE IF NOT EXISTS Users ( "
            " id INTEGER PRIMARY KEY, "
            " username TEXT, "
            " password_hash TEXT "
            " )";
      }
   }
   catch (soci::soci_error&)
   {
      return -2;
   }

   return 0;
}

int DbAccess::addUser(const cpc::string& username, const cpc::string& password_hash)
{
   std::string usernameStr(username.c_str(), username.size());
   std::string passwordHashStr(password_hash.c_str(), password_hash.size());
   soci::session db(mDbPool);
   {
      std::unique_lock lock(mLock);
      db << "INSERT INTO Users(username, password_hash) VALUES(:username, :password)", soci::use(usernameStr), soci::use(passwordHashStr);
   }
   return 0;
}

int DbAccess::updatePassword(const cpc::string& username, const cpc::string& new_password_hash)
{
   std::string usernameStr(username.c_str(), username.size());
   std::string passwordHashStr(new_password_hash.c_str(), new_password_hash.size());
   soci::session db(mDbPool);
   {
      std::unique_lock lock(mLock);
      db << "UPDATE Users SET password_hash=:password_hash WHERE username=:username", soci::use(passwordHashStr), soci::use(usernameStr);
   }
   return 0;
}

int DbAccess::queryUser(const cpc::string& username, const cpc::string& password_hash, bool& userExists, cpc::string& storedPassword)
{
   userExists = false;

   soci::indicator iuser;
   soci::indicator ipass;
   std::string usernameRes;
   std::string password_hashRes;

   const std::string _username(username);

   soci::session db(mDbPool);
   {
      std::shared_lock lock(mLock);
      db << "SELECT username, password_hash FROM Users WHERE username = :username", soci::into(usernameRes, iuser), soci::into(password_hashRes, ipass), soci::use(_username, "username");
   }

   if (db.got_data())
   {
      if (iuser == soci::i_ok)
      {
         userExists = true;
         storedPassword = cpc::string(password_hashRes.c_str());
      }
   }
   return 0;
}

int DbAccess::flushUsers()
{
   soci::session db(mDbPool);
   {
      std::unique_lock lock(mLock);
      db << "DROP TABLE IF EXISTS Users";
      db <<
         "CREATE TABLE IF NOT EXISTS Users ( "
         " id INTEGER PRIMARY KEY, "
         " username TEXT, "
         " password_hash TEXT "
         " )";
   }
   return 0;
}
}
}
#endif
