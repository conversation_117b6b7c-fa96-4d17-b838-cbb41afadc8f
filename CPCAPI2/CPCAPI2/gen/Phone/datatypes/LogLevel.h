#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_PHONE_LOGLEVEL_H
#define JSONRPC_CPCAPI2_PHONE_LOGLEVEL_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  enum class LogLevel
  {
    None,
    Error,
    Warning,
    Info,
    Debug,
    Max
  };

  inline std::string to_string(LogLevel value)
  {
    switch (value)
    {
      case LogLevel::None:
        return "None";
      case LogLevel::Error:
        return "Error";
      case LogLevel::Warning:
        return "Warning";
      case LogLevel::Info:
        return "Info";
      case LogLevel::Debug:
        return "Debug";
      case LogLevel::Max:
        return "Max";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, LogLevel value)
  {
    switch (value)
    {
      case LogLevel::None:
        os << "None";
        break;
      case LogLevel::Error:
        os << "Error";
        break;
      case LogLevel::Warning:
        os << "Warning";
        break;
      case LogLevel::Info:
        os << "Info";
        break;
      case LogLevel::Debug:
        os << "Debug";
        break;
      case LogLevel::Max:
        os << "Max";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const LogLevel& e)
  {
    switch (e)
    {
      case LogLevel::None:
        j = "None";
        break;
      case LogLevel::Error:
        j = "Error";
        break;
      case LogLevel::Warning:
        j = "Warning";
        break;
      case LogLevel::Info:
        j = "Info";
        break;
      case LogLevel::Debug:
        j = "Debug";
        break;
      case LogLevel::Max:
        j = "Max";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, LogLevel& e)
  {
      if (0 == j.get<std::string>().compare("None"))
    {
      e = LogLevel::None;
    }
    else if (0 == j.get<std::string>().compare("Error"))
    {
      e = LogLevel::Error;
    }
    else if (0 == j.get<std::string>().compare("Warning"))
    {
      e = LogLevel::Warning;
    }
    else if (0 == j.get<std::string>().compare("Info"))
    {
      e = LogLevel::Info;
    }
    else if (0 == j.get<std::string>().compare("Debug"))
    {
      e = LogLevel::Debug;
    }
    else if (0 == j.get<std::string>().compare("Max"))
    {
      e = LogLevel::Max;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum LogLevel"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_LOGLEVEL_H
