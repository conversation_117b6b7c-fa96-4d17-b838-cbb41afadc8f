// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef PushToTalk_JSON_HELPER_H
#define PushToTalk_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/experimental/ptt/PushToTalkTypes.h>
#include <impl/ptt/PushToTalkTypesInternal.h>
#include <interface/experimental/ptt/PushToTalkHandler.h>
#include <impl/ptt/PushToTalkHandlerInternal.h>
#include <interface/experimental/ptt/PushToTalkManager.h>
#include <impl/ptt/PushToTalkManagerInternal.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttIdentity& v)
    {
      w.StartObject();
      Write(w, "identityType", v.identityType);
      Write(w, "userName", v.userName);
      Write(w, "displayName", v.displayName);
      Write(w, "ipAddress", v.ipAddress);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttIdentity& v)
    {
      Read(json, "identityType", v.identityType);
      Read(json, "userName", v.userName);
      Read(json, "displayName", v.displayName);
      Read(json, "ipAddress", v.ipAddress);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PushToTalkIpAddressRange& v)
    {
      w.StartObject();
      Write(w, "ipAddrStart", v.ipAddrStart);
      Write(w, "ipAddrEnd", v.ipAddrEnd);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PushToTalkIpAddressRange& v)
    {
      Read(json, "ipAddrStart", v.ipAddrStart);
      Read(json, "ipAddrEnd", v.ipAddrEnd);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PushToTalkServiceSettings& v)
    {
      w.StartObject();
      Write(w, "serviceType", v.serviceType);
      Write(w, "localIdentities", v.localIdentities);
      Write(w, "subscribedChannels", v.subscribedChannels);
      Write(w, "senderIdentity", v.senderIdentity);
      Write(w, "multicastAddress", v.multicastAddress);
      Write(w, "multicastPort", v.multicastPort);
      Write(w, "unicastIpRanges", v.unicastIpRanges);
      Write(w, "unicastBindAddress", v.unicastBindAddress);
      Write(w, "unicastPort", v.unicastPort);
      Write(w, "httpPort", v.httpPort);
      Write(w, "unicastDiscoveryEnabled", v.unicastDiscoveryEnabled);
      Write(w, "keepAliveEnabled", v.keepAliveEnabled);
      Write(w, "endpointListAutoUpdateEnabled", v.endpointListAutoUpdateEnabled);
      Write(w, "endpointListFetchLimit", v.endpointListFetchLimit);
      Write(w, "authServiceAddress", v.authServiceAddress);
      Write(w, "confServiceAddress", v.confServiceAddress);
      Write(w, "username", v.username);
      Write(w, "password", v.password);
      Write(w, "authServiceApiKey", v.authServiceApiKey);
      Write(w, "ignoreCertVerification", v.ignoreCertVerification);
      Write(w, "mediaInactivityIntervalSeconds", v.mediaInactivityIntervalSeconds);
      Write(w, "signallingDscp", v.signallingDscp);
      Write(w, "mediaDscp", v.mediaDscp);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PushToTalkServiceSettings& v)
    {
      Read(json, "serviceType", v.serviceType);
      Read(json, "localIdentities", v.localIdentities);
      Read(json, "subscribedChannels", v.subscribedChannels);
      Read(json, "senderIdentity", v.senderIdentity);
      Read(json, "multicastAddress", v.multicastAddress);
      Read(json, "multicastPort", v.multicastPort);
      Read(json, "unicastIpRanges", v.unicastIpRanges);
      Read(json, "unicastBindAddress", v.unicastBindAddress);
      Read(json, "unicastPort", v.unicastPort);
      Read(json, "httpPort", v.httpPort);
      Read(json, "unicastDiscoveryEnabled", v.unicastDiscoveryEnabled);
      Read(json, "keepAliveEnabled", v.keepAliveEnabled);
      Read(json, "endpointListAutoUpdateEnabled", v.endpointListAutoUpdateEnabled);
      Read(json, "endpointListFetchLimit", v.endpointListFetchLimit);
      Read(json, "authServiceAddress", v.authServiceAddress);
      Read(json, "confServiceAddress", v.confServiceAddress);
      Read(json, "username", v.username);
      Read(json, "password", v.password);
      Read(json, "authServiceApiKey", v.authServiceApiKey);
      Read(json, "ignoreCertVerification", v.ignoreCertVerification);
      Read(json, "mediaInactivityIntervalSeconds", v.mediaInactivityIntervalSeconds);
      Read(json, "signallingDscp", v.signallingDscp);
      Read(json, "mediaDscp", v.mediaDscp);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttServiceStartedEvent& v)
    {
      w.StartObject();
      Write(w, "endpointsDiscovered", v.endpointsDiscovered);
      Write(w, "discoveryRequestsSent", v.discoveryRequestsSent);
      Write(w, "discoveryResponsesReceived", v.discoveryResponsesReceived);
      Write(w, "startupSuccessful", v.startupSuccessful);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttServiceStartedEvent& v)
    {
      Read(json, "endpointsDiscovered", v.endpointsDiscovered);
      Read(json, "discoveryRequestsSent", v.discoveryRequestsSent);
      Read(json, "discoveryResponsesReceived", v.discoveryResponsesReceived);
      Read(json, "startupSuccessful", v.startupSuccessful);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttServiceStatusChangedEvent& v)
    {
      w.StartObject();
      Write(w, "status", v.status);
      Write(w, "reason", v.reason);
      Write(w, "transport", v.transport);
      Write(w, "statusDescription", v.statusDescription);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent& v)
    {
      Read(json, "status", v.status);
      Read(json, "reason", v.reason);
      Read(json, "transport", v.transport);
      Read(json, "statusDescription", v.statusDescription);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttEndpointListEvent& v)
    {
      w.StartObject();
      Write(w, "localIdentity", v.localIdentity);
      Write(w, "endpoints", v.endpoints);
      Write(w, "totalEndpointCount", v.totalEndpointCount);
      Write(w, "offset", v.offset);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttEndpointListEvent& v)
    {
      Read(json, "localIdentity", v.localIdentity);
      Read(json, "endpoints", v.endpoints);
      Read(json, "totalEndpointCount", v.totalEndpointCount);
      Read(json, "offset", v.offset);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttServiceConfiguredEvent& v)
    {
      w.StartObject();
      Write(w, "settings", v.settings);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttServiceConfiguredEvent& v)
    {
      Read(json, "settings", v.settings);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttSessionStateChangedEvent& v)
    {
      w.StartObject();
      Write(w, "service", v.service);
      Write(w, "currentState", v.currentState);
      Write(w, "previousState", v.previousState);
      Write(w, "channelId", v.channelId);
      Write(w, "recipients", v.recipients);
      Write(w, "connectedCalls", v.connectedCalls);
      Write(w, "mediaInfoSent", v.mediaInfoSent);
      Write(w, "mediaInfoReceived", v.mediaInfoReceived);
      Write(w, "totalCalls", v.totalCalls);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttSessionStateChangedEvent& v)
    {
      Read(json, "service", v.service);
      Read(json, "currentState", v.currentState);
      Read(json, "previousState", v.previousState);
      Read(json, "channelId", v.channelId);
      Read(json, "recipients", v.recipients);
      Read(json, "connectedCalls", v.connectedCalls);
      Read(json, "mediaInfoSent", v.mediaInfoSent);
      Read(json, "mediaInfoReceived", v.mediaInfoReceived);
      Read(json, "totalCalls", v.totalCalls);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttIncomingCallEvent& v)
    {
      w.StartObject();
      Write(w, "currentState", v.currentState);
      Write(w, "callerIdentity", v.callerIdentity);
      Write(w, "channelId", v.channelId);
      Write(w, "service", v.service);
      Write(w, "incomingPtt", v.incomingPtt);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttIncomingCallEvent& v)
    {
      Read(json, "currentState", v.currentState);
      Read(json, "callerIdentity", v.callerIdentity);
      Read(json, "channelId", v.channelId);
      Read(json, "service", v.service);
      Read(json, "incomingPtt", v.incomingPtt);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttSessionErrorEvent& v)
    {
      w.StartObject();
      Write(w, "errorCode", v.errorCode);
      Write(w, "callerIdentity", v.callerIdentity);
      Write(w, "channelId", v.channelId);
      Write(w, "service", v.service);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttSessionErrorEvent& v)
    {
      Read(json, "errorCode", v.errorCode);
      Read(json, "callerIdentity", v.callerIdentity);
      Read(json, "channelId", v.channelId);
      Read(json, "service", v.service);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttReceiverDisconnectedEvent& v)
    {
      w.StartObject();
      Write(w, "receiverIdentity", v.receiverIdentity);
      Write(w, "channelId", v.channelId);
      Write(w, "connectedCalls", v.connectedCalls);
      Write(w, "totalCalls", v.totalCalls);
      Write(w, "currentState", v.currentState);
      Write(w, "reason", v.reason);
      Write(w, "service", v.service);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttReceiverDisconnectedEvent& v)
    {
      Read(json, "receiverIdentity", v.receiverIdentity);
      Read(json, "channelId", v.channelId);
      Read(json, "connectedCalls", v.connectedCalls);
      Read(json, "totalCalls", v.totalCalls);
      Read(json, "currentState", v.currentState);
      Read(json, "reason", v.reason);
      Read(json, "service", v.service);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PushToTalkHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PushToTalkHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttInitiateEvent& v)
    {
      w.StartObject();
      Write(w, "pttHandle", v.pttHandle);
      Write(w, "sessionId", v.sessionId);
      Write(w, "transactionId", v.transactionId);
      Write(w, "channelId", v.channelId);
      Write(w, "senderIdentity", v.senderIdentity);
      Write(w, "receiverIdentity", v.receiverIdentity);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttInitiateEvent& v)
    {
      Read(json, "pttHandle", v.pttHandle);
      Read(json, "sessionId", v.sessionId);
      Read(json, "transactionId", v.transactionId);
      Read(json, "channelId", v.channelId);
      Read(json, "senderIdentity", v.senderIdentity);
      Read(json, "receiverIdentity", v.receiverIdentity);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttEndEvent& v)
    {
      w.StartObject();
      Write(w, "pttHandle", v.pttHandle);
      Write(w, "sessionId", v.sessionId);
      Write(w, "transactionId", v.transactionId);
      Write(w, "channelId", v.channelId);
      Write(w, "senderIpAddress", v.senderIpAddress);
      Write(w, "senderIpPort", v.senderIpPort);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttEndEvent& v)
    {
      Read(json, "pttHandle", v.pttHandle);
      Read(json, "sessionId", v.sessionId);
      Read(json, "transactionId", v.transactionId);
      Read(json, "channelId", v.channelId);
      Read(json, "senderIpAddress", v.senderIpAddress);
      Read(json, "senderIpPort", v.senderIpPort);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttSenderInitiateRecordingEvent& v)
    {
      w.StartObject();
      Write(w, "pttHandle", v.pttHandle);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttSenderInitiateRecordingEvent& v)
    {
      Read(json, "pttHandle", v.pttHandle);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttReportEndpointsEvent& v)
    {
      w.StartObject();
      Write(w, "transactionId", v.transactionId);
      Write(w, "senderIdentity", v.senderIdentity);
      Write(w, "endpoints", v.endpoints);
      Write(w, "endpointsCB64", v.endpointsCB64);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttReportEndpointsEvent& v)
    {
      Read(json, "transactionId", v.transactionId);
      Read(json, "senderIdentity", v.senderIdentity);
      Read(json, "endpoints", v.endpoints);
      Read(json, "endpointsCB64", v.endpointsCB64);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttQueryEndpointsRequest& v)
    {
      w.StartObject();
      Write(w, "transactionId", v.transactionId);
      Write(w, "senderIdentity", v.senderIdentity);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttQueryEndpointsRequest& v)
    {
      Read(json, "transactionId", v.transactionId);
      Read(json, "senderIdentity", v.senderIdentity);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttQueryEndpointsResponse& v)
    {
      w.StartObject();
      Write(w, "transactionId", v.transactionId);
      Write(w, "endpointIpAddress", v.endpointIpAddress);
      Write(w, "senderIdentity", v.senderIdentity);
      Write(w, "endpoints", v.endpoints);
      Write(w, "endpointsCB64", v.endpointsCB64);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttQueryEndpointsResponse& v)
    {
      Read(json, "transactionId", v.transactionId);
      Read(json, "endpointIpAddress", v.endpointIpAddress);
      Read(json, "senderIdentity", v.senderIdentity);
      Read(json, "endpoints", v.endpoints);
      Read(json, "endpointsCB64", v.endpointsCB64);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttClientOfferEvent& v)
    {
      w.StartObject();
      Write(w, "pttHandle", v.pttHandle);
      Write(w, "sessionId", v.sessionId);
      Write(w, "sessionDescription", v.sessionDescription);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttClientOfferEvent& v)
    {
      Read(json, "pttHandle", v.pttHandle);
      Read(json, "sessionId", v.sessionId);
      Read(json, "sessionDescription", v.sessionDescription);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttConferenceMediaStatusChangedEvent& v)
    {
      w.StartObject();
      Write(w, "pttHandle", v.pttHandle);
      Write(w, "peerConnection", v.peerConnection);
      Write(w, "cloudConf", v.cloudConf);
      Write(w, "cloudConfSession", v.cloudConfSession);
      Write(w, "mediaStatus", v.mediaStatus);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttConferenceMediaStatusChangedEvent& v)
    {
      Read(json, "pttHandle", v.pttHandle);
      Read(json, "peerConnection", v.peerConnection);
      Read(json, "cloudConf", v.cloudConf);
      Read(json, "cloudConfSession", v.cloudConfSession);
      Read(json, "mediaStatus", v.mediaStatus);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttClientOfferUpdateEvent& v)
    {
      w.StartObject();
      Write(w, "pttHandle", v.pttHandle);
      Write(w, "pc", v.pc);
      Write(w, "sessionId", v.sessionId);
      Write(w, "sessionDescription", v.sessionDescription);
      Write(w, "endpoint", v.endpoint);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttClientOfferUpdateEvent& v)
    {
      Read(json, "pttHandle", v.pttHandle);
      Read(json, "pc", v.pc);
      Read(json, "sessionId", v.sessionId);
      Read(json, "sessionDescription", v.sessionDescription);
      Read(json, "endpoint", v.endpoint);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttReceiverEndedEvent& v)
    {
      w.StartObject();
      Write(w, "pttHandle", v.pttHandle);
      Write(w, "sessionId", v.sessionId);
      Write(w, "transactionId", v.transactionId);
      Write(w, "receiverIdentity", v.receiverIdentity);
      Write(w, "reason", v.reason);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttReceiverEndedEvent& v)
    {
      Read(json, "pttHandle", v.pttHandle);
      Read(json, "sessionId", v.sessionId);
      Read(json, "transactionId", v.transactionId);
      Read(json, "receiverIdentity", v.receiverIdentity);
      Read(json, "reason", v.reason);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttParticipantListUpdateEvent& v)
    {
      w.StartObject();
      Write(w, "localIdentity", v.localIdentity);
      Write(w, "channelId", v.channelId);
      Write(w, "participants", v.participants);
      Write(w, "totalParticipantCount", v.totalParticipantCount);
      Write(w, "offset", v.offset);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttParticipantListUpdateEvent& v)
    {
      Read(json, "localIdentity", v.localIdentity);
      Read(json, "channelId", v.channelId);
      Read(json, "participants", v.participants);
      Read(json, "totalParticipantCount", v.totalParticipantCount);
      Read(json, "offset", v.offset);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttDiscoveryListUpdate& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttDiscoveryListUpdate& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttStatisticsEvent& v)
    {
      w.StartObject();
      Write(w, "statisticsEventType", v.statisticsEventType);
      Write(w, "callerIdentity", v.callerIdentity);
      Write(w, "channelId", v.channelId);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttStatisticsEvent& v)
    {
      Read(json, "statisticsEventType", v.statisticsEventType);
      Read(json, "callerIdentity", v.callerIdentity);
      Read(json, "channelId", v.channelId);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttServiceRestartedEvent& v)
    {
      w.StartObject();
      Write(w, "reason", v.reason);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttServiceRestartedEvent& v)
    {
      Read(json, "reason", v.reason);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttMediaStatistics& v)
    {
      w.StartObject();
      Write(w, "connectionId", v.connectionId);
      Write(w, "mediaStreamId", v.mediaStreamId);
      Write(w, "rtpPacketCount", v.rtpPacketCount);
      Write(w, "rtcpPacketCount", v.rtcpPacketCount);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttMediaStatistics& v)
    {
      Read(json, "connectionId", v.connectionId);
      Read(json, "mediaStreamId", v.mediaStreamId);
      Read(json, "rtpPacketCount", v.rtpPacketCount);
      Read(json, "rtcpPacketCount", v.rtcpPacketCount);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PttMediaStatisticsEvent& v)
    {
      w.StartObject();
      Write(w, "mediaStreamStats", v.mediaStreamStats);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PttMediaStatisticsEvent& v)
    {
      Read(json, "mediaStreamStats", v.mediaStreamStats);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PushToTalkHandlerAdaptor& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PushToTalkHandlerAdaptor& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PushToTalkHandlerInternal& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PushToTalkHandlerInternal& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PushToTalkManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PushToTalkManager& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PushToTalkSettingsInternal& v)
    {
      w.StartObject();
      Write(w, "unicastTransmitEnabled", v.unicastTransmitEnabled);
      Write(w, "unicastReceiveEnabled", v.unicastReceiveEnabled);
      Write(w, "mediaInactivityMonitorEnabled", v.mediaInactivityMonitorEnabled);
      Write(w, "participantUpdateListEnabled", v.participantUpdateListEnabled);
      Write(w, "ignoreIncomingInitiate", v.ignoreIncomingInitiate);
      Write(w, "participantListFetchLimit", v.participantListFetchLimit);
      Write(w, "connectionRetryMaximum", v.connectionRetryMaximum);
      Write(w, "connectedEndpointThresholdPercentage", v.connectedEndpointThresholdPercentage);
      Write(w, "outgoingSessionStartupDelayMsecs", v.outgoingSessionStartupDelayMsecs);
      Write(w, "outgoingSessionWaitForTalkSpurtMsecs", v.outgoingSessionWaitForTalkSpurtMsecs);
      Write(w, "outgoingSessionExpiryMsecs", v.outgoingSessionExpiryMsecs);
      Write(w, "outgoingConnectedMsecs", v.outgoingConnectedMsecs);
      Write(w, "outgoingConnectionErrorMsecs", v.outgoingConnectionErrorMsecs);
      Write(w, "outgoingDisconnectedMsecs", v.outgoingDisconnectedMsecs);
      Write(w, "incomingSetupMsecs", v.incomingSetupMsecs);
      Write(w, "incomingConnectionRetryMsecs", v.incomingConnectionRetryMsecs);
      Write(w, "incomingConnectedMsecs", v.incomingConnectedMsecs);
      Write(w, "incomingWaitForAnswerMsecs", v.incomingWaitForAnswerMsecs);
      Write(w, "incomingRejectedMsecs", v.incomingRejectedMsecs);
      Write(w, "unicastEndpointListIntervalMsecs", v.unicastEndpointListIntervalMsecs);
      Write(w, "unicastDiscoverySendIntervalMsecs", v.unicastDiscoverySendIntervalMsecs);
      Write(w, "unicastInitialDiscoveryRetryIntervalMsecs", v.unicastInitialDiscoveryRetryIntervalMsecs);
      Write(w, "unicastDiscoveryRetryIntervalMsecs", v.unicastDiscoveryRetryIntervalMsecs);
      Write(w, "unicastDiscoveryRetryIntervalMaximumMsecs", v.unicastDiscoveryRetryIntervalMaximumMsecs);
      Write(w, "networkFailureConnectionIntervalMsecs", v.networkFailureConnectionIntervalMsecs);
      Write(w, "networkChangeConnectionIntervalMsecs", v.networkChangeConnectionIntervalMsecs);
      Write(w, "networkWifiStatusIntervalMsecs", v.networkWifiStatusIntervalMsecs);
      Write(w, "unicastInitRetryIntervalMsecs", v.unicastInitRetryIntervalMsecs);
      Write(w, "unicastTargets", v.unicastTargets);
      Write(w, "maximumUnicastTargetLimit", v.maximumUnicastTargetLimit);
      Write(w, "maximumUnicastMessageRetryLimit", v.maximumUnicastMessageRetryLimit);
      Write(w, "networkMaskOverride", v.networkMaskOverride);
      Write(w, "channelOverrideDurationMs", v.channelOverrideDurationMs);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PushToTalkSettingsInternal& v)
    {
      Read(json, "unicastTransmitEnabled", v.unicastTransmitEnabled);
      Read(json, "unicastReceiveEnabled", v.unicastReceiveEnabled);
      Read(json, "mediaInactivityMonitorEnabled", v.mediaInactivityMonitorEnabled);
      Read(json, "participantUpdateListEnabled", v.participantUpdateListEnabled);
      Read(json, "ignoreIncomingInitiate", v.ignoreIncomingInitiate);
      Read(json, "participantListFetchLimit", v.participantListFetchLimit);
      Read(json, "connectionRetryMaximum", v.connectionRetryMaximum);
      Read(json, "connectedEndpointThresholdPercentage", v.connectedEndpointThresholdPercentage);
      Read(json, "outgoingSessionStartupDelayMsecs", v.outgoingSessionStartupDelayMsecs);
      Read(json, "outgoingSessionWaitForTalkSpurtMsecs", v.outgoingSessionWaitForTalkSpurtMsecs);
      Read(json, "outgoingSessionExpiryMsecs", v.outgoingSessionExpiryMsecs);
      Read(json, "outgoingConnectedMsecs", v.outgoingConnectedMsecs);
      Read(json, "outgoingConnectionErrorMsecs", v.outgoingConnectionErrorMsecs);
      Read(json, "outgoingDisconnectedMsecs", v.outgoingDisconnectedMsecs);
      Read(json, "incomingSetupMsecs", v.incomingSetupMsecs);
      Read(json, "incomingConnectionRetryMsecs", v.incomingConnectionRetryMsecs);
      Read(json, "incomingConnectedMsecs", v.incomingConnectedMsecs);
      Read(json, "incomingWaitForAnswerMsecs", v.incomingWaitForAnswerMsecs);
      Read(json, "incomingRejectedMsecs", v.incomingRejectedMsecs);
      Read(json, "unicastEndpointListIntervalMsecs", v.unicastEndpointListIntervalMsecs);
      Read(json, "unicastDiscoverySendIntervalMsecs", v.unicastDiscoverySendIntervalMsecs);
      Read(json, "unicastInitialDiscoveryRetryIntervalMsecs", v.unicastInitialDiscoveryRetryIntervalMsecs);
      Read(json, "unicastDiscoveryRetryIntervalMsecs", v.unicastDiscoveryRetryIntervalMsecs);
      Read(json, "unicastDiscoveryRetryIntervalMaximumMsecs", v.unicastDiscoveryRetryIntervalMaximumMsecs);
      Read(json, "networkFailureConnectionIntervalMsecs", v.networkFailureConnectionIntervalMsecs);
      Read(json, "networkChangeConnectionIntervalMsecs", v.networkChangeConnectionIntervalMsecs);
      Read(json, "networkWifiStatusIntervalMsecs", v.networkWifiStatusIntervalMsecs);
      Read(json, "unicastInitRetryIntervalMsecs", v.unicastInitRetryIntervalMsecs);
      Read(json, "unicastTargets", v.unicastTargets);
      Read(json, "maximumUnicastTargetLimit", v.maximumUnicastTargetLimit);
      Read(json, "maximumUnicastMessageRetryLimit", v.maximumUnicastMessageRetryLimit);
      Read(json, "networkMaskOverride", v.networkMaskOverride);
      Read(json, "channelOverrideDurationMs", v.channelOverrideDurationMs);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PushToTalkSettingsInternal::IpTarget& v)
    {
      w.StartObject();
      Write(w, "address", v.address);
      Write(w, "port", v.port);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PushToTalkSettingsInternal::IpTarget& v)
    {
      Read(json, "address", v.address);
      Read(json, "port", v.port);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PushToTalk::PushToTalkManagerInternal& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PushToTalk::PushToTalkManagerInternal& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // PushToTalk_JSON_HELPER_H