#include "../datatypes/PhoneCreate.h"
#include "../datatypes/PhoneRelease.h"
#include "../datatypes/PhoneGetVersion.h"
#include "../datatypes/PhoneInitialize.h"
#include "../datatypes/LicenseInfo.h"
#include "../datatypes/SslCipherOptions.h"
#include "../datatypes/PhoneSetLoggingEnabled.h"
#include "../datatypes/PhoneSetFileLoggingEnabled.h"
#include "../datatypes/PhoneSetLogDirectory.h"
#include "../datatypes/PhoneSetLogLevel.h"
#include "../datatypes/LogLevel.h"
#include "../datatypes/PhoneGetInstanceId.h"
#include "../datatypes/PhoneCreateEventQueue.h"
#include "../datatypes/PhoneDestroyEventQueue.h"
#include "../datatypes/PhoneGetEvent.h"

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
int64_t Phone::create()
{
    PhoneCreate params = new PhoneCreate();
    nlohmann::json j = this.transport.request("create");
    return j.get<int64_t>();
};

void Phone::release(int64_t handle)
{
    PhoneRelease params = new PhoneRelease(handle);
    nlohmann::json j = this.transport.request("release", params.marshal());
    
};

cpc::string Phone::getVersion()
{
    PhoneGetVersion params = new PhoneGetVersion();
    nlohmann::json j = this.transport.request("getVersion");
    return j.get<cpc::string>();
};

void Phone::initialize(int64_t handle, LicenseInfo licenseInfo, bool useNetworkChangeManager, SslCipherOptions ciphers)
{
    PhoneInitialize params = new PhoneInitialize(handle, licenseInfo, useNetworkChangeManager, ciphers);
    nlohmann::json j = this.transport.request("initialize", params.marshal());
    
};

void Phone::setLoggingEnabled(int64_t handle, bool enabled)
{
    PhoneSetLoggingEnabled params = new PhoneSetLoggingEnabled(handle, enabled);
    nlohmann::json j = this.transport.request("setLoggingEnabled", params.marshal());
    
};

void Phone::setFileLoggingEnabled(int64_t handle, bool enabled, cpc::string id)
{
    PhoneSetFileLoggingEnabled params = new PhoneSetFileLoggingEnabled(handle, enabled, id);
    nlohmann::json j = this.transport.request("setFileLoggingEnabled", params.marshal());
    
};

void Phone::setLogDirectory(int64_t handle, cpc::string directory)
{
    PhoneSetLogDirectory params = new PhoneSetLogDirectory(handle, directory);
    nlohmann::json j = this.transport.request("setLogDirectory", params.marshal());
    
};

void Phone::setLogLevel(int64_t handle, LogLevel level)
{
    PhoneSetLogLevel params = new PhoneSetLogLevel(handle, level);
    nlohmann::json j = this.transport.request("setLogLevel", params.marshal());
    
};

cpc::string Phone::getInstanceId(int64_t handle)
{
    PhoneGetInstanceId params = new PhoneGetInstanceId(handle);
    nlohmann::json j = this.transport.request("getInstanceId", params.marshal());
    return j.get<cpc::string>();
};

int64_t Phone::createEventQueue(int64_t handle, cpc::vector<cpc::string> events)
{
    PhoneCreateEventQueue params = new PhoneCreateEventQueue(handle, events);
    nlohmann::json j = this.transport.request("createEventQueue", params.marshal());
    return j.get<int64_t>();
};

void Phone::destroyEventQueue(int64_t handle, int64_t eventQueueHandle)
{
    PhoneDestroyEventQueue params = new PhoneDestroyEventQueue(handle, eventQueueHandle);
    nlohmann::json j = this.transport.request("destroyEventQueue", params.marshal());
    
};

PhoneEvent Phone::getEvent(int64_t handle, int64_t eventQueueHandle, int64_t timeout)
{
    PhoneGetEvent params = new PhoneGetEvent(handle, eventQueueHandle, timeout);
    nlohmann::json j = this.transport.request("getEvent", params.marshal());
    return j.get<PhoneEvent>();
};
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone