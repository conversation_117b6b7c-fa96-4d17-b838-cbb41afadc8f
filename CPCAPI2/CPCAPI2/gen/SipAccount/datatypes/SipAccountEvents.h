#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTEVENTS_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTEVENTS_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  enum class SipAccountEvents
  {
    SipAccountDotOnAccountStatusChanged,
    SipAccountDotOnError
  };

  inline std::string to_string(SipAccountEvents value)
  {
    switch (value)
    {
      case SipAccountEvents::SipAccountDotOnAccountStatusChanged:
        return "SipAccount.onAccountStatusChanged";
      case SipAccountEvents::SipAccountDotOnError:
        return "SipAccount.onError";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, SipAccountEvents value)
  {
    switch (value)
    {
      case SipAccountEvents::SipAccountDotOnAccountStatusChanged:
        os << "SipAccount.onAccountStatusChanged";
        break;
      case SipAccountEvents::SipAccountDotOnError:
        os << "SipAccount.onError";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const SipAccountEvents& e)
  {
    switch (e)
    {
      case SipAccountEvents::SipAccountDotOnAccountStatusChanged:
        j = "SipAccount.onAccountStatusChanged";
        break;
      case SipAccountEvents::SipAccountDotOnError:
        j = "SipAccount.onError";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, SipAccountEvents& e)
  {
      if (0 == j.get<std::string>().compare("SipAccount.onAccountStatusChanged"))
    {
      e = SipAccountEvents::SipAccountDotOnAccountStatusChanged;
    }
    else if (0 == j.get<std::string>().compare("SipAccount.onError"))
    {
      e = SipAccountEvents::SipAccountDotOnError;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum SipAccountEvents"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTEVENTS_H
