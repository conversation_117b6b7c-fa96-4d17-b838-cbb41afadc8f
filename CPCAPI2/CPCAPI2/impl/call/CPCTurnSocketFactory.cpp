#include "CPCTurnSocketFactory.h"
#include "CPCTurnAsyncUdpSocket.h"

using namespace CPCAPI2;

CPCTurnSocketFactory::CPCTurnSocketFactory()
   : TurnSocketFactory()
{
}

std::shared_ptr<reTurn::TurnAsyncSocket> CPCTurnSocketFactory::createSocket(
   resip::HighPerfReactor& reactor,
   resip::Resolver <resip::HighPerfReactor>& resolver,
   reTurn::TurnAsyncSocketHandler *handler,
   reTurn::StunTuple localBinding
)
{
   std::shared_ptr<reTurn::TurnAsyncSocket> result;

   switch( localBinding.getTransportType() )
   {
   case reTurn::StunTuple::UDP:
      result.reset(new CPCTurnAsyncUdpSocket(reactor, resolver, handler, resip::Tuple::inet_ntop(localBinding.getAddress()), localBinding.getPort()));
      break;
   case reTurn::StunTuple::TCP:
   case reTurn::StunTuple::TLS:
      break;
   default:
      // Bad Transport type!
      assert(false);
   }

   return result;
}

