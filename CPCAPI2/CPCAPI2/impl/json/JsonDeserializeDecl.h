#pragma once

#ifndef JSON_DESERIALIZE_DECL_H
#define JSON_DESERIALIZE_DECL_H

#include <interface/public/cpcstl/string.h>
#include <interface/public/cpcstl/vector.h>
#include <string>
#include <map>
#include <vector>
#include <rutil/Data.hxx>

#include <document.h> // rapidjson

namespace CPCAPI2
{
namespace Json
{
   inline void Deserialize(const rapidjson::Value& json, bool& v)          { if (json.IsBool()) v = json.GetBool(); }
   inline void Deserialize(const rapidjson::Value& json, char& v)          { if (json.IsInt()) v = (char)json.GetInt(); } // Was promoted to int during serialization
   inline void Deserialize(const rapidjson::Value& json, int8_t& v)        { if (json.IsInt()) v = (int8_t)json.GetInt(); } // Was promoted to int during serialization
   inline void Deserialize(const rapidjson::Value& json, uint8_t& v)       { if (json.IsUint()) v = (uint8_t)json.GetUint(); } // Was promoted to uint during serialization
   inline void Deserialize(const rapidjson::Value& json, int16_t& v)       { if (json.IsInt()) v = (int16_t)json.GetInt(); } // Was promoted to int during serialization
   inline void Deserialize(const rapidjson::Value& json, uint16_t& v)      { if (json.IsUint()) v = (uint16_t)json.GetUint(); } // Was promoted to uint during serialization
   inline void Deserialize(const rapidjson::Value& json, int32_t& v)       { if (json.IsInt()) v = json.GetInt(); }
   inline void Deserialize(const rapidjson::Value& json, uint32_t& v)      { if (json.IsUint()) v = json.GetUint(); }
   inline void Deserialize(const rapidjson::Value& json, int64_t& v)       { if (json.IsInt64()) v = json.GetInt64(); }
   inline void Deserialize(const rapidjson::Value& json, uint64_t& v)      { if (json.IsUint64()) v = json.GetUint64(); }
   inline void Deserialize(const rapidjson::Value& json, void*& v)         { if (json.IsUint64()) v = (void*)json.GetUint64(); }
   inline void Deserialize(const rapidjson::Value& json, float& v)         { if (json.IsFloat()) v = json.GetFloat(); }
   inline void Deserialize(const rapidjson::Value& json, double& v)        { if (json.IsDouble()) v = json.GetDouble(); }
   inline void Deserialize(const rapidjson::Value& json, cpc::string& v)   { if (json.IsString()) v = cpc::string(json.GetString(), json.GetStringLength()); }
   inline void Deserialize(const rapidjson::Value& json, std::string& v)   { if (json.IsString()) v = std::string(json.GetString(), json.GetStringLength()); }
   inline void Deserialize(const rapidjson::Value& json, resip::Data& v)   { if (json.IsString()) v = resip::Data(json.GetString(), json.GetStringLength()); }

   template<size_t size> void Deserialize(const rapidjson::Value& json, char (&v)[size]);
   template<typename T, size_t size> void Deserialize(const rapidjson::Value& json, T (&v)[size]);
   template<typename T> void Deserialize(const rapidjson::Value& json, cpc::vector<T>& array);
   template<typename T> void Deserialize(const rapidjson::Value& json, std::vector<T>& array);
   template<typename T, typename U> void Deserialize(const rapidjson::Value& json, std::map<T, U>& map);

   template<typename T> void Read(const rapidjson::Value& parent, const char* name, T& v);
   template<typename T> void Read(const rapidjson::Value& parent, const char* name, T& v, T d);
} // namespace Json
} // namespace CPCAPI2

#endif //JSON_DESERIALIZE_DECL_H
