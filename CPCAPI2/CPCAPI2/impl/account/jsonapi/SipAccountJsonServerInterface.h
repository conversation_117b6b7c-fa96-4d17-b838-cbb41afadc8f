#pragma once

#if !defined(CPCAPI2_SIP_ACCOUNT_JSON_INTERFACE_H)
#define CPCAPI2_SIP_ACCOUNT_JSON_INTERFACE_H

#include "interface/experimental/account/SipAccountJsonApi.h"
#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "jsonapi/JsonApiServerModule.h"
#include "account/SipAccountHandlerInternal.h"
#include "cpcapi2types.h"
#include "phone/EventSyncHandler.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>

namespace CPCAPI2
{
class PhoneInterface;
namespace SipAccount
{
class SipAccountJsonServerInterface : public CPCAPI2::EventSyncHandler<CPCAPI2::SipAccount::SipAccountHandlerInternal>,
                                      public CPCAPI2::SipAccount::SipAccountAdornmentHandler,
                                      public CPCAPI2::SipAccount::SipAccountJson<PERSON>pi,
                                      public CPCAPI2::JsonApi::JsonApiServerModule,
                                      public CPCAPI2::PhoneModule
{
public:
   SipAccountJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~SipAccountJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // SipAccountHandlerInternal
   virtual int onAccountConfigured(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountConfiguredEvent& args) OVERRIDE { return kSuccess; }
   virtual int onAccountEnabled(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountEnabledEvent& args) OVERRIDE { return kSuccess; }
   virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args) OVERRIDE;
   virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args) OVERRIDE { return kSuccess; }
   virtual int onLicensingError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::LicensingErrorEvent& args) OVERRIDE { return kSuccess; }

   // SipAccountAdornmentHandler
   int onAccountAdornment(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountAdornmentEvent& args) OVERRIDE;
private:
   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);

   int handleCreate(const rapidjson::Value& functionObjectVal);
   int handleConfigureDefaultAccountSettings(const rapidjson::Value& functionObjectVal);
   int handleConfigureTransportAccountSettings(const rapidjson::Value& functionObjectVal);
   int handleApplySettings(const rapidjson::Value& functionObjectVal);
   int handleDestroy(const rapidjson::Value& functionObjectVal);
   int handleEnable(const rapidjson::Value& functionObjectVal);
   int handleDisable(const rapidjson::Value& functionObjectVal);
   int handleRequestRegistrationRefresh(const rapidjson::Value& functionObjectVal);
   int handleSetNetworkRestriction(const rapidjson::Value& functionObjectVal);
   int handleRequestStateAllAccounts(const rapidjson::Value& functionObjectVal);
   int handleSetAdornmentHeaders(const rapidjson::Value & functionObjectVal);

private:
   CPCAPI2::PhoneInterface* mPhone;
   CPCAPI2::SipAccount::SipAccountHandle mAcctHandle;
   cpc::vector<CPCAPI2::SipHeader> mAdornHeaders;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;
};
}
}
#endif // CPCAPI2_SIP_ACCOUNT_JSON_INTERFACE_H
