#include <string>
#include <optional>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_PHONE_PHONEONERROREVENT_H
#define JSONRPC_CPCAPI2_PHONE_PHONEONERROREVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  class PhoneOnErrorEvent
  {
  public:
    std::optional<std::string> sourceModule;
    std::optional<std::string> errorText;

    PhoneOnErrorEvent() = default;

    PhoneOnErrorEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump();
    }

    friend void to_json(nlohmann::json& j, const PhoneOnErrorEvent& o)
    {
       if (o.sourceModule) { j["sourceModule"] = *(o.sourceModule); }
     if (o.errorText) { j["errorText"] = *(o.errorText); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, PhoneOnErrorEvent& o)
    {
     if (j.contains("sourceModule")) { o.sourceModule = j.at("sourceModule"); }
     if (j.contains("errorText")) { o.errorText = j.at("errorText"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_PHONEONERROREVENT_H
