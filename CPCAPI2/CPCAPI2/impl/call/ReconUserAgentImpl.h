#pragma once

#if !defined(CPCAPI2_RECON_USER_AGENT_IMPL_H)
#define CPCAPI2_RECON_USER_AGENT_IMPL_H

#include <MediaStackImpl.hxx>
#include <CodecFactoryImpl.hxx>

#include <resip/recon/UserAgent.hxx>
#include <resip/recon/ConversationProfile.hxx>
#include <resip/recon/RTPPortAllocator.hxx>
#include <resip/recon/UserAgentRegistration.hxx>
#include <resip/dum/DialogUsageManager.hxx>

namespace CPCAPI2
{
namespace SipConversation
{
class ReconRegManager : public recon::RegistrationManager
{
public:
   ReconRegManager();

   virtual ~ReconRegManager();

   virtual recon::ConversationProfileHandle findRegistration(const resip::Uri& requestURI);

   virtual bool manageRegistrations() { return false; }

   void setConversationProfile(recon::ConversationProfileHandle cph);

private:
   recon::ConversationProfileHandle mConvProfileHandle;
};

class ReconUserAgent : public recon::UserAgent
{
public:
   ReconUserAgent(const std::shared_ptr<webrtc_recon::MediaStackImpl>& ms);

   virtual ~ReconUserAgent();

   virtual resip::DialogUsageManager* getDialogUsageManager();

   virtual void getPortRange(unsigned int& minPort, unsigned int& maxPort, sdpcontainer::SdpMediaLine::SdpMediaType mediaType);

   virtual recon::MediaStack* getMediaStack()
   {
      if (std::shared_ptr<webrtc_recon::MediaStackImpl> ms = mMediaStack.lock())
      {
         return ms.get();
      }
      return NULL;
   }

   virtual recon::CodecFactory* getCodecFactory()
   {
      return getMediaStack()->codecFactory().get();
   }

   virtual recon::RegistrationManager* getRegistrationManager()
   {
      return mRegManager;
   }

   void setDUM(const resip::SharedPtr<resip::DialogUsageManager>& dum)
   {
      mDum = dum;
   }

   void setPort(unsigned int minPort, unsigned int maxPort, sdpcontainer::SdpMediaLine::SdpMediaType mediaType)
   {
      mMinRTPPortMap[mediaType] = minPort;
      mMaxRTPPortMap[mediaType] = maxPort;
   }

private:
   resip::SharedPtr<resip::DialogUsageManager> mDum;
   ReconRegManager* mRegManager;
   std::weak_ptr<webrtc_recon::MediaStackImpl> mMediaStack;
   std::map<sdpcontainer::SdpMediaLine::SdpMediaType, unsigned int> mMinRTPPortMap;
   std::map<sdpcontainer::SdpMediaLine::SdpMediaType, unsigned int> mMaxRTPPortMap;
};
}
}
#endif // ABPHONE_RECONUSERAGENT_H
