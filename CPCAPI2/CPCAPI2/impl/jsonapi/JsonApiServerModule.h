#pragma once

#if !defined(CPCAPI2_JSON_API_SERVER_MODULE_H)
#define CPCAPI2_JSON_API_SERVER_MODULE_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiTypes.h"

#include <memory>

// rapidjson
#include <document.h>

// websocketpp
#include <websocketpp/common/connection_hdl.hpp>

// Simple-Http-Server
#include <server_https.hpp>

// resip
#include <rutil/Data.hxx>

namespace CPCAPI2
{

namespace JsonApi
{

typedef SimpleWeb::Server<SimpleWeb::HTTP> HttpServer;
typedef SimpleWeb::Server<SimpleWeb::HTTPS> HttpsServer;

struct JsonApiRequestInfo
{
   enum JsonApiTransportType
   {
      JsonApiTransportType_Unknown,
      JsonApiTransportType_WS,
      JsonApiTransportType_HTTP,
      JsonApiTransportType_HTTPS,
      JsonApiTransportType_InMem
   };

   JsonApiRequestInfo()
      : transportType(JsonApiTransportType_Unknown),
        jsonUserHandle(0)
   {
   }

   JsonApiRequestInfo(const websocketpp::connection_hdl& websocket_conn_hdl)
      : transportType(JsonApiTransportType_WS),
        websocket_conn(websocket_conn_hdl),
        jsonUserHandle(0)
   {
   }

   JsonApiRequestInfo(const std::shared_ptr<CPCAPI2::JsonApi::HttpServer::Response>& http_response_arg)
      : transportType(JsonApiTransportType_HTTP),
      http_response(http_response_arg),
      jsonUserHandle(0)
   {
   }

   JsonApiRequestInfo(const std::shared_ptr<CPCAPI2::JsonApi::HttpsServer::Response>& https_response_arg)
      : transportType(JsonApiTransportType_HTTPS),
      https_response(https_response_arg),
      jsonUserHandle(0)
   {
   }

   JsonApiTransportType transportType;

   // websocket transport
   websocketpp::connection_hdl websocket_conn;

   /* http_response - http transport pointer*/
   std::shared_ptr<CPCAPI2::JsonApi::HttpServer::Response> http_response;
   /* https_response - https transport pointer*/
   std::shared_ptr<CPCAPI2::JsonApi::HttpsServer::Response> https_response;

   // user/device identity
   AuthToken authToken;

   JsonApiUserHandle jsonUserHandle;

   std::string websocketUrlResource;

   bool operator<(const JsonApiRequestInfo& rhs) const
   {
      if (transportType == rhs.transportType)
      {
         if (transportType == JsonApiTransportType_WS)
         {
            return websocket_conn.owner_before(rhs.websocket_conn);
         }
         else if (transportType == JsonApiTransportType_HTTP)
         {
            return (http_response < rhs.http_response);
         }
         else if (transportType == JsonApiTransportType_HTTPS)
         {
            return (https_response < rhs.https_response);
         }
         else
         {
            assert(0);
         }
      }
      else
      {
         if (transportType == JsonApiTransportType_WS)
         {
            return true;
         }
      }
      return false;
   }
};

/**
*/
class JsonApiServerModule
{
public:
   virtual int processIncoming(const JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) = 0;
   virtual int processIncoming(const JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request, const std::shared_ptr<resip::Data>& /*binaryData*/) {
      return processIncoming(conn, request);
   }
   virtual int handleConnectionClosed(const JsonApiRequestInfo& conn) {
      return 0;
   }
};

}

}

#endif // CPCAPI2_JSON_API_SERVER_MODULE_H
