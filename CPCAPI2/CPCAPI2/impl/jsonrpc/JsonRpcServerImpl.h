#pragma once

#if !defined(CPCAPI2_JSON_RPC_SERVER_IMPL_H)
#define CPCAPI2_JSON_RPC_SERVER_IMPL_H

#include <memory>
#include <string>
#include <jsonrpccxx/server.hpp>

#include <interface/experimental/jsonrpc/JsonRpcServer.h>

#include "impl/jsonrpc/PhoneInstances.h"

#include "gen/Phone/server/PhoneServer.h"
#include "impl/phone/jsonrpc/PhoneCompatibility.h"
#include "gen/NetworkChangeManager/server/NetworkChangeManagerServer.h"
#include "impl/phone/jsonrpc/NetworkChangeManagerCompatibility.h"
#include "impl/account/jsonrpc/SipAccountCompatibility.h"
#include "gen/SipAccount/server/SipAccountServer.h"
#include "impl/call/jsonrpc/SipConversationCompatibility.h"
#include "gen/MediaManager/server/MediaManagerServer.h"
#include "impl/media/jsonrpc/MediaManagerCompatibility.h"
#include "gen/SipConversation/server/SipConversationServer.h"
#include "impl/mwi/jsonrpc/SipMWICompatibility.h"
#include "gen/SipMwi/server/SipMwiServer.h"
#include "impl/media/jsonrpc/AudioCompatibility.h"
#include "gen/Audio/server/AudioServer.h"

namespace CPCAPI2
{
namespace JsonRpc
{
/**
 */
class JsonRpcServerImpl : public JsonRpcServer,
                          public IJsonRpcServerModuleProcessing
{
public:
    JsonRpcServerImpl();
    virtual ~JsonRpcServerImpl() = default;

    bool registerFunction(const std::string &functionName, jsonrpccxx::MethodHandle callback, const jsonrpccxx::NamedParamMapping &mapping = jsonrpccxx::NAMED_PARAM_MAPPING) override;

    std::string processIncoming(const std::string& incoming) override;

protected:
    std::shared_ptr<jsonrpccxx::JsonRpc2Server> mJsonRpc;

    std::shared_ptr<CPCAPI2::PhoneInstances> mPhoneInstances;
    
    jsonrpc::CPCAPI2::Phone::PhoneServer mPhoneServer;
    jsonrpc::CPCAPI2::Phone::PhoneCompatibility mPhoneCompatibility;
    jsonrpc::CPCAPI2::NetworkChangeManager::NetworkChangeManagerServer mNetworkChangeManagerServer;
    jsonrpc::CPCAPI2::NetworkChangeManager::NetworkChangeManagerCompatibility mNetworkChangeManagerCompatibility;
    jsonrpc::CPCAPI2::SipAccount::SipAccountServer mSipAccountServer;
    jsonrpc::CPCAPI2::SipAccount::SipAccountCompatibility mSipAccountCompatibility;
    jsonrpc::CPCAPI2::SipConversation::SipConversationCompatibility mSipConversationCompatibility;
    jsonrpc::CPCAPI2::SipConversation::SipConversationServer mSipConversationServer;
    jsonrpc::CPCAPI2::MediaManager::MediaManagerCompatibility mMediaManagerCompatibility;
    jsonrpc::CPCAPI2::MediaManager::MediaManagerServer mMediaManagerServer;
    jsonrpc::CPCAPI2::SipMwi::SipMwiCompatibility mSipMwiCompatibility;
    jsonrpc::CPCAPI2::SipMwi::SipMwiServer mSipMwiServer;
    jsonrpc::CPCAPI2::Audio::AudioCompatibility mAudioCompatibility;
    jsonrpc::CPCAPI2::Audio::AudioServer mAudioServer;
}; // class JsonRpcServer
} // namespace JsonRpc
} // namespace CPCAPI2

#endif // CPCAPI2_JSON_RPC_SERVER_IMPL_H
