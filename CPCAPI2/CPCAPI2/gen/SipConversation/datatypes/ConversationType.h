#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_CONVERSATIONTYPE_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_CONVERSATIONTYPE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  enum class ConversationType
  {
    Incoming,
    Outgoing,
    IncomingJoinRequest,
    IncomingTransferRequest,
    OutgoingNetworkChangeHandover
  };

  inline std::string to_string(ConversationType value)
  {
    switch (value)
    {
      case ConversationType::Incoming:
        return "Incoming";
      case ConversationType::Outgoing:
        return "Outgoing";
      case ConversationType::IncomingJoinRequest:
        return "IncomingJoinRequest";
      case ConversationType::IncomingTransferRequest:
        return "IncomingTransferRequest";
      case ConversationType::OutgoingNetworkChangeHandover:
        return "OutgoingNetworkChangeHandover";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, ConversationType value)
  {
    switch (value)
    {
      case ConversationType::Incoming:
        os << "Incoming";
        break;
      case ConversationType::Outgoing:
        os << "Outgoing";
        break;
      case ConversationType::IncomingJoinRequest:
        os << "IncomingJoinRequest";
        break;
      case ConversationType::IncomingTransferRequest:
        os << "IncomingTransferRequest";
        break;
      case ConversationType::OutgoingNetworkChangeHandover:
        os << "OutgoingNetworkChangeHandover";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const ConversationType& e)
  {
    switch (e)
    {
      case ConversationType::Incoming:
        j = "Incoming";
        break;
      case ConversationType::Outgoing:
        j = "Outgoing";
        break;
      case ConversationType::IncomingJoinRequest:
        j = "IncomingJoinRequest";
        break;
      case ConversationType::IncomingTransferRequest:
        j = "IncomingTransferRequest";
        break;
      case ConversationType::OutgoingNetworkChangeHandover:
        j = "OutgoingNetworkChangeHandover";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, ConversationType& e)
  {
      if (0 == j.get<std::string>().compare("Incoming"))
    {
      e = ConversationType::Incoming;
    }
    else if (0 == j.get<std::string>().compare("Outgoing"))
    {
      e = ConversationType::Outgoing;
    }
    else if (0 == j.get<std::string>().compare("IncomingJoinRequest"))
    {
      e = ConversationType::IncomingJoinRequest;
    }
    else if (0 == j.get<std::string>().compare("IncomingTransferRequest"))
    {
      e = ConversationType::IncomingTransferRequest;
    }
    else if (0 == j.get<std::string>().compare("OutgoingNetworkChangeHandover"))
    {
      e = ConversationType::OutgoingNetworkChangeHandover;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum ConversationType"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_CONVERSATIONTYPE_H
