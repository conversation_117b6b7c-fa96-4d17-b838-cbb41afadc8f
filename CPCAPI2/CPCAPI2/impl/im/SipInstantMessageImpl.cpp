#include "brand_branded.h"

#if (CPCAPI2_BRAND_IM_MODULE == 1)
#include "SipInstantMessageImpl.h"
#include "../util/buffer.h"

#include "cpcapi2utils.h"
#include "im/SipInstantMessageHandler.h"
#include "im/SipInstantMessage.h"
#include "../util/ResipConv.h"
#include "../iscomposing/IsComposingHelper.h"
#include "../analytics1/AnalyticsManagerInterface.h"

#include <resip/dum/AppDialogSet.hxx>
#include <resip/dum/ServerPagerMessage.hxx>
#include <resip/dum/ClientPagerMessage.hxx>
#include <resip/stack/OctetContents.hxx>
#include <resip/stack/GenericContents.hxx>
#include <resip/stack/ExtensionParameter.hxx>

#include <utility>

#include <utils/msrp_string.h>

#include "../util/cpc_logger.h"
#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_ACCOUNT

using namespace resip;

using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::IsComposing;

namespace CPCAPI2
{
namespace SipInstantMessage
{
SipInstantMessageImpl::SipInstantMessageImpl(SipInstantMessageInterface* iff, std::shared_ptr<InstantMessageMap> parentMap, PhoneInterface* cpcPhone, SipAccountImpl& account) :
   mInterface(iff),
   IsComposingManager(cpcPhone),
   mAccount(account),
   mNextHandle(account.getHandle() + 1),
   mParentMap(parentMap)
{
   mAccount.registerAccountAwareFeature(this);
}

SipInstantMessageImpl::~SipInstantMessageImpl()
{
   mAccount.unregisterAccountAwareFeature(this);
}

cpc::string getResipMimeAsString(const resip::Mime& mime)
{
   return cpc::string(mime.type().c_str()) + "/" + mime.subType().c_str();
}

resip::Mime getResipMimeTypeFrom(const cpc::string& mt)
{
   std::string mime(mt);
   std::string type;
   std::string subtype;
   const size_t splitIdx = mime.find("/");
   if (splitIdx != std::string::npos)
   {
      type = mime.substr(0, splitIdx);
      subtype = mime.substr(splitIdx+1);
   }
   else
   {
      // handle missing /, maybe we should error here instead
      type = mime;
      subtype = "";
   }
   return Mime(type.c_str(), subtype.c_str());
}

cpc::string getMimeTypeAsString(const SipInstantMessageManager::MimeType mt)
{
   switch (mt)
   {
   case SipInstantMessageManager::MimeType_TextHtml:
      return "text/html";
   case SipInstantMessageManager::MimeType_TextPlain:
      return "text/plain;charset=UTF-8";
   default:
      break;
   }
   return getResipMimeAsString(resip::OctetContents().getType());
}


int SipInstantMessageImpl::adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile)
{
   if (!profile->isMethodSupported(resip::MESSAGE)) profile->addSupportedMethod(resip::MESSAGE);
   profile->addSupportedMimeType(MESSAGE, Mime("text", "plain"));
   profile->addSupportedMimeType(MESSAGE, Mime("text", "html"));
   profile->addSupportedMimeType(MESSAGE, IsComposingHelper::IS_COMPOSING_CONTENT_TYPE);
   return kSuccess;
}

int SipInstantMessageImpl::registerSdkPagerMessageHandler(CPCAPI2::SipAccount::CPPagerMessageHandler& pagerMessageHandler)
{
   pagerMessageHandler.addDelegate(this);
   return kSuccess;
}

class SipInstantMessageAppDialogSet : public resip::AppDialogSet
{
public:
   SipInstantMessageAppDialogSet(resip::DialogUsageManager& dum) : resip::AppDialogSet(dum) {}
   virtual ~SipInstantMessageAppDialogSet() {}
};

class SipInstantMessageAppDialogFactoryDelegate : public CPCAPI2::SipAccount::AppDialogFactoryDelegate
{
public:
   SipInstantMessageAppDialogFactoryDelegate(SipInstantMessageImpl* parent) : parent(parent) {}
   virtual ~SipInstantMessageAppDialogFactoryDelegate() {}

   virtual bool isMyMessage(const resip::SipMessage& msg)
   {
      return (msg.isRequest() && msg.method() == resip::MESSAGE);
   }
   virtual resip::AppDialogSet* createAppDialogSet(resip::DialogUsageManager& dum, const resip::SipMessage& msg)
   {
      return new SipInstantMessageAppDialogSet(dum);
   }

private:
   SipInstantMessageImpl* parent;
};

int SipInstantMessageImpl::registerSdkDialogSetFactory(CPCAPI2::SipAccount::AppDialogSetFactory& factory)
{
   factory.addDelegate(std::shared_ptr<SipInstantMessageAppDialogFactoryDelegate>(new SipInstantMessageAppDialogFactoryDelegate(this)));
   return kSuccess;
}

int SipInstantMessageImpl::onDumBeingDestroyed()
{
   return kSuccess;
}

void SipInstantMessageImpl::release()
{
   if (std::shared_ptr<InstantMessageMap> parentMap = mParentMap.lock())
   {
      parentMap->erase(mAccount.getHandle());
   }

   for (std::map<resip::Uri, InstantMessageIsComposingInfo*>::iterator iter = mRemoteUriIsComposingInfoMap.begin(); iter != mRemoteUriIsComposingInfoMap.end(); iter++)
   {
      IsComposingInfo* currIsComposingInfo = iter->second;
      delete currIsComposingInfo; // also cancels any pending composing timers
   }
   mRemoteUriIsComposingInfoMap.clear();

   delete this;
}

int SipInstantMessageImpl::initialize()
{
   return kSuccess;
}

int SipInstantMessageImpl::acceptIncoming(SipInstantMessageHandle im)
{
   std::map<SipInstantMessageHandle, ServerPagerMessageHandle>::iterator it = mServerHandleMap.find(im);
   if (it != mServerHandleMap.end())
   {
      resip::SharedPtr<SipMessage> msg = it->second->accept();
      it->second->send(msg);
      mServerHandleMap.erase(it);
   }
   return kSuccess;
}

int SipInstantMessageImpl::rejectIncoming(SipInstantMessageHandle im, unsigned int responseCode)
{
   if (responseCode < 400 || responseCode > 699)
   {
      return kError;
   }

   std::map<SipInstantMessageHandle, ServerPagerMessageHandle>::iterator it = mServerHandleMap.find(im);
   if (it != mServerHandleMap.end())
   {
      resip::SharedPtr<SipMessage> msg = it->second->reject(responseCode);
      it->second->send(msg);
      mServerHandleMap.erase(it);
   }
   return kSuccess;
}

int SipInstantMessageImpl::rejectIncomingMimeType(SipInstantMessageHandle im, const cpc::vector<cpc::string>& acceptMimeTypes)
{
   std::map<SipInstantMessageHandle, ServerPagerMessageHandle>::iterator it = mServerHandleMap.find(im);
   if (it != mServerHandleMap.end())
   {
      resip::SharedPtr<SipMessage> msg = it->second->reject(415 /* unsupported media type */ );

      cpc::vector<cpc::string>::const_iterator itMimes = acceptMimeTypes.begin();
      for (; itMimes != acceptMimeTypes.end(); ++itMimes)
      {
         const cpc::string& mimeStr = *itMimes;
         resip::Mime mine = getResipMimeTypeFrom(mimeStr);
         msg->header(resip::h_Accepts).push_back(mine);
      }
      it->second->send(msg);
      mServerHandleMap.erase(it);
   }
   return kSuccess;
}

InstantMessageMimeType SipInstantMessageImpl::parseMimeType(cpc::string mimeType)
{
  InstantMessageMimeType paresedMimeType;
  size_t pos = 0, lastMatch = 0, assignment = 0;
  size_t size = mimeType.size();

  const char* str = mimeType.c_str();

  for (; pos < size; pos++)
  {
    if (str[pos] == ';')
    {
      paresedMimeType.type = mimeType.substr(lastMatch, pos - lastMatch);
      lastMatch = pos;
      DebugLog(<< "Parsed mimetype " << paresedMimeType.type);
      break;
    }
  }

  if (pos >= size)
  {
    DebugLog(<< "Parsed mimetype " << mimeType);
    paresedMimeType.type = mimeType;
    return paresedMimeType;
  }

  for (; pos < size; pos++)
  {
    if (str[pos] == ';')
    {
      if (assignment > lastMatch)
      {
        cpc::string param = mimeType.substr(lastMatch + 1, assignment - lastMatch - 1);
        cpc::string val = mimeType.substr(assignment + 1, pos - assignment - 1);
        paresedMimeType.parameters[param] = val;
        DebugLog(<< "Parsed a parameter for mimetype " << param << "=" << val);
      }
      lastMatch = pos;
    }
    else if (str[pos] == '=')
    {
      assignment = pos;
    }
  }

  if (lastMatch != pos)
  {
    if (assignment > lastMatch)
    {
      cpc::string param = mimeType.substr(lastMatch + 1, assignment - lastMatch - 1);
      cpc::string val = mimeType.substr(assignment + 1, pos - assignment - 1);
      paresedMimeType.parameters[param] = val;
      DebugLog(<< "Parsed a parameter for mimetype " << param << "=" << val);
    }
  }

  return paresedMimeType;
}

int SipInstantMessageImpl::acceptMimeTypeEnum(const SipInstantMessageManager::MimeType mimeType)
{
   cpc::string type = getMimeTypeAsString(mimeType);
   acceptMimeType(type);
   return kSuccess;
}

int SipInstantMessageImpl::acceptMimeType(const cpc::string& mimeType)
{
   mAcceptableMimeTypesStrings.push_back(mimeType);
   mAcceptableMimeTypes.push_back(parseMimeType(mimeType));
   return kSuccess;
}

void SipInstantMessageImpl::fireError(const SipInstantMessageHandle& h, const cpc::string& errorText)
{
   ErrorEvent event;
   event.errorText = errorText;
   mInterface->fireEvent(cpcEvent(SipInstantMessageHandler, onError), h, event);
}

int SipInstantMessageImpl::sendOutgoingMessage(
      SipInstantMessageHandle h,
      cpc::string targetAddress,
      const resip::Data& content,
      SipInstantMessageManager::MimeType mimeType,
      const cpc::string& messageType)
{
   cpc::string contentType = getMimeTypeAsString(mimeType);
   return sendOutgoingMessage(h, targetAddress, content, contentType, messageType);
}

int SipInstantMessageImpl::sendOutgoingMessage(
      SipInstantMessageHandle h,
      cpc::string targetAddress,
      const resip::Data& content,
      const cpc::string& mimeType,
      const cpc::string& messageType)
{
   if (isDumShutdown())
   {
      fireError(h, "Cannot send IM. Account is disabled.");
      return kSuccess;
   }

   // Create the To address
   NameAddr naTarget;
   if (!ResipConv::stringToAddr(targetAddress, naTarget))
   {
      fireError(h, "Invalid targetAddress for outgoing message '" + targetAddress + "'");
      return kSuccess;
   }

   // transform address if transformer defined
   AddressTransformer* transformer = getAddressTransformer();
   if(transformer != NULL)
   {
      cpc::string transformedAddress = "";
      AddressTransformationContext context;
      context.addressUsageType = AddressUsageType_SipChat;
      context.registrationDomain = mAccount.getSettings().domain;
      if(transformer->applyTransformation(targetAddress, context, transformedAddress) == kSuccess)
      {
         ResipConv::stringToAddr(transformedAddress, naTarget);
      }
   }

   // Add the message type, if specified, as a parameter to the To address
   if (!messageType.empty())
   {
      resip::ExtensionParameter messageTypeParam("messagetype");
      naTarget.param(messageTypeParam) = resip::Data::from(messageType);
   }

   // Create the content to attach to the outgoing message
   resip::Mime contentType = getResipMimeTypeFrom(mimeType);
   std::unique_ptr<resip::Contents> customContents(new resip::GenericContents(content, contentType));

   // Create the message with content and send
   ClientPagerMessageHandle cpmh = mAccount.getDUM()->makePagerMessage(naTarget);
   cpmh->page(std::move(customContents));

   // Keep the relationship message handle -> client transaction for callbacks
   mClientHandleMap[h] = cpmh;

   // Update IsComposing state - IM sent
   InstantMessageIsComposingInfo* isComposingInfo = getIsComposingInfo(naTarget, false);
   if (isComposingInfo)
   {
      IsComposingManager::setMessageSent(isComposingInfo);
   }

   return kSuccess;
}

void SipInstantMessageImpl::onMessageArrived(ServerPagerMessageHandle h, const SipMessage& message)
{
   // Process incoming message based on the type of message received
   if (isIsComposingMessageReceived(message))
   {
      // Incoming IsComposing notification
      onIsComposingMessageArrived(h, message);
   }
   else
   {
      // Incoming instant message
      onInstantMessageArrived(h, message);
   }
}

void SipInstantMessageImpl::onSuccess(ClientPagerMessageHandle h, const SipMessage& status)
{
   // Process success based on the type of message sent
   if (isIsComposingNotificationSent(h))
   {
      // IsComposing notification sent
      onSetIsComposingSuccess(h, status);
   }
   else
   {
      // Instant message sent
      onSendInstantMessageSuccess(h, status);
   }
}

void SipInstantMessageImpl::onFailure(ClientPagerMessageHandle h, const SipMessage& status, std::unique_ptr<Contents> contents)
{
   // Process failure based on the type of message sent
   if (isIsComposingNotificationSent(h))
   {
      // IsComposing notification sent
      onSetIsComposingFailure(h, status);
   }
   else
   {
      // Instant message sent
      onSendInstantMessageFailure(h, status);
   }
}

void SipInstantMessageImpl::onIsComposingMessageArrived(ServerPagerMessageHandle h, const SipMessage& message)
{
   // Get the content of the incoming message
   //int contentLength = message.header(h_ContentLength).value();
   resip::Data messageContentBytes(message.getContents()->getBodyData());
   cpc::string messageContent = messageContentBytes.c_str();

   // Retrieve the notification
   IsComposingDocument *isComposingDocument = IsComposingDocument::parse(messageContent);

   if(isComposingDocument != NULL)
   {
      // Process the notification
      NameAddr fromNameAddr = message.header(h_From);
      InstantMessageIsComposingInfo* isComposingInfo = getIsComposingInfo(fromNameAddr, true);
      IsComposingManager::processIsComposingMessageNotification(isComposingInfo, *isComposingDocument);

      // Send a 200/OK back
      resip::SharedPtr<SipMessage> response = h->accept();
      h->send(response);

      delete isComposingDocument;
   }
   else
   {
      // Send a 400 back
      resip::SharedPtr<SipMessage> response = h->reject(400);
      h->send(response);
   }
}

void SipInstantMessageImpl::onInstantMessageArrived(ServerPagerMessageHandle h, const SipMessage& message)
{
   if (!mInterface->hasHandlerRegistered())
   {
      WarningLog( << "No handler set");

      resip::SharedPtr<SipMessage> msg = h->reject(488);
      h->send(msg);
   }

   SipIncomingInstantMessageEvent args;
   args.account = mAccount.getHandle();
   args.im = mNextHandle++;
   //int contentLength = message.header(h_ContentLength).value();
   if (message.getContents() != NULL)
   {
      resip::Data messageBodyData(message.getContents()->getBodyData());
      // !jjg! when we update to a newer resip, we use the following instead:
      //resip::Data messageBodyData(Data::Borrow);
      //message.getRawBody()->toBorrowData(messageBodyData);
      args.content = messageBodyData.c_str();
   }
   Data mimeType;
   {
      DataStream ds(mimeType);
      ds << message.header(h_ContentType).type() << "/" << message.header(h_ContentType).subType();
   }
   args.mimeType = mimeType.c_str();
   NameAddr toAddr = message.header(h_To);
   args.to.address = Data::from(toAddr.uri()).c_str();
   args.to.displayName = Data::from(message.header(h_To).displayName()).c_str();

   NameAddr remoteNameAddr;
   if (mAccount.getSettings().preferPAssertedIdentity && message.exists(h_PAssertedIdentities) && !message.header(h_PAssertedIdentities).empty())
   {
      NameAddrs remoteNameAddrs = message.header(h_PAssertedIdentities);
      remoteNameAddr = remoteNameAddrs.front();
   }
   else
   {
      remoteNameAddr = message.header(h_From);
   }
   args.from.address = Data::from(remoteNameAddr.uri()).c_str();
   args.from.displayName = Data::from(remoteNameAddr.displayName()).c_str();

   resip::ExtensionParameter messageTypeParam("messagetype");
   if (toAddr.exists(messageTypeParam))
   {
      args.messageType = toAddr.param(messageTypeParam).c_str();
   }
   mServerHandleMap[args.im] = h;

   // acceptable list is not empty (which means allow all), so we need to check that mime type is valid
   if (!mAcceptableMimeTypes.empty() && !isMimeTypeAcceptable(args.mimeType))
   {
      // not an acceptable mime type
      rejectIncomingMimeType(args.im, mAcceptableMimeTypesStrings);
      return;
   }

   if (!message.getRawUnknownHeaders().empty())
   {
      SipMessage::UnknownHeaders::const_iterator it = message.getRawUnknownHeaders().begin();
      for (; it != message.getRawUnknownHeaders().end(); ++it)
      {
         CPCAPI2::SipHeader header;
         header.header = it->first.c_str();
         header.value = it->second->front()->getBuffer();
         args.nonStandardHeaders.push_back(header);
      }
   }

   // Update IsComposing state - IM received
   NameAddr fromNameAddr = message.header(h_From);
   InstantMessageIsComposingInfo* isComposingInfo = getIsComposingInfo(fromNameAddr, false);
   if (isComposingInfo)
   {
      IsComposingManager::setMessageReceived(isComposingInfo);
   }

   // Notify that an instant message was received
   mInterface->fireEvent(cpcEvent(SipInstantMessageHandler, onIncomingInstantMessage), mAccount.getHandle(), args);

    #if (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)
       //Send data to Analytics (UEM) module as well
       static_cast<Analytics::AnalyticsManagerInt*>(Analytics::AnalyticsManager::getInterface(mAccount.getPhone()))->instantMessageInfoFired(mAccount.getHandle(), true, true);
    #endif
}

bool SipInstantMessageImpl::isMimeTypeAcceptable(cpc::string mimeType)
{
  // acceptable list is not empty (which means allow all), so we need to check that mime type is valid
  if (mAcceptableMimeTypes.empty())
    return true;

  InstantMessageMimeType incomingType = parseMimeType(mimeType);

  for(cpc::vector<InstantMessageMimeType>::const_iterator it = mAcceptableMimeTypes.begin(); it != mAcceptableMimeTypes.end(); ++it)
  {
    if (incomingType.type == it->type)
    {
      // Check the parameters from the incoming. If a parameter doesn't exist in one, ignore it.
      for(std::map<cpc::string, cpc::string>::const_iterator p = incomingType.parameters.begin(); p != incomingType.parameters.end(); ++p)
      {
        std::map<cpc::string, cpc::string>::const_iterator p2 = it->parameters.find(p->first);
        if (p2 != it->parameters.end() && p2->second != p->second)
        {
          // Parameter doesn't match so don't accept
          continue;
        }
      }

      return true;
    }
  }

  return false;
}

void SipInstantMessageImpl::onSetIsComposingSuccess(ClientPagerMessageHandle h, const SipMessage& status)
{
   // Get the associated state
   assert(isIsComposingNotificationSent(h));
   IsComposingMessageState state = mClientHandleIsComposingStateMap[h];

   // Create the event
   SetIsComposingMessageSuccessEvent event;
   event.state = state;

   mInterface->fireEvent(cpcEvent(SipInstantMessageHandler, onSetIsComposingMessageSuccess), mAccount.getHandle(), event);

   h->end();
   // Remove from map
   mClientHandleIsComposingStateMap.erase(h);
}

void SipInstantMessageImpl::onSetIsComposingFailure(ClientPagerMessageHandle h, const SipMessage& status)
{
   // Create the event
   SetIsComposingMessageFailureEvent event;

   // Notify of the successful call to setIsComposingMessage(...)
   mInterface->fireEvent(cpcEvent(SipInstantMessageHandler, onSetIsComposingMessageFailure), mAccount.getHandle(), event);

   h->end();
   // Remove from map
   mClientHandleIsComposingStateMap.erase(h);

   // Destroy the info object
   resip::NameAddr toNameAddr = status.header(h_To);
   InstantMessageIsComposingInfo* isComposingInfo = getIsComposingInfo(toNameAddr, false);
   if (isComposingInfo)
   {
      removeIsComposingInfo(isComposingInfo);
   }
}

void SipInstantMessageImpl::onSendInstantMessageSuccess(ClientPagerMessageHandle h, const SipMessage& status)
{
   SipOutgoingInstantMessageEvent args;
   args.im = getSdkHandleFrom(h);
   args.signalingResponseText = status.header(h_StatusLine).reason().c_str();
   args.signalingStatusCode = status.header(h_StatusLine).responseCode();
   mInterface->fireEvent(cpcEvent(SipInstantMessageHandler, onOutgoingInstantMessageSuccess), mAccount.getHandle(), args);

   h->end();
   mClientHandleMap.erase(args.im);

    #if (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)
       //Send data to Analytics (UEM) module as well
       static_cast<Analytics::AnalyticsManagerInt*>(Analytics::AnalyticsManager::getInterface(mAccount.getPhone()))->instantMessageInfoFired(mAccount.getHandle(), false, true);
    #endif
}

void SipInstantMessageImpl::onSendInstantMessageFailure(ClientPagerMessageHandle h, const SipMessage& status)
{
   SipOutgoingInstantMessageEvent args;
   args.im = getSdkHandleFrom(h);
   args.signalingResponseText = status.header(h_StatusLine).reason().c_str();
   args.signalingStatusCode = status.header(h_StatusLine).responseCode();
   if (status.exists(h_Accepts))
   {
      const resip::H_Accepts::Type accepts = status.header(h_Accepts);
      resip::H_Accepts::Type::const_iterator it = accepts.begin();
      for(; it != accepts.end(); it++)
      {
         cpc::string acceptMime = getResipMimeAsString(*it);
         args.headers.acceptableMimeTypes.push_back(acceptMime);
      }
   }
   mInterface->fireEvent(cpcEvent(SipInstantMessageHandler, onOutgoingInstantMessageFailure), mAccount.getHandle(), args);
   h->end();
   mClientHandleMap.erase(args.im);
}

bool SipInstantMessageImpl::isMyResponse(resip::ClientPagerMessageHandle h, const resip::SipMessage& msg)
{
   // Check that this service has seen the handle specified
   return getSdkHandleFrom(h) != 0xffffffff || isIsComposingNotificationSent(h);
}

int SipInstantMessageImpl::setIsComposingMessage(const cpc::string& targetAddress, SipInstantMessageManager::MimeType mimeType, struct std::tm datetime, int refreshInterval, int idleInterval)
{
   // Get the content type
   cpc::string mimeTypeStr = getMimeTypeAsString(mimeType);
   resip::Mime contentType = getResipMimeTypeFrom(mimeTypeStr);

   // Indicate that the user is actively composing text
   return setIsComposingMessage(targetAddress, contentType, datetime, refreshInterval, idleInterval);
}

int SipInstantMessageImpl::setIsComposingMessage(const cpc::string& targetAddress, const cpc::string& customMimeType, struct std::tm datetime, int refreshInterval, int idleInterval)
{
   // Get the content type
   resip::Mime contentType = getResipMimeTypeFrom(customMimeType);

   // Indicate that the user is actively composing text
   return setIsComposingMessage(targetAddress, contentType, datetime, refreshInterval, idleInterval);
}

int SipInstantMessageImpl::setIsComposingMessage(const cpc::string& targetAddress, const resip::Mime& contentType, struct std::tm datetime, int refreshInterval, int idleInterval)
{
   // Create the target URI
   NameAddr targetNameAddr;
   ResipConv::stringToAddr(targetAddress, targetNameAddr);

   // Get the associated info object
   InstantMessageIsComposingInfo* isComposingInfo = getIsComposingInfo(targetNameAddr, true);

   // Indicate that the user is actively composing text
   IsComposingManager::setIsComposingMessage(isComposingInfo, contentType, datetime, refreshInterval, idleInterval);

   return kSuccess;
}

void SipInstantMessageImpl::sendIsComposingMessageNotification(IsComposingInfo* info, IsComposingMessageState state, const resip::Mime& contentType, int refreshInterval, const tm& lastActive)
{
   if (isDumShutdown())
   {
      return;
   }

   // Get the info object
   InstantMessageIsComposingInfo* isComposingInfo = dynamic_cast<InstantMessageIsComposingInfo*>(info);
   assert(info);

   // Keep the state
   isComposingInfo->state = state;

   // Create the notification
   IsComposingDocument isComposingDocument = IsComposingManager::createIsComposingMessageNotification(state, contentType, refreshInterval, lastActive);

   // Convert the notification to a byte array
   cpc::string messageContent = isComposingDocument.toString();
   resip::Data messageContentBytes = messageContent.c_str();

   // Send the notification
   std::unique_ptr<resip::Contents> customContents(new resip::GenericContents(messageContentBytes, IsComposingHelper::IS_COMPOSING_CONTENT_TYPE));
   resip::SharedPtr<resip::DialogUsageManager> dum = mAccount.getDUM();
	ClientPagerMessageHandle cpmh;
	if(dum != NULL)
	{
		cpmh = dum->makePagerMessage(isComposingInfo->remoteNameAddr);
      cpmh->page(std::move(customContents));
	}
	else
	{
		return;
	}

   // Keep the relationhip handle->state so that the associated callbacks have access to that information
   mClientHandleIsComposingStateMap[cpmh] = state;
}

void SipInstantMessageImpl::onIsComposingMessage(IsComposingInfo* info, IsComposingMessageState state, const resip::Mime& contentType, const tm& lastActive)
{
   // Get the info object
   InstantMessageIsComposingInfo* isComposingInfo = dynamic_cast<InstantMessageIsComposingInfo*>(info);
   assert(info);

   // Create the event
   IsComposingMessageEvent event;
   event.from = CpmHelper::resipNameAddrToNameAddr(isComposingInfo->remoteNameAddr);
   event.to = CpmHelper::resipNameAddrToNameAddr(IsComposingHelper::getNameAddr(mAccount));
   event.state = state;
   event.mimeType = getResipMimeAsString(contentType);
   event.lastActive = lastActive;

   // Notify of an IM message being composed
   mInterface->fireEvent(cpcEvent(SipInstantMessageHandler, onIsComposingMessage), mAccount.getHandle(), event);
}

void SipInstantMessageImpl::backToComposerIdleState(IsComposingInfo* info)
{
   if (info->receiverState == &IsComposing::ReceiverIdleState::instance)
   {
      // This is a good time to remove the info object from the map
      removeIsComposingInfo(info);
   }
}

void SipInstantMessageImpl::backToReceiverIdleState(IsComposingInfo* info)
{
   if (info->composerState == &IsComposing::ComposerIdleState::instance)
   {
      // This is a good time to remove the info object from the map
      removeIsComposingInfo(info);
   }
}

bool SipInstantMessageImpl::isIsComposingMessageReceived(const SipMessage& message)
{
   return message.header(h_ContentType) == IsComposingHelper::IS_COMPOSING_CONTENT_TYPE;
}

bool SipInstantMessageImpl::isIsComposingNotificationSent(ClientPagerMessageHandle h)
{
   return mClientHandleIsComposingStateMap.find(h) != mClientHandleIsComposingStateMap.end();
}

InstantMessageIsComposingInfo* SipInstantMessageImpl::getIsComposingInfo(const resip::NameAddr& remoteNameAddr, bool createInfo)
{
   InstantMessageIsComposingInfo* isComposingInfo = mRemoteUriIsComposingInfoMap[remoteNameAddr.uri()];
   if (!isComposingInfo && createInfo)
   {
      // Create the info and assign its remote address
      isComposingInfo = new InstantMessageIsComposingInfo(); // Destroyed in removeIsComposingInfo()
      isComposingInfo->remoteNameAddr = remoteNameAddr;
      isComposingInfo->remoteNameAddr.remove(p_tag);

      // Initialize its composing related states
      IsComposingManager::initialize(isComposingInfo);

      // Store info object in the map
      mRemoteUriIsComposingInfoMap[remoteNameAddr.uri()] = isComposingInfo;
   }

   return isComposingInfo;
}

void SipInstantMessageImpl::removeIsComposingInfo(IsComposingInfo* isComposingInfo)
{
   // Scan the map to find the info object specified
   for (std::map<resip::Uri, InstantMessageIsComposingInfo*>::iterator iter = mRemoteUriIsComposingInfoMap.begin(); iter != mRemoteUriIsComposingInfoMap.end(); iter++)
   {
      resip::Uri currTargetUri = iter->first;
      IsComposingInfo* currIsComposingInfo = iter->second;

      // Check if the info object we are looking for has been found
      if (currIsComposingInfo == isComposingInfo)
      {
         // Remove uri->info from the map
         mRemoteUriIsComposingInfoMap.erase(currTargetUri);

         // Destroy the info object
         delete currIsComposingInfo;
         break;
      }
   }
}

SipInstantMessageHandle SipInstantMessageImpl::getSdkHandleFrom(resip::ClientPagerMessageHandle h)
{
   std::map<SipInstantMessageHandle, resip::ClientPagerMessageHandle>::iterator it = mClientHandleMap.begin();
   for (; it != mClientHandleMap.end(); ++it)
   {
      if (it->second == h)
         return it->first;
   }
   return 0xffffffff;
}

bool SipInstantMessageImpl::isMyMessage(const resip::SipMessage& msg)
{
   return true;
}

bool SipInstantMessageImpl::hasIncomingInstantMessage(SipInstantMessageHandle im) const
{
   return mServerHandleMap.find(im) != mServerHandleMap.end();
}

}
}

#endif // CPCAPI2_BRAND_IM_MODULE
