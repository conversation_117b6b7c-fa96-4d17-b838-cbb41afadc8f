#include "interface/experimental/jsonrpc/StringSerialization.h"
#include "interface/experimental/jsonrpc/OptionalSerialization.h"
#include "interface/experimental/jsonrpc/VectorSerialization.h"
#include "../datatypes/LicenseInfo.h"
#include "../datatypes/SslCipherOptions.h"
#include "../datatypes/LogLevel.h"
#include "../datatypes/PhoneEvent.h"

#ifndef JSONRPC_CPCAPI2_PHONE_IPHONE_H
#define JSONRPC_CPCAPI2_PHONE_IPHONE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
class IPhone
{
public:
    virtual int64_t create() = 0;

	virtual void release(const int64_t handle) = 0;

	virtual cpc::string getVersion() = 0;

	virtual void initialize(const int64_t handle, const LicenseInfo& licenseInfo, const bool useNetworkChangeManager, const std::optional<SslCipherOptions>& ciphers) = 0;

	virtual void setLoggingEnabled(const int64_t handle, const bool enabled) = 0;

	virtual void setFileLoggingEnabled(const int64_t handle, const bool enabled, const std::optional<cpc::string>& id) = 0;

	virtual void setLogDirectory(const int64_t handle, const cpc::string& directory) = 0;

	virtual void setLogLevel(const int64_t handle, const LogLevel level) = 0;

	virtual cpc::string getInstanceId(const int64_t handle) = 0;

	virtual int64_t createEventQueue(const int64_t handle, const std::optional<cpc::vector<cpc::string>>& events) = 0;

	virtual void destroyEventQueue(const int64_t handle, const int64_t eventQueueHandle) = 0;

	virtual PhoneEvent getEvent(const int64_t handle, const int64_t eventQueueHandle, const int64_t timeout) = 0;
}; // class Phone
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_IPHONE_H
