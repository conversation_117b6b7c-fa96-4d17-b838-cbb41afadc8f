#include "CPPagerMessageHandler.h"

namespace CPCAPI2
{
namespace SipAccount
{

void CPPagerMessageHandler::onMessageArrived(resip::ServerPagerMessageHandle h, const resip::SipMessage& msg)
{
   for (std::list<CPPagerMessageHandlerDelegatePtr>::iterator it = delegates.begin() ; it != delegates.end() ; ++it)
   {
      CPPagerMessageHandlerDelegatePtr delegate = *it;

      if (delegate->isMyMessage(msg))
         return delegate->onMessageArrived(h, msg);
   }
}

void CPPagerMessageHandler::onSuccess(resip::ClientPagerMessageHandle h, const resip::SipMessage& status)
{
   for (std::list<CPPagerMessageHandlerDelegatePtr>::iterator it = delegates.begin() ; it != delegates.end() ; ++it)
   {
      CPPagerMessageHandlerDelegatePtr delegate = *it;

      if (delegate->isMyResponse(h, status))
         return delegate->onSuccess(h, status);
   }
}

void CPPagerMessageHandler::onFailure(resip::ClientPagerMessageHandle h, const resip::SipMessage& status, std::unique_ptr<resip::Contents> contents)
{
   for (std::list<CPPagerMessageHandlerDelegatePtr>::iterator it = delegates.begin() ; it != delegates.end() ; ++it)
   {
      CPPagerMessageHandlerDelegatePtr delegate = *it;

      if (delegate->isMyResponse(h, status))
         return delegate->onFailure(h, status, std::move(contents));
   }
}

void CPPagerMessageHandler::addDelegate(CPPagerMessageHandlerDelegatePtr delegate, bool addAtEnd)
{
   if (std::find(delegates.begin(), delegates.end(), delegate) != delegates.end())
   {
      return;
   }

   if (addAtEnd)
      delegates.push_back(delegate);
   else
      delegates.push_front(delegate);
}
   
void CPPagerMessageHandler::clearDelegates()
{
   delegates.clear();
}

}
}
