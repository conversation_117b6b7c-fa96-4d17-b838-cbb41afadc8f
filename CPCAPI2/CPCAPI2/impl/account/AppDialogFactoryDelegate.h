#pragma once

#ifndef __CPCAPI2_APP_DIALOG_FACTORY_DELEGATE_H__
#define __CPCAPI2_APP_DIALOG_FACTORY_DELEGATE_H__

#include "resip/dum/AppDialogSetFactory.hxx"

#include <memory>

// fwd decl
namespace resip
{
   class SipMessage;
}

namespace CPCAPI2
{
   namespace SipAccount
   {
      class AppDialogFactoryDelegate : public resip::AppDialogSetFactory
      {
      public:
         AppDialogFactoryDelegate() {};
         virtual ~AppDialogFactoryDelegate() {};

         // returns true if the message should be handled by this delegate
         virtual bool isMyMessage( const resip::SipMessage& msg ) = 0;
      };
      typedef std::shared_ptr< AppDialogFactoryDelegate > AppDialogFactoryDelegatePtr;
   }
}

#endif // __CPCAPI2_APP_DIALOG_FACTORY_DELEGATE_H__
