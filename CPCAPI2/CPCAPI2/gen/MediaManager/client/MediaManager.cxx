#include "../datatypes/MediaManagerInitializeMediaStack.h"

namespace jsonrpc {
namespace CPCAPI2 {
namespace MediaManager {
void MediaManager::initializeMediaStack(int64_t phoneHandle)
{
    MediaManagerInitializeMediaStack params = new MediaManagerInitializeMediaStack(phoneHandle);
    nlohmann::json j = this.transport.request("initializeMediaStack", params.marshal());
    
};
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace MediaManager