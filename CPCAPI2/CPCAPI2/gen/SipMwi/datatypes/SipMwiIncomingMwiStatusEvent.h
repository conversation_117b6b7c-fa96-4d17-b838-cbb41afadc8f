#include "jsonrpc/OptionalSerialization.h"
#include "jsonrpc/VectorSerialization.h"
#include <nlohmann/json.hpp>
#include "SipMwiMessageWaitingItem.h"

#ifndef JSONRPC_CPCAPI2_SIPMWI_SIPMWIINCOMINGMWISTATUSEVENT_H
#define JSONRPC_CPCAPI2_SIPMWI_SIPMWIINCOMINGMWISTATUSEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipMwi {
  class SipMwiIncomingMwiStatusEvent
  {
  public:
    std::optional<int32_t> mwiSubscriptionHandle;
    std::optional<bool> hasMessages;
    std::optional<int32_t> sipAccountHandle;
    std::optional<cpc::vector<SipMwiMessageWaitingItem>> items;

    SipMwiIncomingMwiStatusEvent() = default;

    SipMwiIncomingMwiStatusEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipMwiIncomingMwiStatusEvent& o)
    {
      j = nlohmann::json::object();
       if (o.mwiSubscriptionHandle) { j["mwiSubscriptionHandle"] = *(o.mwiSubscriptionHandle); }
     if (o.hasMessages) { j["hasMessages"] = *(o.hasMessages); }
     if (o.sipAccountHandle) { j["sipAccountHandle"] = *(o.sipAccountHandle); }
     if (o.items) { j["items"] = *(o.items); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipMwiIncomingMwiStatusEvent& o)
    {
     if (j.contains("mwiSubscriptionHandle")) { o.mwiSubscriptionHandle = j.at("mwiSubscriptionHandle"); }
     if (j.contains("hasMessages")) { o.hasMessages = j.at("hasMessages"); }
     if (j.contains("sipAccountHandle")) { o.sipAccountHandle = j.at("sipAccountHandle"); }
     if (j.contains("items")) { o.items = j.at("items"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipMwi
#endif // JSONRPC_CPCAPI2_SIPMWI_SIPMWIINCOMINGMWISTATUSEVENT_H
