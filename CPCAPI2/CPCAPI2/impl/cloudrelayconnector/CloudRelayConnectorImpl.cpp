#include "brand_branded.h"

#if (CPCAPI2_BRAND_CLOUD_RELAY_CONNECTOR_MODULE == 1)
#include "CloudRelayConnectorImpl.h"
#include "CloudRelayConnectorInterface.h"
#include "cloudconnector/CloudServerConnection.h"
#include "cloudconnector/ServiceDesc.h"
#include "cloudrelayconnector/CloudRelayConnectorHandler.h"
#include "phone/PhoneInterface.h"
#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"
#include "util/DeviceInfo.h"
#include "auth_server/AuthServerJwtUtils.h"
#include "cloudrelay/jsonapi/CloudRelayJsonProxyInterface.h"
#include "peerconnection/PeerConnectionManagerInterface.h"
#include "media/VideoInterface.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>
#include <sstream>

#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::CONF_CONNECTOR
#define AUTH_TIMEOUT_SECONDS 5
#define ORCH_TIMEOUT_SECONDS 5

using CPCAPI2::CloudConnector::CloudServerConnection;
using namespace CPCAPI2::CloudRelay;
using namespace CPCAPI2::PeerConnection;

namespace CPCAPI2
{
namespace CloudRelayConnector
{
CloudRelayConnectorImpl::CloudRelayConnectorImpl(CloudRelayConnectorInterface* ccif, CloudRelayConnectorHandle h)
   : mInterface(ccif),
     mHandle(h),
     mWebRequestThread("CloudRelayConnectorImpl"),
     mConfPhone(NULL),
     mConfBridgeMgr(NULL)
{
   StackLog(<< "CloudRelayConnectorImpl::CloudRelayConnectorImpl(): " << this << " handle: " << mHandle);
   mWebRequestThread.start();

}

CloudRelayConnectorImpl::~CloudRelayConnectorImpl()
{
   StackLog(<< "CloudRelayConnectorImpl::~CloudRelayConnectorImpl(): " << this << " handle: " << mHandle);

   mWebRequestThread.stop();
   mWebRequestThread.join();
}

int CloudRelayConnectorImpl::destroy()
{
   disconnectService();
   release();

   return kSuccess;
}

int CloudRelayConnectorImpl::setConnectionSettings(const CloudRelayConnectorSettings& settings)
{
   mSettings = settings;
   return kSuccess;
}

int CloudRelayConnectorImpl::requestService()
{
   return kSuccess;
}

class ConfBridgeRequestor
{
public:
   ConfBridgeRequestor(resip::MultiReactor& requestServiceThread, resip::MultiReactor& callbackThread, const resip::Data& joinUrl, const resip::Data& authToken, bool ignoreCertVerification, SslCipherOptions tlsSettings, const std::function<void(int, const std::string)>& resultCb)
      : mReactor(requestServiceThread),
      mCallbackThread(callbackThread),
      mJoinUrl(joinUrl),
      mAuthToken(authToken),
      mIgnoreCertVerification(ignoreCertVerification), 
      mTlsSettings(tlsSettings),
      mResultCb(resultCb)
   {
   }
   ~ConfBridgeRequestor()
   {
   }

   void start()
   {
      mReactor.post(resip::resip_bind(&ConfBridgeRequestor::doGetConferenceWsUrlFlow, this));
   }

private:
   void doGetConferenceWsUrlFlow()
   {
      std::unique_ptr<ConfBridgeRequestor> thisDeleter(this);
      std::string websocketUrl;
      try
      {
         /* now hit orchestration server */
         CurlPPHelper helper;
         curlpp::Easy cloudrelayRequest;
         //std::string orchMessageBody = "{"
         //   "\"moduleId\":\"OrchestrationServer\","
         //   "\"functionObject\" : {"
         //   "\"functionName\":\"requestService\","
         //   "   \"serviceRequests\" : [{"
         //   "      \"service\" : \"xmppagent\","
         //   "      \"region\" : \"NA\""
         //   "}]"
         //   "}"
         //   "}";

         std::string orchServerUrl(mJoinUrl.c_str(), mJoinUrl.size());

         helper.setDefaultOptions(cloudrelayRequest, orchServerUrl, "POST");
         helper.setTimeoutOption(cloudrelayRequest, ORCH_TIMEOUT_SECONDS);

         std::list<std::string> header;
         header.push_back(std::string("Authorization: bearer ") + mAuthToken.c_str());
         cloudrelayRequest.setOpt(new curlpp::options::HttpHeader(header));

         int acceptableFailures( 0 );
         if( mIgnoreCertVerification )
            acceptableFailures = ( int ) CurlPPSSL::E_CERT_WHATEVER_ERROR;

         CurlPPSSL cssl( mTlsSettings, acceptableFailures );
         cloudrelayRequest.setOpt(new curlpp::options::SslCtxFunction(cssl));

         std::stringstream responseBody;
         cloudrelayRequest.setOpt(new curlpp::options::WriteStream(&responseBody));
         cloudrelayRequest.perform();

         int response = curlpp::infos::ResponseCode::get(cloudrelayRequest);

         StackLog(<< "CloudRelayConnectorImpl::doGetConferenceWsUrlFlow(): response: " << responseBody.str());
         if (response == 200)
         {
            std::unique_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
            jsonRequest->Parse<0>(responseBody.str().c_str());
            if (jsonRequest->HasParseError())
            {
               postFailureCallback();
               return;
            }

            if (!jsonRequest->HasMember("websocketUrl"))
            {
               postFailureCallback();
               return;
            }

            const rapidjson::Value& websocketUrlVal = (*jsonRequest)["websocketUrl"];
            if (!websocketUrlVal.IsString())
            {
               postFailureCallback();
               return;
            }

            websocketUrl = websocketUrlVal.GetString();
            assert(!websocketUrl.empty());

            resip::StdFunctionReadCallback2<std::function<void(int, const std::string&)>, int, std::string >* cb = new resip::StdFunctionReadCallback2<std::function<void(int, const std::string&)>, int, std::string >(mResultCb, 0, websocketUrl);
            mCallbackThread.post(cb);
         }
         else
         {
            postFailureCallback();
         }
      }
      catch (curlpp::RuntimeError& e)
      {
         postFailureCallback();
      }
   }

   void postFailureCallback()
   {
      resip::StdFunctionReadCallback2<std::function<void(int, const std::string&)>, int, std::string >* cb = new resip::StdFunctionReadCallback2<std::function<void(int, const std::string&)>, int, std::string >(mResultCb, -1, "");
      mCallbackThread.post(cb);
   }

private:
   resip::MultiReactor& mReactor;
   resip::MultiReactor& mCallbackThread;
   resip::Data mJoinUrl;
   resip::Data mAuthToken;
   bool mIgnoreCertVerification;
   SslCipherOptions mTlsSettings;
   std::function<void(int, const std::string&)> mResultCb;
};

int CloudRelayConnectorImpl::doFirstTimeConnect(const resip::Data& authToken, const CPCAPI2::CloudConnector::ServiceDesc& cloudrelayServiceDesc)
{
   std::weak_ptr<CloudRelayConnectorImpl> weakThis(shared_from_this());
   std::vector<CPCAPI2::CloudConnector::ServiceDesc> serviceRequests;
   serviceRequests.push_back(cloudrelayServiceDesc);
   CloudServerConnection::doOrchestrationFlow(mWebRequestThread,
      mInterface->getThisSdkPhone()->getSdkModuleThread(),
      serviceRequests,
      mSettings.orchestrationServerUrl.c_str(),
      authToken,
      mSettings.ignoreCertVerification,
      mInterface->getThisSdkPhone()->getSslCipherOptions(),
      [weakThis, authToken, cloudrelayServiceDesc](int orchResultCode, const std::vector<CPCAPI2::CloudConnector::ServiceDescUrl>& serviceUrls, const cpc::string& details) {
      if (std::shared_ptr<CloudRelayConnectorImpl> thisPtr = weakThis.lock())
      {
         if (serviceUrls.size() > 0)
         {
            if (thisPtr->mConfServiceConn.get() == NULL)
            {
               std::shared_ptr<CloudServerConnection> serverConn(new CloudServerConnection(thisPtr.get()));
               serverConn->initialize(thisPtr->mInterface->getThisSdkPhone());
               serverConn->addService(cloudrelayServiceDesc);
               thisPtr->mConfServiceConn = serverConn;
            }
            else
            {
               thisPtr->mConfServiceConn->addService(cloudrelayServiceDesc);
            }

            thisPtr->mConfServiceConn->connect(serviceUrls.front().url, authToken.c_str(), thisPtr->mSettings.ignoreCertVerification);
         }
         else
         {
            ServiceConnectionStatusEvent evt;
            evt.connectionStatus = ServiceConnectionStatus_ConnFailure;
            evt.serverUri = thisPtr->mSettings.orchestrationServerUrl;
            evt.statusDesc = details;
            thisPtr->mInterface->fireConnStatusEvent(thisPtr->mHandle, evt);
         }
      }
   });
   return kSuccess;
}

int CloudRelayConnectorImpl::connectToServices()
{
   if (mSettings.username.size() == 0 || mSettings.authServerUrl.size() == 0)
   {
      ServiceConnectionStatusEvent evt;
      evt.connectionStatus = ServiceConnectionStatus_ConnFailure;
      evt.serverUri = mSettings.orchestrationServerUrl;
      evt.statusDesc = "missing auth username or authServerUrl";
      mInterface->fireConnStatusEvent(mHandle, evt);
      return kSuccess;
   }

   resip::Data addUserUrl;
   {
      resip::DataStream addUserUrlDs(addUserUrl);
      addUserUrlDs << mSettings.authServerUrl;
      addUserUrlDs << "/addUser";
   }
   std::weak_ptr<CloudRelayConnectorImpl> weakThis(shared_from_this());
   CloudServerConnection::doAddUserFlow(mWebRequestThread,
      mInterface->getThisSdkPhone()->getSdkModuleThread(),
      mSettings.username.c_str(),
      mSettings.password.c_str(),
      addUserUrl,
      mSettings.authServerApiKey.c_str(),
      mSettings.ignoreCertVerification,
      mInterface->getThisSdkPhone()->getSslCipherOptions(),
      [weakThis](int resultCode, const cpc::string& details) {
      if (std::shared_ptr<CloudRelayConnectorImpl> thisPtr = weakThis.lock())
      {
         resip::Data loginUrl;
         {
            resip::DataStream loginUrlDs(loginUrl);
            loginUrlDs << thisPtr->mSettings.authServerUrl;
            loginUrlDs << "/login_v1";
         }
         std::vector<resip::Data> requestedResources;
         CloudServerConnection::doAuthFlow(thisPtr->mWebRequestThread,
            thisPtr->mInterface->getThisSdkPhone()->getSdkModuleThread(),
            loginUrl,
            thisPtr->mSettings.username.c_str(),
            thisPtr->mSettings.password.c_str(),
            requestedResources,
            thisPtr->mSettings.ignoreCertVerification,
            thisPtr->mInterface->getThisSdkPhone()->getSslCipherOptions(),
            [weakThis](int resultCode, const resip::Data& authToken, const cpc::string& details) {
            if (std::shared_ptr<CloudRelayConnectorImpl> thisPtr = weakThis.lock())
            {
               if (resultCode == 0)
               {
                  CPCAPI2::CloudConnector::ServiceDesc cloudrelayServiceDesc;
                  cloudrelayServiceDesc.region = thisPtr->mSettings.regionCode;
                  cloudrelayServiceDesc.service = "cloudrelay";
                  thisPtr->doFirstTimeConnect(authToken, cloudrelayServiceDesc);
               }
               else
               {
                  ServiceConnectionStatusEvent evt;
                  evt.connectionStatus = ServiceConnectionStatus_AuthFailure;
                  evt.serverUri = thisPtr->mSettings.authServerUrl;
                  evt.statusDesc = details;
                  thisPtr->mInterface->fireConnStatusEvent(thisPtr->mHandle, evt);
               }
            }
         });
      }
   });
   return kSuccess;
}

int CloudRelayConnectorImpl::disconnectService()
{
   StackLog(<< "CloudRelayConnectorImpl::disconnectService()");
   if (mConfPhone != NULL)
   {
      CPCAPI2::CloudRelay::CloudRelayManagerJsonProxy* confBridgeMgr = CPCAPI2::CloudRelay::CloudRelayManagerJsonProxy::getInterface(mConfPhone);
      if (confBridgeMgr != NULL)
      {
         dynamic_cast<CloudRelayJsonProxyInterface*>(confBridgeMgr)->removeSdkObserver(this);
      }
   }
   mConfServiceUri = "";
   if (mConfServiceConn.get() != NULL)
   {
      StackLog(<< "CloudRelayConnectorImpl::disconnectService(): destroying connection to server");
      mConfServiceConn->disconnect();
   }

   return kSuccess;
}

int CloudRelayConnectorImpl::broadcast(const cpc::string& msg)
{
   mConfBridgeMgr->broadcast(1000, msg);
   return kSuccess;
}

int CloudRelayConnectorImpl::sendTo(CloudRelayEndpointId destinationId, const cpc::string& msg)
{
   mConfBridgeMgr->sendTo(1000, destinationId, msg);
   return kSuccess;
}

//CloudConferenceHandle CloudRelayConnectorImpl::createConferenceHandle(const CPCAPI2::CloudRelay::ConferenceSettings& settings)
//{
//   ConferenceHandle conf = mConfBridgeMgr->createConference(settings);
//   return (CloudConferenceHandle)conf;
//}

void CloudRelayConnectorImpl::handleCloudServerConnectionStatusChanged(CloudServerConnection* conn, const CPCAPI2::CloudConnector::CloudServerConnStatusEvent& args)
{
   if (args.status == CloudConnector::CloudServerConnStatus_ConnFailure)
   {
      return;
   }

   // translate into a per-service set of events
   const std::set<CPCAPI2::CloudConnector::ServiceDesc>& services = conn->getServices();
   std::set<CPCAPI2::CloudConnector::ServiceDesc>::const_iterator it = services.begin();
   ServiceConnectionStatusEvent evt;
   for (; it != services.end(); ++it)
   {
      if (args.status == CPCAPI2::CloudConnector::CloudServerConnStatus_Connected)
      {
         mConfPhone = conn->getPhone();
         mConfBridgeMgr = CPCAPI2::CloudRelay::CloudRelayManagerJsonProxy::getInterface(mConfPhone);
         dynamic_cast<CloudRelayJsonProxyInterface*>(mConfBridgeMgr)->addSdkObserver(this);
         evt.authToken.authToken = conn->getAuthToken();

      }
   }
   evt.serverUri = conn->getUrl();
   evt.connectionStatus = (ServiceConnectionStatus)args.status;
   
   StackLog(<< "CloudRelayConnectorImpl::handleCloudServerConnectionStatusChanged(): connection: " << conn << " handle: " << mHandle << " updated connection status: " << CloudServerConnection::get_debug_string(args.status));
   mInterface->fireConnStatusEvent(mHandle, evt);
}

void CloudRelayConnectorImpl::release()
{
   StackLog(<< "CloudRelayConnectorImpl::release(): handle: " << mHandle);

   if (mConfServiceConn.get() != NULL)
   {
      mConfServiceConn->release();
      mConfServiceConn.reset();
      mConfPhone = NULL;
   }
   mConfServiceUri = "";
}
   
int CloudRelayConnectorImpl::onBroadcast(CloudRelayHandle relay, const CPCAPI2::CloudRelay::BroadcastEvent& args)
{
   InfoLog(<< "CloudRelayConnectorImpl::onBroadcast(" << relay << ")");

   CPCAPI2::CloudRelayConnector::BroadcastEvent evt;
   evt.relay = relay;
   evt.senderId = args.jsonApiUser;
   evt.msg = args.msg;
   mInterface->fireBroadcast(mHandle, evt);
   return kSuccess;
}

int CloudRelayConnectorImpl::onMessage(CloudRelayHandle relay, const CPCAPI2::CloudRelay::MessageEvent& args)
{
   InfoLog(<< "CloudRelayConnectorImpl::onMessage(" << relay << ")");

   CPCAPI2::CloudRelayConnector::MessageEvent evt;
   evt.relay = relay;
   evt.senderId = args.jsonApiUser;
   evt.msg = args.msg;
   mInterface->fireMessage(mHandle, evt);
   return kSuccess;
}

}

}

#endif // CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE
