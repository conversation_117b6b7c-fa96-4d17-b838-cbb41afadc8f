#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_CHAT_MODULE==1 || CPCAPI2_BRAND_IM_MODULE==1 || CPCAPI2_BRAND_XMPP_CHAT_MODULE==1 || CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
#include "ComposerActiveState.h"
#include "ComposerIdleState.h"
#include "IsComposingManager.h"
#include "IsComposingInfo.h"

namespace CPCAPI2
{
namespace IsComposing
{

void ComposerActiveState::onEnterState(IsComposingManager* manager, IsComposingInfo* info)
{
   // Start the timers
   manager->startComposerIdleTimer(info);
   manager->startComposerRefreshTimer(info);
}

void ComposerActiveState::onIsComposingMessage(IsComposingManager* manager, IsComposingInfo* info) 
{
   if (info->composerIdleInterval == 0)
   {
      manager->stopComposerIdleTimer(info);
      onIdleTimeout(manager, info);
      return;
   }

   // Reset the idle timer
   manager->stopComposerIdleTimer(info);
   manager->startComposerIdleTimer(info);

   // Stay in this state
}

void ComposerActiveState::onIdleTimeout(IsComposingManager* manager, IsComposingInfo* info)
{
   // Send a notification with the Idle state
   manager->sendIsComposingMessageNotification(info, IsComposingMessageState_Idle, info->composerContentType, 0, info->composerLastActive);

   // Transition to the Idle state
   manager->transitionTo(info, &ComposerIdleState::instance);
   manager->backToComposerIdleState(info);
}

void ComposerActiveState::onRefreshTimeout(IsComposingManager* manager, IsComposingInfo* info)
{
   // Send a notification with the Active state
   manager->sendIsComposingMessageNotification(info, IsComposingMessageState_Active, info->composerContentType, info->composerRefreshInterval, info->composerLastActive);

   // Start the refresh timer again
   manager->startComposerRefreshTimer(info);

   // Stay in this state
}

void ComposerActiveState::onMessageSent(IsComposingManager* manager, IsComposingInfo* info)
{
   // No notification to send

   // Transition to the Idle state
   manager->transitionTo(info, &ComposerIdleState::instance);
   manager->backToComposerIdleState(info);
}

void ComposerActiveState::onLeaveState(IsComposingManager* manager, IsComposingInfo* info)
{
   // Stop the timers
   manager->stopComposerIdleTimer(info);
   manager->stopComposerRefreshTimer(info);
}

ComposerActiveState ComposerActiveState::instance;

}
}
#endif
