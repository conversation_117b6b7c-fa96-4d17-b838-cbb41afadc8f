#pragma once

#if !defined(__SIP_MSRP_MESSAGING_SESSION_INFO_H__)
#define __SIP_MSRP_MESSAGING_SESSION_INFO_H__

#include "../account/CPInviteHandlerSelectorDialogSet.h"

#include <msrp_forward_decl.h>

namespace CPCAPI2
{
namespace CPM
{

struct SipMsrpMessagingSessionInfo : public SipAccount::CPInviteHandlerSelectorDialogSet
{
   SipMsrpMessagingSessionInfo(int handle, resip::SharedPtr<resip::DialogUsageManager> dum, resip::InviteSessionHandler* handler);
   virtual ~SipMsrpMessagingSessionInfo();

   int handle;
   int accountHandle;
   resip::NameAddr remotePartyAddress;
   msrp_uri_t* localUri;
   msrp_uri_t* remoteUri;
   resip::ServerInviteSessionHandle serverInviteSessionHandle;
   resip::ClientInviteSessionHandle clientInviteSessionHandle;
   bool isActive;
   msrp_session_t* msrpSession;
};
typedef std::map<int, SipMsrpMessagingSessionInfo*> SipMsrpMessagingSessionInfoMap;

}
}

#endif // __SIP_MSRP_MESSAGING_SESSION_INFO_H__