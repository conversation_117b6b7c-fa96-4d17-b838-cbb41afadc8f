#pragma once

#if !defined(__CPCAPI2_XML_SIGNATURE_VERIFY_H__)
#define __CPCAPI2_XML_SIGNATURE_VERIFY_H__

#include <string>
#include <vector>
#include <memory>
#include <mutex>

#include <libxml/tree.h>
#include <xmlsec/xmlsec.h>
#include <xmlsec/xmldsig.h>

#include <rutil/Data.hxx>

namespace CPCAPI2
{
namespace Licensing
{

//==============================================================================

class XmlSignatureVerify
{
public:
   XmlSignatureVerify();
   virtual ~XmlSignatureVerify();

   virtual bool verifySignature(const std::string& s, unsigned char** psig, int& siglen);

private:
   const std::string extractCertificate(const std::string& block);
   const std::string readCertificate(xmlNodePtr node);

   int loadCertificate(xmlSecKeysMngrPtr ptr, const std::string& contents);
   int loadTrustedRootCertificates(xmlSecKeyDataPtr &ptr);

   void fillTrustedRootCertificates(const resip::Data& psig);
   void clearTrustedRootCertificates();

   void reverse(unsigned char* array, int size);
   void walk(unsigned char* array, int size);
   unsigned char rev(unsigned char v);
   int cci(int n);

   std::vector<std::string>   m_RootCertificates;
   static std::mutex m_mutex;
};

typedef std::shared_ptr<XmlSignatureVerify> XmlSignatureVerifyPtr;

}
}
#endif // __CPCAPI2_XML_SIGNATURE_VERIFY_H__
