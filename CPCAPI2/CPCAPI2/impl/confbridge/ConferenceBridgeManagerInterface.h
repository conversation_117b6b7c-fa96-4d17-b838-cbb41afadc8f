#pragma once

#if !defined(CPCAPI2_ConferenceBridge_MANAGER_INTERFACE_H)
#define CPCAPI2_ConferenceBridge_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "confbridge/ConferenceBridgeManager.h"
#include "confbridge/ConferenceBridgeHandler.h"
#include "confbridge/ConferenceBridgeInternal.h"
#include "confbridge/ConferenceRegistrar.h"
//include "ConferenceBridgeSyncHandler.h"
#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"
#include "../util/AutoTestProcessor.h"

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>

// Simple-Http-Server
#include <server_https.hpp>

#include <rutil/Fifo.hxx>

#include <map>
#include <set>
#include <thread>

namespace CPCAPI2
{
class PhoneInterface;

namespace ConferenceBridge
{
class ConferenceBridgeManagerImpl;

class ConferenceBridgeManagerInterface : public ConferenceBridgeManagerInternal,
   public PhoneModule,
   public resip::ReactorBinded
#ifdef CPCAPI2_AUTO_TEST
   , public AutoTestProcessor
#endif
{
public:
   ConferenceBridgeManagerInterface(Phone* phone);
   virtual ~ConferenceBridgeManagerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;
   
   // ReactorBinded
   virtual void release() OVERRIDE;

   virtual int process(unsigned int timeout) OVERRIDE;
#ifdef CPCAPI2_AUTO_TEST
   virtual AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif

   virtual void interruptProcess() OVERRIDE;

   void post(resip::ReadCallbackBase* f);
   void postCallback(resip::ReadCallbackBase*);

   void addSdkObserver(ConferenceBridgeHandler* observer);
   void removeSdkObserver(ConferenceBridgeHandler* observer);

   void addParticipantMapping(ConferenceParticipantHandle participant, ConferenceHandle conference);
   void removeParticipantMapping(ConferenceParticipantHandle participant);

   // ConferenceBridgeManagerInternal
   static ConferenceBridgeManagerInternal* getInternalInterface(Phone* cpcPhone);
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE;
   virtual int getConferenceSummary(cpc::string& summary) OVERRIDE;
   virtual int queryMediaStatistics(ConferenceHandle conference) OVERRIDE;

   // ConferenceBridgeManager
   virtual int start(const ConferenceBridgeConfig& bridgeConfig) OVERRIDE;
   virtual int shutdown() OVERRIDE;
   virtual ConferenceHandle createConference(const ConferenceSettings& conferenceSettings) OVERRIDE;
   virtual ConferenceHandle createConference(const ConferenceSettings& conferenceSettings, ConferenceBridgeHandler* handler) OVERRIDE;
   virtual int setHandler(ConferenceBridgeHandler* handler) OVERRIDE;
   virtual int setHandler(ConferenceHandle conference, ConferenceBridgeHandler* handler) OVERRIDE;
   virtual int destroyConference(ConferenceHandle conference) OVERRIDE;
   virtual int addSipEndpoint(ConferenceHandle conference, CPCAPI2::SipAccount::SipAccountHandle sipAccount) OVERRIDE;
   virtual int queryConferenceDetails(ConferenceHandle conference) OVERRIDE;
   virtual int queryConferenceDetails(ConferenceHandle conference, ConferenceDetailsHandler* handler) OVERRIDE;
   virtual int queryConferenceDetails(const cpc::string& conferenceToken, ConferenceDetailsHandler* handler) OVERRIDE;
   virtual int queryConferenceList(ConferenceListHandler* handler) OVERRIDE;
   virtual int queryParticipantList(ConferenceHandle conference, ParticipantListHandler* handler) OVERRIDE;
   virtual ConferenceParticipantHandle createWebParticipant(ConferenceHandle conference) OVERRIDE;
   virtual ConferenceParticipantHandle createWebParticipant(ConferenceHandle conference, CreateWebParticipantHandler* handler) OVERRIDE;
   virtual ConferenceParticipantHandle createWebParticipant(ConferenceHandle conference, const WebParticipantIdentity& identityInfo, bool addToFloor, CreateWebParticipantHandler* handler) OVERRIDE;
   virtual int setWebParticipantIdentity(ConferenceParticipantHandle webParticipant, const WebParticipantIdentity& identityInfo) OVERRIDE;
   virtual int takeParticipantSnapshot(ConferenceParticipantHandle participant) OVERRIDE;
   virtual int setParticipantPhoto(ConferenceParticipantHandle participant, const cpc::string& photoFileNameUtf8) OVERRIDE;
   virtual int destroyWebParticipant(ConferenceParticipantHandle webParticipant) OVERRIDE;
   virtual int setParticipantPermissions(ConferenceParticipantHandle participant, const ParticipantPermissions& permissionsInfo) OVERRIDE;
   virtual int sendPeerConnectionOffer(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpOffer, PeerConnectionAnswerHandler* handler) OVERRIDE;
   virtual int sendPeerConnectionAnswer(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpAnswer) OVERRIDE;
   virtual int generateLocalOffer(ConferenceParticipantHandle webParticipant, PeerConnectionAnswerHandler* handler) OVERRIDE;
   virtual int addAssociatedConference(ConferenceHandle conference, ConferenceHandle associatedConference) OVERRIDE;
   virtual int requestFloor(ConferenceParticipantHandle requestor) OVERRIDE;
   virtual int addToFloor(ConferenceParticipantHandle participant) OVERRIDE;
   virtual int removeFromFloor(ConferenceParticipantHandle participant) OVERRIDE;
   virtual int setVideoLayout(ConferenceHandle conference, VideoLayout layout) OVERRIDE;
   virtual int setStreamingEnabled(ConferenceHandle conference, bool enabled) OVERRIDE;
   virtual int setRecordingEnabled(ConferenceHandle conference, bool enabled) OVERRIDE;
   virtual int setTranscriptionEnabled(ConferenceHandle conference, bool enabled) OVERRIDE;
   virtual bool isValidJoinUrl(const cpc::string& joinUrl) OVERRIDE;
   virtual int updateJoinUrlBase(const cpc::string& joinUrl) OVERRIDE;

   int createConference(ConferenceHandle conference, const ConferenceSettings& conferenceSettings, ConferenceBridgeHandler* handler=NULL);
   int createWebParticipant(ConferenceParticipantHandle participant, ConferenceHandle conference, bool isContextOwner);
   int createWebParticipant(ConferenceParticipantHandle participant, ConferenceHandle conference, bool isContextOwner, const WebParticipantIdentity& identityInfo, bool addToFloor, CreateWebParticipantHandler* handler);
   int setWebParticipantIdentity(ConferenceParticipantHandle webParticipant, const WebParticipantIdentity& identityInfo, bool isContextOwner);
   int queryConferenceList(const cpc::string& conferenceToken, ConferenceListHandler* handler);
   int destroyEmptyConferences();

   static bool extractConfToken(cpc::string& confToken, const cpc::string& conferenceUrl);

   ConferenceBridgeManagerImpl* getImpl(ConferenceHandle conference) const;
   const ConferenceBridgeConfig& getConferenceBridgeConfig() const {
      return mBridgeConfig;
   }

   const resip::Data& getConferenceUrlRandomness() const {
      return mConferenceUrlRandomness;
   }

   int addResourceHandlerMapping(const std::string& userContext);
   int removeResourceHandlerMapping(const std::string& userContext);

   void updateJoinUrlTs();
   void updateConferenceSummaryTs();

   class RefCountedCameraUsage
   {
   public:
      RefCountedCameraUsage(CPCAPI2::Phone* phone);
      ~RefCountedCameraUsage();

      void AddRef();
      void Release();

   private:
      CPCAPI2::Phone* mPhone;
      resip::Mutex mSafeDeleteMutex;
      unsigned int mSharedInstanceRefCnt;
   };

   void addCameraUsage();
   void removeCameraUsage();

private:
   int startImpl(const ConferenceBridgeConfig& bridgeConfig);
   int shutdownImpl();
   int createConferenceImpl(ConferenceHandle conference, const ConferenceSettings& conferenceSettings, ConferenceBridgeHandler* handler);
   int setHandlerImpl(ConferenceHandle conference, ConferenceBridgeHandler* handler);
   int destroyConferenceImpl(ConferenceHandle conference);
   int addSipEndpointImpl(ConferenceHandle conference, CPCAPI2::SipAccount::SipAccountHandle sipAccount);
   int queryConferenceDetailsImpl(ConferenceHandle conference, ConferenceDetailsHandler* handler);
   int queryConferenceDetailsImpl2(const cpc::string& conferenceToken, ConferenceDetailsHandler* handler);
   int queryConferenceListImpl(const cpc::string& conferenceToken, ConferenceListHandler* handler);
   int queryParticipantListImpl(ConferenceHandle conference, ParticipantListHandler* handler);
   int createWebParticipantImpl(ConferenceParticipantHandle webParticipant, ConferenceHandle conference, bool isContextOwner, const WebParticipantIdentity& identityInfo, bool addToFloor, CreateWebParticipantHandler* handler);
   int destroyWebParticipantImpl(ConferenceParticipantHandle participant);
   int setParticipantPermissionsImpl(ConferenceParticipantHandle participant, const ParticipantPermissions& permissionsInfo);
   int setWebParticipantIdentityImpl(ConferenceParticipantHandle webParticipant, const WebParticipantIdentity& identityInfo, bool isContextOwner);
   int takeParticipantSnapshotImpl(ConferenceParticipantHandle participant);
   int setParticipantPhotoImpl(ConferenceParticipantHandle participant, const cpc::string& photoFileNameUtf8);
   int sendPeerConnectionOfferImpl(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpOffer, PeerConnectionAnswerHandler* handler);
   int sendPeerConnectionAnswerImpl(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpAnswer);
   int generateLocalOfferImpl(ConferenceParticipantHandle webParticipant, PeerConnectionAnswerHandler* handler);
   int addAssociatedConferenceImpl(ConferenceHandle conference, ConferenceHandle associatedConference);
   int requestFloorImpl(ConferenceParticipantHandle requestor);
   int addToFloorImpl(ConferenceParticipantHandle participant);
   int removeFromFloorImpl(ConferenceParticipantHandle participant);
   int setVideoLayoutImpl(ConferenceHandle conference, VideoLayout layout);
   int setStreamingEnabledImpl(ConferenceHandle conference, bool enabled);
   int setRecordingEnabledImpl(ConferenceHandle conference, bool enabled);
   int setTranscriptionEnabledImpl(ConferenceHandle conference, bool enabled);
   cpc::string getConferenceSummaryImpl();
   int queryMediaStatisticsImpl(ConferenceHandle conference);
   int updateJoinUrlBaseImpl(const cpc::string& joinUrl);
   int destroyEmptyConferencesImpl();

   void fireConfNotFoundEvent(ConferenceDetailsHandler* handler, ConferenceHandle confHandle, const ConferenceNotFoundResult& args);
   void fireConferenceListEvent(ConferenceListHandler* handler, const ConferenceListResult& args);
   void fireWebParticipantCreatedEvent(CreateWebParticipantHandler* handler, ConferenceHandle confHandle, const WebParticipantCreatedEvent& args);

   template <typename T>
   void handleUsersRequest(std::shared_ptr<typename SimpleWeb::Server<T>::Response> response,
      std::shared_ptr<typename SimpleWeb::Server<T>::Request> request);

   template <typename T>
   void handleStatusRequest(std::shared_ptr<typename SimpleWeb::Server<T>::Response> response,
      std::shared_ptr<typename SimpleWeb::Server<T>::Request> request);

private:
   bool mShutdown;
   resip::Fifo<resip::ReadCallbackBase> mCallbackFifo;
   PhoneInterface* mPhone;

   std::set<ConferenceBridgeHandler*> mSdkObservers;
   std::function<void(void)> mCbHook;

   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*> mConferences;
   std::map<ConferenceParticipantHandle, ConferenceHandle> mMapWebParticipantToConference;
   ConferenceBridgeConfig mBridgeConfig;
   std::shared_ptr<RefCountedCameraUsage> mCustomVideoSourceUsage;
   resip::Data mConferenceUrlRandomness;

   ConferenceRegistrar* mConfRegistrar;

   resip::Mutex mThreadSafeVarsMtx;
   std::set<std::string> mThreadSafeValidJoinUrls;
   std::string mThreadSafeConferenceSummary;
};


class ConferenceBridgeHandleFactory
{
public:
   static ConferenceHandle getNext() { return sNextHandle++; }
private:
   static ConferenceHandle sNextHandle;
};

class ConferenceParticipantHandleFactory
{
public:
   static ConferenceParticipantHandle getNext() { return sNextHandle++; }
private:
   static ConferenceParticipantHandle sNextHandle;
};

class ConferenceIdGenerator
{
public:
   static resip::Data getNew();
   static void free(const resip::Data& id);
private:
   static resip::Mutex sAllocatedIdsMtx;
   static std::set<resip::Data> sAllocatedIds;
};

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode& mode);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceBridge::ConferenceBitrateConfig& config);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceBridge::ConferenceNatTraversalServerInfo::NatTraversalServerType& serverType);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceBridge::ConferenceNatTraversalServerInfo& info);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceBridge::ConferenceInfo& info);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceBridge::ConferenceDetailsResult& result);

}

}

#endif // CPCAPI2_ConferenceBridge_MANAGER_INTERFACE_H
