#include "jsonrpc/StringSerialization.h"
#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_SIPPARAMETERTYPE_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_SIPPARAMETERTYPE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  class SipParameterType
  {
  public:
    std::optional<cpc::string> name;
    std::optional<cpc::string> value;

    SipParameterType() = default;

    SipParameterType(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipParameterType& o)
    {
      j = nlohmann::json::object();
       if (o.name) { j["name"] = *(o.name); }
     if (o.value) { j["value"] = *(o.value); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipParameterType& o)
    {
     if (j.contains("name")) { o.name = j.at("name"); }
     if (j.contains("value")) { o.value = j.at("value"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_SIPPARAMETERTYPE_H
