#include "jsonrpc/OptionalSerialization.h"
#include "jsonrpc/StringSerialization.h"
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_AUDIOCODEC_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_AUDIOCODEC_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class AudioCodec
  {
  public:
    std::optional<int32_t> pltype;
    std::optional<cpc::string> plname;
    std::optional<int32_t> plfreq;
    std::optional<int32_t> pacsize;
    std::optional<int32_t> channels;
    std::optional<int32_t> rate;
    std::optional<int32_t> priority;
    std::optional<cpc::string> displayName;

    AudioCodec() = default;

    AudioCodec(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const AudioCodec& o)
    {
      j = nlohmann::json::object();
       if (o.pltype) { j["pltype"] = *(o.pltype); }
     if (o.plname) { j["plname"] = *(o.plname); }
     if (o.plfreq) { j["plfreq"] = *(o.plfreq); }
     if (o.pacsize) { j["pacsize"] = *(o.pacsize); }
     if (o.channels) { j["channels"] = *(o.channels); }
     if (o.rate) { j["rate"] = *(o.rate); }
     if (o.priority) { j["priority"] = *(o.priority); }
     if (o.displayName) { j["displayName"] = *(o.displayName); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, AudioCodec& o)
    {
     if (j.contains("pltype")) { o.pltype = j.at("pltype"); }
     if (j.contains("plname")) { o.plname = j.at("plname"); }
     if (j.contains("plfreq")) { o.plfreq = j.at("plfreq"); }
     if (j.contains("pacsize")) { o.pacsize = j.at("pacsize"); }
     if (j.contains("channels")) { o.channels = j.at("channels"); }
     if (j.contains("rate")) { o.rate = j.at("rate"); }
     if (j.contains("priority")) { o.priority = j.at("priority"); }
     if (j.contains("displayName")) { o.displayName = j.at("displayName"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_AUDIOCODEC_H
