#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_CHAT_MODULE==1 || CPCAPI2_BRAND_IM_MODULE==1 || CPCAPI2_BRAND_XMPP_CHAT_MODULE==1 || CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
#include "IsComposingManager.h"
#include "IsComposingInfo.h"
#include "IsComposingHelper.h"

namespace CPCAPI2
{
namespace IsComposing
{

enum TimerId
{
   TimerId_ComposerIdle,
   TimerId_ComposerRefresh,
   TimerId_ReceiverRefresh
};

IsComposingManager::IsComposingManager(PhoneInterface* cpcPhone, resip::MultiReactor* reactor)
   : cpcPhone(cpcPhone)
   , mReactor(reactor)
{
   if (mReactor == NULL) mReactor = &cpcPhone->getSdkModuleThread();
}

IsComposingManager::~IsComposingManager()
{
}

void IsComposingManager::initialize(IsComposingInfo* info)
{
   assert(info);

   transitionTo(info, &ReceiverIdleState::instance);
   transitionTo(info, &ComposerIdleState::instance);
}

void IsComposingManager::setMessageSent(IsComposingInfo* info)
{
   assert(info);

   info->composerState->onMessageSent(this, info);
}

void IsComposingManager::setMessageReceived(IsComposingInfo* info)
{
   assert(info);

   info->receiverState->onMessageReceived(this, info);
}

IsComposingDocument IsComposingManager::createIsComposingMessageNotification(IsComposingMessageState state, const resip::Mime& contentType, int refreshInterval, const tm& lastActive)
{
   return IsComposingHelper::createIsComposingDocument(state, contentType, refreshInterval, lastActive);
}

void IsComposingManager::processIsComposingMessageNotification(IsComposingInfo* info, const IsComposingDocument& isComposingDocument)
{
   assert(info);

   // Extract the content of the IsComposing notification
   IsComposingMessageState state;
   resip::Mime contentType;
   int refreshInterval;
   tm lastActive;
   IsComposingHelper::extractIsComposingDocument(isComposingDocument, state, contentType, refreshInterval, lastActive);

   // Store the information received
   info->receiverRefreshInterval = refreshInterval;
   info->receiverContentType = contentType;
   info->receiverLastActive = lastActive;

   // Notify the state machine of the received state
   if (state == IsComposingMessageState_Idle)
   {
      info->receiverState->onIdleStateReceived(this, info);
   }
   else if (state == IsComposingMessageState_Active)
   {
      info->receiverState->onActiveStateReceived(this, info);
   }
   else
   {
      // Unsupported state
      assert(false);
   }
}

void IsComposingManager::setIsComposingMessage(IsComposingInfo* info, const resip::Mime& contentType, const tm& datetime, int refreshInterval, int idleInterval)
{
   assert(info);

   // Store the information received
   info->composerContentType = contentType;
   info->composerRefreshInterval = refreshInterval;
   info->composerIdleInterval = idleInterval;
   info->composerLastActive = datetime;

   // Notify the state machine of the message composing event
   info->composerState->onIsComposingMessage(this, info);
}

void IsComposingManager::transitionTo(IsComposingInfo* info, ComposerBaseState* newState)
{
   assert(info);

   if (info->composerState)
   {
      // Notify the state machine of leaving the current state
      info->composerState->onLeaveState(this, info);
   }

   info->composerState = newState;

   if (newState)
   {
      // Notify the state machine of entering a new state
      info->composerState->onEnterState(this, info);
   }
}

void IsComposingManager::transitionTo(IsComposingInfo* info, ReceiverBaseState* newState)
{
   assert(info);

   if (info->receiverState)
   {
      // Notify the state machine of leaving the current state
      info->receiverState->onLeaveState(this, info);
   }

   info->receiverState = newState;

   if (newState)
   {
      // Notify the state machine of entering a new state
      info->receiverState->onEnterState(this, info);
   }
}

void IsComposingManager::startComposerIdleTimer(IsComposingInfo* info)
{
   assert(info);

   if (info->composerIdleInterval < 0)
   {
      info->composerIdleInterval = 0;
   }

   // Create the timer
   if(info->composerIdleTimer == NULL)
   {
      info->composerIdleTimer  = new resip::DeadlineTimer<resip::MultiReactor>(*mReactor);
   }

   // Set the timeout value
   info->composerIdleTimer->expires_from_now(info->composerIdleInterval * 1000);

   // Start the timer
   info->composerIdleTimer->async_wait(this, TimerId_ComposerIdle, info);
}

void IsComposingManager::startComposerRefreshTimer(IsComposingInfo* info)
{
   assert(info);

   if (info->composerRefreshInterval < 0)
   {
      info->composerRefreshInterval = 0;
   }

   // Create the timer
   if(info->composerRefreshTimer == NULL)
   {
      info->composerRefreshTimer = new resip::DeadlineTimer<resip::MultiReactor>(*mReactor);
   }

   // Set the timeout value
   info->composerRefreshTimer->expires_from_now(info->composerRefreshInterval * 1000);

   // Start the timer
   info->composerRefreshTimer->async_wait(this, TimerId_ComposerRefresh, info);
}

void IsComposingManager::stopComposerIdleTimer(IsComposingInfo* info)
{
   assert(info);

   // Stop the timer
   if(info->composerIdleTimer != NULL)
   {
      info->composerIdleTimer->cancel();
   }
}

void IsComposingManager::stopComposerRefreshTimer(IsComposingInfo* info)
{
   assert(info);

   // Stop the timer
   if(info->composerRefreshTimer != NULL)
   {
      info->composerRefreshTimer->cancel();
   }
}

void IsComposingManager::startReceiverRefreshTimer(IsComposingInfo* info)
{
   assert(info);

   // Create the timer
   if(info->receiverRefreshTimer == NULL)
   {
      info->receiverRefreshTimer = new resip::DeadlineTimer<resip::MultiReactor>(*mReactor);
   }

   // Determine the refresh interval to use, defaults to 120 secs if not specified
   int refreshInterval = (info->receiverRefreshInterval >= 0) ? info->receiverRefreshInterval : 120;

   // Set the timeout value
   // NOTE: added an extra 1/2 second to offset the composer side a little in order 
   //       to make unit tests running in a more predictable manner.
   info->receiverRefreshTimer->expires_from_now(refreshInterval * 1000 + 500);

   // Start the timer
   info->receiverRefreshTimer->async_wait(this, TimerId_ReceiverRefresh, info);
}

void IsComposingManager::stopReceiverRefreshTimer(IsComposingInfo* info)
{
   assert(info);

   // Stop the timer
   if(info->receiverRefreshTimer != NULL)
   {
      info->receiverRefreshTimer->cancel();
   }
}

void IsComposingManager::onTimer(unsigned short timerId, void* appState)
{
   IsComposingInfo* info = (IsComposingInfo*) appState;

   if (timerId == TimerId_ComposerIdle)
   {
      // Notify the state machine of the idle timer expired
      info->composerState->onIdleTimeout(this, info);
   }
   else if (timerId == TimerId_ComposerRefresh)
   {
      // Notify the state machine of the refresh timer expired
      info->composerState->onRefreshTimeout(this, info);
   }
   else if (timerId == TimerId_ReceiverRefresh)
   {
      // Notify the state machine of the refresh timer expired
      info->receiverState->onRefreshTimeout(this, info);
   }
   else
   {
      // Unsupported timer
      assert(false);
   }
}

}
}
#endif
