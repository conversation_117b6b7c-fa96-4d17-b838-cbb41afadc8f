#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_PHONE_PHONEONLICENSINGSUCCESSEVENT_H
#define JSONRPC_CPCAPI2_PHONE_PHONEONLICENSINGSUCCESSEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  class PhoneOnLicensingSuccessEvent
  {
  public:
    PhoneOnLicensingSuccessEvent() = default;

    PhoneOnLicensingSuccessEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const PhoneOnLicensingSuccessEvent& o)
    {
      j = nlohmann::json::object();
   
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, PhoneOnLicensingSuccessEvent& o)
    {
 
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_PHONEONLICENSINGSUCCESSEVENT_H
