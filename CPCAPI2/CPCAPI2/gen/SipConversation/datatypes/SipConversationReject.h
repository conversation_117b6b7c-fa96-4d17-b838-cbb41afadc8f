#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONREJECT_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONREJECT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationReject
  {
  public:
    int32_t phoneHandle;
    int32_t conversationHandle;
    int32_t rejectReason;

    SipConversationReject() = default;

    SipConversationReject(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationReject& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["conversationHandle"] = o.conversationHandle;
     j["rejectReason"] = o.rejectReason;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationReject& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.conversationHandle = j.at("conversationHandle");
     o.rejectReason = j.at("rejectReason");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONREJECT_H
