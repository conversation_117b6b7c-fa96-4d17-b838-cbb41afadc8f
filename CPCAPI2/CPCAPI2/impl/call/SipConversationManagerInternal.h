#pragma once

#if !defined(CPCAPI2_CONVERSATION_MANAGER_INTERNAL_H)
#define CPCAPI2_CONVERSATION_MANAGER_INTERNAL_H

#include <call/SipConversationManager.h>
#include "SipConversationHandlerInternal.h"

namespace resip
{
class SipMessage;
}

namespace CPCAPI2
{

namespace SipConversation
{

class CPCAPI2_SHAREDLIBRARY_API SipConversationManagerInternal
{

public:

   virtual SipConversationHandle createConversation(CPCAPI2::SipAccount::SipAccountHandle account, SipConversationHandle conversation) = 0;
   virtual int provideSdpOffer(SipConversationHandle conversation, const CPCAPI2::SipConversation::SessionDescription& sdpOffer) = 0;
   virtual int provideSdpAnswer(SipConversationHandle conversation, const CPCAPI2::SipConversation::SessionDescription& sdpOffer, const CPCAPI2::SipConversation::SessionDescription& sdpAnswer) = 0;
   virtual int setExternalSdpHandlingEnabled(SipConversationHandle conversation, bool enabled) = 0;
   virtual int refreshConversationStatistics(SipConversationHandle conversation, bool includeNetworkStatistics, bool includeJitterStatistics, bool includeRemoteStatistics, bool force) = 0;

   // if not called, default is DTLS supported / enabled.
   // controls whether initializeDtlsFactory is called, in ConversationManager.cxx. initializeDtlsFactory can be
   // somewhat slow to execute in some cases.
   // setDtlsSupported should be called just after SipConversation::setHandler
   virtual int setDtlsSupported(CPCAPI2::SipAccount::SipAccountHandle account, bool supported) = 0;

   virtual SipConversationHandle networkChangeStarcodeHandover(SipConversationHandle originalConversation) = 0;

   // When starcode based call pull network change handling is enabled, and a network change occurs, the SDK checks
   // the post network change registration contact binding and compares it to the pre network change registration contact binding.
   // If these contact bindings differ, the SDK goes ahead with the starcode call pull.
   //
   // Invoking ignoreBindingFilterForStarcodeHandover will disable this check and after any network change, if
   // starcode network change handling is enabled, will *always* perform a starcode call pull.
   //
   // ignoreBindingFilterForStarcodeHandover(..) should only be invoked in the following scenarios:
   //
   //    1) An autotest where a simulated network change is induced with a local (i.e. non SIP push) account.
   //       Since our autotest simulated network changes won't result in a new contact binding, we need to skip the filter.
   //       The autotest could either be using an account with useRegistrar = true or useRegistrar = false (less preferable, 
   //       since this does not occur in production).
   //
   // Note that autotests testing SIP push should *not* invoke ignoreBindingFilterForStarcodeHandover, as no contact binding
   // will be in use and as such the SDK should never invoke a starcode call pull (in either autotests or production).
   //
   virtual int ignoreBindingFilterForStarcodeHandover(SipConversationHandle conversation) = 0;

   virtual int setNackPliEnabled(bool enabled) = 0;
};

struct ConversationAdornmentInternalEvent : ConversationAdornmentEvent
{
   ConversationAdornmentInternalEvent(resip::SipMessage& _resipMsg) : resipMsg(_resipMsg) {}

   SipConversationHandle conversation;
   cpc::vector<CPCAPI2::SipHeader> customHeaders;
   const resip::SipMessage& resipMsg;
};

}

}

#endif // CPCAPI2_CONVERSATION_MANAGER_INTERNAL_H
