#include "jsonrpc/StringSerialization.h"
#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTERROREVENT_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTERROREVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  class SipAccountErrorEvent
  {
  public:
    std::optional<cpc::string> errorText;

    SipAccountErrorEvent() = default;

    SipAccountErrorEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipAccountErrorEvent& o)
    {
      j = nlohmann::json::object();
       if (o.errorText) { j["errorText"] = *(o.errorText); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipAccountErrorEvent& o)
    {
     if (j.contains("errorText")) { o.errorText = j.at("errorText"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTERROREVENT_H
