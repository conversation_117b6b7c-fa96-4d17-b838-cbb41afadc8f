#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACCOUNT_MODULE == 1)
#include "SipAccountImpl.h"
#include "CPMessageDecorator.h"
#include "CPOptionsPingManager.h"
#include "CPDialogDnsResultManager.h"
#include "../util/DnsClient.h"
#include "../util/QosHelper.h"
#include "../util/IpHelpers.h"
#include "../util/BrandingHelper.h"
#include "../phone/NetworkChangeManagerImpl.h"
#include "../regevent/SipRegEventManagerInterface.h"
#include "../licensing/LicensingClientManagerInterface.h"
#include "../call/SipAVConversationManagerInterface.h"
#include "../call/BroadsoftCallControlMonitor.h"

#include "gen/SipAccount/datatypes/SipAccountEvents.h"
#include "gen/SipAccount/datatypes/SipAccountStatusChangedEvent.h"
#include "gen/SipAccount/datatypes/SipAccountErrorEvent.h"
#include "gen/SipAccount/datatypes/SipAccountStatus.h"
#include "gen/SipAccount/datatypes/SipAccountTransportType.h"
#include "gen/SipAccount/datatypes/SipAccountReason.h"
#include "phone/EventQueue.h"

#include <resip/stack/Uri.hxx>
#include <resip/stack/Helper.hxx>
#include <resip/stack/ssl/Security.hxx>
#include <resip/stack/ExtensionHeader.hxx>
#include <resip/stack/ExtensionParameter.hxx>
#include <resip/stack/Tuple.hxx>
#include <resip/stack/TransactionState.hxx>
#include <resip/dum/DumException.hxx>
#include <resip/dum/AppDialogSet.hxx>
#include <resip/dum/MasterProfile.hxx>
#include <resip/dum/ClientRegistration.hxx>
#include <resip/dum/DumFeature.hxx>
#include <resip/dum/OutgoingEvent.hxx>
#include <resip/dum/ClientOutOfDialogReq.hxx>
#include <resip/dum/ServerOutOfDialogReq.hxx>
#include <resip/dum/KeepAliveManager.hxx>
#include <resip/dum/UsageUseException.hxx>
#include <rutil/IpSynth.hxx>
#include <rutil/Random.hxx>
#include <rutil/dns/RRList.hxx>

#include "../util/cpc_logger.h"
#include "../util/SipHelpers.h"
#include "../util/PlatformUtils.h"

#include <rutil/Log.hxx>
#include <rutil/DnsUtil.hxx>

#include "tsm/TseUdpTransport.h"
//#include "tsm/TseTcpTransport.h"
#ifdef USE_SSL
//#include "tsm/TseTlsTransport.h"
#endif

#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
#include "../strettotunnel/StrettoTunnelTransport.h"
#endif

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_ACCOUNT

#ifdef USE_SSL
#if defined(WinRT)
  #include <resip/stack/ssl/counterpath/XWinRTSecurity.hxx>
#elif _WIN32
  #include <resip/stack/ssl/counterpath/XWinSecurity.hxx>
#elif ANDROID
  #include <resip/stack/ssl/counterpath/AndroidSecurity.hxx>
#elif BB10
  #include <resip/stack/ssl/counterpath/BlackberrySecurity.hxx>
#elif __APPLE__
  #include "resip/stack/ssl/IOSSecurity.hxx"
#elif __THREADX
  #include <resip/stack/ssl/counterpath/ThreadXSecurity.hxx>
#elif __linux__
  #include <resip/stack/ssl/counterpath/LinuxSecurity.hxx>
#endif
#endif

#include "cpcapi2utils.h"
#include "account/SipAccountSettings.h"
#include "SipAccountInterface.h"
#include "SipAccountAwareFeature.h"
#include "AppDialogSetFactory.h"

#ifdef CPCAPI2_BRAND_STUN_STRING
#include <reTurn/client/TurnAsyncSocket_no_asio.hxx>
#endif

#include <vector>
#include <string>
#include <sstream>

using namespace resip;

#define RETRY_START_INTERVAL                               30
#define REGISTRATION_FAILURE_RETRY_LIMIT_PRIOR_TO_INCREASE 2
#define REGISTRATION_FAILURE_MINIMUM_INTERVAL_SECONDS      5
#define DISABLE_TIMER_ID                                   1
#define RETRY_TIMER_ID                                     2
#define LICENSE_FRAUD_CHECK_TIMER_ID                       3
#define REG_AFTER_CONN_PROBE_TIMER_ID                      4
#define NETWORK_CHANGE_DELAY_TIMER_ID                      5
#define NETWORK_CHANGE_DELAY_IN_SECONDS                    7
#define PROBE_AFTER_MOCK_DELAY_TIMER_ID                    8
#define NETWORK_CHANGE_HISTORY_DEPTH_IN_SECONDS            60*20
#define NETWORK_CHANGE_NUM_CHANGES_BEFORE_SHORT_EXPIRES    5
#define NETWORK_CHANGE_SHORT_EXPIRES_INTERVAL_SECONDS      60U
#define NAT64_FAKE_PORT                                    50000
#define PROBE_TIMEOUT_MSECS                                2000


// Macro such that we capture the caller line number
#define AR_STATE_TO(ars) \
   std::string accFrom; \
   if (mDum && mDum->getMasterProfile()) \
   { \
      accFrom = mDum->getMasterProfile()->getDefaultFrom().uri().getAorNoPort().c_str(); \
   } \
   InfoLog(<< "AccountRegState transition from " << ars_str(mAccountRegState) \
      << " to " << ars_str(ars) << " for mHandle=" << mHandle << " (" << accFrom << ")"); \
   mAccountRegState = ars;


namespace CPCAPI2
{

namespace SipAccount
{

SipAccountImpl::SipAccountImpl(CPCAPI2::SipAccount::SipAccountHandle h,
   const resip::Data& instanceId,
   SipAccountInterface* acctInterface,
   PhoneInterface* cpcPhone) :
   mHandle(h),
   mStack(NULL),
   mShutdown(true),
   mDeleteOnShutdown(false),
   mReEnableOnShutdown(false),
   mRportReregTryCount(0),
   mTransportErrorRetryCount(0),
   mDisabling(false),
   mEnabled(false),
   mNat64DiscoveryFailure(false),
   mMWIhandler(NULL),
   mInstanceId(instanceId),
   mPhone(cpcPhone),
   mNetworkChangeManagerIf(NetworkChangeManager::getInterface(cpcPhone)),
   mInterface(acctInterface),
   mUdpTransport(NULL),
   mUdpTransportV6(NULL),
   mTcpTransport(NULL),
   mTcpTransportV6(NULL),
   mTlsTransport(NULL),
   mTlsTransportV6(NULL),
   mUsingStrettoTunnel(false),
   mRegistrationRetry(0, mPhone->getSdkModuleThread()),
   mRegAfterConnProbe(mPhone->getSdkModuleThread(), resip::SharedPtr<resip::SipMessage>()),
   mDisableTimer(mPhone->getSdkModuleThread()),
   mProbeHandler(NULL),
   mAdornmentHandler(NULL),
   mDecoratorHandler(NULL),
   mAccountRegState(ARS_NotRegistered),
   mLastAccountStatusFired(SipAccountStatusChangedEvent::Status_Unregistered),
   mNetworkChangeHistory(NETWORK_CHANGE_HISTORY_DEPTH_IN_SECONDS),
   mNetworkChangeDelayTimer(mPhone->getSdkModuleThread()),
   mCurrentFailureRegistrationIntervalSeconds(REGISTRATION_FAILURE_MINIMUM_INTERVAL_SECONDS),
   mInitialFailureRegistrationRetryAttemptsPending(REGISTRATION_FAILURE_RETRY_LIMIT_PRIOR_TO_INCREASE),
   mRegEventSubscriptionHandle(0),
   mLicenseFraudCheckTimer(NULL),
   mBroadsoftCallControlMonitor(NULL),
   mUseAlias(false),
   mWwanTransportHoldOverCall(false),
   mNetworkTransportFromLastReg(TransportNone),
#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
   mStrettoTunnelRequiresV4(false),
   mStrettoTunnel(0),
   mSuppressUnregister(false),
#endif
#if TARGET_OS_IPHONE
   mReadActivityCallback(NULL),
   mReadActivityCallbackUserData(NULL),
#endif
   mOptionsPingManger(new CPOptionsPingManager(cpcPhone, this)),
   mDialogDnsResultManager(new CPDialogDnsResultManager(cpcPhone, this)),
   mStunClient(new StunClient()),
   mIgnoreNetworkChangeStarcodeFilterEnabled(true),
   mResetDnsAfterAllCallsEnded(false),
   mSkipResetTransportOnNetworkChange(false)
{
   InfoLog(<< "SipAccountImpl::SipAccountImpl(): Constructor: " << this << " mHandle: " << mHandle << " mInterface: " << mInterface << " mPhone: " << mPhone);
#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)
   memset(&mTseTunnelInfo, 0, sizeof(mTseTunnelInfo));
#endif

#ifdef USE_SSL
#if defined(__APPLE__)
   mCertStorageMask = resip::CERT_FILESYSTEM_STORAGE | resip::CERT_OS_SPECIFIC_STORAGE;
#elif defined(__linux__) && !defined(ANDROID)
	mCertStorageMask = resip::CERT_FILESYSTEM_STORAGE;
#else
   mCertStorageMask = resip::CERT_OS_SPECIFIC_STORAGE;
#endif
#endif

#ifdef CPCAPI2_BRAND_STUN_STRING
   reTurn::TurnAsyncSocket::StunString = CPCAPI2_BRAND_STUN_STRING;
#endif

   mMessageDecorator.reset(new CPMessageDecorator(mPhone, mHandle, resip::Data(), 0, false, false));
   mMessageDecorator->enableReleaseBindings();
   this->registerAccountAwareFeature(mMessageDecorator.get());

   // temporarily disable
   this->addSdkObserver(mOptionsPingManger);
   this->registerAccountAwareFeature(mOptionsPingManger);

   this->addSdkObserver(mDialogDnsResultManager);
   this->registerAccountAwareFeature(mDialogDnsResultManager);
}

SipAccountImpl::~SipAccountImpl()
{
   InfoLog(<< "SipAccountImpl::~SipAccountImpl(): Destructor: " << this << " mHandle: " << mHandle << " mInterface: " << mInterface << " mPhone: " << mPhone);

   if (PhoneInterface* pi = dynamic_cast<PhoneInterface*>(mPhone))
   {
      if (pi->getSdkModuleThread().isCurrentThread() == false)
      {
         ErrLog(<< "SipAccountImpl dtor on unexpected thread");
         abort();
      }
   }

   if (mLicenseFraudCheckTimer != NULL)
   {
      mLicenseFraudCheckTimer->cancel();
      delete mLicenseFraudCheckTimer;
   }

#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
   if (mStrettoTunnel)
   {
      dynamic_cast<StrettoTunnelTransport*>(mStrettoTunnel)->setHandler(0);
      mStrettoTunnel = 0;
   }
#endif

   if (!mShutdown)
   {
      forceShutDown();
   }

   std::vector<SipAccountAwareFeature*> features = mAccountAwareFeatures;
   for (std::vector<SipAccountAwareFeature*>::const_iterator itFeat = features.begin() ; itFeat != features.end() ; ++itFeat)
   {
      (*itFeat)->release();
   }

   // During shutdown, phone module will always return null, as the interface has been removed from the phone module list,
   // so maintain the pointer and trigger accountDestroyed directly. SipAccountInterface will remain active until all the
   // sip accounts have confirmed their destruction (or timeout), by persisting it's reference count in the phone module.
   mInterface->accountDestroyed(mHandle);
   if (mPhone)
   {
      mPhone->getSdkModuleThread().unregisterEventHandler(this);
   }

   delete mOptionsPingManger;
   delete mDialogDnsResultManager;
   // SipAccountInterface* interface = dynamic_cast<SipAccountInterface*>(mPhone->getInterfaceByName("SipAccountInterface"));
   // if (interface)
   //   post(resip::resip_bind(&SipAccountInterface::accountDestroyed, interface, mHandle));

   delete mStunClient;

   if (mMessageDecorator)
   {
      unregisterAccountAwareFeature(mMessageDecorator.get());
   }
   mMessageDecorator.reset();
   mDecoratorHandler = NULL;
}

bool SipAccountImpl::mutualAuthCertConfigured(const SipAccountSettings& settings) const
{
   if (std::string(settings.userCertificatePEM) != "none" && !settings.userPrivateKeyPEM.empty())
   {
      return true;
   }

   return false;
}

Transport* SipAccountImpl::createTransport(resip::TransportType type, resip::IpVersion ipver, const SipAccountSettings& acctSettings)
{
   DebugLog(<< "SipAccountImpl::createTransport Transport: " << resip::getTransportNameFromType(type) << " IP: "
      << resip::toIpVersionString(ipver) << " Source Address: " << acctSettings.sourceAddress);
   Transport* transport = NULL;
   int port = 0;
   bool inc = true;

   if (!acctSettings.sourceAddress.empty())
   {
      if (!IpHelpers::isAvailableIpAddress(acctSettings.sourceAddress.c_str()))
      {
         ErrLog(<< "SipAccountImpl::createTransport sourceAddress " << acctSettings.sourceAddress << " not available");
         return NULL;
      }
      if ((ipver == V6) && !DnsUtil::isIpV6Address(acctSettings.sourceAddress.c_str()))
      {
         ErrLog(<< "SipAccountImpl::createTransport sourceAddress " << acctSettings.sourceAddress << " does not match V6 ip version");
         return NULL;
      }
      else if ((ipver == V4) && !DnsUtil::isIpV4Address(acctSettings.sourceAddress.c_str()))
      {
         ErrLog(<< "SipAccountImpl::createTransport sourceAddress " << acctSettings.sourceAddress << " does not match V4 ip version");
         return NULL;
      }
   }

   if (acctSettings.minSipPort > 0)
   {
      port = acctSettings.minSipPort;
      inc = true;
   }
   else if (acctSettings.maxSipPort > 0)
   {
      port = acctSettings.maxSipPort;
      inc = false;
   }
   else
   {
      if ((type == TransportType::TCP) || (type == TransportType::TLS))
      {
         // OBELISK-6273 - Ensure that the binding does get populated with the ports (49152-65535 port range)
         port = resip::Random::getCryptoRandom() % 16384 + 49152;
         DebugLog(<< "SipAccountImpl::createTransport(): assigning random port " << port << " for binding");
      }
   }

    // by default, open a listen socket
   unsigned int resipTransportFlags = 0;
   if (acctSettings.forceListenSocket == false)
   {
      // unless the app isn't forcing a listen socket, and we hit one of
      // the restrictions below

#if (defined(TARGET_OS_IPHONE) && TARGET_OS_IPHONE == 1)
      if (type == TransportType::TCP ||
          type == TransportType::TLS)
      {
         // OBELISK-4002 -- TCP listsen sockets marked for iOS legacy VoIP seem to have iOS bugs
         resipTransportFlags = RESIP_TRANSPORT_FLAG_NOBIND;
      }
#endif

      if (type == TransportType::TLS && !mutualAuthCertConfigured(acctSettings))
      {
         resipTransportFlags = RESIP_TRANSPORT_FLAG_NOBIND;
      }
   }

   if (acctSettings.tunnelConfig.useTunnel)
   {
      transport = createTunnelTransport(type, ipver, acctSettings, port);
   }
   else
   {
      do
      {
         try
         {
            transport = mDum->getSipStack().addTransport(
               type, port, ipver, StunDisabled,
               acctSettings.sourceAddress.c_str(),
               acctSettings.domain.c_str(),
               Data::Empty, // privateKeyPassPhrase
               getSSLType(acctSettings.sslVersion),
               resipTransportFlags,
               acctSettings.userCertificatePEM.c_str(),
               acctSettings.userPrivateKeyPEM.c_str());
         }
         catch (Transport::Exception&) {}
         port = (inc ? port + 1 : port - 1);
      } while ((transport) == NULL);
   }

   return transport;
}

Transport* SipAccountImpl::createTunnelTransport(resip::TransportType protocol, resip::IpVersion version, const SipAccountSettings& acctSettings, int& port)
{
   Transport* transport = NULL;
   switch (acctSettings.tunnelConfig.tunnelType)
   {
   case TunnelType_TSCF:
#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)
      mTseFailureReason = "";
      try
      {
         transport = createTscfTransport(protocol, version, acctSettings, port);
      }
      catch (Transport::Exception& e)
      {
         mTseFailureReason = e.getMessage().c_str();
         SipAccountStatusChangedEvent args;
         args.accountStatus = SipAccountStatusChangedEvent::Status_Unregistered;
         args.signalingStatusCode = 0;
         args.failureRetryAfterSecs = 0;
         args.reason = SipAccountStatusChangedEvent::Reason_Tunnel_Failure;
         args.transportType = SipAccountTransport_Unknown;
         args.signalingResponseText = mTseFailureReason;
         args.accountBindingIpAddress = "";
         args.ipVersionInUse = IpVersion_Auto;
         args.localContactBinding = ""; // Ensure that binding is not populated for tunnel configuration

         fireAccountStatusEvent(args);
      }
#endif
      break;

   case TunnelType_StrettoTunnel:
#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
      transport = createStrettoTunnelTransport(protocol, version, acctSettings, port);
#endif
      break;

   default:
      break;
   }

   return transport;
}

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)
Transport* SipAccountImpl::createTscfTransport(resip::TransportType protocol, resip::IpVersion version, const SipAccountSettings& acctSettings, int& port)
{
   InternalTransport* transport=0;

   if (protocol != resip::UDP)
   {
      // Only UDP tunnel signalling/media transports supported

      ErrLog(<< "Only UDP is supported as a SIP signaling transport for TSCF.");
      //fireAccountError("Error initializing TSCF transport. Only UDP is supported as a SIP signaling transport for TSCF.");
      // Decided to fire onAccountStatusChanged instead of account error because it is more descriptive. Account error is only text.
      mTseFailureReason = "Error initializing TSCF transport. Only UDP is supported as a SIP signaling transport for TSCF.";
      SipAccountStatusChangedEvent args;
      args.accountStatus = SipAccountStatusChangedEvent::Status_Unregistered;
      args.signalingStatusCode = 0;
      args.failureRetryAfterSecs = 0;
      args.reason = SipAccountStatusChangedEvent::Reason_Tunnel_Failure;
      args.transportType = SipAccountTransport_Unknown;
      args.signalingResponseText = mTseFailureReason;
      args.accountBindingIpAddress = "";
      args.ipVersionInUse = IpVersion_Auto;
      args.localContactBinding = "";

      fireAccountStatusEvent(args);

      return transport;
   }

   SipAccountStatusChangedEvent args;
   args.accountStatus = SipAccountStatusChangedEvent::Status_Registering;
   args.signalingStatusCode = 0;
   args.failureRetryAfterSecs = 0;
   args.reason = SipAccountStatusChangedEvent::Reason_None;
   args.accountBindingIpAddress = "";
   args.transportType = SipAccountTransport_Unknown;
   args.ipVersionInUse = IpVersion_Auto;
   args.localContactBinding = "";

   fireAccountStatusEvent(args, true);

   cpc::string tunnel_server_ip;
   uint16_t tunnel_server_port;

   cpc::string cInAddressString = acctSettings.tunnelConfig.server;
   size_t iColon = cInAddressString.find(":");
   if (iColon != cpc::string::npos)
   {
      tunnel_server_ip = cInAddressString.substr(0, iColon);
      tunnel_server_port = atoi(cInAddressString.substr(iColon + 1, cInAddressString.size() - (iColon + 1)).c_str());
   }
   else
   {
      tunnel_server_ip = cInAddressString;
      if (acctSettings.tunnelConfig.transportType == TunnelTransport_TLS || acctSettings.tunnelConfig.transportType == TunnelTransport_DTLS)
      {
         tunnel_server_port = 443;
      }
      else
      {
         tunnel_server_port = 80;
      }
   }

   resip::TransportType transportType;
   switch (acctSettings.tunnelConfig.transportType)
   {
   case TunnelTransport_TCP:
      transportType = resip::TCP;
      break;
   case TunnelTransport_TLS:
      transportType = resip::TLS;
      break;
   case TunnelTransport_DTLS:
      transportType = resip::DTLS;
      break;
   case TunnelTransport_Auto:
   case TunnelTransport_UDP:
   default:
      transportType = resip::UDP;
      break;
   }

   // TSE - create a tunnel for the transport (one tunnel per SipAccount instance?)
   // NOTE: The tunnel handle is needed for the next step (transport creation)
   TseTunnelManager::TseTunnelInfo tunnel_info;
   memset(&tunnel_info, 0, sizeof(tunnel_info));
   cpc::string tunnelErrorText;
   if (TseTunnelManager::instance()->createTunnelHandle(
      tunnel_server_ip.c_str(),
      tunnel_server_port,
      transportType,
      false,
      acctSettings.tunnelConfig.ignoreCertVerification,
      tunnel_info,
      *mStack->getDnsStub(),
      acctSettings.tunnelConfig.disableNagleAlgorithm,
      tunnelErrorText) != 0)
   {
      throw Transport::Exception(tunnelErrorText.c_str(), __FILE__, __LINE__);

      return transport;
   }
   mTseTunnelInfo = tunnel_info;

   Fifo<TransactionMessage>& stateMacFifo = mDum->getSipStack().stateMacFifo();
   resip::Security security;
   try
   {
      switch (protocol)
      {
         case UDP:
            transport = new TseUdpTransport(stateMacFifo, port, version, tunnel_info.tunnel_internal_ip, tunnel_info.tunnel_handle);
            break;
         case TCP:
            //transport = new TseTcpTransport(stateMacFifo, port, version, tunnel_info.tunnel_internal_ip, 0, 0, resip::Compression::Disabled, (int)tunnel_info.tunnel_handle);
            break;
         case TLS:
#if defined( USE_SSL )
            /*transport = new TseTlsTransport(stateMacFifo,
                                         port,
                                         version,
                                         tunnel_info.tunnel_internal_ip,
                                         security,
                                         resip::Data::Empty,
                                         resip::SecurityTypes::TLS_HIGHEST,
                                         0,
                                         0,
                                         resip::Compression::Disabled,
                                         (int)tunnel_info.tunnel_handle);*/
#else
            CritLog (<< "TLS not supported in this stack. You don't have openssl");
            assert(0);
#endif
            break;
         case DTLS:
#if 0 //defined( USE_DTLS )
            transport = new DtlsTransport(stateMacFifo,
                                          port,
                                          version, // !jf! stun
                                          resip::Data::Empty,
                                          security,
                                          resip::Data::Empty);
#else
            CritLog (<< "DTLS not supported in this stack.");
            assert(0);
#endif
            break;
         default:
            assert(0);
            break;
      }
   }
   catch (BaseException& e)
   {
      ErrLog(<< "Failed to create transport: "
             << (version == V4 ? "V4" : "V6") << " "
             << Tuple::toData(protocol) << " " << port
             << " on ANY: " << e);
      throw Transport::Exception("TSCF Transport Creation Failure", __FILE__, __LINE__);
   }
   if (transport)
   {
      mDum->getSipStack().addTransport(std::unique_ptr<Transport>(transport));
   }
   return transport;
}
#endif // CPCAPI2_BRAND_ACME_TSCF_MODULE

#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
Transport* SipAccountImpl::createStrettoTunnelTransport(resip::TransportType protocol, resip::IpVersion version, const SipAccountSettings& acctSettings, int& port)
{
   StrettoTunnelTransport* transport=0;

   assert(acctSettings.useRegistrar == false);

   Fifo<TransactionMessage>& stateMacFifo = mDum->getSipStack().stateMacFifo();

   resip::IpVersion ipVersionInUse;
   switch (acctSettings.ipVersion)
   {
      case IpVersion_V4:
         ipVersionInUse = V4;
         break;
      case IpVersion_V6:
         ipVersionInUse = V6;
         break;
      case IpVersion_Auto:
      case IpVersion_Auto_PreferV6:
         ipVersionInUse = V4;
         break;
      default:
         assert(0);
         ipVersionInUse = V4;
         break;
   }

   resip::TransportType transportTypeInUse;
   switch (acctSettings.sipTransportType)
   {
      case SipAccountTransport_UDP:
         transportTypeInUse = UDP;
         break;
      case SipAccountTransport_TCP:
         transportTypeInUse = TCP;
         break;
      case SipAccountTransport_TLS:
         transportTypeInUse = TLS;
         break;
      default:
         assert(0);
         transportTypeInUse = UDP;
         break;
   }

   mUsingStrettoTunnel = true;
   DebugLog(<< "SipAccountImpl::createStrettoTunnelTransport(): account: " << mHandle << " using stretto tunnel");

   if (version == ipVersionInUse && version == V4)
   {
      // Active Stretto Tunnel is using IPV4 signalling
      mStrettoTunnelRequiresV4 = true;
   }

   Tuple transportTuple = StrettoTunnelTransport::getNextTuple(
         version,
         protocol,
         getOverrideSourceIpForNAT64(),
         acctSettings.tunnelConfig.strettoTunnelURL.c_str());

   transport = new StrettoTunnelTransport(
         stateMacFifo,
         transportTuple,
         acctSettings.tunnelConfig.strettoTunnelURL.c_str(),
         acctSettings.tunnelConfig.strettoTunnelToken.c_str(),
         acctSettings.tunnelConfig.strettoTunnelSessionID.c_str(),
         acctSettings.tunnelConfig.strettoTunnelTestConnection,
         acctSettings.tunnelConfig.logStrettoTunnelTransportTraces,
         version,
         ipVersionInUse,
         protocol,
         transportTypeInUse,
         acctSettings.ignoreCertVerification,
         acctSettings.requiredCertPublicKeys,
         acctSettings.tunnelConfig.strettoTunnelSkipHandshake);

   if (transport)
   {
      mDum->getSipStack().addTransport(std::unique_ptr<Transport>(transport));

      if (! transport->isStub())
      {
         transport->setHandler(this);
         mStrettoTunnel = transport;
      }
   }
   return transport;
}
#endif

cpc::string SipAccountImpl::getTunnelRecommendedDomain() const
{
#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)
   return mTseTunnelInfo.sip_server;
#else
   return "";
#endif
}

cpc::string SipAccountImpl::getDomain() const
{
   SipAccountSettings settings = getSettings();
   cpc::string tunnelDomain = getTunnelRecommendedDomain();
   if (settings.tunnelConfig.useTunnel && !tunnelDomain.empty())
   {
      return tunnelDomain;
   }
   return settings.domain;
}

std::string SipAccountImpl::getOverrideSourceIpForNAT64() const
{
   assert(mUdpTransport || mUdpTransportV6 || mTcpTransport || mTcpTransportV6 || mTlsTransport || mTlsTransportV6 || mUsingStrettoTunnel);
   const SipAccountSettings acctSettings = getSettings();

   if (!acctSettings.enableNat64Support ||
      (mUdpTransportV6 == NULL && mTcpTransportV6 == NULL && mTlsTransportV6 == NULL && !mUsingStrettoTunnel))
   {
      return "";
   }

#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
   if (mUsingStrettoTunnel && !(mStrettoTunnelRequiresV4 && StrettoTunnelTransport::hasOnlyIPv6Interfaces()))
   {
      return "";
   }
#endif

   bool nat64detected = resip::IpSynthTools::detectNat64(mStack->getDnsStub(), mNat64DiscoveryFailure);
   if (nat64detected)
   {
      return getOverrideSourceIpForNAT64Unconditional();
   }
   return "";
}

int SipAccountImpl::enable()
{
   InfoLog(<< "SipAccountImpl::enable() mHandle=" << mHandle);

   mPhone->logLibVersions();

   mTransportErrorRetryCount = 0;
   mWwanTransportHoldOverCall = false;
   mTransportFromLastReg = NULL;
   mNetworkTransportFromLastReg = TransportNone;

   if (mDisabling)
   {
      ErrLog(<< "SipAccountImpl::enable called while disable in progress.  Queuing re-enable.");
      mReEnableOnShutdown = true;

      // The account is likely still in disabling state, start retry timer
      // Interval > Disable timer interval in the sipaccount
      startRetryTimer(6);

      return kSuccess;
   }

   if (mEnabled)
   {
#ifndef CPCAPI2_AUTO_TEST
      assert(0); // you called enable() twice in a row
#endif
      return kSuccess; // silently fail
   }

   if (!mRestrictions.empty())
   {
      return kError;
   }

   mDisabling = false;
   mEnabled = true;
   mReEnableOnShutdown = false;
   mNetworkChangeDelayTimer.cancel();
   mDisableTimer.cancel();

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)
   mTseFailureReason = "";
#endif

   const SipAccountSettings acctSettings = getSettings();

   if (checkBrandingDomainLock() != kSuccess)
      return kError;

   mCurrentFailureRegistrationIntervalSeconds = acctSettings.minimumRegistrationIntervalSeconds;
   mInitialFailureRegistrationRetryAttemptsPending = REGISTRATION_FAILURE_RETRY_LIMIT_PRIOR_TO_INCREASE;

#if (CPCAPI2_BRAND_SIP_REG_EVENT_MODULE == 1)
   if (acctSettings.enableRegeventDeregistration == true)
   {
      SipRegEvent::SipRegEventManagerInterface* regEventIf = dynamic_cast<SipRegEvent::SipRegEventManagerInterface*>(SipRegEvent::SipRegEventManager::getInterface(mPhone));
      regEventIf->setHandlerImpl(mHandle, this);
   }
#endif

   resip::Uri::resetUserEncodingTable();
   std::string nonescaped = acctSettings.otherNonEscapedCharsInUri.c_str();
   for (std::size_t i = 0; i < nonescaped.length(); ++i)
   {
      resip::Uri::setUriUserEncoding(nonescaped[i], false);
   }

   DnsStub::DnsSettings dnsSettings;
   populateNameServer(dnsSettings);

   SipAccountStatusChangedEvent args;
   args.accountStatus = SipAccountStatusChangedEvent::Status_Registering;
   args.signalingStatusCode = 0;
   args.failureRetryAfterSecs = 0;
   args.reason = SipAccountStatusChangedEvent::Reason_None;
   args.accountBindingIpAddress = "";
   args.transportType = SipAccountTransport_Unknown;
   args.ipVersionInUse = IpVersion_Auto;
   args.localContactBinding = "";

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)
   if (mTseFailureReason.empty())
#endif
      fireAccountStatusEvent(args);

#ifdef USE_SSL
   resip::Security* sec = NULL;
   try
   {
      SslCipherOptions sslOptions = mPhone->getSslCipherOptions();
      resip::SecurityTypes::SSLType sslType = (TLS_DEFAULT == acctSettings.sslVersion) ? getSSLType((SSLVersion)sslOptions.getTLSVersion(SslCipherUsageSip)) : getSSLType(acctSettings.sslVersion);
      BaseSecurity::CipherList ciphers = BaseSecurity::CipherList(Data( acctSettings.cipherSuite.empty() ? sslOptions.getCiphers(SslCipherUsageSip) : acctSettings.cipherSuite ));
#if _WIN32
   #if defined(WinRT)
      sec = new resip::XWinRTSecurity(mCertStorageFileSystemPath, mCertStorageMask, ciphers, sslType);
   #else
      sec = new resip::XWinSecurity(mCertStorageFileSystemPath, mCertStorageMask, ciphers, sslType);
   #endif
#elif __APPLE__
      sec = new resip::IOSSecurity(mCertStorageFileSystemPath, mCertStorageMask, ciphers, sslType);
#elif ANDROID
      sec = new resip::AndroidSecurity(mCertStorageFileSystemPath, mCertStorageMask, ciphers, sslType);
#elif __linux__
      sec = new resip::LinuxSecurity(mCertStorageFileSystemPath, mCertStorageMask, ciphers, sslType);
#elif BB10
      sec = new resip::BlackberrySecurity("", CERT_FILESYSTEM_STORAGE, ciphers, sslType);
#elif __THREADX
      sec = new resip::ThreadXSecurity("", CERT_FILESYSTEM_STORAGE, ciphers, sslType);
#else
      sec = NULL;
#endif
   }
   catch (std::invalid_argument& e)
   {
      ErrLog(<< "SipAccountImpl::enable: invalid argument exception: " << e.what());
      fireAccountError(e.what());

      mEnabled = false;

      SipAccountStatusChangedEvent unregArgs;
      unregArgs.accountStatus = SipAccountStatusChangedEvent::Status_Unregistered;
      unregArgs.signalingStatusCode = 0;
      unregArgs.failureRetryAfterSecs = 0;
      unregArgs.reason = SipAccountStatusChangedEvent::Reason_None;
      unregArgs.accountBindingIpAddress = "";
      unregArgs.transportType = SipAccountTransport_Unknown;
      unregArgs.ipVersionInUse = IpVersion_Auto;
      unregArgs.localContactBinding = "";
      fireAccountStatusEvent(unregArgs);

      return kError;
   }
   catch (std::runtime_error& e)
   {
      ErrLog(<< "SipAccountImpl::enable: runtime error exception: " << e.what());
      fireAccountError(e.what());

      mEnabled = false;

      SipAccountStatusChangedEvent unregArgs;
      unregArgs.accountStatus = SipAccountStatusChangedEvent::Status_Unregistered;
      unregArgs.signalingStatusCode = 0;
      unregArgs.failureRetryAfterSecs = 0;
      unregArgs.reason = SipAccountStatusChangedEvent::Reason_None;
      unregArgs.accountBindingIpAddress = "";
      unregArgs.transportType = SipAccountTransport_Unknown;
      unregArgs.ipVersionInUse = IpVersion_Auto;
      unregArgs.localContactBinding = "";
      fireAccountStatusEvent(unregArgs);

      return kError;
   }
#endif

   resip::Data aor;
   {
      resip::DataStream ds(aor);
      cpc::string domain = getDomain();
      ds << (acctSettings.username.c_str()) << "@" << (domain.empty() ? "unknown" : (domain.c_str()));
   }

#ifdef USE_SSL
   if (sec)
   {
      if (acctSettings.enableLegacyServerConnect) {
         auto ctx = sec->getSslCtx();
         if (ctx) {
            SSL_CTX_set_options(ctx, SSL_OP_LEGACY_SERVER_CONNECT);
         } else {
            ErrLog(<< "Empty context:" << std::endl);
         }
      } 
      sec->setUserPassPhrase(aor, resip::Data::Empty);

      if (acctSettings.ignoreCertVerification)
      {
         sec->setIgnorePeerCertVerification();
      }

      if (!acctSettings.additionalCertPeerNames.empty())
      {
         std::vector<resip::BaseSecurity::PeerName> peerNames;
         for (cpc::vector<cpc::string>::const_iterator it = acctSettings.additionalCertPeerNames.begin(); it != acctSettings.additionalCertPeerNames.end(); ++it)
         {
            resip::BaseSecurity::PeerName peerName(resip::BaseSecurity::SubjectAltName, it->c_str());
            peerNames.push_back(peerName);
         }
         sec->setAdditionalPeerNames(peerNames);
      }

      if (!acctSettings.acceptedCertPublicKeys.empty())
      {
         std::vector<Data> acceptedKeys;
         for (cpc::vector<cpc::string>::const_iterator it = acctSettings.acceptedCertPublicKeys.begin(); it != acctSettings.acceptedCertPublicKeys.end(); ++it)
         {
            acceptedKeys.push_back(it->c_str());
         }
         sec->setAcceptedPublicKeys(acceptedKeys);
      }

      if (!acctSettings.requiredCertPublicKeys.empty())
      {
         std::vector<Data> requiredKeys;
         for (cpc::vector<cpc::string>::const_iterator it = acctSettings.requiredCertPublicKeys.begin(); it != acctSettings.requiredCertPublicKeys.end(); ++it)
         {
            requiredKeys.push_back(it->c_str());
         }
         sec->setRequiredPublicKeys(requiredKeys);
      }


   }
#endif

   Utils::DnsClient dnsClient(dnsSettings);
   resip::Data probeTarget = "";
   resip::Data candidateProxy = IpHelpers::stripPort(acctSettings.outboundProxy.c_str());
   resip::Data candidateDomain = IpHelpers::stripPort(acctSettings.domain.c_str());

   if (!candidateProxy.empty() && !resip::DnsUtil::isIpAddress(candidateProxy))
      probeTarget = candidateProxy;
   else if (!candidateDomain.empty() && !resip::DnsUtil::isIpAddress(candidateDomain))
      probeTarget = candidateDomain;

   // update dns server order if needed:
   if (!dnsClient.probeDns(probeTarget, acctSettings.ipVersion, dnsSettings))
   {
      WarningLog(<< "DNS probe did not detect active DNS servers");
      fireAccountError("No active DNS servers");
   }

   resip::SipStackOptions stackOptions;
#ifdef USE_SSL
   stackOptions.mSecurity = sec;
#endif
   stackOptions.mAsyncProcessHandler = mPhone->getSdkModuleThread().getAsyncProcessHandler();
   stackOptions.mExtraNameserverList = dnsSettings.nameServers.size() > 0 ? new resip::DnsStub::NameserverList(dnsSettings.nameServers) : NULL;
   stackOptions.mIncludeSystemDnsServers = dnsSettings.includeSystemDnsServers;
   stackOptions.mAfterSocketCreationFunc = SignalingQos::getSocketCreateQosFun(acctSettings.sipQosSettings);
   stackOptions.mBeforeSocketCloseFunc = SignalingQos::getSocketCloseQosFun();

   DnsStub::enableDnsFeatures(ExternalDns::TryServersOfNextNetworkUponRcode3);
   DnsStub::setDnsTimeoutAndTries(CPCAPI2_DNS_TIMEOUT, 0 /*use default*/);

   mStack = new SipStack(stackOptions);
   mStack->setStatisticsManagerEnabled(false);

   if (dnsSettings.overrideDefaultSipPort)
   {
      mStack->getDnsStub()->setOverrideSipPort(dnsSettings.overrideDefaultSipPort);
   }
   if (dnsSettings.overrideDefaultSipsPort)
   {
      mStack->getDnsStub()->setOverrideSipsPort(dnsSettings.overrideDefaultSipsPort);
   }

   mClientRegistration = ClientRegistrationHandle::NotValid();
   mDum.reset(new DialogUsageManager(*mStack));

   mBroadsoftCallControlMonitor = new CPCAPI2::SipConversation::BroadsoftCallControlMonitor(*mDum);

   mDum->setClientAuthManager(std::unique_ptr<ClientAuthManager>(new CpcClientAuthManager(*this)));

   mDum->setRedirectHandler(this);

   // .jjg. these are owned by resiprocate
   mUdpTransport = NULL;
   mUdpTransportV6 = NULL;
   mTcpTransport = NULL;
   mTcpTransportV6 = NULL;
   mTlsTransport = NULL;
   mTlsTransportV6 = NULL;

   mUsingStrettoTunnel = false;

   if (!enableTransports())
   {
      ErrLog(<< "enable(): Error initializing transports");

      // bliu: retry is disabled in this case as discussed in http://vcbase.cp.local/D222, transport error should be only be recovered through NetworkChangedManager
      // startRetryTimer(6);

      return kError;
   }

   if (UdpTransport* udpTransport = dynamic_cast<UdpTransport*>(mUdpTransport))
   {
      mStunClient->reInit(dnsSettings, udpTransport);
   }

#if TARGET_OS_IPHONE
   enableReadActivityCallback();
#endif

   populateMasterProfile(false);

   if (acctSettings.keepAliveMode == KeepAliveMode_Default ||
       acctSettings.keepAliveMode == KeepAliveMode_CRLFCRLF)
   {
      mDum->setKeepAliveManager(std::unique_ptr<KeepAliveManager>(new resip::KeepAliveManager));
   }
   else
   {
      WarningLog(<< "SIP keep-alives NOT enabled");
   }
   mDum->setClientRegistrationHandler(this);
   mDum->addOutOfDialogHandler(resip::NOTIFY, this, true);

   mDum->setInviteSessionHandlerSelector(&mHandlerSelector);

   // Create the pager message handler and let the registered features contribute to it
   mPagerMessageHandler.clearDelegates();
   mDum->setServerPagerMessageHandler(&mPagerMessageHandler);
   mDum->setClientPagerMessageHandler(&mPagerMessageHandler);
   for( std::vector<SipAccountAwareFeature*>::iterator itFeat = mAccountAwareFeatures.begin() ; itFeat != mAccountAwareFeatures.end() ; ++itFeat )
      (*itFeat)->registerSdkPagerMessageHandler( mPagerMessageHandler );

   std::unique_ptr< CPCAPI2::SipAccount::AppDialogSetFactory > pFactory(new AppDialogSetFactory());
   for( std::vector<SipAccountAwareFeature*>::iterator itFeat = mAccountAwareFeatures.begin() ; itFeat != mAccountAwareFeatures.end() ; ++itFeat )
      (*itFeat)->registerSdkDialogSetFactory( *pFactory );

   // Set the app dialog factory on DUM
   mDum->setAppDialogSetFactory( std::move(pFactory) );

   std::string overrideSourceIpSignalling;
   std::string overrideSourceIpTransport;
#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)
   TseTunnelConfig tscconfig = {mTseTunnelInfo.tunnel_handle, acctSettings.tunnelConfig.redundancyFactor, acctSettings.tunnelConfig.doLoadBalancing, acctSettings.tunnelConfig.mediaTransportType};
   overrideSourceIpSignalling = mTseTunnelInfo.tunnel_internal_ip;
   overrideSourceIpTransport = mTseTunnelInfo.tunnel_internal_ip; // !jjg! not needed?
#else
   void* tscconfig = NULL;
#endif
   if (overrideSourceIpSignalling.empty())
   {
      overrideSourceIpSignalling = getOverrideSourceIpForNAT64();
   }

   for( std::vector<SipAccountAwareFeature*>::iterator itFeat = mAccountAwareFeatures.begin() ; itFeat != mAccountAwareFeatures.end() ; ++itFeat )
      (*itFeat)->addHandlers(mDum, overrideSourceIpSignalling, overrideSourceIpTransport, &tscconfig);

   for( std::vector<SipAccountAwareFeature*>::iterator itFeat = mAccountAwareFeatures.begin() ; itFeat != mAccountAwareFeatures.end() ; ++itFeat )
      (*itFeat)->isDumShutdown() = false;

   mShutdown = false;
   mPhone->getSdkModuleThread().registerEventHandler(this);

   initRegistration(acctSettings);

   mPhone->getSdkModuleThread().getAsyncProcessHandler()->handleProcessNotification();

   const resip::SharedPtr<resip::MasterProfile> profile = mDum->getMasterProfile();
   InfoLog(<< "MasterProfile:" << std::endl
           << "                   clientOutboundEnabled: " << profile->clientOutboundEnabled() << std::endl
           << "             allowBadRegistrationEnabled: " << profile->allowBadRegistrationEnabled() << std::endl
           << "                           AllowedEvents: " << printTokens(profile->getAllowedEvents()) << std::endl
           << "                          AllowedMethods: " << printTokens(profile->getAllowedMethods()) << std::endl
           << "                             DefaultFrom: " << resip::Data::from(profile->getDefaultFrom()) << std::endl
           << "              DefaultMaxRegistrationTime: " << profile->getDefaultMaxRegistrationTime() << std::endl
           << "                  DefaultPublicationTime: " << profile->getDefaultPublicationTime() << std::endl
           << "            DefaultRegistrationRetryTime: " << profile->getDefaultRegistrationRetryTime() << std::endl
           << "                 DefaultRegistrationTime: " << profile->getDefaultRegistrationTime() << std::endl
           << "                      DefaultSessionTime: " << profile->getDefaultSessionTime() << std::endl
           << "                 DefaultSessionTimerMode: " << profile->getDefaultSessionTimerMode() << std::endl
           << "                    DefaultStaleCallTime: " << profile->getDefaultStaleCallTime() << std::endl
           << "                DefaultStaleReInviteTime: " << profile->getDefaultStaleReInviteTime() << std::endl
           << "                 DefaultSubscriptionTime: " << profile->getDefaultSubscriptionTime() << std::endl
           << "        ExpressOutboundAsRouteSetEnabled: " << profile->getExpressOutboundAsRouteSetEnabled() << std::endl
           << " ExtraHeadersInReferNotifySipFragEnabled: " << profile->getExtraHeadersInReferNotifySipFragEnabled() << std::endl
           << "                 FixedTransportInterface: " << profile->getFixedTransportInterface() << std::endl
           << "  ForceOutboundProxyOnAllRequestsEnabled: " << profile->getForceOutboundProxyOnAllRequestsEnabled() << std::endl
           << "                             ImsAuthHost: " << profile->getImsAuthHost() << std::endl
           << "                         ImsAuthUserName: " << profile->getImsAuthUserName() << std::endl
           << "             MethodParamInReferToEnabled: " << profile->isMethodParamInReferToEnabled() << std::endl
           << "                              InstanceId: " << (profile->hasInstanceId() ? profile->getInstanceId() : "<not set>") << std::endl
           << "                KeepAliveTimeForDatagram: " << profile->getKeepAliveTimeForDatagram() << std::endl
           << "                  KeepAliveTimeForStream: " << profile->getKeepAliveTimeForStream() << std::endl
           << "                           OutboundProxy: " << (profile->hasOutboundProxy() ? resip::Data::from(profile->getOutboundProxy()) : "<not set>") << std::endl
           << "                     OverrideHostAndPort: " << (profile->hasOverrideHostAndPort() ? resip::Data::from(profile->getOverrideHostAndPort()) : "<not set>") << std::endl
           << "                                   RegId: " << profile->getRegId() << std::endl
           << "                        RinstanceEnabled: " << profile->getRinstanceEnabled() << std::endl
           << "                            RportEnabled: " << profile->getRportEnabled() << std::endl
           << "                            ServiceRoute: " << printTokens(profile->getServiceRoute()) << std::endl
           << "       SupportedMimeTypes(resip::INVITE): " << printTokens(profile->getSupportedMimeTypes(resip::INVITE)) << std::endl
           << "                     SupportedOptionTags: " << printTokens(profile->getSupportedOptionTags()) << std::endl
           << "              UacReliableProvisionalMode: " << profile->getUacReliableProvisionalMode() << std::endl
           << "              UasReliableProvisionalMode: " << profile->getUasReliableProvisionalMode() << std::endl
           << "                   UserAgentCapabilities: " << (profile->hasUserAgentCapabilities() ? resip::Data::from(profile->getUserAgentCapabilities()) : "<not set>") << std::endl
           << "                             gruuEnabled: " << profile->gruuEnabled()
           );

   return kSuccess;
}

void SipAccountImpl::sendRegisterAfterConnProbe()
{
   if (!mShutdown)
   {
      // TODO: Strange scenario noted with network changes with or without a full re-init, where the
      // probe timeout occurs and the register is sent, but the probe OPTIONS are sent after that. Digging
      // deeper it seemed that one of the DNS SRV (udp) results for the OPTIONS only came after the
      // probe delay timeout so the REGISTER had been sent out anyway. As such the OPTIONS were then sent
      // out after the REGISTER. Strange part is that the DNS SRV (tls/tcp/udp) results for the REGISTER
      // all came immediately, before all the DNS SRV results for the OPTIONS.
      //
      // Note that the scenarios this occured in was with Auto Transport, Prefer V6 on both Cell-WiFi, and
      // during the changeover from WiFi to Cell, and not the other way around. Also saw this with the
      // basic enable start on Cell, is 2 seconds too short for the DNS queries.

      InfoLog(<< "sendRegisterAfterConnProbe()");
      if (mRegAfterConnProbe.mRegister.get() != NULL)
      {
         // Note there might be additional transactions pending for the other OPTIONS probe messages sent,
         // as there might have been multiple DNS records tried. As we are cleaning up here, and destroying
         // the associated dialogs, the probe responses will be discarded upon arrival.
         probeStatus(SipNetworkProbeStatusChangedEvent::Status_Completed, SipNetworkProbeStatusChangedEvent::Reason_RegisterSent);

         mRegAfterConnProbe.mOptionsV4.reset();
         mRegAfterConnProbe.mOptionsV6.reset();
         mRegAfterConnProbe.mTimer.cancel();
         mRegAfterConnProbe.mMockDelayTimer.cancel();
         mRegAfterConnProbe.mResponseV4.reset();
         mRegAfterConnProbe.mResponseV6.reset();
         mDum->send(mRegAfterConnProbe.mRegister);
         mRegAfterConnProbe.mRegister.reset();
      }
   }
}

int SipAccountImpl::disable(bool force)
{
   InfoLog(<< "SipAccountImpl::disable(" << force << ") mHandle=" << mHandle);

   mTransportErrorRetryCount = 0;

   if (mDisabling && mReEnableOnShutdown)
   {
      InfoLog(<< "SipAccountImpl::disable canceling queued re-enable");
      mReEnableOnShutdown = false;
   }

   if (mEnabled && !mDisabling && !mShutdown)
   {
      mDisabling = true;
      AR_STATE_TO(ARS_Unregistering);

#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
      if (mSuppressUnregister)
      {
         InfoLog(<< "SipAccountImpl::disable is suppressing unregister");
         this->discardRegistration();
      }
#endif
      stopRetryTimer();
      stopRegEventSubscription();

      InfoLog(<< "SipAccountImpl::disable(" << force << ") -- notify account features onDumBeingDestroyed(..)");

      // signal to all of the modules that we're shutting down DUM;
      // they need to know this, because after the call to mDum->shutdown(this), DUM
      // will start throwing exceptions if you try to use it (e.g. make new sessions, etc)
      std::vector<SipAccountAwareFeature*> featuresCopy = mAccountAwareFeatures;
      for (std::vector<SipAccountAwareFeature*>::iterator itFeat = featuresCopy.begin() ; itFeat != featuresCopy.end() ; ++itFeat)
      {
         (*itFeat)->isDumShutdown() = true;
         (*itFeat)->onDumBeingDestroyed();
      }

      if (mClientRegistration.isValid())
      {
         try
         {
            mClientRegistration->end();
         }
         catch (resip::UsageUseException&)
         {
            WarningLog(<< "Tried to do ClientRegistration::end(..) twice");
         }
      }

      if (mDum)
      {
         InfoLog(<< "SipAccountImpl::disable initiating dum shutdown");
         mDum->shutdown(this);
      }

      // at this point DUM is waiting for all dialogs/dialogsets/usages to be closed down;
      // when that happens it will call onDumCanBeDeleted() ...
      // IF for some reason all dialogs/dialogsets/usages are NOT closed down,
      // we still want to hard-kill everything (and clean up memory) -- so we set a timer
      mDisableTimer.cancel();
      mDisableTimer.expires_from_now(5000);
      mDisableTimer.async_wait(this, DISABLE_TIMER_ID, NULL);

      mEnabled = false;

      SipAccountStatusChangedEvent args;
      args.accountStatus = SipAccountStatusChangedEvent::Status_Unregistering;
      args.signalingStatusCode = 0;
      args.failureRetryAfterSecs = 0;
      args.reason = SipAccountStatusChangedEvent::Reason_None;
      args.accountBindingIpAddress = "";
      args.transportType = SipAccountTransport_Unknown;
      args.ipVersionInUse = IpVersion_Auto;
      args.localContactBinding = "";

      fireAccountStatusEvent(args);

      mUnregisteredEvent.reason = SipAccountStatusChangedEvent::Reason_None;
      mUnregisteredEvent.accountStatus = SipAccountStatusChangedEvent::Status_Unregistered;
      mUnregisteredEvent.signalingStatusCode = 0;
      mUnregisteredEvent.failureRetryAfterSecs = 0;
      mUnregisteredEvent.signalingResponseText = "";
      mUnregisteredEvent.accountBindingIpAddress = "";
      mUnregisteredEvent.transportType = SipAccountTransport_Unknown;
      mUnregisteredEvent.ipVersionInUse = IpVersion_Auto;
      mUnregisteredEvent.localContactBinding = "";

      if (!getSettings().useRegistrar)
      {
         fireAccountStatusEvent(mUnregisteredEvent);
      }
   }

   mRegEventSubscriptionHandle = 0;

   mSipServerIpFromLastReg.reset();

   return kSuccess;
}

void SipAccountImpl::destroy(bool force)
{
   mPhone->getSdkModuleThread().post(resip::resip_safe_bind(&SipAccountImpl::destroyImpl, this, force));
}

void SipAccountImpl::destroyImpl(bool force)
{
   InfoLog(<< "SipAccountImpl::destroy(force=" << force << ") mHandle=" << mHandle);
   if (mShutdown || force)
   {
      reactorSafeRelease(&mPhone->getSdkModuleThread());
   }
   else
   {
      mDeleteOnShutdown = true;
   }
}

int SipAccountImpl::requestRegistrationRefresh(unsigned int deadlineSecondsFromNow)
{
   StackLog(<< "SipAccountImpl::requestRegistrationRefresh() mHandle: " << mHandle << " deadlineSecondsFromNow: " << deadlineSecondsFromNow);
   if (mEnabled && mClientRegistration.isValid() && !mDisabling)
   {
      const unsigned int extraMargin = 60;
      if ((mClientRegistration->whenExpires() < (deadlineSecondsFromNow + extraMargin)) || (deadlineSecondsFromNow == 0))
      {
         AR_STATE_TO(ARS_Refreshing);
         mCurrentFailureRegistrationIntervalSeconds = getSettings().minimumRegistrationIntervalSeconds;
         mInitialFailureRegistrationRetryAttemptsPending = REGISTRATION_FAILURE_RETRY_LIMIT_PRIOR_TO_INCREASE;
         SipAccountStatusChangedEvent args;
         args.reason = SipAccountStatusChangedEvent::Reason_None;
         args.accountStatus = SipAccountStatusChangedEvent::Status_Refreshing;
         args.signalingStatusCode = 0;
         args.failureRetryAfterSecs = 0;
         args.signalingResponseText = "";
         args.accountBindingIpAddress = "";
         args.transportType = SipAccountTransport_Unknown;
         args.ipVersionInUse = IpVersion_Auto;
         args.localContactBinding = "";
         fireAccountStatusEvent(args);
         mClientRegistration->requestRefresh();
      }
   }
   return kSuccess;
}

int SipAccountImpl::shutdown()
{
   return kSuccess;
}

void SipAccountImpl::scheduleDelayedLicenseFraudCheck()
{
#if (CPCAPI2_BRAND_LICENSING_MODULE == 1) && (CPCAPI2_BRAND_SDK_LICENSING == 0)
   if (mLicenseFraudCheckTimer != NULL)
   {
      mLicenseFraudCheckTimer->cancel();
   }
   else
   {
      mLicenseFraudCheckTimer = new resip::DeadlineTimer<resip::MultiReactor>(mPhone->getSdkModuleThread());
   }
   // Bria must attempt licensing within 60 seconds after it is successfully registered (when using a registrar)
   // or within 60 seconds of calling enable() (if no registrar)
   mLicenseFraudCheckTimer->expires_from_now(60000);
   mLicenseFraudCheckTimer->async_wait(this, LICENSE_FRAUD_CHECK_TIMER_ID, NULL);
#endif
}

void SipAccountImpl::doDelayedLicenseFraudCheck()
{
   Licensing::LicensingClientManagerInterface* licensingClientManager = dynamic_cast<Licensing::LicensingClientManagerInterface*>(Licensing::LicensingClientManager::getInterface(mPhone));
   if (!licensingClientManager->checkValidateLicensesAttempted())
   {
      // we use an obscure message here on purpose
      mPhone->handleLicensingError(Licensing::LicenseStatus_Error, "Licensing error, code ********");
   }
}

const bool SipAccountImpl::isRestrictedNetwork(const NetworkTransport currentNetworkTransport) const
{
   return mRestrictedNetworks.find(currentNetworkTransport) != mRestrictedNetworks.end();
}

bool containsResponseType(const cpc::vector<SipResponseType>& responseTypes, const resip::SipMessage& msg)
{
   if (msg.isResponse())
   {
      unsigned int respCode = msg.header(h_StatusLine).responseCode();
      cpc::string method = resip::getMethodName(msg.header(h_CSeq).method()).c_str();
      cpc::vector<SipResponseType>::const_iterator it = responseTypes.begin();
      for (; it != responseTypes.end(); ++it)
      {
         if (it->method == method && it->responseCode == respCode)
         {
            return true;
         }
      }
   }
   return false;
}

void SipAccountImpl::process(FdSetType& fdset)
{
   if (mStack)
   {
      try
      {
         mStack->process(fdset);
         while (!mShutdown && mDum->process());
      }
      catch (DialogUsage::Exception& ex)
      {
         ErrLog(<< "Caught a resip::DialogUsage::Exception: " << ex.what());
         fireAccountError(ex.what());
      }
      catch (resip::BaseException& bexc)
      {
         ErrLog(<< "Caught a resip::BaseException: " << bexc.getMessage());
      }
      catch (...)
      {
         ErrLog(<< "SipAccountImpl::process -- Caught an unknown exception");
      }
   }
}

unsigned int SipAccountImpl::getTimeTillNextProcessMS()
{
   if (mStack)
   {
      return mStack->getTimeTillNextProcessMS();
   }
   return 200;
}

void SipAccountImpl::buildFdSet(FdSetType& fdset)
{
   if (mStack)
   {
      mStack->buildFdSet(fdset);
   }
}

void SipAccountImpl::registerAccountAwareFeature(SipAccountAwareFeature* accountAware)
{
   if(mEnabled)
   {
      fireAccountError("Tried to add account aware feature after account was already enabled. Make sure that ALL module\
::getInterface calls are done during initialization prior to enabling any accounts.");
   }
   else
   {
      mAccountAwareFeatures.push_back(accountAware);
   }
}

void SipAccountImpl::unregisterAccountAwareFeature(SipAccountAwareFeature* accountAware)
{
   std::vector<SipAccountAwareFeature*>::iterator itFeat = mAccountAwareFeatures.begin();
   for (; itFeat != mAccountAwareFeatures.end(); ++itFeat)
   {
      if ((*itFeat) == accountAware)
      {
         mAccountAwareFeatures.erase(itFeat);
         return;
      }
   }
}

void SipAccountImpl::onDumCanBeDeleted()
{
   InfoLog(<< "SipAccountImpl::onDumCanBeDeleted() mHandle=" << mHandle);

   mDisableTimer.cancel();
   mNetworkChangeDelayTimer.cancel();

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)
   // Delete the TSCF tunnel
   if (mTseTunnelInfo.tunnel_handle)
   {
      TseTunnelManager::instance()->destroyTunnel(mTseTunnelInfo.tunnel_handle);
      mTseTunnelInfo.tunnel_handle = 0;
   }
#endif

#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
   if (mStrettoTunnel)
   {
      dynamic_cast<StrettoTunnelTransport*>(mStrettoTunnel)->setHandler(0);
      mStrettoTunnel = 0;
   }
#endif

   mClientRegistration = ClientRegistrationHandle::NotValid();
   mPhone->getSdkModuleThread().unregisterEventHandler(this);
   mInterface->postToSdkThread(new resip::ReadCallbackNoOp());

   mPhone->getSdkModuleThread().post(resip::resip_safe_bind(&SipAccountImpl::deleteDum, this));
}

void SipAccountImpl::deleteDum()
{
   InfoLog(<< "SipAccountImpl::deleteDum() mHandle=" << mHandle);
   mShutdown = true;
   mDisabling = false;

   mDum.reset();

   delete mBroadsoftCallControlMonitor;
   mBroadsoftCallControlMonitor = NULL;

   delete mStack;
   mStack = NULL;

   std::vector<SipAccountAwareFeature*> features = mAccountAwareFeatures;
   for (std::vector<SipAccountAwareFeature*>::const_iterator itFeat = features.begin() ; itFeat != features.end() ; ++itFeat)
   {
      (*itFeat)->afterDumDestroyed();
   }

   // 4xx on un-REGISTER would have caused an unregistered status
   // being fired to the app; make sure we don't fire the same event again
   const bool checkPreviousStatus = true;
   fireAccountStatusEvent(mUnregisteredEvent, checkPreviousStatus);

   if (mDeleteOnShutdown)
   {
      reactorSafeRelease(&mPhone->getSdkModuleThread());
   }
   else if (mReEnableOnShutdown)
   {
      mReEnableOnShutdown = false;
      enable();
   }
}

resip::Transport* SipAccountImpl::findTransport(const resip::Transport* transport)
{
   if (transport == mUdpTransportV6)
   {
      return mUdpTransportV6;
   }
   else if (transport == mTcpTransportV6)
   {
      return mTcpTransportV6;
   }
   else if (transport == mTlsTransportV6)
   {
      return mTlsTransportV6;
   }
   else if (transport == mUdpTransport)
   {
      return mUdpTransport;
   }
   else if (transport == mTcpTransport)
   {
      return mTcpTransport;
   }
   else if (transport == mTlsTransport)
   {
      return mTlsTransport;
   }

   return NULL;
}

int SipAccountImpl::handleTransportHoldoverCalls(const NetworkChangeEvent& evt, bool& outContinueRegRefreshHandling)
{
   outContinueRegRefreshHandling = true;

   const SipAccountSettings acctSettings = getSettings();
   if (acctSettings.transportHoldover == TransportHoldover_None)
   {
      return kSuccess;
   }

#if (CPCAPI2_BRAND_CALL_MODULE == 1)
   SipConversation::SipAVConversationManagerInterface* conversationInterface = dynamic_cast<SipConversation::SipAVConversationManagerInterface*>(SipConversation::SipConversationManager::getInterface(mPhone));

   int callCount = 0;
   if (conversationInterface != NULL)
   {
      callCount = conversationInterface->getCallCount();
   }

   if (callCount > 0 && !mWwanTransportHoldOverCall && mNetworkTransportFromLastReg == TransportWWAN && evt.networkTransport == TransportWiFi)
   {
      // a call is active, and we've transitioned from WWAN to WiFi; since the WWAN network is presumably still reachable, keep using it until
      // the call ends.
      if (mTransportFromLastReg != NULL)
      {
         if ((acctSettings.transportHoldover == TransportHoldover_V4 && mTransportFromLastReg->ipVersion() == resip::V4) ||
             (acctSettings.transportHoldover == TransportHoldover_V6 && mTransportFromLastReg->ipVersion() == resip::V6) ||
             (acctSettings.transportHoldover == TransportHoldover_All))
         {
            if (resip::Transport* holdoverTransport = findTransport(mTransportFromLastReg))
            {
               const resip::Data lastSource = holdoverTransport->getLastSource().presentationFormat();
               const std::string ipVersion = holdoverTransport->ipVersion() == resip::V4 ? "IPv4" : "IPv6";

               InfoLog(<< "Transport holdover:  Transitioned from WWAN to WiFi, and WWAN transport was " << ipVersion << ", type "
                       << holdoverTransport->transport()
                       << " will temporarily bind to " << lastSource
                       << " and postpone any network change handling until all calls end");

               holdoverTransport->updateNetworkInterfaceObj(lastSource);

               if (holdoverTransport->transport() == resip::UDP)
               {
                  // does not appear to be necessary for TCP, at least on iOS. note reset() is currently a no-op for TLS/TCP
                  // transports in resip
                  holdoverTransport->reset();
               }

               mWwanTransportHoldOverCall = true;
               outContinueRegRefreshHandling = false;
            }
            else
            {
               ErrLog(<< "Transport holdover: Couldn't find transport for enabling specific transport bind");
            }
         }
      }
   }
   else if (mWwanTransportHoldOverCall && evt.networkTransport == TransportWWAN)
   {
      // we've transitioned back to WWAN, so there's no need to treat this call as special anymore
      mWwanTransportHoldOverCall = false;

      if (resip::Transport* holdoverTransport = findTransport(mTransportFromLastReg))
      {
         InfoLog(<< "Transport holdover: Transitioned back to WWAN; releasing specific transport bind");

         holdoverTransport->updateNetworkInterfaceObj(resip::Data());
         if (holdoverTransport->transport() == resip::UDP)
         {
            holdoverTransport->reset();
         }

         // we also don't need to continue with network change handling, since we mostly
         // ignored the original WWAN->WiFi transition, for the active call
         outContinueRegRefreshHandling = false;
      }
      else
      {
         ErrLog(<< "Transport holdover: Couldn't find transport for releasing specific transport bind");
      }
   }
#endif // #if (CPCAPI2_BRAND_CALL_MODULE == 1)

   return kSuccess;
}

void SipAccountImpl::resetDnsInitiated(const resip::Tuple& currentAddress, const resip::Tuple& preferredAddress)
{
   SipAccountDnsResetEvent args;
   args.currentAddress = resip::Tuple::inet_ntop(currentAddress).c_str();
   args.currentPort = currentAddress.getPort();
   args.currentIpVersion = (currentAddress.isV4() ? IpVersion_V4 : IpVersion_V6);
   args.currentTransportType = getTransportType(currentAddress.getType());
   args.preferredAddress = resip::Tuple::inet_ntop(preferredAddress).c_str();
   args.preferredPort = preferredAddress.getPort();
   args.preferredIpVersion = (preferredAddress.isV4() ? IpVersion_V4 : IpVersion_V6);
   args.preferredTransportType = getTransportType(preferredAddress.getType());

   fireDnsResetEvent(args);
}

int SipAccountImpl::refreshRegOnNetworkChange(const NetworkChangeEvent& evt)
{
   mNetworkChangeHistory.add();

   bool continueRegRefreshHandling = true;
   handleTransportHoldoverCalls(evt, continueRegRefreshHandling);
   if (!continueRegRefreshHandling)
   {
      return kSuccess;
   }

   DnsStub::DnsSettings dnsSettings;
   populateNameServer(dnsSettings);

   if (mStack != NULL)
   {
      SipConversation::SipAVConversationManagerInterface* conversationInterface = dynamic_cast<SipConversation::SipAVConversationManagerInterface*>(SipConversation::SipConversationManager::getInterface(mPhone));
      bool clearWhitelist = false;

      if (conversationInterface && (conversationInterface->getCallCount(mHandle) > 0))
      {
         mResetDnsAfterAllCallsEnded = true;
      }
      else
      {
         clearWhitelist = true;
         mStack->resetDns(); // Clears white list, mark manager, dns cache and triggers reInit on dns stub
      }

      // note the above call to mStack->resetDns() also invokes reInit() on the DNS stub, but does so with
      // default settings. We likely call reInit again here to pass in our custom dnsSettings.
      mStack->getDnsStub()->reInit(dnsSettings, clearWhitelist);

      mCurrentFailureRegistrationIntervalSeconds = getSettings().minimumRegistrationIntervalSeconds;
      mInitialFailureRegistrationRetryAttemptsPending = REGISTRATION_FAILURE_RETRY_LIMIT_PRIOR_TO_INCREASE;
   }

   mTransportErrorRetryCount = 0;

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)
   // TSCF tunnel is automatically reconnected, binding does not change
   if (mTseTunnelInfo.tunnel_handle != 0)
   {
#ifdef ANDROID
      TseTunnelManager::instance()->wakeUpTunnels(mTseTunnelInfo.tunnel_handle);
#endif
      return kSuccess;
   }
#endif

   if (mEnabled)
   {
      mStunClient->reInit(dnsSettings);
   }

   return refreshRegOnNetworkChangeImpl();
}

int SipAccountImpl::refreshRegOnNetworkChangeImpl()
{
   if (mShutdown)
   {
      return 0;
   }

   NetworkTransport currentTransport = mNetworkChangeManagerIf->networkTransport();

   SipNetworkChangeInitiatedEvent netChangeEvent;
   netChangeEvent.newTransport = currentTransport;
   for (std::vector<SipAccountAwareFeature*>::iterator itFeat = mAccountAwareFeatures.begin(); itFeat != mAccountAwareFeatures.end(); ++itFeat)
   {
      (*itFeat)->onAccountNetworkChangeInitiated(netChangeEvent);
   }

   InfoLog(<< "refreshRegOnNetworkChangeImpl() mHandle=" << mHandle << " mEnabled=" << mEnabled);
   stopRetryTimer();
   mNetworkChangeDelayTimer.cancel();

   const SipAccountSettings acctSettings = getSettings();
   mCurrentFailureRegistrationIntervalSeconds = acctSettings.minimumRegistrationIntervalSeconds;
   mInitialFailureRegistrationRetryAttemptsPending = REGISTRATION_FAILURE_RETRY_LIMIT_PRIOR_TO_INCREASE;
   mTransportErrorRetryCount = 0;

   mDum->getMasterProfile()->setDefaultRegistrationTime(acctSettings.registrationIntervalSeconds);
   setUdpAndTcpKeepAliveIntervals(mDum->getMasterProfile());

   SipAccountNetworkChangeEvent args;
   args.curTransport = currentTransport;
   args.settings = acctSettings;
   mInterface->fireEvent(cpcEvent(SipAccountHandlerInternal, onNetworkChange), mHandle, args);

   if (mRegAfterConnProbe.mRegister.get() != NULL)
   {
      mRegAfterConnProbe.mOptionsV4.reset();
      mRegAfterConnProbe.mOptionsV6.reset();
      mRegAfterConnProbe.mTimer.cancel();
      mRegAfterConnProbe.mMockDelayTimer.cancel();
      mRegAfterConnProbe.mRegister.reset();
      mRegAfterConnProbe.mResponseV4.reset();
      mRegAfterConnProbe.mResponseV6.reset();
   }

   /*
   if (handleIpVersionChange())
   {
      DebugLog(<< "refreshRegOnNetworkChangeImpl(): network change handled by ip version change handler");
      return kSuccess;
   }
   */

   if (acctSettings.useRegistrar == false)
   {
      if (mEnabled)
      {
         // closing and resetting our transports here was done for OBELISK-4812 -- for cases where a collab call is active with useRegistrar = false;
         // a network change occurring could break any active TCP or TLS connections; since we don't have retry handling for this scenario when a
         // network change re-INVITE goes out, we should reset any transport connections here in the hope they're in a good state if a re-INVITE
         // needs to goes out.
         closeTransportConnections();
         if (!mSkipResetTransportOnNetworkChange) // .jza. hit UDP transport throwing port in use exception on macOS while running an autoest with useRegistrar = false
         {
            try
            {
               mDum->getSipStack().resetTransports();
            }
            catch (Transport::Exception& e)
            {
               mReEnableOnShutdown = true;
               ErrLog(<< "refreshRegOnNetworkChangeImpl(): transport error " << e.getMessage() << " - re-enabling account.");
               disable();

               return kError;
            }
         }
         updateTransportStatus();
      }

      // Ensure that a refreshing account status update is also sent for no-registrar configuration for consistency
      SipAccountStatusChangedEvent args;
      args.accountStatus = SipAccountStatusChangedEvent::Status_Refreshing;
      args.signalingStatusCode = 0;
      args.failureRetryAfterSecs = 0;
      args.reason = SipAccountStatusChangedEvent::Reason_None;
      args.accountBindingIpAddress = "";
      args.transportType = SipAccountTransport_Unknown;
      args.ipVersionInUse = IpVersion_Auto;
      args.localContactBinding = "";  // Ensure that binding is not populated for no-registrar configuration
      fireAccountStatusEvent(args);
 
      bool useTunnel = false;
#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
      if (mStrettoTunnel)
      {
         NetworkTransport activeNetworkTransport = mNetworkChangeManagerIf->networkTransport();
         dynamic_cast<StrettoTunnelTransport*>(mStrettoTunnel)->reconnectAfterNetworkChange(activeNetworkTransport);

         useTunnel = true;
      }
#endif

      if (useTunnel)
      {
         args.accountStatus = SipAccountStatusChangedEvent::Status_Registering;
         fireAccountStatusEvent(args, true);
      }
      else
      {
         args.accountStatus = SipAccountStatusChangedEvent::Status_Registered;
         fireAccountStatusEvent(args);

         // Ensure that onRegistrationSuccess is sent after account status registered for consistency with normal registration configuration.
         // With tunnel configuration this would be after the tunnel has connected
         handleNoRegistrarConfiguration();
      }

      return kSuccess;
   }

   if (mEnabled)
   {
      closeTransportConnections();

      {
         if (mClientRegistration.isValid())
         {
            DebugLog(<< "Stopping existing client registration");
            mClientRegistration->stopRegistering();
            mClientRegistration = ClientRegistrationHandle::NotValid();
         }
         else
         {
            DebugLog(<< "Stopping existing registration - currently not registered");
            // Need to call stop registration to delete registration sessions that were not successful as such
            // we do not have a valid client registration handle. Otherwise we end up sending re-registrations
            // from stale dialogs.
            mDum->stopRegistration();
         }

         mRportReregTryCount = 0;

         mDum->getMasterProfile()->unsetOverrideHostAndPort();
         populateMasterProfile(true);

         updateTransportStatus();

         // NAT64 message decorator may be required in the new network
         setMessageDecoratorForNAT64IfRequired(mDum->getMasterProfile());
         sendRegistrationStatusUpdateAfterNetworkChange();
         sendRegisterOrProbeIfRequired();
      }

      return kSuccess;
   }

   assert(mDisabling);
   return kError;
}

UInt32 SipAccountImpl::calcPostNetworkChangeRegistrationExpires() const
{
   UInt32 expires = 0;

   if (mNetworkChangeHistory.numChanges() > NETWORK_CHANGE_NUM_CHANGES_BEFORE_SHORT_EXPIRES)
   {
      expires = std::min(getSettings().registrationIntervalSeconds, NETWORK_CHANGE_SHORT_EXPIRES_INTERVAL_SECONDS);
      InfoLog(<< "Detected " << mNetworkChangeHistory.numChanges() << " network changes in specified interval; setting new registration expiry to (smallval) " << expires);
   }
   else
   {
      expires = getSettings().registrationIntervalSeconds;
      InfoLog(<< "Detected only " << mNetworkChangeHistory.numChanges() << " network changes in specified interval; setting new registration expiry to (bigval) " << expires);
   }

   return expires;
}

bool SipAccountImpl::shouldReInitDueToIpVersionChange() const
{
   // Scenarios to consider for configuration changes of the IP version when the network transport changes (wifi-cell/cell-wifi)
   // or when the network transport does not change (wifi-wifi/cell-cell) but the IP environment may have changed. If a transport
   // change is involved or the configured IP version is Auto, then the transports will be re-initialized as long as there is no
   // call in progress.
   //
   // Basic Network Change Actions: Reset DNS, Close Transport Connections, Reset Contact, Reset Rport, Reset Expiry, Reset Registration State
   //
   // - V4 to V4                  Action: Basic
   // - V4 to V6                  Action: Basic, Reset Transports
   // - V4 to Auto V4             Action: Basic, Reset Transports, Probe
   // - V4 to Auto V6             Action: Basic, Reset Transports, Probe
   //
   // - V6 to V6                  Action: Basic
   // - V6 to V4                  Action: Basic, Reset Transports
   // - V6 to Auto V6             Action: Basic, Reset Transports, Probe
   // - V6 to Auto V4             Action: Basic, Reset Transports, Probe
   //
   // - Auto V4 to Auto V4        Action: Basic, Probe
   // - Auto V4 to V4             Action: Basic, Reset Transports
   // - Auto V4 to V6             Action: Basic, Reset Transports
   // - Auto V4 to Auto V6        Action: Basic, Probe
   //
   // - Auto V6 to Auto V6        Action: Basic, Probe
   // - Auto V6 to V4             Action: Basic, Reset Transports
   // - Auto V6 to V6             Action: Basic, Reset Transports
   // - Auto V6 to Auto V4        Action: Basic, Probe
   //
   // The above summarizes the permutations resulting from the configuration changes, but does not account for the network
   // changes (i.e. the IP version supported in the new network versus the IP version supported prior to the network change)
   // or the support for the particular IP version on the proxy-registrar or peer entity.
   //
   // - network is V4
   // - network is V6
   // - network is NAT64
   // - network is V4/V6

   SipConversation::SipAVConversationManagerInterface* conversationInterface = dynamic_cast<SipConversation::SipAVConversationManagerInterface*>(SipConversation::SipConversationManager::getInterface(mPhone));

   int callCount = 0;
   if (conversationInterface != NULL)
   {
      callCount = conversationInterface->getCallCount();
   }

   // TODO: In what scenarios would this end up saving the call, i.e. no use in
   // maintaining the call if the signalling and data are not flowing anyway.
   // The canCallBeSavedDuringNetworkChange function currently always returns false,
   // so we will always end up triggering the transport reset
   if (ipVersionSupportChanged() && ((callCount == 0) || ((callCount > 0) && canCallBeSavedDuringNetworkChange())))
   //   || (networkTransportChanged() && ((callCount == 0) || ((callCount > 0) && canCallBeSavedDuringNetworkChange()))))
   {
      DebugLog(<< "shouldReInitDueToIpVersionChange(): re-init required");
      return true;
   }

   DebugLog(<< "shouldReInitDueToIpVersionChange(): re-init not required");
   return false;
}

bool SipAccountImpl::networkTransportChanged() const
{
   NetworkTransport activeNetworkTransport = mNetworkChangeManagerIf->networkTransport();
   if (activeNetworkTransport != mCurrentNetworkTransport)
   {
      return true;
   }

   return false;
}

bool SipAccountImpl::ipVersionSupportChanged() const
{
   // IP version support is only presumed to have changed if support for a particular IP version is added or removed,
   // as such change from one auto version option to another auto version option impacts the IP version handling but
   // does not impact the supported IP versions as both V4 and V6 are supported in either case.

   const SipAccountSettings acctSettings = getSettings();

   bool bActiveIsAuto((acctSettings.ipVersion == IpVersion_Auto) || (acctSettings.ipVersion == IpVersion_Auto_PreferV6));
   bool bCurrentIsAuto((mCurrentIpVersion == IpVersion_Auto) || (mCurrentIpVersion == IpVersion_Auto_PreferV6));
   if ((acctSettings.ipVersion != mCurrentIpVersion) && (!(bActiveIsAuto && bCurrentIsAuto)))
   {
      DebugLog(<< "ipVersionSupportChanged(): ip version support changed with change from " << mCurrentIpVersion << " to " << acctSettings.ipVersion);
      return true;
   }

   DebugLog(<< "ipVersionSupportChanged(): ip version support has not changed with change from " << mCurrentIpVersion << " to " << acctSettings.ipVersion);
   return false;
}

bool SipAccountImpl::autoIpVersion() const
{

   NetworkTransport activeNetworkTransport = mNetworkChangeManagerIf->networkTransport();
   const SipAccountSettings acctSettings = getSettings();

   DebugLog(<< "SipAccountImpl::autoIpVersion(): Active network transport: " << activeNetworkTransport << " SIP account setting list size: " << mSettings.size());
   if ((acctSettings.ipVersion == IpVersion_Auto) || (acctSettings.ipVersion == IpVersion_Auto_PreferV6))
   {
      DebugLog(<< "SipAccountImpl::autoIpVersion(): IP version set to: " << acctSettings.ipVersion << " in SIP account settings for network transport: " << activeNetworkTransport);
      return true;
   }

   DebugLog(<< "SipAccountImpl::autoIpVersion(): IP version set to: " << acctSettings.ipVersion << " in SIP account settings not set to auto for: " << activeNetworkTransport);
   return false;
}

bool SipAccountImpl::initRegistration(const SipAccountSettings acctSettings)
{
   InfoLog(<< "SipAccountImpl::initRegistration(): mHandle=" << mHandle << " mEnabled=" << mEnabled);

   if (acctSettings.useRegistrar)
   {
      sendRegisterOrProbeIfRequiredImpl(false);
   }
   else
   {
      bool useTunnel = false;
#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
      if (acctSettings.tunnelConfig.useTunnel && acctSettings.tunnelConfig.tunnelType == TunnelType_StrettoTunnel)
      {
         useTunnel = true;
      }
#endif

      SipAccountStatusChangedEvent args;
      args.accountStatus = SipAccountStatusChangedEvent::Status_Registered;
      args.signalingStatusCode = 0;
      args.failureRetryAfterSecs = 0;
      args.reason = SipAccountStatusChangedEvent::Reason_None;
      args.accountBindingIpAddress = "";
      args.transportType = SipAccountTransport_Unknown;
      args.ipVersionInUse = IpVersion_Auto;
      args.localContactBinding = "";  // Ensure that binding is not populated for no-registrar configuration
 
      if (useTunnel)
      {
         args.accountStatus = SipAccountStatusChangedEvent::Status_Registering;
         fireAccountStatusEvent(args, true);
      }
      else
      {
         fireAccountStatusEvent(args);

         // Ensure that onRegistrationSuccess is sent after account status registered for consistency with normal registration configuration.
         // With tunnel configuration this would be after the tunnel has connected
         handleNoRegistrarConfiguration();
      }

      scheduleDelayedLicenseFraudCheck();
   }

   return true;
}

void SipAccountImpl::sendRegisterOrProbeIfRequiredImpl(bool updateExpiry)
{
   DebugLog(<< "SipAccountImpl::sendRegisterOrProbeIfRequiredImpl(): mHandle=" << mHandle << " mEnabled=" << mEnabled);
   if (mDisabling || mShutdown)
   {
      DebugLog(<< "SipAccountImpl::sendRegisterOrProbeIfRequiredImpl(): Ignoring request as " << (mShutdown ? "shutdown" : "disabling") << " is in progress");
      return;
   }

   assert(mDum.get());
   assert(mDum->getMasterProfile().get());
   if (!mDum.get() || (mDum.get() && !mDum->getMasterProfile().get()))
   {
      DebugLog(<< "SipAccountImpl::sendRegisterOrProbeIfRequiredImpl(): Ignoring request as DUM is in invalid state");
      return;
   }

   try
   {
      resip::SharedPtr<SipMessage> reg = makeRegistration(mDum->getMasterProfile()->getDefaultFrom());
      assert(reg->header(h_Contacts).size() == 1);
      adornMessage(*this, *reg);
      if (updateExpiry)
      {
         UInt32 expires = calcPostNetworkChangeRegistrationExpires();
         reg->header(h_Expires).value() = expires;
      }
      reg->header(h_Contacts).front().uri().host() = ""; // Will be filled in with new network values
      reg->header(h_Contacts).front().uri().port() = 0;

      if (autoIpVersion() &&
          ((mTcpTransport != NULL && mTcpTransportV6 != NULL && mTcpTransport->isEnabled() && mTcpTransportV6->isEnabled()) ||
          (mUdpTransport != NULL && mUdpTransportV6 != NULL && mUdpTransport->isEnabled() && mUdpTransportV6->isEnabled()) ||
          (mTlsTransport != NULL && mTlsTransportV6 != NULL && mTlsTransport->isEnabled() && mTlsTransportV6->isEnabled())))
      {
         DebugLog(<< "SipAccountImpl::sendRegisterOrProbeIfRequiredImpl(): initiating probe");

         mRegAfterConnProbe.mOptionsV4.reset();
         mRegAfterConnProbe.mOptionsV6.reset();
         mRegAfterConnProbe.mRegister = reg;
         mRegAfterConnProbe.mTimer.cancel();
         mRegAfterConnProbe.mTimer.expires_from_now(PROBE_TIMEOUT_MSECS);
         mRegAfterConnProbe.mMockDelayTimer.cancel();
         mRegAfterConnProbe.mMockDelayTimer.expires_from_now(mRegAfterConnProbe.mProbeMockDelayMsecs);
         mRegAfterConnProbe.mResponseV4.reset();
         mRegAfterConnProbe.mResponseV6.reset();

         mRegAfterConnProbe.mOptionsV4 = makeV4V6ConnectivityProbe(reg->header(h_RequestLine).uri(), resip::V4);
         mRegAfterConnProbe.mOptionsV6 = makeV4V6ConnectivityProbe(reg->header(h_RequestLine).uri(), resip::V6);

         if (mCurrentIpVersion == IpVersion_Auto_PreferV6)
         {
            sendProbe(IpVersion_V6);
            sendProbe(IpVersion_V4);
         }
         else
         {
            sendProbe(IpVersion_V4);
            sendProbe(IpVersion_V6);
         }

         probeStatus(SipNetworkProbeStatusChangedEvent::Status_Probing, SipNetworkProbeStatusChangedEvent::Reason_ProbesSent);
         mRegAfterConnProbe.mTimer.async_wait(this, REG_AFTER_CONN_PROBE_TIMER_ID, NULL);
      }
      else
      {
         mDum->send(reg);
      }
   }
   catch (DumException& e)
   {
      ErrLog(<< "SipAccountImpl::sendRegisterOrProbeIfRequiredImpl(): dum exception: " << e.what());
   }
}

void SipAccountImpl::sendProbe(CPCAPI2::SipAccount::IpVersion ipVersion)
{
   assert(!mDisabling);
   assert(!mShutdown);
   assert(mDum.get());
   assert(mDum->getMasterProfile().get());
   assert((ipVersion == IpVersion_V4) || (ipVersion == IpVersion_V6));

   if (mDisabling || mShutdown || !mDum.get() || (mDum.get() && !mDum->getMasterProfile().get()))
   {
      DebugLog(<< "SipAccountImpl::sendProbe(): Ignoring request as in invalid state");
      return;
   }

   if ((ipVersion == IpVersion_V4) && mRegAfterConnProbe.mOptionsV4)
   {
      if ((mRegAfterConnProbe.mMockDelayedProbeVersion == IpVersion_V4) && (mRegAfterConnProbe.mProbeMockDelayMsecs > 0)) // && (mRegAfterConnProbe.mProbeMockDelayMsecs < PROBE_TIMEOUT_MSECS)))
      {
         mRegAfterConnProbe.mMockDelayTimer.async_wait(this, PROBE_AFTER_MOCK_DELAY_TIMER_ID, NULL);
      }
      else
      {
         mDum->send(mRegAfterConnProbe.mOptionsV4);
      }
   }
   else if ((ipVersion == IpVersion_V6) && mRegAfterConnProbe.mOptionsV6)
   {
      if ((mRegAfterConnProbe.mMockDelayedProbeVersion == IpVersion_V6) && (mRegAfterConnProbe.mProbeMockDelayMsecs > 0)) // && (mRegAfterConnProbe.mProbeMockDelayMsecs < PROBE_TIMEOUT_MSECS)))
      {
         mRegAfterConnProbe.mMockDelayTimer.async_wait(this, PROBE_AFTER_MOCK_DELAY_TIMER_ID, NULL);
      }
      else
      {
         mDum->send(mRegAfterConnProbe.mOptionsV6);
      }
   }
   else
   {
      DebugLog(<< "SipAccountImpl::sendProbe(): Ignoring invalid probe request for " << ipVersion);
   }
}

void SipAccountImpl::sendProbeAfterMockDelay()
{
   assert(!mDisabling);
   assert(!mShutdown);
   assert(mDum.get());
   assert(mDum->getMasterProfile().get());
   assert((mRegAfterConnProbe.mMockDelayedProbeVersion == IpVersion_V4) || (mRegAfterConnProbe.mMockDelayedProbeVersion == IpVersion_V6));

   if (mDisabling || mShutdown || !mDum.get() || (mDum.get() && !mDum->getMasterProfile().get()))
   {
      DebugLog(<< "SipAccountImpl::sendProbeAfterMockDelay(): Ignoring request as in invalid state");
      return;
   }

   if ((mRegAfterConnProbe.mMockDelayedProbeVersion == IpVersion_V4) && mRegAfterConnProbe.mOptionsV4)
   {
      mDum->send(mRegAfterConnProbe.mOptionsV4);
   }
   else if ((mRegAfterConnProbe.mMockDelayedProbeVersion == IpVersion_V6) && mRegAfterConnProbe.mOptionsV6)
   {
      mDum->send(mRegAfterConnProbe.mOptionsV6);
   }
   else
   {
      DebugLog(<< "SipAccountImpl::sendProbeAfterMockDelay(): Ignoring invalid probe request for " << mRegAfterConnProbe.mMockDelayedProbeVersion);
   }
}

void SipAccountImpl::sendRegisterOrProbeIfRequired()
{
   DebugLog(<< "SipAccountImpl::sendRegisterOrProbeIfRequired(): mHandle=" << mHandle << " mEnabled=" << mEnabled);
   mNetworkChangeDelayTimer.cancel();

   if (!mDisabling && !mShutdown)
   {
      // Workaround for invalid 503 No Route To Host error on iOS, seen with probing and registration messages.
      // Intermittently, probes are lost or sent out of order, after network changes. Errors only seen in specific
      // circumstances when changing from Cell V4/V6 to WiFi V6 network. Adding a delay reduces the occurrence of
      // the errors. Reproducible with UDP and TCP. Issue was isolated to scenarios requiring that the V6 addresses
      // are not immediately available on the network interface.
	   //
      // Give transport layer some time to setup.
      if (autoIpVersion() &&
          ((mTcpTransport && mTcpTransportV6 && mTcpTransport->isEnabled() && mTcpTransportV6->isEnabled()) ||
           (mUdpTransport && mUdpTransportV6 && mUdpTransport->isEnabled() && mUdpTransportV6->isEnabled()) ||
           (mTlsTransport && mTlsTransportV6 && mTlsTransport->isEnabled() && mTlsTransportV6->isEnabled())))
      {
         probeStatus(SipNetworkProbeStatusChangedEvent::Status_Probing, SipNetworkProbeStatusChangedEvent::Reason_NetworkChangeDelay);
      }
      mPhone->getSdkModuleThread().postMS(resip::resip_safe_bind(&SipAccountImpl::sendRegisterOrProbeIfRequiredImpl, this, true), 500);
   }
}

void SipAccountImpl::resetNetworkInterfaces()
{
   InfoLog(<< "SipAccountImpl::resetNetworkInterfaces()");
   const SipAccountSettings acctSettings = getSettings();

   if (acctSettings.useRegistrar)
   {
      // TODO: Handle scenarios where network interface reset is required but for a configuration requiring registration.
      // Cannot simply trigger onRegistrationSuccess without a successful registration.
   }
   else
   {
      handleNoRegistrarConfiguration();
   }
}

void SipAccountImpl::newCallActive()
{
   const SipAccountSettings acctSettings = getSettings();
   if (acctSettings.useRegistrar)
   {
      if (freezeSipDnsCache() != kSuccess)
      {
         ErrLog(<< "Failed to freeze SIP DNS cache");
      }
   }
   else
   {
      // don't freeze the cache if useRegistrar = false -- otherwise our DNS cache
      // for SIP might become very out of date. At least with useRegistrar = true every
      // time a registration refresh occurrs with DNS TTL having expired, we'd get
      // updated DNS records.
   }
}

void SipAccountImpl::allCallsEnded()
{
   if (unfreezeSipDnsCache() != kSuccess)
   {
      ErrLog(<< "Failed to unfreeze SIP DNS cache");
   }

   if (mResetDnsAfterAllCallsEnded)
   {
      mResetDnsAfterAllCallsEnded = false;

      DebugLog(<< "Resetting DNS -- this operation was previously deferred during a network change due to an active call. SIP registration will also refresh");
      mStack->resetDns(); // Clears white list, mark manager, dns cache and triggers reInit on dns stub

      // note that if there is a new preferred IP address for the call server,
      // the BYE will be sent to the old whitelisted IP address and the re-REGISTER
      // will be sent to the new IP address. It's conceivable this could cause the BYE
      // to be rejected if the re-REGISTER is received first. Items that should result
      // in lowering the chance of this being a problem:
      //    - The SIP registration with the previous call server should not yet have expired (i.e. it might 
      //      still accept the BYE and propagate it)
      //    - The re-REGISTER won't be sent out until a DNS query and response has been received,
      //      making it leave the wire later than the BYE
      requestRegistrationRefresh(0);
   }

   NetworkTransport transport = mNetworkChangeManagerIf->networkTransport();
   if (mWwanTransportHoldOverCall)
   {
      mWwanTransportHoldOverCall = false;

      if (transport == TransportWiFi)
      {
         // if we instead call refreshRegOnNetworkChange(..), we have to somehow deal with both
         // a) doing a reset on the network transports, to remove the fixed bind interface,
         //    i.e. mUdpTransportV6->updateNetworkInterfaceObj(resip::Data());
         //    then mUdpTransportV6->reset();
         // b) ensuring the BYE for the just ended call going out
         //
         // since the above is not trivial, and we already have a means of re-enabling after a disable,
         // use that instead.

         mReEnableOnShutdown = true;
         disable();
      }
   }
}

void SipAccountImpl::handleNoRegistrarConfiguration()
{
   InfoLog(<< "SipAccountImpl::handleNoRegistrarConfiguration()");
   const SipAccountSettings acctSettings = getSettings();

   if (acctSettings.useRegistrar == false)
   {
      resip::NameAddr localContact;
      resip::Tuple sipServerTuple;
      if (serverIpForDisabledUseRegistrar(sipServerTuple))
      {
         mSipServerIpFromLastReg.reset(new resip::Tuple(sipServerTuple));
      }

      InfoLog(<< "SipAccountImpl::handleNoRegistrarConfiguration(): useRegistrar disabled, server IP " << sipServerTuple.presentationFormat());

      mNetworkInterfaceForLastSuccessfulRegistration = localContact; // Empty
      SipRegistrationSuccessEvent successEvt;
      successEvt.server = sipServerTuple;
      successEvt.localContact = localContact;
      successEvt.overrideSourceIpSignalling = getOverrideSourceIpForNAT64().c_str();

      for (std::vector<SipAccountAwareFeature*>::iterator itFeat = mAccountAwareFeatures.begin(); itFeat != mAccountAwareFeatures.end(); ++itFeat)
      {
         (*itFeat)->onRegistrationSuccess(successEvt);
      }
   }
}

static jsonrpc::CPCAPI2::SipAccount::SipAccountStatus convert(SipAccountStatusChangedEvent::Status status)
{
   switch (status)
   {
      case SipAccountStatusChangedEvent::Status_Registered:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountStatus::Registered;
      case SipAccountStatusChangedEvent::Status_Unregistered:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountStatus::Unregistered;
      case SipAccountStatusChangedEvent::Status_Registering:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountStatus::Registering;
      case SipAccountStatusChangedEvent::Status_Unregistering:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountStatus::Unregistering;
      case SipAccountStatusChangedEvent::Status_WaitingToRegister:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountStatus::WaitingToRegister;
      case SipAccountStatusChangedEvent::Status_Refreshing:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountStatus::Refreshing;
      default:
         ErrLog(<< "Enum mismatch");
         return jsonrpc::CPCAPI2::SipAccount::SipAccountStatus::Unregistered;
      }
}

static jsonrpc::CPCAPI2::SipAccount::SipAccountTransportType convert(SipAccount::SipAccountTransportType transportType)
{
   switch (transportType)
   {
      case SipAccountTransport_Auto:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountTransportType::Auto;
      case SipAccountTransport_UDP:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountTransportType::Udp;
      case SipAccountTransport_TCP:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountTransportType::Tcp;
      case SipAccountTransport_TLS:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountTransportType::Tls;
      case SipAccountTransport_Unknown:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountTransportType::Unknown;
      default:
         ErrLog(<< "Enum mismatch");
         return jsonrpc::CPCAPI2::SipAccount::SipAccountTransportType::Unknown;
   }
}

static jsonrpc::CPCAPI2::SipAccount::SipAccountReason convert(SipAccountStatusChangedEvent::Reason reason)
{
   switch (reason)
   {
      case SipAccountStatusChangedEvent::Reason_None:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountReason::None;
      case SipAccountStatusChangedEvent::Reason_No_Network:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountReason::NoNetwork;
      case SipAccountStatusChangedEvent::Reason_Restricted_Network:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountReason::RestrictedNetwork;
      case SipAccountStatusChangedEvent::Reason_New_Network:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountReason::NewNetwork;
      case SipAccountStatusChangedEvent::Reason_Server_Response:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountReason::ServerResponse;
      case SipAccountStatusChangedEvent::Reason_Local_Timeout:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountReason::LocalTimeout;
      case SipAccountStatusChangedEvent::Reason_NetworkDeregistered:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountReason::NetworkDeregistered;
      case SipAccountStatusChangedEvent::Reason_Tunnel_Failure:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountReason::TunnelFailure;
      case SipAccountStatusChangedEvent::Reason_Dns_Lookup:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountReason::DnsLookup;
      case SipAccountStatusChangedEvent::Reason_Transport_Protocol_Mismatch:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountReason::TransportProtocolMismatch;
      case SipAccountStatusChangedEvent::Reason_No_Route_To_Host:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountReason::NoRouteToHost;
      case SipAccountStatusChangedEvent::Reason_TLS_Cipher_Mismatch:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountReason::TlsCipherMismatch;
      case SipAccountStatusChangedEvent::Reason_Domain_Locked:
         return jsonrpc::CPCAPI2::SipAccount::SipAccountReason::DomainLocked;
      default:
         ErrLog(<< "Enum mismatch");
         return jsonrpc::CPCAPI2::SipAccount::SipAccountReason::None;
   }
}



void SipAccountImpl::fireAccountStatusEvent(SipAccountStatusChangedEvent& args, bool checkPreviousStatus)
{
   if ((checkPreviousStatus == false) || (mLastAccountStatusFired != args.accountStatus))
   {
      DebugLog(<< "SipAccountImpl::fireAccountStatusEvent(): account: " << mHandle << " status: " << args.accountStatus);
      mInterface->fireEvent(cpcEvent(SipAccountHandler, onAccountStatusChanged), mHandle, args);

      jsonrpc::CPCAPI2::SipAccount::SipAccountStatusChangedEvent jrpcEvt;
      jrpcEvt.sipAccountHandle = mHandle;
      jrpcEvt.accountStatus = convert(args.accountStatus);
      jrpcEvt.reason = convert(args.reason);
      jrpcEvt.signalingStatusCode = args.signalingStatusCode;
      jrpcEvt.signalingResponseText = args.signalingResponseText;
      jrpcEvt.accountBindingIpAddress = args.accountBindingIpAddress;
      jrpcEvt.rinstance = args.rinstance;
      jrpcEvt.transportType = convert(args.transportType);
      jrpcEvt.responseTimeMs = args.responseTimeMs;

      mPhone->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::SipAccount::SipAccountEvents::SipAccountDotOnAccountStatusChanged), std::move(jrpcEvt.marshal())));

      mLastAccountStatusFired = args.accountStatus;
   }
}

void SipAccountImpl::sendRegistrationStatusUpdateAfterNetworkChange()
{
   if (mAccountRegState == ARS_WaitingToRegister)
   {
      AR_STATE_TO(ARS_Registering);
      SipAccountStatusChangedEvent args;
      args.reason = SipAccountStatusChangedEvent::Reason_New_Network;
      args.accountStatus = SipAccountStatusChangedEvent::Status_Registering;
      args.signalingStatusCode = 0;
      args.failureRetryAfterSecs = 0;
      args.signalingResponseText = "";
      args.accountBindingIpAddress = "";
      args.transportType = SipAccountTransport_Unknown;
      args.ipVersionInUse = IpVersion_Auto;
      args.localContactBinding = "";
      fireAccountStatusEvent(args, true);
   }
   else if (mAccountRegState == ARS_Registered)
   {
      AR_STATE_TO(ARS_Refreshing);
      SipAccountStatusChangedEvent args;
      args.reason = SipAccountStatusChangedEvent::Reason_New_Network;
      args.accountStatus = SipAccountStatusChangedEvent::Status_Refreshing;
      args.signalingStatusCode = 0;
      args.failureRetryAfterSecs = 0;
      args.signalingResponseText = "";
      args.accountBindingIpAddress = "";
      args.transportType = SipAccountTransport_Unknown;
      args.ipVersionInUse = IpVersion_Auto;
      args.localContactBinding = "";
      fireAccountStatusEvent(args);
   }
   else if (mAccountRegState == ARS_Refreshing)
   {
      // Do not change state.  Send a new event to the application to distinguish
      // between the existing refreshing state and a new network-change stimulated
      // refreshing state that we find ourselvees in now.
      SipAccountStatusChangedEvent args;
      args.reason = SipAccountStatusChangedEvent::Reason_New_Network;
      args.accountStatus = SipAccountStatusChangedEvent::Status_Refreshing;
      args.signalingStatusCode = 0;
      args.failureRetryAfterSecs = 0;
      args.signalingResponseText = "";
      args.accountBindingIpAddress = "";
      args.transportType = SipAccountTransport_Unknown;
      args.ipVersionInUse = IpVersion_Auto;
      args.localContactBinding = "";
      fireAccountStatusEvent(args);
   }
}

bool SipAccountImpl::enableTransports()
{
   assert(mUdpTransport == NULL && mUdpTransportV6 == NULL && mTcpTransport == NULL && mTcpTransportV6 == NULL && mTlsTransport == NULL && mTlsTransportV6 == NULL);
   const SipAccountSettings acctSettings = getSettings();

   if ((acctSettings.sipTransportType == SipAccountTransport_Auto) || (acctSettings.sipTransportType == SipAccountTransport_UDP))
   {
      bool attemptV4 = false;
      if (isIpv4CapableVersion(acctSettings.ipVersion))
      {
         mUdpTransport = createTransport(resip::UDP, resip::V4, acctSettings);
         attemptV4 = true;
      }

      bool attemptV6 = false;
      if (isIpv6CapableVersion(acctSettings.ipVersion))
      {
         attemptV6 = true;
         mUdpTransportV6 = createTransport(resip::UDP, resip::V6, acctSettings);
      }

      if ((attemptV4 && mUdpTransport == NULL) || (attemptV6 && mUdpTransportV6 == NULL))
      {
         ErrLog(<< "SipAccountImpl::enableTransports(): Error creating UDP transports: mUdpTransport: " << mUdpTransport << " mUdpTransportV6: " << mUdpTransportV6);
         if (mUdpTransport == NULL && mUdpTransportV6 == NULL)
         {
            ErrLog(<< "SipAccountImpl::enableTransports(): Error creating UDP transports");
            return false;
         }
      }

      DebugLog(<< "SipAccountImpl::enableTransports(): UDP transports created: mUdpTransport: " << mUdpTransport << " mUdpTransportV6: " << mUdpTransportV6);
   }

   if ((acctSettings.sipTransportType == SipAccountTransport_Auto) || (acctSettings.sipTransportType == SipAccountTransport_TCP))
   {
      bool attemptV4 = false;
      if (isIpv4CapableVersion(acctSettings.ipVersion))
      {
         mTcpTransport = dynamic_cast<TcpTransport*>(createTransport(resip::TCP, resip::V4, acctSettings));
         attemptV4 = true;
      }

      bool attemptV6 = false;
      if (isIpv6CapableVersion(acctSettings.ipVersion))
      {
         mTcpTransportV6 = dynamic_cast<TcpTransport*>(createTransport(resip::TCP, resip::V6, acctSettings));
         attemptV6 = true;
      }

      if ((attemptV4 && mTcpTransport == NULL) || (attemptV6 && mTcpTransportV6 == NULL))
      {
         ErrLog(<< "SipAccountImpl::enableTransports(): Error creating TCP transports: mTcpTransport: " << mTcpTransport << " mTcpTransportV6: " << mTcpTransportV6);
         if (mTcpTransport == NULL && mTcpTransportV6 == NULL)
         {
            ErrLog(<< "SipAccountImpl::enableTransports(): Error creating TCP transports");
            return false;
         }
      }

      DebugLog(<< "SipAccountImpl::enableTransports(): TCP transports created: mTcpTransport: " << mTcpTransport << " mTcpTransportV6: " << mTcpTransportV6);
   }

#ifdef USE_SSL
   try
   {
      if (acctSettings.excludeEncryptedTransports == false)
      {
         if ((acctSettings.sipTransportType == SipAccountTransport_Auto) || (acctSettings.sipTransportType == SipAccountTransport_TLS))
         {
            bool attemptV4 = false;
            if (isIpv4CapableVersion(acctSettings.ipVersion))
            {
               mTlsTransport = dynamic_cast<TlsTransport*>(createTransport(resip::TLS, resip::V4, acctSettings));
               attemptV4 = true;
            }

            bool attemptV6 = false;
            if (isIpv6CapableVersion(acctSettings.ipVersion))
            {
               mTlsTransportV6 = dynamic_cast<TlsTransport*>(createTransport(resip::TLS, resip::V6, acctSettings));
               attemptV6 = true;
            }

            if ((attemptV4 && mTlsTransport == NULL) || (attemptV6 && mTlsTransportV6 == NULL))
            {
               ErrLog(<< "SipAccountImpl::enableTransports(): Error creating TLS transports: mTlsTransport: " << mTlsTransport << " mTlsTransportV6: " << mTlsTransportV6);
               if (mTlsTransport == NULL && mTlsTransportV6 == NULL)
               {
                  ErrLog(<< "SipAccountImpl::enableTransports(): Error creating TLS transports");
                  return false;
               }
            }

            DebugLog(<< "SipAccountImpl::enableTransports(): TLS transports created: mTlsTransport: " << mTlsTransport << " mTlsTransportV6: " << mTlsTransportV6);
         }
      }
   }
   catch (std::invalid_argument& e)
   {
      ErrLog(<< "SipAccountImpl::enableTransports(): invalid argument exception: " << e.what());
      fireAccountError(e.what());
      return false;
   }
   catch (std::runtime_error& e)
   {
      ErrLog(<< "SipAccountImpl::enableTransports(): runtime error exception: " << e.what());
      fireAccountError(e.what());
      return false;
   }
   catch (BaseSecurity::Exception& e)
   {
      ErrLog(<< "SipAccountImpl::enableTransports(): security exception: " << e.what());
      fireAccountError(e.what());
      return false;
   }
   catch(std::exception& e)
   {
      ErrLog(<< "SipAccountImpl::enableTransports(): exception: " << e.what());
      fireAccountError(e.what());
      return false;
   }
#endif

   updateTransportStatus();

   return true;
}

void SipAccountImpl::setMessageDecoratorForNAT64IfRequired(resip::SharedPtr<resip::MasterProfile>& masterProfile)
{
   if (!getOverrideSourceIpForNAT64().empty())
   {
      const SipAccountSettings acctSettings = getSettings();
      resip::Uri contactUri;
      contactUri.user() = acctSettings.username;
      contactUri.host() = getOverrideSourceIpForNAT64().c_str();
      contactUri.port() = NAT64_FAKE_PORT;

      sockaddr_in6 dns64prefix;
      int prefix_len = 0;
      if (resip::IpSynthTools::getDns64Prefix(mStack->getDnsStub(), dns64prefix, prefix_len, mNat64DiscoveryFailure))
      {
         DebugLog(<< "SipAccountImpl::setMessageDecoratorForNAT64IfRequired(): NAT64 driving outbound decorator for host " << contactUri.host());
         mMessageDecorator->setHostPort(contactUri.host(), contactUri.port());
         mMessageDecorator->setUseAlias(false);
         mMessageDecorator->setOverrideContact(true);
         mMessageDecorator->setDns64Prefix(dns64prefix, prefix_len);
      }
      else
      {
         ErrLog(<< "SipAccountImpl::setMessageDecoratorForNAT64IfRequired(): getOverrideSourceIpForNAT64 not empty but getDns64Prefix returns false");
      }
   }
}

bool SipAccountImpl::shouldDiscoverDns64Again() const
{
   int successCacheType = 0;
   const SipAccountSettings acctSettings = getSettings();

   if (acctSettings.enableNat64Support &&
       mNat64DiscoveryFailure &&
       mStack->getDnsStub()->cacheEntryExists(successCacheType, resip::RR_AAAA::getRRType()))
   {
      // we previously attempted to do a DNS64 discovery query, and it timed out;
      // however, we have seen at least one response from the DNS server for an AAAA query

      return true;
   }

   return false;
}

bool SipAccountImpl::shouldDiscoverDns64Again(const resip::SipMessage& response) const
{
   if (response.getReceivedTransport() &&
       response.getReceivedTransport()->ipVersion() == resip::V6)
   {
      if (hasIp4ReceivedParam(response))
      {
         if (shouldDiscoverDns64Again())
         {
            return true;
         }
      }
   }
   return false;
}

bool SipAccountImpl::hasIp4ReceivedParam(const resip::SipMessage& msg) const
{
   if (!msg.header(resip::h_Vias).empty())
   {
      resip::Vias viaHeaders = msg.header(resip::h_Vias);
      resip::Via viaHeader = viaHeaders.front();

      if (viaHeader.exists(resip::p_received))
      {
         if (resip::DnsUtil::isIpV4Address(viaHeader.param(resip::p_received)))
         {
            return true;
         }
      }
   }

   return false;
}

void SipAccountImpl::refreshRegForDns64Rediscovery()
{
   InfoLog(<< "Will refresh reg via fake network change to rediscover NAT64");
   mNat64DiscoveryFailure = false;
   // as IpSynthTools is currently shared between multiple SipAccountImpl instances, we need to be
   // careful that we don't reset it unless absolutely necessary, as this could result in repeat
   // DNS64 discovery queries, which can bog down the main SDK thread.
   if (IpSynthTools::lastNat64DiscoveryFailed())
   {
      IpSynthTools::reset();
   }

   NetworkTransport transport = mNetworkChangeManagerIf->networkTransport();
   NetworkChangeEvent fakeEvent;
   fakeEvent.networkTransport = transport;

   mPhone->getSdkModuleThread().post(resip::resip_safe_bind(&SipAccountImpl::refreshRegOnNetworkChange, this, fakeEvent));
}

void SipAccountImpl::updateTransportStatus()
{
   const SipAccountSettings acctSettings = getSettings();
   mCurrentNetworkTransport = mNetworkChangeManagerIf->networkTransport();
   mCurrentIpVersion = acctSettings.ipVersion;
   mStack->setPreferIpV6((mCurrentIpVersion == IpVersion_V6 || mCurrentIpVersion == IpVersion_Auto_PreferV6) ? true : false);

   if (mCurrentIpVersion == IpVersion_V4)
   {
      DebugLog(<< "SipAccountImpl::updateTransportStatus(): Current IP Version is V4");
      if (mUdpTransport) mUdpTransport->setEnabled(true);
      if (mUdpTransportV6) mUdpTransportV6->setEnabled(false);
      if (mTcpTransport) mTcpTransport->setEnabled(true);
      if (mTcpTransportV6) mTcpTransportV6->setEnabled(false);
      if (mTlsTransport) mTlsTransport->setEnabled(true);
      if (mTlsTransportV6) mTlsTransportV6->setEnabled(false);
   }
   else if (mCurrentIpVersion == IpVersion_V6)
   {
      DebugLog(<< "SipAccountImpl::updateTransportStatus(): Current IP Version is V6");
      if (mUdpTransport) mUdpTransport->setEnabled(false);
      if (mUdpTransportV6) mUdpTransportV6->setEnabled(true);
      if (mTcpTransport) mTcpTransport->setEnabled(false);
      if (mTcpTransportV6) mTcpTransportV6->setEnabled(true);
      if (mTlsTransport) mTlsTransport->setEnabled(false);
      if (mTlsTransportV6) mTlsTransportV6->setEnabled(true);
   }
   else
   {
      DebugLog(<< "SipAccountImpl::updateTransportStatus(): Current IP Version is " << ((mCurrentIpVersion == IpVersion_Auto) ? "Auto Prefer V4" : "Auto Prefer V6"));
      if (mUdpTransport) mUdpTransport->setEnabled(true);
      if (mUdpTransportV6) mUdpTransportV6->setEnabled(true);
      if (mTcpTransport) mTcpTransport->setEnabled(true);
      if (mTcpTransportV6) mTcpTransportV6->setEnabled(true);
      if (mTlsTransport) mTlsTransport->setEnabled(true);
      if (mTlsTransportV6) mTlsTransportV6->setEnabled(true);
   }
}

bool SipAccountImpl::canCallBeSavedDuringNetworkChange() const
{
   //
   // ====================================================================================================================================================
   //                 |                                                      CONFIGURATION CHANGES                                                       |
   // ================|===================================================================================================================================
   //                 | V4-V4 | V4-V6 | V4-AV4| V4-AV6|| V6-V4 | V6-V6 | V6-AV4| V6-AV6|| AV4-V4| AV4-V6|AV4-AV4|AV4-AV6|| AV6-V4| AV6-V6|AV6-AV4|AV6-AV6|
   // ================|===============================||===============================||===============================||================================
   // NETWORK CHANGES |       |       |       |       ||       |       |       |       ||       |       |       |       ||       |       |       |       |
   // ================|===============================||===============================||===============================||================================
   // V4 to V4        |   Y   |  Nard |  Nad  |  Nad  ||  N/A  |  N/A  |  N/A  |  N/A  ||  Nrd  |  Nrd  |   Y   |   Y   ||  Nrd  |  Nrd  |   Y   |   Y   |
   // ================|===============================||===============================||===============================||================================
   // V4 to V6        |   X   |  Nar  |  Na   |  Na   ||  N/A  |  N/A  |  N/A  |  N/A  ||  Nrd  |  Nrd  |   Y   |   Y   ||  Nrd  |  Nrd  |   Y   |   Y   |
   // ================|===============================||===============================||===============================||================================
   // V4 to Dual      |   Y   |  Nard |  Nad  |  Nad  ||  N/A  |  N/A  |  N/A  |  N/A  ||  Nrd  |  Nrd  |   Y   |   Y   ||  Nrd  |  Nrd  |   Y   |   Y   |
   // ================|===============================||===============================||===============================||================================
   // V6 to V4        |  N/A  |  N/A  |  N/A  |  N/A  ||  Nar  |   X   |  Na   |  Na   ||  Nrd  |  Nrd  |   Y   |   Y   ||  Nrd  |  Nrd  |   Y   |   Y   |
   // ================|===============================||===============================||===============================||================================
   // V6 to V6        |  N/A  |  N/A  |  N/A  |  N/A  ||  Nard |   Y   |  Nad  |  Nad  ||  Nrd  |  Nrd  |   Y   |   Y   ||  Nrd  |  Nrd  |   Y   |   Y   |
   // ================|===============================||===============================||===============================||================================
   // V6 to Dual      |  N/A  |  N/A  |  N/A  |  N/A  ||  Nard |   Y   |  Nad  |  Nad  ||  Nrd  |  Nrd  |   Y   |   Y   ||  Nrd  |  Nrd  |   Y   |   Y   |
   // ================|===============================||===============================||===============================||================================
   // Dual to V4      |   Y   |  Nard |  Nad  |  Nad  ||  Nar  |   X   |  Na   |  Na   ||  Nrd  |  Nrd  |   Y   |   Y   ||  Nrd  |  Nrd  |   Y   |   Y   |
   // ================|===============================||===============================||===============================||================================
   // Dual to V6      |   X   |  Nar  |  Na   |  Na   ||  Nard |   Y   |  Nad  |  Nad  ||  Nrd  |  Nrd  |   Y   |   Y   ||  Nrd  |  Nrd  |   Y   |   Y   |
   // ================|===============================||===============================||===============================||================================
   // Dual to Dual    |   Y   |  Nard |  Nad  |  Nad  ||  Nard |   Y   |  Nad  |  Nad  ||  Nrd  |  Nrd  |   Y   |   Y   ||  Nrd  |  Nrd  |   Y   |   Y   |
   // ================|===================================================================================================================================
   //
   // There are additional permutations not specified in the table, based on the configuration
   // changes for the transport protocol type (udp/tcp/tls/auto), dns responses, sip network
   // elements (e.g. sbc, media servers, etc.), routing elements (e.g. NATs, NAT64, etc.), sip
   // and routing elements of the remote-party and scenarios involving a "no registrar" configuration
   //
   // Basic reset involves resetting the DNS cache and contact header, closing transport connections,
   // probing if required and re-registration
   //
   // Y - the call can be saved, basic reset required immediately
   //
   // N - the call could possibly be saved, it would require the creation or deletion of the
   //     transports or enable-disable of the account, or if it's worthwhile then a delay in
   //     the enable-disable of the account until the call has ended - this would not be
   //     worthwhile if media cannot flow for the call
   //
   //     a - add r - remove d - delay
   //     - Na - need to add a new ip version transport, network no longer supports the previous ip version,
   //       call cannot be saved, proceed with enable-disable
   //     - Nad - need to add a new ip version transport, network still supports the previous ip version,
   //       call can be saved, basic reset required immediately, but proceed with enable-disable only
   //       after the call has ended
   //     - Nar - configured ip version transport changed so we need to add a transport and remove a transport,
   //       network no longer supports the previous ip version, call cannot be saved, proceed with enable-disable
   //     - Nard - configured ip version transport changed so we need to add a transport and remove a transport,
   //       network still supports the previous ip version, call can be saved, basic reset required immediately,
   //       but proceed with enable-disable only after the call has ended
   //     - Nrd - need to remove an existing ip version transport, network still supports the previous ip version,
   //       call can be saved, basic reset required immediately, but proceed with enable-disable only after the
   //       call has ended
   //
   // X - the call cannot be saved, as the new network would not be supported
   //
   // N/A - the scenario is not possible, call could not have been made using an IP version that was not enabled
   //

   return true;
}

bool SipAccountImpl::handleIpVersionChange()
{
   const SipAccountSettings acctSettings = getSettings();

   if (!(mEnabled && shouldReInitDueToIpVersionChange()))
   {
      DebugLog(<< "SipAccountImpl::handleIpVersionChange() IP version change reinit not required");
      mCurrentNetworkTransport = mNetworkChangeManagerIf->networkTransport();
      mCurrentIpVersion = acctSettings.ipVersion;
      mStack->setPreferIpV6((mCurrentIpVersion == IpVersion_V6 || mCurrentIpVersion == IpVersion_Auto_PreferV6) ? true : false);
      return false;
   }

   DebugLog(<< "SipAccountImpl::handleIpVersionChange() reset transports for switch between IPv4 <-> IPv6");

   closeTransportConnections();
   mDum->getSipStack().resetTransports();
   updateTransportStatus();

   if (acctSettings.useRegistrar == false)
   {
      handleNoRegistrarConfiguration();
      return true;
   }

   AR_STATE_TO(ARS_WaitingToRegister);

   mRportReregTryCount = 0;

   if (mClientRegistration.isValid())
   {
      DebugLog(<< "Stopping existing client registration");
      mClientRegistration->stopRegistering();
      mClientRegistration = ClientRegistrationHandle::NotValid();
   }
   else
   {
      DebugLog(<< "Stopping existing registration - currently not registered");
      // Need to call stop registration to delete registration sessions that were not successful as such
      // we do not have a valid client registration handle. Otherwise we end up sending re-registrations
      // from stale dialogs.
      mDum->stopRegistration();
   }

   sendRegistrationStatusUpdateAfterNetworkChange();
   sendRegisterOrProbeIfRequired();

   return true;
}

void SipAccountImpl::closeTransportConnections()
{
   DebugLog(<< "closeTransportConnections " << mHandle);

   if (mTcpTransport != NULL)
   {
      mStack->closeTransportConnections(mTcpTransport);
   }

   if (mTcpTransportV6 != NULL)
   {
      mStack->closeTransportConnections(mTcpTransportV6);
   }

   if (mTlsTransport != NULL)
   {
      mStack->closeTransportConnections(mTlsTransport);
   }

   if (mTlsTransportV6 != NULL)
   {
      mStack->closeTransportConnections(mTlsTransportV6);
   }

   if (mDum)
   {
      mDum->getMasterUserProfile()->clearClientOutboundFlowTuple();
   }
}

void SipAccountImpl::setFakeResponse(bool enable, cpc::string method, int responseCode, cpc::string responseReason, int warningCode)
{
   if (mDum)
   {
      resip::MethodTypes methodType;
      if (method == "REGISTER")
      {
         methodType = REGISTER;
         (void)methodType;

         // Uncomment if required for testing
         // mDum->getSipStack().setFakeResponse(enable, methodType, responseCode, responseReason.c_str(), warningCode);
      }
   }
}

void SipAccountImpl::resetTransports()
{
   if (mDum)
   {
      DebugLog(<< "SipAccountImpl::resetTransports(): account: " << mHandle);
      closeTransportConnections();
      try
      {
         mDum->getSipStack().resetTransports();
      }
      catch (Transport::Exception& e)
      {
         mReEnableOnShutdown = true;
         ErrLog(<< "refreshRegOnNetworkChangeImpl(): transport error " << e.getMessage() << " - re-enabling account.");
         return;
      }
      updateTransportStatus();
   }
}

void SipAccountImpl::populateNameServer(resip::DnsStub::DnsSettings& dnsSettings) const
{
   const SipAccountSettings acctSettings = getSettings();

   dnsSettings.overrideDefaultSipPort = acctSettings.defaultSipPort;
   dnsSettings.overrideDefaultSipsPort = acctSettings.defaultSipsPort;
   dnsSettings.includeSystemDnsServers = false;
   dnsSettings.includeIpv6SystemDnsServers = !(acctSettings.ipVersion == IpVersion_V4);
   dnsSettings.nameServers.clear();

   if (acctSettings.nameServers.size() > 0)
   {
      for (cpc::vector<cpc::string>::const_iterator it = acctSettings.nameServers.begin(); it != acctSettings.nameServers.end(); ++it)
      {
         resip::Tuple t(it->c_str(), 53, resip::UDP);
         dnsSettings.nameServers.push_back(t.toGenericIPAddress());
      }
   }
   else if (acctSettings.additionalNameServers.size() > 0)
   {
      dnsSettings.includeSystemDnsServers = true;
      for (cpc::vector<cpc::string>::const_iterator it = acctSettings.additionalNameServers.begin(); it != acctSettings.additionalNameServers.end(); ++it)
      {
         resip::Tuple t(it->c_str(), 53, resip::UDP);
         dnsSettings.nameServers.push_back(t.toGenericIPAddress());
      }
   }
}

int SipAccountImpl::sendOptionsMessage(const cpc::string target)
{
   resip::SharedPtr<SipMessage> options = mDum->makeOutOfDialogRequest(resip::NameAddr(target.c_str()), resip::OPTIONS);
   mDum->send(options);

   return kSuccess;
}

int SipAccountImpl::setSkipResetTransportOnNetworkChange(bool skip)
{
   mSkipResetTransportOnNetworkChange = skip;
   return kSuccess;
}

void SipAccountImpl::startRetryTimer(int interval)
{
   // Cancel existing timer
   stopRetryTimer();

   // Set the timeout value
   mRegistrationRetry.interval = interval;
   mRegistrationRetry.mTimer.expires_from_now(mRegistrationRetry.interval * 1000);
   InfoLog(<< "Start registration retry timer with interval " << mRegistrationRetry.interval * 1000 << " for mHandle=" << mHandle);

   // Start the timer
   mRegistrationRetry.mTimer.async_wait(this, RETRY_TIMER_ID, 0);
}

void SipAccountImpl::stopRetryTimer()
{
   InfoLog(<< "Stop registration retry timer mHandle=" << mHandle);

   // Stop the timer
   mRegistrationRetry.mTimer.cancel();
   mRegistrationRetry.interval = 0;
}

void SipAccountImpl::setHibernationState(bool hibernation)
{
#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)
   if (mTseTunnelInfo.tunnel_handle != 0)
   {
      TseTunnelManager::instance()->setHibernationState(mTseTunnelInfo.tunnel_handle, hibernation);
   }
#endif
}

void SipAccountImpl::onTimer(unsigned short timerId, void* appState)
{
   if (timerId == RETRY_TIMER_ID)
   {
      InfoLog(<< "onTimer(RETRY_TIMER_ID) mHandle=" << mHandle);
      stopRetryTimer();

      if (mEnabled)
      {
         if (mClientRegistration.isValid())
         {
            refreshRegOnNetworkChangeImpl();
         }
         else
         {
            // We got 408/503 after enable() so retry
            const SipAccountSettings acctSettings = getSettings();
            if (acctSettings.useRegistrar)
            {
               try
               {
                  resip::SharedPtr<SipMessage> reg = makeRegistration(mDum->getMasterProfile()->getDefaultFrom());
                  adornMessage(*this, *reg);
                  mDum->send(reg);

                  AR_STATE_TO(ARS_Registering);
                  SipAccountStatusChangedEvent args;
                  args.reason = SipAccountStatusChangedEvent::Reason_None;
                  args.accountStatus = SipAccountStatusChangedEvent::Status_Registering;
                  args.signalingStatusCode = 0;
                  args.failureRetryAfterSecs = 0;
                  args.signalingResponseText = "";
                  args.accountBindingIpAddress = "";
                  args.transportType = SipAccountTransport_Unknown;
                  args.ipVersionInUse = IpVersion_Auto;
                  args.localContactBinding = "";
                  fireAccountStatusEvent(args, true);
               }
               catch (DumException& e)
               {
                  ErrLog(<< "SipAccountImpl::onTimer(): dum exception: " << e.what());
                  return;
               }
            }
         }
      }
      else
      {
         // Load settings based on the current network
         enable();
      }
   }
   else if (timerId == REG_AFTER_CONN_PROBE_TIMER_ID)
   {
      InfoLog(<< "onTimer(REG_AFTER_CONN_PROBE_TIMER_ID) mHandle=" << mHandle << ", send register as probe has timed-out");
      probeStatus(SipNetworkProbeStatusChangedEvent::Status_Completed, SipNetworkProbeStatusChangedEvent::Reason_ProbeTimeout);
      sendRegisterAfterConnProbe();
   }
   else if (timerId == PROBE_AFTER_MOCK_DELAY_TIMER_ID)
   {
      InfoLog(<< "onTimer(PROBE_AFTER_MOCK_DELAY_TIMER_ID) mHandle=" << mHandle << ", send probe as delay has timed-out");
      sendProbeAfterMockDelay();
   }
   else if (timerId == NETWORK_CHANGE_DELAY_TIMER_ID)
   {
      InfoLog(<< "onTimer(NETWORK_CHANGE_DELAY_TIMER_ID) mHandle=" << mHandle);
      sendRegisterOrProbeIfRequiredImpl();
   }
   else if (timerId == DISABLE_TIMER_ID)
   {
      InfoLog(<< "onTimer(DISABLE_TIMER_ID) mHandle=" << mHandle);
      if (!mShutdown)
      {
         forceShutDown();
      }
   }
   else if (timerId == LICENSE_FRAUD_CHECK_TIMER_ID)
   {
      if (mLicenseFraudCheckTimer != NULL)
      {
         delete mLicenseFraudCheckTimer;
         mLicenseFraudCheckTimer = NULL;
      }
      doDelayedLicenseFraudCheck();
   }
   else
   {
      assert(0);
   }
}

void SipAccountImpl::forceShutDown()
{
   InfoLog(<< "SipAccountImpl::forceShutDown() mHandle=" << mHandle);
   if (mDum.get() != NULL)
   {
      AR_STATE_TO(ARS_NotRegistered);

      mShutdown = true;
      mDisabling = false;

      mDisableTimer.cancel();
      mNetworkChangeDelayTimer.cancel();

#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
      if (mStrettoTunnel)
      {
         dynamic_cast<StrettoTunnelTransport*>(mStrettoTunnel)->setHandler(0);
         mStrettoTunnel = 0;
      }
#endif

      mDum->forceShutdown(this);

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)
      // Delete the TSCF tunnel
      if (mTseTunnelInfo.tunnel_handle)
      {
         TseTunnelManager::instance()->destroyTunnel(mTseTunnelInfo.tunnel_handle);
         mTseTunnelInfo.tunnel_handle = 0;
      }
#endif

      mClientRegistration = ClientRegistrationHandle::NotValid();
      mPhone->getSdkModuleThread().unregisterEventHandler(this);
      mInterface->postToSdkThread(new resip::ReadCallbackNoOp());

      deleteDum();
   }
}

bool findLocalContact(const resip::NameAddrs& responseContacts, const resip::NameAddrs& myContacts, resip::NameAddr& matchingContactFromResponseContacts, resip::NameAddr& matchingContactFromMyContacts)
{
   resip::NameAddrs::const_iterator itResp = responseContacts.begin();
   for (; itResp != responseContacts.end(); ++itResp)
   {
      resip::NameAddrs::const_iterator itMy = myContacts.begin();
      for (; itMy != myContacts.end(); ++itMy)
      {
         if (itResp->uri().exists(p_rinstance) && itMy->uri().exists(p_rinstance))
         {
            if (itResp->uri().param(p_rinstance) == itMy->uri().param(p_rinstance))
            {
               matchingContactFromMyContacts = *itMy;
               matchingContactFromResponseContacts = *itResp;
               return true;
            }
         }
      }
   }

   return false;
}

class FakeContactFeature : public resip::DumFeature
{
public:
   FakeContactFeature(resip::DialogUsageManager& dum, const resip::Uri& pubIp) : DumFeature(dum, dum.dumOutgoingTarget()), mPubIp(pubIp) {}
   virtual ~FakeContactFeature() {}

   virtual ProcessingResult process(Message* msg)
   {
      OutgoingEvent* event = dynamic_cast<OutgoingEvent*>(msg);

      if (!event) return FeatureDone;

      SipMessage& sipmsg = *event->message();

      //check for valid parameter name and value
      if (sipmsg.isRequest())
      {
         if (sipmsg.header(h_RequestLine).method() == INVITE)
         {
            sipmsg.header(h_Contacts).push_back(NameAddr("<sip:1373@************:5060>"));
         }
      }
      return FeatureDone;
   }

private:
   resip::Uri mPubIp;
};

/// Called when registration succeeds or each time it is sucessfully refreshed.
void SipAccountImpl::onSuccess(ClientRegistrationHandle h, const SipMessage& response)
{
   StackLog(<< "SipAccountImpl::onSuccess(): account: " << mHandle);
   if (shouldDiscoverDns64Again(response))
   {
      refreshRegForDns64Rediscovery();
   }

   stopRetryTimer();

   mCurrentFailureRegistrationIntervalSeconds = getSettings().minimumRegistrationIntervalSeconds;
   mInitialFailureRegistrationRetryAttemptsPending = REGISTRATION_FAILURE_RETRY_LIMIT_PRIOR_TO_INCREASE;

   if (mDisabling)
   {
      WarningLog(<< "Ignoring registration success because SipAccountImpl is in disabling state mHandle=" << mHandle);
      return;
   }

   if (!response.exists(h_Contacts) || !response.exists(h_Vias))
   {
      return;
   }

   // notify Account-aware features of the IP of the SIP server
   {
      SipRegistrationDialogSuccessEvent successEvt;
      successEvt.response = response;

      for (std::vector<SipAccountAwareFeature*>::iterator itFeat = mAccountAwareFeatures.begin(); itFeat != mAccountAwareFeatures.end(); ++itFeat)
      {
         (*itFeat)->onRegistrationDialogSuccess(successEvt);
      }
   }

   if (mAccountRegState == ARS_WaitingToRegister)
   {
      // .jjg. this is a fake state transition; from the perspective of the SDK, WaitingToRegister and Registering are the same,
      // but the contract with the app is that we get to Registered via Registering
      AR_STATE_TO(ARS_Registering);
      SipAccountStatusChangedEvent args;
      args.accountStatus = SipAccountStatusChangedEvent::Status_Registering;
      args.signalingStatusCode = 0;
      args.failureRetryAfterSecs = 0;
      args.reason = SipAccountStatusChangedEvent::Reason_None;
      args.transportType = SipAccountTransport_Unknown;
      args.accountBindingIpAddress = "";
      args.ipVersionInUse = IpVersion_Auto;
      args.localContactBinding = "";

      fireAccountStatusEvent(args, true);
   }

   setTlsConnectionInfo();

   bool rfc5626ObCompliant = response.exists(h_Requires) && response.const_header(h_Requires).find(Token(Symbols::Outbound));
   bool draftObCompliant = response.exists(h_Supporteds) && response.const_header(h_Supporteds).find(Token(Symbols::Outbound));

   // ref: https://www.resiprocate.org/DUM_Client_Outbound_Support
   resip::SharedPtr<UserProfile> up = mDum->getMasterUserProfile();
   if (up->clientOutboundEnabled() && !(rfc5626ObCompliant || draftObCompliant))
   {
      // Outbound is enabled locally, but the server doesn't support it (and doesn't return a 439);
      // disable local outbound support since it means we likely registered with an unroutable Contact
      up->setRegId(0);
      up->clientOutboundEnabled() = false;
      if (!getSettings().useInstanceId)
      {
         up->setInstanceId(resip::Data::Empty);
      }
      mDum->getMasterProfile()->removeSupportedOptionTag(resip::Token(resip::Symbols::Path), resip::UNKNOWN);
      mDum->getMasterProfile()->removeSupportedOptionTag(resip::Token(resip::Symbols::Outbound), resip::UNKNOWN);
      InfoLog(<< "Outbound enabled locally, but server does not support it; re-registering with no outbound support");
      AR_STATE_TO(ARS_DoingOutboundReReg);
      mRportModifiedContact = h->myContacts().front();
      InfoLog(<< "using local binding: " << mRportModifiedContact);
      if (!mRportModifiedContact.uri().exists(p_transport) && !mRportModifiedContact.uri().host().empty())
      {
         mRportModifiedContact.uri().param(p_addTransport);
      }
      mRportModifiedContact.clearUnknownParameters();
      mRportModifiedContact.removeParametersExcept();
      h->removeMyBindings(false);
      return;
   }

   bool contactFound = false;
   resip::Via topVia = response.header(h_Vias).back();
   bool sipReceivedParamExists = topVia.exists(p_received);
   bool sipRportParamExists = topVia.exists(p_rport) && (topVia.param(p_rport).port() > 0);
   NameAddrs contacts = response.header(h_Contacts);
   NameAddrs::iterator itContacts = contacts.begin();
   NameAddr localContact;

   if (!getSettings().useRport && sipRportParamExists)
   {
      WarningLog(<< "useRport disabled, but received rport param anyways; ignoring");
   }

   if (getSettings().useRport && sipReceivedParamExists && sipRportParamExists)
   {
      DebugLog(<< "useRport enabled and response includes received and rport parameters");
      for (; itContacts != contacts.end(); ++itContacts)
      {
         if (resip::isEqualNoCase(itContacts->uri().host(),
            topVia.param(p_received)))
         {
            if (itContacts->uri().port() == topVia.param(p_rport).port())
            {
               // Adding an additional check to prevent invalid match from a stale binding, as after a network change
               // we will not reset the tranport port, we could have bindings from prior to the network changes. So if
               // this is the first response we should go through a mandatory rport de-registration and re-registration,
               // to ensure that the internal IP address binding is cleaned up.
               if (mRportReregTryCount == 0)
               {
                  DebugLog(<< "Contact binding found with matching host and port: " << itContacts->uri() << " but ignoring as have not sent an rport de-registration yet");
               }
               else
               {
                  DebugLog(<< "Contact binding found with matching host and port: " << itContacts->uri());
                  localContact = (*itContacts);
                  contactFound = true;

                  fireRegistrationRportUpdateEvent(itContacts->uri().host().c_str(), itContacts->uri().port());
                  break;
               }
            }
         }
      }
   }
   else if (getSettings().useRport && sipRportParamExists && contacts.size() >= 1)
   {
      // Common scenario dealing with multiple bindings when handling multiple network changes. Go through the binding list
      // to see if any match can be found with rport. If a port match is found and this is the first response, go through a
      // mandatory rport de-registration and re-registration to ensure that the internal IP address binding is cleanup up.
      DebugLog(<< "useRport enabled but response includes only the rport parameter");
      int bPortMatches = 0;
      for (NameAddrs::iterator i = contacts.begin(); i != contacts.end(); ++i)
      {
         if (i->uri().port() == topVia.param(p_rport).port())
         {
            if (mRportReregTryCount == 0)
            {
               DebugLog(<< "Contact binding found with matching port: " << i->uri() << " but ignoring as have not sent an rport de-registration yet");
            }
            else
            {
               DebugLog(<< "Contact binding found with matching port: " << i->uri());
               bPortMatches++;
               localContact = (*i);
               itContacts = i;
               contactFound = true;

               fireRegistrationRportUpdateEvent("", i->uri().port());
            }
         }
      }

      if (contactFound && (bPortMatches > 1))
      {
         DebugLog(<< "Found binding matching the rport: " << topVia.param(p_rport).port() << " but could not isolate to a single contact");
         contactFound = false;
         itContacts = contacts.end();
      }
   }
   else
   {
      DebugLog(<< "useRport disabled or response does not include the rport parameter");
      // we can't use rport, because we can't reliably find our Contact
      sipRportParamExists = false;
   }

   mClientRegistration = h;

   if (!contactFound && sipRportParamExists && (mRportReregTryCount == 0) && ((h->myContacts()).size() > 0))
   {
      DebugLog(<< "Matching contact binding not found but rport parameter exists");
      AR_STATE_TO(ARS_DoingRportReReg);
      mRportReregTryCount++;
      resip::NameAddr matchingFromResponse;
      resip::NameAddr matchingFromMy;
      mRportModifiedContact = resip::NameAddr(); // reset to empty
      if (findLocalContact(contacts, h->myContacts(), matchingFromResponse, matchingFromMy))
      {
         mRportModifiedContact = matchingFromMy;
      }
      else
      {
         if (h->myContacts().size() > 0)
         {
            mRportModifiedContact = h->myContacts().front();
         }
      }
      h->removeMyBindings(false);

      if (sipReceivedParamExists)
      {
         mRportModifiedContact.uri().host() = topVia.param(p_received);
      }
      else if (mRportModifiedContact.uri().host().empty())
      {
         if (contacts.size() == 1)
         {
            mRportModifiedContact.uri().host() = contacts.front().uri().host();
         }
         else
         {
            mRportModifiedContact.uri().host() = matchingFromResponse.uri().host();
         }
      }
      mRportModifiedContact.uri().port() = topVia.param(p_rport).port();
      if (!mRportModifiedContact.uri().exists(p_transport) && !mRportModifiedContact.uri().host().empty())
      {
         mRportModifiedContact.uri().param(p_addTransport);
      }
      mRportModifiedContact.clearUnknownParameters();
      mRportModifiedContact.removeParametersExcept();
      mDum->getSipStack().addAlias(mRportModifiedContact.uri().host(), mRportModifiedContact.uri().port());
   }
   else
   {
      mRportReregTryCount = 0;

      if (!contactFound)
      {
         if (contacts.size() == 1 &&
             contacts.front().uri().exists(p_transport) && Tuple::toTransport(contacts.front().uri().param(p_transport)) == resip::TCP)
         {
            // No need to check for TLS as well, as it would also use TCP for the transport parameter (RFC3261 26.2.2)
            localContact = contacts.front();
            contactFound = true;
            itContacts = contacts.begin();
            DebugLog(<< "Matching contact binding based on TCP transport as response contains only one contact binding");
         }
      }

      if (!contactFound)
      {
         DebugLog(<< "Contact has not been found, atleast unset the override host and port to prevent use of stale local contacts");
         mDum->getMasterProfile()->unsetOverrideHostAndPort();
      }

      if (!getOverrideSourceIpForNAT64().empty() &&
          response.getSource().ipVersion() == resip::V6)
      {
         sockaddr_in6 dns64prefix;
         int dns64prefixlen = 0;
         if (resip::IpSynthTools::getDns64Prefix(mStack->getDnsStub(), dns64prefix, dns64prefixlen, mNat64DiscoveryFailure))
         {
            if (resip::IpSynthTools::isSynthesizedIPv6Address(dns64prefix, dns64prefixlen, response.getSource()))
            {
               const SipAccountSettings acctSettings = getSettings();
               resip::Uri contactUri;
               contactUri.user() = acctSettings.username;
               contactUri.host() = getOverrideSourceIpForNAT64().c_str();
               contactUri.port() = NAT64_FAKE_PORT;

               DebugLog(<< "setOverrideHostAndPort for NAT64 to " << contactUri);
               mDum->getMasterProfile()->setOverrideHostAndPort(contactUri);
            }
            else
            {
               DebugLog(<< "NAT64 detected, but this appears to be a real IPv6 server");
            }
         }
      }

      if (contactFound && (itContacts != contacts.end()))
      {
         DebugLog(<< "Matching contact binding found: " << itContacts->uri());
         resip::Uri hostPort(itContacts->uri());
         mDum->getMasterProfile()->setOverrideHostAndPort(hostPort);

         // .jjg. this is here for testing
         //if (mSettings.useFakeContact)
         //{
         //   mDum->addOutgoingFeature(resip::SharedPtr<DumFeature>(new FakeContactFeature(*mDum, hostPort)));
         //}
      }

      std::string overrideSourceIpForNAT64 = getOverrideSourceIpForNAT64();

      mSipServerIpFromLastReg.reset(new resip::Tuple(response.getSource()));

      if (!contactFound && getSettings().useRinstance && getSettings().useRegistrar && (contacts.size() >= 1))
      {
         resip::NameAddr matchingFromResponse;
         resip::NameAddr matchingFromMy;
         if (findLocalContact(contacts, h->myContacts(), matchingFromResponse, matchingFromMy))
         {
            contactFound = true;
            localContact = matchingFromResponse;
            DebugLog(<< "SipAccountImpl::onSuccess(): account: " << mHandle << " populating contact from client registration as last resort: " << localContact.uri());

            resip::Uri hostPort(localContact.uri());
            mDum->getMasterProfile()->setOverrideHostAndPort(hostPort);
         }
      }

      if (!contactFound)
      {
         // TODO: Do we want to continue passing the onRegistrationSuccess callbacks with an empty local binding.
         // Might be a false-positive for the user, as the account would show up enabled, but incoming calls would
         // possibly be missed, if we don't have a confirmed binding with the registrar.
         WarningLog(<< "SipAccountImpl::onSuccess(): account: " << mHandle << " could not isolate our contact binding in successful registration");
      }

      if (mAccountRegState != ARS_Registered)
      {
         AR_STATE_TO(ARS_Registered);

         SipAccountStatusChangedEvent args;
         args.accountStatus = SipAccountStatusChangedEvent::Status_Registered;
         args.signalingStatusCode = response.header(h_StatusLine).statusCode();
         args.failureRetryAfterSecs = 0;
         args.signalingResponseText = response.getReason() != NULL ? response.getReason()->c_str() : "";
         args.accountBindingIpAddress = localContact.uri().getAorNoReally().c_str();
         args.reason = SipAccountStatusChangedEvent::Reason_None;
         args.tlsInfo = mTlsConnInfo;
         args.transportType = SipAccountTransport_UDP; // default
         args.rinstance = mDum->getMasterProfile()->getRinstance().c_str();
         args.serverIpAddress = response.getSource().presentationFormat().c_str();
         args.serverPort = response.getSource().getPort();
         args.localContactBinding = localContact.uri().toString().c_str();
         args.responseTimeMs = dynamic_cast<resip::AppDialogSet*>(h->getAppDialogSet().get())->getResponseTimeMs();

         resip::TransportType transport = response.getReceivedTransport()->transport();
         mTransportFromLastReg = response.getReceivedTransport();
         mNetworkTransportFromLastReg = mNetworkChangeManagerIf->networkTransport();

         if (transport == resip::TLS)
         {
            args.transportType = SipAccountTransport_TLS;
         }
         else if (transport == resip::TCP)
         {
            args.transportType = SipAccountTransport_TCP;
         }
         args.ipVersionInUse = (IpVersion)response.getReceivedTransport()->ipVersion();

         DebugLog(<< "args.accountBindingIpAddress = " << args.accountBindingIpAddress);

         if (response.exists(resip::h_CallId))
         {
            args.callId = response.header(resip::h_CallId).value().c_str();
         }

         args.localPort = response.getReceivedTransport()->port();

         try
         {
            const resip::Data ipHost = IpHelpers::getPreferredLocalIpAddress();
            if (resip::DnsUtil::isIpAddress(ipHost))
            {
               args.localIpAddress = ipHost.c_str();
            } else {
               DebugLog(<< "Cannot convert local hostname: " << ipHost.c_str() << " into IP address");
            }
         }
         catch (const resip::DnsUtil::Exception& exc)
         {
            DebugLog(<< "Failed to get local IP address: " << exc);
         }

         DebugLog(<< "callId=" << args.callId << " localIP=" << args.localIpAddress << " localPort=" << args.localPort);
         fireAccountStatusEvent(args);

         scheduleDelayedLicenseFraudCheck();
      }

#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
      if (mStrettoTunnel)
      {
         DebugLog(<< "SipAccountImpl::onSuccess: Stretto Tunnel does not have server IP");
         mSipServerIpFromLastReg.reset();
      }
#endif

      if (response.exists(h_Vias))
      {
         mNetworkInterfaceForLastSuccessfulRegistration.uri().host() = response.header(h_Vias).back().sentHost().c_str();
         mNetworkInterfaceForLastSuccessfulRegistration.uri().port() = response.header(h_Vias).back().sentPort();
      }
      else
      {
         resip::NameAddr emptyAddr;
         mNetworkInterfaceForLastSuccessfulRegistration = emptyAddr;
      }

      DebugLog(<< "SipAccountImpl::onSuccess: mSipServerIpFromLastReg = " << *mSipServerIpFromLastReg << ", localContact = " << localContact << ", overrideSourceIpForNAT64 = " <<  overrideSourceIpForNAT64 << " response-interface: " << mNetworkInterfaceForLastSuccessfulRegistration.uri().getAorNoReally().c_str() << " mRportModifiedContact: " << mRportModifiedContact.uri().getAorNoReally().c_str());

      // notify Account-aware features of the IP of the SIP server
      SipRegistrationSuccessEvent successEvt;
      successEvt.server = response.getSource();
      successEvt.localContact = localContact;
      successEvt.overrideSourceIpSignalling = overrideSourceIpForNAT64.c_str();

      for (std::vector<SipAccountAwareFeature*>::iterator itFeat = mAccountAwareFeatures.begin(); itFeat != mAccountAwareFeatures.end(); ++itFeat)
      {
         (*itFeat)->onRegistrationSuccess(successEvt);
      }

#if (CPCAPI2_BRAND_SIP_REG_EVENT_MODULE == 1)
      if (getSettings().enableRegeventDeregistration == true)
      {
         mRegistrationCallId = "";
         if (response.exists(resip::h_CallId))
         {
            mRegistrationCallId = response.header(resip::h_CallId).value().c_str();
         }
         // Subscribe to reg event package using own URI
         SipRegEvent::SipRegEventManagerInterface* regEventIf = dynamic_cast<SipRegEvent::SipRegEventManagerInterface*>(SipRegEvent::SipRegEventManager::getInterface(mPhone));
         mRegEventSubscriptionHandle = regEventIf->createSubscription(mHandle);
         SipRegEvent::SipRegEventSubscriptionSettings regSubSettings;
         regEventIf->applySubscriptionSettings(mRegEventSubscriptionHandle, regSubSettings);
         regEventIf->start(mRegEventSubscriptionHandle);
      }
#endif
   }
}

bool SipAccountImpl::serverIpForDisabledUseRegistrar(resip::Tuple& outTuple) const
{
   // this method will block while it synchornously waits for DNS responses

   StackLog(<< "SipAccountImpl::serverIpForDisabledUseRegistrar");

#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
   if (mStrettoTunnel)
   {
      DebugLog(<< "SipAccountImpl::serverIpForDisabledUseRegistrar: Stretto Tunnel does not have server IP");
      return false;
   }
#endif

   Utils::DnsClient dnsClient(mStack->getDnsStub()->dnsSettings());

   int port = resip::Symbols::DefaultSipPort;
   std::stringstream target;
   target << "_sip.";
   SipAccountSettings settings = getSettings();
   resip::TransportType resipTransport;
   switch (settings.sipTransportType)
   {
      case SipAccountTransport_UDP:
         target << "_udp.";
         resipTransport = resip::UDP;
         break;
      case SipAccountTransport_TCP:
         target << "_tcp.";
         resipTransport = resip::TCP;
         break;
      case SipAccountTransport_TLS:
         target << "_tls.";
         resipTransport = resip::TLS;
         port = resip::Symbols::DefaultSipsPort;
         break;
      case SipAccountTransport_Auto:
      default:
         target << "_udp.";
         resipTransport = resip::UDP;
         break;
   }

   resip::NameAddr obProxy = (mDum->getMasterProfile()->hasOutboundProxy() ?
                              mDum->getMasterProfile()->getOutboundProxy() :
                              mDum->getMasterProfile()->getDefaultFrom());

   if (obProxy.uri().port() > 0)
   {
      port = obProxy.uri().port();
   }

   resip::Data obProxyHost = obProxy.uri().host();
   if (resip::DnsUtil::isIpAddress(obProxyHost))
   {
      outTuple = resip::Tuple(obProxyHost, port, resipTransport);
      DebugLog(<< "SipAccountImpl::serverIpForDisabledUseRegistrar: outbound proxy tuple: " << outTuple);
      return true;
   }
   else
   {
      target << obProxyHost;

      resip::Data targetForHostLookup(obProxyHost);
      Utils::DnsSrvRecord dnsSrvRecord = dnsClient.getDnsSrvRecord(resip::Protocol::Sip, target.str().c_str());
      if (!dnsSrvRecord.target.empty())
      {
         targetForHostLookup = dnsSrvRecord.target;
         port = dnsSrvRecord.port;
         DebugLog(<< "SipAccountImpl::serverIpForDisabledUseRegistrar: srv record host target: " << targetForHostLookup << ":" << port);
      }

      Utils::DnsAorAAAARecord hostRecord = dnsClient.getDnsAorAAAARecord(targetForHostLookup, settings.ipVersion);
      if (hostRecord.valid)
      {
         outTuple = resip::Tuple(hostRecord.ipAddr, resipTransport);
         outTuple.setPort(port);
         DebugLog(<< "SipAccountImpl::serverIpForDisabledUseRegistrar: host record is valid, outbound tuple: " << outTuple);
         return true;
      }
   }

   return false;
}

void SipAccountImpl::fireAccountError(const cpc::string& errorText)
{
   SipAccount::ErrorEvent args;
   args.errorText = errorText;

   mInterface->fireEvent(cpcEvent(SipAccountHandler, onError), mHandle, args);

   jsonrpc::CPCAPI2::SipAccount::SipAccountErrorEvent jrpcEvt;
   jrpcEvt.errorText = errorText;

   mPhone->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::SipAccount::SipAccountEvents::SipAccountDotOnError), std::move(jrpcEvt.marshal())));
}

// Called when all of my bindings have been removed
void SipAccountImpl::onRemoved(ClientRegistrationHandle h, const SipMessage& response)
{
   InfoLog(<< "SipAccountImpl::onRemoved() mHandle=" << mHandle << " got " << response.brief());
   if (!h.isValid())
   {
      WarningLog(<< "SipAccountImpl::onRemoved(): ignoring callback as passed in registration handle is invalid");
      return;
   }
   if (mClientRegistration.isValid() && (mClientRegistration.get() != h.get()))
   {
      WarningLog(<< "SipAccountImpl::onRemoved(): ignoring callback with handle mismatch mClientRegistration handle: " << mClientRegistration.get() << " callback handle: " << h.get());
      return;
   }

   if (mAccountRegState == ARS_DoingNetworkChangeReReg)
   {
      AR_STATE_TO(ARS_NotRegistered);
      return;
   }

   if (mAccountRegState == ARS_DoingRportReReg || mAccountRegState == ARS_DoingOutboundReReg)
   {
      if (!getOverrideSourceIpForNAT64().empty())
      {
         if (mAccountRegState == ARS_DoingRportReReg)
         {
            // if we're re-REGISTERing due to rport, the decorator should NOT override our contact (because what we get from rport should be used)
            mMessageDecorator->setOverrideContact(false);
         }

         if (mAccountRegState == ARS_DoingOutboundReReg)
         {
            mRportModifiedContact.uri().host() = getOverrideSourceIpForNAT64().c_str();
         }
      }

      AR_STATE_TO(ARS_Registering);
      h->addBinding(mRportModifiedContact);
   }
   else
   {
      AR_STATE_TO(ARS_NotRegistered);
      mEnabled = false;
      if (mRestrictions.find(NetworkRestriction) != mRestrictions.end())
      {
         mUnregisteredEvent.reason = getAccountNtkRestrictionReason();
      }
      else
      {
         mUnregisteredEvent.reason = SipAccountStatusChangedEvent::Reason_None;
      }
      mUnregisteredEvent.accountStatus = SipAccountStatusChangedEvent::Status_Unregistered;
      mUnregisteredEvent.signalingStatusCode = response.header(h_StatusLine).statusCode();
      mUnregisteredEvent.failureRetryAfterSecs = 0;
      mUnregisteredEvent.signalingResponseText = response.getReason() != NULL ? response.getReason()->c_str() : "";
      mUnregisteredEvent.accountBindingIpAddress = "";
      mUnregisteredEvent.transportType = SipAccountTransport_Unknown;
      mUnregisteredEvent.ipVersionInUse = IpVersion_Auto;
      mUnregisteredEvent.localContactBinding = "";

      if(!mDisabling)
      {
         fireAccountStatusEvent(mUnregisteredEvent);
      }
   }
}

void SipAccountImpl::onRegistrationConnectionTerminated(ClientRegistrationHandle h) // Fake outbound
{
   const SipAccountSettings acctSettings = getSettings();
   DebugLog(<< "SipAccountImpl::onRegistrationConnectionTerminated() for mHandle=" << mHandle);

   if (mClientRegistration.isValid() &&
       mClientRegistration.get() == h.get() &&
       (acctSettings.sipTransportType == SipAccountTransport_TCP || acctSettings.sipTransportType == SipAccountTransport_TLS) &&
       acctSettings.autoRetryOnTransportDisconnect)
   {
      InfoLog(<< "SipAccountImpl::onRegistrationConnectionTerminated() refreshing mHandle=" << mHandle);
      mClientRegistration->requestRefresh();
   }
}

// return true if this function handled the event
bool SipAccountImpl::handle5xx(const resip::SipMessage& msg)
{
   if (!msg.isResponse())
   {
      return false;
   }

   InfoLog(<< "SipAccountImpl::handle5xx() mHandle=" << mHandle << " got " << msg.brief());

   if (mDisabling)
   {
      WarningLog(<< "Ignoring registration 5xx handling because SipAccountImpl is in disabling state mHandle=" << mHandle);
      return false;
   }

   /*
   ((msg.header(h_StatusLine).responseCode() == 503 && msg.getReceivedTransport()) ||
   msg.header(h_StatusLine).responseCode() == 408 ||
   msg.header(h_StatusLine).responseCode() == 504 || // see http://tools.ietf.org/html/draft-bakker-sipping-3gpp-ims-xml-body-handling-06
   (msg.header(h_StatusLine).responseCode() == 407 && !msg.exists(h_ProxyAuthenticates)))) // for ALU
   */

   if (containsResponseType(getSettings().reRegisterOnResponseTypes, msg))
   {
      if (msg.getReceivedTransport() == NULL && msg.header(h_StatusLine).responseCode() != 408)
      {
         return false;
      }
      if (msg.header(h_StatusLine).responseCode() == 407 && msg.exists(h_ProxyAuthenticates))
      {
         return false;
      }

      SipAccountStatusChangedEvent args;
      args.reason = SipAccountStatusChangedEvent::Reason_None;

      AR_STATE_TO(ARS_WaitingToRegister);

      int retryAfter = calcNextRegFailureRetryInterval();
      if (msg.exists(h_RetryAfter) && msg.header(h_RetryAfter).isWellFormed())
      {
         retryAfter = msg.header(h_RetryAfter).value();
      }
      else if (msg.header(h_StatusLine).responseCode() == 504 || msg.header(h_StatusLine).responseCode() == 407)
      {
         // in the IMS case, we want to start with a clean slate -- no Auth headers (i.e. don't simply do a reg refresh)
         const SipAccountSettings acctSettings = getSettings();

         retryAfter = acctSettings.minimumRegistrationIntervalSeconds;
         mCurrentFailureRegistrationIntervalSeconds = acctSettings.minimumRegistrationIntervalSeconds;
         mInitialFailureRegistrationRetryAttemptsPending = REGISTRATION_FAILURE_RETRY_LIMIT_PRIOR_TO_INCREASE;
         if (mStack != NULL)
         {
            mStack->clearDnsCache();
            mStack->clearWhitelist();
         }
      }

      // there is a registration problem, so un-freeze the DNS cache immediately; don't wait for any dangling
      // calls to timeout firts (i.e. don't wait for SipAccountImpl::allCallsEnded() to execute)
      if (unfreezeSipDnsCache() != kSuccess)
      {
         ErrLog(<< "Failed to unfreeze SIP DNS cache");
      }

      InfoLog(<< "Retry registration due to 503/408 from wire in " << retryAfter << " seconds");
      startRetryTimer(retryAfter);
      args.reason = ((msg.getReceivedTransport() == NULL) ? SipAccountStatusChangedEvent::Reason_Local_Timeout : SipAccountStatusChangedEvent::Reason_Server_Response);
      args.accountStatus = SipAccountStatusChangedEvent::Status_WaitingToRegister;
      args.signalingStatusCode = msg.header(h_StatusLine).statusCode();
      args.failureRetryAfterSecs = retryAfter;
      args.signalingResponseText = msg.getReason() != NULL ? msg.getReason()->c_str() : "";
      args.accountBindingIpAddress = "";
      args.transportType = SipAccountTransport_Unknown;
      args.tlsInfo = mTlsConnInfo;
      args.ipVersionInUse = IpVersion_Auto;
      args.localContactBinding = "";

      fireAccountStatusEvent(args);
      return true;
   }
   return false;
}

// call on Retry-After failure.
// return values: -2 = ignore, -1 = fail, 0 = retry immediately, N = retry in N seconds
int SipAccountImpl::onRequestRetry(ClientRegistrationHandle h, int retrySeconds, const SipMessage& response)
{
   if (shouldDiscoverDns64Again())
   {
      refreshRegForDns64Rediscovery();
   }

   InfoLog(<< "SipAccountImpl::onRequestRetry() mHandle=" << mHandle << " handling internally generated message: " << response.brief());

   // Possible to get dangling failures (408/503) from previous requests when we go through multiple network changes,
   // as we no longer go through a full enable-disable.
   // TODO: Can anything else be reset upon network change to avoid this scenario
   if (!h.isValid())
   {
      WarningLog(<< "SipAccountImpl::onRequestRetry(): ignoring callback as passed in registration handle is invalid");
      return (-2);
   }
   if (mClientRegistration.isValid() && (mClientRegistration.get() != h.get()))
   {
      WarningLog(<< "SipAccountImpl::onRequestRetry(): ignoring callback with handle mismatch mClientRegistration handle: " << mClientRegistration.get() << " callback handle: " << h.get());
      return (-2);
   }

   int retVal = -1;

   if (mDisabling)
   {
      WarningLog(<< "Ignoring registration retry because SipAccountImpl is in disabling state mHandle=" << mHandle);
      return retVal;
   }

   const SipAccountSettings acctSettings = getSettings();

   ExtensionHeader altsAvailHeader("X-Alternatives-Available");
   if (response.exists(altsAvailHeader))
   {
      // .jza. try a different non-greylisted DNS result
      retVal = acctSettings.minimumRegistrationIntervalSeconds;
   }
   else
   {
      // the 408/503 was internally generated by the Stack
      if (response.header(h_StatusLine).responseCode() == 503)
      {
         if (response.exists(h_Warnings) && response.header(h_Warnings).front().code() == 395)
         {
            // we hit this case when we do get responses for the DNS queries, but there are no options to try,
            // possibly due to DNS churn due to network issues. Reset the DNS cache to ensure we start from
            // a clean slate
            InfoLog(<< "DNS server query resulted in empty response, clearing DNS cache");
            retVal = calcNextRegFailureRetryInterval();
            if (mStack != NULL)
            {
               mStack->clearDnsCache();
               mStack->clearWhitelist();
            }
         }
         else if (response.exists(h_Warnings) && response.header(h_Warnings).front().code() == 396)
         {
            InfoLog(<< "Possible DNS has not stabilized after network change, re-initializing DNS");
            DnsStub::DnsSettings dnsSettings;
            populateNameServer(dnsSettings);
            mStack->getDnsStub()->reInit(dnsSettings);
            mStunClient->reInit(dnsSettings);

            if (retryOn(resip::getMethodName(response.header(h_CSeq).method()).c_str(), 503))
            {
               retVal = calcNextRegFailureRetryInterval();
            }
         }
         else if ((response.exists(h_Warnings) && response.header(h_Warnings).front().code() == 397) ||
             (response.exists(h_Warnings) && response.header(h_Warnings).front().code() == 398))
         {
            // we hit this case when we get a transport failure (397) OR if we couldn't reach the DNS servers (398);
            // so we want to try again, because it's likely that our network is down
            retVal = calcNextRegFailureRetryInterval();
         }
         else if (response.exists(h_Warnings) && response.header(h_Warnings).front().code() == 399)
         {
            // we hit this case when we have a certificate mismatch failure
            // don't retry
            retVal = -1;
         }
         else
         {
            // well, it was a 503, and we don't really know why ... fail
            retVal = -1;
         }
      }
      else if (response.header(h_StatusLine).responseCode() == 408)
      {
         closeTransportConnections();  // OBELISK-4968

         if (retryOn(resip::getMethodName(response.header(h_CSeq).method()).c_str(), 408))
         {
            retVal = calcNextRegFailureRetryInterval();
         }
      }
      else
      {
         retVal = calcNextRegFailureRetryInterval();
      }
   }

   // there is a registration problem, so un-freeze the DNS cache immediately; don't wait for any dangling
   // calls to timeout firts (i.e. don't wait for SipAccountImpl::allCallsEnded() to execute)
   if (unfreezeSipDnsCache() != kSuccess)
   {
      ErrLog(<< "Failed to unfreeze SIP DNS cache");
   }

   if (retVal >= 0)
   {
      InfoLog(<< "Retry registration due to internally-generated 408/503 from stack in " << retVal << " seconds");

      AR_STATE_TO(ARS_WaitingToRegister);
      SipAccountStatusChangedEvent::Reason networkReason = getAccountNtkRestrictionReason();
      SipAccountStatusChangedEvent args;
      args.reason = (networkReason == SipAccountStatusChangedEvent::Reason_None ? SipAccountStatusChangedEvent::Reason_Local_Timeout : networkReason);

      // Currently the X-Alternatives-Available header is only set in TransactionState when the registration
      // retries have been triggered by DNS lookup, and there are still pending DNS srv records to try. These
      // changes are required to fix issue obelisk-2126, where internally generated 408 responses were triggered
      // as the TransactionState went through the dns lookup srv records, triggering a failed registration status
      // on the application.
      if (response.exists(altsAvailHeader) && (response.header(h_StatusLine).responseCode() == 408))
      {
         InfoLog(<< "SipAccountImpl::onRequestRetry() mHandle=" << mHandle
            << " handling internally generated 408 triggered due to dns lookup: " << response);
         args.reason = SipAccountStatusChangedEvent::Reason_Dns_Lookup;
      }
      else if (response.exists(h_Warnings) && (response.header(h_Warnings).front().code() == 395))
      {
         InfoLog(<< "SipAccountImpl::onRequestRetry() mHandle=" << mHandle
                 << " handling internally generated 503 triggered due to dns lookup: " << response);
         args.reason = SipAccountStatusChangedEvent::Reason_Dns_Lookup;
      }
      // dmak
      else if (response.exists(h_Warnings) && response.header(h_Warnings).front().code() == 397)
      {
         InfoLog(<< "SipAccountImpl::onRequestRetry() mHandle=" << mHandle
                 << " handling internally generated 503 triggered due to no route to host: " << response);
         // Hold-Off on updating UI on internally generated transport error "No route to host" if have just experienced
         // a network change, and this is the first retry attempt. Likely transport layer gets reset after the first
         // registration attempt, as subsequent attempts are successful if the network is available.
         mTransportErrorRetryCount++;
         if (mTransportErrorRetryCount <= 3)
         {
            InfoLog(<< "SipAccountImpl::onRequestRetry() mHandle=" << mHandle << " holding off on updating the UI on transport error retry count=" << mTransportErrorRetryCount);
            return retVal;
         }

         args.reason = SipAccountStatusChangedEvent::Reason_No_Route_To_Host;
      }

      args.accountStatus = SipAccountStatusChangedEvent::Status_WaitingToRegister;
      args.signalingStatusCode = response.header(h_StatusLine).statusCode();
      args.failureRetryAfterSecs = retVal;
      args.signalingResponseText = response.getReason() != NULL ? response.getReason()->c_str() : "";
      args.accountBindingIpAddress = "";
      args.transportType = SipAccountTransport_Unknown;
      args.ipVersionInUse = IpVersion_Auto;
      args.localContactBinding = "";

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)
      if (!mTseFailureReason.empty())
      {
         args.reason = SipAccountStatusChangedEvent::Reason_Tunnel_Failure;
         args.signalingResponseText = mTseFailureReason;
      }
#endif

      fireAccountStatusEvent(args);
   }
   else
   {
      InfoLog(<< "Failing the registration, no retry will be attempted");
   }

   return retVal;
}

/// Called if registration fails, usage will be destroyed (unless a
/// Registration retry interval is enabled in the Profile)
void SipAccountImpl::onFailure(ClientRegistrationHandle h, const SipMessage& response)
{
   assert(!mClientRegistration.isValid() || mClientRegistration.get() == h.get());

   InfoLog(<< "SipAccountImpl::onFailure mHandle=" << mHandle << " handling: " << response);

   if (shouldDiscoverDns64Again())
   {
      refreshRegForDns64Rediscovery();
   }

   if (mDisabling)
   {
      WarningLog(<< "SipAccountImpl is in disabling state mHandle=" << mHandle);
   }

   setTlsConnectionInfo();

   SipAccountStatusChangedEvent::Reason networkReason = getAccountNtkRestrictionReason();
   SipAccountStatusChangedEvent::Status status = SipAccountStatusChangedEvent::Status_Unregistered;

   // ref: https://www.resiprocate.org/DUM_Client_Outbound_Support
   resip::SharedPtr<UserProfile> up = mDum->getMasterUserProfile();
   if(up->clientOutboundEnabled() && response.header(h_StatusLine).responseCode() == 439)
            // 439 means non outbound supported path to Registrar. Retry without outbound support.
   {
      AR_STATE_TO(ARS_Registering);

      up->setInstanceId(Data());
      up->setRegId(0);
      up->clientOutboundEnabled() = false;

      try
      {
         resip::SharedPtr<resip::SipMessage> reg = makeRegistration(up->getDefaultFrom()); // Pass in up, the modified UserProfile
         adornMessage(*this, *reg);
         mDum->send(reg);
      }
      catch (DumException& e)
      {
         ErrLog(<< "SipAccountImpl::onFailure(): dum exception: " << e.what());
      }
      return;
   }
   else if (handle5xx(response))
   {
      // startRetryTimer was called by handle5xx() if we get here
      return;
   }

   // there is a registration problem, so un-freeze the DNS cache immediately; don't wait for any dangling
   // calls to timeout firts (i.e. don't wait for SipAccountImpl::allCallsEnded() to execute)
   if (unfreezeSipDnsCache() != kSuccess)
   {
      ErrLog(<< "Failed to unfreeze SIP DNS cache");
   }

   if (!mDisabling && (response.getReceivedTransport() == NULL))
   {
      AR_STATE_TO(ARS_WaitingToRegister);

      status = SipAccountStatusChangedEvent::Status_WaitingToRegister;

      // .jza. we may want to think about making the 'reason' variable more accurate here. i.e. do we want to have failure to contact DNS
      // server be Reason_Local_Timeout, and DNS contact succees but no results Reason_Server_Response?
      // for now, leaving as was before (always Reason_Server_Response)

      // Reason update is redundant in this case, as the signalng response text will carry this information,
      // but it's more accurate to specify the reason than to leave it to the default value
      cpc::string sResponseReason = (response.header(h_StatusLine).reason().size() > 0 ? response.header(h_StatusLine).reason().c_str() : "");
      if (response.header(h_StatusLine).statusCode() == 503)
      {
         if (sResponseReason == RESIP_STATUS_LINE_REASON_TRANSPORT_PROTOCOL_MISMATCH)
         {
            networkReason = SipAccountStatusChangedEvent::Reason_Transport_Protocol_Mismatch;
         }
         else if (sResponseReason == RESIP_STATUS_LINE_REASON_TLS_CIPHER_MISMATCH)
         {
            networkReason = SipAccountStatusChangedEvent::Reason_TLS_Cipher_Mismatch;
         }
         else if (response.exists(h_Warnings) && (response.header(h_Warnings).front().code() == 395))
         {
            networkReason = SipAccountStatusChangedEvent::Reason_Dns_Lookup;
         }
         // dmak
         else if (response.exists(h_Warnings) && (response.header(h_Warnings).front().code() == 397))
         {
            networkReason = SipAccountStatusChangedEvent::Reason_No_Route_To_Host;
         }
      }
   }
   else
   {
      AR_STATE_TO(ARS_NotRegistered);
      status = SipAccountStatusChangedEvent::Status_Unregistered;
   }

   SipAccountStatusChangedEvent args;
   args.reason = (networkReason == SipAccountStatusChangedEvent::Reason_None ? SipAccountStatusChangedEvent::Reason_Server_Response : networkReason);
   args.accountStatus = status;
   args.signalingStatusCode = response.header(h_StatusLine).statusCode();
   args.failureRetryAfterSecs = 0;
   args.signalingResponseText = response.header(h_StatusLine).reason().size() > 0 ? response.header(h_StatusLine).reason().c_str() : "";
   args.accountBindingIpAddress = "";
   args.transportType = SipAccountTransport_Unknown;
   args.tlsInfo = mTlsConnInfo;
   args.ipVersionInUse = IpVersion_Auto;
   args.localContactBinding = "";

   fireAccountStatusEvent(args);
}

bool SipAccountImpl::warningMatch(const SipMessage& response, int iResponseCode, int iWarningCode, const resip::Data& warning)
{
   if (!response.isResponse())
   {
      return false;
   }

   if (response.header(h_StatusLine).responseCode() != iResponseCode)
   {
      return false;
   }

   if (!response.exists(h_Warnings))
   {
      return false;
   }

   if (response.header(h_Warnings).front().code() != iWarningCode)
   {
      return false;
   }

   if (response.header(h_Warnings).front().text() != warning)
   {
      return false;
   }

   return true;
}

SipAccountStatusChangedEvent::Reason SipAccountImpl::getAccountNtkRestrictionReason()
{
   NetworkTransport currentTransport = mNetworkChangeManagerIf->networkTransport();
   if (currentTransport == TransportNone)
   {
      return SipAccountStatusChangedEvent::Reason_No_Network;
   }
   else if (isRestrictedNetwork(currentTransport))
   {
      return SipAccountStatusChangedEvent::Reason_Restricted_Network;
   }
   else
   {
      return SipAccountStatusChangedEvent::Reason_None;
   }
}

void SipAccountImpl::populateMasterProfile(bool reuseExistingMasterProfile)
{
   DebugLog(<< "SipAccountImpl::populateMasterProfile()");

   SharedPtr<MasterProfile> profile = (reuseExistingMasterProfile ? mDum->getMasterProfile() : SharedPtr<MasterProfile>(new MasterProfile));
   profile->gruuEnabled() = false;
   profile->validateContentEnabled() = false;
   profile->validateContentLanguageEnabled() = false;
   profile->validateAcceptEnabled() = false;
   profile->ignoreInviteOnShutdown() = true;
   const SipAccountSettings acctSettings = getSettings();
   cpc::string domain = getDomain();

   // OBELISK-2680 Interop issues with Avaya when method parameter included in Refer-To header
   profile->setMethodParamInReferTo(acctSettings.useMethodParamInReferTo);

   // .ph. GENBAND specific
   bool useAlias = mUseAlias;
   if (acctSettings.sipTransportType == SipAccountTransport_UDP)
   {
      useAlias = false; // only for TCP/TLS
   }

   resip::Data target;
   {
      resip::DataStream ds(target);
      ds << "sip:";
      if (!acctSettings.username.empty())
      {
         ds << SipHelpers::escapeSipUser(acctSettings.username).c_str() << "@";
      }
      ds << (domain.empty() ? "unknown" : (domain.c_str()));
   }

   NameAddr localIdentity;
   try
   {
      localIdentity.displayName() = (SipHelpers::escapeSipString(acctSettings.displayName)).c_str();
      localIdentity.uri() = Uri(target);
   }
   catch (resip::ParseException&)
   {
      ErrLog(<< "invalid local identity: " << target);
      localIdentity.uri() = Uri("sip:domain.invalid");
   }

   for (std::size_t i = 0; i < acctSettings.additionalFromParameters.size(); i++)
   {
      if (!acctSettings.additionalFromParameters[i].name.empty())
      {
         resip::ExtensionParameter param(acctSettings.additionalFromParameters[i].name.c_str());
         localIdentity.param(param) = acctSettings.additionalFromParameters[i].value.c_str();
      }
   }

   profile->setDefaultFrom(localIdentity);

   resip::Data outboundProxyStr;

   if (acctSettings.outboundProxy.size() > 0)
   {
      resip::DataStream ds(outboundProxyStr);
      if (acctSettings.outboundProxy.find("sip:") == cpc::string::npos)
      {
         ds << "sip:";
      }
      ds << (acctSettings.outboundProxy.c_str());
   }
   else
   {
      if (acctSettings.useRegistrar)
      {
         resip::DataStream ds(outboundProxyStr);
         if (domain.find("sip:") == cpc::string::npos)
         {
            ds << "sip:";
         }
         ds << (domain.c_str());
      }
   }

   if (acctSettings.useRegistrar || outboundProxyStr.size() > 0)
   {
      try
      {
         Uri outboundProxyUri(outboundProxyStr);
         appendTransportTypeParameter(outboundProxyUri);
         profile->setOutboundProxy(outboundProxyUri);
      }
      catch (const resip::ParseException&)
      {
         ErrLog(<< "invalid outbound proxy: " << outboundProxyStr);
         Uri outboundProxyUri("sip:outbound-proxy.invalid"); // Will trigger a 503
         profile->setOutboundProxy(outboundProxyUri);
      }
   }

#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
   if (acctSettings.tunnelConfig.useTunnel && acctSettings.tunnelConfig.tunnelType == TunnelType_StrettoTunnel)
   {
      std::ostringstream buf;
      buf << "sip:" << Tuple::inet_ntop(mStrettoTunnel->getTuple());
      buf << ":" << mStrettoTunnel->getTuple().getPort();
      std::string strettoTunnelOutboundProxyStr = buf.str();

      try
      {
         Uri outboundProxyUri(strettoTunnelOutboundProxyStr.c_str());
         outboundProxyUri.param(p_transport) = toDataLower(mStrettoTunnel->getTuple().getType());
         profile->setOutboundProxy(outboundProxyUri);
         profile->setForceOutboundProxyOnAllRequestsEnabled(true);
      }
      catch (const resip::ParseException&)
      {
         ErrLog(<< "invalid Stretto Tunnel outbound proxy: " << strettoTunnelOutboundProxyStr);
         Uri outboundProxyUri("sip:outbound-proxy.invalid"); // Will trigger a 503
         profile->setOutboundProxy(outboundProxyUri);
      }
   }
#endif

   if (acctSettings.useOutbound)
   {
      if (mInstanceId.size() == 0)
      {
         ErrLog(<< "Tried to use outbound, but instanceId is not available");
      }
      else
      {
         // ref: https://www.resiprocate.org/DUM_Client_Outbound_Support
         profile->addSupportedOptionTag(Token(Symbols::Outbound));  // RFC 5626 - outbound
         profile->addSupportedOptionTag(Token(Symbols::Path));      // RFC 3327 - path

         // .jjg. Until we provide full outbound support (meaning multiple flows to different proxies)
         // we do NOT want the Route header to be populated, since most likely it will end up just being the hostname of
         // the proxy that we connect to (and this could cause issues for non-outbound-supporting servers)
         // Once we do decide to support full outbound, we will want the Route headers to contain the hostname of the specific
         // edge proxy.  (I.e. We will start by using DNS SRV or provisioning to get the outbound proxy route set, and then
         // create a MasterProfile for each of those).
         //profile->setExpressOutboundAsRouteSetEnabled(true);

         cpc::string instanceId = "<urn:uuid:" + cpc::string(mInstanceId.c_str()) + ">";
         profile->setInstanceId(resip::Data(instanceId.c_str()));//"E172AD33-AFE6-4D81-B2F9-A362D5542840");  // See RFC5626 section 4.1
         profile->setRegId(1);
         profile->clientOutboundEnabled() = true;
      }
   }

   if (acctSettings.useGruu)
   {
      if (mInstanceId.size() == 0)
      {
         ErrLog(<< "Tried to use outbound, but instanceId is not available");
      }
      else
      {
         profile->addSupportedOptionTag(Token(Symbols::Gruu));
         profile->gruuEnabled() = true;
         cpc::string instanceId = "<urn:uuid:" + cpc::string(mInstanceId.c_str()) + ">";
         profile->setInstanceId(resip::Data(instanceId.c_str()));//"E172AD33-AFE6-4D81-B2F9-A362D5542840");  // See RFC5626 section 4.1
      }
   }

   if (acctSettings.answerModeSupported)
   {
      profile->addSupportedOptionTag(Token(Symbols::AnswerMode));  // RFC 5373
   }

   if (acctSettings.useInstanceId && (!(acctSettings.useOutbound || acctSettings.useGruu)))
   {
      if (mInstanceId.size() == 0)
      {
         ErrLog(<< "instanceId is not available");
      }
      else
      {
         cpc::string instanceId = "<urn:uuid:" + cpc::string(mInstanceId.c_str()) + ">";
         profile->setInstanceId(resip::Data(instanceId.c_str()));//"E172AD33-AFE6-4D81-B2F9-A362D5542840");  // See RFC5626 section 4.1
      }
   }

   if (acctSettings.alwaysRouteViaOutboundProxy)
   {
      profile->setForceOutboundProxyOnAllRequestsEnabled(true);
   }

   setUdpAndTcpKeepAliveIntervals(profile);

   profile->setDefaultRegistrationTime(acctSettings.registrationIntervalSeconds);

   // we set a really high value here because we want ClientRegistration to be
   // in a state where it is waiting to refresh -- we will initiate the refresh
   // ourselves when, e.g., we get a network change event;
   // without setting this, we lose our ClientRegistrationHandle when we get a
   // 408 or 503 (internally generated)
   profile->setDefaultRegistrationRetryTime(60 * 60 * 24 * 7);
   // also, use the above value even in the presence of Retry-After header
   profile->setDefaultRegistrationRetryTimeIgnoresRetryAfterHeader(true);

   profile->setUserAgent((acctSettings.userAgent).c_str());

   profile->setRportEnabled(acctSettings.useRport);

   // digest credential
   Data authName(acctSettings.auth_username.empty() ? localIdentity.uri().user() : acctSettings.auth_username.c_str());
   Data authRealm(acctSettings.auth_realm.empty() ? localIdentity.uri().host() : acctSettings.auth_realm.c_str());
   profile->setDigestCredential(
      authRealm,
      authName,
      acctSettings.password.c_str()
      );

   if (acctSettings.useImsAuthHeader)
   {
      profile->setImsAuthUser(authName, authRealm);
   }

   profile->setRinstanceEnabled(acctSettings.useRinstance);
   if (acctSettings.useRinstance)
   {
      if (!reuseExistingMasterProfile)
      {
         // setting this here permits us to keep our rinstance the same across network interface changes
         profile->setRinstance(resip::Random::getCryptoRandomHex(8));
      }
   }

   profile->setDefaultSessionTime(acctSettings.sessionTimeSeconds);

   profile->addSupportedOptionTag(resip::Token(resip::Symbols::Replaces), resip::INVITE);

   switch (acctSettings.sessionTimerMode)
   {
   case SipAccountSessionTimerMode_Inactive:
   {
      profile->setDefaultSessionTimerMode(resip::Profile::PreferRemoteRefreshes);
      // need to remove timer tags
      profile->removeSupportedOptionTag(resip::Token(resip::Symbols::Timer), resip::INVITE);
   }
   break;
   case SipAccountSessionTimerMode_Optional:
      profile->setDefaultSessionTimerMode(resip::Profile::PreferRemoteRefreshes);
      profile->addSupportedOptionTag(resip::Token(resip::Symbols::Timer), resip::INVITE);
      break;
   case SipAccountSessionTimerMode_Required: // TODO: how to implement Required
   case SipAccountSessionTimerMode_Always:
      profile->setDefaultSessionTimerMode(resip::Profile::PreferLocalRefreshes);
      profile->addSupportedOptionTag(resip::Token(resip::Symbols::Timer), resip::INVITE);
      break;
   default:
      // doing nothing in Release mode, i.e. the previous behaviour won't be changed
      assert(false);
   }

   profile->clearSupportedMethods();

   DebugLog(<< "SipAccountImpl::populateMasterProfile(): account: " << mHandle << " reset the message decorator");
   mMessageDecorator->reset();
   profile->unsetOutboundDecorator();
   profile->setOutboundDecorator(mMessageDecorator);

   if (useAlias)
   {
      DebugLog(<< "SipAccountImpl::populateMasterProfile(): account: " << mHandle << " useAlias driving outbound decorator");
      mMessageDecorator->setUseAlias(true);
   }
   else
   {
      setMessageDecoratorForNAT64IfRequired(profile);
   }

   profile->clearAllowedEvents();
   profile->addAllowedEvent(Token(CPCAPI2::SipConversation::BroadsoftCallControlMonitor::TalkEvent));
   profile->addAllowedEvent(Token(CPCAPI2::SipConversation::BroadsoftCallControlMonitor::HoldEvent));

   resip::NameAddr capabilities;
   bool extensionsFound = false;
   for (std::size_t i = 0; i < acctSettings.capabilities.size(); i++)
   {
      if (!acctSettings.capabilities[i].name.empty())
      {
         resip::ExtensionParameter param(acctSettings.capabilities[i].name.c_str());
         capabilities.param(param) = acctSettings.capabilities[i].value.c_str();
         DebugLog(<< "SipAccountImpl::populateMasterProfile(): param name: " << acctSettings.capabilities[i].name.c_str() << " param value: " << acctSettings.capabilities[i].value.c_str());

         if ((acctSettings.capabilities[i].name == "extensions") && acctSettings.answerModeSupported && !acctSettings.capabilities[i].value.empty())
         {
            DebugLog(<< "SipAccountImpl::populateMasterProfile(): extensions parameter already exists");
            extensionsFound = true;
            if (capabilities.param(param) != "\"answermode\"")
            {
               std::string featureExtensions = "\"";
               std::string featureValue = acctSettings.capabilities[i].value.c_str();
               if (!featureValue.empty() && featureValue[0] == '\"') featureValue = featureValue.substr(1);
               if (!featureValue.empty() && featureValue[featureValue.size() - 1] == '\"') featureValue = featureValue.substr(0, featureValue.size() - 1);
               if ((featureValue.compare("answermode") != 0) && !featureValue.empty()) featureExtensions.append(featureValue + ",");
               featureExtensions.append("answermode\"");
               capabilities.param(param) = featureExtensions.c_str();
            }
         }
      }
   }

   if (acctSettings.answerModeSupported && !extensionsFound)
   {
      resip::ExtensionParameter param("extensions");
      capabilities.param(param) = "\"answermode\"";
   }

   profile->setUserAgentCapabilities(capabilities);

   profile->setIdOnlyForAnon(acctSettings.usePrivacyHeaderOnlyForAnonymous);

   profile->setMinInvite200RetransmitInterval(acctSettings.minInvite200RetransmitIntervalSec);

   profile->setBase64Prepad(acctSettings.addCryptoKeyPadding);

   std::vector<SipAccountAwareFeature*>::iterator itFeat = mAccountAwareFeatures.begin();
   for (; itFeat != mAccountAwareFeatures.end(); ++itFeat)
   {
      (*itFeat)->adornMasterProfile(profile);
   }

   if (!reuseExistingMasterProfile)
   {
      mDum->setMasterProfile(profile);
   }
}

void SipAccountImpl::appendTransportTypeParameter(resip::Uri & targetUri)
{
   const SipAccountSettings acctSettings = getSettings();
   Data transportParam;

   if (acctSettings.sipTransportType == SipAccountTransport_UDP)
      transportParam = "udp";
   else if (acctSettings.sipTransportType == SipAccountTransport_TCP)
      transportParam = "tcp";
   else if (acctSettings.sipTransportType == SipAccountTransport_TLS)
      transportParam = "tls";

   if (!transportParam.empty())
      targetUri.param(p_transport) = transportParam;
}

void SipAccountImpl::setMWIhandler(const std::function<bool(resip::ServerOutOfDialogReqHandle,const resip::SipMessage&)>& handler)
{
   mMWIhandler = handler;
}

// OutOfDialog - Client Handlers
bool SipAccountImpl::onSuccess(ClientOutOfDialogReqHandle, const SipMessage& successResponse)
{
   DebugLog(<< "SipAccountImpl::onSuccess()");

   const Data& tid = successResponse.getTransactionId();
   if (!mRegAfterConnProbe.mOptionsV4 && !mRegAfterConnProbe.mOptionsV6)
   {
      DebugLog(<< "SipAccountImpl::onSuccess(): Probe OPTIONS messages are NULL");
      probeStatus(SipNetworkProbeStatusChangedEvent::Status_Completed, SipNetworkProbeStatusChangedEvent::Reason_StaleResponseReceived, tid.c_str(), IpVersion_Auto, &successResponse);
      return false;
   }

   bool isV4Probe = (mRegAfterConnProbe.mOptionsV4 && tid == mRegAfterConnProbe.mOptionsV4->getTransactionId());
   bool isV6Probe = (mRegAfterConnProbe.mOptionsV6 && tid == mRegAfterConnProbe.mOptionsV6->getTransactionId());
   if (isV4Probe || isV6Probe)
   {
      updateStackIpPreference(successResponse);
      IpVersion probeVersion = (isV4Probe ? IpVersion_V4 : IpVersion_V6);
      bool isPreferredProbeResponse = (((isV4Probe && (mCurrentIpVersion == IpVersion_Auto)) || (isV6Probe && (mCurrentIpVersion == IpVersion_Auto_PreferV6))) ? true : false);

      if (preferredProbeResponseReceived())
      {
         InfoLog(<< "SipAccountImpl::onSuccess(): send register as received response to probe, preferred IP configuration: "
                 << mCurrentIpVersion << " IP version selected: " << (mStack->getPreferIpV6() ? "V6" : "V4"));

         if (isPreferredProbeResponse)
         {
            probeStatus(SipNetworkProbeStatusChangedEvent::Status_Completed, SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived, tid.c_str(), probeVersion, &successResponse);
         }
         else
         {
            probeStatus(SipNetworkProbeStatusChangedEvent::Status_Completed, SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived, tid.c_str(), probeVersion, &successResponse);
         }

         sendRegisterAfterConnProbe();
      }
      else
      {
         InfoLog(<< "SipAccountImpl::onSuccess(): received probe response from alternate IP version, wait for response to probe, preferred IP configuration: " << mCurrentIpVersion);
         probeStatus(SipNetworkProbeStatusChangedEvent::Status_ResponseReceived, SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived, tid.c_str(), probeVersion, &successResponse);
         mRegAfterConnProbe.mTimer.async_wait(this, REG_AFTER_CONN_PROBE_TIMER_ID, NULL);
      }
   }

   return false;
}

bool SipAccountImpl::onFailure(ClientOutOfDialogReqHandle h, const SipMessage& errorResponse)
{
   DebugLog(<< "SipAccountImpl::onFailure()");

   const Data& tid = errorResponse.getTransactionId();
   if (!mRegAfterConnProbe.mOptionsV4 && !mRegAfterConnProbe.mOptionsV6)
   {
      DebugLog(<< "SipAccountImpl::onFailure(): Probe OPTIONS messages are NULL");
      probeStatus(SipNetworkProbeStatusChangedEvent::Status_Completed, SipNetworkProbeStatusChangedEvent::Reason_StaleResponseReceived, tid.c_str(), IpVersion_Auto, &errorResponse);
      return false;
   }

   bool isV4Probe = (mRegAfterConnProbe.mOptionsV4 && tid == mRegAfterConnProbe.mOptionsV4->getTransactionId());
   bool isV6Probe = (mRegAfterConnProbe.mOptionsV6 && tid == mRegAfterConnProbe.mOptionsV6->getTransactionId());
   if (isV4Probe || isV6Probe)
   {
      // Errors can expected to be internally generated or from the wire, through the 2 second probing timeout will occur prior to the
      // internally generated 408/503 timeout errors, there could also be internally generated 408/503 dns errors such as no route to host

      updateStackIpPreference(errorResponse);
      IpVersion probeVersion = (isV4Probe ? IpVersion_V4 : IpVersion_V6);

      bool isV6ExternalError = (mRegAfterConnProbe.mResponseV6 && (mRegAfterConnProbe.mResponseV6->getReceivedTransport() != 0));
      bool isV4ExternalError = (mRegAfterConnProbe.mResponseV4 && (mRegAfterConnProbe.mResponseV4->getReceivedTransport() != 0));
      bool isPreferredExternalError = (((mCurrentIpVersion == IpVersion_Auto_PreferV6) && isV6ExternalError) || ((mCurrentIpVersion == IpVersion_Auto) && isV4ExternalError));
      bool bothProbeResponsesReceived = (mRegAfterConnProbe.mResponseV6 && mRegAfterConnProbe.mResponseV4);
      bool isPreferredProbeResponse = (((isV4Probe && (mCurrentIpVersion == IpVersion_Auto)) || (isV6Probe && (mCurrentIpVersion == IpVersion_Auto_PreferV6))) ? true : false);

      if (preferredProbeResponseReceived() && isPreferredExternalError)
      {
         InfoLog(<< "SipAccountImpl::onFailure(): send register regardless of probe failure, as received external response to probe, preferred IP configuration: "
                 << mCurrentIpVersion << " IP version selected: " << (mStack->getPreferIpV6() ? "V6" : "V4"));
         probeStatus(SipNetworkProbeStatusChangedEvent::Status_Completed, SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived, tid.c_str(), probeVersion, &errorResponse);
         sendRegisterAfterConnProbe();
      }
      else if (bothProbeResponsesReceived)
      {
         InfoLog(<< "SipAccountImpl::onFailure(): send register regardless of probe failure, as responses have been received for both IP versions, preferred IP configuration: "
                 << mCurrentIpVersion << " IP version selected: " << (mStack->getPreferIpV6() ? "V6" : "V4"));
         if (isPreferredProbeResponse)
         {
            probeStatus(SipNetworkProbeStatusChangedEvent::Status_Completed, SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived, tid.c_str(), probeVersion, &errorResponse);
         }
         else
         {
            probeStatus(SipNetworkProbeStatusChangedEvent::Status_Completed, SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived, tid.c_str(), probeVersion, &errorResponse);
         }

         sendRegisterAfterConnProbe();
      }
      else
      {
         // If failure is for the non-preferred IP version or due to internally generated error on preferred IP version,
         // we should still wait until probe timeout for the other IP version
         InfoLog(<< "SipAccountImpl::onFailure(): wait for response to probe on other IP version, preferred IP configuration: " << mCurrentIpVersion);
         if (preferredProbeResponseReceived())
         {
            probeStatus(SipNetworkProbeStatusChangedEvent::Status_ResponseReceived, SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived, tid.c_str(), probeVersion, &errorResponse);
         }
         else
         {
            probeStatus(SipNetworkProbeStatusChangedEvent::Status_ResponseReceived, SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived, tid.c_str(), probeVersion, &errorResponse);
         }
         mRegAfterConnProbe.mTimer.async_wait(this, REG_AFTER_CONN_PROBE_TIMER_ID, NULL);
      }
   }
   else
   {
      InfoLog(<< "SipAccountImpl::onFailure(): Ignoring error response as it does not match the probe transaction-id");
   }

   return false;
}

void SipAccountImpl::updateStackIpPreference(const SipMessage& response)
{
   if (!mRegAfterConnProbe.mOptionsV4 && !mRegAfterConnProbe.mOptionsV6)
   {
      // The options messages could be null as we have already received the preferred probe response,
      // and as such reset the probe container, and so do not want to update the preferred IP version
      // based on any subsequent probe responses
      DebugLog(<< "SipAccountImpl::updateStackIpPreference(): Ignoring response as the probe OPTIONS messages are NULL");
      return;
   }

   if (response.getReceivedTransport() == 0)
   {
      DebugLog(<< "SipAccountImpl::updateStackIpPreference(): Received internally generated " << response.header(h_StatusLine).responseCode() << " response");

      if (mRegAfterConnProbe.mOptionsV4 && (response.getTransactionId() == mRegAfterConnProbe.mOptionsV4->getTransactionId()))
      {
         mRegAfterConnProbe.mResponseV4 = resip::SharedPtr<resip::SipMessage>(new SipMessage(response));
      }
      else if (mRegAfterConnProbe.mOptionsV6 && (response.getTransactionId() == mRegAfterConnProbe.mOptionsV6->getTransactionId()))
      {
         mRegAfterConnProbe.mResponseV6 = resip::SharedPtr<resip::SipMessage>(new SipMessage(response));
      }
      else
      {
         InfoLog(<< "SipAccountImpl::updateStackIpPreference(): Ignoring error response as it does not match the probe transaction-id");
      }
      return;
   }

   // Presumption that this function is only called when the IP Version is configured IpVersion_Auto or IpVersion_Auto_PreferV6,
   // we should not be probing if the configured IP version is specifically IpVersion_V4 or IpVersion_V6

   if (mCurrentIpVersion == IpVersion_Auto_PreferV6)
   {
      if (response.getSource().ipVersion() == resip::IpVersion::V6)
      {
         DebugLog(<< "SipAccountImpl::updateStackIpPreference(): Set IPv6 as preferred as the response source is IPv6");
         mStack->setPreferIpV6(true);
         mRegAfterConnProbe.mResponseV6 = resip::SharedPtr<resip::SipMessage>(new SipMessage(response));
      }
      else // Source is V4
      {
         if (response.exists(h_Contacts) && response.header(h_Contacts).size() > 0 &&
            DnsUtil::isIpV6Address(response.header(h_Contacts).front().uri().host()))
         {
            DebugLog(<< "SipAccountImpl::updateStackIpPreference(): Set IPv6 as preferred as the response contact host is IPv6");
            mStack->setPreferIpV6(true);
            mRegAfterConnProbe.mResponseV6 = resip::SharedPtr<resip::SipMessage>(new SipMessage(response));
         }
         else if (response.header(h_Vias).front().exists(p_received) &&
            DnsUtil::isIpV6Address(response.header(h_Vias).front().param(p_received)))
         {
            DebugLog(<< "SipAccountImpl::updateStackIpPreference(): Set IPv6 as preferred as the response via received param is IPv6");
            mStack->setPreferIpV6(true);
            mRegAfterConnProbe.mResponseV6 = resip::SharedPtr<resip::SipMessage>(new SipMessage(response));
         }
         else
         {
            DebugLog(<< "SipAccountImpl::updateStackIpPreference(): Set IPv4 as preferred");
            mStack->setPreferIpV6(false);
            mRegAfterConnProbe.mResponseV4 = resip::SharedPtr<resip::SipMessage>(new SipMessage(response));
         }
      }
   }
   else // Preference is V4
   {
      if (response.getSource().ipVersion() == resip::IpVersion::V4)
      {
         DebugLog(<< "SipAccountImpl::updateStackIpPreference(): Set IPv4 as preferred as the response source is IPv4");
         mStack->setPreferIpV6(false);
         mRegAfterConnProbe.mResponseV4 = resip::SharedPtr<resip::SipMessage>(new SipMessage(response));
      }
      else // Source is V6
      {
         if (response.exists(h_Contacts) && response.header(h_Contacts).size() > 0 &&
            DnsUtil::isIpV4Address(response.header(h_Contacts).front().uri().host()))
         {
            DebugLog(<< "SipAccountImpl::updateStackIpPreference(): Set IPv4 as preferred as the response contact host is IPv4");
            mStack->setPreferIpV6(false);
            mRegAfterConnProbe.mResponseV4 = resip::SharedPtr<resip::SipMessage>(new SipMessage(response));
         }
         else if (response.header(h_Vias).front().exists(p_received) &&
            DnsUtil::isIpV4Address(response.header(h_Vias).front().param(p_received)))
         {
            DebugLog(<< "SipAccountImpl::updateStackIpPreference(): Set IPv4 as preferred as the response via received param is IPv4");
            mStack->setPreferIpV6(false);
            mRegAfterConnProbe.mResponseV4 = resip::SharedPtr<resip::SipMessage>(new SipMessage(response));
         }
         else
         {
            DebugLog(<< "SipAccountImpl::updateStackIpPreference(): Set IPv6 as preferred");
            mStack->setPreferIpV6(true);
            mRegAfterConnProbe.mResponseV6 = resip::SharedPtr<resip::SipMessage>(new SipMessage(response));
         }
      }
   }
}

bool SipAccountImpl::preferredProbeResponseReceived()
{
   // Presumption that this function is only called when the IP Version is configured IpVersion_Auto or IpVersion_Auto_PreferV6,
   // we should not be probing if the configured IP version is specifically IpVersion_V4 or IpVersion_V6

   if (!mRegAfterConnProbe.mOptionsV4 && !mRegAfterConnProbe.mOptionsV6)
   {
      DebugLog(<< "SipAccountImpl::preferredProbeResponseReceived(): Probe OPTIONS messages are NULL");
      return false;
   }

   if (mCurrentIpVersion == IpVersion_Auto_PreferV6)
   {
      if (mRegAfterConnProbe.mResponseV6)
      {
         DebugLog(<< "SipAccountImpl::preferredProbeResponseReceived(): Preferred V6 probe response received: " << mRegAfterConnProbe.mResponseV6->header(h_StatusLine).responseCode()
                  << ((mRegAfterConnProbe.mResponseV6->getReceivedTransport() == 0) ? " (internal)" : ""));
         return true;
      }
   }
   else
   {
      if ((mRegAfterConnProbe.mResponseV4))
      {
         DebugLog(<< "SipAccountImpl::preferredProbeResponseReceived(): Preferred V4 probe response received: " << mRegAfterConnProbe.mResponseV4->header(h_StatusLine).responseCode()
                  << ((mRegAfterConnProbe.mResponseV4->getReceivedTransport() == 0) ? " (internal)" : ""));
         return true;
      }
   }

   DebugLog(<< "SipAccountImpl::preferredProbeResponseReceived(): Preferred IP configuration: " << mCurrentIpVersion << " probe response not yet received");
   return false;
}

// OutOfDialog - Server Handlers
bool SipAccountImpl::onReceivedRequest(ServerOutOfDialogReqHandle h, const SipMessage& request)
{
   bool isMessageSummary = false;
   if (request.method() == resip::NOTIFY)
   {
      if (request.exists(h_Event))
      {
         if (request.header(h_Event).value() == "message-summary")
         {
            isMessageSummary = true;
         }
         else if (resip::isEqualNoCase(request.header(h_Event).value(), "keep-alive"))
         {
            // this is probably an out-of-dialog NOTIFY sent as a keep alive by some servers;
            // blindly accept it
            h->send(h->accept());
            return true;
         }
         else
         {
            h->send(h->reject(489));
            return true;
         }
      }
      if (request.exists(h_ContentType))
      {
         if (request.header(h_ContentType).type() == "application" &&
             request.header(h_ContentType).subType() == "simple-message-summary")
         {
            isMessageSummary = true;
         }
      }
   }
   if (isMessageSummary && mMWIhandler)
   {
      return mMWIhandler(h, request);
   }

   return false;
}

void SipAccountImpl::patchStrettoTunnelAccountSettings(SipAccountSettings& sipAccountSettings) const
{
   if (!(sipAccountSettings.tunnelConfig.useTunnel && sipAccountSettings.tunnelConfig.tunnelType == TunnelType_StrettoTunnel))
   {
      return;
   }

   // currently the Stretto Tunnel feature expects a very specific set of SipAccountSettings values in order to work properly.
   // however some of these settings could seem arbitrary to SDK customers; so we 'fix' up those settings, to make the SDK
   // a bit easier to use with SIP push

   if (sipAccountSettings.sipTransportType != SipAccountTransport_UDP)
   {
      WarningLog(<< "Stretto Tunnel only support sipTransportType with value SipAccountTransport_UDP. Auto correcting this");
      sipAccountSettings.sipTransportType = SipAccountTransport_UDP;
   }

   if (sipAccountSettings.useRegistrar)
   {
      WarningLog(<< "Stretto Tunnel only support useRegistrar = false. Auto correcting this");
      sipAccountSettings.useRegistrar = false;
   }
}

int SipAccountImpl::applySettings()
{
   NetworkTransport currentTransport = mNetworkChangeManagerIf->networkTransport();

   std::map<NetworkTransport, SipAccountSettings>::const_iterator it = mPendingSettings.find(TransportNone);
   if (it == mPendingSettings.end())
   {
      fireAccountError("you must call configureDefaultAccountSettings() before applySettings()");
      return kError;
   }

   it = mSettings.find(TransportNone);
   if (it == mSettings.end())
   {
      mSettings[TransportNone] = SipAccountSettings();
   }

   for (auto it = mPendingSettings.begin(); it != mPendingSettings.end(); ++it)
   {
      if (TLS_DEFAULT == it->second.sslVersion)
      {
         it->second.sslVersion = (CPCAPI2::SipAccount::SSLVersion)mPhone->getSslCipherOptions().getTLSVersion(SslCipherUsageSip);
      }

      if (it->second.cipherSuite.empty())
      {
         it->second.cipherSuite = mPhone->getSslCipherOptions().getCiphers(SslCipherUsageSip);
      }

      if (it->second.tunnelConfig.useTunnel && it->second.tunnelConfig.tunnelType == TunnelType_StrettoTunnel)
      {
         patchStrettoTunnelAccountSettings(it->second);
      }
   }

   it = mPendingSettings.find(currentTransport);
   const SipAccountSettings currentPending = (it != mPendingSettings.end() ? it->second : mPendingSettings[TransportNone]);

   it = mSettings.find(currentTransport);
   const SipAccountSettings currentActive = (it != mSettings.end() ? it->second : mSettings[TransportNone]);

   if (currentPending.alwaysRouteViaOutboundProxy != currentActive.alwaysRouteViaOutboundProxy ||
       currentPending.auth_username != currentActive.auth_username ||
       currentPending.auth_realm != currentActive.auth_realm ||
       currentPending.displayName != currentActive.displayName ||
       currentPending.domain != currentActive.domain ||
       currentPending.ignoreCertVerification != currentActive.ignoreCertVerification ||
       !(currentPending.additionalCertPeerNames == currentActive.additionalCertPeerNames) ||
       !(currentPending.acceptedCertPublicKeys == currentActive.acceptedCertPublicKeys) ||
       !(currentPending.requiredCertPublicKeys == currentActive.requiredCertPublicKeys) ||
       currentPending.ipVersion != currentActive.ipVersion ||
       currentPending.sslVersion != currentActive.sslVersion ||
       currentPending.cipherSuite != currentActive.cipherSuite ||
       currentPending.minimumRegistrationIntervalSeconds != currentActive.minimumRegistrationIntervalSeconds ||
       currentPending.maximumRegistrationIntervalSeconds != currentActive.maximumRegistrationIntervalSeconds ||
       currentPending.nameServers.size() != currentActive.nameServers.size() ||
       currentPending.otherNonEscapedCharsInUri != currentActive.otherNonEscapedCharsInUri ||
       currentPending.outboundProxy != currentActive.outboundProxy ||
       currentPending.password != currentActive.password ||
       currentPending.registrationIntervalSeconds != currentActive.registrationIntervalSeconds ||
       currentPending.sessionTimerMode != currentActive.sessionTimerMode ||
       currentPending.sessionTimeSeconds != currentActive.sessionTimeSeconds ||
       currentPending.sipQosSettings != currentActive.sipQosSettings ||
       currentPending.sipTransportType != currentActive.sipTransportType ||
       currentPending.stunServer != currentActive.stunServer ||
       currentPending.stunServerSource != currentActive.stunServerSource ||
       currentPending.tcpKeepAliveTime != currentActive.tcpKeepAliveTime ||
       currentPending.tunnelConfig != currentActive.tunnelConfig ||
       currentPending.udpKeepAliveTime != currentActive.udpKeepAliveTime ||
       currentPending.useImsAuthHeader != currentActive.useImsAuthHeader ||
       currentPending.useMethodParamInReferTo != currentActive.useMethodParamInReferTo ||
       currentPending.useOutbound != currentActive.useOutbound ||
       currentPending.userAgent != currentActive.userAgent ||
       currentPending.useRegistrar != currentActive.useRegistrar ||
       currentPending.username != currentActive.username ||
       currentPending.useRport != currentActive.useRport ||
       currentPending.minSipPort != currentActive.minSipPort ||
       currentPending.maxSipPort != currentActive.maxSipPort ||
       currentPending.defaultSipPort != currentActive.defaultSipPort ||
       currentPending.defaultSipsPort != currentActive.defaultSipsPort ||
       currentPending.enableRegeventDeregistration != currentActive.enableRegeventDeregistration ||
       currentPending.enableDNSResetOnRegistrationRefresh != currentActive.enableDNSResetOnRegistrationRefresh ||
       currentPending.enableAuthResetUponDNSReset != currentActive.enableAuthResetUponDNSReset ||
       currentPending.XCAPRoot != currentActive.XCAPRoot ||
       currentPending.usePrivacyHeaderOnlyForAnonymous != currentActive.usePrivacyHeaderOnlyForAnonymous ||
       currentPending.overrideMsecsTimerF != currentActive.overrideMsecsTimerF)
   {
      if (mEnabled)
      {
         mReEnableOnShutdown = true;
         InfoLog(<< "SipAccountImpl::applySettings is about to call SipAccountImpl::disable() mHandle=" << mHandle);
         disable();
      }

      // Log SipAccountSettings
      std::ostringstream nameServers;
      for (std::size_t i = 0; i < currentPending.nameServers.size(); i++)
      {
         if (i > 0)
         {
            nameServers << ", ";
         }
         nameServers << currentPending.nameServers[i];
      }

      std::ostringstream reRegisterOnResponseTypes;
      for (std::size_t i = 0; i < currentPending.reRegisterOnResponseTypes.size(); i++)
      {
         if (i > 0)
         {
            reRegisterOnResponseTypes << ", ";
         }
         reRegisterOnResponseTypes << currentPending.reRegisterOnResponseTypes[i].method << "/"
                                   << currentPending.reRegisterOnResponseTypes[i].responseCode;
      }

      std::ostringstream tunnelConfig;
      if (currentPending.tunnelConfig.useTunnel)
      {
         tunnelConfig << "enabled";
         if (currentPending.tunnelConfig.tunnelType == TunnelType_TSCF)
         {
            tunnelConfig << ", tscf";
         }
         else if (currentPending.tunnelConfig.tunnelType == TunnelType_StrettoTunnel)
         {
            tunnelConfig << ", strettoTunnel";
         }
         else
         {
            tunnelConfig << ", unknown tunnel type";
         }
         switch (currentPending.tunnelConfig.transportType)
         {
         case TunnelTransport_Auto:
            tunnelConfig << ", Auto";
            break;
         case TunnelTransport_UDP:
            tunnelConfig << ", UDP";
            break;
         case TunnelTransport_TCP:
            tunnelConfig << ", TCP";
            break;
         case TunnelTransport_TLS:
            tunnelConfig << ", TLS";
            break;
         case TunnelTransport_DTLS:
            tunnelConfig << ", DTLS";
            break;
         default:
            tunnelConfig << ", Unknown";
            break;
         }

         tunnelConfig << ", mediaTransportType(";
         switch (currentPending.tunnelConfig.mediaTransportType)
         {
         case TunnelMediaTransport_DatagramOnly:
            tunnelConfig << "DatagramOnly";
            break;
         case TunnelMediaTransport_DatagramPreferred:
            tunnelConfig << "DatagramPreferred";
            break;
         case TunnelMediaTransport_StreamOnly:
            tunnelConfig << "StreamOnly";
            break;
         case TunnelMediaTransport_StreamPreferred:
            tunnelConfig << "StreamPreferred";
            break;
         default:
            tunnelConfig << "Default";
            break;
         }
         tunnelConfig << ")";
         tunnelConfig << ", " << currentPending.tunnelConfig.server;
         tunnelConfig << ", redundancyFactor(" << currentPending.tunnelConfig.redundancyFactor << ")";
         if (currentPending.tunnelConfig.doLoadBalancing) tunnelConfig << ", doLoadBalancing";
         if (currentPending.tunnelConfig.ignoreCertVerification) tunnelConfig << ", ignoreCertVerification";
         if (currentPending.tunnelConfig.disableNagleAlgorithm) tunnelConfig << ", disableNagleAlgorithm";
      }
      else
      {
         tunnelConfig << "disabled";
      }

      // Passwords intentionally not logged
      InfoLog(<< "SipAccountSettings for transport " << currentTransport << ", mHandle=" << mHandle << ":" << std::endl
              << "        alwaysRouteViaOutboundProxy: " << currentPending.alwaysRouteViaOutboundProxy << std::endl
              << "                         auth_realm: " << currentPending.auth_realm << std::endl
              << "                      auth_username: " << currentPending.auth_username << std::endl
              << "                        displayName: " << currentPending.displayName << std::endl
              << "                             domain: " << currentPending.domain << std::endl
              << "       enableRegeventDeregistration: " << currentPending.enableRegeventDeregistration << std::endl
              << "         excludeEncryptedTransports: " << currentPending.excludeEncryptedTransports << std::endl
              << "             ignoreCertVerification: " << currentPending.ignoreCertVerification << std::endl
              << "                          ipVersion: " << currentPending.ipVersion << std::endl
              << "                 enableNat64Support: " << currentPending.enableNat64Support << std::endl
              << "                         sslVersion: " << currentPending.sslVersion << std::endl
              << "                        cipherSuite: " << currentPending.cipherSuite << std::endl
              << "                         maxSipPort: " << currentPending.maxSipPort << std::endl
              << " minimumRegistrationIntervalSeconds: " << currentPending.minimumRegistrationIntervalSeconds << std::endl
              << " maximumRegistrationIntervalSeconds: " << currentPending.maximumRegistrationIntervalSeconds << std::endl
              << "                         minSipPort: " << currentPending.minSipPort << std::endl
              << "                     defaultSipPort: " << currentPending.defaultSipPort << std::endl
              << "                    defaultSipsPort: " << currentPending.defaultSipsPort << std::endl
              << "       enableRegeventDeregistration: " << currentPending.enableRegeventDeregistration << std::endl
              << "enableDNSResetOnRegistrationRefresh: " << currentPending.enableDNSResetOnRegistrationRefresh << std::endl
              << "        enableAuthResetUponDNSReset: " << currentPending.enableAuthResetUponDNSReset << std::endl
              << "                        nameServers: " << nameServers.str() << std::endl
              << "          otherNonEscapedCharsInUri: " << currentPending.otherNonEscapedCharsInUri << std::endl
              << "                      outboundProxy: " << currentPending.outboundProxy << std::endl
              << "        registrationIntervalSeconds: " << currentPending.registrationIntervalSeconds << std::endl
              << "          reRegisterOnResponseTypes: " << reRegisterOnResponseTypes.str() << std::endl
              << "                   sessionTimerMode: " << currentPending.sessionTimerMode << std::endl
              << "                 sessionTimeSeconds: " << currentPending.sessionTimeSeconds << std::endl
              << "                     sipQosSettings: " << currentPending.sipQosSettings << std::endl
              << "                   sipTransportType: " << currentPending.sipTransportType << std::endl
              << "                         stunServer: " << currentPending.stunServer << std::endl
              << "                   stunServerSource: " << currentPending.stunServerSource << std::endl
              << "                   tcpKeepAliveTime: " << currentPending.tcpKeepAliveTime << std::endl
              << "                       tunnelConfig: " << tunnelConfig.str() << std::endl
              << "                   udpKeepAliveTime: " << currentPending.udpKeepAliveTime << std::endl
              << "                            useGruu: " << currentPending.useGruu << std::endl
              << "                   useImsAuthHeader: " << currentPending.useImsAuthHeader << std::endl
              << "            useMethodParamInReferTo: " << currentPending.useMethodParamInReferTo << std::endl
              << "                      useInstanceId: " << currentPending.useInstanceId << std::endl
              << "                answerModeSupported: " << currentPending.answerModeSupported << std::endl
              << "                        useOutbound: " << currentPending.useOutbound << std::endl
              << "                          userAgent: " << currentPending.userAgent << std::endl
              << "                       useRegistrar: " << currentPending.useRegistrar << std::endl
              << "                           username: " << currentPending.username << std::endl
              << "                           useRport: " << currentPending.useRport << std::endl
              << "                           XCAPRoot: " << currentPending.XCAPRoot << std::endl
              << "                       useRinstance: " << currentPending.useRinstance << std::endl
              << "                  transportHoldover: " << currentPending.transportHoldover << std::endl
              << "                overrideMsecsTimerF: " << currentPending.overrideMsecsTimerF << std::endl
              << "            preferPAssertedIdentity: " << currentPending.preferPAssertedIdentity << std::endl
              << "     autoRetryOnTransportDisconnect: " << currentPending.autoRetryOnTransportDisconnect << std::endl);
   }

   mSettings = mPendingSettings;
   mPendingSettings.clear();

   SipAccountConfiguredEvent args;
   args.curTransport = currentTransport;
   args.settings = mSettings;
   mInterface->fireEvent(cpcEvent(SipAccountHandlerInternal, onAccountConfigured), mHandle, args);

   return kSuccess;
}

SipAccountSettings SipAccountImpl::getSettings() const
{
   std::map<NetworkTransport, SipAccountSettings>::const_iterator it = mSettings.find(CPCAPI2::TransportNone);
   if (it != mSettings.end())
   {
      SipAccountSettings ret = it->second;
      NetworkTransport currentTransport = mNetworkChangeManagerIf->networkTransport();
      std::map<NetworkTransport, SipAccountSettings>::const_iterator it = mSettings.find(currentTransport);
      if (it != mSettings.end())
      {
         ret = it->second;
      }
      return ret;
   }
   else
   {
      WarningLog(<< "SipAccountImpl::getSettings() called but settings not setup yet (perhaps account not initialized?");
      return SipAccountSettings();
   }
}

void SipAccountImpl::setUdpAndTcpKeepAliveIntervals(SharedPtr<Profile> profile)
{
   // Change the intervals using the settings for the selected transport
   SipAccountSettings settings = getSettings();
   profile->setKeepAliveTimeForDatagram(settings.udpKeepAliveTime);
   profile->setKeepAliveTimeForStream(settings.tcpKeepAliveTime);
}

void SipAccountImpl::release()
{
   delete this;
}

void SipAccountImpl::setProbeHandler(SipNetworkProbeHandler* handler)
{
   mProbeHandler = handler;
}

void SipAccountImpl::probeStatus(SipNetworkProbeStatusChangedEvent::Status status, SipNetworkProbeStatusChangedEvent::Reason reason, cpc::string tid, IpVersion probeVersion, const resip::SipMessage* response)
{
   StackLog(<< "SipAccountImpl::probeStatus()");
   if (!mProbeHandler)
   {
      StackLog(<< "SipAccountImpl::probeStatus(): Ignoring probe status as there is no probe status handler.");
      return;
   }

   assert((getSettings().ipVersion == IpVersion_Auto) || (getSettings().ipVersion == IpVersion_Auto_PreferV6));
   if ((getSettings().ipVersion != IpVersion_Auto) && (getSettings().ipVersion != IpVersion_Auto_PreferV6))
   {
      DebugLog(<< "SipAccountImpl::probeStatus(): Ignoring probe status as probing is not required");
      return;
   }

   static unsigned int probeMessageId = 1;

   SipNetworkProbeStatusChangedEvent args;

   args.probeMessageId = probeMessageId++;
   args.status = status;
   args.reason = reason;
   args.ipVersionPreferred = ((getSettings().ipVersion == IpVersion_Auto) ? IpVersion_V4 : IpVersion_V6);
   args.ipVersionSelected = args.ipVersionPreferred;

   if (status == SipNetworkProbeStatusChangedEvent::Status_Completed)
   {
      args.ipVersionSelected = (mStack->getPreferIpV6() ? IpVersion_V6 : IpVersion_V4);
   }

   if (!tid.empty())
   {
      // If a tid is specified, then this status update is only applicable to a particular probe transaction

      args.probeIpVersion = probeVersion;
      args.tid = tid;

      if (probeVersion == IpVersion_V4)
      {
         assert(mRegAfterConnProbe.mOptionsV4);
         assert(mRegAfterConnProbe.mOptionsV4->getTransactionId() == tid);

         if (mRegAfterConnProbe.mResponseV4)
         {
            assert(mRegAfterConnProbe.mResponseV4->isResponse());
            extractProbeInfo(*mRegAfterConnProbe.mOptionsV4, *mRegAfterConnProbe.mResponseV4, args);
         }
         else if (response && response->isResponse())
         {
            args.signallingStatusCode = response->header(h_StatusLine).responseCode();
            args.signallingResponseText = (response->header(h_StatusLine).reason().size() > 0 ? response->header(h_StatusLine).reason().c_str() : "");
         }
      }
      else if ((probeVersion == IpVersion_V6) && mRegAfterConnProbe.mResponseV6)
      {
         assert(mRegAfterConnProbe.mOptionsV6);
         assert(mRegAfterConnProbe.mOptionsV6->getTransactionId() == tid);

         if (mRegAfterConnProbe.mResponseV6)
         {
            assert(mRegAfterConnProbe.mResponseV6->isResponse());
            extractProbeInfo(*mRegAfterConnProbe.mOptionsV6, *mRegAfterConnProbe.mResponseV6, args);
         }
      }
      else if (response && response->isResponse())
      {
         // To handle stale responses
         args.signallingStatusCode = response->header(h_StatusLine).responseCode();
         args.signallingResponseText = (response->header(h_StatusLine).reason().size() > 0 ? response->header(h_StatusLine).reason().c_str() : "");
      }
      else
      {
         assert((probeVersion == IpVersion_V4) || (probeVersion == IpVersion_V6));
         DebugLog(<< "SipAccountImpl::probeStatus(): Ignoring probe status as probe version type is invalid: " << probeVersion);
         return;
      }
   }

   StackLog(<< "SipAccountImpl::probeStatus(): Probe Status: " << args);
   mProbeHandler->onNetworkProbeStatusChanged(mHandle, args);
}

void SipAccountImpl::extractProbeInfo(resip::SipMessage& request, resip::SipMessage& response, SipNetworkProbeStatusChangedEvent& args)
{
   assert(request.isRequest());
   assert(response.isResponse());

   args.signallingStatusCode = response.header(h_StatusLine).responseCode();
   args.signallingResponseText = (response.header(h_StatusLine).reason().size() > 0 ? response.header(h_StatusLine).reason().c_str() : "");

   if (response.exists(h_Contacts) && (response.header(h_Contacts).size() > 0))
   {
      args.probeTargetAddress = response.header(h_Contacts).front().uri().getAorAsUri().toString().c_str();
      StackLog(<< "SipAccountImpl::extractProbeInfoFromResponse(): Extracting probe target address from response contact header: " << args.probeTargetAddress);
   }
   else if ((response.getReceivedTransport() != 0) && (response.getSource().presentationFormat().size() > 0) && (response.getSource().presentationFormat() != "0.0.0.0"))
   {
      args.probeTargetAddress = response.getSource().presentationFormat().c_str();
      args.probeTargetAddress += ":";
      args.probeTargetAddress += Data(response.getSource().getPort()).c_str();
      StackLog(<< "SipAccountImpl::extractProbeInfoFromResponse(): Extracting probe target address from response source info: " << args.probeTargetAddress);
   }
   else if ((request.getDestination().presentationFormat().size() > 0) && (request.getDestination().presentationFormat() != "0.0.0.0"))
   {
      args.probeTargetAddress = request.getDestination().presentationFormat().c_str();
      args.probeTargetAddress += ":";
      args.probeTargetAddress += Data(request.getDestination().getPort()).c_str();
      StackLog(<< "SipAccountImpl::extractProbeInfoFromResponse(): Extracting probe target address from request destination info: " << args.probeTargetAddress);
   }
   else
   {
      args.probeTargetAddress = request.header(h_To).uri().getAorAsUri().toString().c_str();
      StackLog(<< "SipAccountImpl::extractProbeInfoFromResponse(): Extracting probe target address from request to header: " << args.probeTargetAddress);
   }

   if (response.exists(h_Vias) && (response.header(h_Vias).size() > 0) && (response.header(h_Vias).front().sentHost().size() > 0) && (response.header(h_Vias).front().sentHost() != "0.0.0.0"))
   {
      args.probeLocalAddress = response.header(h_Vias).front().sentHost().c_str();
      args.probeLocalAddress += ":";
      args.probeLocalAddress += Data(response.header(h_Vias).front().sentPort()).c_str();
      StackLog(<< "SipAccountImpl::extractProbeInfoFromResponse(): Extracting probe local address from response via header: " << args.probeLocalAddress);
   }
   else if (request.exists(h_Contacts) && (request.header(h_Contacts).size() > 0))
   {
      args.probeLocalAddress = request.header(h_Contacts).front().uri().getAorAsUri().toString().c_str();
      StackLog(<< "SipAccountImpl::extractProbeInfoFromResponse(): Extracting probe local address from request contact header: " << args.probeLocalAddress);
   }
   else if (request.exists(h_Vias) && (request.header(h_Vias).size() > 0) && (request.header(h_Vias).front().sentHost().size() > 0) && (request.header(h_Vias).front().sentHost() != "0.0.0.0"))
   {
      args.probeLocalAddress = request.header(h_Vias).front().sentHost().c_str();
      args.probeLocalAddress += ":";
      args.probeLocalAddress += Data(request.header(h_Vias).front().sentPort()).c_str();
      StackLog(<< "SipAccountImpl::extractProbeInfoFromResponse(): Extracting probe local address from request via header: " << args.probeLocalAddress);
   }
   else
   {
      args.probeLocalAddress = request.header(h_From).uri().getAorAsUri().toString().c_str();
      StackLog(<< "SipAccountImpl::extractProbeInfoFromResponse(): Extracting probe local address from request from header: " << args.probeLocalAddress);
   }
}

void SipAccountImpl::setProbeMockDelay(IpVersion ipVersion, unsigned int delayMsecs)
{
   DebugLog(<< "SipAccountImpl::setProbeMockDelay(): Probe delay: " << delayMsecs << " for IP version: " << ipVersion);

   if (((ipVersion != IpVersion_V4) && (ipVersion != IpVersion_V6))) // || (delayMsecs >= PROBE_TIMEOUT_MSECS)
   {
      DebugLog(<< "SipAccountImpl::setProbeMockDelay(): Ignoring invalid ip version or invalid mock delay setting");
      return;
   }

   // Cancel the probe delay timer, if the setting is updated after the timer is enabled
   mRegAfterConnProbe.mMockDelayTimer.cancel();
   mRegAfterConnProbe.mMockDelayedProbeVersion = ipVersion;
   mRegAfterConnProbe.mProbeMockDelayMsecs = delayMsecs;
}

void SipAccountImpl::setDecoratorHandler(CPCAPI2::SipAccount::SipAccountMessageDecoratorHandler* handler)
{
   DebugLog(<< "SipAccountImpl::setDecoratorHandler(): account: " << mHandle << " handler: " << handler);
   mDecoratorHandler = handler;
}

resip::SharedPtr<CPMessageDecorator> SipAccountImpl::getOutboundDecorator()
{
   return mMessageDecorator;
}

void SipAccountImpl::fireMessageDecoratedEvent(resip::SipMessage &msg, const resip::Tuple &source, const resip::Tuple &destination)
{
   if (mDecoratorHandler)
   {
      SipAccountMessageDecoratedEvent args;
      std::ostringstream ss;
      msg.encode(ss);
      args.message = ss.str().c_str();
      args.source = source.presentationFormat().c_str();
      args.destination = destination.presentationFormat().c_str();
      args.method = msg.methodStr().c_str();
      args.request = msg.isRequest();
      mDecoratorHandler->onMessageDecorated(mHandle, args);
   }
}

void SipAccountImpl::fireReleaseBindingsEvent(SipAccountMessageDecoratedBindingReleasedEvent& args)
{
   if (mDecoratorHandler)
   {
      mDecoratorHandler->onBindingReleased(mHandle, args);
   }
}

void SipAccountImpl::setAdornmentHandler(SipAccountAdornmentInternalHandler* handler)
{
   mAdornmentHandler = handler;
}

void SipAccountImpl::adornMessage(SipAccountImpl& acct, resip::SipMessage& msg) const
{
   if (!mAdornmentHandler) return;

   SipAccountAdornmentInternalEvent args;

   if (msg.isRequest())
   {
      args.method = msg.methodStr().c_str();
      args.target = msg.header(h_To).uri().getAorAsUri().toString().c_str();
      args.responseCode = 0;
   }

   if (msg.isResponse())
   {
      args.responseCode = msg.header(h_StatusLine).responseCode();
   }

   std::ostringstream ss;
   msg.encode(ss);
   args.message = ss.str().c_str();

   static unsigned int adornmentMessageId = 1;
   args.adornmentMessageId = adornmentMessageId++;

   mAdornmentHandler->onAccountAdornment(mHandle, args);

   for (cpc::vector<CPCAPI2::SipHeader>::iterator it = args.customHeaders.begin(); it != args.customHeaders.end(); ++it)
   {
      SipHelpers::setHeader(msg, it->header, it->value);
   }
}

void SipAccountImpl::onRedirectReceived(AppDialogSetHandle h, const SipMessage& originalRequest, const SipMessage& response)
{
   std::vector<SipAccountAwareFeature*>::iterator itFeat = mAccountAwareFeatures.begin();
   for (; itFeat != mAccountAwareFeatures.end(); ++itFeat)
   {
      SipAccountAwareFeature* a = *itFeat;
      a->onRedirectReceived(originalRequest, response, h);
   }
}

bool SipAccountImpl::onTryingNextTarget(AppDialogSetHandle h, SipMessage& request, const resip::Uri& target)
{
   if (request.method() == REGISTER)
   {
      request.setForceTarget(target);
      return true;
   }


   std::vector<SipAccountAwareFeature*>::iterator itFeat = mAccountAwareFeatures.begin();
   for (; itFeat != mAccountAwareFeatures.end(); ++itFeat)
   {
      SipAccountAwareFeature* a = *itFeat;
      if (!a->onRedirectTryingNextTarget(target, request, h))
      {
         return false;
      }
   }

   return true;
}

void SipAccountImpl::addSdkObserver(SipAccountHandlerInternal* handler)
{
   mInterface->addSdkObserver(handler, mHandle);
}

void SipAccountImpl::removeSdkObserver(SipAccountHandlerInternal* handler)
{
   mInterface->removeSdkObserver(handler, mHandle);
}

void SipAccountImpl::setDnsHandler(CPCAPI2::SipAccount::DialogDnsResultHandler* handler)
{
   if (mDialogDnsResultManager)
      mDialogDnsResultManager->setHandler(handler);
}

void SipAccountImpl::setIgnoreNetworkChangeStarcodeFilter(bool enabled)
{
   mIgnoreNetworkChangeStarcodeFilterEnabled = enabled;
}

bool SipAccountImpl::isIgnoreNetworkChangeStarcodeFilterEnabled() const
{
   return mIgnoreNetworkChangeStarcodeFilterEnabled;
}

resip::SharedPtr<resip::SipMessage> SipAccountImpl::makeRegistration(const resip::NameAddr& target)
{
   resip::SharedPtr<resip::SipMessage> reg = mDum->makeRegistration(target);
   const SipAccountSettings acctSettings = getSettings();
   if (acctSettings.overrideMsecsTimerF > 0)
   {
      reg->mOverrideTimerF = acctSettings.overrideMsecsTimerF;
   }

   return reg;
}

void SipAccountImpl::addRestriction(Restriction restriction, bool forced)
{
   InfoLog(<< "SipAccountImpl::addRestriction mHandle=" << mHandle << " " << restriction_str(restriction) << " : " << forced );

   if (mRestrictions.find(restriction) != mRestrictions.end())
   {
      if (!forced)
      {
         return;
      }
   }
   else
   {
      mRestrictions.insert(restriction);
   }

   if (forced)
   {
      InfoLog(<< "SipAccountImpl::addRestriction forcing disable");
      disable(forced);
      return;
   }

   if (mDisabling)
   {
      return;
   }

   AccountDisableCondition cond = mDisabler.lock();
   if (!cond)
   {
      cond = AccountDisableCondition(new SipAccountImplDisabler(this));
      mDisabler = cond;
   }

   bool canDisable = true;
   for (std::vector<SipAccountAwareFeature*>::iterator itFeat = mAccountAwareFeatures.begin(); itFeat != mAccountAwareFeatures.end(); ++itFeat)
   {
      canDisable = canDisable && (*itFeat)->canDisable(cond);
      // Do not break on !canDisable because every module needs a query
   }

   // All of this can be collapsed to nothing and the destructor of 'cond'
   // will take care of disabling the account if it's time to do so.  Left in this
   // form while the functionality is new for diagnostic purposes.
   if (canDisable)
   {
      InfoLog(<< "SipAccountImpl::addRestriction is about to call SipAccountImpl::disable()");
      assert(cond.use_count() == 1);
      cond->cancel();

      if (restriction == NetworkRestriction)
      {
         SipNetworkChangeInitiatedEvent netChangeEvent;
         netChangeEvent.newTransport = mNetworkChangeManagerIf->networkTransport();
         netChangeEvent.accountDisableTriggeredForNetworkChange = true;
         for (std::vector<SipAccountAwareFeature*>::iterator itFeat = mAccountAwareFeatures.begin(); itFeat != mAccountAwareFeatures.end(); ++itFeat)
         {
            (*itFeat)->onAccountNetworkChangeInitiated(netChangeEvent);
         }
      }

      disable();
   }
   else
   {
      InfoLog(<< "SipAccountImpl::addRestriction deferring disable for " << cond.use_count()-1 << " module(s)");
      assert(cond.use_count() > 1);
   }
}

void SipAccountImpl::removeRestriction(Restriction restriction, bool autoEnable)
{
   InfoLog(<< "SipAccountImpl::removeRestriction");
   std::set<Restriction>::iterator it = mRestrictions.find(restriction);
   if(it == mRestrictions.end())
   {
      InfoLog(<< "SipAccountImpl::removeRestriction mHandle=" << mHandle << " tried to remove account restriction " << restriction_str(restriction) << " but it doesn't exist");
      return;
   }

   mRestrictions.erase(it);

   if(!mEnabled && autoEnable && mRestrictions.empty())
   {
      InfoLog(<< "SipAccountImpl::removeRestriction mHandle=" << mHandle << " is about to call SipAccountImpl::enable()");
      enable();
   }
}

void SipAccountImpl::onTunnelStarted(const Tuple& transportTuple)
{
   InfoLog(<< "SipAccountImpl::onTunnelStarted transportTuple=" << transportTuple);

#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent args;
   args.accountStatus = SipAccountStatusChangedEvent::Status_Registered;
   args.signalingStatusCode = 0;
   args.failureRetryAfterSecs = 0;
   args.reason = SipAccountStatusChangedEvent::Reason_Server_Response;
   args.accountBindingIpAddress = "";
   args.transportType = SipAccountTransport_Unknown;
   args.ipVersionInUse = IpVersion_Auto;
   args.localContactBinding = "";  // Ensure that binding is not populated for tunnel configuratiohn

   fireAccountStatusEvent(args);

   handleNoRegistrarConfiguration();
#endif

}

void SipAccountImpl::onTunnelStopped(const Tuple& transportTuple, TransportFailure::FailureReason failureReason, int signalingCode, const std::string& signalingText)
{
   InfoLog(<< "SipAccountImpl::onTunnelStopped transportTuple=" << transportTuple << ", failureReason=" << failureReason << ", signalingCode=" << signalingCode << ", signalingText=" << signalingText);

#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent args;
   args.accountStatus = CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Status_Unregistered;
   args.signalingStatusCode = signalingCode;
   args.failureRetryAfterSecs = 0;
   if (failureReason == TransportFailure::None)
      args.reason = CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_No_Network;
   else
      args.reason = CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Tunnel_Failure;
   args.transportType = SipAccountTransport_Unknown;
   args.signalingResponseText = signalingText.c_str();
   args.accountBindingIpAddress = "";
   args.ipVersionInUse = IpVersion_Auto;
   args.localContactBinding = "";

   fireAccountStatusEvent(args);
#endif
}

void SipAccountImpl::onTunnelReconnecting(const Tuple& transportTuple, StrettoTunnelEventHandler::ReconnectingReason reconnectingReason)
{
   InfoLog(<< "SipAccountImpl::onTunnelReconnecting transportTuple=" << transportTuple << ", reconnectingReason" << reconnectingReason);

#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
   CPCAPI2::SipAccount::SipAccountStatusChangedEvent args;
   args.accountStatus = SipAccountStatusChangedEvent::Status_WaitingToRegister;
   args.signalingStatusCode = 0;
   args.failureRetryAfterSecs = 0;
   if (reconnectingReason == StrettoTunnelEventHandler::ReconnectingReason_Timeout)
      args.reason = CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Local_Timeout;
   else if (reconnectingReason == StrettoTunnelEventHandler::ReconnectingReason_NewNetwork)
      args.reason = CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_New_Network;
   else
      args.reason = CPCAPI2::SipAccount::SipAccountStatusChangedEvent::Reason_Tunnel_Failure;

   args.accountBindingIpAddress = "";
   args.transportType = SipAccountTransport_Unknown;
   args.ipVersionInUse = IpVersion_Auto;
   args.localContactBinding = "";

   // Transition to Status_WaitingToRegister first
   fireAccountStatusEvent(args);

   // Then transition to Status_Registering
   args.accountStatus = SipAccountStatusChangedEvent::Status_Registering;
   fireAccountStatusEvent(args, true);
#endif

}

void SipAccountImpl::stopRegEventSubscription()
{
#if (CPCAPI2_BRAND_SIP_REG_EVENT_MODULE == 1)
   if (mRegEventSubscriptionHandle != 0)
   {
      SipRegEvent::SipRegEventManagerInterface* regEventIf = dynamic_cast<SipRegEvent::SipRegEventManagerInterface*>(SipRegEvent::SipRegEventManager::getInterface(mPhone));
      regEventIf->endImpl(mRegEventSubscriptionHandle);
   }
#endif
}

int SipAccountImpl::onNewSubscription(CPCAPI2::SipRegEvent::SipRegEventSubscriptionHandle subscription, const CPCAPI2::SipRegEvent::NewRegEventSubscriptionEvent& args)
{
   return kSuccess;
}

int SipAccountImpl::onSubscriptionEnded(CPCAPI2::SipRegEvent::SipRegEventSubscriptionHandle subscription, const CPCAPI2::SipRegEvent::RegEventSubscriptionEndedEvent& args)
{
   return kSuccess;
}

int SipAccountImpl::onRegStateUpdated(CPCAPI2::SipRegEvent::SipRegEventSubscriptionHandle subscription, const CPCAPI2::SipRegEvent::RegEventUpdatedEvent& args)
{
   StackLog(<< "SipAccountImpl::onRegStateUpdated(): account: " << mHandle << " subscription handle: " << subscription);
   bool detectedNetworkInitiatedDereg = false;
   bool detectedOurContactBindingActive = false;
   SipRegEvent::RegistrationEventInfo evtInfo = args.registrationEventInfo;
   cpc::vector<SipRegEvent::ChangeItem>::const_iterator it = evtInfo.changes.begin();
   for (; it != evtInfo.changes.end(); ++it)
   {
      const SipRegEvent::ChangeItem& ci = *it;
      cpc::vector<SipRegEvent::Contact>::const_iterator contactIterator = ci.registration.contacts.begin();
      for (; contactIterator != ci.registration.contacts.end(); ++ contactIterator)
      {
         if (mRegistrationCallId == contactIterator->callId)
         {
            if (contactIterator->state == SipRegEvent::ContactState_Terminated || ci.registration.state == SipRegEvent::RegistrationState_Terminated)
            {
               if (ci.registrationEvent == SipRegEvent::RegEvent_Deactivated || ci.registrationEvent == SipRegEvent::RegEvent_Rejected)
               {
                  detectedNetworkInitiatedDereg = true;
               }
            } else if (contactIterator->state == SipRegEvent::ContactState_Active && ci.registration.state == SipRegEvent::RegistrationState_Active)
            {
               detectedOurContactBindingActive = true;
               break;
            }
         }
      }

      if (detectedOurContactBindingActive)
      {
         break;
      }
   }

   if (detectedNetworkInitiatedDereg && !detectedOurContactBindingActive && mAccountRegState == ARS_Registered)
   {
      if (mClientRegistration.isValid())
      {
         // .jjg. this *doesn't* un-REGISTER! it just prevents DUM from doing any more
         // registration refreshes;
         // this was added to handle the case where we get the NetworkInitiatedDeregister from
         // the network, but are on a call and so we do NOT unregister ourselves, and the call continues
         // long enough that DUM sends out a registration refresh
         mClientRegistration->stopRegistering();
      }

      AR_STATE_TO(ARS_NotRegistered);

      SipAccountStatusChangedEvent args;
      args.reason = SipAccountStatusChangedEvent::Reason_NetworkDeregistered;
      args.accountStatus = SipAccountStatusChangedEvent::Status_Unregistered;
      args.signalingStatusCode = 0;
      args.failureRetryAfterSecs = 0;
      args.accountBindingIpAddress = "";
      args.transportType = SipAccountTransport_Unknown;
      args.ipVersionInUse = IpVersion_Auto;
      args.localContactBinding = "";

      fireAccountStatusEvent(args);
   }

   return kSuccess;
}

int SipAccountImpl::onSubscriptionStateChanged(CPCAPI2::SipRegEvent::SipRegEventSubscriptionHandle subscription, const CPCAPI2::SipRegEvent::RegEventSubscriptionStateChangedEvent& args)
{
   return kSuccess;
}

int SipAccountImpl::onError(CPCAPI2::SipRegEvent::SipRegEventSubscriptionHandle subscription, const CPCAPI2::SipRegEvent::ErrorEvent& args)
{
   return kSuccess;
}

const char* SipAccountImpl::ars_str(AccountRegState ars)
{
   switch (ars)
   {
   case ARS_NotRegistered:
      return "NotRegistered";
   case ARS_Registering:
      return "Registering";
   case ARS_Registered:
      return "Registered";
   case ARS_Unregistering:
      return "Unregistering";
   case ARS_DoingNetworkChangeReReg:
      return "DoingNetworkChangeReReg";
   case ARS_DoingRportReReg:
      return "DoingRportReReg";
   case ARS_WaitingToRegister:
      return "WaitingToRegister";
   case ARS_Refreshing:
      return "Refreshing";
   case ARS_DoingOutboundReReg:
      return "DoingOutboundReReg";
   default:
      return "Unknown!";
   };
   return "Unknown!";
}

const char* SipAccountImpl::restriction_str(Restriction restriction)
{
   switch (restriction)
   {
   case UserDisabledRestriction:
      return "UserDisabledRestriction";
   case NetworkRestriction:
      return "NetworkRestriction";
   case BackgroundingRestriction:
      return "BackgroundingRestriction";
   case AppDisabledRestriction:
      return "AppDisabledRestriction";
   default:
      return "Unknown!";
   }
   return "Unknown!";
}

void SipAccountImpl::NetworkChangeHistory::add()
{
   time_t now = ::time(0);
   mHistory.push_back(now);

   time_t point = now - mDepth;
   std::list<time_t>::iterator it = mHistory.begin();
   for (; it != mHistory.end();)
   {
      if ((*it) < point)
      {
         it = mHistory.erase(it);
      }
      else
      {
         ++it;
      }
   }
}

unsigned int SipAccountImpl::NetworkChangeHistory::numChanges() const
{
   return mHistory.size();
}

resip::NameAddr SipAccountImpl::getNetworkInterfaceForLastSuccessfulRegistration() const
{
      return mNetworkInterfaceForLastSuccessfulRegistration;
}

bool SipAccountImpl::shouldIgnoreNetworkChange(const std::optional<NetworkChangeEvent>& previousEvt, const NetworkChangeEvent& evt) const
{
   // Apply the filter to ignore network changes only if:
   //  - ignore filter is enabled
   //  - it's a desktop platform
   //  - a network is available, i.e. transport is not none
   //  - we are not transitioning from no network; i.e. previous transport was not none
   //  - there are calls in progress
   //  - starcode network-change handover mode is enabled
   //  - network interface used for the existing call is still valid
#if (CPCAPI2_BRAND_CALL_MODULE == 1)
#if !defined(ANDROID)
#if defined(__linux__) || defined(_WIN32) || ((defined(__APPLE__) && (!defined(TARGET_OS_IPHONE) || TARGET_OS_IPHONE == 0)))
   bool includeLoopback = false;
#if defined(CPCAPI2_AUTO_TEST)
   includeLoopback = true;
#endif

   if (isIgnoreNetworkChangeStarcodeFilterEnabled())
   {
      if (evt.networkTransport != CPCAPI2::TransportNone)
      {
         if (!previousEvt || (previousEvt && previousEvt->networkTransport != CPCAPI2::TransportNone))
         {
            SipConversation::SipAVConversationManagerInterface* conversationInterface = dynamic_cast<SipConversation::SipAVConversationManagerInterface*>(SipConversation::SipConversationManager::getInterface(mPhone));

            if (conversationInterface && (conversationInterface->getCallCount(mHandle) > 0))
            {
               SipConversationSettings settings;
               if (conversationInterface->getConversationSettings(settings, mHandle))
               {
                  if ((settings.networkChangeHandoverMode == SipConversation::NetworkChangeHandoverMode_Starcode) && (!settings.networkChangeHandoverStarcode.empty()))
                  {
                     std::string callIp = mNetworkInterfaceForLastSuccessfulRegistration.uri().host().c_str();
                     NetworkChangeManagerImpl::NetworkChangeManagerInterfaceSet interfaces = NetworkChangeManagerImpl::getLocalIPAddresses(includeLoopback);
                     DebugLog(<< "SipAccountImpl::shouldIgnoreNetworkChange(): network interface used in last successful registration (call-ip): " << callIp << " interface-count: " << interfaces.size());
                     for (NetworkChangeManagerImpl::NetworkChangeManagerInterfaceSet::const_iterator i = interfaces.begin(); i != interfaces.end(); i++)
                     {
                        std::string iface = (*i).c_str();
                        if (callIp.compare(iface) == 0)
                        {
                           InfoLog(<< "SipAccountImpl::shouldIgnoreNetworkChange(): should ignore network change to: " << evt.networkTransport << " as call interface: " << callIp << " is still valid");
                           return true;
                        }
                        DebugLog(<< "SipAccountImpl::shouldIgnoreNetworkChange(): interface: " << iface << " does not match call-ip");
                     }
                  }
                  else
                  {
                     DebugLog(<< "SipAccountImpl::shouldIgnoreNetworkChange(): starcode ignore network change filter not applicable as network change handover is not set to starcode for account: " << mHandle << " handover-mode: " << settings.networkChangeHandoverMode << " starcode: " << settings.networkChangeHandoverStarcode);
                  }
               }
               else
               {
                  DebugLog(<< "SipAccountImpl::shouldIgnoreNetworkChange(): starcode ignore network change filter not applicable as could not get conversation settings for account: " << mHandle);
               }
            }
            else
            {
               DebugLog(<< "SipAccountImpl::shouldIgnoreNetworkChange(): starcode ignore network change filter not applicable as there are no calls");
            }
         }
         else
         {
            DebugLog(<< "SipAccountImpl::shouldIgnoreNetworkChange(): starcode ignore network change filter not applicable as previous network was none and we must handle this network change");
         }
      }
      else
      {
         DebugLog(<< "SipAccountImpl::shouldIgnoreNetworkChange(): starcode ignore network change filter not applicable as there is no network");
      }
   }
   else
   {
      DebugLog(<< "SipAccountImpl::shouldIgnoreNetworkChange(): starcode ignore network change filter is disabled");
   }

   if (getSettings().networkChangeFilter == NetworkChangeFilter_IpVer)
   {
      if (evt.networkTransport == CPCAPI2::TransportNone) return false;
      if (previousEvt && previousEvt->networkTransport == CPCAPI2::TransportNone) return false;
      if (evt.interfacesUp.empty() && evt.interfacesDown.empty()) return false;

      IpVersion ipVer = getSettings().ipVersion;
      if (ipVer != IpVersion_V4 && ipVer != IpVersion_V6) return false;

      std::stringstream down;
      cpc::vector<cpc::string>::const_iterator it = evt.interfacesDown.begin();
      for(; it != evt.interfacesDown.end(); ++it )
      {
         if ((ipVer== IpVersion_V4 && DnsUtil::isIpV4Address(it->c_str())) ||
            (ipVer == IpVersion_V6 && DnsUtil::isIpV6Address(it->c_str())))
            return false;
         down << it->c_str() << " ";
      }
      std::stringstream up;
      for(it = evt.interfacesUp.begin(); it != evt.interfacesUp.end(); ++it )
      {
         if ((ipVer == IpVersion_V4 && DnsUtil::isIpV4Address(it->c_str())) ||
            (ipVer == IpVersion_V6 && DnsUtil::isIpV6Address(it->c_str())))
            return false;
         up << it->c_str() << " ";
      }
      DebugLog(<< "SipAccountImpl::shouldIgnoreNetworkChange(): ignoring network change " << (ipVer == IpVersion_V4 ? "IPv4" : "IPv6")
         << " UP: " << up.str() << "DOWN: " << down.str());
      return true;
   }

#endif
#endif
#endif

   return false;
}

int SipAccountImpl::calcNextRegFailureRetryInterval()
{
   unsigned int failureIntervalSeconds = mCurrentFailureRegistrationIntervalSeconds;

   if (mInitialFailureRegistrationRetryAttemptsPending > 0)
   {
      mInitialFailureRegistrationRetryAttemptsPending--;
   }
   else
   {
      const SipAccountSettings acctSettings = getSettings();
      failureIntervalSeconds = 2 * failureIntervalSeconds;

      if (failureIntervalSeconds <= acctSettings.maximumRegistrationIntervalSeconds)
      {
         mCurrentFailureRegistrationIntervalSeconds = failureIntervalSeconds;
      }
      else
      {
         mCurrentFailureRegistrationIntervalSeconds = acctSettings.maximumRegistrationIntervalSeconds;
      }
   }

   DebugLog(<< "Failure Re-Registration Interval: " << mCurrentFailureRegistrationIntervalSeconds
      << " Initial Attempts Pending: " << mInitialFailureRegistrationRetryAttemptsPending);

   return mCurrentFailureRegistrationIntervalSeconds;
}

bool SipAccountImpl::retryOn(cpc::string method, int responseCode)
{
   cpc::vector<SipResponseType> reRegOn = getSettings().reRegisterOnResponseTypes;
   cpc::vector<SipResponseType>::const_iterator itResp = reRegOn.begin();

   for (; itResp != reRegOn.end(); ++itResp)
   {
      if (itResp->method == method && itResp->responseCode == responseCode)
      {
         return true;
      }
   }
   return false;
}

void SipAccountImpl::setTlsConnectionInfo()
{
   if (mStack && mStack->getSecurity())
   {
      resip::Security::TlsConnectionCertInfo certInfo = mStack->getSecurity()->getLastTlsConnectionCertInfo();

      mTlsConnInfo.certificateStatus = certInfo.status;
      mTlsConnInfo.cipher = certInfo.cipher.c_str();
      mTlsConnInfo.compression = certInfo.compression.c_str();
      mTlsConnInfo.issuer = certInfo.issuer.c_str();
      for (std::list<BaseSecurity::PeerName>::const_iterator it = certInfo.peerNames.begin(); it != certInfo.peerNames.end(); it++)
      {
         mTlsConnInfo.peerNames.push_back(it->mName.c_str());
      }
      mTlsConnInfo.protocol = certInfo.protocol.c_str();
      mTlsConnInfo.publicKey = certInfo.pubKey.c_str();
      mTlsConnInfo.server = certInfo.server.c_str();
      mTlsConnInfo.sslVersion = getSSLVersion(certInfo.sslProtocol);
   }
}

void SipAccountImpl::onDumTimer()
{
   if ( mEnabled && (!mClientRegistration.isValid()) )
   {
      // Actuall retry request is sent from ClientRegistration::dispatch(...).
      // Here we only inform application level of our action
      AR_STATE_TO(ARS_Registering);
      SipAccountStatusChangedEvent args;
      args.reason = SipAccountStatusChangedEvent::Reason_None;
      args.accountStatus = SipAccountStatusChangedEvent::Status_Registering;
      args.signalingStatusCode = 0;
      args.failureRetryAfterSecs = 0;
      args.signalingResponseText = "";
      args.accountBindingIpAddress = "";
      args.transportType = SipAccountTransport_Unknown;
      args.ipVersionInUse = IpVersion_Auto;
      args.localContactBinding = "";
      fireAccountStatusEvent(args, true);
   }
}

#if TARGET_OS_IPHONE
void SipAccountImpl::setReadActivityCallback(void (*callback)(void *userData), void *userData)
{
   mReadActivityCallback = callback;
   mReadActivityCallbackUserData = userData;
   enableReadActivityCallback();
}

void SipAccountImpl::enableReadActivityCallback()
{
   if (mTcpTransport)
   {
      mTcpTransport->setReadActivityCallback(mReadActivityCallback, mReadActivityCallbackUserData);
   }
   if (mTcpTransportV6)
   {
      mTcpTransportV6->setReadActivityCallback(mReadActivityCallback, mReadActivityCallbackUserData);
   }
   if (mTlsTransport)
   {
      mTlsTransport->setReadActivityCallback(mReadActivityCallback, mReadActivityCallbackUserData);
   }
   if (mTlsTransportV6)
   {
      mTlsTransportV6->setReadActivityCallback(mReadActivityCallback, mReadActivityCallbackUserData);
   }
}
#endif


void SipAccountImpl::setUseAlias(bool useAlias)
{
   mUseAlias = useAlias;
}

void SipAccountImpl::discardRegistration()
{
   if (mClientRegistration.isValid())
   {
      DebugLog(<< "Discarding existing client registration");
      mClientRegistration->stopRegistering();
      mClientRegistration = ClientRegistrationHandle::NotValid();
   }
   else if (mDum)
   {
      DebugLog(<< "Discarding existing registration - currently not registered");
      // Need to call stop registration to delete registration sessions that were not successful as such
      // we do not have a valid client registration handle. Otherwise we end up sending re-registrations
      // from stale dialogs.
      mDum->stopRegistration();
   }
}

void SipAccountImpl::setSuppressUnregister(bool suppress)
{
#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)
   mSuppressUnregister = suppress;
#endif
}

void SipAccountImpl::setCertStorageLoadType(SipAccountManagerInternal::CertLoadStorageType type)
{
#ifdef USE_SSL
   mCertStorageMask = 0;
   if (type == SipAccountManagerInternal::CertLoadStorageType_OS)
   {
      mCertStorageMask |= resip::CERT_OS_SPECIFIC_STORAGE;
   }
   else if (type == SipAccountManagerInternal::CertLoadStorageType_FileSystem)
   {
      mCertStorageMask |= resip::CERT_FILESYSTEM_STORAGE;
   }
#endif
}

void SipAccountImpl::setCertStorageFileSystemPath(const std::string& path)
{
   mCertStorageFileSystemPath = path.c_str();
}

bool SipAccountImpl::CpcClientAuthManager::handle(UserProfile& userProfile, SipMessage& origRequest, const SipMessage& response)
{
   bool ret = ClientAuthManager::handle(userProfile, origRequest, response);

   /* StackLog(<< "SipAccountImpl::CpcClientAuthManager::handle(): onClientAuth:"
            << " request method: " << (origRequest.isRequest() ? resip::getMethodName(response.header(h_RequestLine).method()).c_str() : "")
            << " response method: " << (response.isResponse() ? resip::getMethodName(response.header(h_CSeq).method()).c_str() : "")
            << " response code: " << (response.isResponse() ? response.header(h_StatusLine).statusCode() : 0) << " response source: "
            << response.getSource().getPort() << " response: " << response); */

   // Trigger onClientAuth only for REGISTER requests
   if (response.isResponse() && response.header(h_CSeq).method() == REGISTER)
   {
      SipAccountClientAuthEvent args;
      args.willSendUpdatedRequest = ret;
      args.responseStatusCode = response.header(h_StatusLine).statusCode();
      args.responseSourceIp = resip::Tuple::inet_ntop(response.getSource()).c_str();
      args.responseSourcePort = response.getSource().getPort();

      mSipAccountImpl.fireClientAuthEvent(args);
   }

   return ret;
}

void SipAccountImpl::fireClientAuthEvent(const SipAccountClientAuthEvent& args)
{
   mInterface->fireEvent(cpcEvent(SipAccountHandlerInternal, onClientAuth), mHandle, args);
}

void SipAccountImpl::fireDnsResetEvent(const SipAccountDnsResetEvent& args)
{
   mInterface->fireEvent(cpcEvent(SipAccountHandlerInternal, onDnsReset), mHandle, args);
}

void SipAccountImpl::fireRegistrationRportUpdateEvent(const std::string& receivedAddress, const int rport)
{
#ifdef CPCAPI2_AUTO_TEST
   SipAccountRegistrationRportUpdateEvent rportUpdateEvt;
   rportUpdateEvt.rport = rport;
   rportUpdateEvt.receivedAddress = receivedAddress.c_str();
   mInterface->fireEvent(cpcEvent(SipAccountHandlerInternal, onRegistrationRportUpdate), mHandle, rportUpdateEvt);
#endif
}

void SipAccountImpl::fireDnsResultManagerError(const ErrorEvent& args)
{
   mInterface->postToSdkThread(makeFpCommand(SipAccountHandler::onError, mDialogDnsResultManager, mHandle, args));
}

int SipAccountImpl::freezeSipDnsCache()
{
   int retCode = kError;
   if (mStack)
   {
      const resip::SharedPtr<DnsStub>& dnsStub = mStack->getDnsStub();
      if (dnsStub.get())
      {
         DebugLog(<< "Freezing SIP DNS cache");
         dnsStub->setIgnoreTTL(RRList::Protocol::Sip, true);
         retCode = kSuccess;
      }
      else
      {
         ErrLog(<< "Can't freeze SIP DNS cache");
      }
   }
   else
   {
      ErrLog(<< "Can't freeze SIP DNS cache");
   }

   return retCode;
}

int SipAccountImpl::unfreezeSipDnsCache()
{
   int retCode = kError;
   if (mStack)
   {
      const resip::SharedPtr<DnsStub>& dnsStub = mStack->getDnsStub();
      if (dnsStub.get())
      {
         DebugLog(<< "Un-freezing SIP DNS cache");
         dnsStub->setIgnoreTTL(RRList::Protocol::Sip, false);
         retCode = kSuccess;
      }
      else
      {
         ErrLog(<< "Can't un-freeze SIP DNS cache");
      }
   }
   else
   {
      ErrLog(<< "Can't un-freeze SIP DNS cache");
   }

   return retCode;
}


SecurityTypes::SSLType SipAccountImpl::getSSLType(SSLVersion sslVersion)
{
   SecurityTypes::SSLType sslType = SecurityTypes::NoSSL;
   switch (sslVersion)
   {
      case TLS_DEFAULT: sslType = SecurityTypes::TLS_HIGHEST; break;
      case SSL_NONE: sslType = SecurityTypes::NoSSL; break;
      case SSL_V2: sslType = SecurityTypes::SSLv2; break;
      case SSL_V3: sslType = SecurityTypes::SSLv3; break;
      case TLS_V1_0: sslType = SecurityTypes::TLSv1_0; break;
      case TLS_V1_1: sslType = SecurityTypes::TLSv1_1; break;
      case TLS_V1_2: sslType = SecurityTypes::TLSv1_2; break;
      case TLS_V1_3: sslType = SecurityTypes::TLSv1_3; break;
      case SSL_HIGHEST: sslType = SecurityTypes::TLS_HIGHEST; break;
      case TLS_NON_DEPRECATED: sslType = SecurityTypes::TLS_NON_DEPRECATED; break;
   }

   return sslType;
}

SSLVersion SipAccountImpl::getSSLVersion(SecurityTypes::SSLType sslType)
{
   SSLVersion sslVersion = SSL_NONE;
   switch (sslType)
   {
      case SecurityTypes::TLS_Default: sslVersion = TLS_DEFAULT; break;
      case SecurityTypes::SSLv2: sslVersion = SSL_NONE; break;
      case SecurityTypes::SSLv3: sslVersion = SSL_NONE; break;
      case SecurityTypes::NoSSL: sslVersion = SSL_NONE; break;
      case SecurityTypes::TLSv1_0: sslVersion = TLS_V1_0; break;
      case SecurityTypes::TLSv1_1: sslVersion = TLS_V1_1; break;
      case SecurityTypes::TLSv1_2: sslVersion = TLS_V1_2; break;
      case SecurityTypes::TLSv1_3: sslVersion = TLS_V1_3; break;
      case SecurityTypes::TLS_HIGHEST: sslVersion = SSL_HIGHEST; break;
      case SecurityTypes::TLS_NON_DEPRECATED: sslVersion = TLS_NON_DEPRECATED; break;
   }

   return sslVersion;
}

SSLVersion SipAccountImpl::getSSLVersion(const Data& sslType)
{
   SSLVersion sslVersion = SSL_NONE;
   if ((strcmp(sslType.c_str(), "SSLv2") == 0) || (strcmp(sslType.c_str(), "SSL_v2") == 0)
      || (strcmp(sslType.c_str(), "SSL_V2") == 0)) sslVersion = SSL_V2;
   else if ((strcmp(sslType.c_str(), "SSLv3") == 0) || (strcmp(sslType.c_str(), "SSL_v3") == 0)
      || (strcmp(sslType.c_str(), "SSL_V3") == 0)) sslVersion = SSL_V3;
   else if ((strcmp(sslType.c_str(), "TLSv1") == 0) || (strcmp(sslType.c_str(), "TLSv1.0") == 0) || (strcmp(sslType.c_str(), "TLS_v1_0") == 0)
      || (strcmp(sslType.c_str(), "TLS_V1_0") == 0) || (strcmp(sslType.c_str(), "TLS_V1") == 0)) sslVersion = TLS_V1_0;
   else if ((strcmp(sslType.c_str(), "TLSv1.1") == 0) || (strcmp(sslType.c_str(), "TLS_v1_1") == 0)
      || (strcmp(sslType.c_str(), "TLS_V1_1") == 0)) sslVersion = TLS_V1_1;
   else if ((strcmp(sslType.c_str(), "TLSv1.2") == 0) || (strcmp(sslType.c_str(), "TLS_v1_2") == 0)
      || (strcmp(sslType.c_str(), "TLS_V1_2") == 0)) sslVersion = TLS_V1_2;
   else if ((strcmp(sslType.c_str(), "TLSv1.3") == 0) || (strcmp(sslType.c_str(), "TLS_v1_3") == 0)
      || (strcmp(sslType.c_str(), "TLS_V1_3") == 0)) sslVersion = TLS_V1_3;

   // This utility function is for translating SSL version string negotiated with remote entity, commenting
   // out highest enum setting as it is not a valid negotiated version value that will be received from network
   //
   // else if ((strcmp(sslType.c_str(), "SSLv23") == 0) || (strcmp(sslType.c_str(), "SSL_v23") == 0)
   //   || (strcmp(sslType.c_str(), "SSL_V23") == 0)) sslVersion = SSL_HIGHEST;

   return sslVersion;
}

SipAccountTransportType SipAccountImpl::getTransportType(resip::TransportType transportType)
{
   SipAccountTransportType sipTransportType = SipAccountTransport_Unknown;
   switch (transportType)
   {
      case resip::TransportType::TLS: sipTransportType = SipAccountTransport_TLS; break;
      case resip::TransportType::TCP: sipTransportType = SipAccountTransport_TCP; break;
      case resip::TransportType::UDP: sipTransportType = SipAccountTransport_UDP; break;
      default: break;
   }

   return sipTransportType;
}

resip::TransportType SipAccountImpl::getTransportType(SipAccountTransportType sipTransportType)
{
   resip::TransportType transportType = resip::TransportType::UNKNOWN_TRANSPORT;
   switch (sipTransportType)
   {
      case SipAccountTransport_TLS: transportType = resip::TransportType::TLS; break;
      case SipAccountTransport_TCP: transportType = resip::TransportType::TCP; break;
      case SipAccountTransport_UDP: transportType = resip::TransportType::UDP; break;
      default: break;
   }

   return transportType;
}

cpc::string SipAccountImpl::status_str(const SipAccountStatusChangedEvent::Status& eStatus)
{
   std::stringstream os;
   switch (eStatus)
   {
      case SipAccountStatusChangedEvent::Status_Registered: os << "Status_Registered"; break;
      case SipAccountStatusChangedEvent::Status_Failure: os << "Status_Failure"; break;
      case SipAccountStatusChangedEvent::Status_Unregistered: os << "Status_Unregistered"; break;
      case SipAccountStatusChangedEvent::Status_Registering: os << "Status_Registering"; break;
      case SipAccountStatusChangedEvent::Status_Unregistering: os << "Status_Unregistering"; break;
      case SipAccountStatusChangedEvent::Status_WaitingToRegister: os << "Status_WaitingToRegister"; break;
      case SipAccountStatusChangedEvent::Status_Refreshing: os << "Status_Refreshing"; break;
      default: os << "UNDEFINED"; break;
   }

   os << " (" << eStatus << ")";
   return os.str().c_str();
}

cpc::string SipAccountImpl::reason_str(const SipAccountStatusChangedEvent::Reason& eReason)
{
   std::stringstream os;

   switch (eReason)
   {
      case SipAccountStatusChangedEvent::Reason_None: os << "Reason_None"; break;
      case SipAccountStatusChangedEvent::Reason_No_Network: os << "Reason_No_Network"; break;
      case SipAccountStatusChangedEvent::Reason_Restricted_Network: os << "Reason_Restricted_Network"; break;
      case SipAccountStatusChangedEvent::Reason_New_Network: os << "Reason_New_Network"; break;
      case SipAccountStatusChangedEvent::Reason_Server_Response: os << "Reason_Server_Response"; break;
      case SipAccountStatusChangedEvent::Reason_Local_Timeout: os << "Reason_Local_Timeout"; break;
      case SipAccountStatusChangedEvent::Reason_NetworkDeregistered: os << "Reason_NetworkDeregistered"; break;
      case SipAccountStatusChangedEvent::Reason_Tunnel_Failure: os << "Reason_Tunnel_Failure"; break;
      case SipAccountStatusChangedEvent::Reason_Dns_Lookup: os << "Reason_Dns_Lookup"; break;
      case SipAccountStatusChangedEvent::Reason_Transport_Protocol_Mismatch: os << "Reason_Transport_Protocol_Mismatch"; break;
      case SipAccountStatusChangedEvent::Reason_No_Route_To_Host: os << "Reason_No_Route_To_Host"; break;
      case SipAccountStatusChangedEvent::Reason_TLS_Cipher_Mismatch: os << "Reason_TLS_Cipher_Mismatch"; break;
      case SipAccountStatusChangedEvent::Reason_Domain_Locked: os << "Reason_Domain_Locked"; break;
      default: os << "UNDEFINED"; break;
   }

   os << " (" << eReason << ")";
   return os.str().c_str();
}

resip::SharedPtr<resip::SipMessage> SipAccountImpl::makeV4V6ConnectivityProbe(const resip::Uri& target, resip::IpVersion ipVersion)
{
   resip::SharedPtr<SipMessage> options = mDum->makeOutOfDialogRequest(resip::NameAddr(target), resip::OPTIONS);
   if (ipVersion == resip::V4)
   {
      resip::ExtensionHeader h_XConnectivityProbe("X-Connectivity-Probe-V4");
      options->header(h_XConnectivityProbe).push_back(resip::StringCategory("0"));
   }
   else if (ipVersion == resip::V6)
   {
      resip::ExtensionHeader h_XConnectivityProbe("X-Connectivity-Probe-V6");
      options->header(h_XConnectivityProbe).push_back(resip::StringCategory("0"));
   }

   options->header(h_MaxForwards).value() = 0;
   return options;
}

bool SipAccountImpl::isIpv6CapableVersion(IpVersion ipv) const
{
   if (ipv == IpVersion_V6 ||
       ipv == IpVersion_Auto ||
       ipv == IpVersion_Auto_PreferV6)
   {
      return true;
   }

   return false;
}

bool SipAccountImpl::isIpv4CapableVersion(IpVersion ipv) const
{
   if (ipv == IpVersion_V4 ||
       ipv == IpVersion_Auto ||
       ipv == IpVersion_Auto_PreferV6)
   {
      return true;
   }

   return false;
}

StunDnsSrvRecord SipAccountImpl::getStunDnsSrvRecord()
{
   return mStunClient->getDnsSrvRecord(getSettings().domain.c_str());
}

int SipAccountImpl::checkBrandingDomainLock()
{
   cpc::string domainLockStr = "";
#if defined(CPCAPI2_AUTO_TEST)
   domainLockStr = mInterface->getDomainLockString();
#elif defined(CPCAPI2_BRAND_LICENSE_DOMAIN_LOCK)
   domainLockStr = CPCAPI2_BRAND_LICENSE_DOMAIN_LOCK;
#endif
   if (!domainLockStr.empty() && domainLockStr != cpc::string("%BRAND_LICENSE_DOMAIN_LOCK%") /* handle missing brand string */)
   {
      if (BrandingHelper::isDomainWildcardMatch(getDomain(), domainLockStr))
      {
         InfoLog(<< "Registration domain " << getDomain() << " passed domain lock check: " << domainLockStr);
      }
      else
      {
         mEnabled = false;

         WarningLog(<< "Registration for " << getDomain() << " failed due to domain lock: " << domainLockStr);
         SipAccountStatusChangedEvent args;
         args.signalingStatusCode = 0;
         args.failureRetryAfterSecs = 0;
         args.accountBindingIpAddress = "";
         args.transportType = SipAccountTransport_Unknown;
         args.ipVersionInUse = IpVersion_Auto;
         args.accountStatus = SipAccountStatusChangedEvent::Status_Unregistered;
         args.reason = SipAccountStatusChangedEvent::Reason_Domain_Locked;
         args.localContactBinding = "";
         fireAccountStatusEvent(args);

         return kError;
      }
   }

   return kSuccess;
}

}

}

#endif // CPCAPI2_BRAND_ACCOUNT_MODULE
