#pragma once

#if !defined(CPCAPI2_INSTANT_MESSAGE_MANAGER_INTERNAL_H)
#define CPCAPI2_INSTANT_MESSAGE_MANAGER_INTERNAL_H

#include <im/SipInstantMessage.h>

namespace CPCAPI2
{

   namespace SipInstantMessage
   {

      class CPCAPI2_SHAREDLIBRARY_API SipInstantMessageManagerInternal
      {
      public:
         virtual void sendMessage(
            CPCAPI2::SipAccount::SipAccountHandle account,
            SipInstantMessageHandle imHandle,
            const cpc::string& targetAddress,
            const char* content,
            unsigned int contentLength,
            SipInstantMessageManager::MimeType mimeType,
            const cpc::string& messageType = "") = 0;
         virtual void sendMessage(
            CPCAPI2::SipAccount::SipAccountHandle account,
            SipInstantMessageHandle imHandle,
            const cpc::string& targetAddress,
            const char* content,
            unsigned int contentLength,
            const cpc::string& customMimeType,
            const cpc::string& messageType = "") = 0;
      };

   }
}
#endif // CPCAPI2_INSTANT_MESSAGE_MANAGER_INTERNAL_H