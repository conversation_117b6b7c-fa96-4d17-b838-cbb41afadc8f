// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef XmppAgent_JSON_HELPER_H
#define XmppAgent_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/experimental/xmpp_agent/XmppAgentTypes.h>
#include <interface/experimental/xmpp_agent/XmppAgentHandler.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAgent::XmppPushRegistrationInfo& v)
    {
      w.StartObject();
      Write(w, "xmppAccountHandle", v.xmppAccountHandle);
      Write(w, "pushNotificationDev", v.pushNotificationDev);
      Write(w, "pushNotificationServiceHandle", v.pushNotificationServiceHandle);
      Write(w, "jsonUserHandle", v.jsonUserHandle);
      Write(w, "pushServerUrl", v.pushServerUrl);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAgent::XmppPushRegistrationInfo& v)
    {
      Read(json, "xmppAccountHandle", v.xmppAccountHandle);
      Read(json, "pushNotificationDev", v.pushNotificationDev);
      Read(json, "pushNotificationServiceHandle", v.pushNotificationServiceHandle);
      Read(json, "jsonUserHandle", v.jsonUserHandle);
      Read(json, "pushServerUrl", v.pushServerUrl);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAgent::XmppChatEvent& v)
    {
      w.StartObject();
      Write(w, "eventId", v.eventId);
      Write(w, "eventType", v.eventType);
      Write(w, "account", v.account);
      Write(w, "chat", v.chat);
      Write(w, "newChatEvent", v.newChatEvent);
      Write(w, "newMessageEvent", v.newMessageEvent);
      Write(w, "chatEndedEvent", v.chatEndedEvent);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAgent::XmppChatEvent& v)
    {
      Read(json, "eventId", v.eventId);
      Read(json, "eventType", v.eventType);
      Read(json, "account", v.account);
      Read(json, "chat", v.chat);
      Read(json, "newChatEvent", v.newChatEvent);
      Read(json, "newMessageEvent", v.newMessageEvent);
      Read(json, "chatEndedEvent", v.chatEndedEvent);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAgent::XmppPushRegistrationSuccessEvent& v)
    {
      w.StartObject();
      Write(w, "pushEndpointId", v.pushEndpointId);
      Write(w, "xmppAccountHandle", v.xmppAccountHandle);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAgent::XmppPushRegistrationSuccessEvent& v)
    {
      Read(json, "pushEndpointId", v.pushEndpointId);
      Read(json, "xmppAccountHandle", v.xmppAccountHandle);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAgent::XmppPushRegistrationFailureEvent& v)
    {
      w.StartObject();
      Write(w, "errorText", v.errorText);
      Write(w, "pushEndpointId", v.pushEndpointId);
      Write(w, "xmppAccountHandle", v.xmppAccountHandle);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAgent::XmppPushRegistrationFailureEvent& v)
    {
      Read(json, "errorText", v.errorText);
      Read(json, "pushEndpointId", v.pushEndpointId);
      Read(json, "xmppAccountHandle", v.xmppAccountHandle);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAgent::XmppEventHistory& v)
    {
      w.StartObject();
      Write(w, "chatEventHistory", v.chatEventHistory);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAgent::XmppEventHistory& v)
    {
      Read(json, "chatEventHistory", v.chatEventHistory);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAgent::XmppAgentRemoteSyncRegisterResult& v)
    {
      w.StartObject();
      Write(w, "sessionHandle", v.sessionHandle);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAgent::XmppAgentRemoteSyncRegisterResult& v)
    {
      Read(json, "sessionHandle", v.sessionHandle);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAgent::LogoutResult& v)
    {
      w.StartObject();
      Write(w, "success", v.success);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAgent::LogoutResult& v)
    {
      Read(json, "success", v.success);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAgent::XmppAgentQueryListResult& v)
    {
      w.StartObject();
      Write(w, "registrationList", v.registrationList);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAgent::XmppAgentQueryListResult& v)
    {
      Read(json, "registrationList", v.registrationList);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAgent::XmppAgentQueryInfoResult& v)
    {
      w.StartObject();
      Write(w, "xmppAccount", v.xmppAccount);
      Write(w, "xmppPushRegistration", v.xmppPushRegistration);
      Write(w, "xmppAccountHandle", v.xmppAccountHandle);
      Write(w, "pushNotificationDev", v.pushNotificationDev);
      Write(w, "pushNotificationServiceHandle", v.pushNotificationServiceHandle);
      Write(w, "jsonUserHandle", v.jsonUserHandle);
      Write(w, "syncSessionHandle", v.syncSessionHandle);
      Write(w, "isRegisteredForPush", v.isRegisteredForPush);
      Write(w, "isLoggedOut", v.isLoggedOut);
      Write(w, "serviceDownAtMs", v.serviceDownAtMs);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAgent::XmppAgentQueryInfoResult& v)
    {
      Read(json, "xmppAccount", v.xmppAccount);
      Read(json, "xmppPushRegistration", v.xmppPushRegistration);
      Read(json, "xmppAccountHandle", v.xmppAccountHandle);
      Read(json, "pushNotificationDev", v.pushNotificationDev);
      Read(json, "pushNotificationServiceHandle", v.pushNotificationServiceHandle);
      Read(json, "jsonUserHandle", v.jsonUserHandle);
      Read(json, "syncSessionHandle", v.syncSessionHandle);
      Read(json, "isRegisteredForPush", v.isRegisteredForPush);
      Read(json, "isLoggedOut", v.isLoggedOut);
      Read(json, "serviceDownAtMs", v.serviceDownAtMs);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppAgent::XmppAgentHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppAgent::XmppAgentHandler& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // XmppAgent_JSON_HELPER_H