#include "jsonrpc/OptionalSerialization.h"
#include "jsonrpc/StringSerialization.h"
#include <nlohmann/json.hpp>
#include "ConversationState.h"

#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONCONVERSATIONSTATECHANGEDEVENT_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONCONVERSATIONSTATECHANGEDEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationConversationStateChangedEvent
  {
  public:
    std::optional<int32_t> conversationHandle;
    std::optional<ConversationState> conversationState;
    std::optional<cpc::string> remoteAddress;
    std::optional<cpc::string> remoteDisplayName;
    std::optional<cpc::string> statusText;

    SipConversationConversationStateChangedEvent() = default;

    SipConversationConversationStateChangedEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationConversationStateChangedEvent& o)
    {
      j = nlohmann::json::object();
       if (o.conversationHandle) { j["conversationHandle"] = *(o.conversationHandle); }
     if (o.conversationState) { j["conversationState"] = *(o.conversationState); }
     if (o.remoteAddress) { j["remoteAddress"] = *(o.remoteAddress); }
     if (o.remoteDisplayName) { j["remoteDisplayName"] = *(o.remoteDisplayName); }
     if (o.statusText) { j["statusText"] = *(o.statusText); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationConversationStateChangedEvent& o)
    {
     if (j.contains("conversationHandle")) { o.conversationHandle = j.at("conversationHandle"); }
     if (j.contains("conversationState")) { o.conversationState = j.at("conversationState"); }
     if (j.contains("remoteAddress")) { o.remoteAddress = j.at("remoteAddress"); }
     if (j.contains("remoteDisplayName")) { o.remoteDisplayName = j.at("remoteDisplayName"); }
     if (j.contains("statusText")) { o.statusText = j.at("statusText"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONCONVERSATIONSTATECHANGEDEVENT_H
