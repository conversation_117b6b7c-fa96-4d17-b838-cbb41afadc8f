#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_CHAT_MODULE==1 || CPCAPI2_BRAND_IM_MODULE==1 || CPCAPI2_BRAND_XMPP_CHAT_MODULE==1 || CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
#include "cpcapi2utils.h"
#include "IsComposingHelper.h"

#include <assert.h>

namespace CPCAPI2
{
namespace IsComposing
{

const resip::Mime IsComposingHelper::IS_COMPOSING_CONTENT_TYPE = resip::Mime("application", "im-iscomposing+xml");

IsComposingDocument IsComposingHelper::createIsComposingDocument(IsComposingMessageState state, const resip::Mime& contentType, int refresh, const tm& lastActive)
{   
   // Generate the XML content
   IsComposingDocument isComposingDocument;
   isComposingDocument.setState(state);
   isComposingDocument.setLastActive(lastActive);
   isComposingDocument.setContentType(contentType);
   isComposingDocument.setRefresh(refresh);
   cpc::string content = isComposingDocument.toString();

   return isComposingDocument;
}

void IsComposingHelper::extractIsComposingDocument(const IsComposingDocument& isComposingDocument, IsComposingMessageState& state, resip::Mime& contentType, int& refreshInterval, tm& lastActive)
{
   // Get the state
   state = isComposingDocument.getState();

   // Get the content type. Defaults to text/plain if not specified
   resip::Mime specifiedContentType = isComposingDocument.getContentType();
   contentType = (specifiedContentType != resip::Mime()) ? specifiedContentType : TEXT_PLAIN_CONTENT_TYPE;

   // Get the refresh interval
   refreshInterval = isComposingDocument.getRefresh();

   // Get the last active value
   lastActive = isComposingDocument.getLastActive();
}

}
}
#endif
