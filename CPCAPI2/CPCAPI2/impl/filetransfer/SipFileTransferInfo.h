#pragma once

#ifndef __CPCAPI2_SIPFILETRANSFERINFO_H__
#define __CPCAPI2_SIPFILETRANSFERINFO_H__

#include <map>
#include <atomic>

#include "cpcapi2defs.h"
#include "filetransfer/SipFileTransferManager.h"
#include "filetransfer/SipFileTransferHandler.h"
#include "SipFileTransferFileMap.h"
#include "../account/CPInviteHandlerSelectorDialogSet.h"

// MSRP includes
#include <msrp_forward_decl.h>
#include <utils/msrp_list.h>

namespace CPCAPI2
{
   namespace SipFileTransfer
   {
      typedef struct SipFileTransferItemInfo
      {
         SipFileTransferItemInfo( SipFileTransferItemHandle newHandle );
         ~SipFileTransferItemInfo();

         SipFileTransferItemDetail detail; // all the information

         // Additional data required for libmsrp stack
         msrp_uri_t     *m_pLocal;
         msrp_uri_t     *m_pRemote;
         msrp_message_t *m_pMessage; // memory not owned
         FileMap *m_Map;
      } SipFileTransferItemInfo;

      typedef std::shared_ptr< SipFileTransferItemInfo > SipFileTransferItemInfoPtr;
      typedef std::weak_ptr< SipFileTransferItemInfo > SipFileTransferItemInfoWeakPtr;
      typedef std::map< SipFileTransferItemHandle, SipFileTransferItemInfoWeakPtr > SipFileTransferItemInfoMap;

      typedef struct SipFileTransferInfo : public SipAccount::CPInviteHandlerSelectorDialogSet
      {
         SipFileTransferInfo( SipFileTransferHandle newHandle, resip::SharedPtr<resip::DialogUsageManager> dum, resip::InviteSessionHandler* handler);
         ~SipFileTransferInfo();

         // One or the other (but not both) will be NULL
         resip::ClientInviteSessionHandle       clientInviteSessionHandle;
         resip::ServerInviteSessionHandle       serverInviteSessionHandle;

         // weak pointer to file transfer items "contained" within this transfer
         SipFileTransferItemInfoMap transferItems;

         SipAccount::SipAccountHandle           accountHandle;
         resip::NameAddr                        targetAddress;
         SipFileTransferHandle                  handle;

         cpc::string localSIPURI;  // stored in order to build new file transfer event later
         cpc::string remoteSIPURI; // stored in order to build new file transfer event later

         // MSRP Sessions associated with the file transfer call. There might be more than
         // one session because it's possible to transfer multiple items in a single file transfer.
         msrp_list_t *m_pSessions; /** List of msrp_session_t * objects */

         bool                                   m_isActive; // initiates the connection (or not)
         bool                                   m_OfferedAsSendRecv; // for responding correctly
      } SipFileTransferInfo;

      typedef std::map< SipFileTransferHandle, SipFileTransferInfo * > SipFileTransferInfoMap;
   }
}

extern CPCAPI2::SipFileTransfer::SipFileTransferHandle sNextFileTransferHandle;
extern CPCAPI2::SipFileTransfer::SipFileTransferItemHandle sNextFileTransferItemHandle;

#endif // __CPCAPI2_SIPFILETRANSFERINFO_H__
