#include "IAudio.h"
#include "impl/jsonrpc/IJsonRpcServerModuleProcessing.h"

#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOSERVER_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOSERVER_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
class AudioServer
{
public:
    AudioServer(::CPCAPI2::IJsonRpcServerModuleProcessing& transport, IAudio& impl) :
        mTransport(transport),
        mImpl(impl)
    {
        mTransport.registerFunction("Audio::queryDeviceList", jsonrpccxx::methodHandle(&IAudio::queryDeviceList, mImpl), {{ "phoneHandle" }});
	mTransport.registerFunction("Audio::setCaptureDevice", jsonrpccxx::methodHandle(&IAudio::setCaptureDevice, mImpl), {{ "phoneHandle" }, { "deviceId" }, { "role" }});
	mTransport.registerFunction("Audio::setRenderDevice", jsonrpccxx::methodHandle(&IAudio::setRenderDevice, mImpl), {{ "phoneHandle" }, { "deviceId" }, { "role" }});
	mTransport.registerFunction("Audio::playSound", jsonrpccxx::methodHandle(&IAudio::playSound, mImpl), {{ "phoneHandle" }, { "role" }, { "resourceUri" }, { "repeat" }});
	mTransport.registerFunction("Audio::stopPlaySound", jsonrpccxx::methodHandle(&IAudio::stopPlaySound, mImpl), {{ "phoneHandle" }, { "sound" }});
	mTransport.registerFunction("Audio::queryCodecList", jsonrpccxx::methodHandle(&IAudio::queryCodecList, mImpl), {{ "phoneHandle" }});
	mTransport.registerFunction("Audio::setCodecEnabled", jsonrpccxx::methodHandle(&IAudio::setCodecEnabled, mImpl), {{ "phoneHandle" }, { "codecId" }, { "enabled" }});
	mTransport.registerFunction("Audio::setCodecPriority", jsonrpccxx::methodHandle(&IAudio::setCodecPriority, mImpl), {{ "phoneHandle" }, { "codecId" }, { "priority" }});
	mTransport.registerFunction("Audio::setCodecPayloadType", jsonrpccxx::methodHandle(&IAudio::setCodecPayloadType, mImpl), {{ "phoneHandle" }, { "codecId" }, { "payloadType" }});
	mTransport.registerFunction("Audio::setTelephoneEventPayloadType", jsonrpccxx::methodHandle(&IAudio::setTelephoneEventPayloadType, mImpl), {{ "phoneHandle" }, { "payloadType" }});
	mTransport.registerFunction("Audio::setMicMute", jsonrpccxx::methodHandle(&IAudio::setMicMute, mImpl), {{ "phoneHandle" }, { "enabled" }});
	mTransport.registerFunction("Audio::setSpeakerMute", jsonrpccxx::methodHandle(&IAudio::setSpeakerMute, mImpl), {{ "phoneHandle" }, { "enabled" }});
	mTransport.registerFunction("Audio::setMicVolume", jsonrpccxx::methodHandle(&IAudio::setMicVolume, mImpl), {{ "phoneHandle" }, { "level" }});
	mTransport.registerFunction("Audio::setSpeakerVolume", jsonrpccxx::methodHandle(&IAudio::setSpeakerVolume, mImpl), {{ "phoneHandle" }, { "level" }});
	mTransport.registerFunction("Audio::queryDeviceVolume", jsonrpccxx::methodHandle(&IAudio::queryDeviceVolume, mImpl), {{ "phoneHandle" }});
	mTransport.registerFunction("Audio::startMonitoringCaptureDeviceLevels", jsonrpccxx::methodHandle(&IAudio::startMonitoringCaptureDeviceLevels, mImpl), {{ "phoneHandle" }, { "deviceId" }});
	mTransport.registerFunction("Audio::stopMonitoringRenderDeviceLevels", jsonrpccxx::methodHandle(&IAudio::stopMonitoringRenderDeviceLevels, mImpl), {{ "phoneHandle" }});
    }

private:
    ::CPCAPI2::IJsonRpcServerModuleProcessing& mTransport;
    IAudio& mImpl;
}; // class AudioServer
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOSERVER_H
