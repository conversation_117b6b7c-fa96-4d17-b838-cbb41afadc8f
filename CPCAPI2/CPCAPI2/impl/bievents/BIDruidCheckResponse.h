#pragma once
#ifndef __CPCAPI2_BIEVENTS_DRUID_CHECK_RESPONSE_H__
#define __CPCAPI2_BIEVENTS_DRUID_CHECK_RESPONSE_H__

#include <string>
#include <util/strettorpc/RPCResponse.h>

namespace CPCAPI2
{
   namespace BIEvents
   {
      typedef enum BIDruidResponse
      {
         BIDruidResponse_New,     // new DRUID was generated (in response to an empty check request)
         BIDruidResponse_Reissued,// no DRUID was provided but the HW description is so similar to re-issue an existing DRUID
         BIDruidResponse_Valid,   // DRUID provided matches the one on the server with the corresponding hardware info
         BIDruidResponse_Updated, // valid but some of the provided hardware info has changed, still OK
         BIDruidResponse_Conflict,// DRUID provided conflicts with other hardware descriptions, likely more than one instance use the same DRUID, a new one was issued
         BIDruidResponse_Fraud,   // possible fraud was detected, a new DRUID was generated
         BIDruidResponse_Invalid  // provided DRUID was not known to the system, a new one was issued 
      } BIDruidResponse_t;
    
      class BIDruidCheckResponse : public CPCAPI2::RPCResponse
      {
      public:
         /**
          * Returns the response status (convenience wrapper and enum conversion)
          */
         BIDruidResponse_t getResponseStatus( void );

         /**
          * Returns the druid provided by the server in the response.
          */
         std::string getResponseDruid( void );
      };
   }
}

#endif // __CPCAPI2_BIEVENTS_DRUID_CHECK_RESPONSE_H__