#pragma once

#if !defined(CPCAPI2_CALL_QUALITY_REPORTER_IMPL_H)
#define CPCAPI2_CALL_QUALITY_REPORTER_IMPL_H

#include "cpcapi2defs.h"
#include "callquality/CallQualityReportManager.h"
#include "call/SipConversationManager.h"
#include "call/SipConversationHandlerInternal.h"
#include "event/SipPublicationManager.h"
#include "event/SipEventPublicationHandler.h"
#include "phone/EventSyncHandler.h"
#include <resip/recon/media/Mixer.hxx>
#include <RtpStreamCQRCallback.h>

#include <rutil/Data.hxx>
#include <map>
#include <memory>

namespace webrtc_recon
{
   class RtpStreamImpl;
}
namespace CPCAPI2
{
class PhoneInterface;
namespace CallQuality
{
class CallQualityReportManagerInterface;

class CallQualityReporterImpl : public std::enable_shared_from_this<CallQualityReporterImpl>,
                                public CPCAPI2::EventSyncHandler<CPCAPI2::SipEvent::SipEventPublicationHandler>,
                                public recon::MixerObserver,
                                public webrtc_recon::RtpStreamIntervalCQRCallback
{
public:
   CallQualityReporterImpl(CallQualityReportManagerInterface* ccif, CallQualityReporterHandle h);
   virtual ~CallQualityReporterImpl();

   void configureCallQualityReporter(const CallQualityReporterConfig& config);
   void startCallQualityReporter();
   void stopCallQualityReporter();

   // SipEventPublicationHandler
   virtual int onPublicationSuccess(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationSuccessEvent& args) OVERRIDE;
   virtual int onPublicationFailure(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationFailureEvent& args) OVERRIDE;
   virtual int onPublicationRemove(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationRemoveEvent & args) OVERRIDE;
   virtual int onError(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationErrorEvent& args) OVERRIDE;
   virtual int onReady(CPCAPI2::SipAccount::SipAccountHandle sipAccount, const CPCAPI2::SipEvent::PublicationManagerReadyEvent& args) OVERRIDE;

   // MixerObserver
   virtual void onRtpStreamAdded(const std::shared_ptr<recon::RtpStream>& ms) OVERRIDE;
   virtual void onRtpStreamRemoved(const std::shared_ptr<recon::RtpStream>& ms) OVERRIDE;

   // RtpStreamIntervalCQRCallback
   virtual void onIntervalReport(const std::shared_ptr<recon::RtpStream>& rtpStream, const resip::Data& cqr, int appState) OVERRIDE;

private:

private:
   CallQualityReportManagerInterface* mInterface;
   CallQualityReporterHandle mHandle;
   CallQualityReporterConfig mConfig;

   CPCAPI2::SipEvent::SipEventPublicationManager* mSipEventPublishMgr;
   CPCAPI2::SipEvent::SipEventPublicationHandle mPubHandle;
   std::vector<std::weak_ptr<webrtc_recon::RtpStreamImpl> > mRtpStreams;
   CPCAPI2::SipConversation::SipConversationManager* mSipConvMgr;
};

}

}

#endif // CPCAPI2_CALL_QUALITY_REPORTER_IMPL_H

