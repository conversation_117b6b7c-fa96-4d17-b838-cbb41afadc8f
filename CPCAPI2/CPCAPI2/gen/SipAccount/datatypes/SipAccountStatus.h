#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTSTATUS_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTSTATUS_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  enum class SipAccountStatus
  {
    Registered,
    Unregistered,
    Registering,
    Unregistering,
    WaitingToRegister,
    Refreshing
  };

  inline std::string to_string(SipAccountStatus value)
  {
    switch (value)
    {
      case SipAccountStatus::Registered:
        return "Registered";
      case SipAccountStatus::Unregistered:
        return "Unregistered";
      case SipAccountStatus::Registering:
        return "Registering";
      case SipAccountStatus::Unregistering:
        return "Unregistering";
      case SipAccountStatus::WaitingToRegister:
        return "WaitingToRegister";
      case SipAccountStatus::Refreshing:
        return "Refreshing";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, SipAccountStatus value)
  {
    switch (value)
    {
      case SipAccountStatus::Registered:
        os << "Registered";
        break;
      case SipAccountStatus::Unregistered:
        os << "Unregistered";
        break;
      case SipAccountStatus::Registering:
        os << "Registering";
        break;
      case SipAccountStatus::Unregistering:
        os << "Unregistering";
        break;
      case SipAccountStatus::WaitingToRegister:
        os << "WaitingToRegister";
        break;
      case SipAccountStatus::Refreshing:
        os << "Refreshing";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const SipAccountStatus& e)
  {
    switch (e)
    {
      case SipAccountStatus::Registered:
        j = "Registered";
        break;
      case SipAccountStatus::Unregistered:
        j = "Unregistered";
        break;
      case SipAccountStatus::Registering:
        j = "Registering";
        break;
      case SipAccountStatus::Unregistering:
        j = "Unregistering";
        break;
      case SipAccountStatus::WaitingToRegister:
        j = "WaitingToRegister";
        break;
      case SipAccountStatus::Refreshing:
        j = "Refreshing";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, SipAccountStatus& e)
  {
      if (0 == j.get<std::string>().compare("Registered"))
    {
      e = SipAccountStatus::Registered;
    }
    else if (0 == j.get<std::string>().compare("Unregistered"))
    {
      e = SipAccountStatus::Unregistered;
    }
    else if (0 == j.get<std::string>().compare("Registering"))
    {
      e = SipAccountStatus::Registering;
    }
    else if (0 == j.get<std::string>().compare("Unregistering"))
    {
      e = SipAccountStatus::Unregistering;
    }
    else if (0 == j.get<std::string>().compare("WaitingToRegister"))
    {
      e = SipAccountStatus::WaitingToRegister;
    }
    else if (0 == j.get<std::string>().compare("Refreshing"))
    {
      e = SipAccountStatus::Refreshing;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum SipAccountStatus"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTSTATUS_H
