#include "brand_branded.h"

#if (CPCAPI2_BRAND_AUTH_SERVER_MODULE == 1)

#include "AuthServerStrettoProvAccess.h"
#include "strettoprovisioning/StrettoProvisioningInterface.h"

#include "../util/cpc_logger.h"

using namespace CPCAPI2::StrettoProvisioning;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

namespace CPCAPI2
{
namespace AuthServer
{
StrettoProvAccess::StrettoProvAccess(CPCAPI2::Phone* phone)
   : mProvHdl(0)
{
   mProvMgr = CPCAPI2::StrettoProvisioning::StrettoProvisioning::getInterface(phone);
}

StrettoProvAccess::~StrettoProvAccess()
{
}

int StrettoProvAccess::initialize(const cpc::string& dbfile)
{
   mProvHdl = mProvMgr->create();
   dynamic_cast<StrettoProvisioningInterface*>(mProvMgr)->addSdkObserver(mProvHdl, this);
   //mProvMgr->addSdkObserver(...);
   return 0;
}

int StrettoProvAccess::addUser(const cpc::string& username, const cpc::string& password_hash)
{
   return -1;
}

int StrettoProvAccess::updatePassword(const cpc::string& username, const cpc::string& new_password_hash)
{
   return -1;
}

int StrettoProvAccess::queryUser(const cpc::string& username, const cpc::string& password_hash, bool& userExists, cpc::string& storedPassword)
{
   userExists = false;

   StrettoProvisioningSettings settings;
   //settings.provisioningURL = "https://ccs3.cloudprovisioning.com/login";
   //settings.authInfo.spid = "group1.1";
   settings.authInfo.userName = username;
   settings.authInfo.password = password_hash;
   settings.buildInfo.versionString = "3.1.4.15926";
   settings.logJSONPayload = true;                      // not used yet

   mProvMgr->configureSettings(mProvHdl, settings);
   mProvMgr->applySettings(mProvHdl); // prob. not needed

   // Fetch the config
   mProvMgr->request(mProvHdl);

   auto fv = mProvResponse.get_future();
   if (fv.wait_for(std::chrono::seconds(10)) == std::future_status::ready)
   {
      ProvServerResponse resp = fv.get();
      if (resp.success)
      {
         userExists = true;
         storedPassword = password_hash;
      }
   }
   mProvResponse = std::promise<ProvServerResponse>();

   return 0;
}

int StrettoProvAccess::flushUsers()
{
   return 0;
}

int StrettoProvAccess::onProvisioningSuccess(const CPCAPI2::StrettoProvisioning::StrettoProvisioningHandle& handle, const CPCAPI2::StrettoProvisioning::StrettoProvisioningEvent& evt)
{
   ProvServerResponse resp;
   resp.success = true;
   resp.successEvt = evt;
   mProvResponse.set_value(resp);
   return 0;
}

int StrettoProvAccess::onProvisioningError(const CPCAPI2::StrettoProvisioning::StrettoProvisioningHandle& handle, const CPCAPI2::StrettoProvisioning::StrettoProvisioningErrorEvent& evt)
{
   return 0;
}

int StrettoProvAccess::onError(const CPCAPI2::StrettoProvisioning::StrettoProvisioningHandle& handle, const CPCAPI2::StrettoProvisioning::ErrorEvent& evt)
{
   return 0;
}

}
}
#endif
