#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_PHONE_PHONEGETINSTANCEID_H
#define JSONRPC_CPCAPI2_PHONE_PHONEGETINSTANCEID_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  class PhoneGetInstanceId
  {
  public:
    int32_t handle;

    PhoneGetInstanceId() = default;

    PhoneGetInstanceId(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const PhoneGetInstanceId& o)
    {
      j = nlohmann::json::object();
       j["handle"] = o.handle;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, PhoneGetInstanceId& o)
    {
     o.handle = j.at("handle");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_PHONEGETINSTANCEID_H
