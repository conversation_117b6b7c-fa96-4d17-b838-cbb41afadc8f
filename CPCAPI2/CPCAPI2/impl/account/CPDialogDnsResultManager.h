#pragma once

#ifndef __CPCAPI2_CP_DIALOG_DNS_RESULT_MANGER_H__
#define __CPCAPI2_CP_DIALOG_DNS_RESULT_MANGER_H__

#include "cpcapi2defs.h"

#include "SipAccountAwareFeature.h"
#include "SipAccountHandlerInternal.h"
#include "SipAccountImpl.h"

#include <resip/dum/DialogDnsResultHandler.hxx>
#include <resip/dum/OutOfDialogHandler.hxx>
#include <resip/stack/DnsResult.hxx>

#include <map>
#include <list>
#include <unordered_set>
#include <future>

/**
 * The DNS Result Manager triggers a DNS status check 10 seconds prior to the next occurence of the registration refresh,
 * or immediatly if the registration refresh is scheduled to occur in less than 10 seconds. The DNS reset is only
 * triggered if all required conditions are met. A registration refresh is triggered right after the DNS has been reset
 * and the onDnsReset callback is triggered for the SIP account internal handlers, after the DNS reset is initiated.
 *
 * The state of the DNS result manager is reset when the following events are received: account settings configured,
 * network change, destroyed dialog manager and registered account status.
 *
 * The following conditions are required before the DNS reset is triggered:
 *   - dns reset functionality is enabled
 *   - account is registered
 *   - existing registration target does not match the preferred target
 *   - preferred target is active, i.e. it has responded to the SIP OPTIONS ping request within 2 seconds
 *   - history of triggered DNS resets is less than the threshold
 *
 *
 *                                                                O
 *                                                                |
 *                                                    +-----------+-----------+
 *           +<---------------------------------------+                       +--------------------------------------->+
 *           |                                        |                       | onAccountConfigured (reset disabled)   |
 * +------------------------------------------------->+        STARTED        | onNetworkChange (reset disabled)       |
 * |         |     onAccountConfigured (reset enabled)|                       | ** account status ignored **           |
 * |         |     onNetworkChange (reset enabled but |                       |                                        |
 * |         |     dum not initialized)               +-----------+-----------+                                        |
 * |         |                                                    |                                                    |
 * |         |                                                    |    addHandlers                                     |
 * |         |                                                    | (dum initialized)                                  |
 * |         |                                                    v                                                    |
 * |         |                                        +-----------+-----------+                                        |
 * |         +<---------------------------------------+                       +--------------------------------------->+
 * |         |                                        |                       |                                        |
 * +<-------------------------------------------------+      INITIALIZED      +<-------------------------------------------------+
 * |         |                                        |                       |           onNetworkChange              |         |
 * |         |                                        |                       | (reset enabled and dum initialized)    |         |
 * |         |                                        +-----------+-----------+                                        |         |
 * |         |                                                    |                                                    |         |
 * |         |                                                    |  onDnsResult                                       |         |
 * |         |                                                    | (primary set)                                      |         |
 * |         |                                                    v                   onAccountStatus                  |         |
 * |         |              onError or internal error +-----------+-----------+ (unregistering/unregistered)           |         |
 * |         +<---------------------------------------+                       +--------------------------------------->+         |
 * |         |                                        |                       |                                        |         |
 * +<-------------------------------------------------+      PRIMARY_SET      +------------------------------------------------->+
 * |         |                                        |                       |                                        |         |
 * |         |          +-----------------------------+                       |                                        |         |
 * |         |          |      onAccountStatus        +-----------+-----------+                                        |         |
 * |         |          |(registered with secondary)              |                                                    |         |
 * |         |          |                                         |       onAccountStatus                              |         |
 * |         |          |                                         | (registered with primary)                          |         |
 * |         |          |                                         v                                                    |         |
 * |         |          |                             +-----------+-----------+                                        |         |
 * |         +<---------------------------------------+                       +--------------------------------------->+         |
 * |         |          |                             |                       |                                        |         |
 * +<-------------------------------------------------+  REGISTERED_PRIMARY   +------------------------------------------------->+
 * |         |          |                             |                       |                                        |         |
 * |         |          |                             |                       |                                        |         |
 * |         |          |                             +-----------------------+                                        |         |
 * |         |          |                                                                                              |         |
 * |         |          |                                                                                              |         |
 * +<-----------------------------------------------------+                                                            |         |
 * |         |          |                                 |                                                            |         |
 * |         |          |   onError or internal error +---+-------------------+                                        |         |
 * |         +<---------------------------------------+                       +--------------------------------------->+         |
 * |         |          |                             |                       |                                        |         |
 * |         |          +---------------------------->+ REGISTERED_SECONDARY  +------------------------------------------------->+
 * |         |                                        |                       |                                        |         |
 * |         |          +---------------------------->+                       |                                        |         |
 * |         |          |                             +-----+-----------+-----+                                        |         |
 * |         |          |                                   |           ^                                              |         |
 * |         |          |                          onTimer  |           | onSuccess or onFailure (triggers dns reset,  |         |
 * |         |          |                         (refresh) |           | or changes to secondary if call in progress) |         |
 * |         |          |                                   v           |                                              |         |
 * |         |          |   onError or internal error +-----+-----------+-----+                                        |         |
 * |         +<---------------------------------------+                       +--------------------------------------->+         |
 * |         |          |                             |                       |                                        |         |
 * |         |          +-----------------------------+        PROBING        +------------------------------------------------->+
 * |         |           onFailure (internal response)|                       | onNetworkChange                        |         |
 * |         |           onTimer (no response)        |                       | (reset enabled and dum initialized)    |         |
 * |         |                                        +---+-------------------+ onAccountStatus and dum initialized)   |         |
 * |         |                                            |                     (refreshing state after dns-reset)     |         |
 * +<-----------------------------------------------------+                                                            |         |
 * |         |                                                                                                         |         |
 * |         |                                                                  onAccountConfigured (reset disabled)   |         |
 * |         |                                        +-----------------------+ onNetworkChange (reset disabled)       |         |
 * |         +<---------------------------------------+                       | onAccountStatus                        |         |
 * |         |                                        |                       | (unregistering/unregistered)           |         |
 * +<-------------------------------------------------+       DISABLED        +<---------------------------------------+         |
 * |         |                                        |                       |                                        |         |
 * |         |                                        |                       +------------------------------------------------->+
 * |         |                                        +-----------------------+ onNetworkChange                        |         |
 * |         |                                                                  (reset enabled and dum initialized)    |         |
 * |         |                                                                  onAccountStatus (reset enabled)        |         |
 * |         |                                                                  (refreshing/registering/registered)    |         |
 * |         |                                                                                                         |         |
 * |         |                                        +-----------------------+                                        |         |
 * +<-------------------------------------------------+                       +------------------------------------------------->+
 *           |                                        |                       |                                        |
 *           +--------------------------------------->+        INVALID        +--------------------------------------->+
 *                                            onError |                       |
 *                                                    |                       |
 *                                                    +-----------------------+
 *
 *
*/

namespace CPCAPI2
{

namespace SipAccount
{

enum DnsResetStateType
{
   Dns_Reset_Startup_State,
   Dns_Reset_Initialized_State,
   Dns_Reset_Primary_Set_State,
   Dns_Reset_Registered_Primary_State,
   Dns_Reset_Registered_Secondary_State,
   Dns_Reset_Probing_State,
   Dns_Reset_Disabled_State,
   Dns_Reset_Invalid_State
};

class DnsResetStatusChangedEvent
{

public:

   DnsResetStatusChangedEvent() : status(Dns_Reset_Invalid_State) {}
   DnsResetStatusChangedEvent(DnsResetStateType status_) : status(status_) {}
   DnsResetStateType status;

};

class DialogDnsResultSyncHandler {};

class DialogDnsResultHandler
{

public:

      // Fired whenever there is a state change in the CPDialogDnsResultManager
      virtual int onDialogDnsResetStatus(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::DnsResetStatusChangedEvent& args) = 0;

};

class CPDialogDnsResultManager : public CPCAPI2::SipAccount::SipAccountAwareFeature,
                                 public CPCAPI2::EventSyncHandler<CPCAPI2::SipAccount::SipAccountHandlerInternal>,
                                 public resip::DialogDnsResultHandler
{

public:

   CPDialogDnsResultManager(CPCAPI2::Phone* cpcPhone, CPCAPI2::SipAccount::SipAccountImpl* account);
   virtual ~CPDialogDnsResultManager();

   void setHandler(CPCAPI2::SipAccount::DialogDnsResultHandler* handler);
   void postCallback(CPCAPI2::SipAccount::DnsResetStatusChangedEvent& args);

   // SipAccountHandlerInternal
   virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args) OVERRIDE;
   virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args) OVERRIDE;
   virtual int onAccountConfigured(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountConfiguredEvent& args) OVERRIDE;
   virtual int onAccountEnabled(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountEnabledEvent& args) OVERRIDE;
   virtual int onNetworkChange(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountNetworkChangeEvent& args) OVERRIDE;

   // DialogDnsResultHandler
   virtual void onDnsResult(const resip::DnsResultDataMessage& result) OVERRIDE;

   // SipAccountAwareFeature
   virtual int adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile) OVERRIDE;
   virtual int addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig) OVERRIDE;
   virtual int onDumBeingDestroyed() OVERRIDE;
   virtual void release() OVERRIDE;

   class DnsResetState;
   class DnsResetHistory;

   resip::MultiReactor& getReactor();
   resip::SharedPtr<resip::DialogUsageManager>& getDum();
   void changeState(DnsResetStateType type);
   DnsResetStateType getStateType();
   DnsResetState* getState();
   CPCAPI2::SipAccount::SipAccountImpl* getAccount();
   CPCAPI2::SipAccount::SipAccountHandle getHandle();
   resip::Tuple& getPreferredTarget();
   resip::Tuple& getCurrentTarget(); // Registration target in previous registration
   bool isDnsResetEnabled();
   bool isAuthResetEnabled();
   DnsResetHistory& getDnsResetHistory();
   int getHistoricalChangeCount();

   class DnsResetStateFactory
   {

   public:

      DnsResetStateFactory(CPDialogDnsResultManager* manager);
      virtual~ DnsResetStateFactory();

      DnsResetState* getState(DnsResetStateType type);

   private:

      DnsResetStateFactory();

      void create();
      DnsResetState* create(DnsResetStateType type);

      typedef std::map<DnsResetStateType, DnsResetState*> DnsResetStates;
      DnsResetStates mStates;
      CPDialogDnsResultManager* mManager;

   };

   class DnsResetState : public CPCAPI2::SipAccount::SipAccountAwareFeature,
                         public CPCAPI2::EventSyncHandler<CPCAPI2::SipAccount::SipAccountHandlerInternal>,
                         public resip::DialogDnsResultHandler
   {

   public:

      DnsResetState(CPDialogDnsResultManager* manager, DnsResetStateType state);
      virtual~ DnsResetState();
      virtual void onEntry() = 0;
      virtual void onExit() = 0;

      resip::Tuple& getPreferredTarget();
      resip::Tuple& getCurrentTarget(); // Registration target in previous registration
      bool isDnsResetEnabled();
      bool isAuthResetEnabled();
      void changeState(DnsResetStateType state);
      DnsResetStateType getType();
      std::string getName();
      static std::string getName(DnsResetStateType type);

      // SipAccountHandlerInternal
      virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args) OVERRIDE;
      virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args) OVERRIDE;
      virtual int onAccountConfigured(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountConfiguredEvent& args) OVERRIDE;
      virtual int onAccountEnabled(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountEnabledEvent& args) OVERRIDE;
      virtual int onNetworkChange(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountNetworkChangeEvent& args) OVERRIDE;

      // DialogDnsResultHandler
      virtual void onDnsResult(const resip::DnsResultDataMessage& result) OVERRIDE;

      // SipAccountAwareFeature
      virtual int adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile) OVERRIDE;
      virtual int addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig) OVERRIDE;
      virtual int onRegistrationSuccess(const SipRegistrationSuccessEvent& args) OVERRIDE;
      virtual int onDumBeingDestroyed() OVERRIDE;
      virtual void release() OVERRIDE;

   protected:

      DnsResetState();
      CPDialogDnsResultManager* mManager;
      DnsResetStateType mState;
      CPCAPI2::SipAccount::SipAccountImpl* mAccount;
      CPCAPI2::SipAccount::SipAccountHandle mHandle;

   };

   class DnsResetStartupState : public DnsResetState
   {

   public:

      DnsResetStartupState(CPDialogDnsResultManager* manager);
      virtual~ DnsResetStartupState();
      virtual void onEntry() OVERRIDE;
      virtual void onExit() OVERRIDE;

      // SipAccountHandlerInternal
      virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args) OVERRIDE;
      virtual int onAccountConfigured(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountConfiguredEvent& args) OVERRIDE;
      virtual int onNetworkChange(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountNetworkChangeEvent& args) OVERRIDE;

      // SipAccountAwareFeature
      virtual int addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig) OVERRIDE;

   private:

      DnsResetStartupState();

   };

   class DnsResetInitializedState : public DnsResetState
   {

   public:

      DnsResetInitializedState(CPDialogDnsResultManager* manager);
      virtual~ DnsResetInitializedState();
      virtual void onEntry() OVERRIDE;
      virtual void onExit() OVERRIDE;

      // DialogDnsResultHandler
      virtual void onDnsResult(const resip::DnsResultDataMessage& result) OVERRIDE;

   private:

      DnsResetInitializedState();

   };

   class DnsResetPrimarySetState : public DnsResetState
   {

   public:

      DnsResetPrimarySetState(CPDialogDnsResultManager* manager);
      virtual~ DnsResetPrimarySetState();
      virtual void onEntry() OVERRIDE;
      virtual void onExit() OVERRIDE;

      // SipAccountHandlerInternal
      virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args) OVERRIDE;

   private:

      DnsResetPrimarySetState();

   };

   class DnsResetRegisteredPrimaryState : public DnsResetState
   {

   public:

      DnsResetRegisteredPrimaryState(CPDialogDnsResultManager* manager);
      virtual~ DnsResetRegisteredPrimaryState();
      virtual void onEntry() OVERRIDE;
      virtual void onExit() OVERRIDE;

   private:

      DnsResetRegisteredPrimaryState();

   };

   class DnsResetRegisteredSecondaryState : public DnsResetState,
                                            public resip::DeadlineTimerHandler
   {

   public:

      DnsResetRegisteredSecondaryState(CPDialogDnsResultManager* manager);
      virtual~ DnsResetRegisteredSecondaryState();
      virtual void onEntry() OVERRIDE;
      virtual void onExit() OVERRIDE;

      // resip::DeadlineTimerHandler
      virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   private:

      bool shouldResetDns();
      void resetRefreshTimer();

      DnsResetRegisteredSecondaryState();
      resip::DeadlineTimer<resip::MultiReactor> mRefreshTimer;

   };

   class DnsResetProbingState : public DnsResetState,
                                public resip::DeadlineTimerHandler,
                                public resip::OutOfDialogHandler
   {

   public:

      DnsResetProbingState(CPDialogDnsResultManager* manager);
      virtual~ DnsResetProbingState();
      virtual void onEntry() OVERRIDE;
      virtual void onExit() OVERRIDE;

      // resip::DeadlineTimerHandler
      virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

      // SipAccountHandlerInternal
      virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args) OVERRIDE;

      // OutOfDialogHandler
      virtual bool onSuccess(resip::ClientOutOfDialogReqHandle h, const resip::SipMessage& successResponse) OVERRIDE;
      virtual bool onFailure(resip::ClientOutOfDialogReqHandle h, const resip::SipMessage& errorResponse) OVERRIDE;
      virtual bool onReceivedRequest(resip::ServerOutOfDialogReqHandle, const resip::SipMessage& request) OVERRIDE;

   private:

      bool sendPing();
      void resetDns();

      DnsResetProbingState();
      resip::SharedPtr<resip::SipMessage> mOptionsPing;
      resip::DeadlineTimer<resip::MultiReactor> mProbeTimer;

   };

   class DnsResetDisabledState : public DnsResetState
   {

   public:

      DnsResetDisabledState(CPDialogDnsResultManager* manager);
      virtual~ DnsResetDisabledState();
      virtual void onEntry() OVERRIDE;
      virtual void onExit() OVERRIDE;

      // SipAccountHandlerInternal
      virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args) OVERRIDE;
      virtual int onAccountConfigured(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountConfiguredEvent& args) OVERRIDE;
      virtual int onNetworkChange(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountNetworkChangeEvent& args) OVERRIDE;

   private:

      DnsResetDisabledState();

   };

   class DnsResetInvalidState : public DnsResetState
   {

   public:

      DnsResetInvalidState(CPDialogDnsResultManager* manager);
      virtual~ DnsResetInvalidState();
      virtual void onEntry() OVERRIDE;
      virtual void onExit() OVERRIDE;

      // SipAccountHandlerInternal
      virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args) OVERRIDE;

   private:

      DnsResetInvalidState();

   };

   class DnsResetHistory
   {

   public:

      DnsResetHistory(time_t depth) : mDepth(depth) {}
      DnsResetHistory() {}

      void add();
      unsigned int numChanges() const;
      void reset() { mHistory.clear(); }

   private:

      std::list<time_t> mHistory;
      time_t mDepth;

   };

private:

   void reset();
   void resetConfig(const CPCAPI2::SipAccount::SipAccountSettings& settings, bool fullReset = true);
   // bool dnsPreCheck();

   resip::MultiReactor& mReactor;
   CPCAPI2::SipAccount::SipAccountImpl* mAccount;
   CPCAPI2::SipAccount::SipAccountHandle mHandle;
   resip::SharedPtr<resip::DialogUsageManager> mDum;
   CPCAPI2::SipAccount::DialogDnsResultHandler* mDnsHandler;

   bool mDnsResetEnabled;
   bool mAuthResetEnabled;
   bool mChangingState;

   resip::Tuple mPreferredTarget;
   resip::Tuple mCurrentTarget; // Registration target in previous registration

   DnsResetHistory mDnsResetHistory;

   std::unique_ptr<DnsResetStateFactory> mStateFactory;
   DnsResetState* mState;

};

}

}

#endif // __CPCAPI2_CP_DIALOG_DNS_RESULT_MANGER_H__
