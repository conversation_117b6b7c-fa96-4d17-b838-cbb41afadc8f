
#include "brand_branded.h"
#if (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)

#include "SipChatManagerImpl.h"
#include "SipChatAppDialogFactoryDelegate.h"
#include "../iscomposing/IsComposingHelper.h"

#include <resip/dum/ServerInviteSession.hxx>
#include <resip/dum/ClientInviteSession.hxx>

#include <utils/msrp_string.h>

namespace CPCAPI2
{
namespace SipChat
{

const cpc::string SipChatManagerImpl::CPM_FEATURE_TAG = "3gpp-service.ims.icsi.oma.cpm.session";

SipChatManagerImpl::SipChatManagerImpl(SipChatManagerInterface* iff, PhoneInterface* cpcPhone, CPCAPI2::SipAccount::SipAccountImpl& account)
   : mInterface(iff),
     SipMsrpMessagingManager(account),
     IsComposingManager(cpcPhone)
{
   account.registerAccountAwareFeature(this);
}

SipChatManagerImpl::~SipChatManagerImpl()
{
   account.unregisterAccountAwareFeature(this);
}

void SipChatManagerImpl::releaseImpl()
{
   account.getPhone()->getSdkModuleThread().unregisterEventHandler(this);
   account.getPhone()->getSdkModuleThread().safeDelete(this); // will delete this
}

int SipChatManagerImpl::registerSdkDialogSetFactory(CPCAPI2::SipAccount::AppDialogSetFactory& factory)
{   
   std::shared_ptr<SipChatAppDialogFactoryDelegate> appDialogFactoryDelegate(new SipChatAppDialogFactoryDelegate(this)); // Shared pointer
   factory.addDelegate(appDialogFactoryDelegate);

   return kSuccess;
}

void SipChatManagerImpl::onNewSession(ClientInviteSessionHandle cish, InviteSession::OfferAnswerType oat, const SipMessage& msg)
{
   // Get the chat information associated with the SIP session received
   SipChatInfo* chatInfo = dynamic_cast<SipChatInfo*>(cish->getAppDialogSet().get());
   assert(chatInfo);

   // Process the new session
   SipMsrpMessagingManager::onNewSession(cish, oat, msg);

   // Set the initial receiver and composing state
   IsComposingManager::initialize(chatInfo);
}

void SipChatManagerImpl::onOffer(InviteSessionHandle ish, const SipMessage& msg, const SdpContents& contents)
{
   // Get the chat information associated with the SIP session received
   SipChatInfo* chatInfo = dynamic_cast<SipChatInfo*>(ish->getAppDialogSet().get());
   assert(chatInfo);

   // Process the offer
   SipMsrpMessagingManager::onOffer(ish, msg, contents);

   // Set the initial receiver and composing state
   IsComposingManager::initialize(chatInfo);

   // Fire the new chat session event
   NewChatEvent event;
   event.chatType = ChatType_Incoming;
   fireNewChat(chatInfo->handle, event);   
}

void SipChatManagerImpl::onAnswer(InviteSessionHandle ish, const SipMessage& msg, const SdpContents& contents)
{
   // Get the chat information associated with the SIP session received
   SipChatInfo* chatInfo = dynamic_cast<SipChatInfo*>(ish->getAppDialogSet().get());
   assert(chatInfo);

   // Process the answer
   SipMsrpMessagingManager::onAnswer(ish, msg, contents);

   // Fire the new chat session event
   NewChatEvent event;
   event.chatType = ChatType_Outgoing;
   fireNewChat(chatInfo->handle, event);
}

void SipChatManagerImpl::onTerminated(InviteSessionHandle ish, InviteSessionHandler::TerminatedReason reason, const SipMessage* related)
{
   // Get the chat information associated with the SIP session received
   SipChatInfo* chatInfo = dynamic_cast<SipChatInfo*>(ish->getAppDialogSet().get());
   assert(chatInfo);

   // Fire the chat session terminated event
   ChatEndedEvent event;
   event.chat = chatInfo->handle;
   event.endReason = SipChatHelper::getChatEndReason(reason);
   fireChatEnded(chatInfo->handle, event);

   // Process the termination
   SipMsrpMessagingManager::onTerminated(ish, reason, related);
}

void SipChatManagerImpl::fireNewChat(SipChatHandle chat, const NewChatEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipChatHandler, onNewChat), chat, event);
}

void SipChatManagerImpl::fireNewMessage(SipChatHandle chat, const NewMessageEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipChatHandler, onNewMessage), chat, event);
}

void SipChatManagerImpl::fireSendMessageSuccess(SipChatHandle chat, const SendMessageSuccessEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipChatHandler, onSendMessageSuccess), chat, event);
}

void SipChatManagerImpl::fireChatEnded(SipChatHandle chat, const ChatEndedEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipChatHandler, onChatEnded), chat, event);
}

void SipChatManagerImpl::fireError(SipChatHandle chat, const ErrorEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipChatHandler, onError), chat, event);
}

void SipChatManagerImpl::fireError(SipChatHandle chat, const cpc::string& msg)
{
   ErrorEvent event;
   event.errorText = msg;
   fireError(chat, event);
}

void SipChatManagerImpl::fireMessageDelivered(SipChatHandle chat, const MessageDeliveredEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipChatHandler, onMessageDelivered), chat, event);
}

void SipChatManagerImpl::fireMessageDisplayed(SipChatHandle chat, const MessageDisplayedEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipChatHandler, onMessageDisplayed), chat, event);
}

void SipChatManagerImpl::fireIsComposingMessage(SipChatHandle chat, const IsComposingMessageEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipChatHandler, onIsComposingMessage), chat, event);
}

void SipChatManagerImpl::fireSendMessageFailure(SipChatHandle chat, const SendMessageFailureEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipChatHandler, onSendMessageFailure), chat, event);
}

void SipChatManagerImpl::fireNotifyMessageDeliveredSuccess(SipChatHandle chat, const NotifyMessageDeliveredSuccessEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipChatHandler, onNotifyMessageDeliveredSuccess), chat, event);
}

void SipChatManagerImpl::fireNotifyMessageDisplayedSuccess(SipChatHandle chat, const NotifyMessageDisplayedSuccessEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipChatHandler, onNotifyMessageDisplayedSuccess), chat, event);
}

void SipChatManagerImpl::fireSetIsComposingMessageSuccess(SipChatHandle chat, const SetIsComposingMessageSuccessEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipChatHandler, onSetIsComposingMessageSuccess), chat, event);
}

void SipChatManagerImpl::onMsrpMessageSendComplete(msrp_message_t* msrpMessage, int chat, const cpc::string& message, MessageType messageType)
{  
   // Get information on the message from the message store
   SipChatInfo* chatInfo = getChatInfo(chat);
   assert(chatInfo);
   SipChatMessageInfo* messageInfo = chatInfo->getMessageInfo(message);
   assert(messageInfo);

   // Check the type of message received
   if (messageType == MessageType_MessageDeliveredNotification)
   {
      // Message Delivered notification
      NotifyMessageDeliveredSuccessEvent event;
      event.notification = message;
      event.message = messageInfo->origHandle;
      fireNotifyMessageDeliveredSuccess(chat, event);
   }
   else if (messageType == MessageType_MessageDisplayedNotification)
   {
      // Message Displayed notification
      NotifyMessageDisplayedSuccessEvent event;
      event.notification = message;
      event.message = messageInfo->origHandle;
      fireNotifyMessageDisplayedSuccess(chat, event);
   }
   else if (messageType == MessageType_IsComposingNotification)
   {
      // Is Composing notification
      SetIsComposingMessageSuccessEvent event;
      event.state = messageInfo->isComposingState;
      fireSetIsComposingMessageSuccess(chat, event);
   }
   else if (messageType == MessageType_Message)
   {
      // Chat message

      // Notify the state machine of the message sent event
      IsComposingManager::setMessageSent(chatInfo);

      // Fire event
      SendMessageSuccessEvent event;
      event.message = message;
      fireSendMessageSuccess(chat, event);      
   }
   else
   {
      // Unsupported message type
      assert(false);
   }
}

void SipChatManagerImpl::onMsrpMessageRecvComplete(msrp_message_t* msrpMessage, int chat, uint8_t* data, uint64_t dataLength)
{ 
   // Make sure the MSRP message has some content
   if (dataLength == 0)
   {
      // Empty message. Don't try to parse it. Log info and bail out instead.
      CpmHelper::logWarning("Incoming MSRP message does not contain any bytes in its content section. Won't attempt to parse content.");
      return;
   }

   // Get the chat information
   SipChatInfo* chatInfo = getChatInfo(chat);
   assert(chatInfo);

   // Extract the content from the MSRP message (CPIM)
   resip::Data messageContentBytes = resip::Data(data, (long) dataLength);

   // Parse the CPIM message and check the type of message
   CpimMessage cpimMessage = CpimMessage::parse(messageContentBytes);
   if (SipChatHelper::isImdnNotification(cpimMessage))
   {
      // IMDN notification

      // Extract the content of the IMDN notification
      SipChatMessageHandle message;
      SipChatMessageHandle origMessage;
      cpc::string datetimeString;
      int notificationStatus;
      MessageType notificationType;
      CpmHelper::extractCpimMessage(cpimMessage, message, origMessage, datetimeString, notificationType, notificationStatus);

      // Notify of the incoming notification
      if (notificationType == MessageType_MessageDeliveredNotification)
      {
         // IMDN Delivery Notification
         MessageDeliveredEvent event;
         event.notification = message;
         event.message = origMessage;
         event.messageDeliveryStatus = (MessageDeliveryStatus) notificationStatus;
         event.datetime = SipChatHelper::extractDateTime(datetimeString);
         fireMessageDelivered(chat, event);
      }
      else if (notificationType == MessageType_MessageDisplayedNotification)
      {
         // IMDN Display Notification
         MessageDisplayedEvent event;
         event.notification = message;
         event.message = origMessage;
         event.messageDisplayStatus = (MessageDisplayStatus) notificationStatus;
         event.datetime = SipChatHelper::extractDateTime(datetimeString);
         fireMessageDisplayed(chat, event);
      }
      else
      {
         // Unsupported notification
         assert(false);
      }

      // Store information about the message received
      addMessageInfo(chat, message, notificationType, false, cpimMessage.getHeader(CpimHeader::DATETIME).getValue(), "", IsComposingMessageState_Unknown);
   }
   else if (SipChatHelper::isIsComposingMessageNotification(cpimMessage))
   {
      // IsComposing notification

      // Extract the notification from the CPIM message
      cpc::string messageId;
      IsComposingDocument isComposingDocument;
      SipChatHelper::extractCpimMessage(cpimMessage, messageId, isComposingDocument);

      // Process the notification
      IsComposingManager::processIsComposingMessageNotification(chatInfo, isComposingDocument);
   }
   else
   {
      // Chat Message

      // Extract the content of the CPIM message
      SipChatMessageHandle message;
      cpc::string fromUriStr;
      cpc::string toUriStr;
      tm datetime;
      cpc::string datetimeString;
      resip::Mime contentType;
      resip::Data messageContent;
      cpc::vector<DispositionNotificationType> dispositionNotifications;
      CpmHelper::extractCpimMessage(cpimMessage, message, fromUriStr, toUriStr, datetimeString, datetime, contentType, messageContent, dispositionNotifications);

      // Store information about the message received
      addMessageInfo(chat, message, MessageType_Message, false, datetimeString, "", IsComposingMessageState_Unknown);

      // Notify the state machine of the new message received
      IsComposingManager::setMessageReceived(chatInfo);

      // Notify of the incoming chat message
      NewMessageEvent event;
      event.message = message;
      event.messageContent = cpc::string(messageContent.c_str(), messageContent.size());
      event.from = fromUriStr;
      event.to = toUriStr;
      event.mimeType = SipChatHelper::contentTypeToMimeType(contentType);
      event.datetime = datetime;
      event.dispositionNotifications = dispositionNotifications;
      fireNewMessage(chat, event);
   }
}

void SipChatManagerImpl::startChat(SipChatInfo* chatInfo)
{
   SipMsrpMessagingManager::startSession(chatInfo, CPM_FEATURE_TAG);
}

void SipChatManagerImpl::endChat(SipChatInfo *chatInfo)
{
   assert(chatInfo);

   if (!chatInfo->clientInviteSessionHandle.isValid() && !chatInfo->serverInviteSessionHandle.isValid())
   {
      // No SIP session established

      // Fire the chat session terminated event
      ChatEndedEvent event;
      event.chat = chatInfo->handle;
      event.endReason = ChatEndReason_UserTerminatedLocally;
      fireChatEnded(chatInfo->handle, event);
   }

   // Terminate the session
   SipMsrpMessagingManager::terminateSession(chatInfo);
}
   
void SipChatManagerImpl::sendMessage(SipChatInfo *chatInfo, const SipChatMessageHandle& messageId, const tm& datetime, const resip::Mime& contentType, const resip::Data& messageContent, const cpc::vector<DispositionNotificationType>& dispositionNotifications)
{
   assert(chatInfo);

   // Make sure there is a message to send
   if (messageContent.empty())
   {
      SendMessageFailureEvent event;
      event.message = messageId;
      fireSendMessageFailure(chatInfo->handle, event);
      return;
   }

   // Create the content of the CPIM message
   resip::Uri fromUri = SipChatHelper::getUri(getAccount());  
   resip::Uri toUri = chatInfo->remotePartyAddress.uri();
   CpimMessage cpimMessage = CpmHelper::createCpimMessage(messageId, fromUri, toUri, datetime, contentType, messageContent, dispositionNotifications);   

   // Send the message
   SipMsrpMessagingManager::sendMessage(chatInfo, messageId, cpimMessage, MessageType_Message);

   // Store information about the outgoing message
   addMessageInfo(chatInfo->handle, messageId, MessageType_Message, true, cpimMessage.getHeader(CpimHeader::DATETIME).getValue(), "", IsComposingMessageState_Unknown);
}

void SipChatManagerImpl::acceptChat(SipChatInfo* chatInfo)
{
   SipMsrpMessagingManager::acceptSession(chatInfo);
}

void SipChatManagerImpl::rejectChat(SipChatInfo* chatInfo, unsigned int reasonCode)
{
   SipMsrpMessagingManager::rejectSession(chatInfo, reasonCode);
}

void SipChatManagerImpl::notifyMessageDelivered(SipChatInfo* chatInfo, const SipChatMessageHandle& message, const SipChatMessageHandle& origMessage, MessageDeliveryStatus messageDeliveryStatus)
{
   notifyMessageStatus(chatInfo, message, origMessage, MessageType_MessageDeliveredNotification, messageDeliveryStatus);
}

void SipChatManagerImpl::notifyMessageDisplayed(SipChatInfo* chatInfo, const SipChatMessageHandle& message, const SipChatMessageHandle& origMessage, MessageDisplayStatus messageDisplayStatus)
{
   notifyMessageStatus(chatInfo, message, origMessage, MessageType_MessageDisplayedNotification, messageDisplayStatus);
}

void SipChatManagerImpl::notifyMessageStatus(SipChatInfo* chatInfo, const SipChatMessageHandle& message, const SipChatMessageHandle& origMessage, MessageType messageType, int messageStatus)
{
   assert(chatInfo);

   // Get the From and To URIs
   resip::Uri fromUri = SipChatHelper::getUri(getAccount());  
   resip::Uri toUri = chatInfo->remotePartyAddress.uri();

   // Get the original message info
   SipChatMessageInfo* origMessageInfo = chatInfo->getMessageInfo(origMessage);
   assert(origMessageInfo);

   // Send a notification
   CpimMessage cpimMessage = CpmHelper::createCpimMessage(message, fromUri, toUri, origMessage, origMessageInfo->datetimeString, messageType, messageStatus);
   SipMsrpMessagingManager::sendMessage(chatInfo, message, cpimMessage, messageType);

   // Store information about the sent message
   addMessageInfo(chatInfo->handle, message, messageType, true, "", origMessage, IsComposingMessageState_Unknown);
}

SipChatMessageHandle SipChatManagerImpl::getNextMessageId(SipChatInfo* chatInfo)
{
   assert(chatInfo);

   cpc::string messageId;

   while(true)
   {
      // Generate a string based ID with 64 bit of randomness
      char* randomIdStr = msrp_string_new_random(64);
      messageId = randomIdStr;
      msrp_safe_free((void**) &randomIdStr);

      // Make sure the generated message ID is not already in use
      if (!chatInfo->hasMessageId(messageId))
      {
         break;
      }
   }

   return messageId;
}

void SipChatManagerImpl::addMessageInfo(SipChatHandle chat, const SipChatMessageHandle& message, MessageType messageType, bool outgoing, const cpc::string& datetimeString, const SipChatMessageHandle& origMessage, IsComposingMessageState isComposingState)
{
   SipChatInfo* chatInfo = getChatInfo(chat);
   assert(chatInfo);
   SipChatMessageInfo* messageInfo = new SipChatMessageInfo(); // Destroyed in SipChatInfo's destructor
   messageInfo->handle = message;
   messageInfo->type = messageType;
   messageInfo->outgoing = outgoing;
   messageInfo->datetimeString = datetimeString;
   messageInfo->origHandle = origMessage;
   messageInfo->isComposingState = isComposingState;
   chatInfo->addMessageInfo(messageInfo->handle, messageInfo);
}

void SipChatManagerImpl::setIsComposingMessage(IsComposingInfo* info, const resip::Mime& contentType, const tm& datetime, int refreshInterval, int idleInterval)
{
   IsComposingManager::setIsComposingMessage(info, contentType, datetime, refreshInterval, idleInterval);
}

void SipChatManagerImpl::sendIsComposingMessageNotification(IsComposingInfo* info, IsComposingMessageState state, const resip::Mime& contentType, int refreshInterval, const tm& lastActive)
{
   assert(info);

   // Get the chat session information
   SipChatInfo* chatInfo = dynamic_cast<SipChatInfo*>(info);

   // Get the From and To URIs
   resip::Uri fromUri = SipChatHelper::getUri(getAccount());  
   resip::Uri toUri = chatInfo->remotePartyAddress.uri();

   // Generate a new message ID
   SipChatMessageHandle message = getNextMessageId(chatInfo);

   // Create the notification
   IsComposingDocument isComposingDocument = IsComposingManager::createIsComposingMessageNotification(state, contentType, refreshInterval, lastActive);

   // Create the CPIM message which contains the notification
   CpimMessage cpimMessage = SipChatHelper::createCpimMessage(message, fromUri, toUri, isComposingDocument);

   // Send the CPIM message
   SipMsrpMessagingManager::sendMessage(chatInfo, message, cpimMessage, MessageType_IsComposingNotification);

   // Store information about the sent message
   addMessageInfo(chatInfo->handle, message, MessageType_IsComposingNotification, true, "", "", state);
}

void SipChatManagerImpl::onIsComposingMessage(IsComposingInfo* info, IsComposingMessageState state, const resip::Mime& contentType, const tm& lastActive)
{
   assert(info);

   // Get the chat session information
   SipChatInfo* chatInfo = dynamic_cast<SipChatInfo*>(info);

   // Notify of the chat message being composed
   IsComposingMessageEvent event;
   event.state = state;
   event.mimeType = SipChatHelper::contentTypeToMimeType(contentType);
   event.lastActive = lastActive;
   fireIsComposingMessage(chatInfo->handle, event);
}

resip::Data SipChatManagerImpl::buildSdp(msrp_uri_t* localUri, UInt32 localMediaPort, const resip::Mime& cpimContentType, bool isActive)
{
   return SipChatHelper::buildSdp(localUri, localMediaPort, cpimContentType, isActive);
}

}
}

#endif
