#pragma once

#if !defined(CPCAPI2_SIP_CONVERSATION_MANAGER_INTERFACE_H)
#define CPCAPI2_SIP_CONVERSATION_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "call/SipConversationManager.h"
#include "call/SipConversationManagerExt.h"
#include "call/SipConversationHandler.h"
#include "../phone/PhoneModule.h"
#include "SipConversationManagerInternal.h"
#include "SipConversationHandlerInternal.h"
#include "media/audio/AudioHandler.h"
#include "recording/RecordingHandler.h"

#include <rutil/compat.hxx>

#include <map>
#include <memory>
#include <set>
#include <string>
#include <vector>

namespace CPCAPI2
{
class PhoneInterface;

namespace SipAccount
{
class SipAccountInterface;
}

namespace Media
{
class MediaManagerInterface;
}

namespace SipConversation
{
class SipAVConversationManagerImpl;
struct SipCallCreationInfo;

class SipConversationAdornmentInternalHandler
{
public:
   virtual int onConversationAdornment(SipAccount::SipAccountHandle account, ConversationAdornmentInternalEvent& args) = 0;
};

class SipAVConversationManagerInterface : public EventSource2< EventHandler<SipConversationHandler, SipAccount::SipAccountHandle> >,
                                          public SipConversationManager,
                                          public SipConversationManagerExt,
                                          public SipConversationManagerInternal,
                                          public PhoneModule,
                                          public SipConversationAdornmentInternalHandler
{

public:

   SipAVConversationManagerInterface(Phone* phone);
   virtual ~SipAVConversationManagerInterface();

   FORWARD_EVENT_PROCESSOR(SipAVConversationManagerInterface);

   virtual void Release() OVERRIDE;

   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipConversationHandler* handler) OVERRIDE;

   virtual int setDefaultSettings(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const CPCAPI2::SipConversationSettings& settings) OVERRIDE;
   virtual int setDefaultSettings(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const CPCAPI2::SipConversationSettings& settings, CPCAPI2::NetworkTransport transport) OVERRIDE;

   virtual int setCallInitiatedFromPush(SipConversationHandle conversation) OVERRIDE;
   virtual SipConversationHandle createConversation(CPCAPI2::SipAccount::SipAccountHandle account, SipConversationHandle conversation) OVERRIDE;
   virtual SipConversationHandle createConversation(CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;
   virtual int addParticipant(SipConversationHandle conversation, const cpc::string& targetAddress) OVERRIDE;
   virtual int setMediaEnabled(SipConversationHandle conversation, CPCAPI2::SipConversation::MediaType mediaType, bool enabled) OVERRIDE;
   virtual int setMediaEnabledByDirection(SipConversationHandle conversation, CPCAPI2::SipConversation::MediaType mediaType, bool enabled) OVERRIDE;
   virtual int configureMedia(SipConversationHandle conversation, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor) OVERRIDE;
   virtual int setAnonymousMode(SipConversationHandle conversation, unsigned int anonymousMode) OVERRIDE;
   virtual int setBestEffortMediaEncryption(SipConversationHandle conversation, bool enabled) OVERRIDE;
   virtual int setMediaCryptoSuites(SipConversationHandle conversation, cpc::vector<MediaCryptoSuite> cryptoSuites) OVERRIDE;
   virtual int setCryptoSuitesForMedia(SipConversationHandle conversation, CPCAPI2::SipConversation::MediaType mediaType, cpc::vector<MediaCryptoSuite> cryptoSuites) OVERRIDE;
   virtual int start(SipConversationHandle conversation) OVERRIDE;

   virtual int hold(SipConversationHandle conversation) OVERRIDE;
   virtual int unhold(SipConversationHandle conversation) OVERRIDE;
   virtual int sendMediaChangeRequest(SipConversationHandle conversation) OVERRIDE;

   virtual int end(SipConversationHandle conversation) OVERRIDE;

   // incoming
   virtual int redirect(SipConversationHandle conversation, const cpc::string& targetAddress, const cpc::string& reason) OVERRIDE;
   virtual int sendRingingResponse(SipConversationHandle conversation) OVERRIDE;
   virtual int reject(SipConversationHandle conversation, unsigned int rejectReason = 0) OVERRIDE;
   virtual int accept(SipConversationHandle conversation) OVERRIDE;

   // transfer
   virtual int transfer(SipConversationHandle transferTargetConversation, SipConversationHandle transfereeConversation) OVERRIDE;
   virtual int transfer(SipConversationHandle transferTargetConversation, SipConversationHandle transfereeConversation, bool endTargetConversationOnSuccess) OVERRIDE;
   virtual int transfer(SipConversationHandle conversation, const cpc::string& targetAddress) OVERRIDE;
   virtual int acceptIncomingTransferRequest(SipConversationHandle transferTargetConversation) OVERRIDE;
   virtual int rejectIncomingTransferRequest(SipConversationHandle transferTargetConversation) OVERRIDE;

   // DTMF
   virtual int setDtmfMode(CPCAPI2::SipAccount::SipAccountHandle account, unsigned int ordinal, CPCAPI2::SipConversation::DtmfMode dtmfMode) OVERRIDE;
   virtual int startDtmfTone(SipConversationHandle conversation, unsigned int toneId, bool playLocally) OVERRIDE;
   virtual int stopDtmfTone() OVERRIDE;

   // Answer-Mode
   virtual int setAnswerMode(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipConversation::AnswerModeSettings answerMode, CPCAPI2::NetworkTransport transport) OVERRIDE;

   // Network Change Handover
   virtual SipConversationHandle networkChangeStarcodeHandover(SipConversationHandle originalConversation);
   virtual int ignoreBindingFilterForStarcodeHandover(SipConversationHandle conversation) OVERRIDE;

   // Statistics
   virtual int refreshConversationStatistics(SipConversationHandle conversation) OVERRIDE;
   virtual int refreshConversationStatistics(SipConversationHandle conversation, bool includeNetworkStatistics, bool includeJitterStatistics, bool includeRemoteStatistics) OVERRIDE;
   // SipConversationManagerInternal
   virtual int refreshConversationStatistics(SipConversationHandle conversation, bool includeNetworkStatistics, bool includeJitterStatistics, bool includeRemoteStatistics, bool force) OVERRIDE;

   // Adornment
   virtual int setAdornmentHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipConversationAdornmentHandler* handler) OVERRIDE;
   virtual int adornMessage(SipConversationHandle conversation, unsigned int adornmentId, const cpc::vector<SipHeader>& customHeaders) OVERRIDE;

   // Audio levels
   virtual int startMonitoringAudioDeviceLevels(SipConversationHandle conversation) OVERRIDE;
   virtual int stopMonitoringAudioDeviceLevels(SipConversationHandle conversation) OVERRIDE;

   // SDK in the Cloud
   virtual int provideSdpOffer(SipConversationHandle conversation, const CPCAPI2::SipConversation::SessionDescription& sdpOffer) OVERRIDE;
   virtual int provideSdpAnswer(SipConversationHandle conversation, const CPCAPI2::SipConversation::SessionDescription& sdpOffer, const CPCAPI2::SipConversation::SessionDescription& sdpAnswer) OVERRIDE;
   virtual int setExternalSdpHandlingEnabled(SipConversationHandle conversation, bool enabled) OVERRIDE;

   // SipConversationManagerExt
   virtual int setVideoNackSettings(SipConversationHandle conversation, const NackSettings& nackSettings) OVERRIDE;
   virtual int sendInfo(SipConversationHandle conversation, const cpc::string& body) OVERRIDE;
   virtual int setForceTarget(SipConversationHandle conversation, const cpc::string& target) OVERRIDE;

   int setCallToReplace(SipConversationHandle conversation, const cpc::string& callId, const cpc::string& localTag, const cpc::string& remoteTag);
   int setCallToJoin(SipConversationHandle conversation, const cpc::string& callId, const cpc::string& localTag, const cpc::string& remoteTag, const cpc::vector<Parameter>& joinParams = cpc::vector<Parameter>());

   int setFromAddress(SipConversationHandle conversation, const cpc::string& fromAddress);
   int addHeader(SipConversationHandle conversation, const SipHeader& header);

   int addToRecorder(SipConversationHandle conversation, Recording::RecorderHandle recorder);
   int removeFromRecorder(SipConversationHandle conversation, Recording::RecorderHandle recorder);

   PhoneInterface* phoneInterface();

   CPCAPI2::SipAccount::SipAccountHandle getSipAccountHandle(SipConversationHandle conversation) const;
   CPCAPI2::SipAccount::SipAccountHandle getSipAccountHandleForCallId(const cpc::string& callId) const;
   bool getConversationSettings(CPCAPI2::SipConversationSettings& settings, CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::NetworkTransport transport = CPCAPI2::TransportNone);

   // Returns the leftover active call as all others get destroyed, and populates the list of calls that have been ended
   SipConversationHandle endCallsNotApplicableForStarcodeNetworkChange(cpc::vector<SipConversationHandle>& endedCalls);
   int getActiveCallCountForStarcodeHandling();
   bool isStarcodeHandlingRequired();

   int setHandlerImpl(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipConversationHandler* handler);

   int setDefaultSettingsImpl(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const CPCAPI2::SipConversationSettings& settings);

   virtual int playSound(SipConversationHandle conversation,const cpc::string& resourceUri, bool repeat = false) OVERRIDE;
   virtual int playSound(SipConversationHandle conversation, PlaySoundStream* playSoundStream, bool repeat = false) OVERRIDE;
   virtual int stopPlaySound(SipConversationHandle conversation) OVERRIDE;

   int enableMusicOnHold(SipConversationHandle conversation,bool on);

   int decodeProvisioningResponse(const cpc::string& provisioningResponse, cpc::vector<SipConversationSettings>& outConversationSettings) OVERRIDE;

   virtual int getCallCount() OVERRIDE;
   int getCallCount(CPCAPI2::SipAccount::SipAccountHandle account);

   int setCallKitMode(SipConversationHandle conversation) OVERRIDE;
   int setTelecomFrameworkMode(SipConversationHandle conversation) OVERRIDE;

   virtual int setIncomingVideoRenderTarget(SipConversationHandle conversation, void* surface, Media::VideoSurfaceType type) OVERRIDE;

   virtual int setAudioDeviceCloseDelay(SipConversationHandle conversation, int audioDeviceCloseDelay) OVERRIDE;

private:

   SipAVConversationManagerImpl* initImpl(CPCAPI2::SipAccount::SipAccountHandle account);

   int createConversationImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipConversationHandle h);
   int addParticipantImpl(SipConversationHandle conversation, const cpc::string& targetAddress);
   int setMediaEnabledImpl(SipConversationHandle conversation, MediaType mediaType, bool enabled);
   int setMediaEnabledByDirectionImpl(SipConversationHandle conversation, MediaType mediaType, bool enabled);
   int configureMediaImpl(SipConversationHandle conversation, const MediaInfo& mediaDescriptor);
   int setAnonymousModeImpl(SipConversationHandle conversation, unsigned int anonymousMode);
   int setBestEffortMediaEncryptionImpl(SipConversationHandle conversation, bool enabled);
   int setMediaCryptoSuitesImpl(SipConversationHandle conversation, cpc::vector<MediaCryptoSuite> cryptoSuites);
   int setCryptoSuitesForMediaImpl(SipConversationHandle conversation, CPCAPI2::SipConversation::MediaType mediaType, cpc::vector<MediaCryptoSuite> cryptoSuites);
   int startImpl(SipConversationHandle conversation);
   int holdImpl(SipConversationHandle conversation);
   int unholdImpl(SipConversationHandle conversation);
   int sendMediaChangeRequestImpl(SipConversationHandle conversation);
   int endImpl(SipConversationHandle conversation);
   int redirectImpl(SipConversationHandle conversation, const cpc::string& targetAddress, const cpc::string& reason);
   int sendRingingResponseImpl(SipConversationHandle conversation);
   int sendInfoImpl(SipConversationHandle conversation, const cpc::string& body);
   int rejectImpl(SipConversationHandle conversation, unsigned int rejectReason);
   int acceptImpl(SipConversationHandle conversation);
   int transferImpl(SipConversationHandle transferTargetConversation, SipConversationHandle transfereeConversation, bool endTargetConversationOnSuccess=true);
   int transferImpl(SipConversationHandle conversation, const cpc::string& targetAddress);
   int acceptIncomingTransferRequestImpl(SipConversationHandle transferTargetConversation);
   int setDtmfModeImpl(CPCAPI2::SipAccount::SipAccountHandle account, unsigned int ordinal, CPCAPI2::SipConversation::DtmfMode dtmfMode);
   int startDtmfToneImpl(SipConversationHandle conversation, unsigned int toneId, bool playLocally);
   int setAnswerModeImpl(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipConversation::AnswerModeSettings, CPCAPI2::NetworkTransport transport);
   int networkChangeStarcodeHandoverImpl(SipConversationHandle conversation, SipConversationHandle originalConversation);
   int ignoreBindingFilterForStarcodeHandoverImpl(SipConversationHandle conversation);
   int refreshConversationStatisticsImpl(SipConversationHandle conversation, bool includeNetworkStatistics, bool includeJitterStatistics, bool includeRemoteStatistics, bool force);
   int dtmfToneChannelCleanup(int channel_for_tone);
   int setCallToReplaceImpl(SipConversationHandle conversation, const cpc::string& callId, const cpc::string& fromTag, const cpc::string& toTag);
   int setCallToJoinImpl(SipConversationHandle conversation, const cpc::string& callId, const cpc::string& fromTag, const cpc::string& toTag, const cpc::vector<Parameter>& joinParams);
   int setDefaultSettingsImpl2(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipConversationSettings& settings, CPCAPI2::NetworkTransport transport);
   int setFromAddressImpl(SipConversationHandle conversation, const cpc::string& fromAddress);
   int addHeaderImpl(SipConversationHandle conversation, const SipHeader& header);
   int addToRecorderImpl(SipConversationHandle conversation, Recording::RecorderHandle recorder);
   int removeFromRecorderImpl(SipConversationHandle conversation, Recording::RecorderHandle recorder);
   int startMonitoringAudioDeviceLevelsImpl(SipConversationHandle conversation);
   int stopMonitoringAudioDeviceLevelsImpl(SipConversationHandle conversation);
   int playSoundImpl(SipConversationHandle conversation, const cpc::string& resourceUri, bool repeat);
   int playSoundStreamImpl(SipConversationHandle conversation, PlaySoundStream* playSoundStream, bool repeat);
   int stopPlaySoundImpl(SipConversationHandle conversation);
   int setCallKitModeImpl(SipConversationHandle conversation);
   int setTelecomFrameworkModeImpl(SipConversationHandle conversation);
   int provideSdpOfferImpl(SipConversationHandle conversation, const CPCAPI2::SipConversation::SessionDescription& sdpOffer);
   int provideSdpAnswerImpl(SipConversationHandle conversation, const CPCAPI2::SipConversation::SessionDescription& sdpOffer, const CPCAPI2::SipConversation::SessionDescription& sdpAnswer);
   int setExternalSdpHandlingEnabledImpl(SipConversationHandle conversation, bool enabled);
   int setDtlsSupported(CPCAPI2::SipAccount::SipAccountHandle account, bool dtlsSupported) OVERRIDE;
   int setDtlsSupportedImpl(CPCAPI2::SipAccount::SipAccountHandle account, bool dtlsSupported);
   int internalStarCodeHandling(const cpc::string& targetAddress, SipConversationHandle conversation, SipCallCreationInfo& creationInfo);
   int setIncomingVideoRenderTargetImpl(SipConversationHandle conversation, void* surface, Media::VideoSurfaceType type);
   int setAudioDeviceCloseDelayImpl(SipConversationHandle conversation, int audioDeviceCloseDelay);
   int setVideoNackSettingsImpl(SipConversationHandle conversation, const NackSettings& nackSettings);
   int setForceTargetImpl(SipConversationHandle conversation, const cpc::string& target);
   int setNackPliEnabled(bool enabled) OVERRIDE;
   int setNackPliEnabledImpl(bool enabled);

   SipCallCreationInfo* getCreationInfo(SipConversationHandle h) const;
   SipAVConversationManagerImpl* getConvImpl(SipConversationHandle h);
   void configureMedia(const MediaInfo& media, cpc::vector<MediaInfo>& mediaList);

   void logDtmfPrefs(const std::string& msg, SipAVConversationManagerImpl& acct);

   // Adornment
   int setAdornmentHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipConversationAdornmentHandler* handler);
   virtual int onConversationAdornment(SipConversationHandle conversation, ConversationAdornmentInternalEvent& args) OVERRIDE;

private:

   CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;

   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, SipAVConversationManagerImpl*> AccountMap;
   std::shared_ptr<AccountMap> mAccountMap;
   std::map<CPCAPI2::SipAccount::SipAccountHandle, SipConversationHandler*> mHandlers;

   CPCAPI2::Media::MediaManagerInterface* mMediaIf;
   PhoneInterface* mPhone;

   typedef std::map<unsigned int, ConversationAdornmentInternalEvent*> AdornmentEventMap;
   AdornmentEventMap mAdornmentEventMap;

   typedef std::map<SipAccount::SipAccountHandle, SipConversationAdornmentHandler*> AdornmentHandlerMap;
   AdornmentHandlerMap mAdornmentHandlerMap;

   UInt64 mLastStatsRefreshTime_Secs;
   int mVideoPacketRedundancyFactor = 1;
   bool mReceiveTmmbrEnabled = true;

};

std::ostream& operator<<(std::ostream& os, const CPCAPI2::SipConversation::MediaCryptoSuite crypto);
std::ostream& operator<<(std::ostream& os, const std::set<CPCAPI2::SipConversation::MediaCryptoSuite>& cryptos);
std::ostream& operator<<(std::ostream& os, const SipConversation::NewConversationEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipConversation::ErrorEvent& evt);
std::ostream& operator<<(std::ostream& os, const ConversationEndedEvent & evt);
std::ostream& operator<<(std::ostream& os, const TransferRequestEvent& evt);
std::ostream& operator<<(std::ostream& os, const RedirectRequestEvent& evt);
std::ostream& operator<<(std::ostream& os, const TargetChangeRequestEvent& evt);
std::ostream& operator<<(std::ostream& os, const TargetChangeRequestEvent& evt);
std::ostream& operator<<(std::ostream& os, const HangupRequestEvent& evt);
std::ostream& operator<<(std::ostream& os, const BroadsoftTalkEvent& evt);
std::ostream& operator<<(std::ostream& os, const BroadsoftHoldEvent& evt);
std::ostream& operator<<(std::ostream& os, const TransferProgressEvent& evt);
std::ostream& operator<<(std::ostream& os, const TransferResponseEvent& evt);
std::ostream& operator<<(std::ostream& os, const ConversationStateChangeRequestEvent& evt);
std::ostream& operator<<(std::ostream& os, const ConversationStateChangedEvent& evt);
std::ostream& operator<<(std::ostream& os, const ConversationMediaChangeRequestEvent& evt);
std::ostream& operator<<(std::ostream& os, const ConversationMediaChangedEvent& evt);
std::ostream& operator<<(std::ostream& os, const ConversationStatisticsUpdatedEvent& evt);
std::ostream& operator<<(std::ostream& os, const ConversationAudioDeviceLevelChangeEvent& evt);
std::ostream& operator<<(std::ostream& os, const SdpOfferAnswerEvent& evt);
std::ostream& operator<<(std::ostream& os, const LocalSdpOfferEvent& evt);
std::ostream& operator<<(std::ostream& os, const ConversationInitiatedEvent& evt);
std::ostream& operator<<(std::ostream& os, const LocalSdpAnswerEvent& evt);
std::ostream& operator<<(std::ostream& os, const ConversationEndedEventFromStarcodeNetworkChange& evt);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::SipConversation::SipCallCreationInfo& info);

}

}

#endif // CPCAPI2_SIP_CONVERSATION_MANAGER_INTERFACE_H
