#pragma once

#if !defined(CPCAPI2_CloudRelay_MANAGER_INTERFACE_H)
#define CPCAPI2_CloudRelay_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "cloudrelay/CloudRelayManager.h"
#include "cloudrelay/CloudRelayHandler.h"
#include "cloudrelay/CloudRelayInternal.h"
#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"
#include "../util/AutoTestProcessor.h"

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>

#include <rutil/Fifo.hxx>

#include <map>
#include <thread>

namespace CPCAPI2
{
class PhoneInterface;

namespace CloudRelay
{
class CloudRelayManagerImpl;

class CloudRelayManagerInterface : public CloudRelayManagerInternal,
   public PhoneModule
#ifdef CPCAPI2_AUTO_TEST
   , public AutoTestProcessor
#endif
{
public:
   CloudRelayManagerInterface(Phone* phone);
   virtual ~CloudRelayManagerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   virtual int process(unsigned int timeout) OVERRIDE;
#ifdef CPCAPI2_AUTO_TEST
   virtual AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif

   virtual void interruptProcess() OVERRIDE;

   void post(resip::ReadCallbackBase* f);
   void postCallback(resip::ReadCallbackBase*);

   void addSdkObserver(CloudRelayHandler* observer);
   void removeSdkObserver(CloudRelayHandler* observer);

   // CloudRelayManagerInternal
   static CloudRelayManagerInternal* getInternalInterface(Phone* cpcPhone);
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE;

   // CloudRelayManager
   virtual int start() OVERRIDE;
   virtual int shutdown() OVERRIDE;
   virtual CloudRelayHandle getRelay() OVERRIDE;
   virtual int broadcast(CloudRelayHandle relay, const cpc::string& msg) OVERRIDE;
   virtual int sendTo(CloudRelayHandle relay, CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const cpc::string& msg) OVERRIDE;
   virtual int setHandler(CloudRelayHandler* handler) OVERRIDE;
   virtual int setHandler(CloudRelayHandle relay, CloudRelayHandler* handler) OVERRIDE;

   CloudRelayManagerImpl* getImpl(CloudRelayHandle relay) const;
   int broadcast(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, CloudRelayHandle relay, const cpc::string& msg);

private:
   int startImpl();
   int shutdownImpl();
   int getRelayImpl(CloudRelayHandle h, CloudRelayHandler* handler);
   int setHandlerImpl(CloudRelayHandle relay, CloudRelayHandler* handler);
   int broadcastImpl(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, CloudRelayHandle relay, const cpc::string& msg);
   int sendToImpl(CloudRelayHandle relay, CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const cpc::string& msg);

private:
   bool mShutdown;
   resip::Fifo<resip::ReadCallbackBase> mCallbackFifo;
   PhoneInterface* mPhone;

   std::set<CloudRelayHandler*> mSdkObservers;
   std::function<void(void)> mCbHook;

   std::map<CloudRelayHandle, CloudRelayManagerImpl*> mRelays;
};


class CloudRelayHandleFactory
{
public:
   static CloudRelayHandle getNext() { return sNextHandle; }
private:
   static CloudRelayHandle sNextHandle;
};

}
}

#endif // CPCAPI2_CloudRelay_MANAGER_INTERFACE_H
