#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOQUERYCODECLIST_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOQUERYCODECLIST_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
  class AudioQueryCodecList
  {
  public:
    int32_t phoneHandle;

    AudioQueryCodecList() = default;

    AudioQueryCodecList(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const AudioQueryCodecList& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, AudioQueryCodecList& o)
    {
     o.phoneHandle = j.at("phoneHandle");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOQUERYCODECLIST_H
