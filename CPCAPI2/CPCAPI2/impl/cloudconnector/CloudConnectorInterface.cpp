#include "brand_branded.h"

#if (CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE == 1)
#include "CloudConnectorInterface.h"
#include "CloudConnectorImpl.h"
#include "cloudconnector/CloudConnectorHandler.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER


namespace CPCAPI2
{

namespace CloudConnector
{

CloudConnectorHandle CloudConnectorHandleFactory::sNextHandle = 1;

CloudConnectorInterface::CloudConnectorInterface(Phone* phone) :
EventSource<CloudConnectorHandle, CloudConnectorHandler, CloudConnectorSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
   mPhone(dynamic_cast<PhoneInterface*>(phone)),
   mLastServiceConnectionStatus(ServiceConnectionStatus_Disconnected)
{
   StackLog(<< "CloudConnectorInterface::CloudConnectorInterface(): " << this << " phone: " << phone);
   ((NetworkChangeManagerInterface*)NetworkChangeManager::getInterface(mPhone))->addSdkObserver(this);
   mPhone->addRefImpl();
}

CloudConnectorInterface::~CloudConnectorInterface()
{
   StackLog(<< "CloudConnectorInterface::~CloudConnectorInterface(): " << this << " phone: " << mPhone);
   ((NetworkChangeManagerInterface*)NetworkChangeManager::getInterface(mPhone))->removeSdkObserver(this);
   mPhone->releaseImpl();
}

void CloudConnectorInterface::Release()
{
   mInstMap.clear();
   delete this;
}

CloudConnectorHandle CloudConnectorInterface::createCloudConnector()
{
   CloudConnectorHandle h = CloudConnectorHandleFactory::getNext();
   postToSdkThread(resip::resip_bind(&CloudConnectorInterface::createCloudConnectorImpl, this, h));
   return h;
}

void CloudConnectorInterface::createCloudConnectorImpl(CloudConnectorHandle conn)
{
   std::shared_ptr<CloudConnectorImpl> pimpl(new CloudConnectorImpl(this, conn));

   NetworkTransport transport = NetworkChangeManager::getInterface(mPhone)->networkTransport();
   if (pimpl->isRestrictedNetwork(transport) || transport == TransportNone)
   {
      DebugLog(<< "CloudConnectorInterface::createCloudConnectorImpl(): " << this << " adding network restriction for transport: " << transport);
      pimpl->addRestriction(Restriction_FromNetwork);
   }

   mInstMap[conn] = std::move(pimpl);
}

int CloudConnectorInterface::setHandler(CloudConnectorHandle conn, CloudConnectorHandler* handler)
{
   setAppHandler(conn, handler);
   return kSuccess;
}

int CloudConnectorInterface::destroy(CloudConnectorHandle conn)
{
   postToSdkThread(resip::resip_bind(&CloudConnectorInterface::destroyImpl, this, conn));
   return kSuccess;
}

void CloudConnectorInterface::destroyImpl(CloudConnectorHandle conn)
{
   DebugLog(<< "CloudConnectorInterface::destroyImpl(): " << this << " phone: " << mPhone << " cloud connector handle: " << conn);

   CloudConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it == mInstMap.end())
   {
      DebugLog(<< "CloudConnectorInterface::destroyImpl(): " << this << " connector handle: " << conn << " not found");
   }
   else
   {
      it->second->destroy();
      mInstMap.erase(conn);
   }
}

int CloudConnectorInterface::logout(CloudConnectorHandle conn)
{
   DebugLog(<< "CloudConnectorInterface::logout(): " << this << " phone: " << mPhone << " cloud connector handle: " << conn);
   postToSdkThread(resip::resip_bind(&CloudConnectorInterface::logoutImpl, this, conn));
   return kSuccess;
}

void CloudConnectorInterface::logoutImpl(CloudConnectorHandle conn)
{
   DebugLog(<< "CloudConnectorInterface::logoutImpl(): " << this << " phone: " << mPhone << " cloud connector handle: " << conn);

   CloudConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it == mInstMap.end())
   {
      DebugLog(<< "CloudConnectorInterface::logoutImpl(): " << this << " connector handle: " << conn << " not found");
   }
   else
   {
      it->second->logout();
   }
}

int CloudConnectorInterface::setConnectionSettings(CloudConnectorHandle conn, const CloudConnectorSettings& settings)
{
   postToSdkThread(resip::resip_bind(&CloudConnectorInterface::setConnectionSettingsImpl, this, conn, settings));
   return kSuccess;
}

void CloudConnectorInterface::setConnectionSettingsImpl(CloudConnectorHandle conn, const CloudConnectorSettings& settings)
{
   CloudConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it == mInstMap.end())
   {
      DebugLog(<< "CloudConnectorInterface::setConnectionSettingsImpl(): " << this << " connector handle: " << conn << " not found");
   }
   else
   {
      if (it->second)
      {
         it->second->setConnectionSettings(settings);
      }
   }
}

int CloudConnectorInterface::requestService(CloudConnectorHandle conn, const ServiceDescription& serviceDescriptor)
{
   DebugLog(<< "CloudConnectorInterface::requestService(conn=" << conn << ", serviceDescriptor.region=" <<
            serviceDescriptor.region << " serviceDescriptor.service=" << serviceDescriptor.service);
   
   postToSdkThread(resip::resip_bind(&CloudConnectorInterface::requestServiceImpl, this, conn, serviceDescriptor));
   return kSuccess;
}

void CloudConnectorInterface::requestServiceImpl(CloudConnectorHandle conn, const ServiceDescription& serviceDescriptor)
{
   CloudConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it == mInstMap.end())
   {
      DebugLog(<< "CloudConnectorInterface::requestServiceImpl(): " << this << " connector handle: " << conn << " not found");
   }
   else
   {
      it->second->requestService(serviceDescriptor);
   }
}

int CloudConnectorInterface::connectToServices(CloudConnectorHandle conn)
{
   DebugLog(<< "CloudConnectorInterface::connectToServices(" << conn << ")");
   postToSdkThread(resip::resip_bind(&CloudConnectorInterface::connectToServicesImpl, this, conn));
   return kSuccess;
}

void CloudConnectorInterface::connectToServicesImpl(CloudConnectorHandle conn)
{
   CloudConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it == mInstMap.end())
   {
      DebugLog(<< "CloudConnectorInterface::connectToServicesImpl(): " << this << " connector handle: " << conn << " not found");
      // Callback cannot be sent back to handler as the handle is invalid, thus no app handler will be found
   }
   else
   {
      DebugLog(<< "CloudConnectorInterface::connectToServicesImpl for handle " << conn << " last mLastServiceConnectionStatus: " << mLastServiceConnectionStatus);
      if (mLastServiceConnectionStatus == ServiceConnectionStatus_Connected)
      {
         WarningLog(<< "App is trying to call connectToServices but mLastServiceConnectionStatus is already ServiceConnectionStatus_Connected");
      }

      it->second->removeRestriction(Restriction_FromUser);
   }
}

CPCAPI2::Phone* CloudConnectorInterface::getPhone(CloudConnectorHandle conn, const ServiceDescription& serviceDescriptor)
{
   StackLog(<< "CloudConnectorInterface::getPhone(): " << this << " phone: " << mPhone << " conn: " << conn << " service: " << serviceDescriptor.service);
   ServiceDesc desc;
   desc.service = serviceDescriptor.service;
   desc.region = serviceDescriptor.region;
   PhoneMap::iterator it = mPhoneMap.find(desc);
   if (it != mPhoneMap.end())
   {
      StackLog(<< "CloudConnectorInterface::getPhone(): " << this << " phone: " << mPhone << " conn: " << conn << " service: " << serviceDescriptor.service << " returning phone: " << it->second);
      return it->second;
   }

   DebugLog(<< "CloudConnectorInterface::getPhone(): " << this << " connector handle: " << conn << " no phone mapping found for service: " << desc.service << " region: " << desc.region);
   return NULL;
}

int CloudConnectorInterface::disconnectService(CloudConnectorHandle conn, const ServiceDescription& serviceDescriptor)
{
   postToSdkThread(resip::resip_bind(&CloudConnectorInterface::disconnectServiceImpl, this, conn, serviceDescriptor));
   return kSuccess;
}

void CloudConnectorInterface::disconnectServiceImpl(CloudConnectorHandle conn, const ServiceDescription& serviceDescriptor)
{
   CloudConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it == mInstMap.end())
   {
      DebugLog(<< "CloudConnectorInterface::disconnectServiceImpl(): " << this << " connector handle: " << conn << " not found");
   }
   else
   {
      DebugLog(<< "CloudConnectorInterface::disconnectServiceImpl(): " << this << " phone: " << mPhone << " conn: " << conn << " service: " << serviceDescriptor.service << " mLastServiceConnectionStatus: " << mLastServiceConnectionStatus);
      it->second->disconnectService(serviceDescriptor);
   }
}

int CloudConnectorInterface::addUser(CloudConnectorHandle conn, const AddUserRequest& req)
{
   postToSdkThread(resip::resip_bind(&CloudConnectorInterface::addUserImpl, this, conn, req));
   return kSuccess;
}

void CloudConnectorInterface::addUserImpl(CloudConnectorHandle conn, const AddUserRequest& req)
{
   CloudConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it == mInstMap.end())
   {
      DebugLog(<< "CloudConnectorInterface::addUserImpl(): " << this << " connector handle: " << conn << " not found");
   }
   else
   {
      it->second->addUser(req);
   }
}

void CloudConnectorInterface::fireConnStatusEvent(CloudConnectorHandle conn, const ServiceConnectionStatusEvent& evt)
{
   mLastServiceConnectionStatus = evt.connectionStatus;
   DebugLog(<< "CloudConnectorInterface::onServiceConnectionStatusChanged handle " << conn << " event: " << evt);

   fireEvent(cpcFunc(CloudConnectorHandler::onServiceConnectionStatusChanged), conn, evt);
}

void CloudConnectorInterface::fireAddUserResponse(CloudConnectorHandle conn, const AddUserResponse& resp)
{
   fireEvent(cpcFunc(CloudConnectorHandler::onAddUserResponse), conn, resp);
}

void CloudConnectorInterface::fireLogoutResult(CloudConnectorHandle conn, const LogoutResult& result)
{
   fireEvent(cpcFunc(CloudConnectorHandler::onLogout), conn, result);
}

void CloudConnectorInterface::addServiceToPhoneMapping(const ServiceDesc& desc, CPCAPI2::Phone* p)
{
   postCallback(resip::resip_bind(&CloudConnectorInterface::addServiceToPhoneMappingImpl, this, desc, p));
}

void CloudConnectorInterface::addServiceToPhoneMappingImpl(const ServiceDesc& desc, CPCAPI2::Phone* p)
{
   mPhoneMap[desc] = p;
}

void CloudConnectorInterface::removeServiceFromPhoneMapping(const ServiceDesc& desc)
{
   postCallback(resip::resip_bind(&CloudConnectorInterface::removeServiceFromPhoneMappingImpl, this, desc));
}

void CloudConnectorInterface::removeServiceFromPhoneMappingImpl(const ServiceDesc& desc)
{
   mPhoneMap.erase(desc);
}

// XmppAgentHandler
/*
int CloudConnectorInterface::onPushRegistrationSuccess(XmppAgent::XmppPushRegistrationHandle pushRegistration, const XmppAgent::XmppPushRegistrationSuccessEvent& args)
{
   DebugLog(<< "CloudConnectorInterface::onPushRegistrationSuccess(): xmpp push registration handle: " << pushRegistration);
   return kSuccess;
}

int CloudConnectorInterface::onPushRegistrationFailure(XmppAgent::XmppPushRegistrationHandle pushRegistration, const XmppAgent::XmppPushRegistrationFailureEvent& args)
{
   DebugLog(<< "CloudConnectorInterface::onPushRegistrationFailure(): xmpp push registration handle: " << pushRegistration);
   return kSuccess;
}

int CloudConnectorInterface::onEventHistory(XmppAgent::XmppPushRegistrationHandle pushRegistration, const XmppAgent::XmppEventHistory& args)
{
   DebugLog(<< "CloudConnectorInterface::onEventHistory(): xmpp push registration handle: " << pushRegistration);
   return kSuccess;
}

int CloudConnectorInterface::onRemoteSyncRegisterResult(XmppAgent::XmppPushRegistrationHandle pushRegistration, const XmppAgent::XmppAgentRemoteSyncRegisterResult& args)
{
   DebugLog(<< "CloudConnectorInterface::onRemoteSyncRegisterResult(): xmpp push registration handle: " << pushRegistration);
   return kSuccess;
}

int CloudConnectorInterface::onLogout(XmppAgent::XmppPushRegistrationHandle pushRegistration, const XmppAgent::LogoutResult& args)
{
   DebugLog(<< "CloudConnectorInterface::onLogout(): xmpp push registration handle: " << pushRegistration);
   // Account has been disabled and destroyed, IM sync and Push have been de-registered,
   // still need to destroy the sdk instance on agent and the connection
   return kSuccess;
}
*/

// NetworkChangeHandler

int CloudConnectorInterface::onNetworkChange(const NetworkChangeEvent& event)
{
   postToSdkThread(resip::resip_bind(&CloudConnectorInterface::onNetworkChangeImpl, this, event));
   return 0;
}

void CloudConnectorInterface::onNetworkChangeImpl(const NetworkChangeEvent& event)
{
   NetworkTransport transport = event.networkTransport;
   DebugLog(<< "CloudConnectorInterface::onNetworkChangeImpl(): transport: " << transport);

   for (InstanceMap::iterator i = mInstMap.begin(); i != mInstMap.end(); ++i)
   {
      if (transport == TransportNone)
      {
         DebugLog(<< "CloudConnectorInterface::onNetworkChangeImpl(): no network, cloud connector handle: " << i->first);
         i->second->addRestriction(Restriction_FromNetwork);
      }
      else if (i->second->isRestrictedNetwork(transport))
      {
         DebugLog(<< "CloudConnectorInterface::onNetworkChangeImpl(): restricted network, transport: " << transport << " cloud connector handle: " << i->first);
         i->second->addRestriction(Restriction_FromNetwork);
      }
      else
      {
         DebugLog(<< "CloudConnectorInterface::onNetworkChangeImpl(): unrestricted network, cloud connector handle: " << i->first);
         i->second->removeRestriction(Restriction_FromNetwork);
      }
   }
}

int CloudConnectorInterface::setNetworkRestriction(CloudConnectorHandle conn, NetworkTransport transport, bool restricted)
{
   if (transport == TransportNone)
   {
      DebugLog(<< "CloudConnectorInterface::setNetworkRestriction(): ignoring invalid request to set restriction: " << restricted << " on invalid transport: " << transport << " connector handle: " << conn);
      return kError;
   }

   postToSdkThread(resip::resip_bind(&CloudConnectorInterface::setNetworkRestrictionImpl, this, conn, transport, restricted));
   return kSuccess;
}

void CloudConnectorInterface::setNetworkRestrictionImpl(CloudConnectorHandle conn, NetworkTransport transport, bool restricted)
{
   CloudConnectorInterface::InstanceMap::iterator i = mInstMap.find(conn);
   if (i == mInstMap.end())
   {
      DebugLog(<< "CloudConnectorInterface::setNetworkRestrictionImpl(): " << this << " connector handle: " << conn << " not found");
      return;
   }

   if (restricted != i->second->isRestrictedNetwork(transport))
   {
      std::set<NetworkTransport>& networks = i->second->restrictedNetworks();

      if (restricted)
      {
         networks.insert(transport);
      }
      else
      {
         networks.erase(transport);
      }

      NetworkTransport currentTransport = NetworkChangeManager::getInterface(mPhone)->networkTransport();

      if (currentTransport == transport)
      {
         NetworkChangeEvent event;
         event.networkTransport = transport;
         onNetworkChangeImpl(event);
      }
   }
}

cpc::string get_debug_string(const CPCAPI2::CloudConnector::ServiceConnectionStatus& status)
{
   switch (status)
   {
      case ServiceConnectionStatus_Disconnecting: return "disconnecting";
      case ServiceConnectionStatus_Disconnected: return "disconnected";
      case ServiceConnectionStatus_Connecting: return "connecting";
      case ServiceConnectionStatus_Authenticating: return "authenticating";
      case ServiceConnectionStatus_Connected: return "connected";
      case ServiceConnectionStatus_ConnFailure: return "connfailure";
      case ServiceConnectionStatus_AuthFailure: return "authfailure";
      case ServiceConnectionStatus_Destroyed: return "destroyed";
      default: return "invalid";
   }
   return "invalid";
}

std::ostream& operator<<(std::ostream& os, const ServiceConnectionStatusEvent& evt)
{
   cpc::string serverUri;
   cpc::vector<ServiceDescription> services;
   cpc::string statusDesc;
   CPCAPI2::JsonApi::AuthToken authToken;

   os << "ServiceConnectionStatusEvent serverUri: " << serverUri << " services: ";
   for (cpc::vector<ServiceDescription>::const_iterator it = services.begin(); it != services.end(); ++it)
   {
      os << "[region:" << it->region << "][" << it->service << "]";
   }

   os << " status: " << get_debug_string(evt.connectionStatus) << " statusDesc: " << evt.statusDesc;

   return os;
}

}

}

#endif // CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE
