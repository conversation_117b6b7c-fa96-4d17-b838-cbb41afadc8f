#pragma once

#if !defined(__CPCAPI2_CPIM_MESSAGE_H__)
#define __CPCAPI2_CPIM_MESSAGE_H__

#include "CpimHeader.h"

#include <list>
#include <string>

#include <rutil/Data.hxx>

namespace CPCAPI2
{
namespace CPM
{

typedef std::list<CpimHeader> HeaderList;

class CpimMessage
{
public:
   CpimMessage() {}
   CpimMessage(const HeaderList& headers, const HeaderList& contentHeaders, const resip::Data& content);

   bool hasHeader(const cpc::string& name) const;
   bool hasContentHeader(const cpc::string& name) const;
   HeaderList getHeaders() const { return headers; }
   HeaderList getContentHeaders() const { return contentHeaders; }
   resip::Data getContent() const { return content; }
   CpimHeader getHeader(const cpc::string& name) const;
   CpimHeader getContentHeader(const cpc::string& name) const;
   resip::Data toBytes() const;

   static CpimMessage parse(const resip::Data& data);

private:
   HeaderList headers;
   HeaderList contentHeaders;
   resip::Data content;

   static const resip::Data CRLF;
   static const resip::Data DOUBLE_CRLF;
};

}
}

#endif // __CPCAPI2_CPIM_MESSAGE_H__