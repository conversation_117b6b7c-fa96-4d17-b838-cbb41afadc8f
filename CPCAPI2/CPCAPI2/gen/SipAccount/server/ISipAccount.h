#include "interface/experimental/jsonrpc/StringSerialization.h"
#include "interface/experimental/jsonrpc/OptionalSerialization.h"
#include "interface/experimental/jsonrpc/VectorSerialization.h"
#include "../datatypes/SipAccountSettings.h"
#include "../datatypes/SipAccountNetworkTransport.h"

#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_ISIPACCOUNT_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_ISIPACCOUNT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
class ISipAccount
{
public:
    virtual int64_t create(const int64_t phoneHandle) = 0;

	virtual void destroy(const int64_t phoneHandle, const int64_t sipAccountHandle) = 0;

	virtual void configureDefaultAccountSettings(const int64_t phoneHandle, const int64_t sipAccountHandle, const SipAccountSettings& sipAccountSettings) = 0;

	virtual void configureTransportAccountSettings(const int64_t phoneHandle, const int64_t sipAccountHandle, const SipAccountSettings& sipAccountSettings, const SipAccountNetworkTransport transport) = 0;

	virtual void applySettings(const int64_t phoneHandle, const int64_t sipAccountHandle) = 0;

	virtual void enable(const int64_t phoneHandle, const int64_t sipAccountHandle) = 0;

	virtual void disable(const int64_t phoneHandle, const int64_t sipAccountHandle) = 0;
}; // class SipAccount
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_ISIPACCOUNT_H
