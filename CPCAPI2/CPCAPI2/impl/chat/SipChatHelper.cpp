#include "brand_branded.h"
#if (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)

#include "cpcapi2utils.h"
#include "SipChatHelper.h"

#include <assert.h>

namespace CPCAPI2
{
namespace SipChat
{

ChatEndReason SipChatHelper::getChatEndReason(resip::InviteSessionHandler::TerminatedReason reason)
{
   ChatEndReason endReason;

   switch (reason)
   {
   case resip::InviteSessionHandler::TerminatedReason::LocalBye:
   case resip::InviteSessionHandler::TerminatedReason::LocalCancel:
      endReason = ChatEndReason_UserTerminatedLocally;
      break;
   case resip::InviteSessionHandler::TerminatedReason::RemoteBye:
   case resip::InviteSessionHandler::TerminatedReason::RemoteCancel:
      endReason = ChatEndReason_UserTerminatedRemotely;
      break;
   case resip::InviteSessionHandler::TerminatedReason::Rejected:
   case resip::InviteSessionHandler::TerminatedReason::Error:
      endReason = ChatEndReason_Rejected;
      break;
   default:
      endReason = ChatEndReason_Unknown;
      break;
   }

   return endReason;
}

CpimMessage SipChatHelper::createCpimMessage(const cpc::string& messageId, const resip::Uri& fromUri, const resip::Uri& toUri, const IsComposingDocument& isComposingDocument)
{   
   HeaderList headers;

   // Set the From header
   cpc::string fromUriStr = fromUri.toString().c_str();
   CpimHeader fromHeader(CpimHeader::FROM, fromUriStr);
   headers.push_back(fromHeader);

   // Set the To header
   cpc::string toUriStr = toUri.toString().c_str();
   CpimHeader toHeader(CpimHeader::TO, toUriStr);
   headers.push_back(toHeader);

   // Set the IMDN NS header
   CpimHeader imdnNsHeader(CpimHeader::NS, IMDN_NS);
   headers.push_back(imdnNsHeader);

   // Set the IMDN Message-ID header
   CpimHeader imdnMessageIdHeader(CpimHeader::IMDN_MESSAGE_ID, messageId);
   headers.push_back(imdnMessageIdHeader);

   HeaderList contentHeaders;

   // Set the Content-Type header
   CpimHeader contentTypeHeader(CpimHeader::CONTENT_TYPE, contentTypeToString(IS_COMPOSING_CONTENT_TYPE));
   contentHeaders.push_back(contentTypeHeader);

   // Set the Content-Disposition header
   CpimHeader contentDispositionHeader(CpimHeader::CONTENT_DISPOSITION, "notification");
   contentHeaders.push_back(contentDispositionHeader);

   // Create the CPIM message
   cpc::string content = isComposingDocument.toString();
   resip::Data contentBytes = resip::Data(content.c_str());
   CpimMessage cpimMessage(headers, contentHeaders, contentBytes);
   return cpimMessage;
}

bool SipChatHelper::isIsComposingMessageNotification(CpimMessage cpimMessage)
{
   return stringToContentType(cpimMessage.getContentHeader(CpimHeader::CONTENT_TYPE).getValue()) == IS_COMPOSING_CONTENT_TYPE;
}

void SipChatHelper::extractCpimMessage(CpimMessage cpimMessage, cpc::string& messageId, IsComposingDocument& isComposingDocument)
{
   // Retrieve the IMDN Message-ID header
   CpimHeader imdnMessageIdHeader = cpimMessage.getHeader(CpimHeader::IMDN_MESSAGE_ID);
   messageId = imdnMessageIdHeader.getValue();

   // Retrieve the XML content
   resip::Data isComposingContentBytes = cpimMessage.getContent();
   cpc::string isComposingContent = isComposingContentBytes.c_str();
   IsComposingDocument *document = IsComposingDocument::parse(isComposingContent);
   
   if(document)
   {
      isComposingDocument = *document;
      delete document;
   }
}

resip::Data SipChatHelper::buildSdp(msrp_uri_t* localUri, UInt32 localMediaPort, const resip::Mime& cpimContentType, bool isActive)
{
   std::list<resip::Mime> additionalAcceptedWrappedTypes;
   additionalAcceptedWrappedTypes.push_back(IS_COMPOSING_CONTENT_TYPE);
   return CpmHelper::buildSdp(localUri, localMediaPort, cpimContentType, isActive, additionalAcceptedWrappedTypes);
}

}
}

#endif