#include "brand_branded.h"

#if (CPCAPI2_BRAND_IM_MODULE == 1)
#include "SipInstantMessageInterface.h"
#include "../phone/PhoneInterface.h"
#include "../account/SipAccountInterface.h"
#include "../cpm/CpmHelper.h"
#include "../iscomposing/IsComposingInfo.h"

#include "SipInstantMessageImpl.h"

using namespace CPCAPI2::SipAccount;

namespace CPCAPI2
{
namespace SipInstantMessage
{

SipInstantMessageInterface::SipInstantMessageInterface(Phone* cpcPhone) :
   EventSource2<EventHandler<SipInstantMessageHandler, CPCAPI2::SipAccount::SipAccountHandle> >(dynamic_cast<PhoneInterface*>(cpcPhone)),
   mPhone(dynamic_cast<PhoneInterface*>(cpcPhone)),
   mAccountIf(NULL),
   mNextOutgoingHandle(1),
   mInstantMessageMapPtr(new InstantMessageMap),
   mInstantMessageMap(*mInstantMessageMapPtr)
{
   mAccountIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(cpcPhone));
}

SipInstantMessageInterface::~SipInstantMessageInterface()
{
   mInstantMessageMap.clear();
}
   
void SipInstantMessageInterface::Release()
{
   delete this;
}

int SipInstantMessageInterface::setHandler(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipInstantMessageHandler* handler)
{
   resip::ReadCallbackBase* setHandlerCmd = resip::resip_bind(&SipInstantMessageInterface::setHandlerImpl, this, account, handler);
   
   if (handler == NULL)
   {
      // removing the handler involves two steps:
      // 1. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);

      // 2. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      executeOnSdkThread(setHandlerCmd);
   }
   else
   {
      postToSdkThread(setHandlerCmd);
   }

   return kSuccess;
}

int SipInstantMessageInterface::setHandlerImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipInstantMessageHandler* handler)
{
   if (SipAccountImpl* acct = mAccountIf->getAccountImpl(account))
   {
      if (mInstantMessageMap.find(account) == mInstantMessageMap.end())
      {
         SipInstantMessageImpl* im = new SipInstantMessageImpl(this, mInstantMessageMapPtr, mPhone, *acct);
         mInstantMessageMap[account] = im;

         auto it = mHandlers.find(account);
         if (mHandlers.end() != it)
         {
            removeAppHandler(it->second, account);
         }

         if (nullptr != handler)
         {
            mHandlers[account] = handler;
            addAppHandler(handler, account);
         }
         else
            mHandlers.erase(account);
      }
      else
      {
         bool handlerWasNull = true;
         auto it = mHandlers.find(account);
         if (mHandlers.end() != it)
         {
           removeAppHandler(it->second, account);
           handlerWasNull = false;
         }

         if (nullptr != handler)
         {
            mHandlers[account] = handler;
            addAppHandler(handler, account);
         }
         else
            mHandlers.erase(account);

         if (handler == NULL)
         {
            // avoid deleting the instance of SipInstantMessageImpl, to reduce need for cleanup logic.
            // instead, just un-register this as an account aware feature, to take effect on next
            // SipAccountImpl enable
            acct->unregisterAccountAwareFeature(mInstantMessageMap[account]);
         }
         else if (handlerWasNull)
         {
            acct->registerAccountAwareFeature(mInstantMessageMap[account]);
         }
      }
      return kSuccess;
   }
   else
   {
      mAccountIf->fireError("Invalid account handle for SipInstantMessage::setHandler");
      return kError;
   }
}

int SipInstantMessageInterface::acceptMimeType(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& mimeType)
{
   postToSdkThread(resip::resip_bind(&SipInstantMessageInterface::acceptMimeTypeImpl, this, account, mimeType));
   return kSuccess;
}

int SipInstantMessageInterface::acceptMimeTypeImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& mimeType)
{
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (!acct) 
   {
      mAccountIf->fireError("Invalid account handle for SipInstantMessageInterface::acceptMimeType");
      return kSuccess;
   }

   if (mInstantMessageMap.find(account) != mInstantMessageMap.end())
   {
      return mInstantMessageMap[account]->acceptMimeType(mimeType);
   }
   else
   {
      mAccountIf->fireError("SipInstantMessageInterface::acceptMimeType was called before handler was set for account: " + cpc::to_string(account));
      return kSuccess;
   }
}

int SipInstantMessageInterface::acceptMimeType(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const SipInstantMessageManager::MimeType mimeType)
{
   postToSdkThread(resip::resip_bind(&SipInstantMessageInterface::acceptMimeTypeEnumImpl, this, account, mimeType));
   return kSuccess;
}

int SipInstantMessageInterface::acceptMimeTypeEnumImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const SipInstantMessageManager::MimeType mimeType)
{
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (!acct) 
   {
      mAccountIf->fireError("Invalid account handle for SipInstantMessageInterface::acceptMimeTypeEnum");
      return kSuccess;
   }

   if (mInstantMessageMap.find(account) != mInstantMessageMap.end())
   {
      return mInstantMessageMap[account]->acceptMimeTypeEnum(mimeType);
   }
   else
   {
      mAccountIf->fireError("SipInstantMessageInterface::acceptMimeTypeEnum was called before handler was set for account: " + cpc::to_string(account));
      return kSuccess;
   }
}

int SipInstantMessageInterface::acceptIncoming(
      SipInstantMessageHandle im,
      unsigned int statusCode)
{
   postToSdkThread(resip::resip_bind(&SipInstantMessageInterface::acceptIncomingImpl, this, im, statusCode));
   return kSuccess;
}

int SipInstantMessageInterface::acceptIncomingImpl(
      SipInstantMessageHandle im,
      unsigned int /*statusCode*/)
{
   SipInstantMessageImpl* impl = getIM(im);
   if (impl == NULL)
   {
      cpc::string msg = cpc::string("SipInstantMessageInterface::acceptIncoming called with invalid im handle: ") + cpc::to_string(im);
      mAccountIf->fireError(msg);
      return kSuccess;
   }

   return impl->acceptIncoming(im);
}

int SipInstantMessageInterface::rejectIncoming(
      SipInstantMessageHandle im,
      unsigned int statusCode,
      const cpc::string& reasonText)
{
   postToSdkThread(resip::resip_bind(&SipInstantMessageInterface::rejectIncomingImpl, this, im, statusCode, reasonText));
   return kSuccess;
}

int SipInstantMessageInterface::rejectIncomingImpl(
      SipInstantMessageHandle im,
      unsigned int statusCode,
      const cpc::string& /*reasonText*/)
{
   SipInstantMessageImpl* impl = getIM(im);
   if (impl == NULL)
   {
      cpc::string msg = cpc::string("SipInstantMessageInterface::rejectIncoming called with invalid im handle: ") + cpc::to_string(im);
      mAccountIf->fireError(msg);
      return kSuccess;
   }

   return impl->rejectIncoming(im, statusCode);
}

int SipInstantMessageInterface::rejectIncomingMimeType(
      SipInstantMessageHandle im,
      const cpc::vector<cpc::string>& acceptMimeTypes)
{
   postToSdkThread(resip::resip_bind(&SipInstantMessageInterface::rejectIncomingMimeTypeImpl, this, im, acceptMimeTypes));
   return kSuccess;
}

int SipInstantMessageInterface::rejectIncomingMimeTypeImpl(
      SipInstantMessageHandle im,
      const cpc::vector<cpc::string>& acceptMimeTypes)
{
   SipInstantMessageImpl* impl = getIM(im);
   if (impl == NULL)
   {
      cpc::string msg = cpc::string("SipInstantMessageInterface::rejectIncomingMimeType called with invalid im handle: ") + cpc::to_string(im);
      mAccountIf->fireError(msg);
      return kSuccess;
   }

   return impl->rejectIncomingMimeType(im, acceptMimeTypes);
}

SipInstantMessageHandle SipInstantMessageInterface::sendMessage(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& targetAddress,
      const char* content,
      unsigned int contentLength,
      SipInstantMessageManager::MimeType mimeType,
      const cpc::string& messageType)
{
   SipInstantMessageHandle h = mNextOutgoingHandle++;
   sendMessage(account, h, targetAddress, content, contentLength, mimeType, messageType);
   return h;
}

void SipInstantMessageInterface::sendMessage(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipInstantMessageHandle h,
      const cpc::string& targetAddress,
      const char* content,
      unsigned int contentLength,
      SipInstantMessageManager::MimeType mimeType,
      const cpc::string& messageType)
{
   SipInstantMessageInterface::SendMessageInfo mi;
   mi.content = cpc::string(content, contentLength);
   mi.mimeType = mimeType;
   mi.messageType = messageType;
   postToSdkThread(resip::resip_bind(&SipInstantMessageInterface::sendMessageImpl,
      this, account, h, targetAddress, mi));
}

int SipInstantMessageInterface::sendMessageImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipInstantMessageHandle im,
      const cpc::string& targetAddress,
      SipInstantMessageInterface::SendMessageInfo mi)
{
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (!acct) 
   {
      mAccountIf->fireError("Invalid account handle for SipInstantMessageInterface::sendMessage");
      return kSuccess;
   }

   if (!acct->isEnabled())
   {
      mAccountIf->fireError("SipInstantMessageInterface::sendMessage was called before account enabled: " + cpc::to_string(account));
      return kSuccess;
   }

   InstantMessageMap::iterator itMM = mInstantMessageMap.find(account);
   if (itMM == mInstantMessageMap.end())
   {
      mAccountIf->fireError("SipInstantMessageInterface::sendMessage failed to find account in mInstantMessageMap");
      return kSuccess;
   }

   resip::Data data(mi.content.c_str(), mi.content.size());
   return itMM->second->sendOutgoingMessage(im, targetAddress, data, mi.mimeType, mi.messageType);
}

SipInstantMessageHandle SipInstantMessageInterface::sendMessage(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& targetAddress,
      const char* content,
      unsigned int contentLength,
      const cpc::string& customMimeType,
      const cpc::string& messageType)
{
   SipInstantMessageHandle h = mNextOutgoingHandle++;
   sendMessage(account, h, targetAddress, content, contentLength, customMimeType, messageType);
   return h;
}

void SipInstantMessageInterface::sendMessage(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipInstantMessageHandle imHandle,
      const cpc::string& targetAddress,
      const char* content,
      unsigned int contentLength,
      const cpc::string& customMimeType,
      const cpc::string& messageType)
{
   SipInstantMessageInterface::SendMessageInfo mi;
   mi.content = cpc::string(content, contentLength);
   mi.customMimeType = customMimeType;
   mi.messageType = messageType;
   postToSdkThread(resip::resip_bind(&SipInstantMessageInterface::sendMessageCustomImpl,
      this, account, imHandle, targetAddress, mi));
}

int SipInstantMessageInterface::sendMessageCustomImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipInstantMessageHandle im,
      const cpc::string& targetAddress,
      SipInstantMessageInterface::SendMessageInfo mi)
{
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (!acct) 
   {
      mAccountIf->fireError("Invalid account handle for SipInstantMessageInterface::sendMessage");
      return kSuccess;
   }

   if (!acct->isEnabled())
   {
      mAccountIf->fireError("SipInstantMessageInterface::sendMessage was called before account enabled: " + cpc::to_string(account));
      return kSuccess;
   }

   resip::Data data(mi.content.c_str(), mi.content.size());
   assert(mInstantMessageMap.find(account) != mInstantMessageMap.end());
   return mInstantMessageMap[account]->sendOutgoingMessage(im, targetAddress, data, mi.customMimeType, mi.messageType);
}

SipInstantMessageImpl* SipInstantMessageInterface::getIM(SipInstantMessageHandle h) const
{
   for (InstantMessageMap::iterator it = mInstantMessageMap.begin(); it != mInstantMessageMap.end(); ++it)
   {
      if (it->second->hasIncomingInstantMessage(h)) return it->second;
   }
   return NULL;
}

int SipInstantMessageInterface::setIsComposingMessage(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& targetAddress,
      MimeType mimeType, 
      struct std::tm* datetime, 
      int refreshInterval,
      int idleInterval)
{
   // Set the date/time if needed
   tm datetimeVal = datetime ? *datetime : CpmHelper::getCurrentDateTime();

   postToSdkThread(resip::resip_bind(&SipInstantMessageInterface::setIsComposingMessageImpl, this, account, targetAddress, mimeType, datetimeVal, refreshInterval, idleInterval));
   return kSuccess;
}

int SipInstantMessageInterface::setIsComposingMessageImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& targetAddress,
      MimeType mimeType, 
      struct std::tm datetime,
      int refreshInterval,
      int idleInterval)
{
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (!acct) 
   {
      mAccountIf->fireError("Invalid account handle for SipInstantMessageInterface::setIsComposingMessage");
      return kSuccess;
   }

   if (!acct->isEnabled())
   {
      mAccountIf->fireError("SipInstantMessageInterface::setIsComposingMessage was called before account enabled: " + cpc::to_string(account));
      return kSuccess;
   }

   // Send the notification
   assert(mInstantMessageMap.find(account) != mInstantMessageMap.end());
   return mInstantMessageMap[account]->setIsComposingMessage(targetAddress, mimeType, datetime, refreshInterval, idleInterval);
}

int SipInstantMessageInterface::setIsComposingMessage(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& targetAddress,
      const cpc::string& customMimeType, 
      struct std::tm* datetime, 
      int refreshInterval,
      int idleInterval)
{
   // Set the date/time if needed
   tm datetimeVal = datetime ? *datetime : CpmHelper::getCurrentDateTime();

   postToSdkThread(resip::resip_bind(&SipInstantMessageInterface::setIsComposingMessageCustomImpl, this, account, targetAddress, customMimeType, datetimeVal, refreshInterval, idleInterval));
   return kSuccess;
}

int SipInstantMessageInterface::setIsComposingMessageCustomImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& targetAddress,
      const cpc::string& customMimeType, 
      struct std::tm datetime,
      int refreshInterval,
      int idleInterval)
{
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (!acct) 
   {
      mAccountIf->fireError("Invalid account handle for SipInstantMessageInterface::setIsComposingMessage");
      return kSuccess;
   }

   if (!acct->isEnabled())
   {
      mAccountIf->fireError("SipInstantMessageInterface::setIsComposingMessage was called before account enabled: " + cpc::to_string(account));
      return kSuccess;
   }

   // Send the notification
   assert(mInstantMessageMap.find(account) != mInstantMessageMap.end());
   return mInstantMessageMap[account]->setIsComposingMessage(targetAddress, customMimeType, datetime, refreshInterval, idleInterval);
}

std::ostream& operator<<(std::ostream& os, const SipIncomingInstantMessageEvent& evt)
{
   return os << "SipIncomingInstantMessageEvent";
}

std::ostream& operator<<(std::ostream& os, const SipOutgoingInstantMessageEvent& evt)
{
   return os << "SipOutgoingInstantMessageEvent";
}

std::ostream& operator<<(std::ostream& os, const IsComposingMessageEvent& evt)
{
   return os << "IsComposingMessageEvent";
}

std::ostream& operator<<(std::ostream& os, const SetIsComposingMessageSuccessEvent& evt)
{
   return os << "SetIsComposingMessageSuccessEvent";
}

std::ostream& operator<<(std::ostream& os, const SetIsComposingMessageFailureEvent& evt)
{
   return os << "SetIsComposingMessageFailureEvent";
}

std::ostream& operator<<(std::ostream& os, const SipInstantMessage::ErrorEvent& evt)
{
   return os << "SipInstantMessage::ErrorEvent";
}

}
}
#endif // CPCAPI2_BRAND_IM_MODULE
