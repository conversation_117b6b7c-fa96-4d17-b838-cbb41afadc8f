// bliu: support Presence (section .6.1.)
// bliu: support account based cache
// bliu: handle the following cases in getContactCapabilityStatus:
//    case 1) the app has called addUser, but the OPTIONS exchange isn't complete yet
//    case ) the OPTIONS exchange resulted in a 404
//    case 3) addContact was never called (presumably this is a problem with the app layer logic)
// bliu: need timeout in RcsCapabilityStatus

// bliu: default synch after import
// bliu: which to reset timeout, synch or import?
// bliu: ignore already existing status update?
// bliu: args without targetAddress parameter
// bliu: give priority to manually added request?
// bliu: replace isSuccess with signalingStatusCode
// bliu: multiple account are not sharing the same mQueue
// bliu: some request may stuck in mQueue forever, which may cause the others to be in MaxRequestTimeout as well (no good)
// bliu: there are only two ways to add contacts to mQueue: call requestContactCapabilityStatus() or wait for timeout
// bliu: remove then re-add a contact which is under request should give correct response
// bliu: however, this creates a loophole that keeping doing the above would create a lot of requests
// bliu: invalid targetAddress will be removed from mTimeout
// bliu: non-registered targetAddress returns 479
// bliu: RcsCapabilityDiscoverySettings.defaultDisc and .capDiscCommonStack are not yet used?
// bliu: Orange configuration has presenceDisc parameter where the spec doesn't?
// bliu: need to adopt pollingPeriod and pollingRatePeriod

#include "brand_branded.h"

#if (CPCAPI2_BRAND_CAPABILITY_DISCOVERY_MODULE == 1)
#include "RcsCapabilityDiscoveryImpl.h"

#include <chrono>
#include <algorithm>

#include "../account/SipAccountInterface.h" // for mAccountIf

#include "../provision/RcsProvisionImpl.h" // for _ProvisionInfoMap

using namespace resip;

using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::RcsCapabilityDiscovery;

namespace CPCAPI2
{

namespace RcsCapabilityDiscovery
{

static const unsigned int DefaultPollingRate = 1;
static const unsigned int DefaultCapInfoExpiryMs = 300000;
static const unsigned int MinRequestIntervalMs = 10000;

const unsigned int RcsCapabilityDiscoveryInterface::sTimeoutIntervalMs = 1000;

RcsCapabilityDiscoveryImpl::RcsCapabilityDiscoveryImpl(std::shared_ptr<CapabilityDiscoveryMap> parentMap, SipAccountImpl& account, RcsCapabilityDiscovery::RcsCapabilityDiscoveryInterface& intf) :
   mAccount(account),
   mInterface(intf),
   mRcsCapabilityDiscoveryInternal(account),
   mNextOutgoingHandle(1),
   mParentMap(parentMap)
{
   mRcsCapabilityDiscoveryInternal.setHandler(this);

   updateSettings(mSettings);
}

RcsCapabilityDiscoveryImpl::~RcsCapabilityDiscoveryImpl()
{
}

int RcsCapabilityDiscoveryImpl::initialize()
{
   return kSuccess;
}

int RcsCapabilityDiscoveryImpl::addContact(
   const cpc::string& address,
   const RcsCapabilityDiscoveryInterface::time_point& lastUpdated)
{
   RcsCapabilityDiscoveryInterface::time_point now = std::chrono::system_clock::now();
   unsigned int elapsedSinceLastUpdatedMs = now > lastUpdated ? (unsigned int)std::chrono::duration_cast<std::chrono::milliseconds>(now - lastUpdated).count() : 0;
   bool inserted = mTimeout.insert(std::make_pair(address, elapsedSinceLastUpdatedMs > (unsigned int)mSettings.capInfoExpiryMs ? mSettings.capInfoExpiryMs : elapsedSinceLastUpdatedMs)).second; // force update
   assert(inserted); // must not exist

   requestContactCapabilityStatus(address, false);

   return kSuccess;
}

int RcsCapabilityDiscoveryImpl::removeContact(
   const cpc::string& address)
{
   // only remove an item from mQueue if the request hasn't been sent out yet
   // do NOT call removeFromQueue() here
   if (mQueue.size() > (size_t)mSettings.pollingRate)
   {
      mQueue.erase(std::remove(mQueue.begin() + mSettings.pollingRate, mQueue.end(), address), mQueue.end());
   }

   TimeoutMap::size_type n = mTimeout.erase(address);
   assert(n == 1); // must exist

   return kSuccess;
}

int RcsCapabilityDiscoveryImpl::requestContactCapabilityStatus(
   const cpc::string& targetAddress,
   bool immediate)
{
   TimeoutMap::iterator it = mTimeout.find(targetAddress);
   assert(it != mTimeout.end());

   if (!immediate && it->second < (unsigned int)mSettings.capInfoExpiryMs) return kSuccess; // no forced update
   if (immediate && it->second < MinRequestIntervalMs) return kSuccess; // avoid of rapid request

   if (std::find(mQueue.begin(), mQueue.end(), targetAddress) != mQueue.end()) return kSuccess; // already in the queue

   if (mQueue.size() < (size_t)mSettings.pollingRate)
   {
      // the account may not be ready yet, don't put into mQueue
      if (!mAccount.isEnabled()) return kSuccess;

      // other SDK error reported, remove the contact entirely
      if (mRcsCapabilityDiscoveryInternal.sendCapabilityRequest(
            mNextOutgoingHandle++, targetAddress, mMyCapabilities) == kError)
      {
         mTimeout.erase(it);
         return kSuccess;
      }
   }

   mQueue.push_back(targetAddress);

   return kSuccess;
}

int RcsCapabilityDiscoveryImpl::setMyCapabilities(
   const RcsCapabilitySet& caps)
{
   mMyCapabilities = caps;

   for (TimeoutMap::iterator it = mTimeout.begin(); it != mTimeout.end(); ++it)
   {
      TimeoutMap::value_type& item = *it;
      item.second = mSettings.capInfoExpiryMs; // force update
   }

   return kSuccess;
}

int RcsCapabilityDiscoveryImpl::updateSettings(
   const RcsCapabilityDiscoverySettings& settings)
{
   mSettings = settings;

   if (mSettings.pollingPeriodMs <= 0)
   {
      mSettings.pollingRatePeriodMs = 0;
      mSettings.pollingRate = 0;
      mSettings.capInfoExpiryMs = 0;
   }

   if (mSettings.defaultDisc != 1)
   {
      mSettings.capDiscCommonStack = 0;
   }

   if (mSettings.pollingRate <= 0)
   {
      mSettings.pollingRate = DefaultPollingRate;
      assert(mSettings.pollingRate > 0);
   }

   if (mSettings.capInfoExpiryMs <= 0)
   {
      mSettings.capInfoExpiryMs = DefaultCapInfoExpiryMs;
      assert(mSettings.capInfoExpiryMs > 0);
   }

   return kSuccess;
}

int RcsCapabilityDiscoveryImpl::onTimer()
{
   for (TimeoutMap::iterator it = mTimeout.begin(); it != mTimeout.end(); ++it)
   {
      TimeoutMap::value_type& item = *it;
      item.second += RcsCapabilityDiscoveryInterface::sTimeoutIntervalMs; // update timeout

      if (item.second < (unsigned int)mSettings.capInfoExpiryMs) continue;

      requestContactCapabilityStatus(item.first, false);
   }

   // in case something is stuck in mQueue (never receives response), restart it
   // this process must come after the above refreshing timer to yield precedence to the new requests
   std::deque<cpc::string> copy = mQueue;

   for (size_t i = 0; i < std::min<size_t>(copy.size(), (size_t)mSettings.pollingRate); ++i)
   {
      TimeoutMap::iterator it = mTimeout.find(copy[i]);

      if (it == mTimeout.end()) // the item has been removed from mQueue
      {
         removeFromQueue(copy[i]);
         continue;
      }

      if (it->second >= (unsigned int)mSettings.capInfoExpiryMs + 10000)
      {
         removeFromQueue(copy[i]);
         it->second = mSettings.capInfoExpiryMs; // avoid overflow and allow its timer to grow upto capInfoExpiryMs + 10000
         requestContactCapabilityStatus(copy[i], false);
      }
   }

   return kSuccess;
}

int RcsCapabilityDiscoveryImpl::onIncomingCapabilityDiscovery(CPCAPI2::SipAccount::SipAccountHandle account, const RcsIncomingCapabilityDiscoveryEvent& args)
{
   assert(mAccount.getHandle() == account);

   // reply to the incoming request
   mRcsCapabilityDiscoveryInternal.acceptIncoming(args.capability, 200, mMyCapabilities);

   const cpc::string& address = args.from.address;

   TimeoutMap::iterator it = mTimeout.find(address);

   // not in the contact list, don't update the cache
   if (it == mTimeout.end()) // the item has been removed from mQueue
   {
      removeFromQueue(address);
      //mRcsCapabilityDiscoveryImpl.rejectIncoming(args.capability, 404); // NOT FOUND
      return kSuccess;
   }

   // bliu: remote caps haven't been changed at all

   RcsOnContactCapabilityStatusChangedEvent evt;
   evt.signalingStatusCode = 200; // indicate success
   evt.status.targetAddress = address;
   evt.status.caps = args.caps;
   evt.status.isRcsCapable = !evt.status.caps.empty();

   // update cache
   {
      resip::Lock locker (mInterface.mCacheLock);

      RcsCapabilityDiscoveryInterface::CacheMap::mapped_type::iterator it = mInterface.mCache[account].find(address);
      if (it != mInterface.mCache[account].end())
      {
         RcsCapabilityDiscoveryInterface::RcsCapabilityStatusEx& status = it->second;
         static_cast<RcsCapabilityStatus&>(status) = evt.status;
         status.signalingStatusCode = evt.signalingStatusCode;
         status.lastUpdated = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());

         // only fire event if the address exists in mCache
         mInterface.fireEvent(cpcEvent(RcsCapabilityDiscoveryHandler, onContactCapabilityStatusChanged), account, evt);
      }
   }

   assert(mTimeout.find(address) != mTimeout.end());
   mTimeout[address] = 0; // reset timeout

   removeFromQueue(address);

   return kSuccess;
}

int RcsCapabilityDiscoveryImpl::onOutgoingCapabilityDiscoverySuccess(CPCAPI2::SipAccount::SipAccountHandle account, const RcsOutgoingCapabilityDiscoveryEvent& args)
{
   assert(mAccount.getHandle() == account);

   const cpc::string& address = args.to.address;

   TimeoutMap::iterator it = mTimeout.find(address);

   if (it == mTimeout.end()) // the item has been removed from mQueue
   {
      removeFromQueue(address);
      return kSuccess; // not in the contact list
   }

   // bliu: remote caps haven't been changed at all

   RcsOnContactCapabilityStatusChangedEvent evt;
   evt.signalingStatusCode = args.signalingStatusCode; // indicate success
   evt.status.targetAddress = address;
   evt.status.caps = args.caps;
   evt.status.isRcsCapable = !evt.status.caps.empty();

   // update cache
   {
      resip::Lock locker (mInterface.mCacheLock);

      RcsCapabilityDiscoveryInterface::CacheMap::mapped_type::iterator it = mInterface.mCache[account].find(address);
      if (it != mInterface.mCache[account].end())
      {
         RcsCapabilityDiscoveryInterface::RcsCapabilityStatusEx& status = it->second;
         static_cast<RcsCapabilityStatus&>(status) = evt.status;
         status.signalingStatusCode = evt.signalingStatusCode;
         status.lastUpdated = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());

         // only fire event if the address exists in mCache
         mInterface.fireEvent(cpcEvent(RcsCapabilityDiscoveryHandler, onContactCapabilityStatusChanged), account, evt);
      }
   }

   assert(mTimeout.find(address) != mTimeout.end());
   mTimeout[address] = 0; // reset timeout

   removeFromQueue(address);

   return kSuccess;
}

int RcsCapabilityDiscoveryImpl::onOutgoingCapabilityDiscoveryFailure(CPCAPI2::SipAccount::SipAccountHandle account, const RcsOutgoingCapabilityDiscoveryEvent& args)
{
   assert(mAccount.getHandle() == account);

   const cpc::string& address = args.to.address;

   TimeoutMap::iterator it = mTimeout.find(address);

   if (it == mTimeout.end()) // the item has been removed from mQueue
   {
      removeFromQueue(address);
      return kSuccess; // not in the contact list
   }

   // bliu: remote caps haven't been changed at all

   RcsOnContactCapabilityStatusChangedEvent evt;
   evt.signalingStatusCode = args.signalingStatusCode; // indicate success
   evt.status.targetAddress = address;
   evt.status.isRcsCapable = false;

   // update cache
   {
      resip::Lock locker (mInterface.mCacheLock);

      RcsCapabilityDiscoveryInterface::CacheMap::mapped_type::iterator it = mInterface.mCache[account].find(address);
      if (it != mInterface.mCache[account].end())
      {
         RcsCapabilityDiscoveryInterface::RcsCapabilityStatusEx& status = it->second;

         if (args.signalingStatusCode == 408 || args.signalingStatusCode == 480)
         {
            evt.status = status; // OBELISK-245: response code of 408 or 480 requires the status not to be changed
            evt.status.caps.clear();

            if (mSettings.isIMCapAlwaysON && std::find(status.caps.begin(), status.caps.end(), RcsCapability::Chat) != status.caps.end())
            {
               if (std::find(evt.status.caps.begin(), evt.status.caps.end(), RcsCapability::Chat) == evt.status.caps.end()) evt.status.caps.push_back(RcsCapability::Chat);
            }
         }

         static_cast<RcsCapabilityStatus&>(status) = evt.status;
         status.signalingStatusCode = evt.signalingStatusCode;
         status.lastUpdated = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());

         // only fire event if the address exists in mCache
         mInterface.fireEvent(cpcEvent(RcsCapabilityDiscoveryHandler, onContactCapabilityStatusChanged), account, evt);
      }
   }

   assert(mTimeout.find(address) != mTimeout.end());
   mTimeout[address] = 0; // reset timeout

   removeFromQueue(address);

   return kSuccess;
}

int RcsCapabilityDiscoveryImpl::onError(CPCAPI2::SipAccount::SipAccountHandle account, const ErrorEvent& args)
{
   return kSuccess;
}

int RcsCapabilityDiscoveryImpl::onReleased()
{
   if (std::shared_ptr<CapabilityDiscoveryMap> parentMap = mParentMap.lock())
   {
      parentMap->erase(mAccount.getHandle());
   }
   delete this;
   return kSuccess;
}

void RcsCapabilityDiscoveryImpl::removeFromQueue(const cpc::string& address)
{
   assert(std::count(mQueue.begin(), mQueue.end(), address) <= 1);

   std::deque<cpc::string>::iterator it = std::find(mQueue.begin(), mQueue.end(), address);

   // incoming and outgoing update are both possible to remove an existing item in mQueue
   // so it's possible that an address has already been removed from mQueue previously
   if (it == mQueue.end()) return;

   // if the contact is in the requested portion and there is other contact to be requested
   if (it < mQueue.begin() + mSettings.pollingRate && mQueue.size() > (size_t)mSettings.pollingRate)
   {
      mRcsCapabilityDiscoveryInternal.sendCapabilityRequest(mNextOutgoingHandle++, mQueue[mSettings.pollingRate], mMyCapabilities);
   }

   mQueue.erase(it);
}

}
}
#endif // CPCAPI2_CAPABILITY_DISCOVERY_MODULE
