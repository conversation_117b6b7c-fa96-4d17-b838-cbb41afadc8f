#pragma once

#if !defined(CPCAPI2_GENBAND_REST_API_H)
#define CPCAPI2_GENBAND_REST_API_H

#include "cpcapi2defs.h"
#include "event/SipEventState.h"
#include "event/SipEventSubscriptionHandler.h"
#include "event/SipEventManager.h"
#include "genband/GenbandRestAPIInterface.h"
#include "../phone/PhoneModule.h"
#include "../account/SipAccountInterface.h"
#include "../event/SipEventManagerInterface.h"
#include "../event/SipEventSubscriptionCreationInfo.h"
#include "WebSocketClientManager.h"
#include "GenbandRestAPIEventHandler.h"

#include "curl/curl.h"

namespace CPCAPI2
{
namespace Genband
{

struct PutData
{
	char* data;
	size_t len;
};
//class GenbandRestAPIEventHandler;

class GenbandRestAPI : public GenbandRestAPIInterface,
					   public PhoneModule
{
public:
	GenbandRestAPI(Phone* phone);
	~GenbandRestAPI(){};
	virtual void Release() OVERRIDE;
	/**
   * Set the handler.
   */
	virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::Genband::GenbandRestAPIHandler* handler) OVERRIDE;

	GenbandResult makeCall(GenbandHeader header, GenbandBody body) OVERRIDE; // - Make a call
	GenbandResult answerCall(GenbandHeader header, GenbandBody body) OVERRIDE; // - Answer a call
	GenbandResult rejectCall(GenbandHeader header, GenbandBody body) OVERRIDE; // - Reject or forward a call
	GenbandResult forwardCall(GenbandHeader header, GenbandBody body) OVERRIDE; //
	GenbandResult placeOnHold(GenbandHeader header, GenbandBody body) OVERRIDE; // - Place a call on hold
	GenbandResult endCall(GenbandHeader header, GenbandBody body) OVERRIDE; //- End a call
	GenbandResult conferenceCall(GenbandHeader header, GenbandBody body) OVERRIDE; // Conference in calls
	GenbandResult subscribe(GenbandHeader header, GenbandBody body, CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE; // - Subscribe
	GenbandResult extendSubscribtion(GenbandHeader header, GenbandBody body) OVERRIDE; // - Extend subscribtion
	GenbandResult unsubcribe(GenbandHeader header, GenbandBody body, CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE; // - Unsubscribe

private:
	int setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::Genband::GenbandRestAPIHandler* handler);
	curl_slist* applyHeaders(GenbandHeader headers);
	GenbandResult genbandPost(GenbandHeader header, GenbandBody body);
	GenbandResult genbandPut(GenbandHeader header, GenbandBody body);
	GenbandResult genbandDelete(GenbandHeader header, GenbandBody body);

	PhoneInterface* mPhone;
	WebSocketClientManager webSocketClientManager;

	CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;
	typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, CPCAPI2::Genband::GenbandRestAPIEventHandler*> AccountMap;
    AccountMap mAccountMap;
    CPCAPI2::SipEvent::SipEventManagerInterface* mSipEventIf;

};

} // namespace Genband
} // namespace CPCAPI2
#endif // CPCAPI2_GENBAND_REST_API_H
