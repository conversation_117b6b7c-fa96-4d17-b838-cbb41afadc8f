#pragma once

#if !defined(__CPCAPI2_SIP_SIMPLE_MESSAGE_PROCESSOR_H__)
#define __CPCAPI2_SIP_SIMPLE_MESSAGE_PROCESSOR_H__

#include "../account/SipAccountImpl.h"
#include "../account/SipAccountAwareFeature.h"
#include "../account/CPInviteHandlerSelectorDialogSet.h"
#include "../account/CPPagerMessageHandlerDelegate.h"

#include <resip/dum/InviteSessionHandler.hxx>

using resip::InviteSession;
using resip::InviteSessionHandle;
using resip::ClientInviteSessionHandle;
using resip::ServerInviteSessionHandle;
using resip::SipMessage;
using resip::SdpContents;
using resip::Contents;
using resip::ClientSubscriptionHandle;
using resip::ServerSubscriptionHandle;

namespace CPCAPI2
{

namespace SipChat
{

/*
 * This class is needed until we have a separate service that processes SIP/SIMPLE 
 * related SIP messages (SIP INVITEs and SIP MESSAGEs).
 *
 * NewPace server sends this type of message occasionally and we don't want to
 * leave these messages unreplied.
 */
class SipSimpleMessageProcessor : public CPCAPI2::SipAccount::SipAccountAwareFeature,
                                  public CPCAPI2::SipAccount::CPPagerMessageHandlerDelegate,
                                  public resip::InviteSessionHandler
{
public:
   SipSimpleMessageProcessor();
   ~SipSimpleMessageProcessor();

private:
   resip::SharedPtr<resip::DialogUsageManager> dum;

   // SipAccountAwareFeature interface
   int registerSdkDialogSetFactory(CPCAPI2::SipAccount::AppDialogSetFactory& factory) OVERRIDE;
   virtual int registerSdkPagerMessageHandler(CPCAPI2::SipAccount::CPPagerMessageHandler& pagerMessageHandler) OVERRIDE;
   virtual int adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile) OVERRIDE;
   virtual int addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig) OVERRIDE;
   virtual int onDumBeingDestroyed() OVERRIDE;
   virtual void release() OVERRIDE {}

   // InviteSessionHandler interface
   virtual void onNewSession(ClientInviteSessionHandle, InviteSession::OfferAnswerType oat, const SipMessage& msg) OVERRIDE {}
   virtual void onNewSession(ServerInviteSessionHandle, InviteSession::OfferAnswerType oat, const SipMessage& msg) OVERRIDE;
   virtual void onTerminated(InviteSessionHandle, InviteSessionHandler::TerminatedReason reason, const SipMessage* related=0) OVERRIDE {}
   virtual void onAnswer(InviteSessionHandle, const SipMessage& msg, const SdpContents&) OVERRIDE {}
   virtual void onOffer(InviteSessionHandle, const SipMessage& msg, const SdpContents&) OVERRIDE {}
   virtual void onOfferRequired(InviteSessionHandle, const SipMessage& msg) OVERRIDE {}
   virtual void onConnected(ClientInviteSessionHandle, const SipMessage& msg) OVERRIDE {}
   virtual void onConnected(InviteSessionHandle, const SipMessage& msg) OVERRIDE {}
   virtual void onForkDestroyed(ClientInviteSessionHandle) OVERRIDE {}
   virtual void onRedirected(ClientInviteSessionHandle, const SipMessage& msg) OVERRIDE {}
   virtual void onFailure(ClientInviteSessionHandle, const SipMessage& msg) OVERRIDE {}
   virtual void onEarlyMedia(ClientInviteSessionHandle, const SipMessage&, const SdpContents&) OVERRIDE {}
   virtual void onProvisional(ClientInviteSessionHandle, const SipMessage&) OVERRIDE {}
   virtual void onOfferRejected(InviteSessionHandle, const SipMessage* msg) OVERRIDE {}
   virtual void onInfo(InviteSessionHandle, const SipMessage& msg) OVERRIDE {}
   virtual void onInfoSuccess(InviteSessionHandle, const SipMessage& msg) OVERRIDE {}
   virtual void onInfoFailure(InviteSessionHandle, const SipMessage& msg) OVERRIDE {}
   virtual void onMessage(InviteSessionHandle, const SipMessage& msg) OVERRIDE {}
   virtual void onMessageSuccess(InviteSessionHandle, const SipMessage& msg) OVERRIDE {}
   virtual void onMessageFailure(InviteSessionHandle, const SipMessage& msg) OVERRIDE {}
   virtual void onRefer(InviteSessionHandle, ServerSubscriptionHandle, const SipMessage& msg) OVERRIDE {}
   virtual void onReferNoSub(InviteSessionHandle, const SipMessage& msg) OVERRIDE {}
   virtual void onReferRejected(InviteSessionHandle, const SipMessage& msg) OVERRIDE {}
   virtual void onReferAccepted(InviteSessionHandle, ClientSubscriptionHandle, const SipMessage& msg) OVERRIDE {}

   // CPPagerMessageHandlerDelegate interface
   virtual void onMessageArrived(resip::ServerPagerMessageHandle h, const resip::SipMessage& message) OVERRIDE;
   virtual void onSuccess(resip::ClientPagerMessageHandle h, const resip::SipMessage& status) OVERRIDE {}
   virtual void onFailure(resip::ClientPagerMessageHandle h, const resip::SipMessage& status, std::unique_ptr<resip::Contents> contents) OVERRIDE {}
   virtual bool isMyMessage(const resip::SipMessage& msg) OVERRIDE;
   virtual bool isMyResponse(resip::ClientPagerMessageHandle h, const resip::SipMessage& msg) OVERRIDE { return false; }
};

}
}

#endif // __CPCAPI2_SIP_SIMPLE_MESSAGE_PROCESSOR_H__
