#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_CONVERSATIONENDREASON_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_CONVERSATIONENDREASON_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  enum class ConversationEndReason
  {
    Unknown,
    UserTerminatedLocally,
    UserTerminatedRemotely,
    ServerError,
    ServerRejected,
    Redirected,
    CallAnsweredElsewhere
  };

  inline std::string to_string(ConversationEndReason value)
  {
    switch (value)
    {
      case ConversationEndReason::Unknown:
        return "Unknown";
      case ConversationEndReason::UserTerminatedLocally:
        return "UserTerminatedLocally";
      case ConversationEndReason::UserTerminatedRemotely:
        return "UserTerminatedRemotely";
      case ConversationEndReason::ServerError:
        return "ServerError";
      case ConversationEndReason::ServerRejected:
        return "ServerRejected";
      case ConversationEndReason::Redirected:
        return "Redirected";
      case ConversationEndReason::CallAnsweredElsewhere:
        return "CallAnsweredElsewhere";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, ConversationEndReason value)
  {
    switch (value)
    {
      case ConversationEndReason::Unknown:
        os << "Unknown";
        break;
      case ConversationEndReason::UserTerminatedLocally:
        os << "UserTerminatedLocally";
        break;
      case ConversationEndReason::UserTerminatedRemotely:
        os << "UserTerminatedRemotely";
        break;
      case ConversationEndReason::ServerError:
        os << "ServerError";
        break;
      case ConversationEndReason::ServerRejected:
        os << "ServerRejected";
        break;
      case ConversationEndReason::Redirected:
        os << "Redirected";
        break;
      case ConversationEndReason::CallAnsweredElsewhere:
        os << "CallAnsweredElsewhere";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const ConversationEndReason& e)
  {
    switch (e)
    {
      case ConversationEndReason::Unknown:
        j = "Unknown";
        break;
      case ConversationEndReason::UserTerminatedLocally:
        j = "UserTerminatedLocally";
        break;
      case ConversationEndReason::UserTerminatedRemotely:
        j = "UserTerminatedRemotely";
        break;
      case ConversationEndReason::ServerError:
        j = "ServerError";
        break;
      case ConversationEndReason::ServerRejected:
        j = "ServerRejected";
        break;
      case ConversationEndReason::Redirected:
        j = "Redirected";
        break;
      case ConversationEndReason::CallAnsweredElsewhere:
        j = "CallAnsweredElsewhere";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, ConversationEndReason& e)
  {
      if (0 == j.get<std::string>().compare("Unknown"))
    {
      e = ConversationEndReason::Unknown;
    }
    else if (0 == j.get<std::string>().compare("UserTerminatedLocally"))
    {
      e = ConversationEndReason::UserTerminatedLocally;
    }
    else if (0 == j.get<std::string>().compare("UserTerminatedRemotely"))
    {
      e = ConversationEndReason::UserTerminatedRemotely;
    }
    else if (0 == j.get<std::string>().compare("ServerError"))
    {
      e = ConversationEndReason::ServerError;
    }
    else if (0 == j.get<std::string>().compare("ServerRejected"))
    {
      e = ConversationEndReason::ServerRejected;
    }
    else if (0 == j.get<std::string>().compare("Redirected"))
    {
      e = ConversationEndReason::Redirected;
    }
    else if (0 == j.get<std::string>().compare("CallAnsweredElsewhere"))
    {
      e = ConversationEndReason::CallAnsweredElsewhere;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum ConversationEndReason"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_CONVERSATIONENDREASON_H
