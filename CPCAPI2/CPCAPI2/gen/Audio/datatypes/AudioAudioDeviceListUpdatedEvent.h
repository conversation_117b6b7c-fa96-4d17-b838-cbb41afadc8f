#include "jsonrpc/StringSerialization.h"
#include "jsonrpc/OptionalSerialization.h"
#include <any>
#include "jsonrpc/VectorSerialization.h"
#include <nlohmann/json.hpp>
#include "AudioAudioDeviceInfo.h"

#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIODEVICELISTUPDATEDEVENT_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIODEVICELISTUPDATEDEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
  class AudioAudioDeviceListUpdatedEvent
  {
  public:
    std::optional<cpc::vector<AudioAudioDeviceInfo>> deviceInfo;

    AudioAudioDeviceListUpdatedEvent() = default;

    AudioAudioDeviceListUpdatedEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const AudioAudioDeviceListUpdatedEvent& o)
    {
      j = nlohmann::json::object();
       if (o.deviceInfo) { j["deviceInfo"] = *(o.deviceInfo); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, AudioAudioDeviceListUpdatedEvent& o)
    {
     if (j.contains("deviceInfo")) { o.deviceInfo = j.at("deviceInfo"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIODEVICELISTUPDATEDEVENT_H
