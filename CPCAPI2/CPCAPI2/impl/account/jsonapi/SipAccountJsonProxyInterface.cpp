#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACCOUNT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "SipAccountJsonProxyInterface.h"
#include "account/SipAccountInterface.h"
#include "account/SipAccountJsonProxyStateHandler.h"
#include "phone/PhoneInterface.h"
#include "json/JsonHelper.h"
#include "jsonapi/JsonApiClient.h"
#include "jsonapi/JsonApiClientInterface.h"
#include "util/cpc_logger.h"

#include <rutil/Reactor.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_ACCOUNT
#define JSON_MODULE "SipAccountJsonApi"

namespace CPCAPI2
{
namespace SipAccount
{
SipAccountJsonProxyInterface::SipAccountJsonProxyInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone)), mTransport(NULL), mServerCreatedHandle(-1), mStateHandler(NULL)
{
   mFunctionMap["createResult"] = std::bind(&SipAccountJsonProxyInterface::handleCreateResult, this, std::placeholders::_1);
   mFunctionMap["onAccountStatusChanged"] = std::bind(&SipAccountJsonProxyInterface::handleAccountStatusChanged, this, std::placeholders::_1);
   mFunctionMap["onAccountState"] = std::bind(&SipAccountJsonProxyInterface::handleAccountState, this, std::placeholders::_1);
   JsonApi::JsonApiClientInterface* jsonApiClientIf = dynamic_cast<JsonApi::JsonApiClientInterface*>(JsonApi::JsonApiClient::getInterface(phone));
   setTransport(jsonApiClientIf->getTransport());
}

SipAccountJsonProxyInterface::~SipAccountJsonProxyInterface()
{
}

void SipAccountJsonProxyInterface::Release()
{
}

void SipAccountJsonProxyInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void SipAccountJsonProxyInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int SipAccountJsonProxyInterface::setStateHandler(SipAccountJsonProxyStateHandler* handler)
{
   post(resip::resip_bind(&SipAccountJsonProxyInterface::setStateHandlerImpl, this, handler));
   return 0;
}

void SipAccountJsonProxyInterface::setStateHandlerImpl(SipAccountJsonProxyStateHandler* handler)
{
   mStateHandler = handler;
}

int SipAccountJsonProxyInterface::requestStateAllAccounts()
{
   post(resip::resip_bind(&SipAccountJsonProxyInterface::requestStateAllAccountsImpl, this));
   return 0;
}

void SipAccountJsonProxyInterface::requestStateAllAccountsImpl()
{
   JsonFunctionCall(mTransport, "requestStateAllAccounts");
}

// JsonApiClientModule
void SipAccountJsonProxyInterface::setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport)
{
   mTransport = transport;
}

int SipAccountJsonProxyInterface::processIncoming(const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&SipAccountJsonProxyInterface::processIncomingImpl, this, request));
   return kSuccess;
}

void SipAccountJsonProxyInterface::processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

int SipAccountJsonProxyInterface::handleCreateResult(const rapidjson::Value& functionObjectVal)
{
   JsonDeserialize(functionObjectVal, "account", mServerCreatedHandle);
   mCondCreated.notify_one();
   return kSuccess;
}

int SipAccountJsonProxyInterface::handleAccountStatusChanged(const rapidjson::Value& functionObjectVal)
{
   SipAccountHandle account = -1;
   SipAccountStatusChangedEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(args));

   std::map<SipAccountHandle, SipAccountHandler*>::iterator it = mAppHandlers.find(account);
   if (it != mAppHandlers.end())
   {
      resip::ReadCallbackBase* callback = makeFpCommand(SipAccountHandler::onAccountStatusChanged, it->second, account, args);
      postCallback(callback);
   }
   return kSuccess;
}

int SipAccountJsonProxyInterface::handleAccountState(const rapidjson::Value& functionObjectVal)
{
   SipAccount::JsonProxyAccountStateEvent args;
   JsonDeserialize(functionObjectVal, "accountStateArray", args.accountState);

   if (mStateHandler != NULL)
   {
      postCallback(resip::resip_bind(&SipAccountJsonProxyStateHandler::onAccountState, mStateHandler, 0, args));
   }
   return kSuccess;
}

void SipAccountJsonProxyInterface::postCallback(resip::ReadCallbackBase* fp)
{
   mCallbacks.add(fp);
   if (mCbHook) { mCbHook(); }
}

// SipAccountManager
SipAccountHandle SipAccountJsonProxyInterface::create()
{
   return -1;
}
SipAccountHandle SipAccountJsonProxyInterface::create(const SipAccountSettings& accountSettings)
{
   post(resip::resip_bind(&SipAccountJsonProxyInterface::createImpl, this, accountSettings));

   std::unique_lock<std::mutex> lk(mMutex);
   mCondCreated.wait(lk);

   return mServerCreatedHandle;
}
void SipAccountJsonProxyInterface::createImpl(const SipAccountSettings& accountSettings)
{
   DebugLog(<< "createImpl begin");

   JsonFunctionCall(mTransport, "create", JSON_VALUE(accountSettings));

   mServerCreatedHandle = -1;
}

int SipAccountJsonProxyInterface::configureDefaultAccountSettings(SipAccountHandle account, const SipAccountSettings& accountSettings) {
   return kSuccess;
}
int SipAccountJsonProxyInterface::configureTransportAccountSettings(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountSettings& sipAccountSettings, CPCAPI2::NetworkTransport transport) {
   return kSuccess;
}

int SipAccountJsonProxyInterface::applySettings(SipAccountHandle account) {
   return kSuccess;
}

int SipAccountJsonProxyInterface::destroy(CPCAPI2::SipAccount::SipAccountHandle handle) {
   return kSuccess;
}

int SipAccountJsonProxyInterface::setHandler(SipAccount::SipAccountHandle account, SipAccount::SipAccountHandler* handler)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&SipAccountJsonProxyInterface::setHandlerImpl, this, account, handler);
   if (handler == NULL)
   {
      execute(f);
      process(-1);
   }
   else
   {
      post(f);
   }

   return kSuccess;
}
void SipAccountJsonProxyInterface::setHandlerImpl(SipAccount::SipAccountHandle account, SipAccount::SipAccountHandler* handler)
{
   mAppHandlers[account] = handler;
}

int SipAccountJsonProxyInterface::enable(SipAccountHandle account)
{
   post(resip::resip_bind(&SipAccountJsonProxyInterface::enableImpl, this, account));
   return kSuccess;
}
int SipAccountJsonProxyInterface::enableImpl(SipAccountHandle account)
{
   JsonFunctionCall(mTransport, "enable", JSON_VALUE(account));
   return kSuccess;
}

int SipAccountJsonProxyInterface::disable(SipAccountHandle account, bool force) {
   post(resip::resip_bind(&SipAccountJsonProxyInterface::disableImpl, this, account, force));
   return kSuccess;
}
int SipAccountJsonProxyInterface::disableImpl(SipAccountHandle account, bool force)
{
   JsonFunctionCall(mTransport, "disable", JSON_VALUE(account));
   return kSuccess;
}

int SipAccountJsonProxyInterface::requestRegistrationRefresh(CPCAPI2::SipAccount::SipAccountHandle account, unsigned int deadlineSecondsFromNow)
{
   return kSuccess;
}

int SipAccountJsonProxyInterface::setNetworkRestriction(SipAccountHandle account, NetworkTransport transport, bool restricted) {
   return kSuccess;
}

void SipAccountJsonProxyInterface::setT1TimerValueMs(unsigned long t1ValueMs) {
}
void SipAccountJsonProxyInterface::setT2TimerValueMs(unsigned long t2ValueMs) {
}
void SipAccountJsonProxyInterface::setT4TimerValueMs(unsigned long t4ValueMs) {
}
void SipAccountJsonProxyInterface::setTDTimerValueMs(unsigned long tDValueMs) {
}
void SipAccountJsonProxyInterface::setTFTimerValueMs(unsigned long tFValueMs) {
}
void SipAccountJsonProxyInterface::setSipTotalTransactionTimeoutMs(unsigned long sipTotalTransactionTimeoutMs) {
}
unsigned long SipAccountJsonProxyInterface::getT1TimerValueMs() {
   return 0;
}
unsigned long SipAccountJsonProxyInterface::getT2TimerValueMs() {
   return 0;
}
unsigned long SipAccountJsonProxyInterface::getT4TimerValueMs() {
   return 0;
}
unsigned long SipAccountJsonProxyInterface::getTDTimerValueMs() {
   return 0;
}
unsigned long SipAccountJsonProxyInterface::getTFTimerValueMs() {
   return 0;
}

int SipAccountJsonProxyInterface::process(unsigned int timeout)
{
   resip::ReadCallbackBase* fp = mCallbacks.getNext(timeout);
   while (fp)
   {
      (*fp)();
      delete fp;
      fp = mCallbacks.getNext(kBlockingModeNonBlocking);
   }
   return kSuccess;
}


#ifdef CPCAPI2_AUTO_TEST

AutoTestReadCallback* SipAccountJsonProxyInterface::process_test(int timeout)
{
   //if (mShutdown)
   //{
   //   return NULL;
   //}
   resip::ReadCallbackBase* rcb = mCallbacks.getNext(timeout);
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
   {
      return fpCmd;
   }
   if (rcb != NULL)
   {
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0, 0));
   }
   return NULL;
}

#endif


struct ReadCallback : resip::ReadCallbackBase
{
   ReadCallback(std::function<void()> f) : mF(f) {}
   virtual void operator()() { mF(); }
   virtual void* address() { return NULL; }
private:
   std::function<void()> mF;
};

void SipAccountJsonProxyInterface::postToProcessThread(void(*pfun)(void*), void* obj)
{
   std::function<void()> bfunc = std::bind(pfun, obj);
   ReadCallback* brcb = new ReadCallback(bfunc);
   mCallbacks.add(brcb);
}

int SipAccountJsonProxyInterface::setAdornmentHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipAccountAdornmentHandler* handler)
{
   return kError;
}

int SipAccountJsonProxyInterface::adornMessage(SipAccountHandle account, unsigned int adornmentMessageId, const cpc::vector<SipHeader>& customHeaders)
{
   return kError;
}

int SipAccountJsonProxyInterface::decodeProvisioningResponse(const cpc::string& provisioningResponse, cpc::vector<SipAccountSettings>& outSipAccountSettings)
{
   return kSuccess;
}
}
}
#endif
