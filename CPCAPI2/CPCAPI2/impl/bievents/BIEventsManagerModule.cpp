#include "brand_branded.h"

#include "bievents/BIEventsManager.h"

#if (CPCAPI2_BRAND_BIEVENTS_MODULE == 1)
#include "BIEventsManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace BIEvents
   {
      BIEventsManager* BIEventsManager::getInterface( CPCAPI2::Phone* cpcPhone )
      {
#if (CPCAPI2_BRAND_BIEVENTS_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<BIEventsManagerInterface>(phone, "BIEventsManager");
#else
         return NULL;
#endif
      }

   }
}
