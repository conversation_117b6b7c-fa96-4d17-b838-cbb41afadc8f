#include "brand_branded.h"

#ifdef CPCAPI2_GENBAND_MODULE
#include "WebSocketClientManager.h"

using websocketpp::lib::placeholders::_1;
using websocketpp::lib::placeholders::_2;
using websocketpp::lib::bind;

namespace CPCAPI2
{
namespace Genband
{

WebSocketClientManager::WebSocketClientManager() 
{
	nextId = 0;
	// clear all error/access channels
	mEndpoint.clear_access_channels(websocketpp::log::alevel::all);
	mEndpoint.clear_error_channels(websocketpp::log::elevel::all);
        
	// Initialize the endpoint
	mEndpoint.init_asio();

	// Mark this endpoint as perpetual. Perpetual endpoints will not exit
	// even if there are no connections.
	mEndpoint.start_perpetual();

	// Start a background thread and run the endpoint in that thread
	mThread.reset(new thread_type(&client::run, &mEndpoint));
}

int WebSocketClientManager::connect(std::string const & uri, unsigned int subscriptionHdl, GenbandRestAPIEventHandler* msgHandler) 
{
	websocketpp::lib::error_code ec;

	// connect to this address

	client::connection_ptr connection = mEndpoint.get_connection(uri,ec);
	if (ec)
	{
		connection.reset();
		return ec.value();
	}
	
	//connection_ptr app(new WebSocketConnection(connection->get_handle(), subscriptionHdl, msgHandler));

	connection->set_open_handler(bind(&WebSocketClientManager::on_open,this,std::placeholders::_1));
	connection->set_fail_handler(bind(&WebSocketClientManager::on_fail,this,std::placeholders::_1));
	connection->set_message_handler(bind(&WebSocketClientManager::on_message,this,std::placeholders::_1,std::placeholders::_2));
	connection->set_close_handler(bind(&WebSocketClientManager::on_close,this,std::placeholders::_1));

	mConnections.insert(std::pair<unsigned int, websocketpp::connection_hdl>(subscriptionHdl, connection->get_handle()));

	mEndpoint.connect(connection);

	return 0;
}

void WebSocketClientManager::close(unsigned int subscriptionHdl) 
{
	mEndpoint.close(mConnections.find(subscriptionHdl)->second, websocketpp::close::status::normal, "User requested close.");
}

void WebSocketClientManager::shutdown() 
{
    // for each connection call close
	std::map<unsigned int, websocketpp::connection_hdl>::iterator it = mConnections.begin();
	while(it != mConnections.end())
	{
		mEndpoint.close(it->second, websocketpp::close::status::normal, "Closing connections before shutdown.");
		it++;
	}
	mConnections.clear();
    // Unflag the endpoint as perpetual. This will instruct it to stop once
    // all connections are finished.
    mEndpoint.stop_perpetual();

    // Block until everything is done
    mThread->join();
}
/////////////////////////////////////////////////////////////////////

void WebSocketClientManager::on_message(websocketpp::connection_hdl, message_ptr msg)
{
	GenbandNotification result;
	result.Body = msg->get_payload();
	result.Header = msg->get_header();
	msgHandler->onIncomingCallCommand(subscriptionHdl, result);
}

void WebSocketClientManager::on_fail(websocketpp::connection_hdl) 
{
}

void WebSocketClientManager::on_open(websocketpp::connection_hdl) 
{
}

void WebSocketClientManager::on_close(websocketpp::connection_hdl) 
{
}

websocketpp::connection_hdl WebSocketClientManager::get_hdl()
{
    return m_hdl;
}


} // namespace Genband
} // namespace CPCAPI2
#endif // CPCAPI2_GENBAND_MODULE