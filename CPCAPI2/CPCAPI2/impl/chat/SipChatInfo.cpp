#include "brand_branded.h"
#if (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)

#include "SipChatInfo.h"

#include <msrp_uri.h>

namespace CPCAPI2
{
namespace SipChat
{

SipChatHandle SipChatInfo::nextChatHandle = 1;

SipChatInfo::SipChatInfo(SipChatHandle handle, resip::SharedPtr<resip::DialogUsageManager> dum, resip::InviteSessionHandler* handler)
   : SipMsrpMessagingSessionInfo(handle, dum, handler),
     IsComposingInfo()
{
}

SipChatInfo::~SipChatInfo()
{
   // Destroy all the message info objects
   for (SipChatMessageInfoMap::const_iterator iter = messageInfoMap.begin(); iter != messageInfoMap.end(); iter++)
   {
      SipChatMessageInfo* messageInfo = iter->second;
      assert(messageInfo);
      delete messageInfo;
   }
}

SipChatMessageInfo *SipChatInfo::getMessageInfo(const SipChatMessageHandle& handle) const
{
   SipChatMessageInfoMap::const_iterator it = messageInfoMap.find(handle);
   return (it != messageInfoMap.end()) ? it->second : NULL;
}

void SipChatInfo::addMessageInfo(const SipChatMessageHandle& handle, SipChatMessageInfo* messageInfo)
{
   assert(!messageInfoMap[handle] || messageInfoMap[handle] == messageInfo);
      
   // Keep the mapping message handle -> message information
   messageInfoMap[handle] = messageInfo;
}

bool SipChatInfo::hasMessageId(const SipChatMessageHandle& handle)
{
   return messageInfoMap.find(handle) != messageInfoMap.end();
}

}
}

#endif