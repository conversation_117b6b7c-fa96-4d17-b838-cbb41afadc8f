#if !defined(__CPCAPI2_HARDWARE_HASH_H__)
#define __CPCAPI2_HARDWARE_HASH_H__

#include <vector>
#include <string>
#include <stdint.h>

namespace CPCAPI2
{
namespace Licensing
{
   class HardwareHash
   {
   public:
      std::string GenerateHardwareHash();

   private:
      std::vector<std::string> GetMacAddresses();
      std::vector<std::string> GetHddIds();
      std::string GetSerialNumber();
      int64_t GetRamSize();
      void Align(std::string& in, int len);
   };
}
}

#endif // __CPCAPI2_HARDWARE_HASH_H__