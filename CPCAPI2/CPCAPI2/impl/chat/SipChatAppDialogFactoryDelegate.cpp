#include "brand_branded.h"
#if (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)

#include "SipChatAppDialogFactoryDelegate.h"
#include "SipChatManagerImpl.h"
#include "SipChatInfo.h"
#include "SipChatHelper.h"

#include "../cpm/CpmHelper.h"

namespace CPCAPI2
{
namespace SipChat
{

SipChatAppDialogFactoryDelegate::SipChatAppDialogFactoryDelegate(SipChatManagerImpl* parent)
   : parent(parent)
{
}

SipChatAppDialogFactoryDelegate::~SipChatAppDialogFactoryDelegate()
{
}

bool SipChatAppDialogFactoryDelegate::isMyMessage(const resip::SipMessage& msg)
{
   return CpmHelper::contains3gppFeatureTag(msg, SipChatManagerImpl::CPM_FEATURE_TAG) || // This is the standard way of detecting a CPM 1-1 session request
          (CpmHelper::containsNoFeatureTag(msg) && CpmHelper::acceptsCpimMimeType(msg)); // This is for the case of Genband that does not put the standard feature tag in their INVITEs
}

resip::AppDialogSet* SipChatAppDialogFactoryDelegate::createAppDialogSet(resip::DialogUsageManager& dum, const resip::SipMessage& msg)
{
   SipChatHandle sessionHandle = SipChatInfo::nextChatHandle++;
   return new SipChatInfo(sessionHandle, parent->getDum(), parent);
}

}
}

#endif