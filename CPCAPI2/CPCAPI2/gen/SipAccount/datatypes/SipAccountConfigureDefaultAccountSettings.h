#include "jsonrpc/StringSerialization.h"
#include "jsonrpc/OptionalSerialization.h"
#include <any>
#include "jsonrpc/VectorSerialization.h"
#include <nlohmann/json.hpp>
#include "SipAccountSettings.h"

#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTCONFIGUREDEFAULTACCOUNTSETTINGS_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTCONFIGUREDEFAULTACCOUNTSETTINGS_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  class SipAccountConfigureDefaultAccountSettings
  {
  public:
    int32_t phoneHandle;
    int32_t sipAccountHandle;
    SipAccountSettings sipAccountSettings;

    SipAccountConfigureDefaultAccountSettings() = default;

    SipAccountConfigureDefaultAccountSettings(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipAccountConfigureDefaultAccountSettings& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["sipAccountHandle"] = o.sipAccountHandle;
     j["sipAccountSettings"] = o.sipAccountSettings;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipAccountConfigureDefaultAccountSettings& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.sipAccountHandle = j.at("sipAccountHandle");
     o.sipAccountSettings = j.at("sipAccountSettings");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTCONFIGUREDEFAULTACCOUNTSETTINGS_H
