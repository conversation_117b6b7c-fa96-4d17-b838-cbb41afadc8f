#pragma once
#ifndef __CPCAPI2__BIEVENTS_CACHE_H__
#define __CPCAPI2__BIEVENTS_CACHE_H__

#include <stdint.h>
#include <string>
#include <list>
#include <zlib.h>

#include "BIEventsInternalTypes.h"

// missing ssize_t on windows
#if _WIN32
#ifndef ssize_t
#define ssize_t SSIZE_T
#endif
#endif

#define CHUNK 16384

namespace CPCAPI2
{
   namespace BIEvents
   {
      /**
       * This class implements a file-based cache which is used to house events
       * temporarily during periods of disconnected-ness from the BI server. It
       * has a number of important properties:
       *
       * It will use a log rotation scheme, to avoid growing out of control. As
       * a result it will take two parameters, one for the number of log files
       * to keep, and the second for the number of bytes (post compression) of
       * each log.
       *
       * The cache will work from oldest to newest, i.e. the first records in
       * the oldest log rotated file, down through the end of that file,
       * through to the next oldest, etc. until it has "closed the gap" with
       * the most recent file and the oldest files have been deleted.
       *
       * In the situation where events are produced faster than they can be
       * delivered to the server, more "log rotated" files will be created, but
       * the oldest files will be dropped/deleted beyond the parameters
       * specified above.
       *
       * The logfile itself will be a compressed zlib stream, with a header at
       * the front to indicate the last point at which READ processing left off
       * (to allow for quicker seeking in a restart scenario).  This will serve
       * two purposes, a) it will reduce the space requirements at the
       * application layer, and b) it will provide a certain level of
       * obfuscation as to the contents of the events (while understanding that
       * this is not encryption). The header does not contains a write index,
       * that will always be at the end of the file, assuming there is room.
       */
      class BIEventCache
      {
      public:

         /**
          */
         BIEventCache( const std::string& workingDir, const std::string& baseName, const int& numFiles, const off_t& fileSizeLimit );
         virtual ~BIEventCache();

         /**
          * Call this prior to using the BIEventCache. If it returns false,
          * items cannot be cached properly as something failed during setup.
          */
         bool init( void );

         /**
          * Pushes an event into the cache. Compresses the contents of the
          * event before it is persisted in the file.
          *
          * @return true if the event was successfully pushed in the cache,
          *         false otherwise
          */
         bool PushEvent( const BIEventRecord& eventContent );

         /**
          * Reads events from the cache, servicing the oldest items first.
          * Reading from the cache is a two-step operation. Step 1) the data is
          * read from the cache into memory by calling PeekEvents.  Step 2) The
          * events are "Popped". Step 2 is only performed once a successful
          * upload to the server has taken place. This prevents us from losing
          * events due to periods of disconnectivity.
          *
          * @param outEvents a vector of json text that is ready to be sent
          *        to the BI server (in order of oldest first).
          * @param limit an upper bound to the number of events popped from the
          *        queue during this call. This is only a suggestion. In
          *        general, less items may be returned.  However: zero events
          *        will be returned when there is no more data. If there is
          *        at least one item, there is more data remaining in the cache.
          */
         void PopEvents( std::list< BIEventRecord >& outEvents, int limit );

         /**
          * Removes all data. Restarts/reopens files if they were previously open.
          * returns false if there was any failure to do so.
          */
         bool Purge( void );

         /**
          * "Rolls back" the reading of the file done by PopEvents to the last
          * checkpoint. This may be required when a block of events fails to be
          * sent to the server.
          *
          * NB: some events may be sent twice as a result of this
          */
         void RollbackRead( void );

      private: // methods

         bool RotateFiles( void );

         bool initWriteFiles( void );
         bool initReadFiles( bool resetInflate = true );

         void CloseWriteFiles( void );
         void CloseReadFiles( bool resetInflate = true );
         void CloseAllFiles( void );

         // Returns the number of bytes written/read, or -1 if error
         ssize_t WriteDataToFile( uint8_t *pData, size_t dataSize, int fd );
         ssize_t ReadDataFromFile( uint8_t * buf, size_t bufsize, int fd );

         int deflateStuff( uint8_t (&buf)[ CHUNK ], Bytef * input, size_t inputSize, bool lastBlock = false );
         int inflateStuff( uint8_t (&buf)[ CHUNK ], Bytef * output, size_t outputSize );

      private: // data

         const std::string m_WorkingDir;
         const std::string m_BaseName;
         const int m_NumFiles; // must be >= 1
         const off_t m_FileSizeLimit;

         // The last point in the read file, that our buffer was empty and we
         // were at the start of a record.  This is used as a rollback point in
         // case of disconnection issues. The result is that some events may be
         // posted more than once.
         //
         // At the very least there will be one checkpoint per file (the start).
         // However it is possible there may be more than one, depending on how
         // the decompression proceeds.
         off_t m_CheckPoint;
         bool m_IsRollbackSet;

         int   m_CurReadFD;
         int   m_CurWriteFD;
         std::string m_CurReadFileName;
         std::string m_CurWriteFileName;

         z_stream m_DeflateStream;
         z_stream m_InflateStream;
         bool m_DeflateStreamInitialized;
         bool m_InflateStreamInitialized;

         uint8_t m_DeflateBuf[ CHUNK ]; // context needs to be carried over multiple calls
         uint8_t m_InflateBuf[ CHUNK ]; // context needs to be carried over multiple calls
      };
   }
}

#endif /* __CPCAPI2__BIEVENTS_CACHE_H__ */
