#pragma once

#if !defined(CPCAPI2_CLOUD_RELAY_CONNECTOR_INTERFACE_H)
#define CPCAPI2_CLOUD_RELAY_CONNECTOR_INTERFACE_H

#include "cpcapi2defs.h"
#include "cloudrelayconnector/CloudRelayConnector.h"
#include "cloudrelayconnector/CloudRelayConnectorHandler.h"
#include "phone/Cpcapi2EventSource.h"
#include "phone/PhoneModule.h"

namespace CPCAPI2
{
class PhoneInterface;
namespace CloudRelayConnector
{
class CloudRelayConnectorImpl;
class CloudRelayConnectorSyncHandler {};
class CloudRelayConnectorInterface : public CPCAPI2::EventSource<CloudRelayConnectorHandle, CloudRelayConnectorHandler, CloudRelayConnectorSyncHandler>,
                                     public PhoneModule,
                                     public CloudRelayConnector
{
public:
   CloudRelayConnectorInterface(Phone* phone);
   virtual ~CloudRelayConnectorInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // CloudRelayConnectorManager
   virtual int process(unsigned int timeout) OVERRIDE {
      return CPCAPI2::EventSource<CloudRelayConnectorHandle, CloudRelayConnectorHandler, CloudRelayConnectorSyncHandler>::process(timeout);
   }
   virtual void interruptProcess() OVERRIDE {
      CPCAPI2::EventSource<CloudRelayConnectorHandle, CloudRelayConnectorHandler, CloudRelayConnectorSyncHandler>::interruptProcess();
   }
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE {
      CPCAPI2::EventSource<CloudRelayConnectorHandle, CloudRelayConnectorHandler, CloudRelayConnectorSyncHandler>::setCallbackHook(cbHook, context);
   }

   virtual CloudRelayConnectorHandle createCloudRelayConnector() OVERRIDE;
   virtual int setHandler(CloudRelayConnectorHandle conn, CloudRelayConnectorHandler* handler) OVERRIDE;
   virtual int destroyCloudRelayConnector(CloudRelayConnectorHandle conn) OVERRIDE;
   virtual int setConnectionSettings(CloudRelayConnectorHandle conn, const CloudRelayConnectorSettings& settings) OVERRIDE;
   virtual int connectToCloudRelay(CloudRelayConnectorHandle conn) OVERRIDE;
   virtual int disconnect(CloudRelayConnectorHandle conn) OVERRIDE;
   virtual int broadcast(CloudRelayConnectorHandle conn, const cpc::string& msg) OVERRIDE;
   virtual int sendTo(CloudRelayConnectorHandle conn, CloudRelayEndpointId destinationId, const cpc::string& msg) OVERRIDE;

   PhoneInterface* getThisSdkPhone() const {
      return mPhone;
   }

   void fireConnStatusEvent(CloudRelayConnectorHandle conn, const ServiceConnectionStatusEvent& evt);
   void fireBroadcast(CloudRelayConnectorHandle conn, const CPCAPI2::CloudRelayConnector::BroadcastEvent& evt);
   void fireMessage(CloudRelayConnectorHandle conn, const CPCAPI2::CloudRelayConnector::MessageEvent& evt);

private:
   void createCloudRelayConnectorImpl(CloudRelayConnectorHandle conn);
   void destroyCloudRelayConnectorImpl(CloudRelayConnectorHandle conn);
   void setConnectionSettingsImpl(CloudRelayConnectorHandle conn, const CloudRelayConnectorSettings& settings);
   void connectToCloudRelayImpl(CloudRelayConnectorHandle conn);
   void disconnectImpl(CloudRelayConnectorHandle conn);
   void broadcastImpl(CloudRelayConnectorHandle conn, const cpc::string& msg);
   void sendToImpl(CloudRelayConnectorHandle conn, CloudRelayEndpointId destinationId, const cpc::string& msg);

private:
   PhoneInterface* mPhone;
   typedef std::map<CloudRelayConnectorHandle, std::shared_ptr<CloudRelayConnectorImpl> > InstanceMap;
   InstanceMap mInstMap;
};

class CloudRelayConnectorHandleFactory
{
public:
   static CloudRelayConnectorHandle getNext() { return sNextHandle++; }
private:
   static CloudRelayConnectorHandle sNextHandle;
};

}
}

#endif // CPCAPI2_CLOUD_RELAY_CONNECTOR_INTERFACE_H

