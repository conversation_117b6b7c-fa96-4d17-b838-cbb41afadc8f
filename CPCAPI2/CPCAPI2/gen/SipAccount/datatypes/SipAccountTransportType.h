#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTTRANSPORTTYPE_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTTRANSPORTTYPE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  enum class SipAccountTransportType
  {
    Auto,
    Udp,
    Tcp,
    Tls,
    Unknown
  };

  inline std::string to_string(SipAccountTransportType value)
  {
    switch (value)
    {
      case SipAccountTransportType::Auto:
        return "Auto";
      case SipAccountTransportType::Udp:
        return "UDP";
      case SipAccountTransportType::Tcp:
        return "TCP";
      case SipAccountTransportType::Tls:
        return "TLS";
      case SipAccountTransportType::Unknown:
        return "Unknown";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, SipAccountTransportType value)
  {
    switch (value)
    {
      case SipAccountTransportType::Auto:
        os << "Auto";
        break;
      case SipAccountTransportType::Udp:
        os << "UDP";
        break;
      case SipAccountTransportType::Tcp:
        os << "TCP";
        break;
      case SipAccountTransportType::Tls:
        os << "TLS";
        break;
      case SipAccountTransportType::Unknown:
        os << "Unknown";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const SipAccountTransportType& e)
  {
    switch (e)
    {
      case SipAccountTransportType::Auto:
        j = "Auto";
        break;
      case SipAccountTransportType::Udp:
        j = "UDP";
        break;
      case SipAccountTransportType::Tcp:
        j = "TCP";
        break;
      case SipAccountTransportType::Tls:
        j = "TLS";
        break;
      case SipAccountTransportType::Unknown:
        j = "Unknown";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, SipAccountTransportType& e)
  {
      if (0 == j.get<std::string>().compare("Auto"))
    {
      e = SipAccountTransportType::Auto;
    }
    else if (0 == j.get<std::string>().compare("UDP"))
    {
      e = SipAccountTransportType::Udp;
    }
    else if (0 == j.get<std::string>().compare("TCP"))
    {
      e = SipAccountTransportType::Tcp;
    }
    else if (0 == j.get<std::string>().compare("TLS"))
    {
      e = SipAccountTransportType::Tls;
    }
    else if (0 == j.get<std::string>().compare("Unknown"))
    {
      e = SipAccountTransportType::Unknown;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum SipAccountTransportType"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTTRANSPORTTYPE_H
