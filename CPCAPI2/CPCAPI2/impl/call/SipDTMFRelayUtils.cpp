#include <resip/stack/GenericContents.hxx>
#include <resip/stack/OctetContents.hxx>
#include <utils/msrp_strndup.h> // Should be moved somewhere more common?
#include "../util/dtmf_tone_helper.h"
#include "SipDTMFRelayUtils.h"

#include <assert.h>
#include <string.h>

using namespace CPCAPI2::SipConversation;

namespace 
{
   static resip::Mime  sg_cDTMFMimeType    = resip::Mime("application", "dtmf-relay");
   static const char  *sg_szSignalHeader   = "Signal=";
   static const char  *sg_szDurationHeader = "Duration=";
}

resip::Contents *SipDTMFRelayUtils::MakeDTMFRelayContents(
   const unsigned int inToneId, 
   unsigned long ulInDuration
)
{
   char buf[ 256 ];
   char chSignal = DtmfToneHelper::dtmfCharFromToneId( inToneId );
   
   snprintf( buf, 256, "%s%c\r\n%s%lu", sg_szSignalHeader, chSignal, sg_szDurationHeader, ulInDuration );
   
   try
   {
      return new resip::OctetContents(
         resip::Data( buf, strlen( buf )),
         sg_cDTMFMimeType
      );
   }
   catch (const resip::BaseException&)
   {
   }
  
   return NULL;
}

bool SipDTMFRelayUtils::ParseDTMFInfo(
   const resip::Contents&  cInContents,
   unsigned int&           outToneId,
   unsigned long&          ulOutDuration
)
{
#if 0
   resip::Data contentData(cInContents.getBodyData());
   const char* szData = contentData.data();
   if (szData == NULL)
      return false;
   
   outToneId = 0;
   //ulOutDuration = EDurationDefaultMs;

   size_t iSignalHeaderLen = strlen(sg_szSignalHeader);
   size_t iDurationHeaderLen = strlen(sg_szDurationHeader);
   
   char chSignal = '\0';

   // handle Cisco DTMF INFO type
   while (*szData)
   {
      // see if it's a line we understand
      if (strncmp(szData, sg_szSignalHeader, iSignalHeaderLen) == 0)
      {
         szData += iSignalHeaderLen;
         while (*szData == ' ' || *szData == '\t')
            szData++;
            
         if (*szData)  
            chSignal = *szData;
      }
      else if (strncmp(szData, sg_szDurationHeader, iDurationHeaderLen) == 0)
      {
         szData += iDurationHeaderLen;
         const char *szBegin = szData;

         while (*szData && (*szData != '\r'))
            szData++;

         if (*szBegin)
         {
            int iLen = static_cast<int>(szData - szBegin);
            if (iLen > 0)
            {
               char *strDuration = strndup( szBegin, iLen );
               ulOutDuration = atoi(strDuration);
               free( strDuration );
            }
         }
      }
      else
      {
         // ignore
      }

      // skip to the next line
      while (*szData && (*szData != '\n'))
         szData++;
         
      if (*szData)  
         szData++;
   }
   
   outToneId = DtmfToneHelper::dtmfToneIdFromChar( chSignal );
#endif
   return true;
}
