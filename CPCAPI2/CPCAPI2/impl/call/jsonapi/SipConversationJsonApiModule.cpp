#include "brand_branded.h"

#include <interface/experimental/call/SipConversationJsonProxy.h>
#include <interface/experimental/call/SipConversationJsonApi.h>

#if (CPCAPI2_BRAND_CALL_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "SipConversationJsonServerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif
#if (CPCAPI2_BRAND_CALL_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "SipConversationJsonProxyInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace SipConversation
   {
      SipConversationJsonApi* SipConversationJsonApi::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_CALL_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<SipConversationJsonServerInterface>(phone, "SipConversationJsonApi");
#else
         return NULL;
#endif
      }

      SipConversationManagerJsonProxy* SipConversationManagerJsonProxy::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_CALL_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<SipConversationJsonProxyInterface>(phone, "SipConversationManagerJsonProxy");
#else
         return NULL;
#endif
      }
   }
}
