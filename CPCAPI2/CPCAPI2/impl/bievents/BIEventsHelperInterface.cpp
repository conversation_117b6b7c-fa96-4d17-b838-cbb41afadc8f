#include "BIEventsHelperInterface.h"

using namespace CPCAPI2::BIEvents;

BIEventsHelperInterface::BIEventsHelperInterface( BIEventsManagerInterface *pManager )
   : m_Interface( pManager ), m_CurrentGroupID( 1 )
{
}

int BIEventsHelperInterface::createEventGroupID( void )
{
   return m_CurrentGroupID++;
}

int BIEventsHelperInterface::postUIEvent(
   const BIEventsHandle& hEvents,
   const cpc::string& eventSource,
   const cpc::string& parentID,
   const cpc::string& ID,
   const char *uiVerb,
   const cpc::string& text,
   const BIEventBody& extraInfo,
   EventID& outEventID,
   const int eventGroup,
   const int eventContext,
   const cpc::string& eventSummary )
{
   BIEventHeader outHeader;
   BIEventBody outBody( extraInfo );

   outHeader.Type = "UI_T";
   outHeader.Source = eventSource;
   outHeader.Group = eventGroup;
   outHeader.Context = eventContext;
   outHeader.Summary = eventSummary;

   BIPair pair;
   pair.Name = "Verb";
   pair.Value.ValueType = BIValueType_string;
   pair.Value.StringValue = uiVerb;
   outBody.push_back( pair );

   pair.Name = "ParentID";
   pair.Value.ValueType = BIValueType_string;
   pair.Value.StringValue = parentID;
   outBody.push_back( pair );

   pair.Name = "ID";
   pair.Value.ValueType = BIValueType_string;
   pair.Value.StringValue  = ID;
   outBody.push_back( pair );

   if( !text.empty() )
   {
      pair.Name = "Text";
      pair.Value.ValueType = BIValueType_string;
      pair.Value.StringValue = text;
      outBody.push_back( pair );
   }

   return m_Interface->postEvent( hEvents, outHeader, outBody, outEventID );
}

int BIEventsHelperInterface::postModelEvent(
   const BIEventsHandle& hEvents,
   const cpc::string& eventSource,
   const char *modelID,
   const char *modelVerb,
   const int& hResource,
   const BIEventBody& extraInfo,
   EventID& outEventID,
   const int eventGroup,
   const int eventContext,
   const cpc::string& eventSummary )
{
   BIEventHeader outHeader;
   BIEventBody outBody( extraInfo );

   outHeader.Type = "MDL_T";
   outHeader.Source = eventSource;
   outHeader.Group = eventGroup;
   outHeader.Context = eventContext;
   outHeader.Summary = eventSummary;

   BIPair pair;
   pair.Name = "Verb";
   pair.Value.ValueType = BIValueType_string;
   pair.Value.StringValue = modelVerb;
   outBody.push_back( pair );

   pair.Name = "ID";
   pair.Value.ValueType = BIValueType_string;
   pair.Value.StringValue  = modelID;
   outBody.push_back( pair );

   pair.Name = "ResourceID";
   pair.Value.ValueType = BIValueType_int;
   pair.Value.IntValue = hResource;
   outBody.push_back( pair );

   return m_Interface->postEvent( hEvents, outHeader, outBody, outEventID );
}

int BIEventsHelperInterface::postSystemEvent(
   const BIEventsHandle& hEvents,
   const cpc::string& eventSource,
   const char *systemVerb,
   const BIEventBody& extraInfo,
   EventID& outEventID,
   const int eventGroup,
   const int eventContext,
   const cpc::string& eventSummary )
{
   BIEventHeader outHeader;
   BIEventBody outBody( extraInfo );

   outHeader.Type = "SYS_T";
   outHeader.Source = eventSource;
   outHeader.Group = eventGroup;
   outHeader.Context = eventContext;
   outHeader.Summary = eventSummary;

   BIPair pair;
   pair.Name = "Verb";
   pair.Value.ValueType = BIValueType_string;
   pair.Value.StringValue = systemVerb;
   outBody.push_back( pair );

   return m_Interface->postEvent( hEvents, outHeader, outBody, outEventID );
}

int BIEventsHelperInterface::postCommerceEvent(
   const BIEventsHandle& hEvents,
   const cpc::string& eventSource,
   const char *commerceVerb,
   const int& hResource,
   const BIEventBody& extraInfo,
   EventID& outEventID,
   const int eventGroup,
   const int eventContext,
   const cpc::string& eventSummary )
{
   BIEventHeader outHeader;
   BIEventBody outBody( extraInfo );

   outHeader.Type = "COM_T";
   outHeader.Source = eventSource;
   outHeader.Group = eventGroup;
   outHeader.Context = eventContext;
   outHeader.Summary = eventSummary;

   BIPair pair;
   pair.Name = "Verb";
   pair.Value.ValueType = BIValueType_string;
   pair.Value.StringValue = commerceVerb;
   outBody.push_back( pair );

   pair.Name = "ResourceID";
   pair.Value.ValueType = BIValueType_int;
   pair.Value.IntValue = hResource;
   outBody.push_back( pair );

   return m_Interface->postEvent( hEvents, outHeader, outBody, outEventID );
}

int BIEventsHelperInterface::postSDKEvent(
   const BIEventsHandle& hEvents,
   const cpc::string& eventSource,
   const cpc::vector< cpc::string >& labels,
   const BIEventBody& extraInfo,
   EventID& outEventID,
   const int eventGroup,
   const int eventContext,
   const cpc::string& eventSummary )
{
   BIEventHeader outHeader;
   BIEventBody outBody( extraInfo );

   outHeader.Type = "SDK_T";
   outHeader.Source = eventSource;
   outHeader.Group = eventGroup;
   outHeader.Context = eventContext;
   outHeader.Summary = eventSummary;
   outHeader.Labels = labels;

   return m_Interface->postEvent( hEvents, outHeader, outBody, outEventID );
}

int BIEventsHelperInterface::postDebugEvent(
   const BIEventsHandle& hEvents,
   const cpc::string& eventSource,
   const char *debugVerb,
   int threadID,
   const cpc::string& message,
   const cpc::string& stackTrace,
   const BIEventBody& extraInfo,
   EventID& outEventID,
   const int eventGroup,
   const int eventContext,
   const cpc::string& eventSummary )
{
   BIEventHeader outHeader;
   BIEventBody outBody( extraInfo );

   outHeader.Type = "DEBUG";
   outHeader.Source = eventSource;
   outHeader.Group = eventGroup;
   outHeader.Context = eventContext;
   outHeader.Summary = eventSummary;

   BIPair pair;
   pair.Name = "Verb";
   pair.Value.ValueType = BIValueType_string;
   pair.Value.StringValue = debugVerb;
   outBody.push_back( pair );

   pair.Name = "ThreadID";
   pair.Value.ValueType = BIValueType_int;
   pair.Value.IntValue  = threadID;
   outBody.push_back( pair );

   pair.Name = "Message";
   pair.Value.ValueType = BIValueType_string;
   pair.Value.StringValue = message;
   outBody.push_back( pair );

   pair.Name = "StackTrace";
   pair.Value.ValueType = BIValueType_string;
   pair.Value.StringValue = stackTrace;
   outBody.push_back( pair );

   return m_Interface->postEvent( hEvents, outHeader, outBody, outEventID );
}
