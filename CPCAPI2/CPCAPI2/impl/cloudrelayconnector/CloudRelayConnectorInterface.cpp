#include "brand_branded.h"

#if (CPCAPI2_BRAND_CLOUD_RELAY_CONNECTOR_MODULE == 1)
#include "CloudRelayConnectorInterface.h"
#include "CloudRelayConnectorImpl.h"
#include "cloudrelayconnector/CloudRelayConnectorHandler.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::CONF_CONNECTOR

namespace CPCAPI2
{
namespace CloudRelayConnector
{
CloudRelayConnectorHandle CloudRelayConnectorHandleFactory::sNextHandle = 1;

CloudRelayConnectorInterface::CloudRelayConnectorInterface(Phone* phone)
   : EventSource<CloudRelayConnectorHandle, CloudRelayConnectorHandler, CloudRelayConnectorSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
     mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   StackLog(<< "CloudRelayConnectorInterface::CloudRelayConnectorInterface(): " << this << " phone: " << phone);
   mPhone->addRefImpl();
}

CloudRelayConnectorInterface::~CloudRelayConnectorInterface()
{
   StackLog(<< "CloudRelayConnectorInterface::~CloudRelayConnectorInterface(): " << this << " phone: " << mPhone);
   mPhone->releaseImpl();
}

void CloudRelayConnectorInterface::Release()
{
   mInstMap.clear();
   delete this;
}

CloudRelayConnectorHandle CloudRelayConnectorInterface::createCloudRelayConnector()
{
   CloudRelayConnectorHandle h = CloudRelayConnectorHandleFactory::getNext();
   postToSdkThread(resip::resip_bind(&CloudRelayConnectorInterface::createCloudRelayConnectorImpl, this, h));
   return h;
}

void CloudRelayConnectorInterface::createCloudRelayConnectorImpl(CloudRelayConnectorHandle conn)
{
   InfoLog(<< "createCloudRelayConnector - handle: " << conn);
   std::shared_ptr<CloudRelayConnectorImpl> pimpl(new CloudRelayConnectorImpl(this, conn));
   mInstMap[conn] = std::move(pimpl);
}

int CloudRelayConnectorInterface::setHandler(CloudRelayConnectorHandle conn, CloudRelayConnectorHandler* handler)
{
   setAppHandler(conn, handler);
   return kSuccess;
}

int CloudRelayConnectorInterface::destroyCloudRelayConnector(CloudRelayConnectorHandle conn)
{
   postToSdkThread(resip::resip_bind(&CloudRelayConnectorInterface::destroyCloudRelayConnectorImpl, this, conn));
   return kSuccess;
}

void CloudRelayConnectorInterface::destroyCloudRelayConnectorImpl(CloudRelayConnectorHandle conn)
{
   InfoLog(<< "destroyCloudRelayConnector - handle: " << conn);

   CloudRelayConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it != mInstMap.end())
   {
      it->second->destroy();
      mInstMap.erase(it);
   }
}

int CloudRelayConnectorInterface::setConnectionSettings(CloudRelayConnectorHandle conn, const CloudRelayConnectorSettings& settings)
{
   postToSdkThread(resip::resip_bind(&CloudRelayConnectorInterface::setConnectionSettingsImpl, this, conn, settings));
   return kSuccess;
}

void CloudRelayConnectorInterface::setConnectionSettingsImpl(CloudRelayConnectorHandle conn, const CloudRelayConnectorSettings& settings)
{
   InfoLog(<< "setConnectionSettings - handle: " << conn);
   CloudRelayConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it != mInstMap.end())
   {
      it->second->setConnectionSettings(settings);
   }
}

int CloudRelayConnectorInterface::connectToCloudRelay(CloudRelayConnectorHandle conn)
{
   postToSdkThread(resip::resip_bind(&CloudRelayConnectorInterface::connectToCloudRelayImpl, this, conn));
   return kSuccess;
}

void CloudRelayConnectorInterface::connectToCloudRelayImpl(CloudRelayConnectorHandle conn)
{
   InfoLog(<< "connectToCloudRelay - handle: " << conn);
   CloudRelayConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it != mInstMap.end())
   {
      it->second->connectToServices();
   }
}

int CloudRelayConnectorInterface::disconnect(CloudRelayConnectorHandle conn)
{
   postToSdkThread(resip::resip_bind(&CloudRelayConnectorInterface::disconnectImpl, this, conn));
   return kSuccess;
}

void CloudRelayConnectorInterface::disconnectImpl(CloudRelayConnectorHandle conn)
{
   InfoLog(<< "disconnect - handle: " << conn);

   CloudRelayConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it != mInstMap.end())
   {
      StackLog(<< "CloudRelayConnectorInterface::disconnectImpl(): " << this << " phone: " << mPhone << " conn: " << conn);
      it->second->disconnectService();
   }
}

int CloudRelayConnectorInterface::broadcast(CloudRelayConnectorHandle conn, const cpc::string& msg)
{
   postToSdkThread(resip::resip_bind(&CloudRelayConnectorInterface::broadcastImpl, this, conn, msg));
   return kSuccess;
}

void CloudRelayConnectorInterface::broadcastImpl(CloudRelayConnectorHandle conn, const cpc::string& msg)
{
   CloudRelayConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it != mInstMap.end())
   {
      it->second->broadcast(msg);
   }
}

int CloudRelayConnectorInterface::sendTo(CloudRelayConnectorHandle conn, CloudRelayEndpointId destinationId, const cpc::string& msg)
{
   postToSdkThread(resip::resip_bind(&CloudRelayConnectorInterface::sendToImpl, this, conn, destinationId, msg));
   return kSuccess;
}

void CloudRelayConnectorInterface::sendToImpl(CloudRelayConnectorHandle conn, CloudRelayEndpointId destinationId, const cpc::string& msg)
{
   CloudRelayConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it != mInstMap.end())
   {
      it->second->sendTo(destinationId, msg);
   }
}

/*
   ServiceConnectionStatus_Disconnecting = 0,
   ServiceConnectionStatus_Disconnected = 1,
   ServiceConnectionStatus_Connecting = 2,
   ServiceConnectionStatus_Authenticating = 3,
   ServiceConnectionStatus_Connected = 4,
   ServiceConnectionStatus_ConnFailure = 5,
   ServiceConnectionStatus_AuthFailure = 6
*/
resip::Data serviceConnStatusToString(ServiceConnectionStatus s)
{
   switch (s)
   {
   case ServiceConnectionStatus_Disconnecting:
      return "ServiceConnectionStatus_Disconnecting";
   case ServiceConnectionStatus_Disconnected:
      return "ServiceConnectionStatus_Disconnected";
   case ServiceConnectionStatus_Connecting:
      return "ServiceConnectionStatus_Connecting";
   case ServiceConnectionStatus_Authenticating:
      return "ServiceConnectionStatus_Authenticating";
   case ServiceConnectionStatus_Connected:
      return "ServiceConnectionStatus_Connected";
   case ServiceConnectionStatus_ConnFailure:
      return "ServiceConnectionStatus_ConnFailure";
   case ServiceConnectionStatus_AuthFailure:
      return "ServiceConnectionStatus_AuthFailure";
   default:
      break;
   }
   return "Unknown";
}

void CloudRelayConnectorInterface::fireConnStatusEvent(CloudRelayConnectorHandle conn, const ServiceConnectionStatusEvent& evt)
{
   InfoLog(<< "CloudRelayConnectorHandler::onServiceConnectionStatusChanged - handle: " << conn << ", status=" << serviceConnStatusToString(evt.connectionStatus));
   fireEvent(cpcFunc(CloudRelayConnectorHandler::onServiceConnectionStatusChanged), conn, evt);
}

void CloudRelayConnectorInterface::fireBroadcast(CloudRelayConnectorHandle conn, const CPCAPI2::CloudRelayConnector::BroadcastEvent& evt)
{
   InfoLog(<< "CloudRelayConnectorHandler::onBroadcast - handle: " << conn);
   fireEvent(cpcFunc(CloudRelayConnectorHandler::onBroadcast), conn, evt);
}

void CloudRelayConnectorInterface::fireMessage(CloudRelayConnectorHandle conn, const CPCAPI2::CloudRelayConnector::MessageEvent& evt)
{
   InfoLog(<< "CloudRelayConnectorHandler::onMessage - handle: " << conn);
   fireEvent(cpcFunc(CloudRelayConnectorHandler::onMessage), conn, evt);
}

}

}

#endif // CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE
