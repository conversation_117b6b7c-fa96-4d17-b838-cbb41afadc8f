#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_EVENT_MODULE == 1)
#include "cpcapi2utils.h"
#include "SipEventPublicationManagerInterface.h"
#include "../phone/PhoneInterface.h"
#include "../account/SipAccountInterface.h"
#include "SipEventPublicationManagerImpl.h"
#include "SipEventPublicationCreationInfo.h"
#include <resip/stack/GenericContents.hxx>
#include <resip/stack/ExtensionParameter.hxx>
#include "../util/ResipConv.h"
#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_EVENT

using namespace CPCAPI2::SipAccount;

namespace CPCAPI2
{
namespace SipEvent
{
SipEventPublicationHandle SipEventPublicationHandleFactory::sNextHandle = 1;
class SipEventPublicationManagerImpl;

SipEventPublicationManagerInterface::SipEventPublicationManagerInterface(Phone* phone)
   : CPCAPI2::EventSource2< CPCAPI2::EventHandler<SipEventPublicationHandler, CPCAPI2::SipAccount::SipAccountHandle, std::string> >(dynamic_cast<PhoneInterface*>(phone)),
     mAccountIf(NULL),
     mNextPublicationHandle(1),
     mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mAccountMapPtr(new AccountMap),
     mAccountMap(*mAccountMapPtr)
{
   mAccountIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(phone));
}

SipEventPublicationManagerInterface::~SipEventPublicationManagerInterface()
{
   mAccountMap.clear();
}

void SipEventPublicationManagerInterface::Release()
{
   delete this;
}

void SipEventPublicationManagerInterface::addEventType(const CPCAPI2::SipAccount::SipAccountHandle& account, const std::string& eventType)
{
   AccountMap::iterator it = mAccountMap.find(account);
   SipEventPublicationManagerImpl* evtMan = (it == mAccountMap.end() ? NULL : it->second);
   if (evtMan == NULL)
   {
      if (SipAccountImpl* acct = mAccountIf->getAccountImpl(account))
      {
         evtMan = new SipEventPublicationManagerImpl(mAccountMapPtr, *acct, this);
         mAccountMap[account] = evtMan;
      }
      else
      {
         mAccountIf->fireError("Invalid account handle for SipEventPublicationManager::setHandler");
         return;
      }
   }
   evtMan->addEventType(eventType);
}

void SipEventPublicationManagerInterface::addAppHandler(SipEventPublicationHandler* handler, CPCAPI2::SipAccount::SipAccountHandle handle, std::string filter)
{
   addEventType(handle, filter);
   CPCAPI2::EventHandler<SipEventPublicationHandler, CPCAPI2::SipAccount::SipAccountHandle, std::string>::addAppHandler(handler, handle, filter);
}

void SipEventPublicationManagerInterface::addSdkObserver(SipEventPublicationHandler* handler, CPCAPI2::SipAccount::SipAccountHandle handle, std::string filter)
{
   addEventType(handle, filter);
   CPCAPI2::EventHandler<SipEventPublicationHandler, CPCAPI2::SipAccount::SipAccountHandle, std::string>::addSdkObserver(handler, handle, filter);
}

void SipEventPublicationManagerInterface::addSdkObserverSafe(const std::shared_ptr<SipEventPublicationHandler>& handler, CPCAPI2::SipAccount::SipAccountHandle handle, std::string filter)
{
   addEventType(handle, filter);
   CPCAPI2::EventHandler<SipEventPublicationHandler, CPCAPI2::SipAccount::SipAccountHandle, std::string>::addSdkObserverSafe(handler, handle, filter);
}

int SipEventPublicationManagerInterface::setHandler(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& eventType,
      SipEventPublicationHandler* handler)
{
   resip::ReadCallbackBase* setHandlerCmd = resip::resip_bind(&SipEventPublicationManagerInterface::setHandlerImpl, this, account, eventType, handler);
   if (handler == NULL || mPhone->getSdkModuleThread().isCurrentThread())
   {
      // removing the handler involves two steps:
      // 1. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);

      // 2. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      executeOnSdkThread(setHandlerCmd);
   }
   else
   {
      postToSdkThread(setHandlerCmd);
   }

   return kSuccess;
}

int SipEventPublicationManagerInterface::setHandlerImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& eventType,
      SipEventPublicationHandler* handler)
{
   std::tuple<SipAccount::SipAccountHandle, std::string> i = std::make_tuple(account, eventType.c_str());
   auto it2 = mHandlers.find(i);
   if (mHandlers.end() != it2)
   {
      removeAppHandler(it2->second, account, eventType.c_str());
   }

   mHandlers[i] = handler;
   if (nullptr != handler)
   {
      addAppHandler(handler, account, eventType.c_str());
   }
   return kSuccess;
}

// Private classes that do the processing on inbound and outbound messages
class SipEventIgnoreFailureResponseIncomingFeature : public resip::DumFeature
{
public:
   SipEventIgnoreFailureResponseIncomingFeature(resip::DialogUsageManager& dum) : resip::DumFeature(dum, dum.dumIncomingTarget()) {}
   virtual ~SipEventIgnoreFailureResponseIncomingFeature() {}
   virtual DumFeature::ProcessingResult process(resip::Message* msg)
   {
      resip::SipMessage* response = dynamic_cast<resip::SipMessage*>(msg);

      if (!response || !response->isResponse() || response->method() != resip::PUBLISH)
      {
         return FeatureDone;
      }

      response->header(resip::h_StatusLine).responseCode() = 200;
      response->header(resip::h_SIPETag).value() = "null";
      response->header(resip::h_Expires).value() = 3600;

      return FeatureDone;
   }

private:
};

SipEventPublicationHandle SipEventPublicationManagerInterface::createPublication(CPCAPI2::SipAccount::SipAccountHandle account, const SipEventPublicationSettings& settings)
{
   SipEventPublicationHandle h = SipEventPublicationHandleFactory::getNext();
   postToSdkThread(resip::resip_bind(&SipEventPublicationManagerInterface::createPublicationImpl, this, account, h, settings));
   return h;
}

SipEventPublicationHandle SipEventPublicationManagerInterface::createPublicationSync(CPCAPI2::SipAccount::SipAccountHandle account, const SipEventPublicationSettings& settings)
{
   SipEventPublicationHandle h = SipEventPublicationHandleFactory::getNext();
   executeOnSdkThread(resip::resip_bind(&SipEventPublicationManagerInterface::createPublicationImpl, this, account, h, settings));
   return h;
}

int SipEventPublicationManagerInterface::createPublicationImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipEventPublicationHandle h, const SipEventPublicationSettings& settings)
{
   DebugLog(<< "SipEventPublicationManagerInterface::createPublicationImpl(): account handle: " << account << " publication handle: " << h);
   AccountMap::iterator it = mAccountMap.find(account);
   if (it != mAccountMap.end())
   {
      SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
      if (!acct)
      {
         cpc::string msg = cpc::string("SipEventPublicationManagerInterface::createPublicationImpl called with invalid account handle: ") + cpc::to_string(account) +
                              " SipEventSubscriptionHandle invalid: " + cpc::to_string(h);
         mAccountIf->fireError(msg);
      }
      else if(!acct->isEnabled())
      {
         cpc::string msg = cpc::string("SipEventPublicationManagerInterface::createPublicationImpl called before account enabled: ") + cpc::to_string(account) +
                              ", SipEventSubscriptionHandle invalid: " + cpc::to_string(h);
         mAccountIf->fireError(msg);
      }
      else if (!it->second->getDum())
      {
         cpc::string msg = cpc::string("SipEventPublicationManagerInterface::createPublicationImpl called but DUM is not available: ") + cpc::to_string(account) +
                              ", SipEventSubscriptionHandle invalid: " + cpc::to_string(h);
         mAccountIf->fireError(msg);
      }
      else
      {
         AccountMap::iterator it = mAccountMapPtr->find(account);
         if (it != mAccountMapPtr->end())
         {
            SipEventPublicationCreationInfo* ci = new SipEventPublicationCreationInfo(it->second->getDum(), it->second);
            ci->account = account;
            ci->eventType = (settings.eventPackage).c_str();
            ci->eventPackageParams = settings.eventPackageParams;
            ci->sdkh = h;
            it->second->addCreationInfo(h, ci);
            it->second->setPublicationSettings(settings);

            if (settings.ignoreFailures)
            {
               it->second->getDum()->addIncomingFeature(resip::SharedPtr<resip::DumFeature>(new SipEventIgnoreFailureResponseIncomingFeature(*it->second->getDum())));
            }
         }
      }
      return kSuccess;
   }
   return kError;
}

int SipEventPublicationManagerInterface::setTarget(SipEventPublicationHandle publication, const cpc::string& targetAddress)
{
   postToSdkThread(resip::resip_bind(&SipEventPublicationManagerInterface::setTargetImpl, this, publication, targetAddress));
   return kSuccess;
}

int SipEventPublicationManagerInterface::setTargetImpl(SipEventPublicationHandle publication, const cpc::string& targetAddress)
{
   SipEventPublicationCreationInfo* ci = getCreationInfo(publication);
   if (ci != NULL)
   {
      resip::NameAddr targetNameAddr;
      if (!ResipConv::stringToAddr(targetAddress, targetNameAddr))
      {
         AccountMap::iterator it = mAccountMap.find(ci->account);
         SipEventPublicationManagerImpl* evtMan = (it == mAccountMap.end() ? NULL : it->second);
         if (evtMan)
         {
            evtMan->fireError(publication, "Failed to parse target URI '" + targetAddress + "'");
            return kSuccess;
         }
         return kError;
      }
      ci->targetAddresses.push_back(targetNameAddr);
   }
   return kSuccess;
}

SipEventPublicationCreationInfo* SipEventPublicationManagerInterface::getCreationInfo(SipEventPublicationHandle h)
{
   AccountMap::iterator itAcct = mAccountMap.begin();
   for (; itAcct != mAccountMap.end(); ++itAcct)
   {
      SipEventPublicationCreationInfo* ci = itAcct->second->getCreationInfo(h);
      if (ci != NULL)
      {
         return ci;
      }
   }

   return NULL;
}

int SipEventPublicationManagerInterface::publish(SipEventPublicationHandle publication, const SipEventState& eventState)
{
   postToSdkThread(resip::resip_bind(&SipEventPublicationManagerInterface::publishImpl, this, publication, eventState));
   return kSuccess;
}

int SipEventPublicationManagerInterface::publishImpl(SipEventPublicationHandle publication, const SipEventState& eventState)
{
   SipEventPublicationCreationInfo* ci = getCreationInfo(publication);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap.find(ci->account);

      if (itAcct != mAccountMap.end())
      {
         SipAccountImpl* acct = mAccountIf->getAccountImpl(ci->account);
         SipEventPublicationManagerImpl* acctPublicationImpl = itAcct->second;

         if (!acct || !hasHandlerRegistered())
         {
            return kError;
         }
         if (acctPublicationImpl->isDumShutdown())
         {
            acctPublicationImpl->fireError(publication, "Cannot start publication. DUM is shutdown");
            return kSuccess;
         }
         if (ci->targetAddresses.empty())
         {
            acctPublicationImpl->fireError(publication, "Cannot publish. No targets have been added");
            return kSuccess;
         }


         resip::PlainContents* contents = new resip::PlainContents(
               resip::Data(eventState.contentUTF8.c_str(), eventState.contentLength),
               resip::Mime((eventState.mimeType).c_str(), (eventState.mimeSubType).c_str()));

         if (ci->publicationState == SipPublicationState_NotStarted)
         {
            resip::NameAddr targetNameAddr = ci->targetAddresses.front();

            resip::SharedPtr<resip::DialogUsageManager> dum = acct->getDUM();
            resip::SharedPtr<resip::SipMessage> pubMessage = dum->makePublication(
                  targetNameAddr,
                  *(contents->getContents()),
                  ci->eventType,
                  acctPublicationImpl->getPublicationSettings().expiresSeconds,
                  ci);

            for (cpc::vector<Parameter>::const_iterator it = ci->eventPackageParams.begin(); it != ci->eventPackageParams.end(); it++)
            {
               resip::ExtensionParameter eventPackageParam(it->name.c_str());
               pubMessage->header(resip::h_Event).param(eventPackageParam) = it->value;
            }

            dum->send(pubMessage);

            ci->publicationState = SipPublicationState_Active;
         }
         else if (ci->publicationState == SipPublicationState_Active)
         {
            // bliu: dumClientPublicationHandle needs to be set in case of a success and failure
            if (ci->dumClientPublicationHandle.isValid())
            {
               ci->dumClientPublicationHandle->update(contents);
            }
            else
            {
               // bliu: only keep the latest event state
               ci->queuedContents.reset(contents);
            }
         }
      }
   }
   return kSuccess;
}

int SipEventPublicationManagerInterface::end(SipEventPublicationHandle publication)
{
   postToSdkThread(resip::resip_bind(&SipEventPublicationManagerInterface::endImpl, this, publication));
   return kSuccess;
}

int SipEventPublicationManagerInterface::endImpl(SipEventPublicationHandle publication)
{
   SipEventPublicationCreationInfo* ci = getCreationInfo(publication);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap.find(ci->account);
      if (itAcct != mAccountMap.end())
      {
         //SipEventPublicationManagerImpl* acct = itAcct->second;
         if (ci->dumClientPublicationHandle.isValid())
         {
            ci->dumClientPublicationHandle->end();
         }
         if (ci->dumServerPublicationHandle.isValid())
         {
            ci->dumServerPublicationHandle->end();
         }
      }

      ci->publicationState = SipPublicationState_Terminated;
   }
   return kSuccess;
}

int SipEventPublicationManagerInterface::refresh(SipEventPublicationHandle publication)
{
   postToSdkThread(resip::resip_bind(&SipEventPublicationManagerInterface::refreshImpl, this, publication));
   return kSuccess;
}

int SipEventPublicationManagerInterface::refreshImpl(SipEventPublicationHandle publication)
{
   SipEventPublicationCreationInfo* ci = getCreationInfo(publication);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap.find(ci->account);
      if (itAcct != mAccountMap.end())
      {
         //SipEventPublicationManagerImpl* acct = itAcct->second;
         if (ci->dumClientPublicationHandle.isValid())
         {
            ci->dumClientPublicationHandle->refresh();
         }
      }
   }
   else
   {
      DebugLog(<< "Invalid publication handle for SipEventPublicationManager::refresh");
      return kError;
   }

   return kSuccess;
}

bool SipEventPublicationManagerInterface::recreatePublication(CPCAPI2::SipAccount::SipAccountHandle account, SipEventSubscriptionHandle publication)
{
   DebugLog(<< "SipEventPublicationManagerInterface::recreatePublication(): recreate publication, account: " << account << ", publication: " << publication);

   SipAccountImpl* accountImpl = mAccountIf->getAccountImpl(account);
   if (!accountImpl)
   {
      DebugLog(<< "SipEventPublicationManagerInterface::recreatePublication(): invalid account handle, account: " << account << ", publication: " << publication);
      return false;
   }

   SipEventPublicationCreationInfo* ciOld = getCreationInfo(publication);
   if (!ciOld)
   {
      DebugLog(<< "SipEventPublicationManagerInterface::recreatePublication(): invalid publication handle, account: " << account << ", publication: " << publication);
      return false;
   }

   AccountMap::iterator it = mAccountMap.find(account);

   if (it == mAccountMap.end())
   {
      DebugLog(<< "SipEventPublicationManagerInterface::recreatePublication(): no publication impl found, account: " << account << ", publication: " << publication);
      return false;
   }

   if (!accountImpl->isEnabled())
   {
      DebugLog(<< "SipEventPublicationManagerInterface::recreatePublication(): account not enabled, account: " << account << ", publication: " << publication);
      return false;
   }

   SipEventPublicationManagerImpl* publicationImpl = it->second;
   if (!publicationImpl->getDum())
   {
      DebugLog(<< "SipEventPublicationManagerInterface::recreatePublication(): DUM not available for publication impl, account: " << account << ", publication: " << publication);
      return false;
   }

   // Note that the after the call to cleanup below, the creation info will cease to exist, though this will not happen
   // immediately, as the destruction is triggered by the deletion of the resip ClientPublication dialog.
   //
   // We need to set the displaced flag in the stale creation info as the publication impl will now be
   // associated with the new creation info. Otherwise it would result in the deletetion of the publication
   // impl when the stale creation info is destryoyed.

   std::vector<resip::NameAddr> targetAddresses = ciOld->targetAddresses;

   ciOld->displaced = true;
   publicationImpl->removeCreationInfo(publication);
   ciOld->cleanup();

   if (createPublicationImpl(account, publication, publicationImpl->getPublicationSettings()) != kSuccess)
   {
      DebugLog(<< "SipEventPublicationManagerInterface::recreatePublication(): error during publication creation, account: " << account << ", publication: " << publication);
      return false;
   }

   SipEventPublicationCreationInfo* ci = getCreationInfo(publication);
   if (!ci)
   {
      DebugLog(<< "SipEventPublicationManagerInterface::recreatePublication(): invalid publication handle after creation info reset, account: " << account << ", publication: " << publication);
      return false;
   }

   ci->targetAddresses = targetAddresses;

   return true;
}

std::ostream& operator<<(std::ostream& os, const PublicationSuccessEvent& evt)
{
   return os << "PublicationSuccessEvent";
}

std::ostream& operator<<(std::ostream& os, const PublicationFailureEvent& evt)
{
   return os << "PublicationFailureEvent";
}

std::ostream& operator<<(std::ostream& os, const PublicationRemoveEvent& evt)
{
   return os << "PublicationRemoveEvent";
}

std::ostream& operator<<(std::ostream& os, const PublicationErrorEvent& evt)
{
   return os << "PublicationErrorEvent";
}

std::ostream& operator<<(std::ostream& os, const PublicationManagerReadyEvent& evt)
{
   return os << "PublicationManagerReadyEvent";
}

}

}

#endif
