#pragma once

#if !defined(__CPCAPI2_COMPOSER_ACTIVE_STATE_H__)
#define __CPCAPI2_COMPOSER_ACTIVE_STATE_H__

#include "ComposerBaseState.h"

namespace CPCAPI2
{
namespace IsComposing
{

class ComposerActiveState : public ComposerBaseState
{
public:
   virtual void onEnterState(IsComposingManager* manager, IsComposingInfo* info);

   virtual void onIsComposingMessage(IsComposingManager* manager, IsComposingInfo* info);
   virtual void onIdleTimeout(IsComposingManager* manager, IsComposingInfo* info);
   virtual void onRefreshTimeout(IsComposingManager* manager, IsComposingInfo* info);
   virtual void onMessageSent(IsComposingManager* manager, IsComposingInfo* info);

   virtual void onLeaveState(IsComposingManager* manager, IsComposingInfo* info);

   static ComposerActiveState instance;
};

}
}

#endif // __CPCAPI2_COMPOSER_ACTIVE_STATE_H__