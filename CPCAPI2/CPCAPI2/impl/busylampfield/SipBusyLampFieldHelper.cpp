#include "SipBusyLampFieldHelper.h"
#include <assert.h>

using namespace CPCAPI2::SipDialogEvent;

namespace CPCAPI2
{
namespace SipBusyLampField
{

const cpc::string SipBusyLampFieldHelper::SIP_RENDERING_PARAM = "+sip.rendering";
const cpc::string SipBusyLampFieldHelper::SIP_CALL_PARK_PARAM = "+sip.call-parked";
const cpc::string SipBusyLampFieldHelper::BARGE_MODE_PARAM = "barge-mode";
const cpc::string SipBusyLampFieldHelper::ALERT_MODE_PARAM = "alert";
const cpc::string SipBusyLampFieldHelper::SILENT_ALERT_MODE = "silent";

cpc::string SipBusyLampFieldHelper::getBargeInModeString(BargeInMode bargeInMode)
{
   switch(bargeInMode)
   {
   case BargeInMode_Normal:
      return "normal";
   case BargeInMode_Whisper:
      return "whisper";
   case BargeInMode_Listen:
      return "listen";
   default:
      assert(false);
   }

   return "";
}

cpc::string SipBusyLampFieldHelper::getParameterValue(const ParticipantInfo& participant, const cpc::string& name)
{
   for (cpc::vector<Parameter>::const_iterator it = participant.target.params.begin(); it != participant.target.params.end(); it++)
   {
      if (it->name == name)
      {
         // Parameter found
         return it->value;
      }

   }

   // Parameter not found
   return "";
}

bool SipBusyLampFieldHelper::isHeld(const DialogInfo& dialogInfo)
{
   return getParameterValue(dialogInfo.localParticipant, SIP_RENDERING_PARAM) == "no" ||
          getParameterValue(dialogInfo.remoteParticipant, SIP_RENDERING_PARAM) == "no";
}

bool SipBusyLampFieldHelper::isParked(const DialogInfo& dialogInfo)
{
   return getParameterValue(dialogInfo.localParticipant, SIP_CALL_PARK_PARAM) == "yes" ||
          getParameterValue(dialogInfo.remoteParticipant, SIP_CALL_PARK_PARAM) == "yes";
}

bool SipBusyLampFieldHelper::isCall(const CPCAPI2::SipDialogEvent::DialogInfo& dialogInfo)
{
   return !dialogInfo.id.empty() && dialogInfo.stateInfo.state != DialogState_NotSpecified && !dialogInfo.dialogId.callId.empty();
}

}
}
