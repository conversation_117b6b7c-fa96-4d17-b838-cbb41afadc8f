#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTNETWORKTRANSPORT_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTNETWORKTRANSPORT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  enum class SipAccountNetworkTransport
  {
    None,
    WiFi,
    Wwan
  };

  inline std::string to_string(SipAccountNetworkTransport value)
  {
    switch (value)
    {
      case SipAccountNetworkTransport::None:
        return "None";
      case SipAccountNetworkTransport::WiFi:
        return "WiFi";
      case SipAccountNetworkTransport::Wwan:
        return "WWAN";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, SipAccountNetworkTransport value)
  {
    switch (value)
    {
      case SipAccountNetworkTransport::None:
        os << "None";
        break;
      case SipAccountNetworkTransport::WiFi:
        os << "WiFi";
        break;
      case SipAccountNetworkTransport::Wwan:
        os << "WWAN";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const SipAccountNetworkTransport& e)
  {
    switch (e)
    {
      case SipAccountNetworkTransport::None:
        j = "None";
        break;
      case SipAccountNetworkTransport::WiFi:
        j = "WiFi";
        break;
      case SipAccountNetworkTransport::Wwan:
        j = "WWAN";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, SipAccountNetworkTransport& e)
  {
      if (0 == j.get<std::string>().compare("None"))
    {
      e = SipAccountNetworkTransport::None;
    }
    else if (0 == j.get<std::string>().compare("WiFi"))
    {
      e = SipAccountNetworkTransport::WiFi;
    }
    else if (0 == j.get<std::string>().compare("WWAN"))
    {
      e = SipAccountNetworkTransport::Wwan;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum SipAccountNetworkTransport"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTNETWORKTRANSPORT_H
