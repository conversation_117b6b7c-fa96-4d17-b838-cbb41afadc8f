#pragma once
#ifndef __CPCAPI2_BIEVENTS_DRUID_CHECK_REQUEST_H__
#define __CPCAPI2_BIEVENTS_DRUID_CHECK_REQUEST_H__

#include <list>
#include <bievents/BIEventsTypes.h>
#include <util/strettorpc/RPCRequest.h>

#include "BIEventsInternalTypes.h"

namespace CPCAPI2
{
   namespace BIEvents
   {
      class BIDruidCheckRequest : public CPCAPI2::RPCRequest
      {
      public:
         // Used for an initial request
         BIDruidCheckRequest(
            const std::string& sessionID,
            const std::string& authToken,
            const std::string& buildStamp,
            const std::string& strettoUserNameOrID,
            const std::string& druid );
      };
   }
}

#endif // __CPCAPI2_BIEVENTS_DRUID_CHECK_REQUEST_H__