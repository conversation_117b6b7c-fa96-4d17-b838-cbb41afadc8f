#pragma once

#if !defined(CPCAPI2_AUTH_SERVER_DB_ACCESS)
#define CPCAPI2_AUTH_SERVER_DB_ACCESS

#include "AuthServerBackend.h"

#include <cpcstl/string.h>
#include <memory>
#include <shared_mutex>	

#include <soci/soci.h>

namespace webrtc
{
   class RWLockWrapper;
}

namespace CPCAPI2
{
namespace AuthServer
{
class DbAccess : public AuthServerBackend
{
public:
   DbAccess(const size_t num_threads = 1);
   virtual ~DbAccess();

   int initialize(const cpc::string& dbfile) override;
   int addUser(const cpc::string& username, const cpc::string& password_hash) override;
   int updatePassword(const cpc::string& username, const cpc::string& new_password_hash) override;
   int queryUser(const cpc::string& username, const cpc::string& password_hash, bool& userExists, cpc::string& storedPassword) override;
   int flushUsers() override;

private:
   size_t mNumThreads;
   soci::connection_pool mDbPool;
   // Writes lock the sqlite3 db. We can issue parallel reads, but not writes
   std::shared_mutex mLock;
};
}
}

#endif // CPCAPI2_AUTH_SERVER_DB_ACCESS
