#include "jsonrpc/StringSerialization.h"
#include "jsonrpc/OptionalSerialization.h"
#include <any>
#include "jsonrpc/VectorSerialization.h"
#include <nlohmann/json.hpp>
#include "AudioAudioCodecInfo.h"

#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIOCODECLISTUPDATEDEVENT_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIOCODECLISTUPDATEDEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
  class AudioAudioCodecListUpdatedEvent
  {
  public:
    std::optional<cpc::vector<AudioAudioCodecInfo>> codecInfo;

    AudioAudioCodecListUpdatedEvent() = default;

    AudioAudioCodecListUpdatedEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const AudioAudioCodecListUpdatedEvent& o)
    {
      j = nlohmann::json::object();
       if (o.codecInfo) { j["codecInfo"] = *(o.codecInfo); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, AudioAudioCodecListUpdatedEvent& o)
    {
     if (j.contains("codecInfo")) { o.codecInfo = j.at("codecInfo"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIOCODECLISTUPDATEDEVENT_H
