#include "brand_branded.h"

#include "broadsoftxsi/BroadsoftXsi.h"

#if (CPCAPI2_BRAND_BROADSOFT_XSI_MODULE == 1)
#include "BroadsoftXsiInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace BroadsoftXsi
   {
      BroadsoftXsi* BroadsoftXsi::getInterface(CPCAPI2::Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_BROADSOFT_XSI_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<BroadsoftXsiInterface>(phone, "BroadsoftXsi");
#else
         return NULL;
#endif
      }
   }
}
