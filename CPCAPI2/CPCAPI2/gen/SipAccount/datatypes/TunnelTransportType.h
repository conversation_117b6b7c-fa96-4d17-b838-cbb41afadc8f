#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_TUNNELTRANSPORTTYPE_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_TUNNELTRANSPORTTYPE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  enum class TunnelTransportType
  {
    Auto,
    Udp,
    Tcp,
    Tls,
    Dtls
  };

  inline std::string to_string(TunnelTransportType value)
  {
    switch (value)
    {
      case TunnelTransportType::Auto:
        return "AUTO";
      case TunnelTransportType::Udp:
        return "UDP";
      case TunnelTransportType::Tcp:
        return "TCP";
      case TunnelTransportType::Tls:
        return "TLS";
      case TunnelTransportType::Dtls:
        return "DTLS";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, TunnelTransportType value)
  {
    switch (value)
    {
      case TunnelTransportType::Auto:
        os << "AUTO";
        break;
      case TunnelTransportType::Udp:
        os << "UDP";
        break;
      case TunnelTransportType::Tcp:
        os << "TCP";
        break;
      case TunnelTransportType::Tls:
        os << "TLS";
        break;
      case TunnelTransportType::Dtls:
        os << "DTLS";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const TunnelTransportType& e)
  {
    switch (e)
    {
      case TunnelTransportType::Auto:
        j = "AUTO";
        break;
      case TunnelTransportType::Udp:
        j = "UDP";
        break;
      case TunnelTransportType::Tcp:
        j = "TCP";
        break;
      case TunnelTransportType::Tls:
        j = "TLS";
        break;
      case TunnelTransportType::Dtls:
        j = "DTLS";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, TunnelTransportType& e)
  {
      if (0 == j.get<std::string>().compare("AUTO"))
    {
      e = TunnelTransportType::Auto;
    }
    else if (0 == j.get<std::string>().compare("UDP"))
    {
      e = TunnelTransportType::Udp;
    }
    else if (0 == j.get<std::string>().compare("TCP"))
    {
      e = TunnelTransportType::Tcp;
    }
    else if (0 == j.get<std::string>().compare("TLS"))
    {
      e = TunnelTransportType::Tls;
    }
    else if (0 == j.get<std::string>().compare("DTLS"))
    {
      e = TunnelTransportType::Dtls;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum TunnelTransportType"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_TUNNELTRANSPORTTYPE_H
