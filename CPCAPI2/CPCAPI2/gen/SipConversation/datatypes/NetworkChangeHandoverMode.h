#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_NETWORKCHANGEHANDOVERMODE_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_NETWORKCHANGEHANDOVERMODE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  enum class NetworkChangeHandoverMode
  {
    Reinvite,
    Starcode
  };

  inline std::string to_string(NetworkChangeHandoverMode value)
  {
    switch (value)
    {
      case NetworkChangeHandoverMode::Reinvite:
        return "Reinvite";
      case NetworkChangeHandoverMode::Starcode:
        return "Starcode";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, NetworkChangeHandoverMode value)
  {
    switch (value)
    {
      case NetworkChangeHandoverMode::Reinvite:
        os << "Reinvite";
        break;
      case NetworkChangeHandoverMode::Starcode:
        os << "Starcode";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const NetworkChangeHandoverMode& e)
  {
    switch (e)
    {
      case NetworkChangeHandoverMode::Reinvite:
        j = "Reinvite";
        break;
      case NetworkChangeHandoverMode::Starcode:
        j = "Starcode";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, NetworkChangeHandoverMode& e)
  {
      if (0 == j.get<std::string>().compare("Reinvite"))
    {
      e = NetworkChangeHandoverMode::Reinvite;
    }
    else if (0 == j.get<std::string>().compare("Starcode"))
    {
      e = NetworkChangeHandoverMode::Starcode;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum NetworkChangeHandoverMode"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_NETWORKCHANGEHANDOVERMODE_H
