#include "LicensingClientManagerInterface.h"
#include "LicensingClientManagerImpl.h"
#include "LicensingClientInfo.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"
#include "../util/MachineIdentification.h"
#include "licensekey/HardwareHash.h"
#include "brand_branded.h"

#include <ctime>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::LICENSING

using namespace CPCAPI2::SipAccount;

namespace CPCAPI2
{
namespace Licensing
{

LicensingClientManagerInterface::LicensingClientManagerInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mShutdown(false)
{
   StackLog(<< "LicensingClientManagerInterface::LicensingClientManagerInterface(): " << this << " phone: " << phone);
}

LicensingClientManagerInterface::~LicensingClientManagerInterface()
{
   StackLog(<< "LicensingClientManagerInterface::~LicensingClientManagerInterface(): " << this << " phone: " << mPhone);

   // Delete all the manager instances and clear the map
   for (ManagerImplMap::iterator it = mManagerImplMap.begin(); it != mManagerImplMap.end(); it++)
   {
      LicensingClientManagerImpl* licensingClientManager = it->second;

      // Remove the SDK observers
      for (std::set<LicensingClientHandler*>::iterator it = mSdkObservers.begin(); it != mSdkObservers.end(); ++it)
      {
         LicensingClientHandler* sdkObserver = *it;
         licensingClientManager->removeSdkObserver(sdkObserver);
      }

      delete licensingClientManager;
   }
   mManagerImplMap.clear();

   mShutdown = true;
   interruptProcess();
}

void LicensingClientManagerInterface::Release()
{
   delete this;
}

void LicensingClientManagerInterface::addSdkObserver(LicensingClientHandler* observer)
{
   mSdkObservers.insert(observer);

   post(resip::resip_bind(&LicensingClientManagerInterface::addSdkObserverImpl, this, observer));
}

void LicensingClientManagerInterface::removeSdkObserver(LicensingClientHandler* observer)
{
   mSdkObservers.erase(observer);

   post(resip::resip_bind(&LicensingClientManagerInterface::removeSdkObserverImpl, this, observer));
}

void LicensingClientManagerInterface::addSdkObserverImpl(LicensingClientHandler* observer)
{
   for (ManagerImplMap::const_iterator it = mManagerImplMap.begin(); it != mManagerImplMap.end(); it++)
   {
      it->second->addSdkObserver(observer);
   }
}

void LicensingClientManagerInterface::removeSdkObserverImpl(LicensingClientHandler* observer)
{
   for (ManagerImplMap::const_iterator it = mManagerImplMap.begin(); it != mManagerImplMap.end(); it++)
   {
      it->second->removeSdkObserver(observer);
   }
}

int LicensingClientManagerInterface::setHandler(LicensingClientHandle client, LicensingClientHandler* handler)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&LicensingClientManagerInterface::setHandlerImpl, this, client, handler);

   if (handler == NULL)
   {
      mPhone->getSdkModuleThread().execute(f);
      process(-1);
   }
   else
   {
      post(f);
   }

   return kSuccess;
}

void LicensingClientManagerInterface::setHandlerImpl(LicensingClientHandle client, LicensingClientHandler* handler)
{
   // Retrieve the manager associated with the account specified
   LicensingClientManagerImpl* licensingClientManager = getLicensingClientManager(client);
   assert(licensingClientManager);

   if (handler != NULL)
   {
      // Register the SDK observers
      for (std::set<LicensingClientHandler*>::iterator it = mSdkObservers.begin(); it != mSdkObservers.end(); ++it)
      {
         LicensingClientHandler* sdkObserver = *it;
         licensingClientManager->addSdkObserver(sdkObserver);
      }
   }

   licensingClientManager->setHandler(handler);
}

LicensingClientHandle LicensingClientManagerInterface::create()
{
   LicensingClientHandle clientHandle = LicensingClientInfo::nextLicensingClientHandle++;
   post(resip::resip_bind(&LicensingClientManagerInterface::createImpl2, this, clientHandle));
   return clientHandle;
}

LicensingClientHandle LicensingClientManagerInterface::createImpl()
{
   LicensingClientHandle clientHandle = LicensingClientInfo::nextLicensingClientHandle++;
   createImpl2(clientHandle);
   return clientHandle;
}

void LicensingClientManagerInterface::createImpl2(LicensingClientHandle client)
{
   // Retrieve the manager associated with the account specified
   LicensingClientManagerImpl* licensingClientManager = getLicensingClientManager(client);
   if (!licensingClientManager)
   {
      // No manager associated with the client

      // Create a new manager
      licensingClientManager = new LicensingClientManagerImpl(client, *this); // Destroyed in the destructor of this class

      // Keep mapping account -> manager
      mManagerImplMap[client] = licensingClientManager;
   }

   // Create a new licensing client set using the newly allocated handle
   LicensingClientInfo* clientInfo = licensingClientManager->createLicensingClientInfo(client);
   assert(clientInfo);
}

int LicensingClientManagerInterface::applySettings(LicensingClientHandle client, const LicensingClientSettings& settings)
{
   DebugLog(<< __FUNCTION__ << " " << client << " " << settings);

   post(resip::resip_bind(&LicensingClientManagerInterface::applySettingsImpl, this, client, settings));
   return kSuccess;
}

void LicensingClientManagerInterface::applySettingsImpl(LicensingClientHandle client, const LicensingClientSettings& settings)
{
   // Get info on the client
   LicensingClientInfo* clientInfo = getLicensingClientInfo(client);
   assert(clientInfo);

   // Store the settings
   clientInfo->mSettings = settings;

   // Replace with the default settings if needed
   if (clientInfo->mSettings.brand.empty())
   {
      clientInfo->mSettings.brand = CPCAPI2_BRAND_FULL_NAME;
   }
   if (clientInfo->mSettings.licenseUrl.empty())
   {
      clientInfo->mSettings.licenseUrl = CPCAPI2_BRAND_LICENSE_URL;
   }
#ifdef CPCAPI2_BRAND_LICENSE_URL_SECONDARY
   if (clientInfo->mSettings.licenseUrlSecondary.empty())
   {
      clientInfo->mSettings.licenseUrlSecondary = CPCAPI2_BRAND_LICENSE_URL_SECONDARY;
   }
#endif

   if (!mPhone->hasFilePermission(Permission_ReadFiles, settings.licenseDocumentLocation))
   {
      mPhone->requestPermission(0, Permission_ReadFiles);
   }
}

bool LicensingClientManagerInterface::appResourceSignatureCheckEnabled() const
{
   return LicensingClientManagerImpl::appResourceSignatureCheckEnabled();
}

int LicensingClientManagerInterface::validateLicenses(LicensingClientHandle client)
{
   DebugLog(<< __FUNCTION__ << " " << client);
   post(resip::resip_bind(&LicensingClientManagerInterface::validateLicensesImpl, this, client));
   return kSuccess;
}

void LicensingClientManagerInterface::validateLicensesImpl(LicensingClientHandle client)
{
   // Get info on the client
   LicensingClientInfo* clientInfo = getLicensingClientInfo(client);
   assert(clientInfo);

   // Get the manager associated with with the client
   LicensingClientManagerImpl* licensingClientManager = getLicensingClientManager(client);
   assert(licensingClientManager);

   // Make sure that if a license key is specified that the license document location is specified
   if (!clientInfo->mSettings.licenseKeys.empty() && clientInfo->mSettings.licenseDocumentLocation.empty())
   {
      licensingClientManager->fireError(client, "No license document location specified");
      return;
   }

   if (!mPhone->hasFilePermission(Permission_ReadFiles, clientInfo->mSettings.licenseDocumentLocation))
   {
      mPhone->requestPermission(0, Permission_ReadFiles);
      mPendingLicenseValidations.insert(client);
      WarningLog(<< "Cannot open license document for validation.");
      return;
   }

   // Validate the client's licenses
   licensingClientManager->validateLicenses(clientInfo);
}

int LicensingClientManagerInterface::checkValidLicenseKeyFormat(const cpc::string& licenseKey)
{
   if (Licensing::PerformKeySanityCheck(licenseKey.c_str()))
      return kSuccess;
   return kError;
}

int LicensingClientManagerInterface::destroy(LicensingClientHandle client)
{
   post(resip::resip_bind(&LicensingClientManagerInterface::destroyImpl, this, client));
   return kSuccess;
}

void LicensingClientManagerInterface::destroyImpl(LicensingClientHandle client)
{
   mPendingLicenseValidations.erase(client);

   // Get info on the client
   LicensingClientInfo* clientInfo = getLicensingClientInfo(client);
   assert(clientInfo);

   // Get the manager associated with with the client
   LicensingClientManagerImpl* licensingClientManager = getLicensingClientManager(client);
   assert(licensingClientManager);

   // Destroy the client's info object
   LicensingClientInfo* removedClientInfo = licensingClientManager->removeLicensingClientInfo(clientInfo->mHandle);
   assert(removedClientInfo == clientInfo);
   delete clientInfo;
}

int LicensingClientManagerInterface::getHardwareId(cpc::string& hardwareId)
{
   hardwareId = cpc::string(MachineIdentification::GetHardwareId().c_str());
   return kSuccess;
}

int LicensingClientManagerInterface::getHardwareId(LicensingClientHandle client, cpc::string& hardwareId)
{
   // we don't really need the client here since hardware id is tied to the machine
   return getHardwareId(hardwareId);
}

int LicensingClientManagerInterface::getHardwareHash(cpc::string& hardwareHash)
{
   HardwareHash hh;
   hardwareHash = cpc::string(hh.GenerateHardwareHash().c_str());
   return kSuccess;
}

LicensingClientManagerImpl* LicensingClientManagerInterface::getLicensingClientManager(LicensingClientHandle client) const
{
   ManagerImplMap::const_iterator it = mManagerImplMap.find(client);
   return (it != mManagerImplMap.end()) ? it->second : NULL;
}

LicensingClientInfo* LicensingClientManagerInterface::getLicensingClientInfo(LicensingClientHandle client) const
{
   // Browse through all the registered accounts to find
   // the manager associated with the handle specified
   for (ManagerImplMap::const_iterator it = mManagerImplMap.begin(); it != mManagerImplMap.end(); it++)
   {
      LicensingClientManagerImpl* licensingClientManager = it->second;
      LicensingClientInfo* clientInfo = licensingClientManager->getLicensingClientInfo(client);
      if (clientInfo)
      {
         return clientInfo;
      }
   }

   return NULL;
}

int LicensingClientManagerInterface::setBrandedExpiry(LicensingClientSettings& settings) const
{
#if (CPCAPI2_BRAND_LICENSE_EXPIRY_PEROID > 0)
   DebugLog(<< "Build date " << __DATE__);
   std::string date = __DATE__; // MMM DD YYYY

   struct std::tm tm;
   tm.tm_year = std::stoi(date.substr(7, 4)) - 1900;

   std::string month = date.substr(0, 3);
   if (0 == month.compare("Jan")) tm.tm_mon = 0;
   else if (0 == month.compare("Feb")) tm.tm_mon = 1;
   else if (0 == month.compare("Mar")) tm.tm_mon = 2;
   else if (0 == month.compare("Apr")) tm.tm_mon = 3;
   else if (0 == month.compare("May")) tm.tm_mon = 4;
   else if (0 == month.compare("Jun")) tm.tm_mon = 5;
   else if (0 == month.compare("Jul")) tm.tm_mon = 6;
   else if (0 == month.compare("Aug")) tm.tm_mon = 7;
   else if (0 == month.compare("Sep")) tm.tm_mon = 8;
   else if (0 == month.compare("Oct")) tm.tm_mon = 9;
   else if (0 == month.compare("Nov")) tm.tm_mon = 10;
   else if (0 == month.compare("Dec")) tm.tm_mon = 11;

   tm.tm_mday = std::stoi(date.substr(4, 2));
   tm.tm_hour = 0;
   tm.tm_min = 0;
   tm.tm_sec = 0;
   tm.tm_wday = 0;
   tm.tm_isdst	= -1;

   tm.tm_mday += CPCAPI2_BRAND_LICENSE_EXPIRY_PEROID;
   mktime(&tm);
   DebugLog(<< "Expiry date " << asctime(&tm));

   settings.expiryYear = 1900 + tm.tm_year;
   settings.expiryMonth = 1 + tm.tm_mon;
   settings.expiryDay = tm.tm_mday;
   DebugLog(<< "Settings expiry date " << settings.expiryDay << "/" << settings.expiryMonth << "/" <<  settings.expiryYear);
#elif (CPCAPI2_BRAND_SDK_LICENSING == 1)
   settings.expiryYear = CPCAPI2_BRAND_LICENSE_EXPIRY_YEAR;
   settings.expiryMonth = CPCAPI2_BRAND_LICENSE_EXPIRY_MONTH;
   settings.expiryDay = CPCAPI2_BRAND_LICENSE_EXPIRY_DAY;
#endif
   return kSuccess;
}

int LicensingClientManagerInterface::process(unsigned int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return kModuleDisabled;
   }

   for (resip::ReadCallbackBase* fp = mCallbackFifo.getNext(timeout); fp; fp = mCallbackFifo.getNext(kBlockingModeNonBlocking))
   {
      (*fp)();
      delete fp;

      if (mShutdown) return kModuleDisabled;
   }

   return kSuccess;
}

void LicensingClientManagerInterface::interruptProcess()
{
   mCallbackFifo.add(new resip::ReadCallbackNoOp);
}

void LicensingClientManagerInterface::setCallbackHook(void(*cbHook)(void*), void* context)
{
   mCbHook = std::bind(cbHook, context);
}

void LicensingClientManagerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void LicensingClientManagerInterface::postCallback(resip::ReadCallbackBase* command)
{
   mCallbackFifo.add(command);
   if (mCbHook) { mCbHook(); }
}

bool LicensingClientManagerInterface::checkValidateLicensesAttempted() const
{
   return LicensingClientManagerImpl::ensureValidateAttempted();
}

void LicensingClientManagerInterface::resetValidateLicensesAttempted()
{
   LicensingClientManagerImpl::resetValidateAttempted();
}

void LicensingClientManagerInterface::onPermissionGranted(int requestCode, CPCAPI2::Permission permission)
{
  if (Permission_ReadFiles == permission)
  {
    InfoLog(<< "Granted read files permission. Validating license document.");
    for (PendingValidationList::iterator it = mPendingLicenseValidations.begin(); it != mPendingLicenseValidations.end(); ++it)
    {
      validateLicenses(*it);
    }
    mPendingLicenseValidations.clear();
  }
}

std::ostream& operator<<(std::ostream& os, const LicensingClientSettings& settings)
{
   os << "LicenseClientSettings:";
   for (cpc::vector<cpc::string>::const_iterator it = settings.licenseKeys.begin(); it != settings.licenseKeys.end(); ++it)
   {
      os << " license: " << *it;
   }
   return os;
}

}
}
