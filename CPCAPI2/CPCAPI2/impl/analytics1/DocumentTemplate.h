#pragma once

#ifndef __CPCAPI2_ANALYTICSDOCUMENTTEMPLATE_H__
#define __CPCAPI2_ANALYTICSDOCUMENTTEMPLATE_H__

namespace CPCAPI2
{
   namespace Analytics
   {
      /**
       * Template :
       * 1) "value=" attributes will get replaced with contents from a similar position in the registry.
       * 2) "data" elements which have no corresponding entry in the registry will not be written.
       *    Therefore all possible data elements should be listed under a specific entry.
       * 3) [xyz]_list elements should only have a single entry underneath them, which includes all 
       *    possible data elements underneath it. See #2 above.
       * 4) If a [xyz]_list contains no elements, it will not be added to the result document.
       * 5) The type of the field is indicated in the value attribute (at present this is not used)
       * presence of videoIn or videoOut means it was a video call. Omit for audio-only calls.
      */
      static const char *Template =
         "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"
         "<cpc_usage_report version=\"1.0\" start=\"int_64\" end=\"int_64\">"
         "  <general>"
         "     <data name=\"deviceUUID\"            value=\"string\"/>"
         "     <data name=\"clientVersion\"         value=\"string\"/>"
         "     <data name=\"installationDate\"      value=\"string\"/>"
         "     <data name=\"osType\"                value=\"string\"/>"
         "     <data name=\"osVersion\"             value=\"string\"/>"
         "     <data name=\"hardwareModel\"         value=\"string\"/>"
         "     <data name=\"clientPublicIpAddress\" value=\"string\"/>"
         "     <data name=\"clientLaunchTime\"      value=\"int_64\"/>"
         "     <data name=\"xmlTemplateVersion\"    value=\"string\"/>"
         "     <data name=\"language\"              value=\"string\"/>"
         "     <data name=\"timezone\"              value=\"string\"/>"
         "     <data name=\"serialNumber\"          value=\"string\"/>"
         "  </general>"
         "  <settings_data>"
         "     <data name=\"runInBackground\"                value=\"bool\"/>"  // obsolete?
         "     <data name=\"allowDataOverMobileDataNetwork\" value=\"bool\"/>"  // obsolete?
         "     <data name=\"allowVoipOverMobileDataNetwork\" value=\"bool\"/>"  // obsolete?
         "     <data name=\"directoryConfiguration\"         value=\"bool\"/>"
         "     <data name=\"historyDefaultAccount\"          value=\"bool\"/>"
         "     <account_list>"
         "        <account id=\"#6:SipAccountInfo_SettingsData\">"
         "           <data name=\"domain\"                 value=\"string\"/>"
         "           <data name=\"username\"               value=\"string\"/>"
         "           <data name=\"outboundProxy\"          value=\"string\"/>"
         "           <data name=\"protocol\"               value=\"string\"/>"
         "           <data name=\"enabled\"                value=\"bool\"/>"
         "           <data name=\"keepAliveCell\"          value=\"bool\"/>"    // obsolete?
         "           <data name=\"keepAliveCellInterval\"  value=\"int\"/>"     // obsolete?
         "           <data name=\"sipRefreshCellInterval\" value=\"int\"/>"     // mobile only
         "           <data name=\"keepAliveWifi\"          value=\"bool\"/>"    // obsolete?
         "           <data name=\"keepAliveWifiInterval\"  value=\"int\"/>"     // obsolete?
         "           <data name=\"sipRefreshWifiInterval\" value=\"int\"/>"     // mobile only
         "           <data name=\"natTraversalWifiICE\"    value=\"bool\"/>"
         "           <data name=\"natTraversalWifiSTUN\"   value=\"bool\"/>"
         "           <data name=\"natTraversalCellICE\"    value=\"bool\"/>"
         "           <data name=\"natTraversalCellSTUN\"   value=\"bool\"/>"
         "           <data name=\"sipSimpleSupported\"     value=\"bool\"/>"
         "           <data name=\"signalingTransport\"     value=\"string\"/>"
         "           <data name=\"mediaEncryption\"        value=\"bool\"/>"
         "           <data name=\"serviceType\"            value=\"string\"/>"
         "           <data name=\"mediaInactivityIntervalSeconds\"    value=\"int\"/>"
         "           <data name=\"signalingDscp\"                     value=\"int\"/>"
         "           <data name=\"mediaDscp\"                         value=\"int\"/>"
         "           <data name=\"lanUnicastBindAddress\"             value=\"string\"/>"
         "           <data name=\"lanUnicastPort\"                    value=\"int\"/>"
         "           <data name=\"lanUnicastDiscoveryEnabled\"        value=\"bool\"/>"
         "           <data name=\"lanKeepAliveEnabled\"               value=\"bool\"/>"
         "           <data name=\"lanEndpointListAutoUpdateEnabled\"  value=\"bool\"/>"
         "           <data name=\"lanEndpointListFetchLimit\"         value=\"int\"/>"
         "           <data name=\"wanAuthServiceAddress\"             value=\"bool\"/>"
         "           <data name=\"wanConfServiceAddress\"             value=\"bool\"/>"
         "           <identity_list>"
         "              <identity id=\"#6:PttIdentityInfo_SettingsData\"/>"
         "           </identity_list>"
         "           <channel_list>"
         "              <channel id=\"#6:PttChannelInfo_SettingsData\"/>"
         "           </channel_list>"
         "           <lanIpAddressRange_list>"
         "              <lanIpAddressRange ipAddrStart=\"#6:PttIpRangeStart_SettingsData\" ipAddrEnd=\"#6:PttIpRangeEnd_SettingsData\"/>"
         "           </lanIpAddressRange_list>"
         "        </account>"
         "     </account_list>"
         "  </settings_data>"
         "  <activity_data>"
         "      <account_list>"
         "        <account accountid=\"#6:AccountInfo_ActivityData\">"
         "           <data name=\"outgoingIms\"            value=\"int\"/>"
         "           <data name=\"incomingIms\"            value=\"int\"/>"
         "           <data name=\"failedRegistrations\"    value=\"int\"/>"
         "           <data name=\"lanEndpointsDiscovered\" value=\"int\"/>"
         "           <data name=\"wanConferenceCount\"     value=\"int\"/>"
         "        </account>"
         "      </account_list>"
         "      <call_list>"
         "        <call accountid=\"#6:CallInfo_ActivityData\">"
         "           <data name=\"callStart\"                 value=\"int_64\"/>"
         "           <data name=\"callDuration\"              value=\"int\"/>"
         "           <data name=\"incoming\"                  value=\"bool\"/>"
         "           <data name=\"callSuccessful\"            value=\"bool\"/>"
         "           <data name=\"failedDialedCallReason\"    value=\"string\"/>"
         "           <data name=\"numDigitsDialed\"           value=\"int\"/>"
         "           <data name=\"callTransfer\"              value=\"bool\"/>"
         "           <data name=\"localConference\"           value=\"bool\"/>"
         "           <data name=\"maxConferenceParticipants\" value=\"int\"/>"
         "           <data name=\"videoConference\"           value=\"bool\"/>"
         "           <data name=\"audioInCodec\"              value=\"string\"/>"
         "           <data name=\"audioOutCodec\"             value=\"string\"/>"
         "           <data name=\"mediaEncryption\"           value=\"string\"/>"
         "           <data name=\"recordedCall\"              value=\"bool\"/>"
         "           <data name=\"usbDevice\"                 value=\"string\"/>"
         "           <data name=\"bluetoothDevice\"           value=\"string\"/>"
         "           <data name=\"channel\"                   value=\"string\"/>"
         "           <data name=\"serviceType\"               value=\"string\"/>"
         "           <data name=\"remoteIdentity\"            value=\"string\"/>"
         "           <data name=\"failureReason\"             value=\"string\"/>"
         "           <data name=\"responsesReceived\"         value=\"string\"/>"
         "           <videoOut>"
         "              <data name=\"device\" value=\"string\"/>"
         "              <data name=\"codec\"  value=\"string\"/>"
         "              <data name=\"layout\" value=\"string\"/>"
         "              <data name=\"width\"  value=\"int\"/>"
         "              <data name=\"height\" value=\"int\"/>"
         "           </videoOut>"
         "           <videoIn>"
         "              <data name=\"codec\"  value=\"string\"/>"
         "              <data name=\"layout\" value=\"string\"/>"
         "              <data name=\"width\"  value=\"int\"/>"
         "              <data name=\"height\" value=\"int\"/>"
         "           </videoIn>"
         "           <data name=\"oneWayAudio\"                 value=\"bool\"/>"
         "           <data name=\"poorNetworkQualityIndicated\" value=\"bool\"/>"
         "           <data name=\"dataNetworkType\"             value=\"string\"/>"
         "           <data name=\"callHoldUsed\"                value=\"bool\"/>"
         "           <data name=\"networkIpChange\"             value=\"bool\"/>"
         "           <data name=\"callOrigin\"                  value=\"string\"/>"
         "           <vqm_report>#5:vqmReport</vqm_report>"
         "        </call>"
         "     </call_list>"
         "     <presence>"
         "        <data name=\"numContacts\"             value=\"int\"/>"
         "        <data name=\"numContactsWithPresence\" value=\"int\"/>"
         "     </presence>"
         "     <provisioning>"
         "        <data name=\"successfulProvisionAttempts\" value=\"int\"/>"
         "        <data name=\"failedProvisionAttempts\"     value=\"int\"/>"
         "     </provisioning>"
         "     <stability>"
         "        <data name=\"numCrashes\" value=\"int\"/>"
         "     </stability>"
         "  </activity_data>"
         "</cpc_usage_report>";
   }
}

#endif // __CPCAPI2_ANALYTICSDOCUMENTTEMPLATE_H__
