#include "../datatypes/AudioQueryDeviceList.h"
#include "../datatypes/AudioSetCaptureDevice.h"
#include "../datatypes/AudioAudioDeviceRole.h"
#include "../datatypes/AudioSetRenderDevice.h"
#include "../datatypes/AudioPlaySound.h"
#include "../datatypes/AudioStopPlaySound.h"
#include "../datatypes/AudioQueryCodecList.h"
#include "../datatypes/AudioSetCodecEnabled.h"
#include "../datatypes/AudioSetCodecPriority.h"
#include "../datatypes/AudioSetCodecPayloadType.h"
#include "../datatypes/AudioSetTelephoneEventPayloadType.h"
#include "../datatypes/AudioSetMicMute.h"
#include "../datatypes/AudioSetSpeakerMute.h"
#include "../datatypes/AudioSetMicVolume.h"
#include "../datatypes/AudioSetSpeakerVolume.h"
#include "../datatypes/AudioQueryDeviceVolume.h"
#include "../datatypes/AudioStartMonitoringCaptureDeviceLevels.h"
#include "../datatypes/AudioStopMonitoringCaptureDeviceLevels.h"

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
void Audio::queryDeviceList(int64_t phoneHandle)
{
    AudioQueryDeviceList params = new AudioQueryDeviceList(phoneHandle);
    nlohmann::json j = this.transport.request("queryDeviceList", params.marshal());
    
};

void Audio::setCaptureDevice(int64_t phoneHandle, int64_t deviceId, AudioAudioDeviceRole role)
{
    AudioSetCaptureDevice params = new AudioSetCaptureDevice(phoneHandle, deviceId, role);
    nlohmann::json j = this.transport.request("setCaptureDevice", params.marshal());
    
};

void Audio::setRenderDevice(int64_t phoneHandle, int64_t deviceId, AudioAudioDeviceRole role)
{
    AudioSetRenderDevice params = new AudioSetRenderDevice(phoneHandle, deviceId, role);
    nlohmann::json j = this.transport.request("setRenderDevice", params.marshal());
    
};

int64_t Audio::playSound(int64_t phoneHandle, AudioAudioDeviceRole role, cpc::string resourceUri, bool repeat)
{
    AudioPlaySound params = new AudioPlaySound(phoneHandle, role, resourceUri, repeat);
    nlohmann::json j = this.transport.request("playSound", params.marshal());
    return j.get<int64_t>();
};

void Audio::stopPlaySound(int64_t phoneHandle, int64_t sound)
{
    AudioStopPlaySound params = new AudioStopPlaySound(phoneHandle, sound);
    nlohmann::json j = this.transport.request("stopPlaySound", params.marshal());
    
};

void Audio::queryCodecList(int64_t phoneHandle)
{
    AudioQueryCodecList params = new AudioQueryCodecList(phoneHandle);
    nlohmann::json j = this.transport.request("queryCodecList", params.marshal());
    
};

void Audio::setCodecEnabled(int64_t phoneHandle, int64_t codecId, bool enabled)
{
    AudioSetCodecEnabled params = new AudioSetCodecEnabled(phoneHandle, codecId, enabled);
    nlohmann::json j = this.transport.request("setCodecEnabled", params.marshal());
    
};

void Audio::setCodecPriority(int64_t phoneHandle, int64_t codecId, int64_t priority)
{
    AudioSetCodecPriority params = new AudioSetCodecPriority(phoneHandle, codecId, priority);
    nlohmann::json j = this.transport.request("setCodecPriority", params.marshal());
    
};

void Audio::setCodecPayloadType(int64_t phoneHandle, int64_t codecId, int64_t payloadType)
{
    AudioSetCodecPayloadType params = new AudioSetCodecPayloadType(phoneHandle, codecId, payloadType);
    nlohmann::json j = this.transport.request("setCodecPayloadType", params.marshal());
    
};

void Audio::setTelephoneEventPayloadType(int64_t phoneHandle, int64_t payloadType)
{
    AudioSetTelephoneEventPayloadType params = new AudioSetTelephoneEventPayloadType(phoneHandle, payloadType);
    nlohmann::json j = this.transport.request("setTelephoneEventPayloadType", params.marshal());
    
};

void Audio::setMicMute(int64_t phoneHandle, bool enabled)
{
    AudioSetMicMute params = new AudioSetMicMute(phoneHandle, enabled);
    nlohmann::json j = this.transport.request("setMicMute", params.marshal());
    
};

void Audio::setSpeakerMute(int64_t phoneHandle, bool enabled)
{
    AudioSetSpeakerMute params = new AudioSetSpeakerMute(phoneHandle, enabled);
    nlohmann::json j = this.transport.request("setSpeakerMute", params.marshal());
    
};

void Audio::setMicVolume(int64_t phoneHandle, int64_t level)
{
    AudioSetMicVolume params = new AudioSetMicVolume(phoneHandle, level);
    nlohmann::json j = this.transport.request("setMicVolume", params.marshal());
    
};

void Audio::setSpeakerVolume(int64_t phoneHandle, int64_t level)
{
    AudioSetSpeakerVolume params = new AudioSetSpeakerVolume(phoneHandle, level);
    nlohmann::json j = this.transport.request("setSpeakerVolume", params.marshal());
    
};

void Audio::queryDeviceVolume(int64_t phoneHandle)
{
    AudioQueryDeviceVolume params = new AudioQueryDeviceVolume(phoneHandle);
    nlohmann::json j = this.transport.request("queryDeviceVolume", params.marshal());
    
};

void Audio::startMonitoringCaptureDeviceLevels(int64_t phoneHandle, int64_t deviceId)
{
    AudioStartMonitoringCaptureDeviceLevels params = new AudioStartMonitoringCaptureDeviceLevels(phoneHandle, deviceId);
    nlohmann::json j = this.transport.request("startMonitoringCaptureDeviceLevels", params.marshal());
    
};

void Audio::stopMonitoringRenderDeviceLevels(int64_t phoneHandle)
{
    AudioStopMonitoringCaptureDeviceLevels params = new AudioStopMonitoringCaptureDeviceLevels(phoneHandle);
    nlohmann::json j = this.transport.request("stopMonitoringRenderDeviceLevels", params.marshal());
    
};
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio