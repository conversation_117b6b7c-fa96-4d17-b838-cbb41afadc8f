// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef XmppVCard_JSON_HELPER_H
#define XmppVCard_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/public/xmpp/XmppVCardManager.h>
#include <interface/public/xmpp/XmppVCardHandler.h>
#include <interface/public/xmpp/XmppVCardState.h>
#include <interface/experimental/xmpp/XmppVCardJsonProxyStateHandler.h>
#include <impl/xmpp/XmppVCardHandlerInternal.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::XmppVCardDetail& v)
    {
      w.StartObject();
      Write(w, "emailList", v.emailList);
      Write(w, "telephoneList", v.telephoneList);
      Write(w, "addressList", v.addressList);
      Write(w, "labelList", v.labelList);
      Write(w, "name", v.name);
      Write(w, "geo", v.geo);
      Write(w, "organization", v.organization);
      Write(w, "photo", v.photo);
      Write(w, "logo", v.logo);
      Write(w, "classification", v.classification);
      Write(w, "formattedname", v.formattedname);
      Write(w, "nickname", v.nickname);
      Write(w, "url", v.url);
      Write(w, "birthday", v.birthday);
      Write(w, "jid", v.jid);
      Write(w, "title", v.title);
      Write(w, "role", v.role);
      Write(w, "note", v.note);
      Write(w, "desc", v.desc);
      Write(w, "mailer", v.mailer);
      Write(w, "timezone", v.timezone);
      Write(w, "product", v.product);
      Write(w, "revision", v.revision);
      Write(w, "sortstring", v.sortstring);
      Write(w, "phonetic", v.phonetic);
      Write(w, "cpcollab", v.cpcollab);
      Write(w, "cpsoftphone", v.cpsoftphone);
      Write(w, "cpsoftphone_pref", v.cpsoftphone_pref);
      Write(w, "uid", v.uid);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::XmppVCardDetail& v)
    {
      Read(json, "emailList", v.emailList);
      Read(json, "telephoneList", v.telephoneList);
      Read(json, "addressList", v.addressList);
      Read(json, "labelList", v.labelList);
      Read(json, "name", v.name);
      Read(json, "geo", v.geo);
      Read(json, "organization", v.organization);
      Read(json, "photo", v.photo);
      Read(json, "logo", v.logo);
      Read(json, "classification", v.classification);
      Read(json, "formattedname", v.formattedname);
      Read(json, "nickname", v.nickname);
      Read(json, "url", v.url);
      Read(json, "birthday", v.birthday);
      Read(json, "jid", v.jid);
      Read(json, "title", v.title);
      Read(json, "role", v.role);
      Read(json, "note", v.note);
      Read(json, "desc", v.desc);
      Read(json, "mailer", v.mailer);
      Read(json, "timezone", v.timezone);
      Read(json, "product", v.product);
      Read(json, "revision", v.revision);
      Read(json, "sortstring", v.sortstring);
      Read(json, "phonetic", v.phonetic);
      Read(json, "cpcollab", v.cpcollab);
      Read(json, "cpsoftphone", v.cpsoftphone);
      Read(json, "cpsoftphone_pref", v.cpsoftphone_pref);
      Read(json, "uid", v.uid);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::XmppVCardDetail::Name& v)
    {
      w.StartObject();
      Write(w, "family", v.family);
      Write(w, "given", v.given);
      Write(w, "middle", v.middle);
      Write(w, "prefix", v.prefix);
      Write(w, "suffix", v.suffix);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::XmppVCardDetail::Name& v)
    {
      Read(json, "family", v.family);
      Read(json, "given", v.given);
      Read(json, "middle", v.middle);
      Read(json, "prefix", v.prefix);
      Read(json, "suffix", v.suffix);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::XmppVCardDetail::Email& v)
    {
      w.StartObject();
      Write(w, "userid", v.userid);
      Write(w, "home", v.home);
      Write(w, "work", v.work);
      Write(w, "internet", v.internet);
      Write(w, "pref", v.pref);
      Write(w, "x400", v.x400);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::XmppVCardDetail::Email& v)
    {
      Read(json, "userid", v.userid);
      Read(json, "home", v.home);
      Read(json, "work", v.work);
      Read(json, "internet", v.internet);
      Read(json, "pref", v.pref);
      Read(json, "x400", v.x400);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::XmppVCardDetail::Telephone& v)
    {
      w.StartObject();
      Write(w, "number", v.number);
      Write(w, "home", v.home);
      Write(w, "work", v.work);
      Write(w, "voice", v.voice);
      Write(w, "fax", v.fax);
      Write(w, "pager", v.pager);
      Write(w, "msg", v.msg);
      Write(w, "cell", v.cell);
      Write(w, "video", v.video);
      Write(w, "bbs", v.bbs);
      Write(w, "modem", v.modem);
      Write(w, "isdn", v.isdn);
      Write(w, "pcs", v.pcs);
      Write(w, "pref", v.pref);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::XmppVCardDetail::Telephone& v)
    {
      Read(json, "number", v.number);
      Read(json, "home", v.home);
      Read(json, "work", v.work);
      Read(json, "voice", v.voice);
      Read(json, "fax", v.fax);
      Read(json, "pager", v.pager);
      Read(json, "msg", v.msg);
      Read(json, "cell", v.cell);
      Read(json, "video", v.video);
      Read(json, "bbs", v.bbs);
      Read(json, "modem", v.modem);
      Read(json, "isdn", v.isdn);
      Read(json, "pcs", v.pcs);
      Read(json, "pref", v.pref);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::XmppVCardDetail::Address& v)
    {
      w.StartObject();
      Write(w, "pobox", v.pobox);
      Write(w, "extadd", v.extadd);
      Write(w, "street", v.street);
      Write(w, "locality", v.locality);
      Write(w, "region", v.region);
      Write(w, "pcode", v.pcode);
      Write(w, "ctry", v.ctry);
      Write(w, "home", v.home);
      Write(w, "work", v.work);
      Write(w, "postal", v.postal);
      Write(w, "parcel", v.parcel);
      Write(w, "pref", v.pref);
      Write(w, "dom", v.dom);
      Write(w, "intl", v.intl);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::XmppVCardDetail::Address& v)
    {
      Read(json, "pobox", v.pobox);
      Read(json, "extadd", v.extadd);
      Read(json, "street", v.street);
      Read(json, "locality", v.locality);
      Read(json, "region", v.region);
      Read(json, "pcode", v.pcode);
      Read(json, "ctry", v.ctry);
      Read(json, "home", v.home);
      Read(json, "work", v.work);
      Read(json, "postal", v.postal);
      Read(json, "parcel", v.parcel);
      Read(json, "pref", v.pref);
      Read(json, "dom", v.dom);
      Read(json, "intl", v.intl);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::XmppVCardDetail::Label& v)
    {
      w.StartObject();
      Write(w, "lines", v.lines);
      Write(w, "home", v.home);
      Write(w, "work", v.work);
      Write(w, "postal", v.postal);
      Write(w, "parcel", v.parcel);
      Write(w, "pref", v.pref);
      Write(w, "dom", v.dom);
      Write(w, "intl", v.intl);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::XmppVCardDetail::Label& v)
    {
      Read(json, "lines", v.lines);
      Read(json, "home", v.home);
      Read(json, "work", v.work);
      Read(json, "postal", v.postal);
      Read(json, "parcel", v.parcel);
      Read(json, "pref", v.pref);
      Read(json, "dom", v.dom);
      Read(json, "intl", v.intl);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::XmppVCardDetail::Geo& v)
    {
      w.StartObject();
      Write(w, "latitude", v.latitude);
      Write(w, "longitude", v.longitude);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::XmppVCardDetail::Geo& v)
    {
      Read(json, "latitude", v.latitude);
      Read(json, "longitude", v.longitude);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::XmppVCardDetail::Organization& v)
    {
      w.StartObject();
      Write(w, "name", v.name);
      Write(w, "units", v.units);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::XmppVCardDetail::Organization& v)
    {
      Read(json, "name", v.name);
      Read(json, "units", v.units);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::XmppVCardDetail::Photo& v)
    {
      w.StartObject();
      Write(w, "extval", v.extval);
      Write(w, "binval", v.binval);
      Write(w, "type", v.type);
      Write(w, "hash", v.hash);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::XmppVCardDetail::Photo& v)
    {
      Read(json, "extval", v.extval);
      Read(json, "binval", v.binval);
      Read(json, "type", v.type);
      Read(json, "hash", v.hash);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::XmppVCardManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::XmppVCardManager& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::XmppVCardHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::XmppVCardHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::VCardFetchedEvent& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      Write(w, "handle", v.handle);
      Write(w, "jid", v.jid);
      Write(w, "detail", v.detail);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::VCardFetchedEvent& v)
    {
      Read(json, "account", v.account);
      Read(json, "handle", v.handle);
      Read(json, "jid", v.jid);
      Read(json, "detail", v.detail);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::VCardOperationResultEvent& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      Write(w, "handle", v.handle);
      Write(w, "jid", v.jid);
      Write(w, "type", v.type);
      Write(w, "result", v.result);
      Write(w, "success", v.success);
      Write(w, "resultCode", v.resultCode);
      Write(w, "resultStr", v.resultStr);
      Write(w, "xmppErrorCode", v.xmppErrorCode);
      Write(w, "xmppErrorStr", v.xmppErrorStr);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::VCardOperationResultEvent& v)
    {
      Read(json, "account", v.account);
      Read(json, "handle", v.handle);
      Read(json, "jid", v.jid);
      Read(json, "type", v.type);
      Read(json, "result", v.result);
      Read(json, "success", v.success);
      Read(json, "resultCode", v.resultCode);
      Read(json, "resultStr", v.resultStr);
      Read(json, "xmppErrorCode", v.xmppErrorCode);
      Read(json, "xmppErrorStr", v.xmppErrorStr);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::ErrorEvent& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      Write(w, "handle", v.handle);
      Write(w, "errorText", v.errorText);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::ErrorEvent& v)
    {
      Read(json, "account", v.account);
      Read(json, "handle", v.handle);
      Read(json, "errorText", v.errorText);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::XmppVCardState& v)
    {
      w.StartObject();
      Write(w, "detail", v.detail);
      Write(w, "fetchResult", v.fetchResult);
      Write(w, "storeResult", v.storeResult);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::XmppVCardState& v)
    {
      Read(json, "detail", v.detail);
      Read(json, "fetchResult", v.fetchResult);
      Read(json, "storeResult", v.storeResult);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::XmppVCardStateInfo& v)
    {
      w.StartObject();
      Write(w, "vcard", v.vcard);
      Write(w, "account", v.account);
      Write(w, "jid", v.jid);
      Write(w, "state", v.state);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::XmppVCardStateInfo& v)
    {
      Read(json, "vcard", v.vcard);
      Read(json, "account", v.account);
      Read(json, "jid", v.jid);
      Read(json, "state", v.state);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::XmppVCardStateManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::XmppVCardStateManager& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::JsonProxyVCardStateEvent& v)
    {
      w.StartObject();
      Write(w, "states", v.states);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::JsonProxyVCardStateEvent& v)
    {
      Read(json, "states", v.states);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::XmppVCardJsonProxyStateHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::XmppVCardJsonProxyStateHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::XmppVCardCreatedResultEvent& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::XmppVCardCreatedResultEvent& v)
    {
      Read(json, "account", v.account);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppVCard::XmppVCardHandlerInternal& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppVCard::XmppVCardHandlerInternal& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // XmppVCard_JSON_HELPER_H