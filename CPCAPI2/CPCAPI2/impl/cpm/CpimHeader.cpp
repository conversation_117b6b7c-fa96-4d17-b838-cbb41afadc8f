#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_CHAT_MODULE==1 || CPCAPI2_BRAND_IM_MODULE==1 || CPCAPI2_BRAND_XMPP_CHAT_MODULE==1 || CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
#include "CpimHeader.h"
#include "CpmHelper.h"

#include <assert.h>
#include <sstream>

namespace CPCAPI2
{
namespace CPM
{

const cpc::string CpimHeader::FROM = "From";
const cpc::string CpimHeader::TO = "To";
const cpc::string CpimHeader::DATETIME = "DateTime";
const cpc::string CpimHeader::CONTENT_TYPE = "Content-Type";
const cpc::string CpimHeader::NS = "NS";
const cpc::string CpimHeader::IMDN_MESSAGE_ID = "imdn.Message-ID";
const cpc::string CpimHeader::IMDN_DISPOSITION_NOTIFICATION = "imdn.Disposition-Notification";
const cpc::string CpimHeader::CONTENT_DISPOSITION = "Content-Disposition";
const resip::Data CpimHeader::NAME_VALUE_DELIM = ": ";

CpimHeader::CpimHeader(const cpc::string& name, const cpc::string& value) 
   : name(name), value(value)
{
}

resip::Data CpimHeader::toBytes() const
{
   resip::Data data;
   data += name.c_str();
   data += NAME_VALUE_DELIM;
   data += value.c_str();
   return data;
}

CpimHeader CpimHeader::parse(const resip::Data& data)
{
   // Read the name and value of the header
   size_t nameValueDelimPos = data.find(NAME_VALUE_DELIM, 0);
   assert(nameValueDelimPos != std::string::npos);
   resip::Data name = data.substr(0, nameValueDelimPos);
   resip::Data value = data.substr(nameValueDelimPos + NAME_VALUE_DELIM.size());

   return CpimHeader(name.c_str(), value.c_str());
}

}
}
#endif
