#pragma once

#ifndef __CPCAPI2_APP_DIALOG_SET_FACTORY_H__
#define __CPCAPI2_APP_DIALOG_SET_FACTORY_H__

#include "cpcapi2defs.h"
#include <list>
#include "resip/dum/AppDialogSetFactory.hxx"
#include "AppDialogFactoryDelegate.h"

namespace resip
{
   class AppDialogSet;
   class DialogUsageManager;
   class SipMessage;
}

namespace CPCAPI2
{
   namespace SipAccount
   {
      class AppDialogSetFactory : public resip::AppDialogSetFactory
      {
      public:
         AppDialogSetFactory() {};
         virtual ~AppDialogSetFactory() {};

         virtual resip::AppDialogSet* createAppDialogSet( resip::DialogUsageManager&, const resip::SipMessage& ) OVERRIDE;

         // implement the AppDialogSetFactory interface, then register it using this method.
         void addDelegate( AppDialogFactoryDelegatePtr pDelegate, bool addAtEnd = false );

      private:
         std::list< AppDialogFactoryDelegatePtr > mDelegates;
      };
   }
}

#endif // __CPCAPI2_APP_DIALOG_SET_FACTORY_H__
