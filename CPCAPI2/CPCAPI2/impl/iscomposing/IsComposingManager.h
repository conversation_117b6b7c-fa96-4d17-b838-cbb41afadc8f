#pragma once

#if !defined(__CPCAPI2_IS_COMPOSING_MANAGER_H__)
#define __CPCAPI2_IS_COMPOSING_MANAGER_H__

#include "ComposerIdleState.h"
#include "ReceiverIdleState.h"
#include "IsComposingDocument.h"
#include "iscomposing/IsComposingTypes.h"
#include "../cpm/CpimMessage.h"
#include "../phone/PhoneInterface.h"

#include <rutil/DeadlineTimer.hxx>
#include <resip/stack/Uri.hxx>

using namespace CPCAPI2::CPM;

namespace CPCAPI2
{
namespace IsComposing
{
struct IsComposingInfo;

/**
 * Main class for the isComposing feature implementation, which based on RFC 3994.
 * This class is abstract therefore it cannot be instantiated directly. This class
 * implements the state machines and timers as described in the standard document.
 *
 * A component that requires the isComposing functionality must have a class that
 * inherits from IsComposingManager and implements two (2) pure virtual methods. 
 * One pure virtual method, sendIsComposingMessageNotification(...), is invoked
 * when it is time to send a IsComposingMessage notification (Composer side) to the 
 * receiver while the other pure virtual method, onIsComposingMessage(...), is 
 * invoked when the composer is in 'IsComposing' state (Receiver side). 
 *
 * The concrete implementation (i.e. the subclass) must invoke all protected methods 
 * of this class (at one point or another in its implementation) and in particular, 
 * setIsComposingMessage(...), which must be called whenever a new character is 
 * typed/entered by the user
 */
class IsComposingManager : virtual public resip::DeadlineTimerHandler
{
public:
   IsComposingManager(PhoneInterface* cpcPhone, resip::MultiReactor* reactor = NULL);
   virtual ~IsComposingManager();

protected:
   // Need to be implemented by subclasses (invoked by the state machines)
   virtual void sendIsComposingMessageNotification(IsComposingInfo* info, IsComposingMessageState state, const resip::Mime& contentType, int refreshInterval, const tm& lastActive) = 0;
   virtual void onIsComposingMessage(IsComposingInfo* info, IsComposingMessageState state, const resip::Mime& contentType, const tm& lastActive) = 0;

   // May need to be implemented by subclasses - Invoked by the state machines
   virtual void backToComposerIdleState(IsComposingInfo* info) {}
   virtual void backToReceiverIdleState(IsComposingInfo* info) {}

   // Invoked by subclasses
   void initialize(IsComposingInfo* info);
   void setIsComposingMessage(IsComposingInfo* info, const resip::Mime& contentType, const tm& datetime, int refreshInterval, int idleInterval);
   void setMessageSent(IsComposingInfo* info);
   void setMessageReceived(IsComposingInfo* info);
   void processIsComposingMessageNotification(IsComposingInfo* info, const IsComposingDocument& isComposingDocument);
   IsComposingDocument createIsComposingMessageNotification(IsComposingMessageState state, const resip::Mime& contentType, int refreshInterval, const tm& lastActive);

   AddressTransformer* getAddressTransformer() { return cpcPhone->getAddressTransformer(); }

protected:
   PhoneInterface* cpcPhone;
   resip::MultiReactor* mReactor;

private:
   friend class ComposerActiveState;
   friend class ComposerIdleState;
   friend class ReceiverActiveState;
   friend class ReceiverIdleState;

   // Invoked by the state machines
   void transitionTo(IsComposingInfo* info, ComposerBaseState* newState);
   void transitionTo(IsComposingInfo* info, ReceiverBaseState* newState);
   void startComposerIdleTimer(IsComposingInfo* info);
   void startComposerRefreshTimer(IsComposingInfo* info);
   void stopComposerIdleTimer(IsComposingInfo* info);
   void stopComposerRefreshTimer(IsComposingInfo* info);
   void startReceiverRefreshTimer(IsComposingInfo* info);
   void stopReceiverRefreshTimer(IsComposingInfo* info);

   // DeadlineTimerHandler interface
   void onTimer(unsigned short timerId, void* appState);
};

}
}

#endif // __CPCAPI2_IS_COMPOSING_MANAGER_H__