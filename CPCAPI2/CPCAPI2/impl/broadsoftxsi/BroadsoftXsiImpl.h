#pragma once

#ifndef CPCAPI2_BROADSOFT_XSI_IMPL_H
#define CPCAPI2_BROADSOFT_XSI_IMPL_H

#include <vector>

#include <boost/asio.hpp>

#include <rutil/MultiReactor.hxx>
#include <rutil/Fifo.hxx>

#include "../util/HttpClient.h"

#include "cpcapi2defs.h"

#include <broadsoftxsi/BroadsoftXsi.h>
#include <broadsoftxsi/BroadsoftXsiTypes.h>
#include <broadsoftxsi/BroadsoftXsiHandler.h>
#include "BroadsoftXsiTypesInternal.h"
#include "BroadsoftXsiSyncHandler.h"

#include "../util/DumFpCommand.h"

#include <libxml/tree.h>

namespace CPCAPI2
{
   class WebSocketCommand;

   namespace BroadsoftXsi
   {
      class BroadsoftXsiImpl
      {
      public:
         BroadsoftXsiImpl(
            BroadsoftXsiHandle h,
            boost::asio::io_service& ioService,
            resip::Fifo< resip::ReadCallbackBase >& callbackFifo );
         virtual ~BroadsoftXsiImpl();

         // Broadsoft XSI Implementation
         int configureConnectionSettings(BroadsoftXsiInfo* info, const LocalSettings& settings);
         int applySettings();
         int queryServices();
         int queryCallLogs(const CallLogType& callLogType);
         int deleteCallLogs(const CallLogType& callLogType);
         int deleteCallLog(const CallLogType& callLogType, const cpc::string& callLogId);
         int queryEnterpriseDirectory(const DirectorySearchParameters& searchParameters);
         int queryServiceSettings(const ServiceType);
         int setBroadworksAnywhereSettings(const BroadworksAnywhere& settings);
         int setCallForwardAlwaysSettings(const CallForwardAlways& settings);
         int setCallForwardBusySettings(const CallForwardBusy& settings);
         int setCallForwardNoAnswerSettings(const CallForwardNoAnswer& settings);
         int setDoNotDisturbSettings(const DoNotDisturb& settings);
         int setRemoteOfficeSettings(const RemoteOffice& settings);
         int setSimultaneousRingSettings(const SimultaneousRing& settings);
         int createCall(const cpc::string& number );
         int setHandler(BroadsoftXsiHandler* handler);

         void setCallbackHook(void (*cbHook)(void*), void* context);

         const LocalSettings& getSettings() { return m_Settings; }
         const BroadsoftXsiHandle getHandle() { return m_Handle; }

         // Funky macro for firing events
         template<typename TFn, typename TEvt> void fireEvent(
            const char* funcName,
            TFn func,
            const TEvt& args)
         {
            if( m_Handler == NULL )
               return;

            resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, m_Handler, m_Handle, args);
            if (m_Handler != (void*)0xDEADBEEF && dynamic_cast<BroadsoftXsiSyncHandler*>(m_Handler) != NULL)
            {
               // Invoke the callback synchronously in these cases
               ( *cb )();
               delete cb;
            }
            else
            {
               // Object "cb" should be deleted by whomever is processing this FIFO
               m_CallbackFifo.add( cb );
               if (m_CbHook) { m_CbHook(); }
            }
         }

      private: // methods

         // Generic request method for creating a sending a request to server
         // Return's false if request failed
         // Body is used for request body, but upon successful request will be filled with the response body
         bool request(const XsiAction action, const cpc::vector<XsiParameter>& parameters, cpc::string& body);

         // Methods for processing and parsing responses
         // Return's false if processing the response failed
         bool processServerResponse(const XsiAction action, const cpc::vector<XsiParameter>& parameters, int errorCode, int responseStatus, std::string contentType, std::string responseBody);
         bool parseCallLogs(std::string contentType, std::string responseBody, CallLogType callLogType);
         bool parseServices(std::string contentType, std::string responseBody);
         bool parseEnterpriseDirectory(std::string contentType, std::string responseBody);
         bool parseBroadworksAnywhere(std::string contentType, std::string responseBody);
         bool parseCallForwardAlways(std::string contentType, std::string responseBody);
         bool parseCallForwardBusy(std::string contentType, std::string responseBody);
         bool parseCallForwardNoAnswer(std::string contentType, std::string responseBody);
         bool parseDoNotDisturb(std::string contentType, std::string responseBody);
         bool parseRemoteOffice(std::string contentType, std::string responseBody);
         bool parseSimultaneousRing(std::string contentType, std::string responseBody);
         bool parseCreateCallResponse(std::string contentType, std::string responseBody);

         // Convert method for logging
         const char* callLogTypeToString(CallLogType lType);
         // Parse call log type tree
         bool parseCallLogType(xmlNodePtr cur, CallLogType callLogType, cpc::vector<CallLog>& callLogs);
         // Parse directory detail entries
         bool parseDirectoryDetails(xmlNodePtr cur, cpc::vector<DirectoryDetail>& dirDetails);

         // Fire failure event based on action requested
         void fireEvent(XsiAction, BroadsoftXsiFailureEvent&);

      private: // data

         // passed into websocketpp lib
         boost::asio::io_service& m_IOService;

         // shared between Interface and Impl
         resip::Fifo< resip::ReadCallbackBase >& m_CallbackFifo;
         std::function<void(void)> m_CbHook;

         // HTTP Client used to communicate to the Broadsoft XSI server
         HTTPClient* m_HttpClient;

         // The channel for which this service applies.
         BroadsoftXsiHandle m_Handle;

         // Pointer to the settings for this account (owned)
         LocalSettings m_Settings;

         // Application handler for account related activity
         BroadsoftXsiHandler *m_Handler;

         // Used to indicate a new http connection required
         bool m_StartNewConnection;

         // Try using enhanced call logs over basic
         bool m_PreferEnhancedLogs;

         // Cached copy of Broadwork Anywhere settings
         BroadworksAnywhere m_BroadworksAnywhere;
      };
   }
}
#endif // CPCAPI2_BROADSOFT_XSI_IMPL_H
