#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "cpcapi2utils.h"
#include "JsonApiServerSendTransportInterface.h"
#include "JsonApiServer_WebSocket.h"
#include "JsonApiServer_HTTP.h"
#include "JsonApiServerInterface.h"
#include "json/JsonHelper.h"
#include "auth_server/AuthServerJwtUtils.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"
#include "log/LocalLogger.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

using namespace resip;

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL
#define CP_LOCAL_LOGGER_VAR mLocalLogger

namespace CPCAPI2
{

namespace JsonApi
{

JsonApiServerSendTransportInterface::JsonApiServerSendTransportInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mJsonApiServer(NULL),
     mOutgoingJsonHandler(NULL),
     mLocalLogger(dynamic_cast<PhoneInterface*>(phone)->localLogger())
{
   StackLog(<< "JsonApiServerSendTransportInterface::JsonApiServerSendTransportInterface(): " << this << " mPhone: " << mPhone);
}

JsonApiServerSendTransportInterface::~JsonApiServerSendTransportInterface()
{
   StackLog(<< "JsonApiServerSendTransportInterface::~JsonApiServerSendTransportInterface(): " << this << " mPhone: " << mPhone);
}

void JsonApiServerSendTransportInterface::Release()
{
   StackLog(<< "JsonApiServerSendTransportInterface::Release(): " << this << " mPhone: " << mPhone);
   delete this;
}

int JsonApiServerSendTransportInterface::setJsonApiServer(CPCAPI2::JsonApi::JsonApiServer* jsonApiServer)
{
   mJsonApiServer = dynamic_cast<JsonApiServerInterface*>(jsonApiServer);
   mWebsockTransport = mJsonApiServer->getJsonApiWsTransport();
   mOutgoingJsonHandler = mJsonApiServer->getOutgoingJsonHandler();
   DebugLog(<< "JsonApiServerSendTransportInterface::setJsonApiServer(): " << this << " mPhone: " << mPhone << " mWebsockTransport: " << mWebsockTransport << " jsonApiServer: " << jsonApiServer);
   return kSuccess;
}

int JsonApiServerSendTransportInterface::send(const CPCAPI2::Json::JsonDataPointer& eventData, bool suppressLog)
{
   assert(mPhone->getSdkModuleThread().isCurrentThread());

   //StackLog(<< "JsonApiServerSendTransportInterface::send(): " << this << " data: " << eventData->getMessageData());

   if (mOutgoingJsonHandler != NULL)
   {
      cpc::string cpcjsonstr(eventData->getMessageData(), eventData->getMessageSize());
      mOutgoingJsonHandler->onJson(cpcjsonstr);
   }

   int itIndex = 0;
   ConnSet::iterator itConn = mConns.begin();
   while (itConn != mConns.end())
   {
      itIndex++;
      if (itConn->transportType == JsonApiRequestInfo::JsonApiTransportType_WS)
      {
         websocketpp::connection_hdl conn = itConn->websocket_conn;
         if (conn.expired())
         {
            itConn = mConns.erase(itConn);
            continue;
         }

         mWebsockTransport->send(eventData, conn);
      }
      else if (itConn->transportType == JsonApiRequestInfo::JsonApiTransportType_HTTP)
      {
         mHttpTransport->sendHttp(eventData, itConn->http_response);
         itConn = mConns.erase(itConn);
         continue;
      }
      else if (itConn->transportType == JsonApiRequestInfo::JsonApiTransportType_HTTPS)
      {
         mHttpTransport->sendHttps(eventData, itConn->https_response);
         itConn = mConns.erase(itConn);
         continue;
      }
      itConn++;
   }

   return kSuccess;
}

int JsonApiServerSendTransportInterface::send(const JsonApiRequestInfo& jsonConnId, const CPCAPI2::Json::JsonDataPointer& eventData)
{
   assert(mPhone->getSdkModuleThread().isCurrentThread());

   if (jsonConnId.transportType == JsonApiRequestInfo::JsonApiTransportType_WS)
   {
      websocketpp::connection_hdl conn = jsonConnId.websocket_conn;
      if (conn.expired())
      {
         DebugLog(<< "JsonApiServerSendTransportInterface::send(): " << this << " ignoring request due to expired connection");
         return kError;
      }

      mWebsockTransport->send(eventData, conn);
   }
   else if (jsonConnId.transportType == JsonApiRequestInfo::JsonApiTransportType_HTTP)
   {
      mHttpTransport->sendHttp(eventData, jsonConnId.http_response);
   }
   else if (jsonConnId.transportType == JsonApiRequestInfo::JsonApiTransportType_HTTPS)
   {
      mHttpTransport->sendHttps(eventData, jsonConnId.https_response);
   }
   else if (jsonConnId.transportType == JsonApiRequestInfo::JsonApiTransportType_InMem)
   {
      cpc::string cpcjsonstr(eventData->getMessageData(), eventData->getMessageSize());
      mOutgoingJsonHandler->onJson(cpcjsonstr);
   }
   return kSuccess;
}

int JsonApiServerSendTransportInterface::send(const rapidjson::Document& doc)
{
   assert(mPhone->getSdkModuleThread().isCurrentThread());

   StackLog(<< "JsonApiServerSendTransportInterface::send(): " << this);

   if (mOutgoingJsonHandler != NULL)
   {
      std::string stdjsonstr;
      Json::StdStringBuffer jsonbuffer(stdjsonstr);
      Json::StdStringWriter jsonwriter(jsonbuffer);
      doc.Accept(jsonwriter);
      cpc::string cpcjsonstr(stdjsonstr.c_str(), stdjsonstr.size());
      mOutgoingJsonHandler->onJson(cpcjsonstr);
   }

   ConnSet::iterator itConn = mConns.begin();
   while (itConn != mConns.end())
   {
      websocketpp::connection_hdl conn = itConn->websocket_conn;
      if (conn.expired())
      {
         StackLog(<< "JsonApiServerSendTransportInterface::send(): " << this << " erasing expired connection");
         itConn = mConns.erase(itConn);
         continue;
      }

      mWebsockTransport->send([&doc](std::string& buff) {
            Json::StdStringBuffer jsonbuffer(buff);
            Json::StdStringWriter jsonwriter(jsonbuffer);
            doc.Accept(jsonwriter);
         },
         1024, conn
      );

      itConn++;
   }
   return kSuccess;
}

int JsonApiServerSendTransportInterface::send(const JsonApiRequestInfo& jsonConnId, const rapidjson::Document& doc)
{
   assert(mPhone->getSdkModuleThread().isCurrentThread());

   if (jsonConnId.transportType == JsonApiRequestInfo::JsonApiTransportType_WS)
   {
      websocketpp::connection_hdl conn = jsonConnId.websocket_conn;
      if (conn.expired())
      {
         DebugLog(<< "JsonApiServerSendTransportInterface::send(): " << this << " ignoring request due to expired connection");
         return kError;
      }

      mWebsockTransport->send([&doc](std::string& buff) {
            Json::StdStringBuffer jsonbuffer(buff);
            Json::StdStringWriter jsonwriter(jsonbuffer);
#ifndef __clang_analyzer__ // warning inside rapidjson: https://github.com/Tencent/rapidjson/issues/1812
            doc.Accept(jsonwriter);
#endif
         },
         1024, conn
      );
   }
   else if (jsonConnId.transportType == JsonApiRequestInfo::JsonApiTransportType_HTTP)
   {
      mHttpTransport->sendHttp(doc.GetString(), doc.GetStringLength(), jsonConnId.http_response);
   }
   else if (jsonConnId.transportType == JsonApiRequestInfo::JsonApiTransportType_HTTPS)
   {
      mHttpTransport->sendHttps(doc.GetString(), doc.GetStringLength(), jsonConnId.https_response);
   }
   else if (jsonConnId.transportType == JsonApiRequestInfo::JsonApiTransportType_InMem)
   {
      std::string stdjsonstr;
      Json::StdStringBuffer jsonbuffer(stdjsonstr);
      Json::StdStringWriter jsonwriter(jsonbuffer);
      doc.Accept(jsonwriter);
      cpc::string cpcjsonstr(stdjsonstr.c_str(), stdjsonstr.size());
      mOutgoingJsonHandler->onJson(cpcjsonstr);
   }
   return kSuccess;
}

int JsonApiServerSendTransportInterface::send(JsonApiUserHandle jsonUser, const rapidjson::Document& doc)
{
   assert(mPhone->getSdkModuleThread().isCurrentThread());

   websocketpp::connection_hdl conn = mJsonApiServer->getWebsocketConnHdl(jsonUser);
   if (conn.expired())
   {
      DebugLog(<< "JsonApiServerSendTransportInterface::send(): " << this << " ignoring request due to expired connection");
      return kError;
   }

   mWebsockTransport->send([&doc](std::string& buff) {
         Json::StdStringBuffer jsonbuffer(buff);
         Json::StdStringWriter jsonwriter(jsonbuffer);
#ifndef __clang_analyzer__ // warning inside rapidjson: https://github.com/Tencent/rapidjson/issues/1812
         doc.Accept(jsonwriter);
#endif
      },
      1024, conn
   );

   return kSuccess;
}

int JsonApiServerSendTransportInterface::send(JsonApiUserHandle jsonUser, const CPCAPI2::Json::JsonDataPointer& eventData)
{
   assert(mPhone->getSdkModuleThread().isCurrentThread());

   websocketpp::connection_hdl conn = mJsonApiServer->getWebsocketConnHdl(jsonUser);
   if (conn.expired())
   {
      DebugLog(<< "JsonApiServerSendTransportInterface::send(): " << this << " ignoring request due to expired connection");
      return kError;
   }

   mWebsockTransport->send(eventData, conn);
   return kSuccess;
}

void JsonApiServerSendTransportInterface::setConnection(const JsonApiRequestInfo& jsonConnId)
{
   mPhone->getSdkModuleThread().post(resip::resip_bind(&JsonApiServerSendTransportInterface::setConnectionImpl, this, jsonConnId));
}

void JsonApiServerSendTransportInterface::setConnectionImpl(const JsonApiRequestInfo& jsonConnId)
{
   mConns.insert(jsonConnId);
}

int JsonApiServerSendTransportInterface::validateAuthToken(const CPCAPI2::JsonApi::AuthToken& authToken, bool& isValid, std::map<resip::Data, resip::Data>* pubClaims)
{
   StackLog(<< "JsonApiServerSendTransportInterface::validateAuthToken(): " << this << " mPhone: " << mPhone << " mJsonApiServer: " << mJsonApiServer);
   if (mJsonApiServer)
      return (mJsonApiServer->validateAuthToken(authToken, isValid, pubClaims, NULL));

   return kError;
}

}

}

#endif
