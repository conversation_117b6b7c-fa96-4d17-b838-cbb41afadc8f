#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_NATTRAVERSALSERVERTYPE_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_NATTRAVERSALSERVERTYPE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  enum class NatTraversalServerType
  {
    StunAndTurn,
    StunOnly,
    TurnOnly
  };

  inline std::string to_string(NatTraversalServerType value)
  {
    switch (value)
    {
      case NatTraversalServerType::StunAndTurn:
        return "StunAndTurn";
      case NatTraversalServerType::StunOnly:
        return "StunOnly";
      case NatTraversalServerType::TurnOnly:
        return "TurnOnly";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, NatTraversalServerType value)
  {
    switch (value)
    {
      case NatTraversalServerType::StunAndTurn:
        os << "StunAndTurn";
        break;
      case NatTraversalServerType::StunOnly:
        os << "StunOnly";
        break;
      case NatTraversalServerType::TurnOnly:
        os << "TurnOnly";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const NatTraversalServerType& e)
  {
    switch (e)
    {
      case NatTraversalServerType::StunAndTurn:
        j = "StunAndTurn";
        break;
      case NatTraversalServerType::StunOnly:
        j = "StunOnly";
        break;
      case NatTraversalServerType::TurnOnly:
        j = "TurnOnly";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, NatTraversalServerType& e)
  {
      if (0 == j.get<std::string>().compare("StunAndTurn"))
    {
      e = NatTraversalServerType::StunAndTurn;
    }
    else if (0 == j.get<std::string>().compare("StunOnly"))
    {
      e = NatTraversalServerType::StunOnly;
    }
    else if (0 == j.get<std::string>().compare("TurnOnly"))
    {
      e = NatTraversalServerType::TurnOnly;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum NatTraversalServerType"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_NATTRAVERSALSERVERTYPE_H
