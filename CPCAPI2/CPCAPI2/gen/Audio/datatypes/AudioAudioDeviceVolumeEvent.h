#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIODEVICEVOLUMEEVENT_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIODEVICEVOLUMEEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
  class AudioAudioDeviceVolumeEvent
  {
  public:
    std::optional<bool> micMuted;
    std::optional<bool> speakerMuted;
    std::optional<int32_t> micVolumeLevel;
    std::optional<int32_t> speakerVolumeLevel;

    AudioAudioDeviceVolumeEvent() = default;

    AudioAudioDeviceVolumeEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const AudioAudioDeviceVolumeEvent& o)
    {
      j = nlohmann::json::object();
       if (o.micMuted) { j["micMuted"] = *(o.micMuted); }
     if (o.speakerMuted) { j["speakerMuted"] = *(o.speakerMuted); }
     if (o.micVolumeLevel) { j["micVolumeLevel"] = *(o.micVolumeLevel); }
     if (o.speakerVolumeLevel) { j["speakerVolumeLevel"] = *(o.speakerVolumeLevel); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, AudioAudioDeviceVolumeEvent& o)
    {
     if (j.contains("micMuted")) { o.micMuted = j.at("micMuted"); }
     if (j.contains("speakerMuted")) { o.speakerMuted = j.at("speakerMuted"); }
     if (j.contains("micVolumeLevel")) { o.micVolumeLevel = j.at("micVolumeLevel"); }
     if (j.contains("speakerVolumeLevel")) { o.speakerVolumeLevel = j.at("speakerVolumeLevel"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIODEVICEVOLUMEEVENT_H
