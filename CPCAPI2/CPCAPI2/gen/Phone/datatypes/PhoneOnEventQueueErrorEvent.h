#include "jsonrpc/StringSerialization.h"
#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>
#include "EventQueueErrorReason.h"

#ifndef JSONRPC_CPCAPI2_PHONE_PHONEONEVENTQUEUEERROREVENT_H
#define JSONRPC_CPCAPI2_PHONE_PHONEONEVENTQUEUEERROREVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  class PhoneOnEventQueueErrorEvent
  {
  public:
    std::optional<cpc::string> errorText;
    std::optional<EventQueueErrorReason> errorReason;

    PhoneOnEventQueueErrorEvent() = default;

    PhoneOnEventQueueErrorEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const PhoneOnEventQueueErrorEvent& o)
    {
      j = nlohmann::json::object();
       if (o.errorText) { j["errorText"] = *(o.errorText); }
     if (o.errorReason) { j["errorReason"] = *(o.errorReason); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, PhoneOnEventQueueErrorEvent& o)
    {
     if (j.contains("errorText")) { o.errorText = j.at("errorText"); }
     if (j.contains("errorReason")) { o.errorReason = j.at("errorReason"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_PHONEONEVENTQUEUEERROREVENT_H
