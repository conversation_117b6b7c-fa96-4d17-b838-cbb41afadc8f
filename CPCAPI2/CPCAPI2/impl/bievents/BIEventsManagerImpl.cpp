#include <brand_branded.h>

#if (CPCAPI2_BRAND_BIEVENTS_MODULE == 1)

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::BI_EVENTS
#include "util/LogSubsystems.h"

#include <bievents/BIEventsHandler.h>

#include "BIEventsManagerImpl.h"
#include "BIEventRequest.h"
#include "BIEventResponse.h"
#include "BIEventCache.h"
#include "BIDruidCheckRequest.h"
#include "BIDruidCheckResponse.h"

#include "cpcapi2defs.h"
#include "util/DumFpCommand.h"

#include <util/strettorpc/RPCAuthRequest.h>
#include <util/strettorpc/RPCAuthResponse.h>
#include <util/strettorpc/RPCEndRequest.h>

#include <util/CurlHttp.h>

#ifdef _WIN32
#include <io.h>
#else
#include <unistd.h>
#endif
#include <fcntl.h>
#include <sys/types.h>
#include <sys/stat.h>

// Some open compat macros
#ifndef O_BINARY
#define O_BINARY 0
#endif

#ifndef O_TEXT
#define O_TEXT 0
#endif

// Define some things on windows to make the system calls more portable
#ifdef _WIN32
#ifndef S_IRUSR
#define S_IRUSR _S_IREAD
#endif
#ifndef S_IWUSR
#define S_IWUSR _S_IWRITE
#endif
#endif

// Path separator used for open
#ifdef _WIN32
#define PATH_SEP "\\"
#else
#define PATH_SEP "/"
#endif

// For getcwd function
#ifdef _WIN32
#include <direct.h>
#else
#include <unistd.h>
#endif

#define DEFAULT_CACHE_FILENAME "ECACHE"
#define DEFAULT_DRUIDCACHE_FILENAME "DCACHE"

using namespace CPCAPI2::BIEvents;

BIEventsManagerImpl::BIEventsManagerImpl(
   BIEventsHandle hEvents,
   boost::asio::io_service& ioService,
   resip::Fifo< resip::ReadCallbackBase >& callbackFifo )
   : m_hEvents( hEvents ),
     m_IOService( ioService ),
     m_CallbackFifo( callbackFifo ),
     m_Handler( NULL ),
     m_CbHook( NULL ),
     m_EventCache( NULL ),
     m_AuthContext( NULL ),
     m_HttpClient( new HTTPClient ),
     m_IsEnabled( true ),
     m_DeltaTimer( ioService ),
     m_MaxTimer( NULL )
{
}

BIEventsManagerImpl::~BIEventsManagerImpl( void )
{
   StackLog(<< "Destroying BIEventsManagerImpl" );

   m_DeltaTimer.cancel();
   if( m_MaxTimer != NULL )
   {
      m_MaxTimer->cancel();
      delete m_MaxTimer;
      m_MaxTimer = NULL;
   }

   // If auth succeeded, proceed with the druid business.
   if( m_AuthContext != NULL && m_AuthContext->isSuccessResponse() )
   {
      std::string sessionId, authToken;
      m_AuthContext->getSession( sessionId );
      m_AuthContext->getAuthToken( authToken );

      // Destroy the session
      RPCEndRequest endRequest( sessionId, authToken );
      std::string jsonText;
      CPCAPI2::Json::StdStringBuffer buffer(jsonText);
      endRequest.toJSON(buffer);

      cpc::vector< HTTPClient::StringPair > customHeaders; // empty
      int resultErrorCode = 0;
      int responseStatus  = 0;
      cpc::string responseContentType;
      cpc::string responseBody;
      HTTPClient::RedirectInfo redirectInfo;

      StackLog(<< "RPC Session End request: " << jsonText );

      // Send the HTTP Post.
      m_HttpClient->HTTPSendMessage(
         HTTPClient::EHTTPVerbPOST,
         m_Settings.serverLocationURL.c_str(),
         "application/json",
         m_Settings.httpAuthUser.c_str(),
         m_Settings.httpAuthPasswd.c_str(),
         NULL,
         NULL,
         jsonText.c_str(),
         jsonText.size(),
         0,
         false,
         false,
         false,
         false,
         NULL,
         customHeaders,
         m_Settings.httpVerboseLogging,
         false,
         resultErrorCode,
         responseStatus,
         responseContentType,
         responseBody,
         redirectInfo,
         "CounterPath-HTTP",
         "",
         2,   // specify a timeout to make sure things don't block on ending
         0);

      if( resultErrorCode == -1 )
      {
         // connection problem, fire an error
         OnErrorEvent evt;
         evt.errorMessage = "Connection Failure";
         evt.errorCode    = responseStatus;
         fireEvent( cpcFunc( BIEventsHandler::onError ), evt );
      }

      StackLog(<< "Stretto RPC Session Ended: " << responseBody );

      if(( responseStatus >= 200 && responseStatus < 300 ) &&
           strncmp( responseContentType.c_str(), "application/json", strlen( "application/json" )) == 0 )
      {
         RPCResponse response;
         rapidjson::Document parsedDoc;

         parsedDoc.Parse<0>( responseBody.c_str() );
         if( !parsedDoc.HasParseError() && response.fromJSON( parsedDoc ))
         {
            StackLog(<< "Successfully logged out: " << response.isSuccessResponse() );
         }
      }
   }

   delete m_EventCache;
   delete m_HttpClient;
   delete m_AuthContext;

   // fire an event to indicate destruction complete
   OnDestroyEvent evt;
   fireEvent( cpcFunc( BIEventsHandler::onDestroy ), evt );
   setHandler( NULL );
}

int BIEventsManagerImpl::configureSettings( const BIEventsSettings & settings )
{
   StackLog(<< "New settings being configured" );

   // Always update the settings
   m_Settings = settings;

   // Put some limits on the timers
   if( m_Settings.slidingWindowDeltaMillis < 500 )
      m_Settings.slidingWindowDeltaMillis = 500;

   if( m_Settings.slidingWindowMaxMillis < 2000 )
      m_Settings.slidingWindowMaxMillis = 2000;

   // Make sure max >= delta
   if( m_Settings.slidingWindowDeltaMillis > m_Settings.slidingWindowMaxMillis )
      m_Settings.slidingWindowMaxMillis = m_Settings.slidingWindowDeltaMillis;

   // Set the working directory default
   if( m_Settings.cacheDirectory.empty() )
   {
      char *dir = NULL;
      dir = getcwd( NULL, 0 );
      if( dir != NULL )
      {
         m_Settings.cacheDirectory = dir;
         free( dir ); // NB: dir was allocated using malloc
      }
      else
      {
         return kError;
      }
   }

   // Set the default filename
   if( m_Settings.cacheFileName.empty() )
      m_Settings.cacheFileName = DEFAULT_CACHE_FILENAME;

   // Bounds check the chunkSize
   if( m_Settings.chunkSize <= 0 )
      m_Settings.chunkSize = 100;
   if( m_Settings.chunkSize > 1000 )
      m_Settings.chunkSize = 1000;

   // Protect against multiple calls to configureSettings
   if( m_EventCache )
      delete m_EventCache;

   m_EventCache = new BIEventCache( m_Settings.cacheDirectory.c_str(), m_Settings.cacheFileName.c_str(), m_Settings.cacheNumFiles, m_Settings.cacheFileSize );
   return kSuccess;
}

int BIEventsManagerImpl::setHandler( BIEventsHandler * notificationHandler )
{
   // Ensure handler isn't already set.
   if( m_Handler != NULL && notificationHandler != NULL )
      return kError; // FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLER_STR );

   m_Handler = notificationHandler;
   return kSuccess;
}

bool BIEventsManagerImpl::performAuthorization( void )
{
   if( m_Settings.authUser.empty() || m_Settings.authPass.empty() )
      return false;

   // Create an easy handle, set its options, and post the request to the server
   RPCAuthRequest authRequest(
      m_Settings.authUser.c_str(),
      m_Settings.authPass.c_str() );

   std::string jsonText;
   CPCAPI2::Json::StdStringBuffer buffer(jsonText);
   authRequest.toJSON(buffer);

   cpc::vector< HTTPClient::StringPair > customHeaders; // empty
   int resultErrorCode = 0;
   int responseStatus  = 0;
   cpc::string responseContentType;
   cpc::string responseBody;
   HTTPClient::RedirectInfo redirectInfo;

   StackLog(<< "Authentication request: " << jsonText );

   // Send the HTTP Post.
   m_HttpClient->HTTPSendMessage(
      HTTPClient::EHTTPVerbPOST,
      m_Settings.serverLocationURL.c_str(),
      "application/json",
      m_Settings.httpAuthUser.c_str(),
      m_Settings.httpAuthPasswd.c_str(),
      NULL,
      NULL,
      jsonText.c_str(),
      jsonText.size(),
      0,
      false,
      false,
      false,
      false,
      NULL,
      customHeaders,
      m_Settings.httpVerboseLogging,
      false,
      resultErrorCode,
      responseStatus,
      responseContentType,
      responseBody,
      redirectInfo );

   if( resultErrorCode == -1 )
   {
      // connection problem, fire an error
      OnErrorEvent evt;
      evt.errorMessage = "Connection Failure";
      evt.errorCode    = responseStatus;
      fireEvent( cpcFunc( BIEventsHandler::onError ), evt );
      return false;
   }

   StackLog(<< "Authentication response: " << responseBody );

   if(( responseStatus >= 200 && responseStatus < 300 ) &&
         strncmp( responseContentType.c_str(), "application/json", strlen( "application/json" )) == 0 )
   {
      RPCAuthResponse *response = new RPCAuthResponse;
      rapidjson::Document parsedDoc;

      parsedDoc.Parse<0>( responseBody.c_str() );
      if( !parsedDoc.HasParseError() && response->fromJSON( parsedDoc ))
      {
         m_AuthContext = response;
         return true;
      }
      else
      {
         delete response;
         return false;
      }
   }
   else
   {
      return false;
   }
}

int BIEventsManagerImpl::enable( void )
{
   // send the authentication request if necessary
   if( m_AuthContext == NULL )
      performAuthorization();

   // If auth succeeded, proceed with the druid business.
   if( m_AuthContext != NULL && m_AuthContext->isSuccessResponse() )
   {
      std::string sessionId, authToken;
      m_AuthContext->getSession( sessionId );
      m_AuthContext->getAuthToken( authToken );

      // Fetch the druid if there is a cached version present (if it is
      // not already loaded into memory).
      if( m_Druid.empty() )
      {
         // - Ensure the working directory exists and is writable/writable
         std::string fullPath;
         fullPath = m_Settings.cacheDirectory;
         fullPath += PATH_SEP;
         fullPath += DEFAULT_DRUIDCACHE_FILENAME;

         // Try to open the file
         int druidFD = 0;
         if(( druidFD = open( fullPath.c_str(), O_RDONLY | O_TEXT )) < 0 )
         {
            // Just do nothing on error (assume new druid)
         }
         else
         {
            // File opened. read contents into m_Druid
            size_t readCount = 0;
            int result = 0;
            const size_t bufsize = 256; // druids are ASCII and have less chars than this
            char buf[ bufsize ] = { 0 };

            do
            {
               // Read a full "chunk" of info
               result = read( druidFD, buf + readCount, bufsize - readCount );
               if( result < 0 )
                  break;

               readCount += result;
            }
            while(( result > 0 ) && ( readCount < bufsize ));
            m_Druid.assign( buf );
            close( druidFD );
         }
      }

      // Create an easy handle, set its options, and post the request to the server
      BIDruidCheckRequest druidRequest(
         sessionId,
         authToken,
         m_Settings.buildStamp.c_str(),
         m_Settings.strettoUserNameOrID.c_str(),
         m_Druid );

      std::string jsonText;
      CPCAPI2::Json::StdStringBuffer buffer(jsonText);
      druidRequest.toJSON(buffer);


      cpc::vector< HTTPClient::StringPair > customHeaders; // empty
      int resultErrorCode = 0;
      int responseStatus  = 0;
      cpc::string responseContentType;
      cpc::string responseBody;
      HTTPClient::RedirectInfo redirectInfo;

      StackLog(<< "Druid Check request: " << jsonText );

      // Send the HTTP Post.
      m_HttpClient->HTTPSendMessage(
         HTTPClient::EHTTPVerbPOST,
         m_Settings.serverLocationURL.c_str(),
         "application/json",
         m_Settings.httpAuthUser.c_str(),
         m_Settings.httpAuthPasswd.c_str(),
         NULL,
         NULL,
         jsonText.c_str(),
         jsonText.size(),
         0,
         false,
         false,
         false,
         false,
         NULL,
         customHeaders,
         m_Settings.httpVerboseLogging,
         false,
         resultErrorCode,
         responseStatus,
         responseContentType,
         responseBody,
         redirectInfo );

      if( resultErrorCode == -1 )
      {
         // connection problem, fire an error
         OnErrorEvent evt;
         evt.errorMessage = "Connection Failure";
         evt.errorCode    = responseStatus;
         fireEvent( cpcFunc( BIEventsHandler::onError ), evt );
         return kError;
      }

      StackLog(<< "Druid Check response: " << responseBody );

      if(( responseStatus >= 200 && responseStatus < 300 ) &&
           strncmp( responseContentType.c_str(), "application/json", strlen( "application/json" )) == 0 )
      {
         BIDruidCheckResponse response;
         rapidjson::Document parsedDoc;

         parsedDoc.Parse<0>( responseBody.c_str() );
         if( !parsedDoc.HasParseError() && response.fromJSON( parsedDoc ))
         {
            const std::string newDruid( response.getResponseDruid() );
            if( !newDruid.empty() )
            {
               // Persist the druid, if it is new
               if( newDruid != m_Druid )
               {
                  // - Ensure the working directory exists and is writable/writable
                  std::string fullPath = m_Settings.cacheDirectory.c_str();
                  fullPath += PATH_SEP;
                  fullPath += DEFAULT_DRUIDCACHE_FILENAME;

                  // If there's any file there beforehand, pitch it out
                  unlink( fullPath.c_str() );

                  // Try to open the file for writing.
                  int druidFD = 0;
                  if(( druidFD = open( fullPath.c_str(), O_WRONLY | O_TEXT | O_CREAT | O_TRUNC, S_IRUSR | S_IWUSR )) < 0 )
                  {
                     // Just do nothing on error (a new one will happen. this is a failure
                     // and may impact tracking, but not a fatal one).
                  }
                  else
                  {
                     // File opened. write contents from m_Druid into file.
                     size_t writeCount = 0;
                     int result = 0;
                     do
                     {
                        result = write( druidFD, newDruid.c_str() + writeCount, newDruid.size() - writeCount );
                        if( result < 0 )
                           break;

                        // write succeeded
                        writeCount += result;
                     }
                     while(( result > 0 ) && ( writeCount < newDruid.size() ));
                     close( druidFD );
                  }

                  m_Druid = newDruid;
               }

               // Fire the druid event to the application
               OnServerInfoEvent evt;
               evt.druid = newDruid.c_str();
               fireEvent( cpcFunc( BIEventsHandler::onServerInfo ), evt );
            }
         }
      }
   }

   restartTimers();
   m_IsEnabled = true;
   return kSuccess;
}

int BIEventsManagerImpl::disable( void )
{
   m_IsEnabled = false;
   return kSuccess;
}

int BIEventsManagerImpl::postEvent( const BIEventHeader& eventHeader, const BIEventBody& eventData, const CPCAPI2::RPCIdentifier& eventID )
{
   //StackLog(<< "Pushing event into the cache" );

   // Remember the event data for later
   BIEventRecord pushRecord( eventID, eventHeader, eventData );
   if( m_EventCache->PushEvent( pushRecord ))
   {
      // Start a timer to upload the event (or some old ones perhaps),
      // if the event manager is enabled.
      if( m_IsEnabled )
         restartTimers();
   }

   return kSuccess;
}

int BIEventsManagerImpl::purgeCache( void )
{
   abort(); // make sure no timers or HTTP messages are ongoing while purge occurs
   m_EventCache->Purge();
   return kSuccess;
}

void BIEventsManagerImpl::abort( void )
{
   StackLog(<< "Stopping the timers and any outstanding HTTP requests" );

   m_HttpClient->Abort();
   m_DeltaTimer.cancel();
   if( m_MaxTimer != NULL )
   {
      m_MaxTimer->cancel();
      delete m_MaxTimer;
      m_MaxTimer = NULL;
   }
}

void BIEventsManagerImpl::restartTimers( void )
{
   // Always restart the delta timer.
   boost::posix_time::milliseconds deltaTime = boost::posix_time::milliseconds( m_Settings.slidingWindowDeltaMillis );
   m_DeltaTimer.expires_from_now( deltaTime );
   BIEventsManagerTimer *onDelta = new BIEventsManagerTimer( shared_from_this(), BIEventsTimer_OnDelta ); // cleanup in dtor
   m_DeltaTimer.async_wait( std::bind( &BIEventsManagerTimer::operator(), onDelta, std::placeholders::_1 ));

   // Only restart the max timer if it's not already started.
   if( m_MaxTimer == NULL )
   {
      boost::posix_time::milliseconds m_MaxTime = boost::posix_time::milliseconds( m_Settings.slidingWindowMaxMillis );
      m_MaxTimer = new boost::asio::deadline_timer( m_IOService );
      m_MaxTimer->expires_from_now( m_MaxTime );
      BIEventsManagerTimer *onMax = new BIEventsManagerTimer( shared_from_this(), BIEventsTimer_OnMax ); // cleanup in dtor
      m_MaxTimer->async_wait( std::bind( &BIEventsManagerTimer::operator(), onMax, std::placeholders::_1 ));
   }
}

void BIEventsManagerImpl::onDeltaTimer( const boost::system::error_code & e )
{
   if( e == boost::asio::error::operation_aborted )
      return; // We were cancelled, just like firefly. .. (too soon?)

   StackLog(<< "Delta Timer fired" );

   if( m_MaxTimer != NULL )
   {
      m_MaxTimer->cancel();
      delete m_MaxTimer;
      m_MaxTimer = NULL;
   }

   uploadEvents();
}

void BIEventsManagerImpl::onMaxTimer( const boost::system::error_code& e )
{
   if( e == boost::asio::error::operation_aborted )
      return; // We were cancelled, just like futurama. .. (what?)

   DebugLog(<< "Max Timer fired" );

   m_DeltaTimer.cancel();
   uploadEvents();
}

void BIEventsManagerImpl::uploadEvents( void )
{
   StackLog(<< "Uploading events to the BI server" );

   // Double check enabled state, just in case
   if( !m_IsEnabled )
   {
      StackLog(<< "BIEventsManagerImpl::uploadEvents returning -- not enabled" );
      return;
   }

   // Get the session ID and auth token
   if( m_AuthContext == NULL )
   {
      StackLog(<< "BIEventsManagerImpl::uploadEvents returning -- mAuthContext is NULL" );
      return;
   }

   std::string sessionID;
   if( !m_AuthContext->getSession( sessionID ))
   {
      StackLog(<< "BIEventsManagerImpl::uploadEvents returning -- getSession false" );
      return;
   }

   std::string authToken;
   if( !m_AuthContext->getAuthToken( authToken ))
   {
      StackLog(<< "BIEventsManagerImpl::uploadEvents returning -- getAuthToken false" );
      return;
   }

   std::list< BIEventRecord > popRecords;
   m_EventCache->PopEvents( popRecords, m_Settings.chunkSize );
   if( popRecords.size() == 0 )
   {
      StackLog(<< "BIEventsManagerImpl::uploadEvents returning -- no records" );
      return; // nothing to do.
   }

   // Bundle all the events together in a single request
   BIEventRequest request( sessionID, authToken, m_Settings.buildStamp.c_str(), m_Settings.strettoUserNameOrID.c_str(), popRecords );
   std::string jsonText;
   CPCAPI2::Json::StdStringBuffer buffer(jsonText);
   request.toJSON(buffer);

   StackLog(<< "HTTP Upload request: " << jsonText );

   // Just send the event immediately over http
   cpc::vector< HTTPClient::StringPair > customHeaders; // empty
   int resultErrorCode = 0;
   int responseStatus  = 0;
   cpc::string responseContentType;
   cpc::string responseBody;
   HTTPClient::RedirectInfo redirectInfo;

   // Send the HTTP Post.
   m_HttpClient->HTTPSendMessage(
      HTTPClient::EHTTPVerbPOST,
      m_Settings.serverLocationURL.c_str(),
      "application/json",
      m_Settings.httpAuthUser.c_str(),
      m_Settings.httpAuthPasswd.c_str(),
      NULL,
      NULL,
      jsonText.c_str(),
      jsonText.size(),
      0,
      false,
      false,
      false,
      false,
      NULL,
      customHeaders,
      m_Settings.httpVerboseLogging,
      false,
      resultErrorCode,
      responseStatus,
      responseContentType,
      responseBody,
      redirectInfo );

   // Restart the timers now, it's OK to do so because it runs in the
   // same thread :)
   restartTimers();

   // Continue to handle error/success paths

   if( resultErrorCode == -1 )
   {
      // connection problem, fire an error
      OnErrorEvent evt;
      evt.errorMessage = "Connection Failure";
      evt.errorCode    = responseStatus;
      fireEvent( cpcFunc( BIEventsHandler::onError ), evt );
      m_EventCache->RollbackRead();
      return;
   }

   StackLog( << "HTTP Upload response: " << responseBody );

   // Check for error status codes and non-matching content types.
   if(( responseStatus < 200 || responseStatus >= 300 ) ||
        strncmp( responseContentType.c_str(), "application/json", strlen( "application/json" )) != 0 )
   {
      OnErrorEvent evt;
      evt.errorMessage = "HTTP Error";
      evt.errorCode    = responseStatus;
      fireEvent( cpcFunc( BIEventsHandler::onError ), evt );
      m_EventCache->RollbackRead();
      return;
   }

   BIEventResponse response;
   rapidjson::Document responseDoc;
   responseDoc.Parse< 0 >( responseBody );

   if( !responseDoc.HasParseError() )
      response.fromJSON( responseDoc );

   if( !response.isSuccessResponse() )
   {
      // From an email thread on the topic:
      //
      // if you get a StrettoRPC (Json) error back (one of those -32xxx error
      // codes) then:
      //
      //     examine the error code if it is any authentication error (session
      //     timed out etc.) then resolve the issue and retry
      //     if the error is a parsing error (that means the request you formed
      //     could not be read, then I would throw thing away, possibly you
      //     have illegal characters somewhere inbetween, wrong encoding, that
      //     would be really hard to detect and to recover from)
      //
      OnErrorEvent evt;
      evt.errorMessage = response.getErrorMessage().c_str();
      evt.errorCode    = response.getErrorCode();
      switch( evt.errorCode )
      {
      // For authentication related errors, keep the event for a
      // subsequent retry (perform a rollback).
      case RPCErrorCode_Forbidden:
      case RPCErrorCode_Invalid_Sess:
      case RPCErrorCode_Authentication:
      case RPCErrorCode_Session_Expiry:

         m_EventCache->RollbackRead();
         StackLog( << "Error received from BI server: [" << evt.errorCode << "]. Events will be kept for retry" );

         if( evt.errorCode == RPCErrorCode_Session_Expiry )
         {
            // Invisibly Handle session expiry by reperforming ~auth~ step
            m_AuthContext = NULL;
            performAuthorization();
            break; // continue (request should be re-sent at next timer event if auth successful)
         }
         else
         {
            // Otherwise fire an error to the application.
            fireEvent( cpcFunc( BIEventsHandler::onError ), evt );
            return; // no more processing
         }
         break;
      default:
         // For other errors, drop the events from the queue because
         // retrying them won't help.
         StackLog( << "Error received from BI server: [" << evt.errorCode << "]. Events dropped" );
         fireEvent( cpcFunc( BIEventsHandler::onError ), evt );
         return; // no more processing
      }
   }
   else // if( response.isSuccessResponse() )
   {
      // If you get a "result" block back, you are good to go, throw all the
      // events you just posted away, they were processed
      //
      //     optionally inside the result block you get a status of either (REC
      //     = received but not processed, SUCC = received, processed, deemed
      //     good and stored, FAIL = the payload did not match the schema, or
      //     was offensive etc.)
      //     if the status is anything but SUCC, we could log this to a debug
      //     log if enabled on the clients, this can help us to determine if we
      //     don\92t see events on the server what happened. However, do not
      //     repost the event, you would just get the same result.
      //
   }

   // The response has a parameter called events, which contains
   // an array of objects
   //
   // TODO: move this into a method on BIEventResponse
   std::map< std::string, RPCParam > params;
   if( !response.getResultParams( params ))
      return; // I doubt rolling back will help here (shouldn't happen)

   std::map< std::string, RPCParam >::const_iterator eventParamIter = params.find( "events" );
   if( eventParamIter != params.end() )
   {
      // Get the array
      const RPCParam& param( eventParamIter->second );
      if( param.m_Value.m_Type == RPCValueType_array )
      {
         assert( popRecords.size() == param.m_Value.m_ArrayVal.size() );

         // Simultaneously loop over the popRecords and m_ArrayVal
         std::list< BIEventRecord >::const_iterator popIter = popRecords.begin();
         std::list< RPCValue >::const_iterator arrayIter    = param.m_Value.m_ArrayVal.begin();
         for( ; ( arrayIter != param.m_Value.m_ArrayVal.end() ) && ( popIter != popRecords.end() ) ; ++arrayIter, ++popIter )
         {
            // BIG ASSUMPTION (check with Helge). That the server responds
            // in the same order that the events are posted. Grab the event
            // from the envelope data structure, reverse order.

            // Object has 3 values, type, time, and status
            const RPCValue &val( *arrayIter );
            if( val.m_Type == RPCValueType_object )
            {
               // Hmm. what to do with this. IDs are not per event but rather per request.
               std::map< std::string, RPCValue >::const_iterator objIter = val.m_ObjectVal.find( "status" );
               if( objIter != val.m_ObjectVal.end() )
               {
                  if( objIter->second.m_Type == RPCValueType_string )
                  {
                     if( objIter->second.m_StringVal == "SUCC" ||
                         objIter->second.m_StringVal == "REC" )
                     {
                        // Both of these responses mean that the server received the event
                        OnPostSuccessEvent e;
                        e.id.assign( popIter->m_Fixed.ID, 64 );
                        fireEvent( cpcFunc( BIEventsHandler::onEventSuccess ), e );
                     }
                     else if( objIter->second.m_StringVal == "FAIL" )
                     {
                        OnPostFailureEvent e;
                        e.id.assign( popIter->m_Fixed.ID, 64 );
                        fireEvent( cpcFunc( BIEventsHandler::onEventFailure ), e );
                     }
                  }
               }
            }
         }
      }
   }
}

void BIEventsManagerImpl::setCallbackHook(void (*cbHook)(void*), void* context)
{
   if( cbHook != NULL && context != NULL )
      m_CbHook = std::bind( cbHook, context );
}


#endif
