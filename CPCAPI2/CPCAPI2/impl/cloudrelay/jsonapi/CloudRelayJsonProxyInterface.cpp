#include "brand_branded.h"

#if (CPCAPI2_BRAND_CLOUD_RELAY_JSON_PROXY_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "CloudRelayJsonProxyInterface.h"
#include "cloudrelay/CloudRelayManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiClient.h"
#include "jsonapi/JsonApiClientInterface.h"
#include "json/JsonHelper.h"
#include "util/LogSubsystems.h"
#include "util/IpHelpers.h"

#include <rutil/Logger.hxx>
#include <rutil/Random.hxx>
#include <rutil/ParseBuffer.hxx>
#include <resip/stack/SdpContents.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_CALL
#define JSON_MODULE "CloudRelayJsonApi"

namespace CPCAPI2
{
namespace CloudRelay
{
CloudRelayJsonProxyInterface::CloudRelayJsonProxyInterface(Phone* phone)
   : CPCAPI2::EventSource<CloudRelayHandle, CloudRelayHandler, CloudRelaySyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
     mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mTransport(NULL),
     mHandlePrefix(0),
     mNextCloudRelayHandle(1)
{
   mFunctionMap["onBroadcast"] = std::bind(&CloudRelayJsonProxyInterface::handleBroadcast, this, std::placeholders::_1);
   mFunctionMap["onMessage"] = std::bind(&CloudRelayJsonProxyInterface::handleMessage, this, std::placeholders::_1);

   JsonApi::JsonApiClientInterface* jsonApiClientIf = dynamic_cast<JsonApi::JsonApiClientInterface*>(JsonApi::JsonApiClient::getInterface(phone));
   setTransport(jsonApiClientIf->getTransport());

   mHandlePrefix = resip::Random::getCryptoRandom();
   while ((mHandlePrefix & 0xFFFF0000) == 0)
   {
      mHandlePrefix = (mHandlePrefix + 1) << 1;
   }
   mHandlePrefix = (mHandlePrefix & 0xFFFF0000);

   mProcessCheck.reset(new int(0));
}

CloudRelayJsonProxyInterface::~CloudRelayJsonProxyInterface()
{
}

void CloudRelayJsonProxyInterface::Release()
{
   reactorSafeReleaseAfter(&mReactor);
}

void CloudRelayJsonProxyInterface::release()
{
   delete this;
}

// JsonApiClientModule
void CloudRelayJsonProxyInterface::setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport)
{
   mTransport = transport;
}

int CloudRelayJsonProxyInterface::processIncoming(const std::shared_ptr<rapidjson::Document>& request)
{
   postToSdkThread(resip::resip_safe_bind(&CloudRelayJsonProxyInterface::processIncomingImpl, this, request, std::weak_ptr<int>(mProcessCheck)));
   return kSuccess;
}

void CloudRelayJsonProxyInterface::processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request, const std::weak_ptr<int>& processCheck)
{
   if (std::shared_ptr<int> pc = processCheck.lock())
   {
      const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
      const char* funcName = functionObjectVal["functionName"].GetString();

      FunctionMap::iterator it = mFunctionMap.find(funcName);
      if (it != mFunctionMap.end())
      {
         it->second(functionObjectVal);
      }
   }
}

CloudRelayHandle CloudRelayJsonProxyInterface::getRelay()
{
   CloudRelayHandle accountHandle = mHandlePrefix | mNextCloudRelayHandle++;
   postToSdkThread(resip::resip_safe_bind(&CloudRelayJsonProxyInterface::getRelayImpl, this, accountHandle));
   return accountHandle;
}

int CloudRelayJsonProxyInterface::getRelayImpl(CloudRelayHandle relay)
{
   JsonFunctionCall(mTransport, "getRelay", JSON_VALUE(relay));
   return kSuccess;
}

int CloudRelayJsonProxyInterface::setHandler(CloudRelayHandle conference, CloudRelayHandler* handler)
{
   setAppHandler(conference, handler);
   return kSuccess;
}

int CloudRelayJsonProxyInterface::setHandler(CloudRelayHandler* handler)
{
   addSdkObserver(handler);
   return kSuccess;
}

int CloudRelayJsonProxyInterface::start()
{
   postToSdkThread(resip::resip_safe_bind(&CloudRelayJsonProxyInterface::startImpl, this));
   return kSuccess;
}

int CloudRelayJsonProxyInterface::startImpl()
{
   return kSuccess;
}

int CloudRelayJsonProxyInterface::broadcast(CloudRelayHandle relay, const cpc::string& msg)
{
   postToSdkThread(resip::resip_safe_bind(&CloudRelayJsonProxyInterface::broadcastImpl, this, relay, msg));
   return kSuccess;
}

int CloudRelayJsonProxyInterface::broadcastImpl(CloudRelayHandle relay, const cpc::string& msg)
{
   JsonFunctionCall(mTransport, "broadcast", JSON_VALUE(relay), JSON_VALUE(msg));
   return kSuccess;
}

int CloudRelayJsonProxyInterface::sendTo(CloudRelayHandle relay, CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const cpc::string& msg)
{
   postToSdkThread(resip::resip_safe_bind(&CloudRelayJsonProxyInterface::sendToImpl, this, relay, jsonApiUser, msg));
   return kSuccess;
}

int CloudRelayJsonProxyInterface::sendToImpl(CloudRelayHandle relay, CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const cpc::string& msg)
{
   JsonFunctionCall(mTransport, "sendTo", JSON_VALUE(relay), JSON_VALUE(jsonApiUser), JSON_VALUE(msg));
   return kSuccess;
}

int CloudRelayJsonProxyInterface::handleBroadcast(const rapidjson::Value& functionObjectVal)
{
   CloudRelayHandle relay = -1;
   BroadcastEvent args;
   JsonDeserialize(functionObjectVal, "relay", relay, "args", args);
   fireEvent(cpcFunc(CloudRelayHandler::onBroadcast), relay, args);

   return kSuccess;
}

int CloudRelayJsonProxyInterface::handleMessage(const rapidjson::Value& functionObjectVal)
{
   CloudRelayHandle relay = -1;
   MessageEvent args;
   JsonDeserialize(functionObjectVal, "relay", relay, "args", args);
   fireEvent(cpcFunc(CloudRelayHandler::onMessage), relay, args);

   return kSuccess;
}

}
}
#endif
