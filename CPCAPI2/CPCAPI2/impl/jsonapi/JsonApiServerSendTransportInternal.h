#pragma once

#if !defined(CPCAPI2_JSON_API_SERVER_SEND_TRANSPORT_INTERNAL_H)
#define CPCAPI2_JSON_API_SERVER_SEND_TRANSPORT_INTERNAL_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiTransport.h"
#include "jsonapi/JsonApiServer_WebSocket.h"

// websocketpp
#include <websocketpp/connection.hpp>

// rapidjson
#include <document.h>

#include <memory>

namespace CPCAPI2
{
namespace JsonApi
{
class JsonApiServerSendTransportInternal
{
public:
   virtual ~JsonApiServerSendTransportInternal() {}

   virtual void setConnection(const JsonApiRequestInfo& jsonConnId) = 0;
   virtual int send(const rapidjson::Document& doc) = 0;
   virtual int send(const JsonApiRequestInfo& jsonConnId, const rapidjson::Document& doc) = 0;
   virtual int send(const JsonApiRequestInfo& jsonConnId, const CPCAPI2::Json::JsonDataPointer& eventData) = 0;
   virtual int send(JsonApiUserHandle jsonUser, const rapidjson::Document& doc) = 0;
   virtual int send(JsonApiUserHandle jsonUser, const CPCAPI2::Json::JsonDataPointer& eventData) = 0;
   virtual int validateAuthToken(const CPCAPI2::JsonApi::AuthToken& authToken, bool& isValid, std::map<resip::Data, resip::Data>* pubClaims) = 0;
};

}
}

#endif // CPCAPI2_JSON_API_SERVER_SEND_TRANSPORT_INTERNAL_H
