#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_BUSY_LAMP_FIELD_MODULE == 1)

#include "SipBusyLampFieldManagerInterface.h"
#include "SipBusyLampFieldManagerImpl.h"
#include "SipBusyLampFieldRemoteLineSetInfo.h"
#include "phone/PhoneInterface.h"

using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipDialogEvent;
using namespace CPCAPI2::SipConversation;

namespace CPCAPI2
{
namespace SipBusyLampField
{

SipBusyLampFieldManagerInterface::SipBusyLampFieldManagerInterface(Phone* phone)
   : EventSource2< EventHandler<SipBusyLampFieldHandler, SipAccount::SipAccountHandle> >(dynamic_cast<PhoneInterface*>(phone)),
     mAccountIf(NULL),
     mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   mAccountIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(phone));
   mStateManager = SipBusyLampFieldStateManager::getInterface(this);
   mSipDialogEventSubscriptionIf = dynamic_cast<SipDialogEventSubscriptionManagerInterface*>(SipDialogEventSubscriptionManager::getInterface(phone));
   mSipConversationIf = dynamic_cast<SipAVConversationManagerInterface*>(SipConversationManager::getInterface(phone));
}

SipBusyLampFieldManagerInterface::~SipBusyLampFieldManagerInterface()
{
   resip::Lock locker(mMutex);

   // Delete all the registered handlers and clear the map
   for (AccountMap::iterator it = mAccountMap.begin(); it != mAccountMap.end(); it++)
   {
      SipBusyLampFieldManagerImpl* blfManager = it->second;

      delete blfManager;
   }   
   mAccountMap.clear();
}

void SipBusyLampFieldManagerInterface::Release()
{
   delete this;
}

PhoneInterface* SipBusyLampFieldManagerInterface::phoneInterface()
{
   return mPhone;
}

int SipBusyLampFieldManagerInterface::setHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipBusyLampFieldHandler* handler)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&SipBusyLampFieldManagerInterface::setHandlerImpl, this, account, handler);
   if (handler == NULL)
   {
      // removing the handler involves two steps:
      // 1. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);

      // 2. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      executeOnSdkThread(f);
   }
   else
   {
      postToSdkThread(f);
   }
   
   return kSuccess;
}

void SipBusyLampFieldManagerInterface::setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipBusyLampFieldHandler* handler)
{
   resip::Lock locker(mMutex);

   // Retrieve the manager associated with the account specified
   SipBusyLampFieldManagerImpl* blfManager = getBusyLampFieldManager(account);
   if (!blfManager)
   {
      // No manager associated with the account

      // Get the associated account object
      CPCAPI2::SipAccount::SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
      if (acct)
      {
         // Create a new manager
         blfManager = new SipBusyLampFieldManagerImpl(mPhone, this, acct->getHandle(), mStateManager, mSipDialogEventSubscriptionIf, mSipConversationIf); // Destroyed in the destructor of this class

         // Keep mapping account -> manager
         mAccountMap[account] = blfManager;
      }
      else
      {
         mAccountIf->fireError("Invalid account handle for SipBusyLampFieldManagerInterface::setHandler");
         return;
      }
   }

   mSipDialogEventSubscriptionIf->setHandlerImpl(account, blfManager);
   
   auto it = mHandlers.find(account);
   if (mHandlers.end() != it)
   {
     removeAppHandler(it->second, account);
   }

   mHandlers[account] = handler;
   if (nullptr != handler)
   {
      addAppHandler(handler, account);
   }
}

SipBusyLampFieldRemoteLineSetHandle SipBusyLampFieldManagerInterface::createBusyLampFieldRemoteLineSet(CPCAPI2::SipAccount::SipAccountHandle account, const SipBusyLampFieldRemoteLineSetSettings& settings)
{
   SipBusyLampFieldRemoteLineSetHandle remoteLineSetHandle = SipBusyLampFieldRemoteLineSetInfo::nextRemoteLineSet++;
   postToSdkThread(resip::resip_bind(&SipBusyLampFieldManagerInterface::createBusyLampFieldRemoteLineSetImpl, this, account, remoteLineSetHandle, settings));
   return remoteLineSetHandle;
}

void SipBusyLampFieldManagerInterface::createBusyLampFieldRemoteLineSetImpl(SipAccount::SipAccountHandle account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineSetSettings& settings) 
{
   resip::Lock locker(mMutex);

   // Retrieve the account
   CPCAPI2::SipAccount::SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (!acct)
   {
      // No account found. Send an error
      cpc::string msg = cpc::string("Creating remote line set with invalid account handle: ") + cpc::to_string(account) +
         ", SipBusyLampFieldRemoteLineSetHandle invalid: " + cpc::to_string(remoteLineSet);
      mAccountIf->fireError(msg);
      return;
   }

   // Make sure the account is enabled
   if (!acct->isEnabled())
   {
      // Account not enabled. Send an error
      cpc::string msg = cpc::string("Creating remote line set before account enabled: ") + cpc::to_string(account) +
         ", SipBusyLampFieldRemoteLineSetHandle invalid: " + cpc::to_string(remoteLineSet);
      mAccountIf->fireError(msg);
      return;
   }

   // Retrieve the manager associated with the account specified
   SipBusyLampFieldManagerImpl* blfManager = getBusyLampFieldManager(account);
   if (!blfManager)
   {
      cpc::string msg = cpc::string("Creating remote line set with no handler set for account: ") + cpc::to_string(account) +
         ", SipBusyLampFieldRemoteLineSetHandle invalid: " + cpc::to_string(remoteLineSet);
      mAccountIf->fireError(msg);
      return;
   }

   // Create a new remote line set using the newly allocated handle   
   SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo = blfManager->createRemoteLineSetInfo(remoteLineSet);
   remoteLineSetInfo->mSettings = settings;
}

int SipBusyLampFieldManagerInterface::addRemoteLine(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine)
{
   postToSdkThread(resip::resip_bind(&SipBusyLampFieldManagerInterface::addRemoteLineImpl, this, remoteLineSet, remoteLine));
   return kSuccess;
}

void SipBusyLampFieldManagerInterface::addRemoteLineImpl(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine)
{
   resip::Lock locker(mMutex);

   // Get info on the remote line set
   SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo = getRemoteLineSetInfo(remoteLineSet);
   if (!remoteLineSetInfo)
      return;

   // Get the manager associated with the account asssociated with the remote line set
   SipBusyLampFieldManagerImpl* blfManager = getBusyLampFieldManager(remoteLineSetInfo->mAccountHandle);
   if (!blfManager)
   {
      return;
   }

   if (!remoteLineSetInfo->mSettings.resourceListAddress.empty())
   {
      blfManager->fireError(remoteLineSet, "Cannot add remote lines when resource list URI is configured.");
      return;
   }

   // Add the remote line
   remoteLineSetInfo->mRemoteLineSubscriptions[remoteLine] = SipBusyLampFieldRemoteLineInfo();
}

int SipBusyLampFieldManagerInterface::start(SipBusyLampFieldRemoteLineSetHandle remoteLineSet)
{
   postToSdkThread(resip::resip_bind(&SipBusyLampFieldManagerInterface::startImpl, this, remoteLineSet));
   return kSuccess;
}

void SipBusyLampFieldManagerInterface::startImpl(SipBusyLampFieldRemoteLineSetHandle remoteLineSet)
{
   resip::Lock locker(mMutex);

   // Get info on the remote line set
   SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo = getRemoteLineSetInfo(remoteLineSet);
   if (!remoteLineSetInfo)
   {
      return;
   }

   // Get the manager associated with the account asssociated with the remote line set
   SipBusyLampFieldManagerImpl* blfManager = getBusyLampFieldManager(remoteLineSetInfo->mAccountHandle);
   if (!blfManager)
   {
      return;
   }

   // Make sure there is at least one remote line added to the set
   if (remoteLineSetInfo->mRemoteLineSubscriptions.empty() && remoteLineSetInfo->mSettings.resourceListAddress.empty())
   {
      // No remote line specified. Send an error
      blfManager->fireError(remoteLineSet, "Cannot start subscriptions. No remote line have been added or no resource list URI set.");
      return;
   }

   // Start subscriptions of the remote lines
   blfManager->startSubscriptions(remoteLineSetInfo);
}

int SipBusyLampFieldManagerInterface::end(SipBusyLampFieldRemoteLineSetHandle remoteLineSet)
{
   postToSdkThread(resip::resip_bind(&SipBusyLampFieldManagerInterface::endImpl, this, remoteLineSet));
   return kSuccess;
}

void SipBusyLampFieldManagerInterface::endImpl(SipBusyLampFieldRemoteLineSetHandle remoteLineSet)
{
   resip::Lock locker(mMutex);

   // Get info on the remote line set
   SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo = getRemoteLineSetInfo(remoteLineSet);
   if (!remoteLineSetInfo)
   {
      return;
   }

   // Get the manager associated with with the remote line set
   SipBusyLampFieldManagerImpl* blfManager = getBusyLampFieldManager(remoteLineSetInfo->mAccountHandle);
   if (!blfManager)
   {
      return;
   }

   // Terminate subscriptions of the remote lines
   blfManager->endSubscriptions(remoteLineSetInfo);
}

CPCAPI2::SipConversation::SipConversationHandle SipBusyLampFieldManagerInterface::answerCall(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, const cpc::string& dialogId, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor)
{
   resip::Lock locker(mMutex);

   // Get info on the remote line set
   SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo = getRemoteLineSetInfo(remoteLineSet);
   if (!remoteLineSetInfo)
   {
      return 0;
   }

   // Get the manager associated with with the remote line set
   SipBusyLampFieldManagerImpl* blfManager = getBusyLampFieldManager(remoteLineSetInfo->mAccountHandle);
   if (!blfManager)
   {
      return 0;
   }

   // Prepare the answer call operation
   return blfManager->answerCall(remoteLineSetInfo, remoteLine, dialogId, mediaDescriptor);
}

CPCAPI2::SipConversation::SipConversationHandle SipBusyLampFieldManagerInterface::joinCall(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, const cpc::string& dialogId, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor, BargeInMode bargeInMode, AlertMode alertMode)
{
   resip::Lock locker(mMutex);

   // Get info on the remote line set
   SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo = getRemoteLineSetInfo(remoteLineSet);
   if (!remoteLineSetInfo)
   {
      return 0;
   }

   // Get the manager associated with with the remote line set
   SipBusyLampFieldManagerImpl* blfManager = getBusyLampFieldManager(remoteLineSetInfo->mAccountHandle);
   if (!blfManager)
   {
      return 0;
   }

   // Prepare the join call operation
   return blfManager->joinCall(remoteLineSetInfo, remoteLine, dialogId, mediaDescriptor, bargeInMode, alertMode);
}

SipBusyLampFieldManagerImpl* SipBusyLampFieldManagerInterface::getBusyLampFieldManager(CPCAPI2::SipAccount::SipAccountHandle account) const
{
   AccountMap::const_iterator it = mAccountMap.find(account);
   return (it != mAccountMap.end()) ? it->second : NULL;
}

SipBusyLampFieldRemoteLineSetInfo* SipBusyLampFieldManagerInterface::getRemoteLineSetInfo(SipBusyLampFieldRemoteLineSetHandle remoteLineSet) const
{
   // Browse through all the registered accounts to find
   // the manager associated with the handle specified
   for (AccountMap::const_iterator it = mAccountMap.begin(); it != mAccountMap.end(); it++)
   {
      SipBusyLampFieldManagerImpl* blfManager = it->second;
      SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo = blfManager->getRemoteLineSetInfo(remoteLineSet);
      if (remoteLineSetInfo)
      {
         return remoteLineSetInfo;
      }
   }

   return NULL;
}

std::ostream& operator<<(std::ostream& os, const RemoteLineNewSubscriptionEvent& evt)
{
   return os << "RemoteLineNewSubscriptionEvent";
}

std::ostream& operator<<(std::ostream& os, const RemoteLineSubscriptionStateChangedEvent& evt)
{
   return os << "RemoteLineSubscriptionStateChangedEvent";
}

std::ostream& operator<<(std::ostream& os, const RemoteLineSubscriptionEndedEvent& evt)
{
   return os << "RemoteLineSubscriptionEndedEvent";
}

std::ostream& operator<<(std::ostream& os, const RemoteLineStateChangedEvent& evt)
{
   os << "account: " << evt.account << ", remoteLine: " << evt.remoteLine << ", calls: ";
   for (cpc::vector<SipBusyLampFieldRemoteLineCallInfo>::const_iterator it = evt.calls.begin(); it != evt.calls.end(); ++it)
   {
      os << "[" << *it << "]";
   }
   return os;
}

std::ostream& operator<<(std::ostream& os, const RemoteLineEmptySubscriptionsEvent& evt)
{
   os << "account: " << evt.account << ", all subscriptions removed";

   return os;
}

std::ostream& operator<<(std::ostream& os, const SipBusyLampField::ErrorEvent& evt)
{
   return os << "SipBusyLampField";
}

std::ostream& operator<<(std::ostream& os, const SipBusyLampFieldRemoteLineCallInfo& info)
{
   return os << "remoteLine: " << info.remoteLine << ", dialog: " << info.dialog << ", isHeld: " 
             << info.isHeld << ", isParked: " << info.isParked;
}

}
}

#endif // CPCAPI2_BRAND_SIP_BUSY_LAMP_FIELD_MODULE
