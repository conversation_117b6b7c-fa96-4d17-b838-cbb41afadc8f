#pragma once

#if !defined(CPCAPI2_VIDEO_STREAMING_INTERFACE_H)
#define CPCAPI2_VIDEO_STREAMING_INTERFACE_H

#include "cpcapi2defs.h"
#include "audiotranscription/AudioTrans.h"
#include "audiotranscription/AudioTransSyncHandler.h"
#include "phone/Cpcapi2EventSource.h"

#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>

#include <map>
#include <thread>

namespace CPCAPI2
{
class PhoneInterface;

namespace AudioTrans
{
class AudioTransImpl;

class AudioTransInterface : public AudioTransManager
                              , public PhoneModule
                              , public CPCAPI2::EventSource<AudioTransHandle, AudioTransHandler, AudioTransSyncHandler>
{
public:
   AudioTransInterface(Phone* phone);
   virtual ~AudioTransInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // AudioTransManager
   virtual int startAudioTransServer() OVERRIDE;
   virtual int stopAudioTransServer() OVERRIDE;
   virtual int setAudioTransServer(AudioTransManager* masterAudioTransManager) OVERRIDE;

   virtual AudioTransHandle createAudioTranscriptionSession() OVERRIDE;
   virtual int setAudioTransSettings(AudioTransHandle videoStream, const AudioTransSettings& settings) OVERRIDE;
   virtual int setAudioTransHandler(AudioTransHandle videoStream, AudioTransHandler* handler) OVERRIDE;
   virtual int startTranscription(AudioTransHandle videoStream) OVERRIDE;
   virtual int stopTranscription(AudioTransHandle videoStream) OVERRIDE;

   void post(resip::ReadCallbackBase* f);
   void fireEventInternal(AudioTransHandle h, const AudioTranscriptionResult& evt);

private:
   void startAudioTransServerImpl();
   void stopAudioTransServerImpl();
   void setAudioTransServerImpl(AudioTransManager* masterMgr);
   void createAudioTransImpl(AudioTransHandle videoStream);
   void setAudioTransSettingsImpl(AudioTransHandle videoStream, const AudioTransSettings& settings);
   void startAudioTransImpl(AudioTransHandle videoStream);
   void stopAudioTransImpl(AudioTransHandle videoStream);

private:
   PhoneInterface* mPhone;
   typedef std::map<AudioTransHandle, AudioTransImpl*> InstanceMap;
   InstanceMap mInstMap;
};

class AudioTransHandleFactory
{
public:
   static AudioTransHandle getNext() { return sNextHandle++; }
private:
   static std::atomic<AudioTransHandle> sNextHandle;
};
}
}

#endif // CPCAPI2_VIDEO_STREAMING_INTERFACE_H
