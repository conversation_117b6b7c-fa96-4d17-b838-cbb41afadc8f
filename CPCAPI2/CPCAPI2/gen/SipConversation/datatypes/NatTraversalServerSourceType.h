#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_NATTRAVERSALSERVERSOURCETYPE_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_NATTRAVERSALSERVERSOURCETYPE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  enum class NatTraversalServerSourceType
  {
    None,
    Srv,
    Custom
  };

  inline std::string to_string(NatTraversalServerSourceType value)
  {
    switch (value)
    {
      case NatTraversalServerSourceType::None:
        return "None";
      case NatTraversalServerSourceType::Srv:
        return "SRV";
      case NatTraversalServerSourceType::Custom:
        return "Custom";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, NatTraversalServerSourceType value)
  {
    switch (value)
    {
      case NatTraversalServerSourceType::None:
        os << "None";
        break;
      case NatTraversalServerSourceType::Srv:
        os << "SRV";
        break;
      case NatTraversalServerSourceType::Custom:
        os << "Custom";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const NatTraversalServerSourceType& e)
  {
    switch (e)
    {
      case NatTraversalServerSourceType::None:
        j = "None";
        break;
      case NatTraversalServerSourceType::Srv:
        j = "SRV";
        break;
      case NatTraversalServerSourceType::Custom:
        j = "Custom";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, NatTraversalServerSourceType& e)
  {
      if (0 == j.get<std::string>().compare("None"))
    {
      e = NatTraversalServerSourceType::None;
    }
    else if (0 == j.get<std::string>().compare("SRV"))
    {
      e = NatTraversalServerSourceType::Srv;
    }
    else if (0 == j.get<std::string>().compare("Custom"))
    {
      e = NatTraversalServerSourceType::Custom;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum NatTraversalServerSourceType"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_NATTRAVERSALSERVERSOURCETYPE_H
