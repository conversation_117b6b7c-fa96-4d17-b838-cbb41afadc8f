#pragma once

#ifndef CPCAPI2_MOS_ESTIMATOR_H
#define CPCAPI2_MOS_ESTIMATOR_H

#include "call/SipConversationTypes.h"

namespace CPCAPI2
{
   class MosEstimator
   {
   public:
      static short calculateNetworkMos(unsigned short localFractionLost, 
                                       unsigned int totalReceivedPackets, int64_t rttMs, unsigned int averageJitterMs,
                                       unsigned int numIntervalsWithoutUpdate);

   private:


   };
}



#endif // CPCAPI2_MOS_ESTIMATOR_H