#pragma once

#if !defined(CPCAPI2_CAPABILITY_DISCOVERY_INTERFACE_H)
#define CPCAPI2_CAPABILITY_DISCOVERY_INTERFACE_H

#include "cpcapi2defs.h"
#include "capability/RcsCapabilityDiscovery.h"
#include "capability/RcsCapabilityDiscoveryHandler.h"
#include "../phone/PhoneModule.h"
#include "../phone/PhoneInterface.h"

#include <rutil/MultiReactor.hxx>
#include <rutil/DeadlineTimer.hxx>
#include <rutil/RecursiveMutex.hxx>

#include <map>
#include <chrono>

namespace CPCAPI2
{

namespace SipAccount
{
class SipAccountInterface;
}

namespace RcsCapabilityDiscovery
{
class RcsCapabilityDiscoveryImpl;

class RcsCapabilityDiscoveryInterface :
   public CPCAPI2::EventSource2<CPCAPI2::EventHandler<RcsCapabilityDiscoveryHandler, CPCAPI2::SipAccount::SipAccountHandle> >,
   public RcsCapabilityDiscoveryManager,
   public PhoneModule,
   public RcsCapabilityDiscoveryHandler,
   public resip::DeadlineTimerHandler
{
public:
   RcsCapabilityDiscoveryInterface(Phone* cpcPhone);
   virtual ~RcsCapabilityDiscoveryInterface();

   virtual void Release() OVERRIDE;

   // begin RcsCapabilityDiscoveryManager
   virtual int setHandler(
      CPCAPI2::SipAccount::SipAccountHandle account,
      RcsCapabilityDiscoveryHandler* handler) OVERRIDE;

   virtual int importToCache(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::vector<RcsCapabilityStatus>& cache) OVERRIDE;

   virtual int exportFromCache(
         CPCAPI2::SipAccount::SipAccountHandle account,
         cpc::vector<RcsCapabilityStatus>& cache) OVERRIDE;

   virtual int addContact(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& targetAddress) OVERRIDE;

   virtual int removeContact(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& targetAddress) OVERRIDE;

   virtual int synchronizeAllContacts(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::vector<cpc::string>& targetAddresses) OVERRIDE;

   virtual int getContactCapabilityStatus(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& targetAddress,
      RcsCapabilityStatus& status) OVERRIDE;

   virtual int setMyCapabilities(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const RcsCapabilitySet& caps) OVERRIDE;

   virtual int updateSettings(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const RcsCapabilityDiscoverySettings& settings) OVERRIDE;
   // end RcsCapabilityDiscoveryManager

   // begin RcsCapabilityDiscoveryHandler
   virtual int onContactCapabilityStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const RcsOnContactCapabilityStatusChangedEvent& args) OVERRIDE;
   // end RcsCapabilityDiscoveryHandler

private:
   void setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, RcsCapabilityDiscoveryHandler* handler);
   RcsCapabilityDiscoveryImpl* getCapabilityDiscoveryImpl(CPCAPI2::SipAccount::SipAccountHandle account);

   int addContacts(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const std::set<cpc::string>& targetAddresses);

   int removeContacts(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const std::set<cpc::string>& targetAddresses);

   void addContactsImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::vector<RcsCapabilityStatus>& cache);

   void getContactCapabilityStatusImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& targetAddress);

   void setMyCapabilitiesImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const RcsCapabilitySet& caps);

   void addContactsImpl2(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const std::map<cpc::string, unsigned int>& toAdd);

   void removeContactsImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const std::set<cpc::string>& targetAddresses);

   void updateSettingsImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const RcsCapabilityDiscoverySettings& settings);

   // begin resip::DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;
   // end resip::DeadlineTimerHandler

private:
   friend class RcsCapabilityDiscoveryImpl;

   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, std::pair<RcsCapabilityDiscoveryHandler*, RcsCapabilityDiscoveryImpl*> > CapabilityDiscoveryMap;
   std::shared_ptr<CapabilityDiscoveryMap> mCapabilityDiscoveryMap;
   CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;

   typedef std::chrono::time_point<std::chrono::system_clock> time_point;

   struct RcsCapabilityStatusEx : RcsCapabilityStatus
   {
      unsigned int signalingStatusCode;

      RcsCapabilityStatusEx() : signalingStatusCode(0) {}
   };

   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, std::map<cpc::string, RcsCapabilityStatusEx> > CacheMap;
   CacheMap mCache;
   resip::RecursiveMutex mCacheLock;
   std::map<CPCAPI2::SipAccount::SipAccountHandle, RcsCapabilityDiscoveryHandler*> mHandlers;

   resip::DeadlineTimer<resip::MultiReactor> mTimer;
   static const unsigned int sTimeoutIntervalMs;
   CPCAPI2::PhoneInterface* mPhone;
};

std::ostream& operator<<(std::ostream& os, const RcsOnContactCapabilityStatusChangedEvent& evt);

}
}
#endif // CPCAPI2_CAPABILITY_DISCOVERY_INTERFACE_H
