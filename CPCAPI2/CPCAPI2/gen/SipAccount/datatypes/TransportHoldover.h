#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_TRANSPORTHOLDOVER_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_TRANSPORTHOLDOVER_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  enum class TransportHoldover
  {
    None,
    All,
    V4,
    V6
  };

  inline std::string to_string(TransportHoldover value)
  {
    switch (value)
    {
      case TransportHoldover::None:
        return "None";
      case TransportHoldover::All:
        return "All";
      case TransportHoldover::V4:
        return "V4";
      case TransportHoldover::V6:
        return "V6";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, TransportHoldover value)
  {
    switch (value)
    {
      case TransportHoldover::None:
        os << "None";
        break;
      case TransportHoldover::All:
        os << "All";
        break;
      case TransportHoldover::V4:
        os << "V4";
        break;
      case TransportHoldover::V6:
        os << "V6";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const TransportHoldover& e)
  {
    switch (e)
    {
      case TransportHoldover::None:
        j = "None";
        break;
      case TransportHoldover::All:
        j = "All";
        break;
      case TransportHoldover::V4:
        j = "V4";
        break;
      case TransportHoldover::V6:
        j = "V6";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, TransportHoldover& e)
  {
      if (0 == j.get<std::string>().compare("None"))
    {
      e = TransportHoldover::None;
    }
    else if (0 == j.get<std::string>().compare("All"))
    {
      e = TransportHoldover::All;
    }
    else if (0 == j.get<std::string>().compare("V4"))
    {
      e = TransportHoldover::V4;
    }
    else if (0 == j.get<std::string>().compare("V6"))
    {
      e = TransportHoldover::V6;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum TransportHoldover"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_TRANSPORTHOLDOVER_H
