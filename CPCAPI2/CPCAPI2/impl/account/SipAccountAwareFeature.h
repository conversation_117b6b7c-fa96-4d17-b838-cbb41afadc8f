#pragma once

#if !defined(CPCAPI2_IACCOUNT_AWARE_H)
#define CPCAPI2_IACCOUNT_AWARE_H

#include <resip/dum/MasterProfile.hxx>
#include <resip/dum/DialogUsageManager.hxx>
#include "AppDialogSetFactory.h"
#include "SipAccountImpl.h"

namespace CPCAPI2
{

namespace SipAccount
{

struct SipRegistrationSuccessEvent
{
   resip::Tuple server;
   resip::NameAddr localContact;
   resip::Data overrideSourceIpSignalling;

   SipRegistrationSuccessEvent() : overrideSourceIpSignalling("") {}
   virtual~ SipRegistrationSuccessEvent() {}
};

struct SipRegistrationDialogSuccessEvent
{
   resip::SipMessage response;
   SipRegistrationDialogSuccessEvent() {}
   virtual~ SipRegistrationDialogSuccessEvent() {}
};

struct SipNetworkChangeInitiatedEvent
{
   CPCAPI2::NetworkTransport newTransport;
   bool accountDisableTriggeredForNetworkChange;
   SipNetworkChangeInitiatedEvent() : newTransport(CPCAPI2::TransportNone), accountDisableTriggeredForNetworkChange(false) {}
   virtual~ SipNetworkChangeInitiatedEvent() {}
};

class SipAccountAwareFeature
{
public:
   virtual int registerSdkDialogSetFactory(AppDialogSetFactory& factory) { return kSuccess; };
   virtual int registerSdkPagerMessageHandler(CPPagerMessageHandler& pagerMessageHandler) { return kSuccess; }
   virtual int adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile) = 0;
   virtual int addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig) = 0;
   virtual int onRegistrationSuccess(const SipRegistrationSuccessEvent& args) { return 0; }
   virtual int onAccountNetworkChangeInitiated(const SipNetworkChangeInitiatedEvent& args) { return 0; }
   virtual int onRegistrationDialogSuccess(const SipRegistrationDialogSuccessEvent& args) { return 0; }
   // called when a redirect is received
   virtual int onRedirectReceived(const resip::SipMessage& originalRequest, const resip::SipMessage& response, resip::AppDialogSetHandle h) { return kSuccess; }

   // called when a new request is about to be made as a result of a redirect. The handler can optionally adorn the request.
   // return false to disallow the target from being tried.
   // allowing the target to be tried will result in DUM sending 'request'.
   virtual bool onRedirectTryingNextTarget(const resip::Uri& target, resip::SipMessage& request, resip::AppDialogSetHandle h) { return true; }

   // called when account is asked to disable; gives an opportunity to defer disable
   // until pending state is cleared (e.g. call, file transfer, etc); take a copy of the
   // disable condition arg when returning false and use its reset method when done
   virtual bool canDisable(const AccountDisableCondition& cond) { return true; }

   bool& isDumShutdown() { return mDumShutdown; }
   bool isDumShutdown() const { return mDumShutdown; }

   // called just prior to shutting down DUM;
   // gives an opportunity to clean up by ending any DUM usages
   virtual int onDumBeingDestroyed() = 0;

   // called after DUM is destroyed
   virtual int afterDumDestroyed() { return 0; }

   // called by SipAccount when shutdown is forced; clean up 
   // all resources immediately and return
   virtual void release() = 0;

protected:
   SipAccountAwareFeature() : mDumShutdown(false) {}
   virtual ~SipAccountAwareFeature() {}

   bool mDumShutdown;
};

}

}

#endif // CPCAPI2_IACCOUNT_AWARE_H