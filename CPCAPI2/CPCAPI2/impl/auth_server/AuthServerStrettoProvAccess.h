#pragma once

#if !defined(CPCAPI2_AUTH_SERVER_STRETTO_PROV_ACCESS)
#define CPCAPI2_AUTH_SERVER_STRETTO_PROV_ACCESS

#include "AuthServerBackend.h"
#include "strettoprovisioning/StrettoProvisioning.h"
#include "strettoprovisioning/StrettoProvisioningHandler.h"
#include "strettoprovisioning/StrettoProvisioningSyncHandler.h"

#include <cpcstl/string.h>
#include <memory>
#include <future>

namespace CPCAPI2
{
namespace AuthServer
{
class StrettoProvAccess : public AuthServerBackend,
                          public CPCAPI2::StrettoProvisioning::StrettoProvisioningHandler,
                          public CPCAPI2::StrettoProvisioning::StrettoProvisioningSyncHandler
{
public:
   StrettoProvAccess(CPCAPI2::Phone* phone);
   virtual ~StrettoProvAccess();

   int initialize(const cpc::string& dbfile) override;
   int addUser(const cpc::string& username, const cpc::string& password_hash) override;
   int updatePassword(const cpc::string& username, const cpc::string& new_password_hash) override;
   int queryUser(const cpc::string& username, const cpc::string& password_hash, bool& userExists, cpc::string& storedPassword) override;
   int flushUsers() override;

   virtual int onProvisioningSuccess(const CPCAPI2::StrettoProvisioning::StrettoProvisioningHandle& handle, const CPCAPI2::StrettoProvisioning::StrettoProvisioningEvent& evt) OVERRIDE;
   virtual int onProvisioningError(const CPCAPI2::StrettoProvisioning::StrettoProvisioningHandle& handle, const CPCAPI2::StrettoProvisioning::StrettoProvisioningErrorEvent& evt) OVERRIDE;
   virtual int onError(const CPCAPI2::StrettoProvisioning::StrettoProvisioningHandle& handle, const CPCAPI2::StrettoProvisioning::ErrorEvent& evt) OVERRIDE;


private:
   CPCAPI2::StrettoProvisioning::StrettoProvisioning* mProvMgr;
   CPCAPI2::StrettoProvisioning::StrettoProvisioningHandle mProvHdl;
   
   struct ProvServerResponse
   {
      bool success = false;
      CPCAPI2::StrettoProvisioning::StrettoProvisioningEvent successEvt;
      CPCAPI2::StrettoProvisioning::StrettoProvisioningErrorEvent provErrorEvt;
      CPCAPI2::StrettoProvisioning::ErrorEvent errorEvt;
   };
   std::promise<ProvServerResponse> mProvResponse;
};
}
}

#endif // CPCAPI2_AUTH_SERVER_STRETTO_PROV_ACCESS
