#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_PHONE_PHONEEVENTS_H
#define JSONRPC_CPCAPI2_PHONE_PHONEEVENTS_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  enum class PhoneEvents
  {
    PhoneDotLog,
    PhoneDotOnLicensingError,
    PhoneDotOnLicensingSuccess,
    PhoneDotOnEventQueueError
  };

  inline std::string to_string(PhoneEvents value)
  {
    switch (value)
    {
      case PhoneEvents::PhoneDotLog:
        return "Phone.Log";
      case PhoneEvents::PhoneDotOnLicensingError:
        return "Phone.onLicensingError";
      case PhoneEvents::PhoneDotOnLicensingSuccess:
        return "Phone.onLicensingSuccess";
      case PhoneEvents::PhoneDotOnEventQueueError:
        return "Phone.onEventQueueError";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, PhoneEvents value)
  {
    switch (value)
    {
      case PhoneEvents::PhoneDotLog:
        os << "Phone.Log";
        break;
      case PhoneEvents::PhoneDotOnLicensingError:
        os << "Phone.onLicensingError";
        break;
      case PhoneEvents::PhoneDotOnLicensingSuccess:
        os << "Phone.onLicensingSuccess";
        break;
      case PhoneEvents::PhoneDotOnEventQueueError:
        os << "Phone.onEventQueueError";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const PhoneEvents& e)
  {
    switch (e)
    {
      case PhoneEvents::PhoneDotLog:
        j = "Phone.Log";
        break;
      case PhoneEvents::PhoneDotOnLicensingError:
        j = "Phone.onLicensingError";
        break;
      case PhoneEvents::PhoneDotOnLicensingSuccess:
        j = "Phone.onLicensingSuccess";
        break;
      case PhoneEvents::PhoneDotOnEventQueueError:
        j = "Phone.onEventQueueError";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, PhoneEvents& e)
  {
      if (0 == j.get<std::string>().compare("Phone.Log"))
    {
      e = PhoneEvents::PhoneDotLog;
    }
    else if (0 == j.get<std::string>().compare("Phone.onLicensingError"))
    {
      e = PhoneEvents::PhoneDotOnLicensingError;
    }
    else if (0 == j.get<std::string>().compare("Phone.onLicensingSuccess"))
    {
      e = PhoneEvents::PhoneDotOnLicensingSuccess;
    }
    else if (0 == j.get<std::string>().compare("Phone.onEventQueueError"))
    {
      e = PhoneEvents::PhoneDotOnEventQueueError;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum PhoneEvents"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_PHONEEVENTS_H
