#pragma once

#if !defined(CPCAPI2_CONFERENCE_BRIDGE_JSON_INTERFACE_H)
#define CPCAPI2_CONFERENCE_BRIDGE_JSON_INTERFACE_H

#include "interface/experimental/confbridge/ConferenceBridgeJsonApi.h"
#include "interface/experimental/confbridge/ConferenceBridgeManager.h"
#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "confbridge/ConferenceBridgeHandler.h"
#include "confbridge/ConferenceBridgeSyncHandler.h"
#include "jsonapi/JsonApiServerModule.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>

namespace CPCAPI2
{
class PhoneInterface;
namespace ConferenceBridge
{
class ConferenceBridgeJsonServerInterface : public CPCAPI2::ConferenceBridge::ConferenceBridgeHandler,
                                           public CPCAPI2::ConferenceBridge::ConferenceDetailsHandler,
                                           public CPCAPI2::ConferenceBridge::ConferenceListHandler,
                                           public CPCAPI2::ConferenceBridge::ConferenceBridgeJsonA<PERSON>,
                                           public CPCAPI2::ConferenceBridge::ConferenceBridgeSync<PERSON>and<PERSON>,
                                           public CPCAPI2::JsonApi::JsonApiServerModule,
                                           public CPCAPI2::PhoneModule
{
public:
   ConferenceBridgeJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~ConferenceBridgeJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE {
      return 0;
   }
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request, const std::shared_ptr<resip::Data>& binaryData) OVERRIDE;
   virtual int handleConnectionClosed(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn) OVERRIDE;

   // Inherited via ConferenceBridgeHandler
   virtual int onConferenceDetails(ConferenceHandle conference, const ConferenceDetailsResult& args) OVERRIDE;
   virtual int onConferenceNotFound(ConferenceHandle conference, const ConferenceNotFoundResult& args) OVERRIDE;
   virtual int onConferenceEnded(ConferenceHandle conference, const ConferenceEndedEvent& args) OVERRIDE;
   virtual int onParticipantListState(ConferenceHandle conference, const ParticipantListState& args) OVERRIDE;
   virtual int onConferenceTranscriptionResult(ConferenceHandle conference, const ConferenceTranscriptionEvent& args) OVERRIDE;

   // ConferenceListHandler
   virtual int onConferenceList(const ConferenceListResult& args) OVERRIDE;

private:
   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request, const std::shared_ptr<resip::Data>& binaryData);
   void handleConnectionClosedImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn);

   int handleSetHandler(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleCreateConference(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleAddSipEndpoint(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleQueryParticipantList(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleQueryConferenceDetails(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleQueryConferenceDetails2(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleQueryConferenceList(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleCreateWebParticipant(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleDestroyWebParticipant(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleSetParticipantPermissions(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleSetWebParticipantIdentity(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleSendPeerConnectionOffer(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleSendPeerConnectionAnswer(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleTakeParticipantSnapshot(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleSetParticipantPhoto(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleAddAssociatedConference(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleDestroyConference(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleAddToFloor(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleRemoveFromFloor(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleRequestFloor(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleSetStreamingEnabled(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleSetRecordingEnabled(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleSetTranscriptionEnabled(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);

private:
   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(CPCAPI2::JsonApi::JsonApiRequestInfo,const rapidjson::Value&,const std::shared_ptr<resip::Data>&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;
   CPCAPI2::ConferenceBridge::ConferenceBridgeManager* mConfBridgeMgr;
   typedef std::map<CPCAPI2::JsonApi::JsonApiRequestInfo, std::vector<CPCAPI2::ConferenceBridge::ConferenceParticipantHandle> > MapConnToWebParticipants;
   MapConnToWebParticipants mMapConnToWebPart;
};
}
}
#endif // CPCAPI2_CONFERENCE_BRIDGE_JSON_INTERFACE_H
