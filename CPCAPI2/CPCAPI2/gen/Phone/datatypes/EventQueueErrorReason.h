#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_PHONE_EVENTQUEUEERRORREASON_H
#define JSONRPC_CPCAPI2_PHONE_EVENTQUEUEERRORREASON_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  enum class EventQueueErrorReason
  {
    EventQueueErrorReasonInvalidQueueId,
    EventQueueErrorReasonUnknown
  };

  inline std::string to_string(EventQueueErrorReason value)
  {
    switch (value)
    {
      case EventQueueErrorReason::EventQueueErrorReasonInvalidQueueId:
        return "EventQueueErrorReason_InvalidQueueId";
      case EventQueueErrorReason::EventQueueErrorReasonUnknown:
        return "EventQueueErrorReason_Unknown";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, EventQueueErrorReason value)
  {
    switch (value)
    {
      case EventQueueErrorReason::EventQueueErrorReasonInvalidQueueId:
        os << "EventQueueErrorReason_InvalidQueueId";
        break;
      case EventQueueErrorReason::EventQueueErrorReasonUnknown:
        os << "EventQueueErrorReason_Unknown";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const EventQueueErrorReason& e)
  {
    switch (e)
    {
      case EventQueueErrorReason::EventQueueErrorReasonInvalidQueueId:
        j = "EventQueueErrorReason_InvalidQueueId";
        break;
      case EventQueueErrorReason::EventQueueErrorReasonUnknown:
        j = "EventQueueErrorReason_Unknown";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, EventQueueErrorReason& e)
  {
      if (0 == j.get<std::string>().compare("EventQueueErrorReason_InvalidQueueId"))
    {
      e = EventQueueErrorReason::EventQueueErrorReasonInvalidQueueId;
    }
    else if (0 == j.get<std::string>().compare("EventQueueErrorReason_Unknown"))
    {
      e = EventQueueErrorReason::EventQueueErrorReasonUnknown;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum EventQueueErrorReason"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_EVENTQUEUEERRORREASON_H
