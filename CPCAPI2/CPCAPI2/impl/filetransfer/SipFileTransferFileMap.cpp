#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1 || CPCAPI2_BRAND_XMPP_FILE_TRANSFER_MODULE)

#include "SipFileTransferFileMap.h"

#include <sys/types.h>
#include <sys/stat.h>
#ifndef _WIN32
#include <sys/mman.h>
#include <unistd.h>
#include <fcntl.h>
#endif

#include "cpcapi2utils.h"
#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_FILETRANSFER

using namespace CPCAPI2::SipFileTransfer;

FileMap::FileMap( const cpc::string& fileName, const cpc::string& filePath )
   : 
#ifdef _WIN32
     m_hFile( NULL ),
     m_hMapping( NULL ),
#else
     m_hFD( -1 ),
     m_FileSize( 0 ),
#endif
     m_IsValid( false )
{
   // Build the full path name
   cpc::string fullPath( filePath );
#ifdef _WIN32
   fullPath += "\\";
#else
   fullPath += "/";
#endif
   fullPath += fileName;

   // open file for reading
#ifdef _WIN32
   std::wstring ws = fullPath;
   if(( m_hFile = CreateFileW( ws.c_str(), GENERIC_READ, FILE_SHARE_READ, NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL )) == INVALID_HANDLE_VALUE )
      return;

   /* memory map the file */
   m_hMapping = CreateFileMapping( m_hFile, NULL, PAGE_READONLY, 0, 0, NULL );
   if( m_hMapping != NULL )
      m_Data = ( uint8_t * ) MapViewOfFile( m_hMapping, FILE_MAP_READ, 0, 0, 0 );

#else
   struct stat file_stat;

   // Create a memory mapped file
   if(( m_hFD = open( fullPath.c_str(), O_RDONLY )) == -1 )
      return;

   // Get the size of the file
   fstat( m_hFD, &file_stat );
   m_FileSize = file_stat.st_size;

   // Try to memory map the file
   m_Data = ( uint8_t * ) mmap( NULL, m_FileSize, PROT_READ, MAP_SHARED, m_hFD, 0 );
   if( m_Data == ( uint8_t * ) MAP_FAILED )
      m_Data = NULL;

#endif
}

FileMap::FileMap( const cpc::string& fileName, const cpc::string& filePath, const uint64_t& fileSizeBytes )
   :
#ifdef _WIN32
     m_hFile( NULL ),
     m_hMapping( NULL ),
#else
     m_hFD( -1 ),
     m_FileSize( fileSizeBytes ),
#endif
     m_IsValid( false )
{
   // Build the full path name
   cpc::string fullPath( filePath );
#ifdef _WIN32
   fullPath += "\\";
#else
   fullPath += "/";
#endif
   fullPath += fileName;

   // Create the file and open it using a memory map
#ifdef _WIN32
   std::wstring ws = fullPath;
   if(( m_hFile = CreateFileW( ws.c_str(), GENERIC_READ | GENERIC_WRITE, 0, NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL )) == INVALID_HANDLE_VALUE )
      return;

   /* Set the size of the file */
   LARGE_INTEGER file_size;
   file_size.QuadPart = fileSizeBytes;
   SetFilePointerEx( m_hFile, file_size, NULL, FILE_BEGIN );
   SetFileValidData( m_hFile, fileSizeBytes );
   SetEndOfFile( m_hFile );

   /* memory map the file */
   m_hMapping = CreateFileMapping( m_hFile, NULL, PAGE_READWRITE, 0, 0, NULL );
   if( m_hMapping != NULL )
   {
      m_Data = ( uint8_t * ) MapViewOfFile( m_hMapping, FILE_MAP_ALL_ACCESS, 0, 0, 0 );
      if( m_Data == NULL )
         return;
   }
#else

   // open the file for writing (try to create it)
   if(( m_hFD = open( fullPath.c_str(), O_RDWR | O_CREAT, S_IRUSR | S_IWUSR | S_IRGRP | S_IROTH )) == -1 )
      return;

   // truncate the file to the right size
   ftruncate( m_hFD, fileSizeBytes );

   // memory map the file
   m_Data = ( uint8_t * ) mmap( NULL, m_FileSize, PROT_READ|PROT_WRITE, MAP_SHARED, m_hFD, 0 );
   if( m_Data == ( uint8_t * ) MAP_FAILED )
      m_Data = NULL;

#endif

}

FileMap::~FileMap()
{
#ifdef _WIN32
   UnmapViewOfFile(( LPCVOID ) m_Data );

   CloseHandle( m_hMapping );
   m_hMapping = NULL;

   CloseHandle( m_hFile );
   m_hFile = NULL;
#else
   if( m_Data != NULL )
   {
      munmap( m_Data, m_FileSize );
      m_Data = NULL;
      m_FileSize = 0;
   }
   if( m_hFD != -1 )
   {
      // Not sure what to do for executable bits.. but maybe it's best not to set them anyways.
      fchmod( m_hFD, S_IRUSR | S_IWUSR | S_IRGRP | S_IROTH );
      close( m_hFD );
      m_hFD = -1;
   }
#endif
}

bool FileMap::IsValid() const
{
   return( m_Data != NULL );
}

uint8_t& FileMap::operator[]( int index )
{
   return m_Data[ index ];
}

bool FileMap::FileExists( const cpc::string& fileName, const cpc::string& filePath )
{
   bool found = false;

   // Build the full path name
   cpc::string fullPath( filePath );
#ifdef _WIN32
   fullPath += "\\";
#else
   fullPath += "/";
#endif
   fullPath += fileName;

#ifdef _WIN32
   std::wstring ws = fullPath;
   WIN32_FIND_DATAW FindFileData;
   HANDLE handle = FindFirstFileW(ws.c_str(), &FindFileData) ;
   found = handle != INVALID_HANDLE_VALUE;
   if( found ) 
       FindClose(handle);
#else
   if( access( fullPath.c_str(), F_OK ) == 0 )
      found = true;
#endif

   DebugLog(<< "FileExists: fileName=" << fileName << " filePath=" << filePath << " result=" << found);

   return found;
}

bool FileMap::IsRegularFile( const cpc::string& fileName, const cpc::string& filePath )
{
   bool result = false;

   // Build the full path name
   cpc::string fullPath( filePath );
#ifdef _WIN32
   fullPath += "\\";
#else
   fullPath += "/";
#endif
   fullPath += fileName;

#ifdef _WIN32
   std::wstring ws = fullPath;
   struct _stat64i32 s = { 0 };
   if( _wstat( ws.c_str(), &s ) == 0 )
   {
      if( s.st_mode & S_IFREG )
      {
         result = true;
      }
   }
#else
   struct stat file_stat = { 0 };
   if( stat( fullPath.c_str(), &file_stat ) == 0 )
      result = S_ISREG( file_stat.st_mode );
#endif

   DebugLog(<< "IsRegularFile: fileName=" << fileName << " filePath=" << filePath << " result=" << result);

   return result;
}

uint64_t FileMap::GetFileSize( const cpc::string& fileName, const cpc::string& filePath )
{
   uint64_t result = 0;

   // Build the full path name
   cpc::string fullPath( filePath );
#ifdef _WIN32
   fullPath += "\\";
#else
   fullPath += "/";
#endif
   fullPath += fileName;

#ifdef _WIN32
   std::wstring ws = fullPath;
   struct _stat64i32 s = { 0 };
   if( _wstat( ws.c_str(), &s ) == 0 )
      result = s.st_size;
#else
   cpc::string p_fileName = fullPath;
   struct stat file_stat = { 0 };
   if( stat( p_fileName.c_str(), &file_stat ) == 0 )
      result = file_stat.st_size;
#endif

   DebugLog(<< "GetFileSize: fileName=" << fileName << " filePath=" << filePath << " result=" << result);

   return result;
}

#endif