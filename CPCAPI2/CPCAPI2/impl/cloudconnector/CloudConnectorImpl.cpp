#include "brand_branded.h"

#if (CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE == 1)
#include "CloudConnectorImpl.h"
#include "CloudConnectorInterface.h"
#include "CloudServerConnection.h"
#include "cloudconnector/CloudConnectorHandler.h"
#include "json/JsonHelper.h"
#include "phone/PhoneInterface.h"
#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"
#include "util/DeviceInfo.h"
#include "util/IpHelpers.h"
#include "auth_server/AuthServerJwtUtils.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>
#include <sstream>

#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER

namespace CPCAPI2
{

namespace CloudConnector
{

static const char* toRestrictionString(CloudConnector::Restriction restriction);

CloudConnectorImpl::CloudConnectorImpl(CloudConnectorInterface* ccif, CloudConnectorHandle h) :
mInterface(ccif),
mHandle(h),
mWebRequestThread("CloudConnectorImpl"),
mReconnectCount(0)
{
   StackLog(<< "CloudConnectorImpl::CloudConnectorImpl(): " << this << " handle: " << mHandle);
   mCurrentInterfaceIPAddress = resip::Data::Empty;
   mRestrictions.insert(Restriction_FromUser);
   mWebRequestThread.start();
}

CloudConnectorImpl::~CloudConnectorImpl()
{
   StackLog(<< "CloudConnectorImpl::~CloudConnectorImpl(): " << this << " handle: " << mHandle);

   for (std::map<cpc::string, std::shared_ptr<CloudServerConnection>>::iterator i = mMapUriToConnection.begin(); i != mMapUriToConnection.end(); ++i)
   {
      StackLog(<< "CloudConnectorImpl::~CloudConnectorImpl(): " << this << " handle: " << mHandle << " uri: " << i->first << " connection: " << i->second);
      (i->second).reset();
   }

   mMapUriToConnection.clear();
   mMapServiceToUri.clear();
   mRestrictions.clear();
   mRestrictedNetworks.clear();
   mWebRequestThread.stop();
   mReconnectCount = 0;
   mCurrentInterfaceIPAddress = resip::Data::Empty;
}

int CloudConnectorImpl::setConnectionSettings(const CloudConnectorSettings& settings)
{
   mReconnectCount = 0;
   mSettings = settings;
   return kSuccess;
}

int CloudConnectorImpl::requestService(const ServiceDescription& service)
{
   ServiceDesc key = { service.service, service.region };
   mMapServiceToUri[key] = "";

   xten::CurlURI loopbackUrl(service.loopbackUrl.c_str());
   if (loopbackUrl.valid())
   {
      mMapServiceToLoopbackUri[key] = service.loopbackUrl;
   }
   return kSuccess;
}

int CloudConnectorImpl::connectToServices()
{
   mReconnectCount = 0;

   // const resip::Tuple& target = ip address of target
   // IpHelpers::getPreferredLocalIpAddress(target, newInterfaceAddress);
   mCurrentInterfaceIPAddress = IpHelpers::getPreferredLocalIpAddress();  // Use call with target parameter for accuracy

   if (!mRestrictions.empty())
   {
      InfoLog(<< "CloudConnectorImpl::connectToServices(): network restriction is in effect, local-ip: " << mCurrentInterfaceIPAddress);

      ServiceConnectionStatusEvent evt;
      evt.serverUri = mSettings.authServerUrl;
      for (std::map<ServiceDesc, cpc::string>::iterator i = mMapServiceToUri.begin(); i != mMapServiceToUri.end(); i++)
      {
         evt.services.push_back(ServiceDescription(i->first.region, i->first.service));
      }
      evt.connectionStatus = ServiceConnectionStatus::ServiceConnectionStatus_ConnFailure;
      evt.statusDesc = "ConnFailure: network restriction in effect: ";

      if (mRestrictions.find(Restriction_FromNetwork) != mRestrictions.end())
      {
         evt.statusDesc.append("no available network");
      }
      else if (mRestrictions.find(Restriction_FromUser) != mRestrictions.end())
      {
         evt.statusDesc.append("network disabled by user");
      }

      mInterface->fireConnStatusEvent(mHandle, evt);
      return kError;
   }

   /* TODO:
   if (isConnected())
   {
      DebugLog(<< "CloudConnectorImpl::connectToServices(): handle: " << mHandle << " already connected");
      return kSuccess;
   }
   */

   resip::Data realmStr;
   if (!mSettings.orchestrationServerUrl.empty())
   {
      realmStr = mSettings.orchestrationServerUrl.c_str();
      if (realmStr.postfix("/jsonApi"))
      {
         realmStr = realmStr.substr(0, realmStr.size() - 8);
      }
   }

   std::vector<resip::Data> requestedResources;
   std::weak_ptr<CloudConnectorImpl> weakThis(shared_from_this());
   CloudServerConnection::doAuthFlow(mWebRequestThread,
      mInterface->getThisSdkPhone()->getSdkModuleThread(),
      mSettings.authServerUrl.c_str(),
      mSettings.username.c_str(),
      mSettings.password.c_str(),
      requestedResources,
      resip::Data::Empty,
      mSettings.ignoreCertVerification,
      mInterface->getThisSdkPhone()->getSslCipherOptions(),
      mSettings.authenticationTimeoutSeconds,
      [weakThis, realmStr](int resultCode, const resip::Data& authToken, const cpc::string& details)
   {
      if (std::shared_ptr<CloudConnectorImpl> thisPtr = weakThis.lock())
      {
         DebugLog(<< "CloudConnectorImpl::connectToServices(): thread-id: " << resip::ThreadIf::selfId() << " auth server: " << thisPtr->mSettings.authServerUrl);
         assert(thisPtr->mInterface->getThisSdkPhone()->getSdkModuleThread().isCurrentThread());
         if (resultCode == 0)
         {
            std::vector<ServiceDesc> serviceRequests;
            auto itMap = thisPtr->mMapServiceToUri.begin();
            for (; itMap != thisPtr->mMapServiceToUri.end(); ++itMap)
            {
               serviceRequests.push_back(itMap->first);
            }
            CloudServerConnection::doOrchestrationFlow(thisPtr->mWebRequestThread,
               thisPtr->mInterface->getThisSdkPhone()->getSdkModuleThread(),
               serviceRequests,
               thisPtr->mSettings.orchestrationServerUrl.c_str(),
               authToken,
               thisPtr->mSettings.ignoreCertVerification,
               thisPtr->mInterface->getThisSdkPhone()->getSslCipherOptions(),
               thisPtr->mSettings.orchestrationTimeoutSeconds,
               [weakThis, realmStr, authToken](int orchResultCode, const std::vector<ServiceDescUrl>& serviceUrls, const cpc::string& details)
            {
               if (std::shared_ptr<CloudConnectorImpl> thisPtr = weakThis.lock())
               {
                  assert(thisPtr->mInterface->getThisSdkPhone()->getSdkModuleThread().isCurrentThread());
                  if (orchResultCode == 0)
                  {
                     auto it = serviceUrls.begin();
                     for (; it != serviceUrls.end(); ++it)
                     {
                        ServiceDesc sd;
                        sd.region = it->serviceDesc.region;
                        sd.service = it->serviceDesc.service;

                        // For testing only, required when the cloud service such as the xmpp agent is being tested on a particular machine,
                        // different from what is configured on the orchestrator database.
                        std::map<ServiceDesc, cpc::string>::iterator it2 = thisPtr->mMapServiceToLoopbackUri.find(sd);
                        if (it2 == thisPtr->mMapServiceToLoopbackUri.end())
                        {
                           thisPtr->mMapServiceToUri[sd] = it->url.c_str();
                           DebugLog(<< "mMapServiceToUri[" << sd.region << "," << sd.service << "] set to " << it->url.c_str());
                        }
                        else
                        {
                           thisPtr->mMapServiceToUri[sd] = it2->second;
                           DebugLog(<< "mMapServiceToUri[" << sd.region << "," << sd.service << "] set to " << it2->second);
                        }

                        if (thisPtr->mMapServiceToUri[sd].size() > 0)
                        {
                           std::map<cpc::string, std::shared_ptr<CloudServerConnection>>::iterator itConn = thisPtr->mMapUriToConnection.find(thisPtr->mMapServiceToUri[sd].c_str());
                           if (itConn == thisPtr->mMapUriToConnection.end())
                           {
                              std::shared_ptr<CloudServerConnection> serverConn = std::shared_ptr<CloudServerConnection>(new CloudServerConnection(thisPtr.get()));
                              serverConn->initialize(thisPtr->mInterface->getThisSdkPhone());
                              serverConn->addService(sd);
                              thisPtr->mMapUriToConnection[thisPtr->mMapServiceToUri[sd].c_str()] = serverConn;
                           }
                           else
                           {
                              itConn->second->addService(sd);
                           }
                        }
                     }

                     std::map<cpc::string, std::shared_ptr<CloudServerConnection>>::iterator itConn2 = thisPtr->mMapUriToConnection.begin();
                     for (; itConn2 != thisPtr->mMapUriToConnection.end(); ++itConn2)
                     {
                        std::shared_ptr<CloudServerConnection> serverConn = itConn2->second;
                        if (serverConn->connect(itConn2->first, authToken.c_str(), realmStr.c_str(), thisPtr->mSettings.ignoreCertVerification) != kSuccess)
                        {
                           ServiceConnectionStatusEvent evt;
                           evt.serverUri = thisPtr->mSettings.orchestrationServerUrl;
                           for (std::map<ServiceDesc, cpc::string>::iterator i = thisPtr->mMapServiceToUri.begin(); i != thisPtr->mMapServiceToUri.end(); i++)
                           {
                              evt.services.push_back(ServiceDescription(i->first.region, i->first.service));
                           }
                           evt.connectionStatus = ServiceConnectionStatus::ServiceConnectionStatus_ConnFailure;
                           evt.statusDesc = "error when attempting to connect to: ";
                           evt.statusDesc.append(itConn2->first);
                           thisPtr->mInterface->fireConnStatusEvent(thisPtr->mHandle, evt);
                        }
                     }
                  }
                  else
                  {
                     ServiceConnectionStatusEvent evt;
                     evt.serverUri = thisPtr->mSettings.orchestrationServerUrl;
                     for (std::map<ServiceDesc, cpc::string>::iterator i = thisPtr->mMapServiceToUri.begin(); i != thisPtr->mMapServiceToUri.end(); i++)
                     {
                        evt.services.push_back(ServiceDescription(i->first.region, i->first.service));
                     }
                     evt.connectionStatus = ServiceConnectionStatus::ServiceConnectionStatus_ConnFailure;
                     evt.statusDesc = "ConnFailure";
                     evt.statusDesc.append(": ");
                     evt.statusDesc.append(resip::Data::from(orchResultCode).c_str());
                     evt.statusDesc.append(": ");
                     evt.statusDesc.append(details.c_str());
                     thisPtr->mInterface->fireConnStatusEvent(thisPtr->mHandle, evt);
                  }
               }
            });
         }
         else
         {
            ServiceConnectionStatusEvent evt;
            evt.serverUri = thisPtr->mSettings.authServerUrl;
            for (std::map<ServiceDesc, cpc::string>::iterator i = thisPtr->mMapServiceToUri.begin(); i != thisPtr->mMapServiceToUri.end(); i++)
            {
               evt.services.push_back(ServiceDescription(i->first.region, i->first.service));
            }
            evt.connectionStatus = ServiceConnectionStatus::ServiceConnectionStatus_AuthFailure;
            evt.statusDesc = "AuthFailure";
            evt.statusDesc.append(": ");
            evt.statusDesc.append(resip::Data::from(resultCode).c_str());
            evt.statusDesc.append(": ");
            evt.statusDesc.append(details.c_str());
            thisPtr->mInterface->fireConnStatusEvent(thisPtr->mHandle, evt);
         }
      }
   });
   return kSuccess;
}

int CloudConnectorImpl::disconnectService(const ServiceDescription& service)
{
   DebugLog(<< "CloudConnectorImpl::disconnectService(): service: " << service.service << " region: " << service.region << " service list: " << mMapServiceToUri.size() << " connection list:" << mMapUriToConnection.size());
   mReconnectCount = 0;

   ServiceDesc key = { service.service, service.region };
   std::map<ServiceDesc, cpc::string>::iterator it = mMapServiceToUri.find(key);
   if (it == mMapServiceToUri.end())
   {
      ServiceConnectionStatusEvent evt;
      evt.services.push_back(ServiceDescription(service));
      evt.connectionStatus = ServiceConnectionStatus::ServiceConnectionStatus_ConnFailure;
      evt.statusDesc = "error: invalid service description ";
      evt.statusDesc.append(service.service);
      evt.statusDesc.append(":");
      evt.statusDesc.append(service.region);
      mInterface->fireConnStatusEvent(mHandle, evt);
      return kError;
   }

   mInterface->removeServiceFromPhoneMapping(it->first);

   ServiceDescription serviceDescription = ServiceDescription(it->first.region, it->first.service);
   cpc::string serviceUri = it->second;
   DebugLog(<< "CloudConnectorImpl::disconnectService(): removing " << serviceUri << " from service to uri mapping for service: " << service.service << " region: " << service.region);
   mMapServiceToUri.erase(it);
   it = mMapServiceToUri.begin();
   size_t numOtherServicesWithSameUri = 0;
   for (; it != mMapServiceToUri.end(); ++it)
   {
      if (it->second == serviceUri)
      {
         numOtherServicesWithSameUri++;
      }
   }

   // TODO: If there are still services with the same uri, the service connection will not be disconnected. This will
   // prevent the SDK client from receiving the onServiceConnectionStatusChanged for the service that did get removed.

   if (numOtherServicesWithSameUri == 0)
   {
      // no other services are using this server (identified by uri); disconnect from the server
      std::map<cpc::string, std::shared_ptr<CloudServerConnection>>::iterator itConn = mMapUriToConnection.find(serviceUri);
      if (itConn == mMapUriToConnection.end())
      {
         ServiceConnectionStatusEvent evt;
         evt.serverUri = serviceUri;
         evt.services.push_back(serviceDescription);
         evt.connectionStatus = ServiceConnectionStatus::ServiceConnectionStatus_Disconnected;
         evt.statusDesc = "no existing connection to: ";
         evt.statusDesc.append(serviceUri);
         mInterface->fireConnStatusEvent(mHandle, evt);
      }
      else
      {
         DebugLog(<< "CloudConnectorImpl::disconnectService(): destroying connection to server for " << serviceUri);
         std::shared_ptr<CloudServerConnection> serverConn = itConn->second;
         if (serverConn->disconnect() != kSuccess)
         {
            ServiceConnectionStatusEvent evt;
            evt.serverUri = itConn->first;
            evt.services.push_back(serviceDescription);
            evt.connectionStatus = ServiceConnectionStatus::ServiceConnectionStatus_ConnFailure;
            evt.statusDesc = "error when attempting to disconnect from: ";
            evt.statusDesc.append(serviceUri);
            mInterface->fireConnStatusEvent(mHandle, evt);
         }

         // TODO: Should we erase the connection here from the uri to connection map
      }
   }

   return kSuccess;
}

int CloudConnectorImpl::addUser(const AddUserRequest& req)
{
   std::weak_ptr<CloudConnectorImpl> weakThis(shared_from_this());
   CloudServerConnection::doAddUserFlow(mWebRequestThread, mInterface->getThisSdkPhone()->getSdkModuleThread(),
      req.username.c_str(), req.password.c_str(), req.authServerUrl.c_str(), req.authServerApiKey.c_str(), mSettings.ignoreCertVerification, mInterface->getThisSdkPhone()->getSslCipherOptions(), mSettings.authenticationTimeoutSeconds, [weakThis](int result, const cpc::string& details)
   {
      if (std::shared_ptr<CloudConnectorImpl> thisPtr = weakThis.lock())
      {
         AddUserResponse respEvt;
         respEvt.success = (result == 0);
         if (!respEvt.success)
         {
            respEvt.errorDetails = resip::Data::from(result).c_str();
            respEvt.errorDetails.append(": ");
            respEvt.errorDetails.append(details.c_str());
         }
         thisPtr->mInterface->fireAddUserResponse(thisPtr->mHandle, respEvt);
      }
   });
   return kSuccess;
}

void CloudConnectorImpl::handleCloudServerConnectionStatusChanged(CloudServerConnection* conn, const CloudServerConnStatusEvent& args)
{
   if (args.status == CloudServerConnStatus_LoggedOut)
   {
      DebugLog(<< "CloudConnectorImpl::handleCloudServerConnectionStatusChanged(): successfully logged out from connection to " << conn->getUrl());
      LogoutResult evt;
      evt.success = true;
      mInterface->fireLogoutResult(mHandle, evt);

      releaseConnections(false);
      return;
   }

   if (args.status == CloudServerConnStatus_LogoutFailure)
   {
      DebugLog(<< "CloudConnectorImpl::handleCloudServerConnectionStatusChanged(): remote error when attempting to logout from connection to " << conn->getUrl());
      LogoutResult evt;
      evt.success = false;
      evt.errorDetails = "Remote error when attempting to logout from connection to " + conn->getUrl();
      mInterface->fireLogoutResult(mHandle, evt);

      releaseConnections(false);
      return;
   }

   // translate into a per-service set of events
   const std::set<ServiceDesc>& services = conn->getServices();
   std::set<ServiceDesc>::const_iterator it = services.begin();
   ServiceConnectionStatusEvent evt;
   for (; it != services.end(); ++it)
   {
      ServiceDescription desc;
      desc.region = it->region;
      desc.service = it->service;
      evt.services.push_back(desc);
      if (args.status == CloudServerConnStatus_Connected)
      {
         evt.authToken.authToken = conn->getAuthToken();
         mInterface->addServiceToPhoneMapping(*it, conn->getPhone());
      }
   }
   evt.serverUri = conn->getUrl();
   evt.connectionStatus = (ServiceConnectionStatus)args.status;

   bool servicesExist(false);
   for (std::map<ServiceDesc, cpc::string>::const_iterator it2 = mMapServiceToUri.begin(); it2 != mMapServiceToUri.end(); ++it2)
   {
      if (conn->getUrl() == it2->second)
      {
         servicesExist = true;
         break;
      }
   }

   bool networkRestricted = ((mRestrictions.find(Restriction_FromNetwork) != mRestrictions.end()) && (mRestrictions.find(Restriction_FromUser) == mRestrictions.end()));
   if ((args.status == CloudServerConnStatus_Disconnected) && networkRestricted && servicesExist)
   {
      // Ensure services exist for this disconnected connection before flagging an network error condition,
      // as the disconnected status could also be as a result of a disconnect-service request
      evt.connectionStatus = ServiceConnectionStatus_ConnFailure;
      StackLog(<< "CloudConnectorImpl::handleCloudServerConnectionStatusChanged(): updating connection status from disconnected to connection failure");
   }

   mInterface->fireConnStatusEvent(mHandle, evt);
}

void CloudConnectorImpl::destroy()
{
   DebugLog(<< "CloudConnectorImpl::destroy(): handle: " << mHandle);

   // Note
   // - mMapServiceToUri uri may be empty if an invalid service description was provided, i.e. orch flow returned an empty list
   // - mMapServiceToUri uri may be empty if the orch flow was never executed, i.e. connectToServices was never called
   // - mMapServiceToUri will be empty if the app has already cleaned up the services, i.e. disconnect service
   // - mMapUriToConnection may still have a connection entry for a uri, even though no services exist in mMapServiceToUri, i.e. all services were disconnected
   // - mMapUriToConnection for a particular service may not exist, as not all services may have had a successful connection
   // - mMapServiceToUri and mMapUriToConnection may both be empty, e.g. when cloud is enabled in airplane mode, and app has disconnected services

   bool destroySent(false);
   std::set<cpc::string> servers;
   for (std::map<ServiceDesc, cpc::string>::iterator i = mMapServiceToUri.begin(); i != mMapServiceToUri.end(); ++i)
   {
      if (i->second.empty())
      {
         destroySent = true;
         ServiceConnectionStatusEvent evt;
         evt.services.push_back(ServiceDescription(i->first.region, i->first.service));
         evt.connectionStatus = ServiceConnectionStatus::ServiceConnectionStatus_Destroyed;
         mInterface->fireConnStatusEvent(mHandle, evt);
         continue;
      }

      if (servers.find(i->second) != servers.end())
      {
         DebugLog(<< "CloudConnectorImpl::destroy(): connection has already been destroyed for serviceUrl: " << i->second);
         continue;
      }

      std::map<cpc::string, std::shared_ptr<CloudServerConnection>>::iterator j = mMapUriToConnection.find(i->second);
      if (j == mMapUriToConnection.end())
      {
         destroySent = true;
         ServiceConnectionStatusEvent evt;
         evt.serverUri = i->second;
         evt.services.push_back(ServiceDescription(i->first.region, i->first.service));
         evt.connectionStatus = ServiceConnectionStatus::ServiceConnectionStatus_Destroyed;
         mInterface->fireConnStatusEvent(mHandle, evt);
         continue;
      }

      std::shared_ptr<CloudServerConnection> conn = j->second;
      mMapUriToConnection.erase(j);

      if (conn)
      {
         destroySent = true;
         ServiceConnectionStatusEvent evt;
         evt.serverUri = (i->second);

         const std::set<ServiceDesc>& services = conn->getServices();
         for (std::set<ServiceDesc>::const_iterator k = services.begin(); k != services.end(); ++k)
         {
            evt.services.push_back(ServiceDescription(k->region, k->service));
         }

         conn->release();
         conn.reset();
         servers.insert(i->second);
         DebugLog(<< "CloudConnectorImpl::destroy(): " << this << " destroyed connection for serviceUrl: " << i->second);

         evt.connectionStatus = ServiceConnectionStatus::ServiceConnectionStatus_Destroyed;
         mInterface->fireConnStatusEvent(mHandle, evt);
      }
   }

   mMapServiceToUri.clear();

   for (std::map<cpc::string, std::shared_ptr<CloudServerConnection>>::iterator i = mMapUriToConnection.begin(); i != mMapUriToConnection.end(); ++i)
   {
      destroySent = true;
      ServiceConnectionStatusEvent evt;
      evt.serverUri = i->first;
      getServicesForConnection(i->first, evt.services);
      evt.connectionStatus = ServiceConnectionStatus::ServiceConnectionStatus_Destroyed;
      mInterface->fireConnStatusEvent(mHandle, evt);
   }

   mMapUriToConnection.clear();

   if (!destroySent)
   {
      ServiceConnectionStatusEvent evt;
      evt.connectionStatus = ServiceConnectionStatus::ServiceConnectionStatus_Destroyed;
      mInterface->fireConnStatusEvent(mHandle, evt);
   }
}

void CloudConnectorImpl::logout()
{
   DebugLog(<< "CloudConnectorImpl::logout(): handle: " << mHandle << " servers connections: " << mMapUriToConnection.size());

   if (mMapUriToConnection.size() == 0)
   {
      // Looks like never really logged in, so just return success
      LogoutResult evt;
      evt.success = true;
      mInterface->fireLogoutResult(mHandle, evt);
   }

   for (std::map<cpc::string, std::shared_ptr<CloudServerConnection>>::iterator i = mMapUriToConnection.begin(); i != mMapUriToConnection.end(); ++i)
   {
      DebugLog(<< "CloudConnectorImpl::logout(): destroying connection to server for " << i->first);
      std::shared_ptr<CloudServerConnection> connection = i->second;
      if (connection->logout() == kSuccess)
      {
         DebugLog(<< "CloudConnectorImpl::logout(): successfully initiated logout from connection to " << i->first);
      }
      else
      {
         DebugLog(<< "CloudConnectorImpl::logout(): error when attempting to logout from connection to " << i->first);
         LogoutResult evt;
         evt.success = false;
         evt.errorDetails = "Error when attempting to logout from connection to " + i->first;
         mInterface->fireLogoutResult(mHandle, evt);
      }

      // TODO: Need to handle multiple connections, logout from some connections may be successful or not
      break;
   }
}

void CloudConnectorImpl::releaseConnections(bool sendStatus, ServiceConnectionStatus reason)
{
   cpc::vector<cpc::string> connections;
   for (std::map<cpc::string, std::shared_ptr<CloudServerConnection>>::iterator i = mMapUriToConnection.begin(); i != mMapUriToConnection.end(); ++i)
   {
      connections.push_back(i->first);
   }

   for (cpc::vector<cpc::string>::iterator j = connections.begin(); j != connections.end(); ++j)
   {
      ServiceConnectionStatusEvent evt;
      evt.serverUri = *j;
      for (std::map<ServiceDesc, cpc::string>::iterator k = mMapServiceToUri.begin(); k != mMapServiceToUri.end(); ++k)
      {
         if (evt.serverUri == (k->second))
         {
            ServiceDescription service(k->first.region, k->first.service);
            evt.services.push_back(service);
         }
      }

      // Retain the server to uri list, so that it can be re-established when the network recovers
      std::shared_ptr<CloudServerConnection> conn = mMapUriToConnection[*j];
      if (conn->disconnect() != kSuccess)
      {
         evt.connectionStatus = ServiceConnectionStatus::ServiceConnectionStatus_ConnFailure;
         evt.statusDesc = "error when attempting to disconnect from: ";
         evt.statusDesc.append(evt.serverUri);
         mInterface->fireConnStatusEvent(mHandle, evt);
         continue;
      }

      if (!sendStatus) continue;

      evt.connectionStatus = reason;
      evt.statusDesc = "disconnecting due to network changes from: ";
      evt.statusDesc.append(evt.serverUri);
      mInterface->fireConnStatusEvent(mHandle, evt);
   }
}

const bool CloudConnectorImpl::isRestrictedNetwork(const NetworkTransport currentNetworkTransport) const
{
   return mRestrictedNetworks.find(currentNetworkTransport) != mRestrictedNetworks.end();
}

void CloudConnectorImpl::addRestriction(Restriction restriction)
{
   DebugLog(<< "CloudConnectorImpl::addRestriction(): handle: " << mHandle << " restriction: " << toRestrictionString(restriction));

   bool restricted = !mRestrictions.empty();

   mRestrictions.insert(restriction);

   // Fire disconnecting event if network is not restricted or the new restriction is issued by user
   if (restricted && restriction != Restriction_FromUser)
   {
      DebugLog(<< "CloudConnectorImpl::addRestriction(): handle: " << mHandle << " not initiating disconnect");
      return;
   }

   releaseConnections();
}

void CloudConnectorImpl::removeRestriction(Restriction restriction)
{
   DebugLog(<< "CloudConnectorImpl::removeRestriction(): mHandle: " << mHandle << " restriction: " << toRestrictionString(restriction));

   mRestrictions.erase(restriction);

   // reset due to user intervention
   mReconnectCount = 0;

   if (restriction == Restriction_FromNetwork)
   {
      // mCurrentInterfaceIPAddress can only be assigned once the connection is valid.
      // an empty mCurrentInterfaceIPAddress means cloud might be still connecting using old network interface.
      // it wouldn't hurt too much to force reconnection even with the preferred network interface been chosen.
      if (mCurrentInterfaceIPAddress.empty())
      {
         InfoLog(<< "CloudConnectorImpl::removeRestriction(): not yet connected hence reconnection is enforced");

         releaseConnections(false);
         connectToServices();
         return;
      }

      // TODO: assert(isConnected());

      // const resip::Tuple& target = ip address of target
      // IpHelpers::getPreferredLocalIpAddress(target, newInterfaceAddress);
      resip::Data newInterfaceAddress = IpHelpers::getPreferredLocalIpAddress(); // Use call with target parameter for accuracy

      DebugLog(<< "CloudConnectorImpl::removeRestriction(): preferred interface: " << newInterfaceAddress << ", old interface: " << mCurrentInterfaceIPAddress);

      if (newInterfaceAddress != mCurrentInterfaceIPAddress)
      {
         InfoLog(<< "CloudConnectorImpl::removeRestriction(): network interface switch to " << newInterfaceAddress << " from " << mCurrentInterfaceIPAddress);

         releaseConnections(false);
         connectToServices();
         return;
      }

      // leave with the only case that the account is connected with the same valid mCurrentInterfaceIPAddress
      InfoLog(<< "CloudConnectorImpl::removeRestriction(): network interface switch is not required");
   }
   else if (restriction == Restriction_FromUser)
   {
      InfoLog(<< "CloudConnectorImpl::removeRestriction(): reset session data due to user intervention");
   }

   connectToServices();
}

void CloudConnectorImpl::getServicesForUri(cpc::string uri, cpc::vector<ServiceDescription>& services)
{
   services.clear();
   if (uri.empty())
   {
      StackLog(<< "CloudConnectorImpl::getServicesForUri(): empty uri parameter");
      return;
   }

   for (std::map<ServiceDesc, cpc::string>::iterator i = mMapServiceToUri.begin(); i != mMapServiceToUri.end(); ++i)
   {
      if (i->second == uri)
      {
         services.push_back(ServiceDescription(i->first.region, i->first.service));
      }
   }
}

bool CloudConnectorImpl::getUriForService(ServiceDesc service, cpc::string& uri)
{
   std::map<ServiceDesc, cpc::string>::iterator i = mMapServiceToUri.find(service);
   if (i == mMapServiceToUri.end())
   {
      StackLog(<< "CloudConnectorImpl::getUriForService(): no uri found for service: " << service.service << " region: " << service.region);
      return false;
   }

   if (i->second.empty())
   {
      StackLog(<< "CloudConnectorImpl::getUriForService(): uri not initialized for service: " << service.service << " region: " << service.region);
      return false;
   }

   uri = i->second;
   return true;
}

bool CloudConnectorImpl::getUriForService(ServiceDescription service, cpc::string& uri)
{
   return (getUriForService(ServiceDesc(service.service, service.region), uri));
}

void CloudConnectorImpl::getServicesForConnection(cpc::string uri, std::set<ServiceDesc>& services)
{
   std::map<cpc::string, std::shared_ptr<CloudServerConnection>>::iterator i = mMapUriToConnection.find(uri);
   if (i == mMapUriToConnection.end())
   {
      StackLog(<< "CloudConnectorImpl::getServicesForConnection(): no connection found for uri: " << uri);
   }

   services = i->second->getServices();
}

void CloudConnectorImpl::getServicesForConnection(cpc::string uri, cpc::vector<ServiceDescription>& services)
{
   std::set<ServiceDesc> serviceDescs;
   getServicesForConnection(uri, serviceDescs);

   for (std::set<ServiceDesc>::const_iterator i = serviceDescs.begin(); i != serviceDescs.end(); ++i)
   {
      services.push_back(ServiceDescription(i->region, i->service));
   }
}

static const char* toRestrictionString(CloudConnector::Restriction restriction)
{
   switch (restriction)
   {
      case Restriction_FromUser:
         return "Restriction_FromUser";
      case Restriction_FromNetwork:
         return "Restriction_FromNetwork";
      default:
         return "Restriction_Unknown";
   }
}

}

}

#endif // CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE
