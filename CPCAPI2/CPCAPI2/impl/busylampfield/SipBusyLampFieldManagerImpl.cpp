#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_BUSY_LAMP_FIELD_MODULE == 1)

#include "SipBusyLampFieldManagerImpl.h"
#include "SipBusyLampFieldStateImpl.h"
#include "SipBusyLampFieldHelper.h"
#include "cpm/CpmHelper.h"
#include "../util/cpc_logger.h"
#include <rutil/Random.hxx>

using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipDialogEvent;
using namespace CPCAPI2::SipConversation;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_BLF

const int NewSubscriptionHandle = -1;   // this line has not yet been subscribed
const int OldSubscriptionHandle = -2;   // this line was previously subscribed but is not currently subscribed

namespace CPCAPI2
{
namespace SipBusyLampField
{

SipBusyLampFieldManagerImpl::SipBusyLampFieldManagerImpl(CPCAPI2::PhoneInterface* phone, SipBusyLampFieldManagerInterface* iff, CPCAPI2::SipAccount::SipAccountHandle account, SipBusyLampFieldStateManager* stateManager, SipDialogEventSubscriptionManagerInterface* sipDialogEventIf, SipAVConversationManagerInterface *sipConversationIf)
   : mInterface(iff),
     mPhone(phone),
     mAccount(account),
     mStateManager(stateManager),
     mSipDialogEventSubscriptionIf(sipDialogEventIf),
     mSipConversationIf(sipConversationIf)
{
   mSipDialogEventSubscriptionIf->addSdkObserver(this, mAccount);
}

SipBusyLampFieldManagerImpl::~SipBusyLampFieldManagerImpl()
{
   mSipDialogEventSubscriptionIf->removeSdkObserver(this, mAccount);
   // Destroy the remote line set info objects
   for (SipBusyLampFieldRemoteLineSetMap::const_iterator it = mRemoteLineSetInfoMap.begin(); it != mRemoteLineSetInfoMap.end(); it++)
   {
      SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo = it->second;
      delete remoteLineSetInfo;
   }

   for (ResubscribeMap::const_iterator itr = mResubscribeMap.begin(); itr != mResubscribeMap.end(); itr++)
   {
      BusyLampFieldResubscribe* resubscribe = itr->second;
      resubscribe->m_Timer->cancel();
      delete resubscribe;
   }
}

void SipBusyLampFieldManagerImpl::startSubscriptions(SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo)
{
   assert(remoteLineSetInfo);

   // Create the dialog-event related settings
   SipDialogEventSubscriptionSettings settings = dialogEventSubSettings(remoteLineSetInfo);

   // we're on the SDK thread already here, call the impl versions
   if (!remoteLineSetInfo->mSettings.resourceListAddress.empty())
   {
      SipDialogEventSubscriptionHandle subscription = mSipDialogEventSubscriptionIf->createSubscriptionImpl(mAccount);
      mSipDialogEventSubscriptionIf->applySubscriptionSettingsImpl(subscription, settings);
      mSipDialogEventSubscriptionIf->addParticipantImpl(subscription, remoteLineSetInfo->mSettings.resourceListAddress, true);
      mSipDialogEventSubscriptionIf->startImpl(subscription);

      // Associate the subscription with the remote line
      remoteLineSetInfo->mResourceListSubscription = subscription;
   }
   else
   {
      // Subscribe each remote line individually
      for (std::map<SipBusyLampFieldRemoteLineHandle, SipBusyLampFieldRemoteLineInfo>::iterator it = remoteLineSetInfo->mRemoteLineSubscriptions.begin(); it != remoteLineSetInfo->mRemoteLineSubscriptions.end(); it++)
      {
         SipBusyLampFieldRemoteLineHandle remoteLine = it->first;
      
         SipDialogEventSubscriptionHandle subscription = mSipDialogEventSubscriptionIf->createSubscriptionImpl(mAccount);
         mSipDialogEventSubscriptionIf->applySubscriptionSettingsImpl(subscription, settings);
         mSipDialogEventSubscriptionIf->addParticipantImpl(subscription, remoteLine);
         mSipDialogEventSubscriptionIf->startImpl(subscription);

         // Associate the subscription with the remote line
         it->second.mSubscriptionHandle = subscription;
      }
   }
}

void SipBusyLampFieldManagerImpl::endSubscriptions(SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo)
{
   assert(remoteLineSetInfo);

   if (remoteLineSetInfo->mResourceListSubscription != 0)
   {
      DebugLog(<< "SipBusyLampFieldManagerImpl::endSubscriptions() for subscription " << remoteLineSetInfo->mResourceListSubscription);

      mSipDialogEventSubscriptionIf->endImpl(remoteLineSetInfo->mResourceListSubscription);
      setSubscriptionState(remoteLineSetInfo->mResourceListSubscription, SipBusyLampFieldSubscriptionState::Unsubscribed);
   }

   // Unsubscribe each remote line individually
   for (std::map<SipBusyLampFieldRemoteLineHandle, SipBusyLampFieldRemoteLineInfo>::const_iterator it = remoteLineSetInfo->mRemoteLineSubscriptions.begin(); it != remoteLineSetInfo->mRemoteLineSubscriptions.end(); it++)
   {
      SipDialogEventSubscriptionHandle subscription = it->second.mSubscriptionHandle;
      if (subscription != 0)
      {
         DebugLog(<< "SipBusyLampFieldManagerImpl::endSubscriptions() for subscription " << subscription);

         mSipDialogEventSubscriptionIf->endImpl(subscription);
         setSubscriptionState(subscription, SipBusyLampFieldSubscriptionState::Unsubscribed);
      }
   }
}

SipConversationHandle SipBusyLampFieldManagerImpl::answerCall(SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo, const SipBusyLampFieldRemoteLineHandle& remoteLine, const cpc::string& dialogId, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor)
{
   assert(remoteLineSetInfo);

   // Get call information on the selected remote line. Make sure the call was found
   DialogInfo dialogInfo = getDialogForRemoteLine(remoteLineSetInfo->mHandle, remoteLine, dialogId);
   if (!SipBusyLampFieldHelper::isCall(dialogInfo))
   {
      fireError(remoteLineSetInfo->mHandle, "Cannot perform BLF answer call operation on remote line - No call available: " + remoteLine);
      return 0;
   }

   // Make sure it's an incoming call, from the remote line perspective
   if (dialogInfo.direction != DialogDirection_Recipient)
   {
      fireError(remoteLineSetInfo->mHandle, "Cannot perform BLF answer call operation on remote line - Call not incoming on remote line: " + remoteLine);
      return 0;
   }

   if (dialogInfo.localParticipant.identities.size() == 0)
   {
      fireError(remoteLineSetInfo->mHandle, "Cannot perform BLF answer call operation on remote line - Missing local identity: " + remoteLine);
      return 0;
   }

   // Create a new conversation with the remote line
   SipConversationHandle conversation = mSipConversationIf->createConversation(mAccount);
   mSipConversationIf->addParticipant(conversation, dialogInfo.localParticipant.identities[0].address);
   mSipConversationIf->configureMedia(conversation, mediaDescriptor);

   // Set Replaces header with Dialog-ID of call
   mSipConversationIf->setCallToReplace(conversation, dialogInfo.dialogId.callId, dialogInfo.dialogId.localTag, dialogInfo.dialogId.remoteTag);

   // Start the conversation
   mSipConversationIf->start(conversation);

   return conversation;
}

SipConversationHandle SipBusyLampFieldManagerImpl::joinCall(SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo, const SipBusyLampFieldRemoteLineHandle& remoteLine, const cpc::string& dialogId, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor, BargeInMode bargeInMode, AlertMode alertMode)
{
   assert(remoteLineSetInfo);

   // Make sure the barge-in mode specified is allowed
   if (!isAllowedBargeInMode(remoteLineSetInfo, bargeInMode))
   {
      fireError(remoteLineSetInfo->mHandle, "Not allowed to use barge-in mode: " + cpc::to_string(bargeInMode));
      return 0;
   }

   // Make sure setting silent alert mode is allowed
   if (alertMode == AlertMode_Silent && !remoteLineSetInfo->mSettings.allowedSilentAlertMode)
   {
      fireError(remoteLineSetInfo->mHandle, "Not allowed to set silent alert mode");
      return 0;
   }

   // Get call information on the selected remote line. Make sure the call was found
   DialogInfo dialogInfo = getDialogForRemoteLine(remoteLineSetInfo->mHandle, remoteLine, dialogId);
   if (!SipBusyLampFieldHelper::isCall(dialogInfo))
   {
      fireError(remoteLineSetInfo->mHandle, "Cannot perform BLF join call operation on remote line - No call available: " + remoteLine);
      return 0;
   }

   // Make sure the call is in the 'confirmed' state
   if (dialogInfo.stateInfo.state != DialogState_Confirmed)
   {
      fireError(remoteLineSetInfo->mHandle, "Cannot perform BLF join call operation on remote line - Call not in confirmed state: " + remoteLine);
      return 0;
   }

   if (dialogInfo.localParticipant.identities.size() == 0)
   {
      fireError(remoteLineSetInfo->mHandle, "Cannot perform BLF join call operation on remote line - Missing local identity: " + remoteLine);
      return 0;
   }

   // Create a new conversation with the monitored line
   SipConversationHandle conversation = mSipConversationIf->createConversation(mAccount);
   mSipConversationIf->addParticipant(conversation, dialogInfo.localParticipant.identities[0].address);
   mSipConversationIf->configureMedia(conversation, mediaDescriptor);

   // Set Join header with Dialog-ID of call
   // Append barge-in mode and silent mode to join header
   cpc::vector<Parameter> joinHeaderParams;
   cpc::string bargeInModeStr = SipBusyLampFieldHelper::getBargeInModeString(bargeInMode);
   joinHeaderParams.push_back(Parameter(SipBusyLampFieldHelper::BARGE_MODE_PARAM, bargeInModeStr));
   if (alertMode == AlertMode_Silent)
   {
      joinHeaderParams.push_back(Parameter(SipBusyLampFieldHelper::ALERT_MODE_PARAM, SipBusyLampFieldHelper::SILENT_ALERT_MODE));
   }
   mSipConversationIf->setCallToJoin(conversation, dialogInfo.dialogId.callId, dialogInfo.dialogId.localTag, dialogInfo.dialogId.remoteTag, joinHeaderParams);

   // Start the conversation
   mSipConversationIf->start(conversation);

   return conversation;
}

SipBusyLampFieldRemoteLineSetInfo* SipBusyLampFieldManagerImpl::createRemoteLineSetInfo(SipBusyLampFieldRemoteLineSetHandle remoteLineSetHandle)
{
   assert(mRemoteLineSetInfoMap[remoteLineSetHandle] == NULL);

   SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo = new SipBusyLampFieldRemoteLineSetInfo(remoteLineSetHandle);
   remoteLineSetInfo->mAccountHandle = mAccount;
   mRemoteLineSetInfoMap[remoteLineSetHandle] = remoteLineSetInfo;

   return remoteLineSetInfo;
}

SipBusyLampFieldRemoteLineSetInfo* SipBusyLampFieldManagerImpl::getRemoteLineSetInfo(SipBusyLampFieldRemoteLineSetHandle remoteLineSet) const
{
   SipBusyLampFieldRemoteLineSetMap::const_iterator it = mRemoteLineSetInfoMap.find(remoteLineSet);
   return (it != mRemoteLineSetInfoMap.end()) ? it->second : NULL;
}

void SipBusyLampFieldManagerImpl::fireRemoteLineNewSubscription(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const RemoteLineNewSubscriptionEvent& event)
{
   DebugLog(<< "EVT: fireRemoteLineNewSubscription " << remoteLineSet << " line " << event.remoteLine << " name " << event.remoteLineName);
   mInterface->fireEvent(cpcEvent(SipBusyLampFieldHandler, onRemoteLineNewSubscription), mAccount, remoteLineSet, event);
}

void SipBusyLampFieldManagerImpl::fireRemoteLineSubscriptionStateChanged(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const RemoteLineSubscriptionStateChangedEvent& event)
{
   DebugLog(<< "EVT: fireRemoteLineSubscriptionStateChanged " << remoteLineSet <<  " line " << event.remoteLine << " subState " << event.subscriptionState);
   mInterface->fireEvent(cpcEvent(SipBusyLampFieldHandler, onRemoteLineSubscriptionStateChanged), mAccount, remoteLineSet, event);
}

void SipBusyLampFieldManagerImpl::fireRemoteLineSubscriptionEnded(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const RemoteLineSubscriptionEndedEvent& event)
{
   DebugLog(<< "EVT: fireRemoteLineSubscriptionEnded " << remoteLineSet << " line " << event.remoteLine << " endReason " << event.endReason);
   mInterface->fireEvent(cpcEvent(SipBusyLampFieldHandler, onRemoteLineSubscriptionEnded), mAccount, remoteLineSet, event);
}

void SipBusyLampFieldManagerImpl::fireRemoteLineStateChanged(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const RemoteLineStateChangedEvent& event)
{
   DebugLog(<< "EVT: fireRemoteLineStateChanged " << remoteLineSet <<  " line " << event.remoteLine);
   mInterface->fireEvent(cpcEvent(SipBusyLampFieldHandler, onRemoteLineStateChanged), mAccount, remoteLineSet, event);
}

void SipBusyLampFieldManagerImpl::fireRemoteLineEmptySubscriptions(const RemoteLineEmptySubscriptionsEvent& event)
{
   DebugLog(<< "EVT: fireRemoteLineEmptySubscriptions");
   mInterface->fireEvent(cpcEvent(SipBusyLampFieldHandler, onRemoteLineEmptySubscriptions), mAccount, event);
}

void SipBusyLampFieldManagerImpl::fireError(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const ErrorEvent& event)
{
   DebugLog(<< "EVT: fireError " << remoteLineSet);
   mInterface->fireEvent(cpcEvent(SipBusyLampFieldHandler, onError), mAccount, remoteLineSet, event);
}

void SipBusyLampFieldManagerImpl::fireError(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const cpc::string& msg)
{
   DebugLog(<< "EVT: fireError " << remoteLineSet << ": " << msg);

   // Create the event
   ErrorEvent event;
   event.account = mAccount;
   event.errorText = msg;

   // Publish the event
   fireError(remoteLineSet, event);
}

int SipBusyLampFieldManagerImpl::onNewSubscription(SipDialogEventSubscriptionHandle subscription, const NewDialogEventSubscriptionEvent& args)
{
   DebugLog(<< "SipBusyLampFieldManagerImpl::onNewSubscription() for subscription " << subscription);

   // Get the remote line that contains the subscription
   bool rlsMode = false;
   RemoteLinePairVector remoteLinePairs = getRemoteLinesForSubscription(subscription, rlsMode);

// DRL I removed this check so that when the line set subscription is terminated and then re-subscribed we send the
// new subscription events, because we did send subscription ended events for each line and we should be pairing those.
// Result is that if RLS mode is enabled, initial onNewSubscription will be sent from onDialogResourceListUpdated, but
// on re-subscribe for RLS mode onNewSubscription will be sent from here.
//   if (rlsMode) return kSuccess;

// Note that in the case of a delayed resubscribe the handling is a little different in that we don't send subscription
// End and then New, we just send a subscription state change of WaitingToSubscribe and then Active, so we check below 
// for lines that are resubscribing and skip sending them the New event.

   for (RemoteLinePairVector::const_iterator pairIt = remoteLinePairs.begin(); pairIt != remoteLinePairs.end(); ++pairIt)
   {
      RemoteLinePair remoteLinePair = *pairIt;
      if (!isRemoteLinePairValid(remoteLinePair))
         continue;

      // we don't want to send new subscription event for lines that are resubscribing as we didn't sent subscription ended
      if (getLineSubscriptionState(remoteLinePair.remoteLine) == SipBusyLampFieldSubscriptionState::ResubscribePending)
         continue;

      // Create the event
      RemoteLineNewSubscriptionEvent blfArgs;
      blfArgs.account = mAccount;
      blfArgs.remoteLine = remoteLinePair.remoteLine;
	//      blfArgs.remoteLineName = remoteLineName;   TODO: We don't have it here!

      // Publish the event
      fireRemoteLineNewSubscription(remoteLinePair.remoteLineSet, blfArgs);
   }

   setSubscriptionState(subscription, SipBusyLampFieldSubscriptionState::DefaultSubscribing);

   return kSuccess;
}

int SipBusyLampFieldManagerImpl::onSubscriptionEnded(SipDialogEventSubscriptionHandle subscription, const DialogEventSubscriptionEndedEvent& args)
{
   DebugLog(<< "SipBusyLampFieldManagerImpl::onSubscriptionEnded() for subscription " << subscription);

   bool willResubscribe = false;

      // Only outgoing subscriptions are handled here.
   if (args.subscriptionType == SipSubscriptionType_Outgoing && 
      // This scenario does not require handling.
      args.isNotifyTerminated && 
      // These are cases where the specification says we should NOT resubscribe.
      args.reason != cpc::string("rejected") && args.reason != cpc::string("noresource") && 
      // Don't resubscribe if the app has ended the subscription
      getSubscriptionState(subscription) != SipBusyLampFieldSubscriptionState::Unsubscribed)
   {
      unsigned int resub_delay_ms = 0;

      // These are cases the specification says we should resubscribe immediately regardless of the retry-after.
      if (args.reason == "deactivated" || args.reason == "timeout")
      {
         // retry immediately
      } 
      else if (args.retryAfter > 0)
      {
         // use retry-after in Subscription-State header
         resub_delay_ms = args.retryAfter * 1000;
      }
      else
      {
         // retry within 5 seconds
         resub_delay_ms = resip::Random::getRandom() % 5000;
      }

      BusyLampFieldResubscribe* resubscribe = new BusyLampFieldResubscribe(mPhone->getSdkModuleThread(), args.remoteAddress, subscription);
      resubscribe->m_Timer->expires_from_now(resub_delay_ms);
      resubscribe->account = mAccount;
      resubscribe->statusCode = args.statusCode;
      resubscribe->m_Timer->async_wait(this, mAccount, resubscribe);
      mResubscribeMap[subscription] = resubscribe;
      DebugLog(<< "Started " << (int)(resub_delay_ms / 1000) << " second timer for account " << mAccount << " and subscription " << subscription);
   
      setSubscriptionState(subscription, SipBusyLampFieldSubscriptionState::ResubscribePending);

      DebugLog(<< "Will resubscribe " << subscription);
      willResubscribe = true;
   }

   // Get the remote line that contains the subscription
   bool rlsMode = false;
   RemoteLinePairVector remoteLinePairs = getRemoteLinesForSubscription(subscription, rlsMode);
   for (RemoteLinePairVector::const_iterator pairIt = remoteLinePairs.begin(); pairIt != remoteLinePairs.end(); ++pairIt)
   {
      RemoteLinePair remoteLinePair = *pairIt;
      if (!isRemoteLinePairValid(remoteLinePair))
         continue;

      if (willResubscribe)
      {
         // Create the event
         RemoteLineSubscriptionStateChangedEvent blfArgs;
         blfArgs.account = mAccount;
         blfArgs.remoteLine = remoteLinePair.remoteLine;
         blfArgs.subscriptionState = SipSubscriptionState_WaitingToSubscribe;

         // Publish the event
         fireRemoteLineSubscriptionStateChanged(remoteLinePair.remoteLineSet, blfArgs);
      }
      else
      {
         // Create the event
         RemoteLineSubscriptionEndedEvent blfArgs;
         blfArgs.account = mAccount;
         blfArgs.remoteLine = remoteLinePair.remoteLine;
         blfArgs.endReason = args.endReason;

         // Publish the event
         fireRemoteLineSubscriptionEnded(remoteLinePair.remoteLineSet, blfArgs);
      }
   }

   return kSuccess;
}

int SipBusyLampFieldManagerImpl::onIncomingDialogInfo(SipDialogEventSubscriptionHandle subscription, const IncomingDialogInfoEvent& args)
{
   // Get the remote line that contains the subscription
   bool rlsMode = false;
   RemoteLinePairVector remoteLinePairs = getRemoteLinesForSubscription(subscription, rlsMode);
   if (rlsMode) return kSuccess;

   for (RemoteLinePairVector::const_iterator pairIt = remoteLinePairs.begin(); pairIt != remoteLinePairs.end(); ++pairIt)
   {
      RemoteLinePair remoteLinePair = *pairIt;
      if (!isRemoteLinePairValid(remoteLinePair))
         continue;

      // Create the event
      RemoteLineStateChangedEvent blfArgs;
      blfArgs.account = mAccount;
      blfArgs.remoteLine = remoteLinePair.remoteLine;
      blfArgs.calls = createCalls(remoteLinePair.remoteLine, args.dialogInfoDoc);
      blfArgs.orderNumber = 0;   // we don't have the full list here so we can't provide an order number

      // Publish the event
      fireRemoteLineStateChanged(remoteLinePair.remoteLineSet, blfArgs);
   }

   return kSuccess;
}

int SipBusyLampFieldManagerImpl::onDialogResourceListUpdated(SipDialogEventSubscriptionHandle subscription, const DialogResourceListEvent& args)
{
   DebugLog(<< __FUNCTION__ << " subscription: " << subscription);

   // update remote lines from resource list
   for (SipBusyLampFieldRemoteLineSetMap::const_iterator remoteLineSetIter = mRemoteLineSetInfoMap.begin(); remoteLineSetIter != mRemoteLineSetInfoMap.end(); ++remoteLineSetIter)
   {
      const SipBusyLampFieldRemoteLineSetHandle remoteLineSet = remoteLineSetIter->first;
      SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo = remoteLineSetIter->second;
      if (remoteLineSetInfo->mResourceListSubscription == subscription)
      {
         const cpc::vector<DialogResourceState>& resources = args.resources;

         // we don't know whether the update we are receiving is a partial list or a full one, and we
         // can only use it for ordering the items if it's a full list, so we flag the case where the
         // server should be sending a full list by marking the existing items as "old"
         bool isFullList = remoteLineSetInfo->mRemoteLineSubscriptions.size() == 0;
         if (isFullList)
            DebugLog(<< "Treating as a full list since we don't have a previous list");
         for (std::map<SipBusyLampFieldRemoteLineHandle, SipBusyLampFieldRemoteLineInfo>::const_iterator remoteLineSubscriptionIter = remoteLineSetInfo->mRemoteLineSubscriptions.begin();
             !isFullList && remoteLineSubscriptionIter != remoteLineSetInfo->mRemoteLineSubscriptions.end(); ++remoteLineSubscriptionIter)
         {
            // if any of the old items identify this as a resubscribe then the server should be sending
            // a full list
            if (remoteLineSubscriptionIter->second.mSubscriptionHandle == OldSubscriptionHandle)
            {
               DebugLog(<< "Treating as a full list as this is a resubscribe");
               isFullList = true;
            }
         }

         int orderNumber = 0;
         size_t terminatedLinesCount = 0;
         for (cpc::vector<DialogResourceState>::const_iterator resIt = resources.begin(); resIt != resources.end(); ++resIt)
         {
            if (isFullList)
               orderNumber++; // this is a full list of lines so we can provide the order number
             
            SipBusyLampFieldRemoteLineHandle remoteLineHandle = resIt->uri;

            if (resIt->state == SipEvent::SipSubscriptionState_Terminated) {
               ++terminatedLinesCount;
            } else {
               cpc::string remoteLineName = getRemoteLineName(resIt->dialogInfoDoc);
               bool handleAsNewResource = false;
               
               if (remoteLineSetInfo->mRemoteLineSubscriptions.find(remoteLineHandle) == remoteLineSetInfo->mRemoteLineSubscriptions.end())
               {
                  // new resource
                  remoteLineSetInfo->mRemoteLineSubscriptions[remoteLineHandle].mSubscriptionHandle = NewSubscriptionHandle;
                  remoteLineSetInfo->mRemoteLineSubscriptions[remoteLineHandle].mRemoteLineName = remoteLineName;
                  
                  handleAsNewResource = true;
               }
               else if (remoteLineSetInfo->mRemoteLineSubscriptions[remoteLineHandle].mRemoteLineName != remoteLineName)
               {
                  // changed resource
                  remoteLineSetInfo->mRemoteLineSubscriptions[remoteLineHandle].mRemoteLineName = remoteLineName;

                  // we don't have an event in the API to update the line name so we handle it by faking 
                  // an unsubscribe followed by a subscribe

                  RemoteLineSubscriptionEndedEvent blfArgs;
                  blfArgs.account = mAccount;
                  blfArgs.remoteLine = remoteLineHandle;
                  blfArgs.endReason = SipEvent::SipSubscriptionEndReason_Unknown;
                  fireRemoteLineSubscriptionEnded(remoteLineSet, blfArgs);

                  handleAsNewResource = true;
               }

               if (handleAsNewResource)
               {
                  RemoteLineNewSubscriptionEvent blfArgs;
                  blfArgs.account = mAccount;
                  blfArgs.remoteLine = remoteLineHandle;
                  blfArgs.remoteLineName = remoteLineName;

                  fireRemoteLineNewSubscription(remoteLineSet, blfArgs);
               }

               // immediately update the subscription state as well
               remoteLineSetInfo->mRemoteLineSubscriptions[remoteLineHandle].mSubscriptionHandle = NewSubscriptionHandle;
               
               RemoteLineSubscriptionStateChangedEvent blfArgs;
               blfArgs.account = mAccount;
               blfArgs.remoteLine = remoteLineHandle;
               blfArgs.subscriptionState = resIt->state;

               fireRemoteLineSubscriptionStateChanged(remoteLineSet, blfArgs);
            }

            // update the dialog state if present
            // Create the event
            RemoteLineStateChangedEvent blfArgs;
            blfArgs.account = mAccount;
            blfArgs.remoteLine = remoteLineHandle;
            blfArgs.calls = createCalls(remoteLineHandle, resIt->dialogInfoDoc);
            blfArgs.orderNumber = orderNumber;

            // Publish the event
            fireRemoteLineStateChanged(remoteLineSet, blfArgs);
         }

         // now we check for any lines that have the "old" flag (i.e. were not updated above) and
         // remove them because they were not part of the update and therefore no longer on the server
         for (std::map<SipBusyLampFieldRemoteLineHandle, SipBusyLampFieldRemoteLineInfo>::const_iterator remoteLineSubscriptionIter = remoteLineSetInfo->mRemoteLineSubscriptions.begin();
              isFullList && remoteLineSubscriptionIter != remoteLineSetInfo->mRemoteLineSubscriptions.end();)
         {
            if (remoteLineSubscriptionIter->second.mSubscriptionHandle == OldSubscriptionHandle)
            {
               SipBusyLampFieldRemoteLineHandle remoteLineHandle = remoteLineSubscriptionIter->first;
                
               DebugLog(<< "Removing old subscription that no longer exists in the new list: " <<
                        remoteLineHandle);
                
               RemoteLineSubscriptionEndedEvent blfArgs;
               blfArgs.account = mAccount;
               blfArgs.remoteLine = remoteLineHandle;
               blfArgs.endReason = SipEvent::SipSubscriptionEndReason_Unknown;
               fireRemoteLineSubscriptionEnded(remoteLineSet, blfArgs);

               remoteLineSubscriptionIter = remoteLineSetInfo->mRemoteLineSubscriptions.erase(remoteLineSubscriptionIter);
               ++terminatedLinesCount;
            }
            else {
               remoteLineSubscriptionIter++;
            }
         }

         if (resources.size() && terminatedLinesCount >= resources.size()) {
            RemoteLineEmptySubscriptionsEvent blfArgs;
            blfArgs.account = mAccount;
            fireRemoteLineEmptySubscriptions(blfArgs);
         }
         break;
      }
   }
   
   return kSuccess;
}

int SipBusyLampFieldManagerImpl::onSubscriptionStateChanged(SipDialogEventSubscriptionHandle subscription, const DialogEventSubscriptionStateChangedEvent& args)
{
   // Get the remote line that contains the subscription
   bool rlsMode = false;
   RemoteLinePairVector remoteLinePairs = getRemoteLinesForSubscription(subscription, rlsMode);
   if (rlsMode) return kSuccess;

   for (RemoteLinePairVector::const_iterator pairIt = remoteLinePairs.begin(); pairIt != remoteLinePairs.end(); ++pairIt)
   {
      RemoteLinePair remoteLinePair = *pairIt;
      if (!isRemoteLinePairValid(remoteLinePair))
         continue;

      // Create the event
      RemoteLineSubscriptionStateChangedEvent blfArgs;
      blfArgs.account = mAccount;
      blfArgs.remoteLine = remoteLinePair.remoteLine;
      blfArgs.subscriptionState = args.subscriptionState;

      // Publish the event
      fireRemoteLineSubscriptionStateChanged(remoteLinePair.remoteLineSet, blfArgs);
   }

   return kSuccess;
}

int SipBusyLampFieldManagerImpl::onNotifyDialogInfoFailure(SipDialogEventSubscriptionHandle subscription, const NotifyDialogInfoFailureEvent& args)
{
   return kSuccess;
}

int SipBusyLampFieldManagerImpl::onError(SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::ErrorEvent& args)
{
   // Get the remote line that contains the subscription
   bool rlsMode = false;
   RemoteLinePairVector remoteLinePairs = getRemoteLinesForSubscription(subscription, rlsMode);
   for (RemoteLinePairVector::const_iterator pairIt = remoteLinePairs.begin(); pairIt != remoteLinePairs.end(); ++pairIt)
   {
      RemoteLinePair remoteLinePair = *pairIt;
      if (!isRemoteLinePairValid(remoteLinePair))
         continue;

      // Create the event
      ErrorEvent blfArgs;
      blfArgs.account = mAccount;
      blfArgs.remoteLine = remoteLinePair.remoteLine;
      blfArgs.errorText = args.errorText;

      // Publish the event
      fireError(remoteLinePair.remoteLineSet, blfArgs);
   }
   

   return kSuccess;
}

void SipBusyLampFieldManagerImpl::onTimer(unsigned short timerId, void* appState)
{
   BusyLampFieldResubscribe* resubscribeState = static_cast<BusyLampFieldResubscribe*>(appState);
   DebugLog(<< "SipBusyLampFieldManagerImpl::onTimer(" << timerId << ") for subscription " << resubscribeState->subscription);

   ResubscribeMap::iterator itr = mResubscribeMap.find(resubscribeState->subscription);
   if (itr != mResubscribeMap.end())
   {
      BusyLampFieldResubscribe* resubscribe = itr->second;

      if (resubscribe->account == resubscribeState->account && resubscribe->subscription == resubscribeState->subscription)
      {
         DebugLog(<< "Found mapping, cancelling timer for account " << resubscribeState->account << " and subscription " << resubscribeState->subscription);
         resubscribe->m_Timer->cancel();
         
         // Browse through all the subscriptions in the remote line set to find the subscription specified
         SipBusyLampFieldSubscriptionState subscriptionState = SipBusyLampFieldSubscriptionState::Unsubscribed;
         SipDialogEventSubscriptionSettings settings;

         for (SipBusyLampFieldRemoteLineSetMap::const_iterator remoteLineSetIter = mRemoteLineSetInfoMap.begin();
            remoteLineSetIter != mRemoteLineSetInfoMap.end() &&
            subscriptionState == SipBusyLampFieldSubscriptionState::Unsubscribed; remoteLineSetIter++)
         {
            SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo  = remoteLineSetIter->second;

            // Create the dialog-event related settings, required if we find a match
            settings = dialogEventSubSettings(remoteLineSetInfo);

            if (remoteLineSetInfo->mResourceListSubscription == resubscribeState->subscription)
            {
                DebugLog(<< "Remote line set " << remoteLineSetInfo->mHandle << " subscription " << remoteLineSetInfo->mResourceListSubscription << " found");
                subscriptionState = remoteLineSetInfo->mSubscriptionStates[remoteLineSetInfo->mResourceListSubscription];
                break;
            }

            for (std::map<SipBusyLampFieldRemoteLineHandle, SipBusyLampFieldRemoteLineInfo>::const_iterator remoteLineSubscriptionIter = remoteLineSetInfo->mRemoteLineSubscriptions.begin();
               remoteLineSubscriptionIter != remoteLineSetInfo->mRemoteLineSubscriptions.end(); remoteLineSubscriptionIter++)
            {
               SipBusyLampFieldRemoteLineHandle remoteLine = remoteLineSubscriptionIter->first;
               SipDialogEventSubscriptionHandle remoteLineSubscription = remoteLineSubscriptionIter->second.mSubscriptionHandle;

               // Check to see if this is the subscription we are looking for
               if (remoteLineSubscription == resubscribeState->subscription)
               {
                  DebugLog(<< "Remote line " << remoteLine << " subscription " << remoteLineSubscription << " found");
                  subscriptionState = getLineSubscriptionState(remoteLine);
                  break;
               }
            }
         }
         
         if (subscriptionState == SipBusyLampFieldSubscriptionState::Unsubscribed)
         {
            ErrLog(<< "Unable to find subscription, not resubscribing");
         }
         // restart the subscription unless it's already subscribed or has been ended by the app
         else if (subscriptionState != SipBusyLampFieldSubscriptionState::ResubscribePending)
         {
            DebugLog(<< "Subscription state is " << subscriptionState << " instead of ResubscribePending, not resubscribing");
         }
         else if (mSipDialogEventSubscriptionIf->recreateSubscription(resubscribeState->account, resubscribeState->subscription))
         {
            InfoLog(<< "Resubscribing " << resubscribeState->subscription);

            // this is required so that our subcribe contains the proper "Accept" header
            mSipDialogEventSubscriptionIf->applySubscriptionSettings(resubscribeState->subscription, settings);

            mSipDialogEventSubscriptionIf->addParticipant(resubscribeState->subscription, resubscribe->buddyAddress);
            mSipDialogEventSubscriptionIf->start(resubscribeState->subscription);
         }
         else
         {
            ErrLog(<< "Unable to recreate subscription, not resubscribing");
         }
      }
      else
      {
         ErrLog(<< "Mismatched found v.s. incoming: account " << resubscribe->account << " v.s. " << resubscribeState->account << 
            " or subscription " << resubscribe->subscription << " v.s. " << resubscribeState->subscription);
      }

      // Remove this entry from the resubscribe map
      mResubscribeMap.erase(itr);
      delete resubscribe;
   }
   else
   {
      DebugLog(<< "Resubscribe mapping not found");
   }
}

RemoteLinePairVector SipBusyLampFieldManagerImpl::getRemoteLinesForSubscription(SipDialogEventSubscriptionHandle subscription, bool& isRlsSubscription) const
{
   RemoteLinePairVector remoteLines;
   // Browse through all the remote line sets to find the one that contains the subscription specified
   for (SipBusyLampFieldRemoteLineSetMap::const_iterator remoteLineSetIter = mRemoteLineSetInfoMap.begin(); remoteLineSetIter != mRemoteLineSetInfoMap.end(); remoteLineSetIter++)
   {
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet = remoteLineSetIter->first;
      SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo  = remoteLineSetIter->second;
      isRlsSubscription = remoteLineSetInfo->mResourceListSubscription != 0 && remoteLineSetInfo->mResourceListSubscription == subscription;

      // Browse through all the subscriptions in the remote line set to find the subscription specified
      for (std::map<SipBusyLampFieldRemoteLineHandle, SipBusyLampFieldRemoteLineInfo>::const_iterator remoteLineSubscriptionIter = remoteLineSetInfo->mRemoteLineSubscriptions.begin(); remoteLineSubscriptionIter != remoteLineSetInfo->mRemoteLineSubscriptions.end(); remoteLineSubscriptionIter++)
      {
         SipBusyLampFieldRemoteLineHandle remoteLine = remoteLineSubscriptionIter->first;
         SipDialogEventSubscriptionHandle remoteLineSubscription = remoteLineSubscriptionIter->second.mSubscriptionHandle;

         // Check to see if this is the subscription we are looking for
         if (isRlsSubscription || remoteLineSubscription == subscription)
         {
            // Subscription found. Return remote line
            RemoteLinePair remoteLinePair;
            remoteLinePair.remoteLine = remoteLine;
            remoteLinePair.remoteLineSet = remoteLineSet;
            remoteLines.push_back(remoteLinePair);
            if (!isRlsSubscription)
               break;
         }
      }
   }

   return remoteLines;
}


SipBusyLampFieldSubscriptionState SipBusyLampFieldManagerImpl::getLineSubscriptionState(SipBusyLampFieldRemoteLineHandle remoteLine) const
{
   for (SipBusyLampFieldRemoteLineSetMap::const_iterator remoteLineSetIter = mRemoteLineSetInfoMap.begin(); remoteLineSetIter != mRemoteLineSetInfoMap.end(); remoteLineSetIter++)
   {
      SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo = remoteLineSetIter->second;

      // Browse through all the subscriptions in the remote line set to find the line specified
      for (std::map<SipBusyLampFieldRemoteLineHandle, SipBusyLampFieldRemoteLineInfo>::const_iterator remoteLineSubscriptionIter = remoteLineSetInfo->mRemoteLineSubscriptions.begin(); remoteLineSubscriptionIter != remoteLineSetInfo->mRemoteLineSubscriptions.end(); remoteLineSubscriptionIter++)
      {
         if (remoteLine == remoteLineSubscriptionIter->first)
         {
            if (remoteLineSetInfo->mResourceListSubscription > 0)
               return remoteLineSetInfo->mSubscriptionStates[remoteLineSetInfo->mResourceListSubscription];

            return remoteLineSetInfo->mSubscriptionStates[remoteLineSetInfo->mRemoteLineSubscriptions[remoteLine].mSubscriptionHandle];
         }
      }
   }

   return SipBusyLampFieldSubscriptionState::DefaultSubscribing;
}

SipBusyLampFieldSubscriptionState SipBusyLampFieldManagerImpl::getSubscriptionState(SipDialogEventSubscriptionHandle subscription) const
{
   if (subscription == 0)
       return SipBusyLampFieldSubscriptionState::Unsubscribed;

   // Browse through all the remote line sets to find the one that contains the subscription specified
   for (SipBusyLampFieldRemoteLineSetMap::const_iterator remoteLineSetIter = mRemoteLineSetInfoMap.begin(); remoteLineSetIter != mRemoteLineSetInfoMap.end(); remoteLineSetIter++)
   {
      SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo = remoteLineSetIter->second;

       if (remoteLineSetInfo->mResourceListSubscription == subscription)
       {
          DebugLog(<< "Remote line set " << remoteLineSetInfo->mHandle << " subscription " << subscription << " is " << remoteLineSetInfo->mSubscriptionStates[subscription]);
          return remoteLineSetInfo->mSubscriptionStates[subscription];
       }

      std::map<SipDialogEventSubscriptionHandle, SipBusyLampFieldSubscriptionState>::const_iterator it =
         remoteLineSetInfo->mSubscriptionStates.find(subscription);

       if (it != remoteLineSetInfo->mSubscriptionStates.end())
       {
           DebugLog(<< "Remote line subscription " << subscription << " is " << it->second);
           return it->second;
       }
   }

   InfoLog(<< "getSubscriptionState subscription " << subscription << " not found");
   return SipBusyLampFieldSubscriptionState::Unsubscribed;
}

void SipBusyLampFieldManagerImpl::setSubscriptionState(SipDialogEventSubscriptionHandle subscription, SipBusyLampFieldSubscriptionState state)
{
   if (subscription == 0)
      return;

   // Browse through all the remote line sets to find the one that contains the subscription specified
   for (SipBusyLampFieldRemoteLineSetMap::const_iterator remoteLineSetIter = mRemoteLineSetInfoMap.begin(); remoteLineSetIter != mRemoteLineSetInfoMap.end(); remoteLineSetIter++)
   {
      SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo  = remoteLineSetIter->second;

      if (remoteLineSetInfo->mResourceListSubscription == subscription)
      {
         InfoLog(<< "Set remote line set " << remoteLineSetInfo->mHandle << " subscription " << subscription << " state to " << state);
         auto oldState = remoteLineSetInfo->mSubscriptionStates[subscription];
         remoteLineSetInfo->mSubscriptionStates[subscription] = state;
          
         // when the line set is subscribed we will receive a new set of lines and we don't know if
         // any lines have been removed so we flag them all here and they will get checked later
         if (oldState != DefaultSubscribing && state == DefaultSubscribing)
         {
             InfoLog(<< "Set remote line set subscriptions to \"old\"");
             for (std::map<SipBusyLampFieldRemoteLineHandle, SipBusyLampFieldRemoteLineInfo>::iterator it = remoteLineSetInfo->mRemoteLineSubscriptions.begin(); it != remoteLineSetInfo->mRemoteLineSubscriptions.end(); it++)
             {
                 it->second.mSubscriptionHandle = OldSubscriptionHandle;
             }
         }
      }

      for (std::map<SipBusyLampFieldRemoteLineHandle, SipBusyLampFieldRemoteLineInfo>::const_iterator it = remoteLineSetInfo->mRemoteLineSubscriptions.begin(); it != remoteLineSetInfo->mRemoteLineSubscriptions.end(); it++)
      {
         if (it->second.mSubscriptionHandle == subscription)
         {
            InfoLog(<< "Set remote line subscription " << subscription << " state to " << state);
            remoteLineSetInfo->mSubscriptionStates[subscription] = state;
         }
      }
   }
}

SipDialogEventSubscriptionSettings SipBusyLampFieldManagerImpl::dialogEventSubSettings(const SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo) const
{   
   SipDialogEventSubscriptionSettings settings;
   settings.expiresSeconds = remoteLineSetInfo->mSettings.expires;
   settings.includeSessionDescription = false;
   settings.enableNotifyTerminationRetryHandling = false;   // we handle resubscribes in this module
   return settings;
}

cpc::vector<SipBusyLampFieldRemoteLineCallInfo> SipBusyLampFieldManagerImpl::createCalls(SipBusyLampFieldRemoteLineHandle remoteLine, const DialogInfoDocument& dialogInfoDoc)
{
   cpc::vector<SipBusyLampFieldRemoteLineCallInfo> ret;

   for (cpc::vector<DialogInfo>::const_iterator it = dialogInfoDoc.dialogs.begin(); it != dialogInfoDoc.dialogs.end(); it++)
   {
      DialogInfo dialogInfo = *it;
      SipBusyLampFieldRemoteLineCallInfo callInfo;
      callInfo.remoteLine = remoteLine;
      callInfo.dialog = dialogInfo;
      callInfo.isHeld = SipBusyLampFieldHelper::isHeld(dialogInfo);
      callInfo.isParked = SipBusyLampFieldHelper::isParked(dialogInfo);
      ret.push_back(callInfo);
   }

   return ret;
}

cpc::string SipBusyLampFieldManagerImpl::getRemoteLineName(const DialogInfoDocument& dialogInfoDoc) const
{
   for (cpc::vector<DialogInfo>::const_iterator it = dialogInfoDoc.dialogs.begin(); it != dialogInfoDoc.dialogs.end(); it++)
   {
      DialogInfo dialogInfo = *it;
      cpc::vector<IdentityInfo>::const_iterator it2 = dialogInfo.localParticipant.identities.begin();
      for (; it2 != dialogInfo.localParticipant.identities.end(); it++)
         if (!it2->displayName.empty())
            return it2->displayName;
   }

   return cpc::string();
}

DialogInfo SipBusyLampFieldManagerImpl::getDialogForRemoteLine(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, const cpc::string& dialogId)
{
   // Get the latest state of the remote line specified
   SipBusyLampFieldRemoteLineCallInfo callInfo;
   mStateManager->getCall(remoteLineSet, remoteLine, dialogId, callInfo);

   return callInfo.dialog;
}

bool SipBusyLampFieldManagerImpl::isAllowedBargeInMode(SipBusyLampFieldRemoteLineSetInfo* remoteLineSetInfo, BargeInMode bargeInMode)
{
   for (cpc::vector<BargeInMode>::const_iterator it = remoteLineSetInfo->mSettings.allowedBargeInModes.begin(); it != remoteLineSetInfo->mSettings.allowedBargeInModes.end(); it++)
   {
      if (*it == bargeInMode)
      {
         return true;
      }
   }

   return false;
}

bool SipBusyLampFieldManagerImpl::isRemoteLinePairValid(RemoteLinePair remoteLinePair) const
{
   if (remoteLinePair.remoteLineSet > 0 && !remoteLinePair.remoteLine.empty())
   {
      return true;
   }

   return false;
}

}
}

#endif // CPCAPI2_BRAND_SIP_BUSY_LAMP_FIELD_MODULE
