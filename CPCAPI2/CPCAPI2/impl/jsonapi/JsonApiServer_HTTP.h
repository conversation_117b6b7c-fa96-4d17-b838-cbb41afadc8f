#pragma once

#if !defined(CPCAPI2_JSON_API_SERVER_HTTP_H)
#define CPCAPI2_JSON_API_SERVER_HTTP_H

#include "cpcapi2defs.h"
#include "json/JsonData.h"
#include "jsonapi/JsonApiServerModule.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerHandler.h"

#include <rutil/MultiReactor.hxx>
#include <contrib/folly/ProducerConsumerQueue.h>

// rapidjson
#include <document.h>

#include <iostream>
#include <set>
#include <thread>
#include <memory>
#include <atomic>

namespace CPCAPI2
{
class LocalLogger;

namespace JsonApi
{
struct JsonApiUserContext;

class JsonApiServerHTTP
{
public:
   JsonApiServerHTTP(CPCAPI2::Phone* phone);
   virtual ~JsonApiServerHTTP();

   void startServer(const JsonApiServerConfig& serverConfig, std::function<void()> onPrivilegedAccessCompleted);
   void stopServer();

   void addHttpResourceHandler(const std::string& method, const std::string& pathRegex, const std::function<void(const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Response>&, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Request>&)>& handler);
   void removeHttpResourceHandler(const std::string& method, const std::string& pathRegex);
   void addHttpsResourceHandler(const std::string& method, const std::string& pathRegex, const std::function<void(const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>&, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>&)>& handler);
   void removeHttpsResourceHandler(const std::string& method, const std::string& pathRegex);

   void handleRequestInContext(unsigned int requestHandle, JsonApiUserContext* sdkContext);
   template <typename T> std::shared_ptr<typename SimpleWeb::Server<T>::Response> getResponse(unsigned int requestHandle) const;
   template <typename T> void send(const CPCAPI2::Json::JsonDataPointer& eventData, const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& httpResponse);
   template <typename T> void send(const char* msgData, size_t msgLen, const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& httpResponse);

   void sendHttp(const CPCAPI2::Json::JsonDataPointer& eventData, const std::shared_ptr<typename SimpleWeb::Server<SimpleWeb::HTTP>::Response>& httpResponse);
   void sendHttps(const CPCAPI2::Json::JsonDataPointer& eventData, const std::shared_ptr<typename SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& httpResponse);
   void sendHttp(const char* msgData, size_t msgLen, const std::shared_ptr<typename SimpleWeb::Server<SimpleWeb::HTTP>::Response>& httpResponse);
   void sendHttps(const char* msgData, size_t msgLen, const std::shared_ptr<typename SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& httpResponse);

   bool useHttps() const { return mUseHttps; }
   const JsonApiServerConfig& config() const { return mConfig; }

   template <typename T>
   static int getAuthTokenFromRequest(const std::shared_ptr<typename SimpleWeb::Server<T>::Request>& request, resip::Data& authToken);

private:
   struct BaseRequest {};
   template <typename T>
   struct UnhandledRequestInfo : BaseRequest
   {
      AuthToken authToken;
      std::shared_ptr<rapidjson::Document> requestJsonDoc;
      std::shared_ptr<typename SimpleWeb::Server<T>::Response> response;
   };

   template <typename T>
   void handleRequest(const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& response, const std::shared_ptr<typename SimpleWeb::Server<T>::Request>& request);
   template <typename T>
   void handleRequestOnSdkThread(const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& response, const std::shared_ptr<typename SimpleWeb::Server<T>::Request>& request);
   void handleRequestInContextImpl(unsigned int requestHandle, JsonApiUserContext* sdkContext, const std::shared_ptr<BaseRequest> requestInfo);
   template <typename T>
   void mapUnhandledRequest(unsigned int requestHandle, const AuthToken& authToken, const std::shared_ptr<rapidjson::Document>& jsonRequest, const std::shared_ptr<typename SimpleWeb::Server<T>::Request>& request, const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& response);
   void clearUnhandledRequest(unsigned int requestHandle);
   template <typename T>
   void startServerImpl(std::unique_ptr<typename SimpleWeb::Server<T> >& server, const JsonApiServerConfig& serverConfig);
   bool fileExists(const cpc::string& filename);
   
private:
   std::thread* mServerThread;
   std::unique_ptr<HttpServer> mWebHttpServer;
   std::unique_ptr<HttpsServer> mWebHttpsServer;
   CPCAPI2::Phone* mPhone;
   JsonApiServerConfig mConfig;

   std::map<unsigned int, std::shared_ptr<BaseRequest>> mUnhandledRequestMap;
   std::atomic_uint mNextRequestHandle;

   bool mUseHttps;
   LocalLogger* mLocalLogger;
};

}

}

#endif // CPCAPI2_JSON_API_SERVER_HTTP_H

