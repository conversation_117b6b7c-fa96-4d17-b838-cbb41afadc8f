#pragma once

namespace CPCAPI2
{

namespace JsonApi
{

class JsonApiClientWebSocketHandler
{

public:

   enum State
   {
      State_Connecting,
      State_Connected,
      State_Failed,
      State_Disconnected
   };

   virtual int onStateChanged(State newState) = 0;
   
   virtual int onLogin() = 0;
   virtual int onLoginResponse(const std::shared_ptr<rapidjson::Document>& response) = 0;

};

static cpc::string get_debug_string(const CPCAPI2::JsonApi::JsonApiClientWebSocketHandler::State& state);

}

}
