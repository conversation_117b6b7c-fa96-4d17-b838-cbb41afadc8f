#include "jsonrpc/OptionalSerialization.h"
#include "jsonrpc/StringSerialization.h"
#include <nlohmann/json.hpp>
#include "ConversationState.h"
#include "ConversationType.h"

#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONNEWCONVERSATIONEVENT_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONNEWCONVERSATIONEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationNewConversationEvent
  {
  public:
    std::optional<int32_t> conversationHandle;
    std::optional<ConversationState> conversationState;
    std::optional<ConversationType> conversationType;
    std::optional<cpc::string> localAddress;
    std::optional<cpc::string> localDisplayName;
    std::optional<cpc::string> remoteAddress;
    std::optional<cpc::string> remoteDisplayName;
    std::optional<int32_t> relatedConversation;
    std::optional<int32_t> conversationToReplace;
    std::optional<int32_t> conversationToJoin;
    std::optional<int32_t> accountHandle;
    std::optional<bool> isAudioCodecsMismatched;
    std::optional<bool> isVideoCodecsMismatched;

    SipConversationNewConversationEvent() = default;

    SipConversationNewConversationEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationNewConversationEvent& o)
    {
      j = nlohmann::json::object();
       if (o.conversationHandle) { j["conversationHandle"] = *(o.conversationHandle); }
     if (o.conversationState) { j["conversationState"] = *(o.conversationState); }
     if (o.conversationType) { j["conversationType"] = *(o.conversationType); }
     if (o.localAddress) { j["localAddress"] = *(o.localAddress); }
     if (o.localDisplayName) { j["localDisplayName"] = *(o.localDisplayName); }
     if (o.remoteAddress) { j["remoteAddress"] = *(o.remoteAddress); }
     if (o.remoteDisplayName) { j["remoteDisplayName"] = *(o.remoteDisplayName); }
     if (o.relatedConversation) { j["relatedConversation"] = *(o.relatedConversation); }
     if (o.conversationToReplace) { j["conversationToReplace"] = *(o.conversationToReplace); }
     if (o.conversationToJoin) { j["conversationToJoin"] = *(o.conversationToJoin); }
     if (o.accountHandle) { j["accountHandle"] = *(o.accountHandle); }
     if (o.isAudioCodecsMismatched) { j["isAudioCodecsMismatched"] = *(o.isAudioCodecsMismatched); }
     if (o.isVideoCodecsMismatched) { j["isVideoCodecsMismatched"] = *(o.isVideoCodecsMismatched); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationNewConversationEvent& o)
    {
     if (j.contains("conversationHandle")) { o.conversationHandle = j.at("conversationHandle"); }
     if (j.contains("conversationState")) { o.conversationState = j.at("conversationState"); }
     if (j.contains("conversationType")) { o.conversationType = j.at("conversationType"); }
     if (j.contains("localAddress")) { o.localAddress = j.at("localAddress"); }
     if (j.contains("localDisplayName")) { o.localDisplayName = j.at("localDisplayName"); }
     if (j.contains("remoteAddress")) { o.remoteAddress = j.at("remoteAddress"); }
     if (j.contains("remoteDisplayName")) { o.remoteDisplayName = j.at("remoteDisplayName"); }
     if (j.contains("relatedConversation")) { o.relatedConversation = j.at("relatedConversation"); }
     if (j.contains("conversationToReplace")) { o.conversationToReplace = j.at("conversationToReplace"); }
     if (j.contains("conversationToJoin")) { o.conversationToJoin = j.at("conversationToJoin"); }
     if (j.contains("accountHandle")) { o.accountHandle = j.at("accountHandle"); }
     if (j.contains("isAudioCodecsMismatched")) { o.isAudioCodecsMismatched = j.at("isAudioCodecsMismatched"); }
     if (j.contains("isVideoCodecsMismatched")) { o.isVideoCodecsMismatched = j.at("isVideoCodecsMismatched"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONNEWCONVERSATIONEVENT_H
