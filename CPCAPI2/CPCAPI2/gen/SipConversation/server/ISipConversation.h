#include "interface/experimental/jsonrpc/StringSerialization.h"
#include "interface/experimental/jsonrpc/OptionalSerialization.h"
#include "interface/experimental/jsonrpc/VectorSerialization.h"
#include "../datatypes/SipConversationSettings.h"
#include "../datatypes/MediaInfo.h"
#include "../datatypes/MediaType.h"
#include "../datatypes/DtmfMode.h"

#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_ISIPCONVERSATION_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_ISIPCONVERSATION_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
class ISipConversation
{
public:
    virtual void setDefaultSettings(const int64_t phoneHandle, const int64_t accountHandle, const SipConversationSettings& settings) = 0;

	virtual int64_t createConversation(const int64_t phoneHandle, const int64_t accountHandle) = 0;

	virtual void addParticipant(const int64_t phoneHandle, const int64_t conversationHandle, const cpc::string& targetAddress) = 0;

	virtual void configureMedia(const int64_t phoneHandle, const int64_t conversationHandle, const MediaInfo& mediaDescriptor) = 0;

	virtual void setMediaEnabled(const int64_t phoneHandle, const int64_t conversationHandle, const MediaType mediaType, const bool enabled) = 0;

	virtual void setAnonymousMode(const int64_t phoneHandle, const int64_t conversationHandle, const int64_t anonymousMode) = 0;

	virtual void start(const int64_t phoneHandle, const int64_t conversationHandle) = 0;

	virtual void hold(const int64_t phoneHandle, const int64_t conversationHandle) = 0;

	virtual void unhold(const int64_t phoneHandle, const int64_t conversationHandle) = 0;

	virtual void sendMediaChangeRequest(const int64_t phoneHandle, const int64_t conversationHandle) = 0;

	virtual void end(const int64_t phoneHandle, const int64_t conversationHandle) = 0;

	virtual void redirect(const int64_t phoneHandle, const int64_t conversationHandle, const cpc::string& targetAddress, const cpc::string& reason) = 0;

	virtual void sendRingingResponse(const int64_t phoneHandle, const int64_t conversationHandle) = 0;

	virtual void reject(const int64_t phoneHandle, const int64_t conversationHandle, const int64_t rejectReason) = 0;

	virtual void accept(const int64_t phoneHandle, const int64_t conversationHandle) = 0;

	virtual void acceptIncomingTransferRequest(const int64_t phoneHandle, const int64_t conversationHandle) = 0;

	virtual void rejectIncomingTransferRequest(const int64_t phoneHandle, const int64_t conversationHandle) = 0;

	virtual void transfer(const int64_t phoneHandle, const int64_t transferTargetConversation, const int64_t transfereeConversation) = 0;

	virtual void transferWithFlag(const int64_t phoneHandle, const int64_t transferTargetConversation, const int64_t transfereeConversation, const bool endTargetConversationOnSuccess) = 0;

	virtual void transferToAddress(const int64_t phoneHandle, const int64_t transfereeConversation, const cpc::string& targetAddress) = 0;

	virtual void setDtmfMode(const int64_t phoneHandle, const int64_t account, const int64_t ordinal, const DtmfMode dtmfMode) = 0;

	virtual void startDtmfTone(const int64_t phoneHandle, const int64_t conversationHandle, const int64_t toneId, const bool playLocally) = 0;

	virtual void stopDtmfTone(const int64_t phoneHandle) = 0;

	virtual void playSound(const int64_t phoneHandle, const int64_t conversationHandle, const cpc::string& resourceUri, const bool repeat) = 0;

	virtual void stopPlaySound(const int64_t phoneHandle, const int64_t conversationHandle) = 0;

	virtual int64_t getCallCount(const int64_t phoneHandle) = 0;

	virtual void setCallKitMode(const int64_t phoneHandle, const int64_t conversationHandle) = 0;

	virtual void setTelecomFrameworkMode(const int64_t phoneHandle, const int64_t conversationHandle) = 0;
}; // class SipConversation
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_ISIPCONVERSATION_H
