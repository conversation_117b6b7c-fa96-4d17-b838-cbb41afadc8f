#include "PhoneInstances.h"

#include <jsonrpccxx/server.hpp>

#include <cstdio>

namespace CPCAPI2
{
PhoneHandle PhoneInstances::add(Phone* instance)
{
    PhoneHandle h = mNextPhoneHandle++;
    mInstances[h] = instance;
    return h;
}

void PhoneInstances::remove(PhoneHandle handle)
{
    mInstances.erase(handle);
}

Phone* PhoneInstances::get(PhoneHandle handle)
{
    if (auto it = mInstances.find(handle); mInstances.end() != it)
    {
        return it->second;
    }
    else
    {
        char temp[1024];
        snprintf(temp, sizeof(temp), "invalid parameter: phone handle %d", (int)handle);
        throw jsonrpccxx::JsonRpcException(jsonrpccxx::error_type::invalid_params, temp);
    }
}

Phone* PhoneInstances::getNoThrow(PhoneHandle handle)
{
    if (auto it = mInstances.find(handle); mInstances.end() != it)
    {
        return it->second;
    }
    else
    {
        return nullptr;
    }
}
} // namespace CPCAPI2