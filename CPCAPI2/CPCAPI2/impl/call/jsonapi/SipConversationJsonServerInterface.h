#pragma once

#if !defined(CPCAPI2_SIP_CONVERSATION_JSON_INTERFACE_H)
#define CPCAPI2_SIP_CONVERSATION_JSON_INTERFACE_H

#include "interface/experimental/call/SipConversationJsonApi.h"
#include "interface/experimental/call/SipConversationJsonProxy.h"
#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "interface/experimental/videostreaming/VideoStreaming.h"
#include "call/SipConversationHandlerInternal.h"
#include "jsonapi/JsonApiServerModule.h"
#include "phone/EventSyncHandler.h"
#include "phone/PhoneModule.h"
#include "peerconnection/PeerConnectionManager.h"
#include "peerconnection/PeerConnectionHandler.h"
#include "peerconnection/PeerConnectionSyncHandler.h"
#include "media/MediaManagerInterface.h"

#include <rutil/Reactor.hxx>

namespace CPCAPI2
{
class PhoneInterface;
namespace SipConversation
{
class SipConversationJsonServerInterface : public CPCAPI2::EventSyncHandler<CPCAPI2::SipConversation::SipConversationHandlerInternal>,
                                           public CPCAPI2::SipConversation::SipConversationAdornmentHandler,
                                           public CPCAPI2::SipConversation::SipConversationJsonApi,
                                           public CPCAPI2::PeerConnection::PeerConnectionHandler,
                                           public CPCAPI2::PeerConnection::PeerConnectionSyncHandler,
                                           public CPCAPI2::JsonApi::JsonApiServerModule,
                                           public CPCAPI2::PhoneModule
{
public:
   SipConversationJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~SipConversationJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // SipConversationHandlerInternal
   virtual int onConversationInitiated(SipConversationHandle conversation, const ConversationInitiatedEvent& args) OVERRIDE;
   virtual int onSdpOfferAnswer(SipConversationHandle conversation, const SdpOfferAnswerEvent& args) OVERRIDE;
   virtual int onLocalSdpOffer(SipConversationHandle conversation, const LocalSdpOfferEvent& args) OVERRIDE;
   virtual int onLocalSdpAnswer(SipConversationHandle conversation, const LocalSdpAnswerEvent& args) OVERRIDE;
   virtual int onNewConversation(SipConversationHandle conversation, const NewConversationEvent& args) OVERRIDE;
   virtual int onConversationEnded(SipConversationHandle conversation, const ConversationEndedEvent& args) OVERRIDE;
   virtual int onIncomingTransferRequest(SipConversationHandle conversation, const TransferRequestEvent& args) OVERRIDE;
   virtual int onIncomingRedirectRequest(SipConversationHandle conversation, const RedirectRequestEvent& args) OVERRIDE;
   virtual int onIncomingTargetChangeRequest(SipConversationHandle conversation, const TargetChangeRequestEvent& args) OVERRIDE;
   virtual int onIncomingHangupRequest(SipConversationHandle conversation, const HangupRequestEvent& args) OVERRIDE;
   virtual int onIncomingBroadsoftTalkRequest(SipConversationHandle conversation, const BroadsoftTalkEvent& args) OVERRIDE;
   virtual int onIncomingBroadsoftHoldRequest(SipConversationHandle conversation, const BroadsoftHoldEvent& args) OVERRIDE;
   virtual int onTransferProgress(SipConversationHandle conversation, const TransferProgressEvent& args) OVERRIDE;
   virtual int onConversationStateChangeRequest(SipConversationHandle conversation, const ConversationStateChangeRequestEvent& args) OVERRIDE;
   virtual int onConversationStateChanged(SipConversationHandle conversation, const ConversationStateChangedEvent& args) OVERRIDE;
   virtual int onConversationMediaChangeRequest(SipConversationHandle conversation, const ConversationMediaChangeRequestEvent& args) OVERRIDE;
   virtual int onConversationMediaChanged(SipConversationHandle conversation, const ConversationMediaChangedEvent& args) OVERRIDE;
   virtual int onConversationStatisticsUpdated(SipConversationHandle conversation, const ConversationStatisticsUpdatedEvent& args) OVERRIDE;
   virtual int onAudioDeviceLevelChange(SipConversationHandle conversation, const ConversationAudioDeviceLevelChangeEvent& args) OVERRIDE;
   virtual int onError(SipConversationHandle conversation, const ErrorEvent& args) OVERRIDE;

   // SipConversationAdornmentInternalHandler
   virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args) OVERRIDE;

private:
   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);

   int handleSetHandler(const rapidjson::Value & functionObjectVal);
   int handleSetDefaultSettings(const rapidjson::Value & functionObjectVal);
   int handleCreateConversation(const rapidjson::Value& functionObjectVal);
   int handleAddParticipant(const rapidjson::Value& functionObjectVal);
   int handleStart(const rapidjson::Value& functionObjectVal);
   int handleEnd(const rapidjson::Value& functionObjectVal);
   int handleAccept(const rapidjson::Value& functionObjectVal);
   int handleReject(const rapidjson::Value& functionObjectVal);
   int handlePeerConnectionOffer(const rapidjson::Value& functionObjectVal);
   int handlePeerConnectionAnswer(const rapidjson::Value& functionObjectVal);
   int handleProvideSdpOffer(const rapidjson::Value& functionObjectVal);
   int handleProvideSdpAnswer(const rapidjson::Value& functionObjectVal);
   int handleRequestStateAllConversations(const rapidjson::Value& functionObjectVal);
   int handleJoinRemoteConversation(const rapidjson::Value& functionObjectVal);
   int handleConfigureMedia(const rapidjson::Value& functionObjectVal);
   int handleSetCryptoSuitesForMedia(const rapidjson::Value& functionObjectVal);
   int handleSetMediaEnabled(const rapidjson::Value& functionObjectVal);
   int handleSendMediaChangeRequest(const rapidjson::Value& functionObjectVal);
   int handleHold(const rapidjson::Value& functionObjectVal);
   int handleUnhold(const rapidjson::Value& functionObjectVal);
   int handleSetCallInitiatedFromPush(const rapidjson::Value& functionObjectVal);
   int handleSetMediaEnabledByDirection(const rapidjson::Value& functionObjectVal);
   int handleSetMediaCryptoSuites(const rapidjson::Value& functionObjectVal);
   int handleSetAnonymousMode(const rapidjson::Value& functionObjectVal);
   int handleSetBestEffortMediaEncryption(const rapidjson::Value& functionObjectVal);
   int handleRedirect(const rapidjson::Value& functionObjectVal);
   int handleSendRingingResponse(const rapidjson::Value& functionObjectVal);
   int handleAcceptIncomingTransferRequest(const rapidjson::Value& functionObjectVal);
   int handleRejectIncomingTransferRequest(const rapidjson::Value& functionObjectVal);
   int handleTransfer(const rapidjson::Value& functionObjectVal);
   int handleSetDtmfMode(const rapidjson::Value& functionObjectVal);
   int handleStartDtmfTone(const rapidjson::Value& functionObjectVal);
   int handleStopDtmfTone(const rapidjson::Value& functionObjectVal);
   int handleSetAnswerMode(const rapidjson::Value& functionObjectVal);
   int handleRefreshConversationStatistics(const rapidjson::Value& functionObjectVal);
   int handleSetAdornmentHeaders(const rapidjson::Value & functionObjectVal);
   int handleAdornMessage(const rapidjson::Value& functionObjectVal);
   int handlePlaySound(const rapidjson::Value& functionObjectVal);
   int handleStopPlaySound(const rapidjson::Value& functionObjectVal);
   int handleStartMonitoringAudioDeviceLevels(const rapidjson::Value& functionObjectVal);
   int handleStopMonitoringAudioDeviceLevels(const rapidjson::Value& functionObjectVal);
   int handleSetCallKitMode(const rapidjson::Value& functionObjectVal);
   int handleSetTelecomFrameworkMode(const rapidjson::Value& functionObjectVal);
   int handleSetIncomingVideoRenderTarget(const rapidjson::Value& functionObjectVal);
   int handleSetVideoNackSettings(const rapidjson::Value& functionObjectVal);

   struct PeerConnectionInfo
   {
      cpc::string sessionId;
      CPCAPI2::PeerConnection::PeerConnectionHandle pc;
   };
   std::map<cpc::string, PeerConnectionInfo>::iterator findPeerConnectionInfo(CPCAPI2::PeerConnection::PeerConnectionHandle pc);
   int handleJoinRemoteConversationFullMediaStack();

   // PeerConnectionHandler
   virtual int onSignalingStateChange(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SignalingStateChangeEvent& args) OVERRIDE;
   virtual int onCreateOfferResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateOfferResult& args) OVERRIDE;
   virtual int onCreateAnswerResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args) OVERRIDE;
   virtual int onSetLocalSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetLocalSessionDescriptionResult& args) OVERRIDE;
   virtual int onSetRemoteSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetRemoteSessionDescriptionResult& args) OVERRIDE;
   virtual int onWebVideoServerReady(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::WebVideoServerReadyEvent& args) OVERRIDE {
      return kSuccess;
   }
   virtual int onError(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::ErrorEvent& args) OVERRIDE;

private:
   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;
   CPCAPI2::PeerConnection::PeerConnectionManager* mPeerConnMgr;
   CPCAPI2::VideoStreaming::VideoStreamingManager* mVideoStreamingMgr;
   CPCAPI2::Media::MediaManagerInterface* mMediaMgr;
   std::map<CPCAPI2::SipConversation::SipConversationHandle, CPCAPI2::VideoStreaming::VideoStreamHandle> mVideoStreamingHandleMap;
   cpc::vector<SipHeader> mAdornHeaders;
   std::map<cpc::string, PeerConnectionInfo> mMapPeerConnections;

   struct MediaMappingInfo
   {
      CPCAPI2::SipConversation::SessionDescription remoteSdp;
   };
   std::map<CPCAPI2::SipConversation::SipConversationHandle, MediaMappingInfo> mMediaMappingInfo;
};
}
}
#endif // CPCAPI2_SIP_CONVERSATION_JSON_INTERFACE_H
