#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_IPVERSION_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_IPVERSION_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  enum class IpVersion
  {
    V4,
    V6,
    Auto,
    AutoPreferV6
  };

  inline std::string to_string(IpVersion value)
  {
    switch (value)
    {
      case IpVersion::V4:
        return "V4";
      case IpVersion::V6:
        return "V6";
      case IpVersion::Auto:
        return "Auto";
      case IpVersion::AutoPreferV6:
        return "Auto_PreferV6";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, IpVersion value)
  {
    switch (value)
    {
      case IpVersion::V4:
        os << "V4";
        break;
      case IpVersion::V6:
        os << "V6";
        break;
      case IpVersion::Auto:
        os << "Auto";
        break;
      case IpVersion::AutoPreferV6:
        os << "Auto_PreferV6";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const IpVersion& e)
  {
    switch (e)
    {
      case IpVersion::V4:
        j = "V4";
        break;
      case IpVersion::V6:
        j = "V6";
        break;
      case IpVersion::Auto:
        j = "Auto";
        break;
      case IpVersion::AutoPreferV6:
        j = "Auto_PreferV6";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, IpVersion& e)
  {
      if (0 == j.get<std::string>().compare("V4"))
    {
      e = IpVersion::V4;
    }
    else if (0 == j.get<std::string>().compare("V6"))
    {
      e = IpVersion::V6;
    }
    else if (0 == j.get<std::string>().compare("Auto"))
    {
      e = IpVersion::Auto;
    }
    else if (0 == j.get<std::string>().compare("Auto_PreferV6"))
    {
      e = IpVersion::AutoPreferV6;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum IpVersion"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_IPVERSION_H
