#include "brand_branded.h"
#if (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)

#include "SipChatManagerInterface.h"
#include "SipChatManagerImpl.h"
#include "SipChatInfo.h"
#include "SipChatHelper.h"

#include "../util/ResipConv.h"
#include "phone/Phone.h"
#include "../phone/PhoneInterface.h"
#include "../account/SipAccountImpl.h"

namespace CPCAPI2
{
namespace SipChat
{

SipChatManagerInterface::SipChatManagerInterface(Phone* phone)
   : EventSource2<EventHandler<SipChatHandler, CPCAPI2::SipAccount::SipAccountHandle> >(dynamic_cast<PhoneInterface*>(phone)),
     mAccountIf(NULL),
     mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   mAccountIf = dynamic_cast<CPCAPI2::SipAccount::SipAccountInterface*>(CPCAPI2::SipAccount::SipAccountManager::getInterface(phone));
}

SipChatManagerInterface::~SipChatManagerInterface() 
{
   accountMap.clear();
}

void SipChatManagerInterface::Release() 
{
   delete this;
}

int SipChatManagerInterface::setHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipChatHandler* handler) 
{
   resip::ReadCallbackBase* f = resip::resip_bind(&SipChatManagerInterface::setHandlerImpl, this, account, handler);
   
   if (handler == NULL)
   {
      // removing the handler involves two steps:
      // 1. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);

      // 2. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      executeOnSdkThread(f);
   }
   else
   {
      postToSdkThread(f);
   }
   
   return kSuccess;
}

void SipChatManagerInterface::setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipChatHandler* handler)
{
   // Retrieve the chat manager associated with the account specified
   SipChatManagerImpl* chatManager = getChatManager(account);
   if (!chatManager)
   {
      // No chat manager associated with the account

      // Get the associated account object
      if (CPCAPI2::SipAccount::SipAccountImpl* acct = mAccountIf->getAccountImpl(account))
      {
         // Create a new chat manager
         chatManager = new SipChatManagerImpl(this, mPhone, *acct); // Destroyed in the destructor of this class

         // Register the event handler
         mPhone->getSdkModuleThread().registerEventHandler(chatManager);

         // Keep mapping account -> chat manager
         accountMap[account] = chatManager;
      }
      else
      {
         mAccountIf->fireError("Invalid account handle for SipChatManager::setHandler");
         return;
      }
   }

   auto it = mHandlers.find(account);
   if (mHandlers.end() != it)
   {
     removeAppHandler(it->second, account);
   }

   mHandlers[account] = handler;
   if (nullptr != handler)
   {
      addAppHandler(handler, account);
   }
}

SipChatHandle SipChatManagerInterface::createChat(SipAccount::SipAccountHandle account) 
{
   SipChatHandle sessionHandle = SipChatInfo::nextChatHandle++;
   postToSdkThread(resip::resip_bind(&SipChatManagerInterface::createChatImpl, this, account, sessionHandle));
   return sessionHandle;
}

void SipChatManagerInterface::createChatImpl(SipAccount::SipAccountHandle account, SipChatHandle sessionHandle) 
{
   // Retrieve the account
   CPCAPI2::SipAccount::SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (!acct)
   {
      // No account found. Send an error
      cpc::string msg = cpc::string("Creating chat session with invalid account handle: ") + cpc::to_string(account);
      mAccountIf->fireError(msg);
      return;
   }

   // Make sure the account is enabled
   if (!acct->isEnabled())
   {
      // Account not enabled. Send an error
      cpc::string msg = cpc::string("Creating chat session before account enabled: ") + cpc::to_string(account);
      mAccountIf->fireError(msg);
      return;
   }

   // Retrieve the chat manager associated with the account specified
   SipChatManagerImpl* chatManager = getChatManager(account);
   assert(chatManager);

   // Make sure the associated DUM is available
   if (!chatManager->getDum())
   {
      // No DUM available. Send an error.
      cpc::string msg = cpc::string("SipChatManagerInterface::createChat called but DUM is not available: ") + cpc::to_string(account) + 
                           ", SipChatHandle invalid: " + cpc::to_string(sessionHandle);
      mAccountIf->fireError(msg);
      return;
   }

   // Create the chat information associated with the chat session
   // and store it in the chat manager
   SipChatInfo* chatInfo = new SipChatInfo(sessionHandle, chatManager->getDum(), chatManager); // Destroyed in SipChatManagerImpl::endChat()
   chatInfo->accountHandle = account;
   chatInfo->isActive = true;
   chatManager->addChatInfo(sessionHandle, chatInfo);

   // NOTE: onNewSession from SipChatManagerImpl takes care of firing events for the new chat session
}

int SipChatManagerInterface::addParticipant(SipChatHandle chat, const cpc::string& participantSipAddress) 
{
   postToSdkThread(resip::resip_bind(&SipChatManagerInterface::addParticipantImpl, this, chat, participantSipAddress));
   return kSuccess;
}

void SipChatManagerInterface::addParticipantImpl(SipChatHandle chat, const cpc::string& participantSipAddress) 
{
   // Get the information for the specified chat session
   SipChatInfo* chatInfo = getChatInfo(chat);
   if (chatInfo == NULL)
   {
      cpc::string msg = cpc::string("SipChatManagerInterface::addParticipant called with invalid chat handle: ") + cpc::to_string(chat);
      this->mAccountIf->fireError(msg);
      return;
   }

   // Retrieve the chat manager associated with the account specified
   SipChatManagerImpl* chatManager = getChatManager(chatInfo->accountHandle);
   assert(chatManager);

   // Convert the participant's SIP address string into the proper structure
   resip::NameAddr participantNameAddr;
   if (!ResipConv::stringToAddr(participantSipAddress, participantNameAddr))
   {
      // Error converting SIP address
      cpc::string msg = "Failed to parse participant URI '" + participantSipAddress + "'";
      chatManager->fireError(chat, msg);
      return;
   }

   // Keep the participant's SIP address
   chatInfo->remotePartyAddress = participantNameAddr;
}

int SipChatManagerInterface::start(SipChatHandle chat) 
{
   postToSdkThread(resip::resip_bind(&SipChatManagerInterface::startImpl, this, chat));
   return kSuccess;
}

void SipChatManagerInterface::startImpl(SipChatHandle chat)
{
   // Get the information for the specified chat session
   SipChatInfo* chatInfo = getChatInfo(chat);
   if (chatInfo == NULL)
   {
      cpc::string msg = cpc::string("SipChatManagerInterface::start called with invalid chat handle: ") + cpc::to_string(chat);
      this->mAccountIf->fireError(msg);
      return;
   }

   // Get the chat manager associated with the account
   SipChatManagerImpl* chatManager = getChatManager(chatInfo->accountHandle);
   assert(chatManager);

   // Make sure there is one participant added to the session
   if (chatInfo->remotePartyAddress.uri().getAor().empty())
   {
      // No participant specified. Send an error
      chatManager->fireError(chatInfo->handle, "Cannot start chat session. No participants have been added");
      return;
   }

   // Make sure the participant is not the same as the creator of the chat session
   CPCAPI2::SipAccount::SipAccountImpl* acct = mAccountIf->getAccountImpl(chatInfo->accountHandle);

   if (!acct)
   {
      mAccountIf->fireError("Invalid account handle for SipChatManager::start");
      return;
   }

   resip::Uri accountUri = SipChatHelper::getUri(*acct);
   if (accountUri == chatInfo->remotePartyAddress.uri())
   {
      // Participant same as creator. Send an error
      chatManager->fireError(chat, "Cannot start chat session. Creator and participant are the same");
      return;
   }

   // transform address if transformer defined
   AddressTransformer* transformer = mPhone->getAddressTransformer();
   if(transformer != NULL)
   {
      cpc::string transformedAddress = "";
      AddressTransformationContext context;
      context.addressUsageType = AddressUsageType::AddressUsageType_SipChat;

      context.registrationDomain = acct->getSettings().domain;

      if(transformer->applyTransformation(chatInfo->remotePartyAddress.uri().getAOR(false).data(), context, transformedAddress) == kSuccess)
      {
         ResipConv::stringToAddr(transformedAddress, chatInfo->remotePartyAddress);
      }
   }

   // Start the chat session
   chatManager->startChat(chatInfo);
}

int SipChatManagerInterface::end(SipChatHandle chat)
{
   postToSdkThread(resip::resip_bind(&SipChatManagerInterface::endImpl, this, chat));
   return kSuccess;
}

void SipChatManagerInterface::endImpl(SipChatHandle chat)
{
   SipChatInfo* chatInfo = getChatInfo(chat);
   if (chatInfo == NULL)
   {
      cpc::string msg = cpc::string("SipChatManagerInterface::end called with invalid chat handle: ") + cpc::to_string(chat);
      this->mAccountIf->fireError(msg);
      return;
   }

   // Get the chat manager associated with the account
   SipChatManagerImpl* chatManager = getChatManager(chatInfo->accountHandle);
   assert(chatManager);

   // Terminate the chat session
   chatManager->endChat(chatInfo);
}

SipChatMessageHandle SipChatManagerInterface::sendMessage(SipChatHandle chat, const cpc::string& messageContent, CPM::MimeType mimeType, struct std::tm* datetime, const cpc::vector<DispositionNotificationType>& dispositionNotifications)
{
   // Get the information for the specified chat session
   SipChatInfo* chatInfo = getChatInfo(chat);
   if (chatInfo == NULL)
   {
      cpc::string msg = cpc::string("SipChatManagerInterface::sendMessage called with invalid chat handle: ") + cpc::to_string(chat);
      this->mAccountIf->fireError(msg);
      return "";
   }

   // Get the chat manager associated with the account
   SipChatManagerImpl* chatManager = getChatManager(chatInfo->accountHandle);
   assert(chatManager);

   // Generate a new message ID
   SipChatMessageHandle messageId = chatManager->getNextMessageId(chatInfo);

   // Send the message
   postToSdkThread(resip::resip_bind(&SipChatManagerInterface::sendMessageImpl, this, chatInfo, messageId, messageContent, mimeType, datetime, dispositionNotifications));
   return messageId;
}

void SipChatManagerInterface::sendMessageImpl(SipChatInfo* chatInfo, const SipChatMessageHandle& messageId, const cpc::string& messageContent, CPM::MimeType mimeType, struct std::tm* datetime, const cpc::vector<DispositionNotificationType>& dispositionNotifications) 
{
   // Get the chat manager associated with the account
   SipChatManagerImpl* chatManager = getChatManager(chatInfo->accountHandle);
   assert(chatManager);

   // Set the date/time if needed
   tm datetimeVal = datetime ? *datetime : SipChatHelper::getCurrentDateTime();

   // Send the message
   chatManager->sendMessage(chatInfo, messageId, datetimeVal, SipChatHelper::mimeTypeToContentType(mimeType), resip::Data(messageContent.c_str(), messageContent.size()), dispositionNotifications);
}

int SipChatManagerInterface::accept(SipChatHandle chat) 
{
   postToSdkThread(resip::resip_bind(&SipChatManagerInterface::acceptImpl, this, chat));
   return kSuccess;
}

void SipChatManagerInterface::acceptImpl(SipChatHandle chat)
{
   // Get the information associated with the chat session specified
   SipChatInfo* chatInfo = getChatInfo(chat);
   if (chatInfo == NULL)
   {
      cpc::string msg = cpc::string("SipChatManagerInterface::accept called with invalid chat handle: ") + cpc::to_string(chat);
      this->mAccountIf->fireError(msg);
      return;
   }

   // Get the chat manager associated with the account
   SipChatManagerImpl* chatManager = getChatManager(chatInfo->accountHandle);
   assert(chatManager);

   // Accept the chat session
   chatManager->acceptChat(chatInfo);
}

int SipChatManagerInterface::reject(SipChatHandle chat, unsigned int reasonCode)
{
   postToSdkThread(resip::resip_bind(&SipChatManagerInterface::rejectImpl, this, chat, reasonCode));
   return kSuccess;
}

void SipChatManagerInterface::rejectImpl(SipChatHandle chat, unsigned int reasonCode)
{
   // Get the information associated with the chat session specified
   SipChatInfo* chatInfo = getChatInfo(chat);
   if (chatInfo == NULL)
   {
      cpc::string msg = cpc::string("SipChatManagerInterface::reject called with invalid chat handle: ") + cpc::to_string(chat);
      this->mAccountIf->fireError(msg);
      return;
   }

   // Get the chat manager associated with the account
   SipChatManagerImpl* chatManager = getChatManager(chatInfo->accountHandle);
   assert(chatManager);

   // Reject the chat session
   chatManager->rejectChat(chatInfo, reasonCode);
}

int SipChatManagerInterface::setIsComposingMessage(SipChatHandle chat, CPM::MimeType mimeType, struct std::tm* datetime, int refreshInterval, int idleInterval)
{
   // Set the date/time if needed
   tm datetimeVal = datetime ? *datetime : SipChatHelper::getCurrentDateTime();

   postToSdkThread(resip::resip_bind(&SipChatManagerInterface::setIsComposingMessageImpl, this, chat, mimeType, datetimeVal, refreshInterval, idleInterval));
   return kSuccess;
}

void SipChatManagerInterface::setIsComposingMessageImpl(SipChatHandle chat, CPM::MimeType mimeType, struct std::tm datetime, int refreshInterval, int idleInterval)
{
   // Get the information associated with the chat session specified
   SipChatInfo* chatInfo = getChatInfo(chat);
   if (chatInfo == NULL)
   {
      cpc::string msg = cpc::string("SipChatManagerInterface::setIsComposingMessage called with invalid chat handle: ") + cpc::to_string(chat);
      this->mAccountIf->fireError(msg);
      return;
   }

   // Get the chat manager associated with the account
   SipChatManagerImpl* chatManager = getChatManager(chatInfo->accountHandle);
   assert(chatManager);

   // Send the notification
   chatManager->setIsComposingMessage(chatInfo, SipChatHelper::mimeTypeToContentType(mimeType), datetime, refreshInterval, idleInterval);
}

SipChatMessageHandle SipChatManagerInterface::notifyMessageDelivered(SipChatHandle chat, SipChatMessageHandle message, MessageDeliveryStatus messageDeliveryStatus)
{
   // Get the information for the specified chat session
   SipChatInfo* chatInfo = getChatInfo(chat);
   if (chatInfo == NULL)
   {
      cpc::string msg = cpc::string("SipChatManagerInterface::notifyMessageDelivered called with invalid chat handle: ") + cpc::to_string(chat);
      this->mAccountIf->fireError(msg);
      return "";
   }

   // Get the chat manager associated with the account
   SipChatManagerImpl* chatManager = getChatManager(chatInfo->accountHandle);
   assert(chatManager);

   // Generate a new message ID
   SipChatMessageHandle handle = chatManager->getNextMessageId(chatInfo);

   // Send the delivery notification
   postToSdkThread(resip::resip_bind(&SipChatManagerInterface::notifyMessageDeliveredImpl, this, chat, handle, message, messageDeliveryStatus));
   return handle;
}

void SipChatManagerInterface::notifyMessageDeliveredImpl(SipChatHandle chat, const SipChatMessageHandle& message, const SipChatMessageHandle& origMessage, MessageDeliveryStatus messageDeliveryStatus)
{
   // Get the information associated with the chat session specified
   SipChatInfo* chatInfo = getChatInfo(chat);
   if (chatInfo == NULL)
   {
      cpc::string msg = cpc::string("SipChatManagerInterface::notifyMessageDelivered called with invalid chat handle: ") + cpc::to_string(chat);
      this->mAccountIf->fireError(msg);
      return;
   }

   // Get the chat manager associated with the account
   SipChatManagerImpl* chatManager = getChatManager(chatInfo->accountHandle);
   assert(chatManager);

   // Send the notification
   chatManager->notifyMessageDelivered(chatInfo, message, origMessage, messageDeliveryStatus);
}

SipChatMessageHandle SipChatManagerInterface::notifyMessageDisplayed(SipChatHandle chat, SipChatMessageHandle message, MessageDisplayStatus messageDisplayStatus)
{
   // Get the information for the specified chat session
   SipChatInfo* chatInfo = getChatInfo(chat);
   if (chatInfo == NULL)
   {
      cpc::string msg = cpc::string("SipChatManagerInterface::notifyMessageDisplayed called with invalid chat handle: ") + cpc::to_string(chat);
      this->mAccountIf->fireError(msg);
      return "";
   }

   // Get the chat manager associated with the account
   SipChatManagerImpl* chatManager = getChatManager(chatInfo->accountHandle);
   assert(chatManager);

   // Generate a new message ID
   SipChatMessageHandle handle = chatManager->getNextMessageId(chatInfo);

   // Send the display notification
   postToSdkThread(resip::resip_bind(&SipChatManagerInterface::notifyMessageDisplayedImpl, this, chat, handle, message, messageDisplayStatus));
   return handle;
}

void SipChatManagerInterface::notifyMessageDisplayedImpl(SipChatHandle chat, const SipChatMessageHandle& message, const SipChatMessageHandle& origMessage, MessageDisplayStatus messageDisplayStatus)
{
   // Get the information associated with the chat session specified
   SipChatInfo* chatInfo = getChatInfo(chat);
   if (chatInfo == NULL)
   {
      cpc::string msg = cpc::string("SipChatManagerInterface::notifyMessageDisplayed called with invalid chat handle: ") + cpc::to_string(chat);
      this->mAccountIf->fireError(msg);
      return;
   }

   // Get the chat manager associated with the account
   SipChatManagerImpl* chatManager = getChatManager(chatInfo->accountHandle);
   assert(chatManager);

   // Send the notification
   chatManager->notifyMessageDisplayed(chatInfo, message, origMessage, messageDisplayStatus);
}

SipChatInfo *SipChatManagerInterface::getChatInfo(SipChatHandle handle)
{
   // Browse through all the registered accounts to find
   // the chat manager associated with the handle specified
   for (AccountMap::iterator it = accountMap.begin(); it != accountMap.end(); it++)
   {
      SipChatManagerImpl* chatManager = it->second;
      SipChatInfo* chatInfo = chatManager->getChatInfo(handle);
      if (chatInfo)
      {
         return chatInfo;
      }
   }

   return NULL;
}

SipChatManagerImpl* SipChatManagerInterface::getChatManager(CPCAPI2::SipAccount::SipAccountHandle account)
{
   AccountMap::iterator it = accountMap.find(account);
   return (it != accountMap.end()) ? it->second : NULL;
}

std::ostream& operator<<(std::ostream& os, const NewChatEvent& evt)
{
  return os << "NewChatEvent";
}

std::ostream& operator<<(std::ostream& os, const IsComposingMessageEvent& evt)
{
  return os << "IsComposingMessageEvent";
}

std::ostream& operator<<(std::ostream& os, const NewMessageEvent& evt)
{
  return os << "NewMessageEvent";
}

std::ostream& operator<<(std::ostream& os, const MessageDeliveredEvent& evt)
{
  return os << "MessageDeliveredEvent";
}

std::ostream& operator<<(std::ostream& os, const MessageDisplayedEvent& evt)
{
  return os << "MessageDisplayedEvent";
}

std::ostream& operator<<(std::ostream& os, const SendMessageSuccessEvent& evt)
{
  return os << "SendMessageSuccessEvent";
}

std::ostream& operator<<(std::ostream& os, const SendMessageFailureEvent& evt)
{
  return os << "SendMessageFailureEvent";
}

std::ostream& operator<<(std::ostream& os, const NotifyMessageDeliveredSuccessEvent& evt)
{
  return os << "NotifyMessageDeliveredSuccessEvent";
}

std::ostream& operator<<(std::ostream& os, const NotifyMessageDisplayedSuccessEvent& evt)
{
  return os << "NotifyMessageDisplayedSuccessEvent";
}

std::ostream& operator<<(std::ostream& os, const SetIsComposingMessageSuccessEvent& evt)
{
  return os << "SetIsComposingMessageSuccessEvent";
}

std::ostream& operator<<(std::ostream& os, const ChatEndedEvent& evt)
{
  return os << "ChatEndedEvent";
}

std::ostream& operator<<(std::ostream& os, const SipChat::ErrorEvent& evt)
{
  return os << "SipChat::ErrorEvent";
}

}
}

#endif
