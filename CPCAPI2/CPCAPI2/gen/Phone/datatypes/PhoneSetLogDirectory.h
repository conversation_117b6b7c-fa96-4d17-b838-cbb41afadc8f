#include "jsonrpc/StringSerialization.h"
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_PHONE_PHONESETLOGDIRECTORY_H
#define JSONRPC_CPCAPI2_PHONE_PHONESETLOGDIRECTORY_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  class PhoneSetLogDirectory
  {
  public:
    int32_t handle;
    cpc::string directory;

    PhoneSetLogDirectory() = default;

    PhoneSetLogDirectory(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const PhoneSetLogDirectory& o)
    {
      j = nlohmann::json::object();
       j["handle"] = o.handle;
     j["directory"] = o.directory;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, PhoneSetLogDirectory& o)
    {
     o.handle = j.at("handle");
     o.directory = j.at("directory");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_PHONESETLOGDIRECTORY_H
