// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef WebSocket_JSON_HELPER_H
#define WebSocket_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/public/websocket/WebSocketTypes.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::WebSocket::WebSocketSettings& v)
    {
      w.StartObject();
      Write(w, "webSocketURL", v.webSocketURL);
      Write(w, "sendImmediatePing", v.sendImmediatePing);
      Write(w, "pingIntervalSeconds", v.pingIntervalSeconds);
      Write(w, "initialRetryIntervalSeconds", v.initialRetryIntervalSeconds);
      Write(w, "maxRetryIntervalSeconds", v.maxRetryIntervalSeconds);
      Write(w, "certMode", v.certMode);
      Write(w, "logPayload", v.logPayload);
      Write(w, "backgroundSocketsIfPossible", v.backgroundSocketsIfPossible);
      Write(w, "isLoginRequired", v.isLoginRequired);
      Write(w, "tlsVersion", v.tlsVersion);
      Write(w, "cipherSuite", v.cipherSuite);
      Write(w, "acceptedCertPublicKeys", v.acceptedCertPublicKeys);
      Write(w, "requiredCertPublicKeys", v.requiredCertPublicKeys);
      Write(w, "certStorageLoadType", v.certStorageLoadType);
      Write(w, "certStorageFileSystemPath", v.certStorageFileSystemPath);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::WebSocket::WebSocketSettings& v)
    {
      Read(json, "webSocketURL", v.webSocketURL);
      Read(json, "sendImmediatePing", v.sendImmediatePing);
      Read(json, "pingIntervalSeconds", v.pingIntervalSeconds);
      Read(json, "initialRetryIntervalSeconds", v.initialRetryIntervalSeconds);
      Read(json, "maxRetryIntervalSeconds", v.maxRetryIntervalSeconds);
      Read(json, "certMode", v.certMode);
      Read(json, "logPayload", v.logPayload);
      Read(json, "backgroundSocketsIfPossible", v.backgroundSocketsIfPossible);
      Read(json, "isLoginRequired", v.isLoginRequired);
      Read(json, "tlsVersion", v.tlsVersion);
      Read(json, "cipherSuite", v.cipherSuite);
      Read(json, "acceptedCertPublicKeys", v.acceptedCertPublicKeys);
      Read(json, "requiredCertPublicKeys", v.requiredCertPublicKeys);
      Read(json, "certStorageLoadType", v.certStorageLoadType);
      Read(json, "certStorageFileSystemPath", v.certStorageFileSystemPath);
    }
  }
}
#pragma warning(pop)
#endif // WebSocket_JSON_HELPER_H