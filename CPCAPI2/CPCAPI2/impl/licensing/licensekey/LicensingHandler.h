#pragma once

#if !defined(__CPCAPI2_LICENSING_HANDLER_H__)
#define __CPCAPI2_LICENSING_HANDLER_H__

#include "LicensingTypes.h"
#include <string>

namespace CPCAPI2
{
namespace Licensing
{
class LicenseStateEvent                           
{
public:
   LicenseStateEvent(LicenseStatus status, const std::string& message, const std::string& url, 
                      const std::string& urldescr, unsigned long daysleft, unsigned long errorCode,
                      const std::string& hwID, const LicenseList& licenses, const InvalidLicenseList& invalidLicenses, void *context) :
      mLicenseStatus(status),
      mLicenseMessage(message),
      mLicenseURL(url),
      mLicenseURLDescr(urldescr),
      mDaysLeft(daysleft),
      mErrorCode(errorCode),
      mHardwareID(hwID),
      mLicenses(licenses),
      mInvalidLicenses(invalidLicenses),
      mContext(context)
   {}
   
   virtual LicenseStatus GetLicenseStatus() const                 { return mLicenseStatus; }
   virtual const std::string          GetLicenseMessage() const   { return mLicenseMessage; }
   virtual const std::string          GetLicenseURL() const       { return mLicenseURL; }
   virtual const std::string          GetLicenseURLDescr() const  { return mLicenseURLDescr; }
   virtual unsigned long              GetDaysLeft() const         { return mDaysLeft; }
   virtual unsigned long              GetErrorCode() const        { return mErrorCode; }
   virtual const std::string&         GetHardwareID() const       { return mHardwareID; }
   virtual const LicenseList&         GetLicenses() const         { return mLicenses; }
   virtual const InvalidLicenseList&  GetInvalidLicenses() const  { return mInvalidLicenses; }
   //virtual const std::string          GetLicenseKey()       { return mLicenseKey; }
   //virtual std::vector<std::pair<std::string, bool> > GetFeatures() { return mFeatures; }
   void* getContext() const { return mContext; }

private:
   LicenseStatus mLicenseStatus;
   std::string                     mLicenseMessage;
   std::string                     mLicenseURL;
   std::string                     mLicenseURLDescr;
   std::string                     mHardwareID;
   //string                     mLicenseKey;
   unsigned long              mDaysLeft;
   unsigned long              mErrorCode;
   LicenseList                mLicenses;
   InvalidLicenseList         mInvalidLicenses;
   //std::vector<std::pair<std::string, bool> > mFeatures;
   void* mContext;
};

class LicensingHandler
{
public:
   virtual void OnLicenseState(const LicenseStateEvent& event) = 0;
};

}
}

#endif // __CPCAPI2_LICENSING_HANDLER_H__
