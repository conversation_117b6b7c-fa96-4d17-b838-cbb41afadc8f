#pragma once

#if !defined(CPCAPI2_JSON_DATA_IMPL_H)
#define CPCAPI2_JSON_DATA_IMPL_H

#include "cpcapi2defs.h"
#include "json/JsonData.h"

#include <string>
#include <stringbuffer.h> // rapidjson

namespace CPCAPI2
{
namespace Json
{
class JsonDataImpl : public JsonData
{
public:
   JsonDataImpl()
   {}

   virtual int32_t getMessageSize() const;
   virtual int32_t getBinarySize() const;

   virtual const char* getMessageData() const;
   virtual const char* getBinaryData() const;

   virtual bool isBinary() const;
   virtual void setBinaryData(const char* buffer, uint32_t size);

   rapidjson::StringBuffer& getStringBuffer();

private:
   rapidjson::StringBuffer mBuffer;
   std::string mBinData;
}; // class JsonDataImpl
} // namespace Json
} // namespace CPCAPI2

#endif // CPCAPI2_JSON_DATA_IMPL_H
