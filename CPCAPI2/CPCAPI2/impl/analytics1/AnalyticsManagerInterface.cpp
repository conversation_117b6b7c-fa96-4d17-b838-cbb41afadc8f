#include <brand_branded.h>
#include <analytics1/AnalyticsTypes.h>
#include "AnalyticsManagerInterface.h"
#include "AnalyticsManagerImpl.h"

#if (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)

#include <analytics1/AnalyticsManager.h>
#include <account/SipAccount.h>
#include <account/SipAccountInterface.h>
#include <call/SipConversationManager.h>
#include <call/SipConversationState.h>
#include <call/SipAVConversationManagerInterface.h>
#include "phone/PhoneInterface.h"
#include <xmpp/XmppAccount.h>
#include <xmpp/XmppAccountInterface.h>
#include <ptt/PushToTalkHandler.h>
#include <ptt/PushToTalkTypes.h>
#include <ptt/PushToTalkManager.h>

using namespace CPCAPI2::Analytics;
using CPCAPI2::SipAccount::SipAccountManager;
using CPCAPI2::SipAccount::SipAccountInterface;
using CPCAPI2::XmppAccount::XmppAccountManager;
using CPCAPI2::XmppAccount::XmppAccountInterface;
using CPCAPI2::PushToTalk::PushToTalkManager;
using CPCAPI2::PushToTalk::PushToTalkManagerInterface;
using CPCAPI2::SipConversation::SipConversationManager;
using CPCAPI2::SipConversation::SipConversationStateManager;
using CPCAPI2::SipConversation::SipAVConversationManagerInterface;

// static handle counter
static AnalyticsHandle CurrentHandle = 0;

AnalyticsManagerInterface::AnalyticsManagerInterface(CPCAPI2::Phone* phone)
   : mImpl(NULL),
     mShutdown(false),
     mPhone(static_cast<PhoneInterface*>(phone)),
     mReactor(new resip::MultiReactor("AnalyticsManager"))
{
   mPhone->addRefImpl();

   mReactor->start();
   CurrentHandle = 0;

   // Fetch the Sip Account Module (we need to listen to this)
   SipAccountInterface* acctIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(phone));

   // Fetch the SipConversation Manager interface (we need to listen to this)
   SipAVConversationManagerInterface* convMgrIf = dynamic_cast<SipAVConversationManagerInterface*> (SipConversationManager::getInterface(phone));

   // From the SipConversationManager interface, fetch the SipConversationStateManager interface.
   SipConversationStateManager* callStateIf = SipConversationStateManager::getInterface(convMgrIf);

   XmppAccountInterface* xmppAcctIf = NULL;
#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
   xmppAcctIf = dynamic_cast<XmppAccountInterface*>(XmppAccountManager::getInterface(phone));
#endif

   PushToTalkManagerInterface* pttIf = NULL;
#if (CPCAPI2_BRAND_PTT_MODULE == 1)
   // Fetch the SipConversation Manager interface (we need to listen to this)
   pttIf = dynamic_cast<PushToTalkManagerInterface*> (PushToTalkManager::getInterface(phone));
#endif
   mImpl = new AnalyticsManagerImpl(mCallbackFifo, acctIf, xmppAcctIf, pttIf, convMgrIf, callStateIf, phone, mReactor);
}

AnalyticsManagerInterface::~AnalyticsManagerInterface()
{
   mPhone->releaseImpl();

   // ask AnalyticsMangerImpl to destruct asynchronously. once it has, it will stop mReactor
   mImpl->Release();

   mReactor->join();
}

void AnalyticsManagerInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
   mReactor->post(resip::resip_bind(&AnalyticsManagerInterface::setCallbackHookImpl, this, cbHook, context ));
}

void AnalyticsManagerInterface::setCallbackHookImpl(void (*cbHook)(void*), void* context)
{
   mImpl->setCallbackHook(cbHook, context);
}

void AnalyticsManagerInterface::setHandler(AnalyticsHandler* handler)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&AnalyticsManagerInterface::setHandlerImpl, this, handler);

   if (handler == NULL)
   {
      // removing the handler involves two steps:

      // 1. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      mReactor->execute(f);

      // 2. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);
   }
   else
   {
      mReactor->post(f);
   }

   return;
}

void AnalyticsManagerInterface::setHandlerImpl(AnalyticsHandler * handler)
{
   mImpl->setHandler( handler ); // inside reactor thread
}

AnalyticsHandle AnalyticsManagerInterface::open(const AnalyticsSettings& settings, const GeneralStats& general, const AnalyticsHandle& serverHandle)
{
    //check for passed in handle to use, otherwise create a new one
    AnalyticsHandle result(serverHandle);
    if (serverHandle == 0) {
        result = ++CurrentHandle;
    }
   
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::open, mImpl, settings, general, result));
   return result;
}

int AnalyticsManagerInterface::close(const AnalyticsHandle& serverHandle)
{
   mReactor->post( resip::resip_safe_bind( &AnalyticsManagerImpl::close, mImpl, serverHandle ));
   return kSuccess;
}

int AnalyticsManagerInterface::setPresenceStats(const AnalyticsHandle& serverHandle, const PresenceStats& stats)
{
   mReactor->post( resip::resip_safe_bind( &AnalyticsManagerImpl::setPresenceStats, mImpl, serverHandle, stats ));
   return kSuccess;
}

int AnalyticsManagerInterface::setProvisioningStats(const AnalyticsHandle& serverHandle, const ProvisioningStats& stats)
{
   mReactor->post( resip::resip_safe_bind( &AnalyticsManagerImpl::setProvisioningStats, mImpl, serverHandle, stats ));
   return kSuccess;
}

int AnalyticsManagerInterface::setStabilityStats(const AnalyticsHandle& serverHandle, const StabilityStats& stats)
{
   mReactor->post( resip::resip_safe_bind( &AnalyticsManagerImpl::setStabilityStats, mImpl, serverHandle, stats ));
   return kSuccess;
}

int AnalyticsManagerInterface::setSettingsStats(const AnalyticsHandle& serverHandle, const SettingsStats& stats)
{
   mReactor->post( resip::resip_safe_bind( &AnalyticsManagerImpl::setSettingsStats, mImpl, serverHandle, stats ));
   return kSuccess;
}

int AnalyticsManagerInterface::sendReport(const AnalyticsHandle& serverHandle)
{
   mReactor->post( resip::resip_safe_bind( &AnalyticsManagerImpl::sendReport, mImpl, serverHandle ));
   return kSuccess;
}

int AnalyticsManagerInterface::xmppAccountStatusChangeFired(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& args)
{
    mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::xmppAccountStatusChangeFired, mImpl, account, args));
    return kSuccess;
}

int AnalyticsManagerInterface::pttServiceStatusChangeFired(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PttServiceStatusChangedEvent& args)
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::pttServiceStatusChangeFired, mImpl, service, args));
   return kSuccess;
}

int AnalyticsManagerInterface::pttSessionStatusChangeFired(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttSessionStateChangedEvent& args)
{
   mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::pttSessionStatusChangeFired, mImpl, session, args));
   return kSuccess;
}

int AnalyticsManagerInterface::CurrentVideoDeviceUpdatedFired(CPCAPI2::Media::VideoDeviceInfo devInfo)
{
    mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::CurrentVideoDeviceUpdatedFired, mImpl, devInfo));
    return kSuccess;
}

int AnalyticsManagerInterface::instantMessageInfoFired(unsigned int accountHandle, const bool incoming, const bool isSip)
{
    mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::instantMessageInfoFired, mImpl, accountHandle, incoming, isSip));
    return kSuccess;
}

int AnalyticsManagerInterface::ConvRecordingStarted(CPCAPI2::SipConversation::SipConversationHandle conversationHandle)
{
    mReactor->post(resip::resip_safe_bind(&AnalyticsManagerImpl::ConvRecordingStarted, mImpl, conversationHandle));
    return kSuccess;
}

void AnalyticsManagerInterface::Release()
{
   // attempts to immediately abort any HTTP operations (not using a reactor), in the
   // hope that we don't get stuck waiting for active HTTP operations during shutdown
   mImpl->abortActiveHttpClients();

   mShutdown = true;
   
   delete this;
}

int AnalyticsManagerInterface::process( unsigned int timeout )
{
   // -1 == no wait
   if( mShutdown )
      return -1;

   resip::ReadCallbackBase* fp = mCallbackFifo.getNext( timeout );
   while( fp )
   {
      (*fp)();
      delete fp;
      if( mShutdown )
         return -1;

      fp = mCallbackFifo.getNext( -1 );
   }

   return kSuccess;
}

#ifdef CPCAPI2_AUTO_TEST
CPCAPI2::AutoTestReadCallback* AnalyticsManagerInterface::process_test(int timeout)
{
   if (mShutdown)
      return NULL;

   resip::ReadCallbackBase* rcb = mCallbackFifo.getNext( timeout );
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
   {
      return fpCmd;
   }
   if (rcb != NULL)
   {
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   }
   return NULL;
}
#endif

#endif
