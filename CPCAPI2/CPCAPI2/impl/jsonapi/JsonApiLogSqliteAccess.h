#pragma once

#if !defined(CPCAPI2_JSON_API_LOG_SQLITE_ACCESS)
#define CPCAPI2_JSON_API_LOG_SQLITE_ACCESS

#include <cpcapi2defs.h>
#include <cpcstl/string.h>
#include <cpcstl/vector.h>

#include <rutil/MultiReactor.hxx>

#include <functional>
#include <thread>

#if !defined(_WIN32) && !defined(ANDROID)
#include <mm_malloc.h>
#endif

namespace soci
{
   class session;
}

namespace CPCAPI2
{
namespace JsonApi
{
struct JsonApiLogDbAccessConfig
{
   std::string dbFileName;
   bool wipeOnRun = false;
};

struct AddApiLogArgs
{
   std::string userContext;
   std::string moduleName;
   std::string functionName;
   std::string jsonFunctionObject;
   int64_t invocationTime = 0;
};

struct AddApiLogResult
{
   int result = 0;
};

struct QueryApiLogArgs
{
};

struct QueryApiLogResult
{
   int result = 0;
};

class JsonApiLogSqliteAccess
{
public:
   JsonApiLogSqliteAccess();
   virtual ~JsonApiLogSqliteAccess();

   void* operator new(size_t i)
   {
#if defined(ANDROID)
      return memalign(folly::hardware_destructive_interference_size, i);
#else
      return _mm_malloc(i, folly::hardware_destructive_interference_size);
#endif
   }

   void operator delete(void* p)
   {
#if defined(ANDROID)
      free(p);
#else
      _mm_free(p);
#endif
   }

   int initialize(const JsonApiLogDbAccessConfig& serverConfig, const std::function<void(bool, const cpc::string&)>& initCb);
   void shutdown();

   int addApiLog(const AddApiLogArgs& apiLogInfo, const std::function<void(bool, const AddApiLogResult&)>& apiLogAddedCb);
   int queryApiLog(const QueryApiLogArgs& queryArgs, const std::function<void(bool, const QueryApiLogResult&)>& queryApiLogResultCb);

   int flushAll();

private:

   void initializeImpl(const JsonApiLogDbAccessConfig& serverConfig, const std::function<void(bool, const cpc::string&)>& initCb);
   void addApiLogImpl(const AddApiLogArgs& apiLogInfo, const std::function<void(bool, const AddApiLogResult&)>& apiLogAddedCb);
   void queryApiLogImpl(const QueryApiLogArgs& queryArgs, const std::function<void(bool, const QueryApiLogResult&)>& queryApiLogResultCb);
   void flushAllImpl();

   void createTables();
   void dump();

private:

   resip::MultiReactor mReactor;
   std::unique_ptr<soci::session> mSOCI;
   int64_t mPushRegTimeoutIntervalMs;

};

}

}

#endif // CPCAPI2_PUSH_SERVICE_SQLITE_ACCESS
