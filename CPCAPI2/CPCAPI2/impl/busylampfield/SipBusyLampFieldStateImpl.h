#pragma once

#if !defined(CPCAPI2_SIP_BUSY_LAMP_FIELD_STATE_IMPL_H)
#define CPCAPI2_SIP_BUSY_LAMP_FIELD_STATE_IMPL_H

#include "cpcapi2defs.h"
#include "busylampfield/SipBusyLampFieldManager.h"
#include "busylampfield/SipBusyLampFieldState.h"
#include "busylampfield/SipBusyLampFieldHandler.h"
#include "phone/PhoneModule.h"

#include <map>

namespace CPCAPI2
{
namespace SipBusyLampField
{
class SipBusyLampFieldManagerInterface;

class SipBusyLampFieldStateImpl : public SipBusyLampFieldStateManager,
                                  public PhoneModule,
                                  public SipBusyLampFieldHandler

{
public:
   SipBusyLampFieldStateImpl(SipBusyLampFieldManagerInterface* blfManagerIf);
   virtual ~SipBusyLampFieldStateImpl();

   virtual void Release() OVERRIDE;

   // SipBusyLampFieldStateManager interface
   virtual int getState(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, SipBusyLampFieldRemoteLineSetState& remoteLineState) OVERRIDE;
   virtual int getState(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, SipBusyLampFieldRemoteLineHandle remoteLine, SipBusyLampFieldRemoteLineState& remoteLineState) OVERRIDE;
   virtual int getCall(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, SipBusyLampFieldRemoteLineHandle remoteLine, const cpc::string& dialogId, SipBusyLampFieldRemoteLineCallInfo& call) OVERRIDE;

private:
   SipBusyLampFieldManagerInterface* mBlfManagerIf;
   std::map<SipBusyLampFieldRemoteLineSetHandle, SipBusyLampFieldRemoteLineSetState> mStateMap;

   static SipBusyLampFieldRemoteLineState REMOTE_LINE_STATE_NOT_FOUND;
   static SipBusyLampFieldRemoteLineCallInfo REMOTE_LINE_CALL_NOT_FOUND;

   // SipBusyLampFieldHandler interface
   virtual int onRemoteLineNewSubscription(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const RemoteLineNewSubscriptionEvent& args) OVERRIDE;
   virtual int onRemoteLineSubscriptionStateChanged(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const RemoteLineSubscriptionStateChangedEvent& args) OVERRIDE;
   virtual int onRemoteLineSubscriptionEnded(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const RemoteLineSubscriptionEndedEvent& args) OVERRIDE;
   virtual int onRemoteLineStateChanged(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const RemoteLineStateChangedEvent& args) OVERRIDE;
   virtual int onRemoteLineEmptySubscriptions(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const RemoteLineEmptySubscriptionsEvent& args) OVERRIDE;
   virtual int onError(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const ErrorEvent& args);

   SipBusyLampFieldRemoteLineState& getRemoteLineState(CPCAPI2::SipAccount::SipAccountHandle account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, SipBusyLampFieldRemoteLineHandle remoteLine);
   SipBusyLampFieldRemoteLineState& getRemoteLineState(SipBusyLampFieldRemoteLineSetState& remoteLineSetState, SipBusyLampFieldRemoteLineHandle remoteLine, bool create);
   SipBusyLampFieldRemoteLineCallInfo& getRemoteLineCall(SipBusyLampFieldRemoteLineState& remoteLineState, const cpc::string& dialogId);
};

}
}

#endif // CPCAPI2_SIP_BUSY_LAMP_FIELD_STATE_IMPL_H
