#include "jsonrpc/StringSerialization.h"
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONTRANSFERTOADDRESS_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONTRANSFERTOADDRESS_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationTransferToAddress
  {
  public:
    int32_t phoneHandle;
    int32_t transfereeConversation;
    cpc::string targetAddress;

    SipConversationTransferToAddress() = default;

    SipConversationTransferToAddress(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationTransferToAddress& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["transfereeConversation"] = o.transfereeConversation;
     j["targetAddress"] = o.targetAddress;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationTransferToAddress& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.transfereeConversation = j.at("transfereeConversation");
     o.targetAddress = j.at("targetAddress");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONTRANSFERTOADDRESS_H
