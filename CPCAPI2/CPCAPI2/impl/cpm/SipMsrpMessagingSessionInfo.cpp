#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)

#include "SipMsrpMessagingSessionInfo.h"

#include <msrp_uri.h>

namespace CPCAPI2
{
namespace CPM
{

SipMsrpMessagingSessionInfo::SipMsrpMessagingSessionInfo(int handle, resip::SharedPtr<resip::DialogUsageManager> dum, resip::InviteSessionHandler* handler)
   : SipAccount::CPInviteHandlerSelectorDialogSet(*dum, handler),
     handle(handle),
     localUri(NULL),
     remoteUri(NULL),
     isActive(false),
     msrpSession(NULL)
{
}

SipMsrpMessagingSessionInfo::~SipMsrpMessagingSessionInfo()
{
   if (localUri)
   {
      msrp_uri_destroy(localUri);
   }

   if (remoteUri)
   {
      msrp_uri_destroy(remoteUri);
   }
}

}
}

#endif // CPCAPI2_BRAND_SIP_CHAT_MODULE
