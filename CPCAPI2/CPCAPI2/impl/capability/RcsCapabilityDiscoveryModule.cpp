#include "brand_branded.h"

#include "interface/experimental/capability/RcsCapabilityDiscovery.h"

#if (CPCAPI2_BRAND_CAPABILITY_DISCOVERY_MODULE == 1)
#include "RcsCapabilityDiscoveryInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace RcsCapabilityDiscovery
{
RcsCapabilityDiscoveryManager* RcsCapabilityDiscoveryManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_CAPABILITY_DISCOVERY_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<RcsCapabilityDiscoveryInterface>(phone, "RcsCapabilityDiscoveryInterface");
#else
   return NULL;
#endif
}

}
}
