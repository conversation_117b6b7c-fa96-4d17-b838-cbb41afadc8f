#pragma once

#if !defined(__CPCAPI2_RECEIVER_IDLE_STATE_H__)
#define __CPCAPI2_RECEIVER_IDLE_STATE_H__

#include "ReceiverBaseState.h"

namespace CPCAPI2
{
namespace IsComposing
{

class ReceiverIdleState : public ReceiverBaseState
{
public:
   virtual void onIdleStateReceived(IsComposingManager* manager, IsComposingInfo* info);
   virtual void onActiveStateReceived(IsComposingManager* manager, IsComposingInfo* info);
   virtual void onMessageReceived(IsComposingManager* manager, IsComposingInfo* info);

   static ReceiverIdleState instance;
};

}
}

#endif // __CPCAPI2_RECEIVER_IDLE_STATE_H__