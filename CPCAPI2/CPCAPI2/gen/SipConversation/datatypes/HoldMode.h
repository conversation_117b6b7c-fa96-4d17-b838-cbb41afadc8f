#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_HOLDMODE_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_HOLDMODE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  enum class HoldMode
  {
    Rfc3264,
    Rfc2543
  };

  inline std::string to_string(HoldMode value)
  {
    switch (value)
    {
      case HoldMode::Rfc3264:
        return "RFC3264";
      case HoldMode::Rfc2543:
        return "RFC2543";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, HoldMode value)
  {
    switch (value)
    {
      case HoldMode::Rfc3264:
        os << "RFC3264";
        break;
      case HoldMode::Rfc2543:
        os << "RFC2543";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const HoldMode& e)
  {
    switch (e)
    {
      case HoldMode::Rfc3264:
        j = "RFC3264";
        break;
      case HoldMode::Rfc2543:
        j = "RFC2543";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, HoldMode& e)
  {
      if (0 == j.get<std::string>().compare("RFC3264"))
    {
      e = HoldMode::Rfc3264;
    }
    else if (0 == j.get<std::string>().compare("RFC2543"))
    {
      e = HoldMode::Rfc2543;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum HoldMode"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_HOLDMODE_H
