// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef PeerConnection_JSON_HELPER_H
#define PeerConnection_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/experimental/peerconnection/PeerConnectionTypes.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PeerConnection::MediaEncryptionOptions& v)
    {
      w.StartObject();
      Write(w, "mediaEncryptionMode", v.mediaEncryptionMode);
      Write(w, "secureMediaRequired", v.secureMediaRequired);
      Write(w, "mediaCryptoSuite", v.mediaCryptoSuite);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PeerConnection::MediaEncryptionOptions& v)
    {
      Read(json, "mediaEncryptionMode", v.mediaEncryptionMode);
      Read(json, "secureMediaRequired", v.secureMediaRequired);
      Read(json, "mediaCryptoSuite", v.mediaCryptoSuite);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PeerConnection::MediaCodec& v)
    {
      w.StartObject();
      Write(w, "codecPayloadName", v.codecPayloadName);
      Write(w, "codecFrequency", v.codecFrequency);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PeerConnection::MediaCodec& v)
    {
      Read(json, "codecPayloadName", v.codecPayloadName);
      Read(json, "codecFrequency", v.codecFrequency);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PeerConnection::MediaInfo& v)
    {
      w.StartObject();
      Write(w, "mediaType", v.mediaType);
      Write(w, "mediaDirection", v.mediaDirection);
      Write(w, "mediaEncryptionOptions", v.mediaEncryptionOptions);
      Write(w, "mediaStream", v.mediaStream);
      Write(w, "mediaStreamId", v.mediaStreamId);
      Write(w, "codecs", v.codecs);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PeerConnection::MediaInfo& v)
    {
      Read(json, "mediaType", v.mediaType);
      Read(json, "mediaDirection", v.mediaDirection);
      Read(json, "mediaEncryptionOptions", v.mediaEncryptionOptions);
      Read(json, "mediaStream", v.mediaStream);
      Read(json, "mediaStreamId", v.mediaStreamId);
      Read(json, "codecs", v.codecs);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PeerConnection::PeerConnectionSettings& v)
    {
      w.StartObject();
      Write(w, "sessionName", v.sessionName);
      Write(w, "certAor", v.certAor);
      Write(w, "natTraversalMode", v.natTraversalMode);
      Write(w, "secureMediaMode", v.secureMediaMode);
      Write(w, "secureMediaRequired", v.secureMediaRequired);
      Write(w, "natTraversalServerHostname", v.natTraversalServerHostname);
      Write(w, "natTraversalServerUsername", v.natTraversalServerUsername);
      Write(w, "natTraversalServerPassword", v.natTraversalServerPassword);
      Write(w, "natTraversalServerPort", v.natTraversalServerPort);
      Write(w, "natTraversalServerType", v.natTraversalServerType);
      Write(w, "localInterface", v.localInterface);
      Write(w, "forcedHostCandidate", v.forcedHostCandidate);
      Write(w, "useUnixDomainSockets", v.useUnixDomainSockets);
      Write(w, "mixId", v.mixId);
      Write(w, "mixContribution", v.mixContribution);
      Write(w, "startPaused", v.startPaused);
      Write(w, "videoCaptureDeviceId", v.videoCaptureDeviceId);
      Write(w, "adaptVideoCodecOnRemotePacketLoss", v.adaptVideoCodecOnRemotePacketLoss);
      Write(w, "isScreenshare", v.isScreenshare);
      Write(w, "defaultVideoRenderSurface", v.defaultVideoRenderSurface);
      Write(w, "videoMaxBitrateKbps", v.videoMaxBitrateKbps);
      Write(w, "videoTargetBitrateKbps", v.videoTargetBitrateKbps);
      Write(w, "videoStartBitrateKbps", v.videoStartBitrateKbps);
      Write(w, "videoMinBitrateKbps", v.videoMinBitrateKbps);
      Write(w, "rtcpEnabled", v.rtcpEnabled);
      Write(w, "rtcpMux", v.rtcpMux);
      Write(w, "useRandomPortsInIceCandidates", v.useRandomPortsInIceCandidates);
      Write(w, "mediaDscp", v.mediaDscp);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PeerConnection::PeerConnectionSettings& v)
    {
      Read(json, "sessionName", v.sessionName);
      Read(json, "certAor", v.certAor);
      Read(json, "natTraversalMode", v.natTraversalMode);
      Read(json, "secureMediaMode", v.secureMediaMode);
      Read(json, "secureMediaRequired", v.secureMediaRequired);
      Read(json, "natTraversalServerHostname", v.natTraversalServerHostname);
      Read(json, "natTraversalServerUsername", v.natTraversalServerUsername);
      Read(json, "natTraversalServerPassword", v.natTraversalServerPassword);
      Read(json, "natTraversalServerPort", v.natTraversalServerPort);
      Read(json, "natTraversalServerType", v.natTraversalServerType);
      Read(json, "localInterface", v.localInterface);
      Read(json, "forcedHostCandidate", v.forcedHostCandidate);
      Read(json, "useUnixDomainSockets", v.useUnixDomainSockets);
      Read(json, "mixId", v.mixId);
      Read(json, "mixContribution", v.mixContribution);
      Read(json, "startPaused", v.startPaused);
      Read(json, "videoCaptureDeviceId", v.videoCaptureDeviceId);
      Read(json, "adaptVideoCodecOnRemotePacketLoss", v.adaptVideoCodecOnRemotePacketLoss);
      Read(json, "isScreenshare", v.isScreenshare);
      Read(json, "defaultVideoRenderSurface", v.defaultVideoRenderSurface);
      Read(json, "videoMaxBitrateKbps", v.videoMaxBitrateKbps);
      Read(json, "videoTargetBitrateKbps", v.videoTargetBitrateKbps);
      Read(json, "videoStartBitrateKbps", v.videoStartBitrateKbps);
      Read(json, "videoMinBitrateKbps", v.videoMinBitrateKbps);
      Read(json, "rtcpEnabled", v.rtcpEnabled);
      Read(json, "rtcpMux", v.rtcpMux);
      Read(json, "useRandomPortsInIceCandidates", v.useRandomPortsInIceCandidates);
      Read(json, "mediaDscp", v.mediaDscp);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::PeerConnection::SessionDescription& v)
    {
      w.StartObject();
      Write(w, "sdpString", v.sdpString);
      Write(w, "sdpLen", v.sdpLen);
      Write(w, "sdpType", v.sdpType);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::PeerConnection::SessionDescription& v)
    {
      Read(json, "sdpString", v.sdpString);
      Read(json, "sdpLen", v.sdpLen);
      Read(json, "sdpType", v.sdpType);
    }
  }
}
#pragma warning(pop)
#endif // PeerConnection_JSON_HELPER_H