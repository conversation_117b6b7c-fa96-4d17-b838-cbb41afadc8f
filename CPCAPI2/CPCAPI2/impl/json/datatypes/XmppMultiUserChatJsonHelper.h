// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef XmppMultiUserChat_JSON_HELPER_H
#define XmppMultiUserChat_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/public/xmpp/XmppMultiUserChatManager.h>
#include <interface/public/xmpp/XmppMultiUserChatHandler.h>
#include <interface/public/xmpp/XmppMultiUserChatState.h>
#include <interface/experimental/xmpp/XmppMultiUserChatJsonProxyStateHandler.h>
#include <impl/xmpp/XmppMultiUserChatHandlerInternal.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::RoomConfig& v)
    {
      w.StartObject();
      Write(w, "isInstant", v.isInstant);
      Write(w, "createIfNotExisting", v.createIfNotExisting);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::RoomConfig& v)
    {
      Read(json, "isInstant", v.isInstant);
      Read(json, "createIfNotExisting", v.createIfNotExisting);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::XmppMultiUserChatRoomState& v)
    {
      w.StartObject();
      Write(w, "isCreatedBySelf", v.isCreatedBySelf);
      Write(w, "isReady", v.isReady);
      Write(w, "subject", v.subject);
      Write(w, "name", v.name);
      Write(w, "description", v.description);
      Write(w, "creation", v.creation);
      Write(w, "isPublic", v.isPublic);
      Write(w, "isPasswordProtected", v.isPasswordProtected);
      Write(w, "isOpen", v.isOpen);
      Write(w, "isModerated", v.isModerated);
      Write(w, "isPersistent", v.isPersistent);
      Write(w, "isRecorded", v.isRecorded);
      Write(w, "anonymousMode", v.anonymousMode);
      Write(w, "numOfParticipants", v.numOfParticipants);
      Write(w, "owners", v.owners);
      Write(w, "maxHistoryFetch", v.maxHistoryFetch);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::XmppMultiUserChatRoomState& v)
    {
      Read(json, "isCreatedBySelf", v.isCreatedBySelf);
      Read(json, "isReady", v.isReady);
      Read(json, "subject", v.subject);
      Read(json, "name", v.name);
      Read(json, "description", v.description);
      Read(json, "creation", v.creation);
      Read(json, "isPublic", v.isPublic);
      Read(json, "isPasswordProtected", v.isPasswordProtected);
      Read(json, "isOpen", v.isOpen);
      Read(json, "isModerated", v.isModerated);
      Read(json, "isPersistent", v.isPersistent);
      Read(json, "isRecorded", v.isRecorded);
      Read(json, "anonymousMode", v.anonymousMode);
      Read(json, "numOfParticipants", v.numOfParticipants);
      Read(json, "owners", v.owners);
      Read(json, "maxHistoryFetch", v.maxHistoryFetch);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHistoryItem& v)
    {
      w.StartObject();
      Write(w, "from", v.from);
      Write(w, "plain", v.plain);
      Write(w, "html", v.html);
      Write(w, "timestamp", v.timestamp);
      Write(w, "millisecond", v.millisecond);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHistoryItem& v)
    {
      Read(json, "from", v.from);
      Read(json, "plain", v.plain);
      Read(json, "html", v.html);
      Read(json, "timestamp", v.timestamp);
      Read(json, "millisecond", v.millisecond);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::XmppMultiUserChatConfigurationItem& v)
    {
      w.StartObject();
      Write(w, "type", v.type);
      Write(w, "name", v.name);
      Write(w, "required", v.required);
      Write(w, "label", v.label);
      Write(w, "values", v.values);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::XmppMultiUserChatConfigurationItem& v)
    {
      Read(json, "type", v.type);
      Read(json, "name", v.name);
      Read(json, "required", v.required);
      Read(json, "label", v.label);
      Read(json, "values", v.values);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::XmppMultiUserChatConfigurations& v)
    {
      w.StartObject();
      Write(w, "type", v.type);
      Write(w, "instructions", v.instructions);
      Write(w, "title", v.title);
      Write(w, "items", v.items);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::XmppMultiUserChatConfigurations& v)
    {
      Read(json, "type", v.type);
      Read(json, "instructions", v.instructions);
      Read(json, "title", v.title);
      Read(json, "items", v.items);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::XmppMultiUserChatConfigurationsListItem& v)
    {
      w.StartObject();
      Write(w, "jid", v.jid);
      Write(w, "nick", v.nick);
      Write(w, "affiliation", v.affiliation);
      Write(w, "role", v.role);
      Write(w, "reason", v.reason);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::XmppMultiUserChatConfigurationsListItem& v)
    {
      Read(json, "jid", v.jid);
      Read(json, "nick", v.nick);
      Read(json, "affiliation", v.affiliation);
      Read(json, "role", v.role);
      Read(json, "reason", v.reason);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::RoomBookmark& v)
    {
      w.StartObject();
      Write(w, "name", v.name);
      Write(w, "jid", v.jid);
      Write(w, "nickname", v.nickname);
      Write(w, "password", v.password);
      Write(w, "autojoin", v.autojoin);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::RoomBookmark& v)
    {
      Read(json, "name", v.name);
      Read(json, "jid", v.jid);
      Read(json, "nickname", v.nickname);
      Read(json, "password", v.password);
      Read(json, "autojoin", v.autojoin);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::ServiceAvailabilityEvent& v)
    {
      w.StartObject();
      Write(w, "available", v.available);
      Write(w, "service", v.service);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::ServiceAvailabilityEvent& v)
    {
      Read(json, "available", v.available);
      Read(json, "service", v.service);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::RoomListItem& v)
    {
      w.StartObject();
      Write(w, "jid", v.jid);
      Write(w, "name", v.name);
      Write(w, "node", v.node);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::RoomListItem& v)
    {
      Read(json, "jid", v.jid);
      Read(json, "name", v.name);
      Read(json, "node", v.node);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::RoomListRetrievedEvent& v)
    {
      w.StartObject();
      Write(w, "rooms", v.rooms);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::RoomListRetrievedEvent& v)
    {
      Read(json, "rooms", v.rooms);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::ParticipantState& v)
    {
      w.StartObject();
      Write(w, "nickname", v.nickname);
      Write(w, "jid", v.jid);
      Write(w, "presence", v.presence);
      Write(w, "message", v.message);
      Write(w, "affiliation", v.affiliation);
      Write(w, "role", v.role);
      Write(w, "isBanned", v.isBanned);
      Write(w, "isKicked", v.isKicked);
      Write(w, "isRemoved", v.isRemoved);
      Write(w, "isRoomDestroyed", v.isRoomDestroyed);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::ParticipantState& v)
    {
      Read(json, "nickname", v.nickname);
      Read(json, "jid", v.jid);
      Read(json, "presence", v.presence);
      Read(json, "message", v.message);
      Read(json, "affiliation", v.affiliation);
      Read(json, "role", v.role);
      Read(json, "isBanned", v.isBanned);
      Read(json, "isKicked", v.isKicked);
      Read(json, "isRemoved", v.isRemoved);
      Read(json, "isRoomDestroyed", v.isRoomDestroyed);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::ParticipantAddedEvent& v)
    {
      w.StartObject();
      Write(w, "nickname", v.nickname);
      Write(w, "state", v.state);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::ParticipantAddedEvent& v)
    {
      Read(json, "nickname", v.nickname);
      Read(json, "state", v.state);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::ParticipantRemovedEvent& v)
    {
      w.StartObject();
      Write(w, "nickname", v.nickname);
      Write(w, "jid", v.jid);
      Write(w, "reason", v.reason);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::ParticipantRemovedEvent& v)
    {
      Read(json, "nickname", v.nickname);
      Read(json, "jid", v.jid);
      Read(json, "reason", v.reason);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::ParticipantUpdatedEvent& v)
    {
      w.StartObject();
      Write(w, "nickname", v.nickname);
      Write(w, "reason", v.reason);
      Write(w, "state", v.state);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::ParticipantUpdatedEvent& v)
    {
      Read(json, "nickname", v.nickname);
      Read(json, "reason", v.reason);
      Read(json, "state", v.state);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::ParticipantSelfUpdatedEvent& v)
    {
      w.StartObject();
      Write(w, "jid", v.jid);
      Write(w, "reason", v.reason);
      Write(w, "state", v.state);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::ParticipantSelfUpdatedEvent& v)
    {
      Read(json, "jid", v.jid);
      Read(json, "reason", v.reason);
      Read(json, "state", v.state);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::ParticipantChatStateEvent& v)
    {
      w.StartObject();
      Write(w, "nickname", v.nickname);
      Write(w, "jid", v.jid);
      Write(w, "state", v.state);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::ParticipantChatStateEvent& v)
    {
      Read(json, "nickname", v.nickname);
      Read(json, "jid", v.jid);
      Read(json, "state", v.state);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::LocalUserLeftEvent& v)
    {
      w.StartObject();
      Write(w, "reason", v.reason);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::LocalUserLeftEvent& v)
    {
      Read(json, "reason", v.reason);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::MultiUserChatReadyEvent& v)
    {
      w.StartObject();
      Write(w, "features", v.features);
      Write(w, "room", v.room);
      Write(w, "roomjid", v.roomjid);
      Write(w, "isNewRoom", v.isNewRoom);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::MultiUserChatReadyEvent& v)
    {
      Read(json, "features", v.features);
      Read(json, "room", v.room);
      Read(json, "roomjid", v.roomjid);
      Read(json, "isNewRoom", v.isNewRoom);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::MultiUserChatConfigurationRequestedEvent& v)
    {
      w.StartObject();
      Write(w, "dataform", v.dataform);
      Write(w, "configurations", v.configurations);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::MultiUserChatConfigurationRequestedEvent& v)
    {
      Read(json, "dataform", v.dataform);
      Read(json, "configurations", v.configurations);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::MultiUserChatListRequestedEvent& v)
    {
      w.StartObject();
      Write(w, "type", v.type);
      Write(w, "items", v.items);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::MultiUserChatListRequestedEvent& v)
    {
      Read(json, "type", v.type);
      Read(json, "items", v.items);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::MultiUserChatSubjectChangedEvent& v)
    {
      w.StartObject();
      Write(w, "nickname", v.nickname);
      Write(w, "subject", v.subject);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::MultiUserChatSubjectChangedEvent& v)
    {
      Read(json, "nickname", v.nickname);
      Read(json, "subject", v.subject);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::MultiUserChatNewMessageEvent& v)
    {
      w.StartObject();
      Write(w, "message", v.message);
      Write(w, "messageId", v.messageId);
      Write(w, "stanzaId", v.stanzaId);
      Write(w, "originId", v.originId);
      Write(w, "nickname", v.nickname);
      Write(w, "jid", v.jid);
      Write(w, "plain", v.plain);
      Write(w, "html", v.html);
      Write(w, "timestamp", v.timestamp);
      Write(w, "millisecond", v.millisecond);
      Write(w, "isPrivate", v.isPrivate);
      Write(w, "isDelayedDelivery", v.isDelayedDelivery);
      Write(w, "replaces", v.replaces);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::MultiUserChatNewMessageEvent& v)
    {
      Read(json, "message", v.message);
      Read(json, "messageId", v.messageId);
      Read(json, "stanzaId", v.stanzaId);
      Read(json, "originId", v.originId);
      Read(json, "nickname", v.nickname);
      Read(json, "jid", v.jid);
      Read(json, "plain", v.plain);
      Read(json, "html", v.html);
      Read(json, "timestamp", v.timestamp);
      Read(json, "millisecond", v.millisecond);
      Read(json, "isPrivate", v.isPrivate);
      Read(json, "isDelayedDelivery", v.isDelayedDelivery);
      Read(json, "replaces", v.replaces);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::MultiUserChatNewReactionEvent& v)
    {
      w.StartObject();
      Write(w, "message", v.message);
      Write(w, "messageId", v.messageId);
      Write(w, "nickname", v.nickname);
      Write(w, "jid", v.jid);
      Write(w, "target", v.target);
      Write(w, "reactions", v.reactions);
      Write(w, "timestamp", v.timestamp);
      Write(w, "millisecond", v.millisecond);
      Write(w, "isPrivate", v.isPrivate);
      Write(w, "isDelayedDelivery", v.isDelayedDelivery);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::MultiUserChatNewReactionEvent& v)
    {
      Read(json, "message", v.message);
      Read(json, "messageId", v.messageId);
      Read(json, "nickname", v.nickname);
      Read(json, "jid", v.jid);
      Read(json, "target", v.target);
      Read(json, "reactions", v.reactions);
      Read(json, "timestamp", v.timestamp);
      Read(json, "millisecond", v.millisecond);
      Read(json, "isPrivate", v.isPrivate);
      Read(json, "isDelayedDelivery", v.isDelayedDelivery);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::MultiUserChatNewMessageRetractionEvent& v)
    {
      w.StartObject();
      Write(w, "message", v.message);
      Write(w, "messageId", v.messageId);
      Write(w, "nickname", v.nickname);
      Write(w, "jid", v.jid);
      Write(w, "target", v.target);
      Write(w, "timestamp", v.timestamp);
      Write(w, "millisecond", v.millisecond);
      Write(w, "isPrivate", v.isPrivate);
      Write(w, "isDelayedDelivery", v.isDelayedDelivery);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::MultiUserChatNewMessageRetractionEvent& v)
    {
      Read(json, "message", v.message);
      Read(json, "messageId", v.messageId);
      Read(json, "nickname", v.nickname);
      Read(json, "jid", v.jid);
      Read(json, "target", v.target);
      Read(json, "timestamp", v.timestamp);
      Read(json, "millisecond", v.millisecond);
      Read(json, "isPrivate", v.isPrivate);
      Read(json, "isDelayedDelivery", v.isDelayedDelivery);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::SendMessageSuccessEvent& v)
    {
      w.StartObject();
      Write(w, "message", v.message);
      Write(w, "messageId", v.messageId);
      Write(w, "replaces", v.replaces);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::SendMessageSuccessEvent& v)
    {
      Read(json, "message", v.message);
      Read(json, "messageId", v.messageId);
      Read(json, "replaces", v.replaces);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::SendMessageFailureEvent& v)
    {
      w.StartObject();
      Write(w, "message", v.message);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::SendMessageFailureEvent& v)
    {
      Read(json, "message", v.message);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::MultiUserChatInvitationReceivedEvent& v)
    {
      w.StartObject();
      Write(w, "room", v.room);
      Write(w, "roomjid", v.roomjid);
      Write(w, "jid", v.jid);
      Write(w, "reason", v.reason);
      Write(w, "password", v.password);
      Write(w, "timestamp", v.timestamp);
      Write(w, "millisecond", v.millisecond);
      Write(w, "isDelayedDelivery", v.isDelayedDelivery);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::MultiUserChatInvitationReceivedEvent& v)
    {
      Read(json, "room", v.room);
      Read(json, "roomjid", v.roomjid);
      Read(json, "jid", v.jid);
      Read(json, "reason", v.reason);
      Read(json, "password", v.password);
      Read(json, "timestamp", v.timestamp);
      Read(json, "millisecond", v.millisecond);
      Read(json, "isDelayedDelivery", v.isDelayedDelivery);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::MultiUserChatInvitationDeclinedEvent& v)
    {
      w.StartObject();
      Write(w, "jid", v.jid);
      Write(w, "reason", v.reason);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::MultiUserChatInvitationDeclinedEvent& v)
    {
      Read(json, "jid", v.jid);
      Read(json, "reason", v.reason);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::MultiUserChatErrorEvent& v)
    {
      w.StartObject();
      Write(w, "type", v.type);
      Write(w, "error", v.error);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::MultiUserChatErrorEvent& v)
    {
      Read(json, "type", v.type);
      Read(json, "error", v.error);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::MultiUserChatRoomStateChangedEvent& v)
    {
      w.StartObject();
      Write(w, "state", v.state);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::MultiUserChatRoomStateChangedEvent& v)
    {
      Read(json, "state", v.state);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::RoomBookmarksReceivedEvent& v)
    {
      w.StartObject();
      Write(w, "bookmarks", v.bookmarks);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::RoomBookmarksReceivedEvent& v)
    {
      Read(json, "bookmarks", v.bookmarks);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::NewRoomEvent& v)
    {
      w.StartObject();
      Write(w, "hAccount", v.hAccount);
      Write(w, "roomjid", v.roomjid);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::NewRoomEvent& v)
    {
      Read(json, "hAccount", v.hAccount);
      Read(json, "roomjid", v.roomjid);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::MessageDeliveredEvent& v)
    {
      w.StartObject();
      Write(w, "messageId", v.messageId);
      Write(w, "from", v.from);
      Write(w, "timestamp", v.timestamp);
      Write(w, "millisecond", v.millisecond);
      Write(w, "isDelayedDelivery", v.isDelayedDelivery);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::MessageDeliveredEvent& v)
    {
      Read(json, "messageId", v.messageId);
      Read(json, "from", v.from);
      Read(json, "timestamp", v.timestamp);
      Read(json, "millisecond", v.millisecond);
      Read(json, "isDelayedDelivery", v.isDelayedDelivery);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::MessageReadEvent& v)
    {
      w.StartObject();
      Write(w, "messageId", v.messageId);
      Write(w, "from", v.from);
      Write(w, "timestamp", v.timestamp);
      Write(w, "millisecond", v.millisecond);
      Write(w, "isDelayedDelivery", v.isDelayedDelivery);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::MessageReadEvent& v)
    {
      Read(json, "messageId", v.messageId);
      Read(json, "from", v.from);
      Read(json, "timestamp", v.timestamp);
      Read(json, "millisecond", v.millisecond);
      Read(json, "isDelayedDelivery", v.isDelayedDelivery);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::XmppMultiUserChatManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::XmppMultiUserChatManager& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::XmppMultiUserChatState& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      Write(w, "services", v.services);
      Write(w, "room", v.room);
      Write(w, "participants", v.participants);
      Write(w, "self", v.self);
      Write(w, "dataform", v.dataform);
      Write(w, "configurations", v.configurations);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::XmppMultiUserChatState& v)
    {
      Read(json, "account", v.account);
      Read(json, "services", v.services);
      Read(json, "room", v.room);
      Read(json, "participants", v.participants);
      Read(json, "self", v.self);
      Read(json, "dataform", v.dataform);
      Read(json, "configurations", v.configurations);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::XmppMultiUserChatStateInfo& v)
    {
      w.StartObject();
      Write(w, "muc", v.muc);
      Write(w, "state", v.state);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::XmppMultiUserChatStateInfo& v)
    {
      Read(json, "muc", v.muc);
      Read(json, "state", v.state);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::XmppMultiUserChatStateManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::XmppMultiUserChatStateManager& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::JsonProxyMultiUserChatStateEvent& v)
    {
      w.StartObject();
      Write(w, "states", v.states);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::JsonProxyMultiUserChatStateEvent& v)
    {
      Read(json, "states", v.states);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::XmppMultiUserChatJsonProxyStateHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::XmppMultiUserChatJsonProxyStateHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::XmppMultiUserChatCreatedResultEvent& v)
    {
      w.StartObject();
      Write(w, "muc", v.muc);
      Write(w, "account", v.account);
      Write(w, "room", v.room);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::XmppMultiUserChatCreatedResultEvent& v)
    {
      Read(json, "muc", v.muc);
      Read(json, "account", v.account);
      Read(json, "room", v.room);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandlerInternal& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandlerInternal& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // XmppMultiUserChat_JSON_HELPER_H