#pragma once

#if !defined(__CPCAPI2_CPIM_HEADER_H__)
#define __CPCAPI2_CPIM_HEADER_H__

#include <cpcstl/string.h>
#include <rutil/Data.hxx>

namespace CPCAPI2
{
namespace CPM
{

class CpimHeader
{
public:
   static const cpc::string FROM;
   static const cpc::string TO;
   static const cpc::string DATETIME;
   static const cpc::string CONTENT_TYPE;
   static const cpc::string NS;
   static const cpc::string IMDN_MESSAGE_ID;
   static const cpc::string IMDN_DISPOSITION_NOTIFICATION;
   static const cpc::string CONTENT_DISPOSITION;

   CpimHeader() {}
   CpimHeader(const cpc::string& name, const cpc::string& value);

   cpc::string getName() const { return name; }
   cpc::string getValue() const { return value; }
   resip::Data toBytes() const;

   static CpimHeader parse(const resip::Data& data);

private:
   cpc::string name;
   cpc::string value;

   static const resip::Data NAME_VALUE_DELIM;
};

}
}

#endif // __CPCAPI2_CPIM_HEADER_H__