#include "interface/experimental/jsonrpc/StringSerialization.h"
#include "interface/experimental/jsonrpc/OptionalSerialization.h"
#include "interface/experimental/jsonrpc/VectorSerialization.h"
#include "../datatypes/AudioAudioDeviceRole.h"

#ifndef JSONRPC_CPCAPI2_AUDIO_IAUDIO_H
#define JSONRPC_CPCAPI2_AUDIO_IAUDIO_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
class IAudio
{
public:
    virtual void queryDeviceList(const int64_t phoneHandle) = 0;

	virtual void setCaptureDevice(const int64_t phoneHandle, const int64_t deviceId, const AudioAudioDeviceRole role) = 0;

	virtual void setRenderDevice(const int64_t phoneHandle, const int64_t deviceId, const AudioAudioDeviceRole role) = 0;

	virtual int64_t playSound(const int64_t phoneHandle, const AudioAudioDeviceRole role, const cpc::string& resourceUri, const bool repeat) = 0;

	virtual void stopPlaySound(const int64_t phoneHandle, const int64_t sound) = 0;

	virtual void queryCodecList(const int64_t phoneHandle) = 0;

	virtual void setCodecEnabled(const int64_t phoneHandle, const int64_t codecId, const bool enabled) = 0;

	virtual void setCodecPriority(const int64_t phoneHandle, const int64_t codecId, const int64_t priority) = 0;

	virtual void setCodecPayloadType(const int64_t phoneHandle, const int64_t codecId, const int64_t payloadType) = 0;

	virtual void setTelephoneEventPayloadType(const int64_t phoneHandle, const int64_t payloadType) = 0;

	virtual void setMicMute(const int64_t phoneHandle, const bool enabled) = 0;

	virtual void setSpeakerMute(const int64_t phoneHandle, const bool enabled) = 0;

	virtual void setMicVolume(const int64_t phoneHandle, const int64_t level) = 0;

	virtual void setSpeakerVolume(const int64_t phoneHandle, const int64_t level) = 0;

	virtual void queryDeviceVolume(const int64_t phoneHandle) = 0;

	virtual void startMonitoringCaptureDeviceLevels(const int64_t phoneHandle, const int64_t deviceId) = 0;

	virtual void stopMonitoringRenderDeviceLevels(const int64_t phoneHandle) = 0;
}; // class Audio
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_IAUDIO_H
