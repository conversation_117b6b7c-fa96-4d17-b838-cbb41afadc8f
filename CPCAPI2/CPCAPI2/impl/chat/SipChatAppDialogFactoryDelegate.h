#pragma once

#if !defined(__CPCAPI2_SIP_CHAT_APP_DIALOG_FACTORY_DELEGATE_H__)
#define __CPCAPI2_SIP_CHAT_APP_DIALOG_FACTORY_DELEGATE_H__

#include "../account/AppDialogFactoryDelegate.h"

namespace CPCAPI2
{
namespace SipChat
{
   class SipChatManagerImpl;

   class SipChatAppDialogFactoryDelegate : public CPCAPI2::SipAccount::AppDialogFactoryDelegate
   {
   public:
      SipChatAppDialogFactoryDelegate(SipChatManagerImpl* parent);
      virtual ~SipChatAppDialogFactoryDelegate();

      virtual bool isMyMessage(const resip::SipMessage& msg);
      virtual resip::AppDialogSet* createAppDialogSet(resip::DialogUsageManager& dum, const resip::SipMessage& msg);

   private:
      SipChatManagerImpl *parent;
   };

}
}

#endif // __CPCAPI2_SIP_CHAT_APP_DIALOG_FACTORY_DELEGATE_H__