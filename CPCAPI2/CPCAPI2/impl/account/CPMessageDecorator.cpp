#include "CPMessageDecorator.h"
#include "SipAccountInterface.h"
#include <resip/stack/SipMessage.hxx>
#include <resip/stack/ExtensionHeader.hxx>
#include <rutil/IpSynth.hxx>
#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_ACCOUNT

namespace CPCAPI2
{

namespace SipAccount
{

CPMessageDecorator::CPMessageDecorator(CPCAPI2::Phone* phone, CPCAPI2::SipAccount::SipAccountHandle account) :
mHost(""),
mPort(0),
mUse<PERSON>lias(false),
mOverrideContact(false),
mDns64prefixlen(0),
mHandle(account),
mPhone(phone)
{
   StackLog(<< "PMessageDecorator::CPMessageDecorator(): " << this);
   memset(&mDns64prefix, 0, sizeof(sockaddr_in6));
}

CPMessageDecorator::CPMessageDecorator(CPCAPI2::Phone* phone, CPCAPI2::SipAccount::SipAccountHandle account, const resip::Data& host, const int port, const bool useAlias, const bool overrideContact) :
mHost(host),
mPort(port),
mUseAlias(useAlias),
mOverrideContact(overrideContact),
mDns64prefixlen(0),
mHandle(account),
mPhone(phone)
{
   StackLog(<< "PMessageDecorator::CPMessageDecorator(): " << this);
   memset(&mDns64prefix, 0, sizeof(sockaddr_in6));
}

CPMessageDecorator::~CPMessageDecorator()
{
   StackLog(<< "PMessageDecorator::~CPMessageDecorator(): destroying " << this);
   reset();
   mPhone = NULL;
   mHandle = 0;
   mBindingReleaseDecorator.reset();
}

SipAccountImpl* CPMessageDecorator::getAccount()
{
   SipAccountImpl* account = NULL;
   SipAccountInterface* managerIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(mPhone));
   if (managerIf)
   {
      account = managerIf->getAccountImpl(mHandle);
   }

   assert(account);
   if (!account)
   {
      WarningLog(<< "CPMessageDecorator::getAccount(): " << this << " null account");
   }
   return account;
}

void CPMessageDecorator::reset()
{
   StackLog(<< "CPMessageDecorator::reset(): " << this);
   mHost = resip::Data();
   mPort = 0;
   mUseAlias = false;
   mOverrideContact = false;
   mDns64prefixlen = 0;
   memset(&mDns64prefix, 0, sizeof(sockaddr_in6));

   // Persist the members that are specific to the profile decorator. Maintain the cleanup binding decorator so that
   // the stale network can be released during network change handling, the cleanup status flags should get reset
   // once the message has been decorated with the expired header, or if the account is re-enabled.
   //
   // mAccount = 0;
   // mBindingReleaseDecorator.reset();
}

void CPMessageDecorator::decorateMessage(resip::SipMessage &msg, const resip::Tuple &source, const resip::Tuple &destination, const resip::Data& sigcompId)
{
   // StackLog(<< "CPMessageDecorator::decorateMessage(): " << this << " account: " << mHandle << " binding-release-decorator: " << mBindingReleaseDecorator << " message: " << msg);
   if (msg.isRequest())
   {
      // .ph. GENBAND specific
      if (mUseAlias && !msg.header(resip::h_Vias).empty())
      {
         // Get the top most Via: header
         resip::Vias viaHeaders = msg.header(resip::h_Vias);
         resip::Via viaHeader = viaHeaders.front();

         // Add alias parameter to top Via header
         viaHeader.param(resip::p_alias);

         // Set the top most Via: with the modified Via header
         viaHeaders.pop_front();
         viaHeaders.push_front(viaHeader);
         msg.header(resip::h_Vias) = viaHeaders;
      }

      if (mBindingReleaseDecorator)
      {
         mBindingReleaseDecorator->decorateMessage(msg);
      }
   }

   resip::ExtensionHeader h_XConnectivityProbe("X-Connectivity-Probe");
   if (msg.method() == resip::OPTIONS &&
      msg.exists(h_XConnectivityProbe))
   {
      SipAccountImpl* account = getAccount();
      if (account)
      {
         account->fireMessageDecoratedEvent(msg, source, destination);
      }
      return;
   }

/*
   // !jjg! TODO: this code went missing, and now we don't properly update our Via/Contact 
   // with STUN-discovered IP/port?
   if (!host.empty() && !msg.header(resip::h_Vias).empty())
   {
      // Get the top most Via: header
      resip::Vias viaHeaders = msg.header(resip::h_Vias);
      resip::Via viaHeader = viaHeaders.front();

      // Replace the sent-by host and port with the public ones
      viaHeader.sentHost() = host;
      viaHeader.sentPort() = port;

      // Set the top most Via: with the modified Via header
      viaHeaders.pop_front();
      viaHeaders.push_front(viaHeader);
      msg.header(resip::h_Vias) = viaHeaders;
   }

   if (overrideContact)
   {
      if (msg.exists(resip::h_Contacts) && !msg.header(resip::h_Contacts).empty())
      {
         msg.header(resip::h_Contacts).front().uri().host() = host;
         msg.header(resip::h_Contacts).front().uri().port() = port;
      }
   }
*/
   decorateMessageNat64(msg, source, destination, sigcompId);
}

void CPMessageDecorator::decorateMessageNat64(resip::SipMessage &msg, const resip::Tuple &source, const resip::Tuple &destination, const resip::Data& sigcompId)
{
   SipAccountImpl* account = getAccount();
   if (!account)
   {
      return;
   }

   if (destination.ipVersion() == resip::V4 && source.ipVersion() == resip::V4)
   {
      account->fireMessageDecoratedEvent(msg, source, destination);
      return;
   }

   if (mDns64prefixlen > 0 && destination.ipVersion() == resip::V6 && source.ipVersion() == resip::V6)
   {
      if (!resip::IpSynthTools::isSynthesizedIPv6Address(mDns64prefix, mDns64prefixlen, destination))
      {
         account->fireMessageDecoratedEvent(msg, source, destination);
         return;
      }
   }

   // at this point, we have verified we should be sending to an IPv4 server over NAT64

   if (msg.method() == resip::REGISTER && msg.isRequest())
   {
      // only update the Contact header for REGISTER requests. All other Contact header updates should be done
      // in SipAccountImpl via setOverrideHostAndPort
      if (mOverrideContact)
      {
         if (msg.exists(resip::h_Contacts) && !msg.header(resip::h_Contacts).empty())
         {
            msg.header(resip::h_Contacts).front().uri().host() = mHost;
            msg.header(resip::h_Contacts).front().uri().port() = mPort;
         }
      }
   }

   if (msg.isRequest())
   {
      if (!mHost.empty() && !msg.header(resip::h_Vias).empty())
      {
         // Get the top most Via: header
         resip::Vias viaHeaders = msg.header(resip::h_Vias);
         resip::Via viaHeader = viaHeaders.front();

         // Replace the sent-by host and port with the public ones
         viaHeader.sentHost() = mHost;
         viaHeader.sentPort() = mPort;

         // Set the top most Via: with the modified Via header
         viaHeaders.pop_front();
         viaHeaders.push_front(viaHeader);
         msg.header(resip::h_Vias) = viaHeaders;
      }
   }
   else
   {
      // patch the received parameter we may have added -- it likely is
      // the synthesized IPv6 address of the IPv4 SIP server
      if (!msg.header(resip::h_Vias).empty())
      {
         // Get the top most Via: header
         resip::Vias viaHeaders = msg.header(resip::h_Vias);
         resip::Via viaHeader = viaHeaders.front();

         if (viaHeader.exists(resip::p_received))
         {
            if (resip::DnsUtil::isIpV6Address(viaHeader.param(resip::p_received)))
            {
               bool receivedPatched = false;
               resip::Tuple inAddr = resip::Tuple(viaHeader.param(resip::p_received), 0, resip::V6);
               if (resip::IpSynthTools::isSynthesizedIPv6Address(mDns64prefix, mDns64prefixlen, inAddr))
               {
                  resip::Tuple outAddr;
                  if (resip::IpSynthTools::unSynthesizeIPv6Address(mDns64prefix, mDns64prefixlen, inAddr, outAddr))
                  {
                     viaHeader.param(resip::p_received) = resip::Tuple::inet_ntop(outAddr);
                     viaHeaders.pop_front();
                     viaHeaders.push_front(viaHeader);
                     msg.header(resip::h_Vias) = viaHeaders;
                     receivedPatched = true;
                  }
               }

               if (!receivedPatched)
               {
                  // last ditch effort; replace the IPv6 address with any IPv4 address,
                  // to prevent potential issues with IPv4 only servers encountering an IPv6 literal
                  viaHeader.param(resip::p_received) = resip::Tuple::inet_ntop(resip::Tuple("***********", 5060, resip::V4));
                  viaHeaders.pop_front();
                  viaHeaders.push_front(viaHeader);
                  msg.header(resip::h_Vias) = viaHeaders;
               }
            }
         }
      }
   }

   account->fireMessageDecoratedEvent(msg, source, destination);
}

void CPMessageDecorator::rollbackMessage(resip::SipMessage& msg)
{
}

resip::MessageDecorator* CPMessageDecorator::clone() const
{
   return new CPMessageDecorator(*this);
}

const resip::Data& CPMessageDecorator::getHost() const
{
   return mHost;
}

int CPMessageDecorator::getPort() const
{
   return mPort;
}

void CPMessageDecorator::setUseAlias(bool enable)
{
   mUseAlias = enable;
}

void CPMessageDecorator::setOverrideContact(bool overrideC)
{
   mOverrideContact = overrideC;
}

void CPMessageDecorator::setHostPort(const resip::Data& host, const int port)
{
   mHost = host;
   mPort = port;
}

void CPMessageDecorator::setDns64Prefix(const sockaddr_in6& prefix, int prefix_len)
{
   mDns64prefix = prefix;
   mDns64prefixlen = prefix_len;
}

std::shared_ptr<CPCAPI2::SipAccount::CPBindingReleaseDecorator> CPMessageDecorator::getBindingReleaseDecorator()
{
   return mBindingReleaseDecorator;
}

int CPMessageDecorator::onRegistrationSuccess(const SipRegistrationSuccessEvent& args)
{
   DebugLog(<< "CPMessageDecorator::onRegistrationSuccess(): " << this << " account: " << mHandle << " local-contact: " << args.localContact.uri().toString().c_str() << " overrideSourceIp: " << args.overrideSourceIpSignalling);
   if (mBindingReleaseDecorator)
   {
      mBindingReleaseDecorator->onRegistrationSuccess(args);
   }
   return kSuccess;
}

int CPMessageDecorator::onRegistrationDialogSuccess(const SipRegistrationDialogSuccessEvent& args)
{
   DebugLog(<< "CPMessageDecorator::onRegistrationDialogSuccess(): " << this << " account: " << mHandle);
   if (mBindingReleaseDecorator)
   {
      mBindingReleaseDecorator->onRegistrationDialogSuccess(args);
   }
   return kSuccess;
}

int CPMessageDecorator::onAccountNetworkChangeInitiated(const SipNetworkChangeInitiatedEvent& args)
{
   if (mBindingReleaseDecorator)
   {
      DebugLog(<< "CPMessageDecorator::onAccountNetworkChangeInitiated(): " << this << " account: " << mHandle << " release bindings activated - changing to network: " << args.newTransport << " disable-triggered: " << args.accountDisableTriggeredForNetworkChange);
      mBindingReleaseDecorator->setActivateCleanup(true);
   }
   return kSuccess;
}

void CPMessageDecorator::release()
{
   if (mBindingReleaseDecorator)
   {
      StackLog(<< "CPMessageDecorator::release(): " << this << " account: " << mHandle << " release bindings reset");
      mBindingReleaseDecorator->resetBindingList();
   }
}

bool CPMessageDecorator::canDisable(const AccountDisableCondition& cond)
{
   if (mBindingReleaseDecorator)
   {
      StackLog(<< "CPMessageDecorator::canDisable(): " << this << " account: " << mHandle << " release bindings reset");
   }
   return true;
}

void CPMessageDecorator::enableReleaseBindings(bool enable)
{
   if (enable)
   {
      // mBindingReleaseDecorator.reset(new CPBindingReleaseDecorator(mPhone, mHandle));
   }
   else
   {
      // mBindingReleaseDecorator.reset();
   }
}

CPBindingReleaseDecorator::CPBindingReleaseDecorator(CPCAPI2::Phone* phone, CPCAPI2::SipAccount::SipAccountHandle account) :
mCleanupActive(false),
mPhone(phone),
mHandle(account)
{
}

CPBindingReleaseDecorator::~CPBindingReleaseDecorator()
{
}

void CPBindingReleaseDecorator::onRegistrationSuccess(const SipRegistrationSuccessEvent& args)
{
   resetBindingList();
   addToReleaseBindingList(args.localContact);
}

void CPBindingReleaseDecorator::onRegistrationDialogSuccess(const SipRegistrationDialogSuccessEvent& args)
{
   if (args.response.isResponse() && (args.response.header(resip::h_StatusLine).responseCode() == 200))
   {
      // The reset binding list is also triggered here so as to handle scenarios where the registration
      // flow has not fully completed to trigger the onRegistrationSuccess, but a registration transaction
      // has actually been successful, e.g. with rport enabled, three registration transactions would have
      // completed before the onRegistrationSuccess would be triggered. But the released bindings only
      // need to be decorated once, and not in each of the rport registration requests.
      resetBindingList();
   }
}

void CPBindingReleaseDecorator::resetBindingList()
{
   DebugLog(<< "CPBindingReleaseDecorator::resetBindingList(): " << this << " account: " << mHandle << " before reset - cleanup-active: " << mCleanupActive << " binding-list: " << mCleanupBindingList.size());
   mCleanupActive = false;
   mCleanupBindingList.clear();
}

void CPBindingReleaseDecorator::setActivateCleanup(bool activate)
{
   mCleanupActive = activate;
}

void CPBindingReleaseDecorator::decorateMessage(resip::SipMessage& msg)
{
   if (msg.isRequest())
   {
      if (msg.method() == resip::REGISTER)
      {
         StackLog(<< "CPBindingReleaseDecorator::decorateMessage(): " << this << " account: " << mHandle << " cleanup-active: " << mCleanupActive);
         if (mCleanupActive)
         {
            StackLog(<< "CPBindingReleaseDecorator::decorateMessage(): " << this << " account: " << mHandle << " cleanup-active: " << mCleanupActive);
            releaseBindings(msg);
         }
      }
   }
}

void CPBindingReleaseDecorator::addToReleaseBindingList(const resip::NameAddr& localContact)
{
   // Support to release bindings is only supported for contacts that include the rinstance param
   const resip::Uri& localUri = localContact.uri();
   if (localUri.exists(resip::p_rinstance))
   {
      bool found = false;
      for (resip::NameAddrs::iterator i = mCleanupBindingList.begin(); i != mCleanupBindingList.end(); ++i)
      {
         const resip::Uri& uri = i->uri();
         if (uri == localUri)
         {
            // Cleanup binding list is only populated if the binding had a rinstance and transport parameter
            if (uri.param(resip::p_rinstance).c_str() == localUri.param(resip::p_rinstance).c_str())
            {
               if (uri.exists(resip::p_transport) && localUri.exists(resip::p_transport))
               {
                  if (uri.param(resip::p_transport) == localUri.param(resip::p_transport))
                  {
                     found = true;
                  }
               }
               else
               {
                  // Set to true as we cannot compare transports
                  found = true;
                  break;
               }
            }
         }
      }

      if (found)
      {
         DebugLog(<< "CPBindingReleaseDecorator::addToReleaseBindingList(): " << this << " binding: " << localContact.uri().toString().c_str() << " already exists in cleanup bindings list");
      }
      else
      {
         DebugLog(<< "CPBindingReleaseDecorator::addToReleaseBindingList(): " << this << " adding binding: " << localContact.uri().toString().c_str() << " to cleanup bindings list");
         mCleanupBindingList.push_back(localContact);
      }
   }
   else
   {
      DebugLog(<< "CPBindingReleaseDecorator::addToReleaseBindingList(): " << this << " account: " << mHandle << " ignoring binding: " << localContact.uri().toString().c_str() << " as rinstance param param was not found");
   }
}

void CPBindingReleaseDecorator::releaseBindings(resip::SipMessage& msg)
{
   resip::NameAddrs& bindingList = msg.header(resip::h_Contacts);
   resip::NameAddrs expiredBindings;

   for (resip::NameAddrs::iterator i = mCleanupBindingList.begin(); i != mCleanupBindingList.end(); ++i)
   {
      const resip::Uri& cleanupUri = i->uri();
      bool found = false;
      for (resip::NameAddrs::iterator j = bindingList.begin(); j != bindingList.end(); ++j)
      {
         const resip::Uri& bindingUri = j->uri();

         // Check if the expires host:port that we want to release is not already part of the binding list, e.g.
         //  - a host:port that is being released by account impl or by the stack
         //  - a host:port that is still valid or being used in the new registration
         //
         // We do not need to check rinstance here, as we do not want to release a binding with the same host
         // and port regardless of rinstance.

         // StackLog(<< "CPBindingReleaseDecorator::releaseBindings(): " << this << " cleanup-binding: " << cleanupUri.toString().c_str() << " binding: " << bindingUri.toString().c_str() << " cleanup-host: " << cleanupUri.host().c_str() << " cleanup-port: " << cleanupUri.port() << " binding-host: " << bindingUri.host().c_str() << " binding-port: " << bindingUri.port());

         if ((cleanupUri.host() == bindingUri.host()) && (cleanupUri.port() == bindingUri.port()))
         {
            found = true;
            if (cleanupUri.exists(resip::p_transport) && bindingUri.exists(resip::p_transport))
            {
               if (cleanupUri.param(resip::p_transport) != bindingUri.param(resip::p_transport))
               {
                  found = false;
               }
            }
         }
      }

      if (found)
      {
         DebugLog(<< "CPBindingReleaseDecorator::releaseBindings(): " << this << " not adding expired parameter as binding: " << i->uri().toString().c_str() << " is part of new binding list");
      }
      else
      {
         // The released bindings are always pushed behind the primary binding being registered to avoid
         // any compatibility issues with CPE/CPE2 as we were informed that the registration binding is
         // pulled from the front of the contact binding list.
         DebugLog(<< "CPBindingReleaseDecorator::releaseBindings(): " << this << " adding expired parameter for binding: " << i->uri().toString().c_str());
         i->param(resip::p_expires) = 0;
         bindingList.push_back(*i);
         expiredBindings.push_back(*i);
      }
   }

   if (expiredBindings.size() > 0)
   {
      SipAccountMessageDecoratedBindingReleasedEvent args;
      for (resip::NameAddrs::iterator i = bindingList.begin(); i != bindingList.end(); ++i)
      {
         args.bindings.push_back(i->uri().toString().c_str());
      }
      for (resip::NameAddrs::iterator i = mCleanupBindingList.begin(); i != mCleanupBindingList.end(); ++i)
      {
         args.releasedBindings.push_back(i->uri().toString().c_str());
      }

      SipAccountImpl* account = NULL;
      SipAccountInterface* managerIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(mPhone));
      if (managerIf)
      {
         account = managerIf->getAccountImpl(mHandle);
         assert(account);
         if (account)
         {
            account->fireReleaseBindingsEvent(args);
         }
         else
         {
            WarningLog(<< "CPBindingReleaseDecorator::releaseBindings(): " << this << " null account");
         }
      }
   }
}

}

}
