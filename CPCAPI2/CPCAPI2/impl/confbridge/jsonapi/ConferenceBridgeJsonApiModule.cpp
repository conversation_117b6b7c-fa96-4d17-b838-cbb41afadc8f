#include "brand_branded.h"

#include <interface/experimental/confbridge/ConferenceBridgeJsonProxy.h>
#include <interface/experimental/confbridge/ConferenceBridgeJsonApi.h>

#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "phone/PhoneInterface.h"
#include "ConferenceBridgeJsonServerInterface.h"
#endif
#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_JSON_PROXY_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "phone/PhoneInterface.h"
#include "ConferenceBridgeJsonProxyInterface.h"
#endif

namespace CPCAPI2
{
   namespace ConferenceBridge
   {
      ConferenceBridgeJsonApi* ConferenceBridgeJsonApi::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<ConferenceBridgeJsonServerInterface>(phone, "ConferenceBridgeJsonApi");
#else
         return NULL;
#endif
      }

      ConferenceBridgeManagerJsonProxy* ConferenceBridgeManagerJsonProxy::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_JSON_PROXY_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<ConferenceBridgeJsonProxyInterface>(phone, "ConferenceBridgeManagerJsonProxy");
#else
         return NULL;
#endif
      }
   }
}
