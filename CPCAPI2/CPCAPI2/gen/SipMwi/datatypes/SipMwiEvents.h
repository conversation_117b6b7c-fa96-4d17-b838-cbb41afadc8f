#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPMWI_SIPMWIEVENTS_H
#define JSONRPC_CPCAPI2_SIPMWI_SIPMWIEVENTS_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipMwi {
  enum class SipMwiEvents
  {
    SipMwiDotOnIncomingMwiStatus
  };

  inline std::string to_string(SipMwiEvents value)
  {
    switch (value)
    {
      case SipMwiEvents::SipMwiDotOnIncomingMwiStatus:
        return "SipMwi.onIncomingMWIStatus";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, SipMwiEvents value)
  {
    switch (value)
    {
      case SipMwiEvents::SipMwiDotOnIncomingMwiStatus:
        os << "SipMwi.onIncomingMWIStatus";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const SipMwiEvents& e)
  {
    switch (e)
    {
      case SipMwiEvents::SipMwiDotOnIncomingMwiStatus:
        j = "SipMwi.onIncomingMWIStatus";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, SipMwiEvents& e)
  {
      if (0 == j.get<std::string>().compare("SipMwi.onIncomingMWIStatus"))
    {
      e = SipMwiEvents::SipMwiDotOnIncomingMwiStatus;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum SipMwiEvents"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipMwi
#endif // JSONRPC_CPCAPI2_SIPMWI_SIPMWIEVENTS_H
