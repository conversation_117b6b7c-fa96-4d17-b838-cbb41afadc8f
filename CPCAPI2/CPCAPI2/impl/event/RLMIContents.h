#if !defined(CPCAPI2_RLMI_CONTENTS)
#define CPCAPI2_RLMI_CONTENTS 

#include "resip/stack/Contents.hxx"
#include "resip/dum/SubscriptionState.hxx"
#include "event/SipEventSubscriptionHandler.h"
#include <libxml/xmlreader.h>

namespace CPCAPI2
{
namespace SipEvent
{
class RLMIContents : public resip::Contents
{
public:
   RLMIContents(void);
   RLMIContents(const RLMIContents& rhs);
   virtual ~RLMIContents(void);

   static bool init();

   RLMIContents(const resip::HeaderFieldValue& hfv, const resip::Mime& contentType);
   RLMIContents& operator=(const RLMIContents& rhs);
   virtual Contents* clone() const;
   static const resip::Mime& getStaticType();
   virtual std::ostream& encodeParsed(std::ostream& str) const;
   virtual void parse(resip::ParseBuffer& pb);

   typedef std::pair<cpc::string,cpc::string> Name;
   typedef cpc::vector<Name> Names;

   static void parseName(xmlTextReaderPtr reader, Name* pName);

   UInt32 version(void) const;
   UInt32& version(void);

   enum StateType
   {
      full = 0,
      partial
   };

   StateType stateType(void) const;
   StateType& stateType(void);

   const resip::Uri& entity(void) const;
   resip::Uri& entity(void);

   class Resource
   {
   public:
      Resource(void) {}
      Resource(const Resource& rhs);
      Resource(const resip::Uri& uri);
      virtual ~Resource(void) {}

      class Instance
      {
      public:
         Instance(void) {}
         Instance(const Instance& rhs);
         Instance(
            const cpc::string& id, 
            resip::SubscriptionState state, 
            resip::TerminateReason = resip::Deactivated, 
            const cpc::string& contentId = "");

         virtual ~Instance(void) {}

         void parse(xmlTextReaderPtr reader);

         static resip::SubscriptionState convertToSubscriptionState(const cpc::string& str);

         const cpc::string& getId() const { return mId; }
         const cpc::string& getContentId() const { return mContentId; }
         const resip::SubscriptionState getSubscriptionState() const { return mSubscriptionState; }
         const cpc::string& getTerminateReason() const { return mTerminateReason; }

      private:
         cpc::string mId;
         resip::SubscriptionState mSubscriptionState;
         cpc::string mTerminateReason;
         cpc::string mContentId;
      };

      const resip::Uri& uri() const { return mUri; }
      resip::Uri& uri() { return mUri; }

      const Names& names() const { return mNames; }
      Names& names() { return mNames; }

      const cpc::vector<Instance>& instances() const { return mInstances; }
      cpc::vector<Instance>& instances() { return mInstances; }

      void parse(xmlTextReaderPtr reader);

   private:
      resip::Uri mUri;
      cpc::vector<Name> mNames;
      cpc::vector<Instance> mInstances;
   };

   typedef cpc::vector<Resource::Instance> Instances;
   typedef cpc::vector<Resource> Resources;

   const Resources& getResources() const;
   const Names& getNames() const;

   static CPCAPI2::SipEvent::SipSubscriptionState instanceStateToSdk(resip::SubscriptionState state);

private:
   unsigned int mVersion;
   StateType mState;
   resip::Uri mEntity;
   cpc::vector<Name> mNames;
   cpc::vector<Resource> mResources;
   unsigned int mContentLength; // stupid that we have to store this ourselves!
};

}
}

#endif

