#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONTRANSFER_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONTRANSFER_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationTransfer
  {
  public:
    int32_t phoneHandle;
    int32_t transferTargetConversation;
    int32_t transfereeConversation;

    SipConversationTransfer() = default;

    SipConversationTransfer(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationTransfer& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["transferTargetConversation"] = o.transferTargetConversation;
     j["transfereeConversation"] = o.transfereeConversation;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationTransfer& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.transferTargetConversation = j.at("transferTargetConversation");
     o.transfereeConversation = j.at("transfereeConversation");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONTRANSFER_H
