#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_CHAT_MODULE==1 || CPCAPI2_BRAND_IM_MODULE==1 || CPCAPI2_BRAND_XMPP_CHAT_MODULE==1 || CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
#include "CpimMessage.h"
#include "CpmHelper.h"

#include <assert.h>
#include <sstream>

namespace CPCAPI2
{
namespace CPM
{

const resip::Data CpimMessage::CRLF = "\r\n";
const resip::Data CpimMessage::DOUBLE_CRLF = CRLF + CRLF;

CpimMessage::CpimMessage(const HeaderList& headers, const HeaderList& contentHeaders, const resip::Data& content)
   : headers(headers), contentHeaders(contentHeaders), content(content)
{
}

bool CpimMessage::hasHeader(const cpc::string& name) const
{
   for (HeaderList::const_iterator iter = headers.begin(); iter != headers.end(); iter++)
   {
      CpimHeader header = *iter;
      if (header.getName() == name)
      {
         return true;
      }
   }

   return false;
}

CpimHeader CpimMessage::getHeader(const cpc::string& name) const
{
   for (HeaderList::const_iterator iter = headers.begin(); iter != headers.end(); iter++)
   {
      CpimHeader header = *iter;
      if (header.getName() == name)
      {
         return header;
      }
   }

   return CpimHeader();
}

bool CpimMessage::hasContentHeader(const cpc::string& name) const
{
   for (HeaderList::const_iterator iter = contentHeaders.begin(); iter != contentHeaders.end(); iter++)
   {
      CpimHeader header = *iter;
      if (header.getName() == name)
      {
         return true;
      }
   }

   return false;
}

CpimHeader CpimMessage::getContentHeader(const cpc::string& name) const
{
   for (HeaderList::const_iterator iter = contentHeaders.begin(); iter != contentHeaders.end(); iter++)
   {
      CpimHeader header = *iter;
      if (header.getName() == name)
      {
         return header;
      }
   }

   return CpimHeader();
}

resip::Data CpimMessage::toBytes() const
{
   resip::Data data;

   // Write the message headers
   for (HeaderList::const_iterator iter = headers.begin(); iter != headers.end(); iter++)
   {
      CpimHeader header = *iter;
      data += header.toBytes();
      data += CRLF;
   }
   data += CRLF;

   // Write the MIME-encapsulated content headers
   for (HeaderList::const_iterator iter = contentHeaders.begin(); iter != contentHeaders.end(); iter++)
   {
      CpimHeader contentHeader = *iter;
      data += contentHeader.toBytes();
      data += CRLF;
   }
   data += CRLF;

   // Append the content to the headers
   data += content;

   return data;
}

CpimMessage CpimMessage::parse(const resip::Data& data)
{
   // Read message headers
   size_t begin = 0;
   size_t end = data.find(DOUBLE_CRLF, begin);
   assert(end > 0);
   resip::Data block1 = data.substr(begin, end);
   std::list<resip::Data> lines = CpmHelper::getLines(block1, CRLF);
   HeaderList headers;
   for (std::list<resip::Data>::iterator iter = lines.begin(); iter != lines.end(); iter++)
   {
      resip::Data line = *iter;
      CpimHeader header = CpimHeader::parse(line);
      headers.push_back(header);
   }

   // Read the MIME-encapsulated content headers
   begin = end + DOUBLE_CRLF.size();
   end = data.find(DOUBLE_CRLF, begin);
   assert(end > 0);
   resip::Data block2 = data.substr(begin, end - begin);
   lines = CpmHelper::getLines(block2, CRLF);
   HeaderList contentHeaders;
   for (std::list<resip::Data>::iterator iter = lines.begin(); iter != lines.end(); iter++)
   {
      resip::Data line = *iter;
      CpimHeader header = CpimHeader::parse(line);
      contentHeaders.push_back(header);
   }

   // Read the message content
   begin = end + DOUBLE_CRLF.size();
   resip::Data content = data.substr(begin);

   return CpimMessage(headers, contentHeaders, content);
}

}
}

#endif
