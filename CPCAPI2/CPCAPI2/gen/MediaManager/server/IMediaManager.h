#include "interface/experimental/jsonrpc/StringSerialization.h"
#include "interface/experimental/jsonrpc/OptionalSerialization.h"
#include "interface/experimental/jsonrpc/VectorSerialization.h"


#ifndef JSONRPC_CPCAPI2_MEDIAMANAGER_IMEDIAMANAGER_H
#define JSONRPC_CPCAPI2_MEDIAMANAGER_IMEDIAMANAGER_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace MediaManager {
class IMediaManager
{
public:
    virtual void initializeMediaStack(const int64_t phoneHandle) = 0;
}; // class MediaManager
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace MediaManager
#endif // JSONRPC_CPCAPI2_MEDIAMANAGER_IMEDIAMANAGER_H
