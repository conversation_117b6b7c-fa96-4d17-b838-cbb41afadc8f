#include <brand_branded.h>

#include <util/strettorpc/RPCIdentifier.h>

#include <util/MachineIdentification.h>
#include <util/PlatformUtils.h>

#include "BIDruidCheckRequest.h"
#include "BIEventsInternalTypes.h"
#include "BIMachineIdentification.h"

#if (CPCAPI2_BRAND_BIEVENTS_MODULE == 1)

using namespace CPCAPI2::BIEvents;

BIDruidCheckRequest::BIDruidCheckRequest(
   const std::string & sessionID,
   const std::string & authToken,
   const std::string & buildStamp,
   const std::string & strettoUserNameOrID,
   const std::string & druid )
   : CPCAPI2::RPCRequest( "druid_check", sessionID, authToken )
{
   RPCValue systemVal;
   systemVal.m_Type = RPCValueType_object;
   {
      // Determine the OS Name and Version string.
      std::string osValue;
      PlatformUtils::OSInfo osInfo;
      if( PlatformUtils::PlatformUtils::getOSInfo( osInfo ))
      {
         switch( osInfo.osType )
         {
         case PlatformUtils::OSType_Windows:
            osValue += "Windows ";
            break;
         case PlatformUtils::OSType_OSX:
            osValue += "OSX ";
            break;
         case PlatformUtils::OSType_Linux:
            osValue += "Linux ";
            break;
         case PlatformUtils::OSType_Android:
            osValue += "Android ";
            break;
         case PlatformUtils::OSType_iOS:
            osValue += "iOS ";
            break;
         case PlatformUtils::OSType_Other:
         default:
            break;
         }
         osValue += osInfo.osVersion;
      }

      systemVal.m_ObjectVal[ "OS"    ] = RPCValue( osValue );
      systemVal.m_ObjectVal[ "type"  ] = RPCValue( "APP" );
      systemVal.m_ObjectVal[ "build" ] = RPCValue( buildStamp );
      systemVal.m_ObjectVal[ "user"  ] = RPCValue( strettoUserNameOrID );

      // Add extra hardware IDs
      std::vector< std::pair< std::string, std::string > > hardwareIDs;
      MachineIdentification::GetHardwareIds( hardwareIDs );
      for( std::vector< std::pair< std::string, std::string > >::const_iterator iter = hardwareIDs.begin();
           iter != hardwareIDs.end() ; ++iter )
      {
         const std::pair< std::string, std::string >& item( *iter );
         systemVal.m_ObjectVal[ item.first ] = RPCValue( item.second );
      }
   }

   addParameter( RPCParam( "system", systemVal ));
   if( druid.size() > 0 )
      addParameter( RPCParam( "druid", druid ));
}

#endif