#pragma once

#if !defined(CPCAPI2_VIDEO_STREAM_IMPL_H)
#define CPCAPI2_VIDEO_STREAM_IMPL_H

#include "cpcapi2defs.h"
#include "audiotranscription/AudioTrans.h"

#include <rutil/Data.hxx>
#include <rutil/Reactor.hxx>

#include <map>
#include <memory>
#include <thread>
#include <atomic>

namespace CPCAPI2
{
class PhoneInterface;
namespace Media
{
class MediaManagerInterface;
}
namespace AudioTrans
{
class TranscriptionEncodedAudioObserver;
class AudioTransInterface;
class AudioTransImpl
{
public:
   AudioTransImpl(CPCAPI2::PhoneInterface* phone, AudioTransInterface* ccif, AudioTransHandle h);
   virtual ~AudioTransImpl();

   void setAudioTransSettings(const AudioTransSettings& settings);
   void startTranscription();
   void stopTranscription();
   void stopTranscriptionInternal();
   void restartTranscription();
   void post(resip::ReadCallbackBase* rcb);

   void HandleEncodedAudio(uint32_t timeStamp,
      const uint8_t* payloadData,
      size_t payloadSize);

private:
   void transcriptionResultsThread();

private:
   CPCAPI2::PhoneInterface* mPhone;
   CPCAPI2::Media::MediaManagerInterface* mMediaManager;
   AudioTransInterface* mInterface;
   AudioTransHandle mHandle;
   AudioTransSettings mSettings;
   std::unique_ptr<TranscriptionEncodedAudioObserver> mEncodedAudioObs;
   std::unique_ptr<std::thread> mTransResultsThread;
   std::atomic_bool mShouldReadTranscribeResults;
   int mCurrentResultSetId;
};
}
}

#endif // CPCAPI2_VIDEO_STREAM_IMPL_H

