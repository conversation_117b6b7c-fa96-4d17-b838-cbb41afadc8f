#pragma once

#ifndef __CPCAPI2_CP_MESSAGE_DECORATOR_H__
#define __CPCAPI2_CP_MESSAGE_DECORATOR_H__

#include <rutil/Socket.hxx>
#include <resip/stack/MessageDecorator.hxx>
#include <resip/stack/SipMessage.hxx>
#include <rutil/Data.hxx>
#include "SipAccountHandlerInternal.h"
#include "SipAccountAwareFeature.h"
#include "SipAccountImpl.h"

namespace CPCAPI2
{
   namespace SipAccount
   {

      class CPBindingReleaseDecorator;

      class CPMessageDecorator : public resip::MessageDecorator,
                                 public CPCAPI2::SipAccount::SipAccountAwareFeature
      {

      public:

         CPMessageDecorator(CPCAPI2::Phone* phone, CPCAPI2::SipAccount::SipAccountHandle account);
         CPMessageDecorator(CPCAPI2::Phone* phone, CPCAPI2::SipAccount::SipAccountHandle account, const resip::Data& host, const int port, const bool useAlias, const bool overrideContact);
         virtual~ CPMessageDecorator();

         const resip::Data& getHost() const;
         int getPort() const;
         void setUseAlias(bool enable);
         void setOverrideContact(bool overrideC);
         void setHostPort(const resip::Data& host, const int port);
         void setDns64Prefix(const sockaddr_in6& prefix, int prefix_len);

         void reset();

         /**
          * Enabling release bindings functionality in the CPMessageDecorator spawns a CPBindingReleaseDecorator object. Disabling
          * the release bindings functionality, results in the destruction of the CPBindingReleaseDecorator if one exists.
          *
          * If release bindings functionality is enabled, the CPBindingReleaseDecorator instance will then track all successful
          * registration bindings (via the onRegistrationSuccess callback). The binding cleanup is activated when registration
          * refreshes are triggered as would be the case in transport disconnects or network changes. The CPBindingReleaseDecorator
          * would add the release bindings to the contact list of the subsequent outgoing REGISTER messages, until the
          * registration transaction is successful (i.e. 200 OK).
         */
         void enableReleaseBindings(bool enable = true);
         void fireReleaseBindingsEvent(SipAccountMessageDecoratedBindingReleasedEvent& args);

         /**
          * A shared pointer is used here as the CPMessageDecorator may be cloned multiple times in the resip stack, yet we only
          * want to sustain a single CPBindingReleaseDecorator instance to manage the release bindings functionality.
         */
         std::shared_ptr<CPCAPI2::SipAccount::CPBindingReleaseDecorator> getBindingReleaseDecorator();

      private:

         // MessageDecorator
         virtual void decorateMessage(resip::SipMessage &msg, const resip::Tuple &source, const resip::Tuple &destination, const resip::Data& sigcompId) OVERRIDE;
         virtual void rollbackMessage(resip::SipMessage& msg) OVERRIDE;
         virtual resip::MessageDecorator* clone() const OVERRIDE;
         virtual bool copyToStackCancels() const OVERRIDE { return true; }
         virtual bool copyToStackFailureAcks() const OVERRIDE { return true; }

         // SipAccountAwareFeature
         virtual int adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile) OVERRIDE { return kSuccess; };
         virtual int addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig) OVERRIDE { return kSuccess; };
         virtual int onRegistrationSuccess(const SipRegistrationSuccessEvent& args) OVERRIDE;
         virtual int onRegistrationDialogSuccess(const SipRegistrationDialogSuccessEvent& args) OVERRIDE;
         virtual int onAccountNetworkChangeInitiated(const SipNetworkChangeInitiatedEvent& args) OVERRIDE;
         virtual int onDumBeingDestroyed() OVERRIDE { return kSuccess; };
         virtual void release() OVERRIDE;
         virtual bool canDisable(const AccountDisableCondition& cond) OVERRIDE;

         virtual void decorateMessageNat64(resip::SipMessage &msg, const resip::Tuple &source, const resip::Tuple &destination, const resip::Data& sigcompId);
         void updateDecoratorHandler(resip::SipMessage &msg, const resip::Tuple &source, const resip::Tuple &destination);

         SipAccountImpl* getAccount();

      private:

         CPMessageDecorator(); // Not implemented

         resip::Data mHost;
         int mPort;
         bool mUseAlias;
         bool mOverrideContact;
         sockaddr_in6 mDns64prefix;
         int mDns64prefixlen;

         CPCAPI2::Phone* mPhone;
         CPCAPI2::SipAccount::SipAccountHandle mHandle;
         std::shared_ptr<CPBindingReleaseDecorator> mBindingReleaseDecorator;

      };

      class CPBindingReleaseDecorator
      {

      public:

         CPBindingReleaseDecorator(CPCAPI2::Phone* phone, CPCAPI2::SipAccount::SipAccountHandle account);
         virtual~ CPBindingReleaseDecorator();

         void decorateMessage(resip::SipMessage &msg);
         void addToReleaseBindingList(const resip::NameAddr& localContact);
         void setActivateCleanup(bool activate);
         void resetBindingList();

         void onRegistrationSuccess(const SipRegistrationSuccessEvent& args);
         void onRegistrationDialogSuccess(const SipRegistrationDialogSuccessEvent& args);

      private:

         void releaseBindings(resip::SipMessage& msg);

         bool mCleanupActive;
         resip::NameAddrs mCleanupBindingList;
         CPCAPI2::Phone* mPhone;
         CPCAPI2::SipAccount::SipAccountHandle mHandle;

      };
   }
}

#endif // __CPCAPI2_CP_MESSAGE_DECORATOR_H__
