#pragma once

#if !defined(CPCAPI2_JSON_API_SERVER_SEND_TRANSPORT_INTERFACE_H)
#define CPCAPI2_JSON_API_SERVER_SEND_TRANSPORT_INTERFACE_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServerSendTransport.h"
#include "jsonapi/JsonApiServerSendTransportInternal.h"
#include "jsonapi/JsonApiServerModule.h"
#include "jsonapi/JsonApiClientHandler.h"

#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"
#include "../util/AutoTestProcessor.h"

#include <rutil/Fifo.hxx>

#include <map>

namespace CPCAPI2
{
class PhoneInterface;
class LocalLogger;

namespace JsonApi
{
class JsonApiServerWebSocket;
class JsonApiServerHTTP;
class JsonApiServerModule;
class JsonApiServerInterface;

class JsonApiServerSendTransportInterface : public JsonApiServerSendTransport,
                                            public JsonApiServerSendTransportInternal,
                                            public PhoneModule
{

public:

   JsonApiServerSendTransportInterface(Phone* phone);
   virtual ~JsonApiServerSendTransportInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerSendTransport
   virtual int setJsonApiServer(CPCAPI2::JsonApi::JsonApiServer* jsonApiServer) OVERRIDE;
   virtual int send(const CPCAPI2::Json::JsonDataPointer& eventData, bool suppressLog = false) OVERRIDE;

   // JsonApiServerSendTransportInternal
   virtual void setConnection(const JsonApiRequestInfo& jsonConnId) OVERRIDE;
   virtual int send(const rapidjson::Document& doc) OVERRIDE;
   virtual int send(const JsonApiRequestInfo& jsonConnId, const rapidjson::Document& doc) OVERRIDE;
   virtual int send(const JsonApiRequestInfo& jsonConnId, const CPCAPI2::Json::JsonDataPointer& eventData) OVERRIDE;
   virtual int send(JsonApiUserHandle jsonUser, const rapidjson::Document& doc) OVERRIDE;
   virtual int send(JsonApiUserHandle jsonUser, const CPCAPI2::Json::JsonDataPointer& eventData) OVERRIDE;
   virtual int validateAuthToken(const CPCAPI2::JsonApi::AuthToken& authToken, bool& isValid, std::map<resip::Data, resip::Data>* pubClaims) OVERRIDE;

   CPCAPI2::JsonApi::JsonApiServerInterface* getJsonApiServer() const {
      return mJsonApiServer;
   }
   
private:
   void setConnectionImpl(const JsonApiRequestInfo& jsonConnId);

private:
   CPCAPI2::PhoneInterface* mPhone;
   CPCAPI2::JsonApi::JsonApiServerInterface* mJsonApiServer;
   std::shared_ptr<JsonApiServerWebSocket> mWebsockTransport;
   std::shared_ptr<JsonApiServerHTTP> mHttpTransport;
   OutgoingJsonHandler* mOutgoingJsonHandler;
   typedef std::set<JsonApiRequestInfo> ConnSet;
   ConnSet mConns;
   LocalLogger* mLocalLogger;
};

}

}

#endif // CPCAPI2_JSON_API_SERVER_SEND_TRANSPORT_INTERFACE_H
