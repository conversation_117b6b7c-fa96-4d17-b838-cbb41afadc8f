#pragma once

#if !defined(CPCAPI2_CONFERENCE_HANDLE_FACTORY_H)
#define CPCAPI2_CONFERENCE_HANDLE_FACTORY_H

//includes
#include "cpcapi2defs.h"
#include "conference/SipConferenceTypes.h"

namespace CPCAPI2
{
namespace SipConference
{

class SipConferenceHandleFactory
{
public:
    static SipConferenceHandle getSipConferenceHandle() { return nextSipConferenceHandle++; }
	static void setSipConferenceHandle(SipConferenceHandle value) { nextSipConferenceHandle = value; }
private:
	static SipConferenceHandle nextSipConferenceHandle;
};

} // SipConference
} // CPCAPI2
#endif // CPCAPI2_CONFERENCE_HANDLE_FACTORY_H