#include "brand_branded.h"

#if (CPCAPI2_BRAND_AUTH_PROVIDER_MOCK_MODULE == 1)
#include "cpcapi2utils.h"
#include "AuthProviderInterface.h"
#include "AuthProviderMockInterface.h"
#include "../phone/PhoneModule.h"
#include "../phone/PhoneInterface.h"

#include "util/cpc_logger.h"
#include "util/HttpClient.h"
#include <rutil/Log.hxx>

#include <sstream>


namespace CPCAPI2
{
namespace Auth
{
   
void AuthProviderMockInterface::Release()
{
   delete this;
}

AuthProviderMockInterface::AuthProviderMockInterface(AuthProviderInterface* authProvider) :
   mAuthProviderInterface(*authProvider)
{
}

AuthProviderMockInterface::~AuthProviderMockInterface()
{
}

cpc::string AuthProviderMockInterface::providerName()
{
   return "mock";
}

int AuthProviderMockInterface::login(const cpc::string& username)
{
   return kSuccess;
}


}
}
#endif
