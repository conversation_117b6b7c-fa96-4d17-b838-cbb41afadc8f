#pragma once

#if !defined(CPCAPI2_CAPABILITY_DISCOVERY_INTERNAL_H)
#define CPCAPI2_CAPABILITY_DISCOVERY_INTERNAL_H

#include "cpcapi2defs.h"

#include "RcsCapabilityDiscoveryInterface.h"

#include "../account/SipAccountImpl.h"
#include "../account/SipAccountAwareFeature.h"

#include <resip/dum/OutOfDialogHandler.hxx>
#include <resip/dum/MasterProfile.hxx>
#include <resip/dum/DialogUsageManager.hxx>

namespace CPCAPI2
{
namespace RcsCapabilityDiscovery
{

class RcsCapabilityDiscoveryInternalHandler;

typedef unsigned int RcsCapabilityDiscoveryInternalHandle;

class RcsCapabilityDiscoveryInternal :
   public CPCAPI2::SipAccount::SipAccountAwareFeature,
   public resip::OutOfDialogHandler
{
public:
   RcsCapabilityDiscoveryInternal(CPCAPI2::SipAccount::SipAccountImpl& account);
   virtual ~RcsCapabilityDiscoveryInternal();

   int initialize();
   int setHandler(RcsCapabilityDiscoveryInternalHandler* handler);

   int sendCapabilityRequest(
         RcsCapabilityDiscoveryInternalHandle capability,
         cpc::string targetAddress,
         const RcsCapabilitySet& caps = RcsCapabilitySet());

   int acceptIncoming(
         RcsCapabilityDiscoveryInternalHandle capability,
         unsigned int statusCode = 200,
         const RcsCapabilitySet& caps = RcsCapabilitySet());

   int rejectIncoming(
         RcsCapabilityDiscoveryInternalHandle capability,
         unsigned int statusCode);

   // IAccountAware
   virtual int adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile) OVERRIDE;
   virtual int addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig) OVERRIDE;
   virtual int onDumBeingDestroyed() OVERRIDE;
   virtual void release() OVERRIDE;

   // Client Handlers
   virtual bool onSuccess(resip::ClientOutOfDialogReqHandle h, const resip::SipMessage& successResponse) OVERRIDE;
   virtual bool onFailure(resip::ClientOutOfDialogReqHandle h, const resip::SipMessage& errorResponse) OVERRIDE;

   // Server Handlers
   virtual bool onReceivedRequest(resip::ServerOutOfDialogReqHandle h, const resip::SipMessage& request) OVERRIDE;

private:
   RcsCapabilityDiscoveryInternalHandle getSdkHandleFrom(resip::Data transactionId);
   void fireError(RcsCapabilityDiscoveryInternalHandle capability, const cpc::string& errorText);

private:
   CPCAPI2::SipAccount::SipAccountImpl& mAccount;
   RcsCapabilityDiscoveryInternalHandler* mCapabilityDiscoveryHandler;
   std::map<RcsCapabilityDiscoveryInternalHandle, resip::ServerOutOfDialogReqHandle> mServerHandleMap;
   std::map<RcsCapabilityDiscoveryInternalHandle, resip::Data> mClientHandleMap;
   RcsCapabilityDiscoveryInternalHandle mNextHandle;
};

}
}
#endif // CPCAPI2_CAPABILITY_DISCOVERY_INTERNAL_H
