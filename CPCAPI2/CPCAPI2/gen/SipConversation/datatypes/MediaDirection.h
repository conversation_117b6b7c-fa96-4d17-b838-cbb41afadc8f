#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_MEDIADIRECTION_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_MEDIADIRECTION_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  enum class MediaDirection
  {
    None,
    SendReceive,
    SendOnly,
    ReceiveOnly,
    Inactive
  };

  inline std::string to_string(MediaDirection value)
  {
    switch (value)
    {
      case MediaDirection::None:
        return "None";
      case MediaDirection::SendReceive:
        return "SendReceive";
      case MediaDirection::SendOnly:
        return "SendOnly";
      case MediaDirection::ReceiveOnly:
        return "ReceiveOnly";
      case MediaDirection::Inactive:
        return "Inactive";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, MediaDirection value)
  {
    switch (value)
    {
      case MediaDirection::None:
        os << "None";
        break;
      case MediaDirection::SendReceive:
        os << "SendReceive";
        break;
      case MediaDirection::SendOnly:
        os << "SendOnly";
        break;
      case MediaDirection::ReceiveOnly:
        os << "ReceiveOnly";
        break;
      case MediaDirection::Inactive:
        os << "Inactive";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const MediaDirection& e)
  {
    switch (e)
    {
      case MediaDirection::None:
        j = "None";
        break;
      case MediaDirection::SendReceive:
        j = "SendReceive";
        break;
      case MediaDirection::SendOnly:
        j = "SendOnly";
        break;
      case MediaDirection::ReceiveOnly:
        j = "ReceiveOnly";
        break;
      case MediaDirection::Inactive:
        j = "Inactive";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, MediaDirection& e)
  {
      if (0 == j.get<std::string>().compare("None"))
    {
      e = MediaDirection::None;
    }
    else if (0 == j.get<std::string>().compare("SendReceive"))
    {
      e = MediaDirection::SendReceive;
    }
    else if (0 == j.get<std::string>().compare("SendOnly"))
    {
      e = MediaDirection::SendOnly;
    }
    else if (0 == j.get<std::string>().compare("ReceiveOnly"))
    {
      e = MediaDirection::ReceiveOnly;
    }
    else if (0 == j.get<std::string>().compare("Inactive"))
    {
      e = MediaDirection::Inactive;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum MediaDirection"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_MEDIADIRECTION_H
