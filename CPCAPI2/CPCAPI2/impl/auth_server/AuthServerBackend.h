#pragma once

#if !defined(CPCAPI2_AUTH_SERVER_BACKEND)
#define CPCAPI2_AUTH_SERVER_BACKEND

#include <cpcstl/string.h>
#include <memory>

namespace CPCAPI2
{
namespace AuthServer
{
class AuthServerBackend
{
public:
   AuthServerBackend() {}
   virtual ~AuthServerBackend() {}

   virtual int initialize(const cpc::string& dbfile) = 0;
   virtual int addUser(const cpc::string& username, const cpc::string& password_hash) = 0;
   virtual int updatePassword(const cpc::string& username, const cpc::string& new_password_hash) = 0;
   virtual int queryUser(const cpc::string& username, const cpc::string& password_hash, bool& userExists, cpc::string& storedPassword) = 0;
   virtual int flushUsers() = 0;

};
}
}

#endif // CPCAPI2_AUTH_SERVER_BACKEND
