#include "brand_branded.h"

#if (CPCAPI2_BRAND_CONFERENCE_MODULE == 1)
#define MULTIPART_BOUNDARY "boundary1"
#include "SipConferenceManagerImpl.h"
#include "SipConferenceHandlerImpl.h"

using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;

namespace CPCAPI2
{
namespace SipConference
{

SipConferenceHandle SipConferenceHandleFactory::nextSipConferenceHandle = 1;

SipConferenceManagerImpl::SipConferenceManagerImpl(Phone* phone): 
	mPhone(dynamic_cast<PhoneInterface*>(phone))
{
	mAccountIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(phone));
	mSipEventIf = dynamic_cast<SipEventManagerInterface*>(SipEventManager::getInterface(phone));
	mConversationIf = dynamic_cast<SipAVConversationManagerInterface*>(SipAVConversationManagerInterface::getInterface(phone));

	isFocus = false;
	isConferenceAware = false;

	localAudioPort = 20000;
    localVideoPort = 20002;
}

void SipConferenceManagerImpl::Release()
{
	delete this;
}

int SipConferenceManagerImpl::setHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipConferenceHandler* handler)
{
	mAccountIf->postToSdkThread(resip::resip_bind(&SipConferenceManagerImpl::setHandlerImpl, this, account, handler));
    return kSuccess;
}

int SipConferenceManagerImpl::setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipConferenceHandler* handler)
{
   	AccountMap::iterator it = mAccountMap.find(account);
	SipConferenceHandlerImpl* evtMan = (it == mAccountMap.end() ? NULL : it->second);
	SipAccountImpl* acct = mAccountIf->getAccountImpl(account);

	if (!acct)
	{
		mAccountIf->fireError("Invalid account handle for SipConferenceManagerImpl::setHandler");
		return kError;
	}

	if (acct->isEnabled())
	{
		mAccountIf->fireError("SipConferenceManagerImpl::setHandler was called after account enabled: " + cpc::to_string(account));
	}
	else
	{
		if (evtMan == NULL)
		{
			evtMan = new SipConferenceHandlerImpl(*acct, *mSipEventIf, *this);
			mConversationIf->addSdkObserver(evtMan);
			mAccountMap[account] = evtMan;
		}
		evtMan->setSubscriptionHandler(handler);
		mSipEventIf->setHandler(account, CONFERENCE_PACKAGE, evtMan);
	}
	return kSuccess;
}

int SipConferenceManagerImpl::setDefaultPolicy(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipConference::SipConferencePolicy& policy)
{
	mAccountIf->postToSdkThread(resip::resip_bind(&SipConferenceManagerImpl::setDefaultPolicyImpl, this, account, policy));
	return kSuccess;
}

int SipConferenceManagerImpl::setDefaultPolicyImpl(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipConference::SipConferencePolicy& policy)
{
	AccountPolicyMap::iterator it = mAccountPolicyMap.find(account);
	if(it != mAccountPolicyMap.end())
	{
		mAccountPolicyMap.at(account) = policy;
	}
	else
	{
		mAccountPolicyMap.insert(std::pair<SipAccountHandle, SipConferencePolicy>(account, policy));
	}
	confPolicy = policy;
	return kSuccess;
}

SipConferenceHandle SipConferenceManagerImpl::createConference(CPCAPI2::SipAccount::SipAccountHandle account)
{
	SipConferenceHandle conference = SipConferenceHandleFactory::getSipConferenceHandle();
	mAccountIf->postToSdkThread(resip::resip_bind(&SipConferenceManagerImpl::createConferenceImpl, this, account, conference));
	return conference;
}

int SipConferenceManagerImpl::createConferenceImpl(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipConference::SipConferenceHandle conference)
{
	SipConferenceInfo* conferenceInfo = new SipConferenceInfo();

	conferenceInfo->version = 1;
	conferenceInfo->focusUri = generateUniqueConferenceUri(account);
	conferenceInfo->expirationTime = 3600; //seconds
	conferenceInfo->conferenceStatus = SipConferenceStatus::Full;
	conferenceInfo->conferenceDescription.displayText = "";
	conferenceInfo->conferenceDescription.freeText = "";
	conferenceInfo->conferenceDescription.subject = "";
	conferenceInfo->conferenceDescription.keywords = ""; 
	AccountPolicyMap::iterator it = mAccountPolicyMap.find(account);
	if(it != mAccountPolicyMap.end())
	{
		conferenceInfo->conferenceDescription.maxUserCount = it->second.maxUserCount;
	}
	else
	{
		conferenceInfo->conferenceDescription.maxUserCount = DEFAULT_MAX_USER_COUNT;
	}
	conferenceInfo->hostInfo.displayText = mAccountIf->getAccountImpl(account)->getSettings().displayName;
	conferenceInfo->hostInfo.uris.push_back(getUri(account));
	conferenceInfo->conferenceState.active = true;
	conferenceInfo->conferenceState.locked = false;
	conferenceInfo->conferenceState.userCount = 0;
	
	mConferenceMap.insert(std::pair<CPCAPI2::SipConference::SipConferenceHandle, CPCAPI2::SipConference::SipConferenceInfo*>(conference, conferenceInfo));
	mConfAccountMap.insert(std::pair<CPCAPI2::SipConference::SipConferenceHandle, SipAccount::SipAccountHandle>(conference, account));

	isFocus = true;
	isConferenceAware = true;
	return kSuccess;
}

int SipConferenceManagerImpl::setPolicy(SipConferenceHandle conference, const CPCAPI2::SipConference::SipConferencePolicy& policy)
{
	mAccountIf->postToSdkThread(resip::resip_bind(&SipConferenceManagerImpl::setPolicyImpl, this, conference, policy));
	return kSuccess;
}

int SipConferenceManagerImpl::setPolicyImpl(SipConferenceHandle conference, const CPCAPI2::SipConference::SipConferencePolicy& policy)
{
	ConferencePolicyMap::iterator it = mConferencePolicyMap.find(conference);
	if(it != mConferencePolicyMap.end())
	{
		mConferencePolicyMap.at(conference) = policy;
	}
	else
	{
		mConferencePolicyMap.insert(std::pair<SipConferenceHandle, SipConferencePolicy>(conference, policy));
	}
	confPolicy = policy;
	return kSuccess;
}

int SipConferenceManagerImpl::addParticipant(SipConferenceHandle conference, const cpc::string& targetAddress)
{
	mAccountIf->postToSdkThread(resip::resip_bind(&SipConferenceManagerImpl::addParticipantImpl, this, conference, targetAddress));
	return kSuccess;
}

int SipConferenceManagerImpl::addParticipantImpl(SipConferenceHandle conference, const cpc::string& targetAddress)
{
	
	SipConferenceParticipant participant;
	ConferenceMap::iterator conferenceIt = mConferenceMap.find(conference);
	if(conferenceIt == mConferenceMap.end())
	{
		mAccountIf->fireError("ERROR: Invalid conference handle for SipConferenceManagerImpl::addParticipantImpl.");
		return kSuccess;
	}

	if(conferenceIt->second->conferenceState.userCount >= confPolicy.maxUserCount)
	{
		mAccountIf->fireError("ERROR: Maximum conference participant count reached. SipConferenceManagerImpl::addParticipantImpl.");
		return kSuccess;
	}

	for(unsigned int i=0;i<confPolicy.notAllowedParticipants.size();i++)
	{
		if(strcmp(targetAddress, confPolicy.notAllowedParticipants[i]) == 0)
		{
			mAccountMap[mConfAccountMap[conference]]->onParticipationNotAllowed(conference, targetAddress);
			mAccountIf->fireError("ERROR: User not allowed to participate in conference. SipConferenceManagerImpl::addParticipantImpl.");

			return kSuccess;
		}
	}

	for(unsigned int i=0;i<confPolicy.needApprovalParticipants.size();i++)
	{
		if(strcmp(targetAddress, confPolicy.needApprovalParticipants[i]) == 0)
		{
			mAccountMap[mConfAccountMap[conference]]->onParticipationApproval(conference, targetAddress);
			return kSuccess;
		}
	}

	participant.uri = targetAddress;
	participant.status = SipConferenceStatus::Full;
	participant.displayText = ""; //name
	participant.roles = "participant";
	mConferenceMap[conference]->participants.push_back(participant);
	return kSuccess;
}

int SipConferenceManagerImpl::configureMedia(SipConferenceHandle conference, const SipConferenceMedia& mediaDescriptor)
{
	mAccountIf->postToSdkThread(resip::resip_bind(&SipConferenceManagerImpl::configureMediaImpl, this, conference, mediaDescriptor));
	return kSuccess;
}

int SipConferenceManagerImpl::configureMediaImpl(SipConferenceHandle conference, const SipConferenceMedia& mediaDescriptor)
{
	ConferenceMap::iterator it = mConferenceMap.find(conference);
	if(it != mConferenceMap.end())
	{
		it->second->conferenceDescription.availableMedia.push_back(mediaDescriptor);
	}
	else
	{
		mAccountIf->fireError("ERROR: Invalid conference handle for SipConferenceManagerImpl::configureMediaImpl.");
		return kSuccess;
	}

	return kSuccess;
}

int SipConferenceManagerImpl::start(SipConferenceHandle conference)
{
	mAccountIf->postToSdkThread(resip::resip_bind(&SipConferenceManagerImpl::startImpl, this, conference));
	return kSuccess;
}

int SipConferenceManagerImpl::startImpl(SipConferenceHandle conference)
{
	// TODO: Start comunication (call,chat, both...) with user defined by target address. That way we can probably get info (name...) about user.
	// TODO: Make coversion functions XML to SipConferenceInfo and other way around

	// fill INVITE header
	// send INVITE message using resip functions.
	// implement onNewConference or something like that. Process the response from Conference Factory
	// Implement notify function for sending notifications to subscribers
	// Implement onIncomingEventState or something like that for receiving notifications from Conference factory
	
	
	return kSuccess;
}

int SipConferenceManagerImpl::end(SipConferenceHandle conference)
{
	return kSuccess;
}

int SipConferenceManagerImpl::reject(SipConferenceHandle conference, unsigned int rejectReason)
{
	return kSuccess;
}

int SipConferenceManagerImpl::accept(SipConferenceHandle conference)
{
	return kSuccess;
}

int SipConferenceManagerImpl::getFocusUri(SipConferenceHandle conference, cpc::string &uri)
{
	ConferenceMap::iterator it = mConferenceMap.find(conference);
	if(it != mConferenceMap.end())
	{
		uri = it->second->focusUri;
		return kSuccess;
	}
	return kError;
}

int SipConferenceManagerImpl::removeParticipant(SipConferenceHandle conference, const cpc::string& targetAddress)
{
	return kSuccess;
}

int SipConferenceManagerImpl::fetchConferenceInfo(SipConferenceHandle conference, const cpc::string& targetAddress)
{
	return kSuccess;
}

int SipConferenceManagerImpl::notify(SipConferenceHandle conference, const SipConferenceInfo& state)
{
	return kSuccess;
}

int SipConferenceManagerImpl::setSubject(cpc::string subject)
{
	return kSuccess;
}

int SipConferenceManagerImpl::delegateFocus(const cpc::string& targetAddress)
{
	return kSuccess;
}

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// server based (hosted) conference interface
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

SipConferenceHandle SipConferenceManagerImpl::createServerConference(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& confFactoryAddress)
{
	SipConferenceHandle conference = SipConferenceHandleFactory::getSipConferenceHandle();
	mAccountIf->postToSdkThread(resip::resip_bind(&SipConferenceManagerImpl::createServerConferenceImpl, this, account, conference, confFactoryAddress));
	return conference;
}

int SipConferenceManagerImpl::createServerConferenceImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipConferenceHandle conference, const cpc::string& confFactoryAddress)
{
	createConferenceImpl(account, conference);
	mConferenceMap[conference]->focusUri = confFactoryAddress;
	return kSuccess;
}

int SipConferenceManagerImpl::startServerConf(SipConferenceHandle conference)
{
	mAccountIf->postToSdkThread(resip::resip_bind(&SipConferenceManagerImpl::startServerConfImpl, this, conference));
	return kSuccess;
}

int SipConferenceManagerImpl::startServerConfImpl(SipConferenceHandle conference)
{

	ConferenceAccountMap::iterator confAccountIt = mConfAccountMap.find(conference);
	if(confAccountIt == mConfAccountMap.end())
	{
		mAccountIf->fireError("ERROR: Invalid conference handle for SipConferenceManagerImpl::startServerConfImpl.");
		return kSuccess;
	}

	ConferenceMap::iterator confIt = mConferenceMap.find(conference);
	if(confIt == mConferenceMap.end())
	{
		mAccountIf->fireError("ERROR: Invalid conference handle for SipConferenceManagerImpl::startServerConfImpl.");
		return kSuccess;
	}

	SipConversationHandle conversationHandle = mConversationIf->createConversation(confAccountIt->second);
	mConversationIf->addParticipant(conversationHandle, confIt->second->focusUri);
	mConversationIf->start(conversationHandle);
	mConfConversationMap.insert(std::pair<CPCAPI2::SipConversation::SipConversationHandle, CPCAPI2::SipConference::SipConferenceHandle>(conversationHandle, conference));

	return kSuccess;
}
int SipConferenceManagerImpl::inviteToServerConference(SipConversationHandle conversation, cpc::string reason)
{
	mAccountIf->postToSdkThread(resip::resip_bind(&SipConferenceManagerImpl::inviteToServerConferenceImpl, this, conversation, reason));
	return kSuccess;
}

int SipConferenceManagerImpl::inviteToServerConferenceImpl(SipConversationHandle conversation, cpc::string reason)
{
	ConfConversationMap::iterator convIt = mConfConversationMap.find(conversation);
	if(convIt == mConfConversationMap.end())
	{
		mAccountIf->fireError("ERROR: Invalid conversation handle for SipConferenceManagerImpl::setServerConfFocusUri.");
		return kSuccess;
	}

	ConferenceMap::iterator it = mConferenceMap.find(convIt->second);
	if(it == mConferenceMap.end())
	{
		mAccountIf->fireError("ERROR: Invalid conference handle for SipConferenceManagerImpl::inviteToServerConference.");
		return kSuccess;
	}

	mConversationIf->transfer(conversation, it->second->focusUri);

	return kSuccess;
}

int SipConferenceManagerImpl::setServerConfFocusUri(SipConversationHandle conversation, cpc::string focusUri)
{
	ConfConversationMap::iterator convIt = mConfConversationMap.find(conversation);
	if(convIt == mConfConversationMap.end())
	{
		mAccountIf->fireError("ERROR: Invalid conversation handle for SipConferenceManagerImpl::setServerConfFocusUri.");
		return kSuccess;
	}

	ConferenceMap::iterator confIt = mConferenceMap.find(convIt->second);
	if(confIt == mConferenceMap.end())
	{
		mAccountIf->fireError("ERROR: Invalid conference handle for SipConferenceManagerImpl::setServerConfFocusUri.");
		return kSuccess;
	}

	confIt->second->focusUri = focusUri;

	return kSuccess;
}

int SipConferenceManagerImpl::callServerConfParticipants(SipConversationHandle conversation)
{
	ConfConversationMap::iterator convIt = mConfConversationMap.find(conversation);
	if(convIt == mConfConversationMap.end())
	{
		mAccountIf->fireError("ERROR: Invalid conversation handle for SipConferenceManagerImpl::setServerConfFocusUri.");
		return kSuccess;
	}

	ConferenceMap::iterator it = mConferenceMap.find(convIt->second);
	if(it == mConferenceMap.end())
	{
		mAccountIf->fireError("ERROR: Invalid conference handle for SipConferenceManagerImpl::inviteToServerConference.");
		return kSuccess;
	}
	ConferenceAccountMap::iterator confAccountIt = mConfAccountMap.find(convIt->second);
	if(confAccountIt == mConfAccountMap.end())
	{
		mAccountIf->fireError("ERROR: Invalid conference handle for SipConferenceManagerImpl::startServerConfImpl.");
		return kSuccess;
	}

	for(int i=0;i<it->second->participants.size();i++)
	{
		SipConversationHandle conversationHandle = mConversationIf->createConversation(confAccountIt->second);
		mConversationIf->addParticipant(conversationHandle, it->second->participants[i].uri);
		mConversationIf->start(conversationHandle);
		mConfConversationMap.insert(std::pair<CPCAPI2::SipConversation::SipConversationHandle, CPCAPI2::SipConference::SipConferenceHandle>(conversationHandle, convIt->second));
	}

	return kSuccess;
}

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// private functions
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**
* Returns unique conference uri in form sip:<EMAIL>
* datetime format is YYYYMMDDHHmmss
*/
cpc::string SipConferenceManagerImpl::generateUniqueConferenceUri(CPCAPI2::SipAccount::SipAccountHandle account)
{ 
	return  "sip:" + mAccountIf->getAccountImpl(account)->getSettings().username + "_" + currentDateTime() + "@" + mAccountIf->getAccountImpl(account)->getSettings().domain;
}

/**
* Returns account uri in form sip:<EMAIL>
*/
cpc::string SipConferenceManagerImpl::getUri(CPCAPI2::SipAccount::SipAccountHandle account)
{
	return "sip:" + mAccountIf->getAccountImpl(account)->getSettings().username + "@" + mAccountIf->getAccountImpl(account)->getSettings().domain;
}

/**
* Returns current datetime in format YYYYMMDDHHmmss
*/
cpc::string SipConferenceManagerImpl::currentDateTime() 
{
    time_t     now = time(0);
    struct std::tm  tstruct;
    char       buf[80];

    tstruct = *localtime(&now);
    strftime(buf, sizeof(buf), "%Y%m%d%H%M%S", &tstruct);

    return buf;
}

/*
* Creates SDP part of multipart SIP message body.
*/
cpc::string SipConferenceManagerImpl::buildSdp(SipAccountImpl* account)
{
	SipAccountSettings accountSettings =  account->getSettings();
	UInt64 currentTime = resip::Timer::getTimeMicroSec();
	resip::Data localIpAddr = accountSettings.domain;
	resip::Data sdpString = "";
	cpc::string ret;
	char* buff = new char[20];
	resip::DataStream ds(sdpString);

	ret = "--";
	ret += MULTIPART_BOUNDARY;
	ret += "\nContent-Type: application/sdp\r\n";
	ret += "v=0\r\n";
	ret += "o=";
	ret += accountSettings.username;
	itoa(currentTime, buff, 10);
	ret += buff;
	ret += " ";
	itoa(currentTime, buff, 10);
	ret += buff;
	ret += " ";
	ret += "IN ";
	ret += (resip::DnsUtil::isIpV6Address(localIpAddr) ? "IP6" : "IP4");
	ret += " ";
	ret += localIpAddr.c_str();
	ret += "\r\n";
	ret += "s=- \r\n";
	ret += "t=0 0\r\n";
	ret += "m=audio ";
	itoa(localAudioPort, buff, 10);
	ret += buff;
	ret += " RTP/AVP 0\r\n";
	ret += "a=rtpmap:0 PCMU/8000 \r\n";
	ret += "m=video ";
	itoa(localVideoPort, buff, 10);
	ret += buff;
	ret += " RTP/AVP 31\r\n";
	ret += "a=rtpmap:31 H261/90000 \r\n";
	return ret;
}

/*
* Creates URI list part of multipart SIP message body.
*/
cpc::string SipConferenceManagerImpl::buildUriList(SipConferenceHandle conference)
{
	cpc::string body;

	ConferenceMap::iterator confIt = mConferenceMap.find(conference);
	if(confIt == mConferenceMap.end())
	{
		mAccountIf->fireError("ERROR: Invalid conference handle for SipConferenceManagerImpl::startServerConfImpl.");
		return kSuccess;
	}

	body += "\r\n--"; 
	body += MULTIPART_BOUNDARY;
	body += "\r\nContent-Type: application/resource-lists+xml\r\nContent-Disposition: recipient-list\r\n";

	cpc::string uriList = "\r\n<?xml version=\"1.0\" encoding=\"UTF-8\"?><resource-lists xmlns=\"urn:ietf:params:xml:ns:resource-lists\"xmlns:cp=\"urn:ietf:params:xml:ns:copycontrol\">\r\n<list>";
	for(unsigned int i=0; i<confIt->second->participants.size();i++)
	{
		uriList += "\r\n<entry uri=\"";
		uriList += confIt->second->participants[i].uri;
		uriList += "\" />";
	}
	uriList += "\r\n</list>\r\n</resource-lists>";

	body += uriList;
	body += "\r\n--";
	body += MULTIPART_BOUNDARY;
	body += "--\r\n";
	return body;
}

} // SipConference
} // CPCAPI2
#endif // CPCAPI2_BRAND_CONFERENCE_MODULE
