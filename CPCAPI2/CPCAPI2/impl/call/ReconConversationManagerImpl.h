#pragma once

#if !defined(CPCAPI2_RECON_CONVERSATION_MANAGER_IMPL_H)
#define CPCAPI2_RECON_CONVERSATION_MANAGER_IMPL_H

#include "../account/SipAccountImpl.h"
#include "account/SipAccountSettings.h"
#include "call/SipConversationTypes.h"
#include "call/SipConversationManager.h"
#include "call/SipConversationHandler.h"
#include "SipCallCreationInfo.h"
#include "../media/AudioLevelMonitor.h"
#include "../util/ThrottledInvoker.h"

#include <RtpStreamImpl.hxx>

#include <ConversationManager.hxx>

namespace CPCAPI2
{

namespace SipConversation
{

class SipConversationHandler;
class SipConversationHandlerInternal;
class SipConversationAdornmentInternalHandler;
class SipAVConversationManagerImpl;

class ReconConversationManagerImpl : public recon::ConversationManager,
                                     private CPCAPI2::Media::AudioLevelObserver,
                                     private webrtc_recon::RtpStreamErrorCallback,
                                     private webrtc_recon::RtpStreamKeyFrameRequestCallback
{
public:
   ReconConversationManagerImpl(recon::UserAgent& ua, CPCAPI2::SipAccount::SipAccountImpl& account, SipAVConversationManagerImpl& callAcct, const std::map<CPCAPI2::NetworkTransport, SipConversationSettings>& convSettings, const resip::Data& overrideSourceIpSignalling, const resip::Data& overrideSourceIpTransport, void* tscconfig, bool dtlsSupported);
   virtual ~ReconConversationManagerImpl();

   virtual void shutdown();

   SipCallCreationInfo* getCreationInfo(const SipConversationHandle& h) const;
   SipCallCreationInfo* getCreationInfoForMediaStream(int webrtc_channel, SipConversationHandle& h) const;
   void addCreationInfo(const SipConversationHandle& h, SipCallCreationInfo* ci);
   void removeCreationInfo(const SipConversationHandle& h);

   recon::ConversationProfileHandle getDefaultConversationProfile() const;
   void updateDefaultConversationProfile(const resip::Tuple* serverIp=NULL);
   void resetLocalInterfaceOverride();
   const resip::Data& overrideSourceIpSignalling() const;
   resip::Data& overrideSourceIpSignalling();
   recon::ParticipantHandle getLocalParticipant() const;
   void sendInfo( recon::ParticipantHandle partHandle, resip::Contents& content );
   int updateMediaConnections();
   int startStarcodeHandovers();
   
   // Returns true only if the binding has changed. Will return false if the current binding
   // was never populated as would be the case with a new registration.
   bool hasLocalBindingUpdated(const resip::NameAddr& contact);
   void updateLocalContact(const resip::NameAddr& contact);
   void updateLocalInterfaceOverride(const resip::Tuple* serverIp = NULL);
   void setOfferTelephoneEvent( const bool& enable ); // modifies the default conversation profile
   const SipConversationSettings getConvSettings() const;
   int refreshConversationStatisticsImpl(SipConversationHandle conversation, bool includeNetworkStats, bool includeJitterStats, bool includeRemoteStats, bool internalOnly);

   ConversationState conversationState() const { return mConvState; }
   bool hasCallWithCallId(const cpc::string& callId) const;
   int getRemoteParticipantCount(SipConversationHandle conversation);

   void destroyAllConversations();
   void destroyRelatedConversations(SipConversationHandle h);

   void setNackPliSupported(bool nackPliSupported) { mNackPliSupported = nackPliSupported; }
   void setTmmbrSupported(bool tmmbrSupported) { mTmmbrSupported = tmmbrSupported; }
   //void setDtlsSupported(bool dtlsSupported) { mDtlsSupported = dtlsSupported; }

   // RtpStreamErrorCallback
   virtual void onRtpStreamError(const std::shared_ptr<recon::RtpStream>& rtpStream, int errorCode, const resip::Data& errorMsg, int appState);

   // RtpStreamKeyFrameRequestCallback
   virtual void onKeyFrameRequest(const std::shared_ptr<recon::RtpStream>& rtpStream, int appState);

   ///////////////////////////////////////////////////////////////////////
   // Conversation Manager Functions /////////////////////////////////////
   ///////////////////////////////////////////////////////////////////////

   virtual void alertParticipant(recon::ParticipantHandle partHandle, bool earlyFlag = true);

   ///////////////////////////////////////////////////////////////////////
   // Conversation Manager Handlers //////////////////////////////////////
   ///////////////////////////////////////////////////////////////////////

   virtual void onMediaStreamCreated(recon::ParticipantHandle partHandle, std::shared_ptr<recon::RtpStream> rtpStream);

   /**
     Notifies an application about a new remote participant that is attempting
     to contact it.

     @param partHandle Handle of the new incoming participant
     @param msg Includes information about the caller such as name and address
     @param autoAnswer Set to true if auto answer has been requested
   */
   virtual void onIncomingParticipant(
      recon::ParticipantHandle partHandle, 
      const resip::SipMessage& msg, 
      bool autoAnswer,
      bool isCodecsMismatched,
      const MediaSpecificMismatchInfo& mediaMismatchedInfo,
      const ConversationManager::MediaAttributes& requestedMedia);

   /**
     Notifies an application about a new remote participant that is trying 
     to be contacted.  This event is required to notify the application if a 
     call request has been initiated by a signaling mechanism other than
     the application, such as an out-of-dialog REFER request.

     @param partHandle Handle of the new incoming participant
     @param msg Includes information about the destination requested
                to be attempted
   */
   virtual void onRequestOutgoingParticipant(recon::ParticipantHandle partHandle, const resip::SipMessage& msg);
   virtual void onRequestOutgoingParticipant(recon::ParticipantHandle transferTargetHandle, recon::ParticipantHandle transfererHandle, const resip::SipMessage& msg);

   /**
     Notifies an application about a disconnect by a remote participant.  
     For SIP this could be a BYE or a CANCEL request.

     @param partHandle Handle of the participant that terminated
     @param statusCode The status Code for the termination.
   */
   virtual void onParticipantTerminated(recon::ParticipantHandle partHandle, unsigned int statusCode, resip::InviteSessionHandler::TerminatedReason reason, const resip::SipMessage* msg);

   /**
     Notifies an application when a conversation has been destroyed.  
     This is useful for tracking conversations that get created when forking 
     occurs, and are destroyed when forked call is answered or ended.

     @param convHandle Handle of the destroyed conversation
   */
   virtual void onConversationDestroyed(recon::ConversationHandle convHandle);

   /** 
     Notifies an application when a Participant has been destroyed.  This is 
     useful for tracking when audio playback via MediaResourceParticipants has 
     stopped.

     @param partHandle Handle of the destroyed participant
   */
   virtual void onParticipantDestroyed(recon::ParticipantHandle partHandle);

   /**
     Notifies an applications that a outbound remote participant request has 
     forked.  A new Related Conversation and Participant are created.  
     Both new handles and original handles are conveyed to the application 
     so it can track related conversations.

     @param relatedConvHandle Handle of newly created related conversation
     @param relatedPartHandle Handle of the newly created related participant
     @param origConvHandle    Handle of the conversation that contained the
                              participant that forked
     @param origParticipant   Handle of the participant that forked
   */
   virtual void onRelatedConversation(recon::ConversationHandle relatedConvHandle, recon::ParticipantHandle relatedPartHandle, 
                                      recon::ConversationHandle origConvHandle, recon::ParticipantHandle origPartHandle);

   /**
     Notifies an application that a remote participant call attempt is 
     alerting the remote party.  

     This is called from the DUM InviteSessionHandler::onProvisional(..) handler in recon.

     @param partHandle Handle of the participant that is alerting
     @param msg SIP message that caused the alerting
   */
   virtual void onParticipantAlerting(recon::ParticipantHandle partHandle, const resip::SipMessage& msg);
   virtual void onParticipantEarlyMedia(recon::ParticipantHandle partHandle, const resip::SipMessage& msg);

   /**
     Notifies an application that a remote participant call attempt is 
     now connected.

     @param partHandle Handle of the participant that is connected
     @param msg SIP message that caused the connection
   */
   virtual void onParticipantConnected(recon::ParticipantHandle partHandle, const resip::SipMessage& msg);

   /**
     Notifies an application that a redirect request is progressing.
     Implies a NOTIFY w/SipFrag status < 200.  Safe to ignore.

     @param partHandle Handle of the participant that was redirected
   */
   virtual void onParticipantRedirectProgress(recon::ParticipantHandle partHandle, const resip::SipMessage* msg=NULL);

   /**
     Notifies an application that a redirect request has succeeded.  
     Indicates blind transfer or attended transfer status. 

     @param partHandle Handle of the participant that was redirected
   */
   virtual void onParticipantRedirectSuccess(recon::ParticipantHandle partHandle, const resip::SipMessage* msg=NULL);

   /**
     Notifies an application that a redirect request has failed.  
     Indicates blind transfer or attended transfer status. 

     @param partHandle Handle of the participant that was redirected
   */
   virtual void onParticipantRedirectFailure(recon::ParticipantHandle partHandle, unsigned int statusCode, const resip::Data& reasonPhrase, const resip::SipMessage* msg=NULL);

   virtual void onParticipantStale(recon::ParticipantHandle partHandle) {}

   /**
     Notifies an application when an RFC2833 DTMF event is received from a 
     particular remote participant.

     @param partHandle Handle of the participant that received the digit
     @param dtmf Integer representation of the DTMF tone received
     @param duration Duration of the DTMF tone received
     @param up Set to true if the DTMF key is up (otherwise down)
   */
   virtual void onDtmfEvent(recon::ParticipantHandle partHandle, int dtmf, int duration, bool up);

   virtual void onParticipantMediaChangeRequested(
      recon::ParticipantHandle partHandle,
      recon::ConversationManager::MediaDirection remoteAudioState,
      recon::ConversationManager::MediaDirection remoteVideoState,
      const recon::ConversationManager::MediaAttributes& mediaAttribs,
      bool mediaDidChange,
      const resip::SipMessage* msg);
   virtual void onParticipantMediaChanged(
      recon::ParticipantHandle partHandle,
      recon::ConversationManager::MediaDirection audioState,
      recon::ConversationManager::MediaDirection videoState,
      recon::ConversationManager::MediaDirection remoteAudioState,
      ConversationManager::MediaDirection remoteVideoState,
      recon::ConversationManager::SecureMediaMode remoteAudioCryptoSuiteMode,
      recon::ConversationManager::SecureMediaMode remoteVideoCryptoSuiteMode,
      recon::ConversationManager::SecureMediaCryptoSuite remoteAudioCryptoSuite,
      recon::ConversationManager::SecureMediaCryptoSuite remoteVideoCryptoSuite,
      std::vector<recon::ConversationManager::SecureMediaCryptoSuite>& supportedAudioCryptoSuites,
      std::vector<recon::ConversationManager::SecureMediaCryptoSuite>& supportedVideoCryptoSuites,
      const resip::SipMessage* msg);
   virtual void onNewOutgoingParticipant(recon::ParticipantHandle partHandle, const resip::SipMessage& msg);
   virtual void onNewOutgoingParticipant(recon::ParticipantHandle partHandle, recon::ParticipantHandle origParticipantHandle, const resip::SipMessage& msg);
   virtual void onLocalParticipantRedirected(recon::ParticipantHandle partHandle, const resip::SipMessage& msg);
   virtual void onIncomingJoinRequest(recon::ParticipantHandle newPartHandle, recon::ParticipantHandle existingPartHandle, const resip::SipMessage& msg, bool autoAnswer, bool isCodecsMismatched, const MediaSpecificMismatchInfo& mediaMismatchedInfo, const recon::ConversationManager::MediaAttributes& requestedMedia);
   virtual void onIncomingTransferRequest(recon::ParticipantHandle newPartHandle, recon::ParticipantHandle existingPartHandle, const resip::SipMessage& msg, bool autoAnswer, bool isCodecsMismatched, const MediaSpecificMismatchInfo& mediaMismatchedInfo, const recon::ConversationManager::MediaAttributes& requestedMedia);
   virtual void onReadyToSendInvite(recon::ParticipantHandle partHandle, resip::SipMessage& msg);
   virtual bool onSuccess(resip::ClientOutOfDialogReqHandle, const resip::SipMessage& response);
   virtual bool onFailure(resip::ClientOutOfDialogReqHandle, const resip::SipMessage& response);

   // InviteSessionHandler overrides
   virtual void onNewSession(resip::ClientInviteSessionHandle h, resip::InviteSession::OfferAnswerType oat, const resip::SipMessage& msg);
   virtual void onNewSession(resip::ServerInviteSessionHandle h, resip::InviteSession::OfferAnswerType oat, const resip::SipMessage& msg);
   virtual void onInfo(resip::InviteSessionHandle h, const resip::SipMessage& msg);
   virtual void onInfoFailure(resip::InviteSessionHandle h, const resip::SipMessage& msg);
   virtual void onTerminated(resip::InviteSessionHandle h, resip::InviteSessionHandler::TerminatedReason reason, const resip::SipMessage* msg);
   virtual void onProvisional(resip::ClientInviteSessionHandle h, const resip::SipMessage& msg);
   virtual void onConnected(resip::InviteSessionHandle h, const resip::SipMessage& msg);
   virtual void onConnected(resip::ClientInviteSessionHandle h, const resip::SipMessage& msg);
   virtual void onAnswer(resip::InviteSessionHandle, const resip::SipMessage& msg, const resip::SdpContents&);
   virtual void onOffer(resip::InviteSessionHandle handle, const resip::SipMessage& msg, const resip::SdpContents& offer);
   virtual void onOfferRequired(resip::InviteSessionHandle, const resip::SipMessage& msg);
   virtual void onReferResponseReceived(resip::InviteSessionHandle, const resip::SipMessage& msg);

   // RedirectHandler overrides /////////////////////////////////////////////////////////////
   virtual void onRedirectReceived(resip::AppDialogSetHandle, const resip::SipMessage& response);
   virtual bool onTryingNextTarget(resip::AppDialogSetHandle, const resip::SipMessage& request);

   virtual bool isOutOfDialogReferSupported() const;

   // adornment
   void addAdornmentHandler(SipConversationAdornmentInternalHandler* handler);

   virtual void onReadyToSend(resip::ClientSubscriptionHandle h, resip::SipMessage& msg);
   virtual void onReadyToSend(resip::ServerSubscriptionHandle h, resip::SipMessage& msg);
   virtual void onReadyToSend(resip::InviteSessionHandle h, resip::SipMessage& msg);

   int setReconConvManSettings(const SipConversationSettings& settings, CPCAPI2::NetworkTransport transport);

   int startMonitoringAudioLevels(SipConversationHandle convHandle);
   int stopMonitoringAudioLevels(SipConversationHandle convHandle);

   virtual void updateConversationProfile(resip::SharedPtr<recon::ConversationProfile>& convProfile, const resip::Tuple* serverIp=NULL) const;
   virtual bool onLocalOfferRequired(recon::ConversationHandle conversation, recon::ParticipantHandle participant, const recon::ConversationManager::MediaAttributes& mediaAttribs);
   virtual bool onLocalAnswerRequired(recon::ParticipantHandle participant, const resip::SdpContents& sdpOffer, const recon::ConversationManager::MediaAttributes& mediaAttribs);

   static recon::ConversationManager::MediaAttributes toReconMediaAttribs(const cpc::vector<MediaInfo>& mi, unsigned int defaultMixId);
   static SipConversation::MediaDirection convertMediaDirection(recon::ConversationManager::MediaDirection md);
   static SipConversation::MediaEncryptionMode convertMediaEncryptionMode(recon::ConversationManager::SecureMediaMode smm);
   static SipConversation::MediaCryptoSuite convertMediaCryptoSuite(recon::ConversationManager::SecureMediaCryptoSuite crypto);
   static recon::ConversationManager::SecureMediaCryptoSuite convertMediaCryptoSuite(SipConversation::MediaCryptoSuite crypto);
   static std::vector<recon::ConversationManager::SecureMediaCryptoSuite> convertMediaCryptoSuites(cpc::vector<SipConversation::MediaCryptoSuite> cryptos);
   static cpc::vector<SipConversation::MediaCryptoSuite> convertMediaCryptoSuites(std::vector<recon::ConversationManager::SecureMediaCryptoSuite> cryptos);
   static cpc::vector<MediaCryptoSuite> getMediaCryptoSuites(const cpc::vector<MediaInfo>& mi, const MediaType& mediaType);
   static SipConversation::AnswerModeSettings toSdkAnswerMode(const recon::ConversationManager::AnswerModeAttributes& reconAnswerMode);
   static recon::ConversationManager::AnswerModeAttributes toReconAnswerMode(const SipConversation::AnswerModeSettings& sdkAnswerMode);
   static SipConversation::AnswerModeSettings extractAnswerMode(const resip::SipMessage& msg);

private:
   /**
    * DO NOT CALL THIS DIRECTLY -- use removeCreationInfo
    */
   void removeCreationInfoImpl(SipCallCreationInfo* ci);
   void createConversationProfiles(resip::DialogUsageManager* dum);
   resip::Data determineSrcIp() const;

   SipConversationHandle findSipConversationHandle(recon::ConversationHandle reconConvHandle) const;
   SipConversationHandle findSipConversationHandleByParticipantHandle(recon::ParticipantHandle reconPartHandle, bool includeOrig=false) const;

   void finalizeConversation(recon::ParticipantHandle partHandle, resip::InviteSessionHandler::TerminatedReason reason, const resip::SipMessage* msg);

   void adornMessage(recon::ParticipantHandle partHandle, resip::SipMessage& msg) const;

   cpc::string getHeader(const resip::SipMessage& msg, const cpc::string& headerName);
   
   void populateLocalRemoteConversationValues(SipConversationHandle convHandle);

   void clearStatistics(AudioStatistics* stats);
   void clearStatistics(RemoteAudioStatistics* stats);
   void clearStatistics(VideoStatistics* stats);
   void clearStatistics(RemoteVideoStatistics* stats);

   virtual void onAudioLevels(int voe_channel, unsigned int inputLevel, unsigned int outputLevel);

   ConversationCallQuality calculateCallQuality(SipConversationHandle h, unsigned short localFractionLost, unsigned short remoteFractionLost, unsigned int totalReceivedPackets, unsigned int totalSentPackets, int64_t latency, unsigned int numIntervalsWithoutUpdate );

private:
   resip::SharedPtr<recon::ConversationProfile> mDefaultConvProfile;
   recon::ConversationProfileHandle mDefaultConvProfileHandle;
   CPCAPI2::SipAccount::SipAccountImpl& mAccount;
   SipAVConversationManagerImpl& mCallImpl;
   CPCAPI2::SipAccount::SipAccountSettings mAcctSettings;
   std::map<NetworkTransport, SipConversationSettings> mConvSettings;
   recon::ParticipantHandle mLocalParticipant;
   SipCallCreationInfoMap mCallCreationInfo;
   ConversationState mConvState;
   SipConversationAdornmentInternalHandler* mAdornmentHandler;
   CPCAPI2::Media::AudioLevelMonitor* mAudioLevelMonitor;
   resip::Data mOverrideSourceIpSignalling;
   resip::Data mOverrideSourceIpTransport;
   resip::NameAddr mLocalContactBinding;
   bool mNackPliSupported;
   bool mTmmbrSupported;
   bool mDtlsSupported;
   Utils::ThrottledInvoker* mThrottledSipInfoPliSender;
};

}

}

#endif // CPCAPI2_RECON_CONVERSATION_MANAGER_IMPL_H
