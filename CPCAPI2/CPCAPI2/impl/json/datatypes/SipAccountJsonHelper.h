// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef SipAccount_JSON_HELPER_H
#define SipAccount_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/public/account/SipAccountSettings.h>
#include <interface/public/account/SipAccountHandler.h>
#include <interface/public/account/SipAccountState.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipAccount::TunnelConfig& v)
    {
      w.StartObject();
      Write(w, "useTunnel", v.useTunnel);
      Write(w, "tunnelType", v.tunnelType);
      Write(w, "server", v.server);
      Write(w, "transportType", v.transportType);
      Write(w, "mediaTransportType", v.mediaTransportType);
      Write(w, "redundancyFactor", v.redundancyFactor);
      Write(w, "doLoadBalancing", v.doLoadBalancing);
      Write(w, "ignoreCertVerification", v.ignoreCertVerification);
      Write(w, "disableNagleAlgorithm", v.disableNagleAlgorithm);
      Write(w, "strettoTunnelURL", v.strettoTunnelURL);
      Write(w, "strettoTunnelToken", v.strettoTunnelToken);
      Write(w, "strettoTunnelSessionID", v.strettoTunnelSessionID);
      Write(w, "strettoTunnelTestConnection", v.strettoTunnelTestConnection);
      Write(w, "logStrettoTunnelTransportTraces", v.logStrettoTunnelTransportTraces);
      Write(w, "strettoTunnelSkipHandshake", v.strettoTunnelSkipHandshake);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipAccount::TunnelConfig& v)
    {
      Read(json, "useTunnel", v.useTunnel);
      Read(json, "tunnelType", v.tunnelType);
      Read(json, "server", v.server);
      Read(json, "transportType", v.transportType);
      Read(json, "mediaTransportType", v.mediaTransportType);
      Read(json, "redundancyFactor", v.redundancyFactor);
      Read(json, "doLoadBalancing", v.doLoadBalancing);
      Read(json, "ignoreCertVerification", v.ignoreCertVerification);
      Read(json, "disableNagleAlgorithm", v.disableNagleAlgorithm);
      Read(json, "strettoTunnelURL", v.strettoTunnelURL);
      Read(json, "strettoTunnelToken", v.strettoTunnelToken);
      Read(json, "strettoTunnelSessionID", v.strettoTunnelSessionID);
      Read(json, "strettoTunnelTestConnection", v.strettoTunnelTestConnection);
      Read(json, "logStrettoTunnelTransportTraces", v.logStrettoTunnelTransportTraces);
      Read(json, "strettoTunnelSkipHandshake", v.strettoTunnelSkipHandshake);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipAccount::SipAccountSettings& v)
    {
      w.StartObject();
      Write(w, "username", v.username);
      Write(w, "domain", v.domain);
      Write(w, "password", v.password);
      Write(w, "displayName", v.displayName);
      Write(w, "auth_username", v.auth_username);
      Write(w, "auth_realm", v.auth_realm);
      Write(w, "useRegistrar", v.useRegistrar);
      Write(w, "outboundProxy", v.outboundProxy);
      Write(w, "alwaysRouteViaOutboundProxy", v.alwaysRouteViaOutboundProxy);
      Write(w, "registrationIntervalSeconds", v.registrationIntervalSeconds);
      Write(w, "minimumRegistrationIntervalSeconds", v.minimumRegistrationIntervalSeconds);
      Write(w, "maximumRegistrationIntervalSeconds", v.maximumRegistrationIntervalSeconds);
      Write(w, "useRport", v.useRport);
      Write(w, "sipTransportType", v.sipTransportType);
      Write(w, "excludeEncryptedTransports", v.excludeEncryptedTransports);
      Write(w, "userAgent", v.userAgent);
      Write(w, "udpKeepAliveTime", v.udpKeepAliveTime);
      Write(w, "tcpKeepAliveTime", v.tcpKeepAliveTime);
      Write(w, "useOutbound", v.useOutbound);
      Write(w, "useGruu", v.useGruu);
      Write(w, "otherNonEscapedCharsInUri", v.otherNonEscapedCharsInUri);
      Write(w, "nameServers", v.nameServers);
      Write(w, "additionalNameServers", v.additionalNameServers);
      Write(w, "sessionTimerMode", v.sessionTimerMode);
      Write(w, "sessionTimeSeconds", v.sessionTimeSeconds);
      Write(w, "stunServerSource", v.stunServerSource);
      Write(w, "stunServer", v.stunServer);
      Write(w, "ignoreCertVerification", v.ignoreCertVerification);
      Write(w, "additionalCertPeerNames", v.additionalCertPeerNames);
      Write(w, "acceptedCertPublicKeys", v.acceptedCertPublicKeys);
      Write(w, "requiredCertPublicKeys", v.requiredCertPublicKeys);
      Write(w, "sipQosSettings", v.sipQosSettings);
      Write(w, "useImsAuthHeader", v.useImsAuthHeader);
      Write(w, "minSipPort", v.minSipPort);
      Write(w, "maxSipPort", v.maxSipPort);
      Write(w, "defaultSipPort", v.defaultSipPort);
      Write(w, "defaultSipsPort", v.defaultSipsPort);
      Write(w, "useMethodParamInReferTo", v.useMethodParamInReferTo);
      Write(w, "useInstanceId", v.useInstanceId);
      Write(w, "answerModeSupported", v.answerModeSupported);
      Write(w, "ipVersion", v.ipVersion);
      Write(w, "sslVersion", v.sslVersion);
      Write(w, "cipherSuite", v.cipherSuite);
      Write(w, "enableLegacyServerConnect", v.enableLegacyServerConnect);
      Write(w, "reRegisterOnResponseTypes", v.reRegisterOnResponseTypes);
      Write(w, "enableRegeventDeregistration", v.enableRegeventDeregistration);
      Write(w, "enableDNSResetOnRegistrationRefresh", v.enableDNSResetOnRegistrationRefresh);
      Write(w, "enableAuthResetUponDNSReset", v.enableAuthResetUponDNSReset);
      Write(w, "XCAPRoot", v.XCAPRoot);
      Write(w, "tunnelConfig", v.tunnelConfig);
      Write(w, "capabilities", v.capabilities);
      Write(w, "additionalFromParameters", v.additionalFromParameters);
      Write(w, "sourceAddress", v.sourceAddress);
      Write(w, "preferPAssertedIdentity", v.preferPAssertedIdentity);
      Write(w, "autoRetryOnTransportDisconnect", v.autoRetryOnTransportDisconnect);
      Write(w, "keepAliveMode", v.keepAliveMode);
      Write(w, "useRinstance", v.useRinstance);
      Write(w, "enableNat64Support", v.enableNat64Support);
      Write(w, "usePrivacyHeaderOnlyForAnonymous", v.usePrivacyHeaderOnlyForAnonymous);
      Write(w, "transportHoldover", v.transportHoldover);
      Write(w, "useOptionsPing", v.useOptionsPing);
      Write(w, "optionsPingInterval", v.optionsPingInterval);
      Write(w, "userCertificatePEM", v.userCertificatePEM);
      Write(w, "userPrivateKeyPEM", v.userPrivateKeyPEM);
      Write(w, "forceListenSocket", v.forceListenSocket);
      Write(w, "localGroup", v.localGroup);
      Write(w, "overrideMsecsTimerF", v.overrideMsecsTimerF);
      Write(w, "minInvite200RetransmitIntervalSec", v.minInvite200RetransmitIntervalSec);
      Write(w, "addCryptoKeyPadding", v.addCryptoKeyPadding);
      Write(w, "networkChangeFilter", v.networkChangeFilter);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipAccount::SipAccountSettings& v)
    {
      Read(json, "username", v.username);
      Read(json, "domain", v.domain);
      Read(json, "password", v.password);
      Read(json, "displayName", v.displayName);
      Read(json, "auth_username", v.auth_username);
      Read(json, "auth_realm", v.auth_realm);
      Read(json, "useRegistrar", v.useRegistrar);
      Read(json, "outboundProxy", v.outboundProxy);
      Read(json, "alwaysRouteViaOutboundProxy", v.alwaysRouteViaOutboundProxy);
      Read(json, "registrationIntervalSeconds", v.registrationIntervalSeconds);
      Read(json, "minimumRegistrationIntervalSeconds", v.minimumRegistrationIntervalSeconds);
      Read(json, "maximumRegistrationIntervalSeconds", v.maximumRegistrationIntervalSeconds);
      Read(json, "useRport", v.useRport);
      Read(json, "sipTransportType", v.sipTransportType);
      Read(json, "excludeEncryptedTransports", v.excludeEncryptedTransports);
      Read(json, "userAgent", v.userAgent);
      Read(json, "udpKeepAliveTime", v.udpKeepAliveTime);
      Read(json, "tcpKeepAliveTime", v.tcpKeepAliveTime);
      Read(json, "useOutbound", v.useOutbound);
      Read(json, "useGruu", v.useGruu);
      Read(json, "otherNonEscapedCharsInUri", v.otherNonEscapedCharsInUri);
      Read(json, "nameServers", v.nameServers);
      Read(json, "additionalNameServers", v.additionalNameServers);
      Read(json, "sessionTimerMode", v.sessionTimerMode);
      Read(json, "sessionTimeSeconds", v.sessionTimeSeconds);
      Read(json, "stunServerSource", v.stunServerSource);
      Read(json, "stunServer", v.stunServer);
      Read(json, "ignoreCertVerification", v.ignoreCertVerification);
      Read(json, "additionalCertPeerNames", v.additionalCertPeerNames);
      Read(json, "acceptedCertPublicKeys", v.acceptedCertPublicKeys);
      Read(json, "requiredCertPublicKeys", v.requiredCertPublicKeys);
      Read(json, "sipQosSettings", v.sipQosSettings);
      Read(json, "useImsAuthHeader", v.useImsAuthHeader);
      Read(json, "minSipPort", v.minSipPort);
      Read(json, "maxSipPort", v.maxSipPort);
      Read(json, "defaultSipPort", v.defaultSipPort);
      Read(json, "defaultSipsPort", v.defaultSipsPort);
      Read(json, "useMethodParamInReferTo", v.useMethodParamInReferTo);
      Read(json, "useInstanceId", v.useInstanceId);
      Read(json, "answerModeSupported", v.answerModeSupported);
      Read(json, "ipVersion", v.ipVersion);
      Read(json, "sslVersion", v.sslVersion);
      Read(json, "cipherSuite", v.cipherSuite);
      Read(json, "enableLegacyServerConnect", v.enableLegacyServerConnect);
      Read(json, "reRegisterOnResponseTypes", v.reRegisterOnResponseTypes);
      Read(json, "enableRegeventDeregistration", v.enableRegeventDeregistration);
      Read(json, "enableDNSResetOnRegistrationRefresh", v.enableDNSResetOnRegistrationRefresh);
      Read(json, "enableAuthResetUponDNSReset", v.enableAuthResetUponDNSReset);
      Read(json, "XCAPRoot", v.XCAPRoot);
      Read(json, "tunnelConfig", v.tunnelConfig);
      Read(json, "capabilities", v.capabilities);
      Read(json, "additionalFromParameters", v.additionalFromParameters);
      Read(json, "sourceAddress", v.sourceAddress);
      Read(json, "preferPAssertedIdentity", v.preferPAssertedIdentity);
      Read(json, "autoRetryOnTransportDisconnect", v.autoRetryOnTransportDisconnect);
      Read(json, "keepAliveMode", v.keepAliveMode);
      Read(json, "useRinstance", v.useRinstance);
      Read(json, "enableNat64Support", v.enableNat64Support);
      Read(json, "usePrivacyHeaderOnlyForAnonymous", v.usePrivacyHeaderOnlyForAnonymous);
      Read(json, "transportHoldover", v.transportHoldover);
      Read(json, "useOptionsPing", v.useOptionsPing);
      Read(json, "optionsPingInterval", v.optionsPingInterval);
      Read(json, "userCertificatePEM", v.userCertificatePEM);
      Read(json, "userPrivateKeyPEM", v.userPrivateKeyPEM);
      Read(json, "forceListenSocket", v.forceListenSocket);
      Read(json, "localGroup", v.localGroup);
      Read(json, "overrideMsecsTimerF", v.overrideMsecsTimerF);
      Read(json, "minInvite200RetransmitIntervalSec", v.minInvite200RetransmitIntervalSec);
      Read(json, "addCryptoKeyPadding", v.addCryptoKeyPadding);
      Read(json, "networkChangeFilter", v.networkChangeFilter);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipAccount::SipTLSConnectionInfo& v)
    {
      w.StartObject();
      Write(w, "certificateStatus", v.certificateStatus);
      Write(w, "issuer", v.issuer);
      Write(w, "server", v.server);
      Write(w, "peerNames", v.peerNames);
      Write(w, "protocol", v.protocol);
      Write(w, "cipher", v.cipher);
      Write(w, "compression", v.compression);
      Write(w, "publicKey", v.publicKey);
      Write(w, "sslVersion", v.sslVersion);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipAccount::SipTLSConnectionInfo& v)
    {
      Read(json, "certificateStatus", v.certificateStatus);
      Read(json, "issuer", v.issuer);
      Read(json, "server", v.server);
      Read(json, "peerNames", v.peerNames);
      Read(json, "protocol", v.protocol);
      Read(json, "cipher", v.cipher);
      Read(json, "compression", v.compression);
      Read(json, "publicKey", v.publicKey);
      Read(json, "sslVersion", v.sslVersion);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& v)
    {
      w.StartObject();
      Write(w, "accountStatus", v.accountStatus);
      Write(w, "reason", v.reason);
      Write(w, "signalingStatusCode", v.signalingStatusCode);
      Write(w, "failureRetryAfterSecs", v.failureRetryAfterSecs);
      Write(w, "signalingResponseText", v.signalingResponseText);
      Write(w, "accountBindingIpAddress", v.accountBindingIpAddress);
      Write(w, "rinstance", v.rinstance);
      Write(w, "transportType", v.transportType);
      Write(w, "tlsInfo", v.tlsInfo);
      Write(w, "ipVersionInUse", v.ipVersionInUse);
      Write(w, "serverIpAddress", v.serverIpAddress);
      Write(w, "serverPort", v.serverPort);
      Write(w, "localIpAddress", v.localIpAddress);
      Write(w, "localPort", v.localPort);
      Write(w, "localContactBinding", v.localContactBinding);
      Write(w, "responseTimeMs", v.responseTimeMs);
      Write(w, "callId", v.callId);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipAccount::SipAccountStatusChangedEvent& v)
    {
      Read(json, "accountStatus", v.accountStatus);
      Read(json, "reason", v.reason);
      Read(json, "signalingStatusCode", v.signalingStatusCode);
      Read(json, "failureRetryAfterSecs", v.failureRetryAfterSecs);
      Read(json, "signalingResponseText", v.signalingResponseText);
      Read(json, "accountBindingIpAddress", v.accountBindingIpAddress);
      Read(json, "rinstance", v.rinstance);
      Read(json, "transportType", v.transportType);
      Read(json, "tlsInfo", v.tlsInfo);
      Read(json, "ipVersionInUse", v.ipVersionInUse);
      Read(json, "serverIpAddress", v.serverIpAddress);
      Read(json, "serverPort", v.serverPort);
      Read(json, "localIpAddress", v.localIpAddress);
      Read(json, "localPort", v.localPort);
      Read(json, "localContactBinding", v.localContactBinding);
      Read(json, "responseTimeMs", v.responseTimeMs);
      Read(json, "callId", v.callId);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipAccount::ErrorEvent& v)
    {
      w.StartObject();
      Write(w, "errorText", v.errorText);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipAccount::ErrorEvent& v)
    {
      Read(json, "errorText", v.errorText);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipAccount::LicensingErrorEvent& v)
    {
      w.StartObject();
      Write(w, "errorText", v.errorText);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipAccount::LicensingErrorEvent& v)
    {
      Read(json, "errorText", v.errorText);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipAccount::SipAccountHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipAccount::SipAccountHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipAccount::SipAccountAdornmentEvent& v)
    {
      w.StartObject();
      Write(w, "adornmentMessageId", v.adornmentMessageId);
      Write(w, "target", v.target);
      Write(w, "method", v.method);
      Write(w, "responseCode", v.responseCode);
      Write(w, "message", v.message);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipAccount::SipAccountAdornmentEvent& v)
    {
      Read(json, "adornmentMessageId", v.adornmentMessageId);
      Read(json, "target", v.target);
      Read(json, "method", v.method);
      Read(json, "responseCode", v.responseCode);
      Read(json, "message", v.message);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipAccount::SipAccountAdornmentHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipAccount::SipAccountAdornmentHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipAccount::SipAccountState& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      Write(w, "accountStatus", v.accountStatus);
      Write(w, "settings", v.settings);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipAccount::SipAccountState& v)
    {
      Read(json, "account", v.account);
      Read(json, "accountStatus", v.accountStatus);
      Read(json, "settings", v.settings);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::SipAccount::SipAccountStateManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::SipAccount::SipAccountStateManager& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // SipAccount_JSON_HELPER_H