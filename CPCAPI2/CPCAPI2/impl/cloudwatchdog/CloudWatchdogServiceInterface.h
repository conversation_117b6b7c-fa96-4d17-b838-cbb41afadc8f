#pragma once

#if !defined(CPCAPI2_CLOUD_WATCHDOG_SERVICE_INTERFACE_H)
#define CPCAPI2_CLOUD_WATCHDOG_SERVICE_INTERFACE_H

#include "cpcapi2defs.h"
#include "interface/experimental/cloudwatchdog/CloudWatchdogServiceHandler.h"
#include "cloudwatchdog/CloudWatchdogService.h"
#include "orchestration_server/OrchestrationServer.h"
#include "orchestration_server/OrchestrationServerHandler.h"
#include "orchestration_server/OrchestrationServerInterface.h"
#include "phone/Cpcapi2EventSource.h"

#include "phone/PhoneModule.h"
#include "util/DumFpCommand.h"
#include "util/AutoTestProcessor.h"

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>
#include <rutil/DeadlineTimer.hxx>

#include <map>
#include <list>
#include <thread>

namespace CPCAPI2
{

class PhoneInterface;

namespace CloudWatchdog
{

class CloudWatchdogSyncHandler {};

class CloudWatchdogServiceInterface : public CloudWatchdogService,
                                      public PhoneModule,
                                      public CPCAPI2::EventSource<CloudWatchdogHandle, CloudWatchdogHandler, CloudWatchdogSyncHandler>,
                                      public resip::DeadlineTimerHandler,
                                      public CPCAPI2::OrchestrationServer::OrchestrationServerHandler,
                                      public CPCAPI2::OrchestrationServer::OrchestrationServerSyncHandler
{

public:

   CloudWatchdogServiceInterface(Phone* phone);
   virtual ~CloudWatchdogServiceInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // CloudWatchdogService
   virtual int start(const CloudWatchdogConfig& serverConfig = CloudWatchdogConfig()) OVERRIDE;
   virtual int shutdown() OVERRIDE;
   virtual int setHandler(CloudWatchdogHandler* handler) OVERRIDE;

   // OrchestrationServerHandler
   virtual int onSetServerInfoResult(int requestHandle, const CPCAPI2::OrchestrationServer::SetServerInfoResult& args) OVERRIDE;
   virtual int onRequestServiceResult(int requestHandle, const CPCAPI2::OrchestrationServer::RequestServiceResult& args) OVERRIDE;
   virtual int onQueryServersResult(int requestHandle, const CPCAPI2::OrchestrationServer::QueryServersResult& args) OVERRIDE;
   virtual int onQueryServerTtlResult(int requestHandle, const CPCAPI2::OrchestrationServer::QueryServerTtlResult& args) OVERRIDE;
   virtual int onQueryServerUsersResult(int requestHandle, const CPCAPI2::OrchestrationServer::QueryServerUsersResult& args) OVERRIDE;

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

private:

   void startImpl(const CloudWatchdogConfig& serverConfig);
   void shutdownImpl();

private:

   PhoneInterface* mPhone;
   CPCAPI2::OrchestrationServer::OrchestrationServer* mOrchServer;

   typedef std::map<cpc::string, CPCAPI2::OrchestrationServer::ServerInfo> ServerList; // key is uri and associated services, e.g. <uri>.xmppagent
   ServerList mLastServerList;

   /*
   struct NotificationServerInfo
   {
      cpc::string uri;
      bool serverDown;
      bool missedPush;

      NotificationServerInfo() : uri(""), serverDown(false), missedPush(false) {}
      NotificationServerInfo(cpc::string uri_, bool down) : uri(uri_), serverDown(down), missedPush(false) {}
      NotificationServerInfo(cpc::string uri_, bool down, bool missed) : uri(uri_), serverDown(down), missedPush(missed) {}
   };
   */

   struct NotificationServerInfo
   {
      enum NotificationType
      {
         REGISTER
      };

      cpc::string uri;
      NotificationType type;
      NotificationServerInfo(cpc::string uri_) : uri(uri_), type(REGISTER) {}
      NotificationServerInfo(cpc::string uri_, NotificationType type_) : uri(uri_), type(type_) {}
   };

   typedef std::list<NotificationServerInfo> NotificationServerList;
   NotificationServerList mNotificationServerList;
   
   typedef std::list<MonitoredServerInfo> SetInfoServerList;
   SetInfoServerList mSetInfoServerList;

   bool mStartupQuery;

   resip::DeadlineTimer<resip::MultiReactor> mTimer;
   CloudWatchdogConfig mConfig;

};

}

}

#endif // CPCAPI2_CLOUD_WATCHDOG_SERVICE_INTERFACE_H
