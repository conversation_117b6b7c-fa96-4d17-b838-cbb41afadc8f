#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOSTARTMONITORINGCAPTUREDEVICELEVELS_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOSTARTMONITORINGCAPTUREDEVICELEVELS_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
  class AudioStartMonitoringCaptureDeviceLevels
  {
  public:
    int32_t phoneHandle;
    int32_t deviceId;

    AudioStartMonitoringCaptureDeviceLevels() = default;

    AudioStartMonitoringCaptureDeviceLevels(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const AudioStartMonitoringCaptureDeviceLevels& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["deviceId"] = o.deviceId;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, AudioStartMonitoringCaptureDeviceLevels& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.deviceId = j.at("deviceId");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOSTARTMONITORINGCAPTUREDEVICELEVELS_H
