#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_PHONE_PHONECREATE_H
#define JSONRPC_CPCAPI2_PHONE_PHONECREATE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  class PhoneCreate
  {
  public:
    PhoneCreate() = default;

    PhoneCreate(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const PhoneCreate& o)
    {
      j = nlohmann::json::object();
   
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, PhoneCreate& o)
    {
 
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_PHONECREATE_H
