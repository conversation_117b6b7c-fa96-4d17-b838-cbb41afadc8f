#include "brand_branded.h"

#if (CPCAPI2_BRAND_CLOUD_RELAY_MODULE == 1)
#include "cpcapi2utils.h"
#include "interface/public/media/video/Video.h"
#include "cloudrelay/CloudRelayHandler.h"
#include "CloudRelayManagerImpl.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "jsonapi/JsonApiServerSendTransportInterface.h"
#include "jsonapi/JsonApiServer_HTTP.h"

#include <rutil/Random.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

#include <time.h>

using namespace resip;

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER
#define CONFBRIDGE_MANAGER_IMPL_SNAPSHOT_TIMER_ID 1

namespace CPCAPI2
{
namespace CloudRelay
{
CloudRelayManagerImpl::CloudRelayManagerImpl(CloudRelayHandle h, CPCAPI2::Phone* phone, CloudRelayManagerInterface* confBridgeMgrIf)
   : mHandle(h), mPhone(phone), mConfBridgeMgrIf(confBridgeMgrIf), mShutdown(false),
     mAppHandler(NULL)
{
}

CloudRelayManagerImpl::~CloudRelayManagerImpl()
{
   mShutdown = true;
}

void CloudRelayManagerImpl::setSdkObservers(const std::set<CloudRelayHandler*>* observers)
{
   mSdkObservers = observers;
}

int CloudRelayManagerImpl::setHandler(CloudRelayHandler* handler)
{
   mAppHandler = handler;
   return 0;
}

void CloudRelayManagerImpl::postCallback(resip::ReadCallbackBase* rcb)
{
   mConfBridgeMgrIf->postCallback(rcb);
}

void CloudRelayManagerImpl::broadcast(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const cpc::string& msg)
{
   BroadcastEvent args;
   args.jsonApiUser = jsonApiUser;
   args.msg = msg;
   fireEvent(cpcFunc(CloudRelayHandler::onBroadcast), args);
}

void CloudRelayManagerImpl::sendTo(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const cpc::string& msg)
{
   MessageEvent args;
   args.jsonApiUser = jsonApiUser;
   args.msg = msg;
   fireEvent(cpcFunc(CloudRelayHandler::onMessage), args);
}

}
}

#endif
