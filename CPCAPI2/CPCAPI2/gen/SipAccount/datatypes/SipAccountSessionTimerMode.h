#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTSESSIONTIMERMODE_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTSESSIONTIMERMODE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  enum class SipAccountSessionTimerMode
  {
    Inactive,
    Optional,
    Required,
    Always
  };

  inline std::string to_string(SipAccountSessionTimerMode value)
  {
    switch (value)
    {
      case SipAccountSessionTimerMode::Inactive:
        return "Inactive";
      case SipAccountSessionTimerMode::Optional:
        return "Optional";
      case SipAccountSessionTimerMode::Required:
        return "Required";
      case SipAccountSessionTimerMode::Always:
        return "Always";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, SipAccountSessionTimerMode value)
  {
    switch (value)
    {
      case SipAccountSessionTimerMode::Inactive:
        os << "Inactive";
        break;
      case SipAccountSessionTimerMode::Optional:
        os << "Optional";
        break;
      case SipAccountSessionTimerMode::Required:
        os << "Required";
        break;
      case SipAccountSessionTimerMode::Always:
        os << "Always";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const SipAccountSessionTimerMode& e)
  {
    switch (e)
    {
      case SipAccountSessionTimerMode::Inactive:
        j = "Inactive";
        break;
      case SipAccountSessionTimerMode::Optional:
        j = "Optional";
        break;
      case SipAccountSessionTimerMode::Required:
        j = "Required";
        break;
      case SipAccountSessionTimerMode::Always:
        j = "Always";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, SipAccountSessionTimerMode& e)
  {
      if (0 == j.get<std::string>().compare("Inactive"))
    {
      e = SipAccountSessionTimerMode::Inactive;
    }
    else if (0 == j.get<std::string>().compare("Optional"))
    {
      e = SipAccountSessionTimerMode::Optional;
    }
    else if (0 == j.get<std::string>().compare("Required"))
    {
      e = SipAccountSessionTimerMode::Required;
    }
    else if (0 == j.get<std::string>().compare("Always"))
    {
      e = SipAccountSessionTimerMode::Always;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum SipAccountSessionTimerMode"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTSESSIONTIMERMODE_H
