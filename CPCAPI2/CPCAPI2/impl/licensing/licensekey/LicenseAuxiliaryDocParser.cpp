#include <sstream>

#include "LicenseAuxiliaryDocParser.h"
#include "../../util/libXmlHelper.h"

namespace CPCAPI2
{
namespace Licensing
{

   LicenseAuxiliaryDocParser::LicenseAuxiliaryDocParser(const std::string& xml)
   {
      Parse(xml);
   }

   void
   LicenseAuxiliaryDocParser::Parse(const std::string& xml)
   {
      m_AccumulatedRuntime = std::string();
      m_CheckTime = std::string();
      int ret = 0;

      xmlTextReaderPtr reader = xmlReaderForMemory(xml.data(), (int)xml.size(), NULL, "UTF-8", 0);

      if (reader != NULL)
      {
         ret = xmlTextReaderRead(reader);
         while (ret == 1)
         {
				std::string nodeName = xmlString(xmlTextReaderName(reader));
            if (nodeName == "art" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
            {
               m_AccumulatedRuntime = xmlGetElementText(reader);               
            }
            else if (nodeName == "checkTime" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
            {
               m_CheckTime = xmlGetElementText(reader);
            }         

            ret = xmlTextReaderRead(reader);
         }

         xmlFreeTextReader(reader);
      }
   }

}
}