#include "brand_branded.h"

#if (CPCAPI2_BRAND_AUDIO_TRANS_MODULE == 1)
#include "AudioTransImpl.h"
#include "AudioTransInterface.h"
#include "media/MediaManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "auth_server/AuthServerJwtUtils.h"

#include <MediaStackImpl.hxx>

// WebRTC
#include <vie_codec.h>
#include <voe_base.h>
#include <vie_base.h>
#include <vie_rtp_rtcp.h>
#include <webrtc/video_engine/vie_encoder.h>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>
#include <sstream>

// grpc
#include <grpc++/grpc++.h>

// Google Cloud Speech API
#include "google/cloud/speech/v1/cloud_speech.grpc.pb.h"

#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

using namespace CPCAPI2::Media;
using google::cloud::speech::v1::RecognitionConfig;
using google::cloud::speech::v1::Speech;
using google::cloud::speech::v1::StreamingRecognizeRequest;
using google::cloud::speech::v1::StreamingRecognizeResponse;

namespace CPCAPI2
{
namespace AudioTrans
{
AudioTransImpl::AudioTransImpl(CPCAPI2::PhoneInterface* phone, AudioTransInterface* ccif, AudioTransHandle h)
   : mPhone(phone),
     mMediaManager(dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone))),
     mInterface(ccif),
     mHandle(h),
     mCurrentResultSetId(1),
     mShouldReadTranscribeResults(true)
{
}

AudioTransImpl::~AudioTransImpl()
{
}

void AudioTransImpl::setAudioTransSettings(const AudioTransSettings& settings)
{
   mSettings = settings;
}

class TranscriptionEncodedAudioObserver : public webrtc::VoEMediaProcess
{
public:
   TranscriptionEncodedAudioObserver(AudioTransImpl* audioTransImpl)
      : mAudioTrans(audioTransImpl), mAudioBuffIdx(0), mNumSamplesVadPassive(0) 
   {
      // Create a Speech Stub connected to the speech service.
      mCreds = grpc::GoogleDefaultCredentials();
      mGrpcChannel = grpc::CreateChannel("speech.googleapis.com", mCreds);
      mSpeech.reset(Speech::NewStub(mGrpcChannel).release());
   }
   virtual ~TranscriptionEncodedAudioObserver() {}

   void InitGoogleSpeech()
   {
      InfoLog(<< "InitGoogleSpeech");
      mContext.reset(new grpc::ClientContext);

      // Parse command line arguments.
      StreamingRecognizeRequest configRequest;
      auto* streaming_config = configRequest.mutable_streaming_config();
      RecognitionConfig* config = streaming_config->mutable_config();

      InfoLog(<< "InitGoogleSpeech - set config");
      config->set_language_code("en");
      config->set_sample_rate_hertz(16000);  // Default sample rate.
      config->set_encoding(RecognitionConfig::LINEAR16);

      InfoLog(<< "InitGoogleSpeech - call StreamingRecognize");
      // Begin a stream.
      mStreamer = mSpeech->StreamingRecognize(mContext.get());
      // Write the first request, containing the config only.
      streaming_config->set_interim_results(true);
      InfoLog(<< "InitGoogleSpeech - write config request");
      mStreamer->Write(configRequest);
   }

   virtual void Process(int channel,
      webrtc::ProcessingTypes type,
      webrtc::AudioFrame* audioFrame)
   {
      mResampler.InitializeIfNeeded(audioFrame->sample_rate_hz_, 16000, 1);
      int numSamples = mResampler.Resample(audioFrame->data_, audioFrame->samples_per_channel_*audioFrame->num_channels_, &mAudioBuff[mAudioBuffIdx], webrtc::AudioFrame::kMaxDataSizeSamples) / audioFrame->num_channels_;
      mAudioBuffIdx += numSamples;
      if (mAudioBuffIdx >= 32800)
      {
         mRequest.set_audio_content(&mAudioBuff[0], 32800 * 2);
         InfoLog(<< "write streaming recognize request");
         mStreamer->Write(mRequest);
         mAudioBuffIdx = 0;

         if (audioFrame->vad_activity_ == webrtc::AudioFrame::kVadPassive)
         {
            mNumSamplesVadPassive += numSamples;

            if (mNumSamplesVadPassive >= 640)
            {
               mNumSamplesVadPassive = 0;
               Reset();
               //mAudioTrans->post(resip::resip_bind(&TranscriptionEncodedAudioObserver::Reset, this));
            }
         }
         else
         {
            mNumSamplesVadPassive = 0;
         }
      }
   }

   void Reset()
   {
      InfoLog(<< "writes done");
      mStreamer->WritesDone();
      mAudioTrans->stopTranscriptionInternal();
      //mStreamer->Finish();
      mStreamer.reset();
      mAudioBuffIdx = 0;
      InitGoogleSpeech();
      mAudioTrans->restartTranscription();
   }

   grpc::ClientReaderWriterInterface<StreamingRecognizeRequest,
      StreamingRecognizeResponse>* streamer() const
   {
      return mStreamer.get();
   }

   //virtual void OnEncodedAudio(
   //   uint32_t timeStamp,
   //   const uint8_t* payloadData,
   //   size_t payloadSize)
   //{
   //   StreamingRecognizeRequest request;
   //   request.set_audio_content(payloadData, payloadSize);
   //   mStreamer->Write(request);
   //}

private:
   AudioTransImpl* mAudioTrans;
   webrtc::PushResampler<int16_t> mResampler;
   //webrtc::AudioFrame mResampledAudio;
   int16_t mAudioBuff[32800]; // just over 64kb, and evenly divisible by 320 since we get 320 bytes in each frame of audio from webrtc
   int mAudioBuffIdx;
   StreamingRecognizeRequest mRequest;
   std::unique_ptr<google::cloud::speech::v1::Speech::Stub> mSpeech;
   std::shared_ptr<grpc::Channel> mGrpcChannel;
   std::shared_ptr<grpc::ClientReaderWriterInterface<StreamingRecognizeRequest, StreamingRecognizeResponse> > mStreamer;
   std::shared_ptr<grpc::ChannelCredentials> mCreds;
   std::unique_ptr<grpc::ClientContext> mContext;
   int mNumSamplesVadPassive;
};

void AudioTransImpl::startTranscription()
{
   if (mSettings.audioRecvChannel >= 0 && mEncodedAudioObs.get() == NULL)
   {
      mEncodedAudioObs.reset(new TranscriptionEncodedAudioObserver(this));
      mEncodedAudioObs->InitGoogleSpeech();

      std::shared_ptr<webrtc_recon::MediaStackImpl> mediaStack = mMediaManager->media_stack_ptr();
      std::shared_ptr<webrtc_recon::MixerImpl> mixerImpl = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mediaStack->mixer());
      mixerImpl->registerDecodedAudioObserver(mSettings.audioRecvChannel, mEncodedAudioObs.get());

      mTransResultsThread.reset(new std::thread(&AudioTransImpl::transcriptionResultsThread, this));
   }
}

void AudioTransImpl::stopTranscriptionInternal()
{
   InfoLog(<< "stopTranscriptionInternal()");
   //mStreamer->WritesDone();
   mShouldReadTranscribeResults = false;
   mTransResultsThread->join();
   mTransResultsThread.reset();
   //mStreamer.reset();
}

void AudioTransImpl::stopTranscription()
{
   InfoLog(<< "stopTranscription()");
   //mStreamer->WritesDone();
   mShouldReadTranscribeResults = false;
   mEncodedAudioObs->streamer()->WritesDone();

   std::shared_ptr<webrtc_recon::MediaStackImpl> mediaStack = mMediaManager->media_stack_ptr();
   std::shared_ptr<webrtc_recon::MixerImpl> mixerImpl = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mediaStack->mixer());
   mixerImpl->unregisterDecodedAudioObserver(mSettings.audioRecvChannel, mEncodedAudioObs.get());

   mTransResultsThread->join();
   mTransResultsThread.reset();
   //mStreamer.reset();
}

void AudioTransImpl::restartTranscription()
{
   InfoLog(<< "restartTranscription()");
   mShouldReadTranscribeResults = true;
   mTransResultsThread.reset(new std::thread(&AudioTransImpl::transcriptionResultsThread, this));
}

void AudioTransImpl::post(resip::ReadCallbackBase* rcb)
{
   mInterface->post(rcb);
}

void AudioTransImpl::HandleEncodedAudio(uint32_t timeStamp,
   const uint8_t* payloadData,
   size_t payloadSize)
{
}

void AudioTransImpl::transcriptionResultsThread()
{
   InfoLog(<< "transcriptionResultsThread()");

   grpc::ClientReaderWriterInterface<google::cloud::speech::v1::StreamingRecognizeRequest, google::cloud::speech::v1::StreamingRecognizeResponse>* streamer = mEncodedAudioObs->streamer();

   // Read responses.
   StreamingRecognizeResponse response;
   while (mShouldReadTranscribeResults) {  
      if (streamer->Read(&response)) { // Returns false when no more to read.
         InfoLog(<< "Read " << response.results_size() << " responses");
         for (int r = 0; r < response.results_size(); ++r) {
            auto result = response.results(r);
            InfoLog(<< "Result stability for result " << r << ": " << result.stability());
            //std::cout << "Result stability: " << result.stability() << std::endl;
            for (int a = 0; a < result.alternatives_size(); ++a) {
               auto alternative = result.alternatives(a);
               //InfoLog(<< alternative.confidence() << "\t" << alternative.transcript());

               AudioTranscriptionResult evt;
               evt.resultSetId = mCurrentResultSetId;
               evt.participantId = mSettings.audioRecvChannel;
               evt.result = alternative.transcript().c_str();
               mInterface->post(resip::resip_bind(&AudioTransInterface::fireEventInternal, mInterface, mHandle, evt));
               InfoLog(<< "Firing transcription result event for alternative " << a << ": " << evt.resultSetId << " - " << evt.result);
               //std::cout << alternative.confidence() << "\t"
               //   << alternative.transcript() << std::endl;
            }

            if (result.is_final())
            {
               InfoLog(<< "Result is final");
               mCurrentResultSetId++;
            }
         }
      }
      else {
         std::this_thread::sleep_for(std::chrono::milliseconds(200));
      }
   }

   InfoLog(<< "transcriptionResultsThread() - exit");

   grpc::Status status = streamer->Finish();
   if (!status.ok()) {
      // Report the RPC failure.
      ErrLog(<< "AudioTranscription error: " << status.error_message());
      //std::cerr << status.error_message() << std::endl;
      //return -1;
   }
}


}
}

#endif // CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE
