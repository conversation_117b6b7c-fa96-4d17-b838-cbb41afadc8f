#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_TRANSFERPROGRESSEVENTTYPE_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_TRANSFERPROGRESSEVENTTYPE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  enum class TransferProgressEventType
  {
    Trying,
    Ringing,
    Connected,
    Redirected,
    Failed
  };

  inline std::string to_string(TransferProgressEventType value)
  {
    switch (value)
    {
      case TransferProgressEventType::Trying:
        return "Trying";
      case TransferProgressEventType::Ringing:
        return "Ringing";
      case TransferProgressEventType::Connected:
        return "Connected";
      case TransferProgressEventType::Redirected:
        return "Redirected";
      case TransferProgressEventType::Failed:
        return "Failed";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, TransferProgressEventType value)
  {
    switch (value)
    {
      case TransferProgressEventType::Trying:
        os << "Trying";
        break;
      case TransferProgressEventType::Ringing:
        os << "Ringing";
        break;
      case TransferProgressEventType::Connected:
        os << "Connected";
        break;
      case TransferProgressEventType::Redirected:
        os << "Redirected";
        break;
      case TransferProgressEventType::Failed:
        os << "Failed";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const TransferProgressEventType& e)
  {
    switch (e)
    {
      case TransferProgressEventType::Trying:
        j = "Trying";
        break;
      case TransferProgressEventType::Ringing:
        j = "Ringing";
        break;
      case TransferProgressEventType::Connected:
        j = "Connected";
        break;
      case TransferProgressEventType::Redirected:
        j = "Redirected";
        break;
      case TransferProgressEventType::Failed:
        j = "Failed";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, TransferProgressEventType& e)
  {
      if (0 == j.get<std::string>().compare("Trying"))
    {
      e = TransferProgressEventType::Trying;
    }
    else if (0 == j.get<std::string>().compare("Ringing"))
    {
      e = TransferProgressEventType::Ringing;
    }
    else if (0 == j.get<std::string>().compare("Connected"))
    {
      e = TransferProgressEventType::Connected;
    }
    else if (0 == j.get<std::string>().compare("Redirected"))
    {
      e = TransferProgressEventType::Redirected;
    }
    else if (0 == j.get<std::string>().compare("Failed"))
    {
      e = TransferProgressEventType::Failed;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum TransferProgressEventType"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_TRANSFERPROGRESSEVENTTYPE_H
