#include "jsonrpc/StringSerialization.h"
#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>
#include "TlsVersion.h"

#ifndef JSONRPC_CPCAPI2_PHONE_SSLCIPHEROPTIONS_H
#define JSONRPC_CPCAPI2_PHONE_SSLCIPHEROPTIONS_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  class SslCipherOptions
  {
  public:
    std::optional<cpc::string> mHttpCiphers;
    std::optional<cpc::string> mSipCiphers;
    std::optional<cpc::string> mXmppCiphers;
    std::optional<cpc::string> mWebSocketsCiphers;
    std::optional<cpc::string> mDtlsSrtpCiphers;
    std::optional<TlsVersion> mHttpTlsVersion;
    std::optional<TlsVersion> mSipTlsVersion;
    std::optional<TlsVersion> mXmppTlsVersion;
    std::optional<TlsVersion> mWebSocketsTlsVersion;
    std::optional<TlsVersion> mDtlsSrtpTlsVersion;

    SslCipherOptions() = default;

    SslCipherOptions(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SslCipherOptions& o)
    {
      j = nlohmann::json::object();
       if (o.mHttpCiphers) { j["mHttpCiphers"] = *(o.mHttpCiphers); }
     if (o.mSipCiphers) { j["mSipCiphers"] = *(o.mSipCiphers); }
     if (o.mXmppCiphers) { j["mXmppCiphers"] = *(o.mXmppCiphers); }
     if (o.mWebSocketsCiphers) { j["mWebSocketsCiphers"] = *(o.mWebSocketsCiphers); }
     if (o.mDtlsSrtpCiphers) { j["mDtlsSrtpCiphers"] = *(o.mDtlsSrtpCiphers); }
     if (o.mHttpTlsVersion) { j["mHttpTlsVersion"] = *(o.mHttpTlsVersion); }
     if (o.mSipTlsVersion) { j["mSipTlsVersion"] = *(o.mSipTlsVersion); }
     if (o.mXmppTlsVersion) { j["mXmppTlsVersion"] = *(o.mXmppTlsVersion); }
     if (o.mWebSocketsTlsVersion) { j["mWebSocketsTlsVersion"] = *(o.mWebSocketsTlsVersion); }
     if (o.mDtlsSrtpTlsVersion) { j["mDtlsSrtpTlsVersion"] = *(o.mDtlsSrtpTlsVersion); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SslCipherOptions& o)
    {
     if (j.contains("mHttpCiphers")) { o.mHttpCiphers = j.at("mHttpCiphers"); }
     if (j.contains("mSipCiphers")) { o.mSipCiphers = j.at("mSipCiphers"); }
     if (j.contains("mXmppCiphers")) { o.mXmppCiphers = j.at("mXmppCiphers"); }
     if (j.contains("mWebSocketsCiphers")) { o.mWebSocketsCiphers = j.at("mWebSocketsCiphers"); }
     if (j.contains("mDtlsSrtpCiphers")) { o.mDtlsSrtpCiphers = j.at("mDtlsSrtpCiphers"); }
     if (j.contains("mHttpTlsVersion")) { o.mHttpTlsVersion = j.at("mHttpTlsVersion"); }
     if (j.contains("mSipTlsVersion")) { o.mSipTlsVersion = j.at("mSipTlsVersion"); }
     if (j.contains("mXmppTlsVersion")) { o.mXmppTlsVersion = j.at("mXmppTlsVersion"); }
     if (j.contains("mWebSocketsTlsVersion")) { o.mWebSocketsTlsVersion = j.at("mWebSocketsTlsVersion"); }
     if (j.contains("mDtlsSrtpTlsVersion")) { o.mDtlsSrtpTlsVersion = j.at("mDtlsSrtpTlsVersion"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_SSLCIPHEROPTIONS_H
