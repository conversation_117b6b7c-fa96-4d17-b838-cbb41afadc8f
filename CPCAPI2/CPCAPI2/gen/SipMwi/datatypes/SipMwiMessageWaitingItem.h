#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>
#include "MwiType.h"

#ifndef JSONRPC_CPCAPI2_SIPMWI_SIPMWIMESSAGEWAITINGITEM_H
#define JSONRPC_CPCAPI2_SIPMWI_SIPMWIMESSAGEWAITINGITEM_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipMwi {
  class SipMwiMessageWaitingItem
  {
  public:
    std::optional<MwiType> mwiType;
    std::optional<int32_t> newMessageCount;
    std::optional<int32_t> oldMessageCount;
    std::optional<int32_t> newUrgentMessageCount;
    std::optional<int32_t> oldUrgentMessageCount;

    SipMwiMessageWaitingItem() = default;

    SipMwiMessageWaitingItem(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipMwiMessageWaitingItem& o)
    {
      j = nlohmann::json::object();
       if (o.mwiType) { j["mwiType"] = *(o.mwiType); }
     if (o.newMessageCount) { j["newMessageCount"] = *(o.newMessageCount); }
     if (o.oldMessageCount) { j["oldMessageCount"] = *(o.oldMessageCount); }
     if (o.newUrgentMessageCount) { j["newUrgentMessageCount"] = *(o.newUrgentMessageCount); }
     if (o.oldUrgentMessageCount) { j["oldUrgentMessageCount"] = *(o.oldUrgentMessageCount); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipMwiMessageWaitingItem& o)
    {
     if (j.contains("mwiType")) { o.mwiType = j.at("mwiType"); }
     if (j.contains("newMessageCount")) { o.newMessageCount = j.at("newMessageCount"); }
     if (j.contains("oldMessageCount")) { o.oldMessageCount = j.at("oldMessageCount"); }
     if (j.contains("newUrgentMessageCount")) { o.newUrgentMessageCount = j.at("newUrgentMessageCount"); }
     if (j.contains("oldUrgentMessageCount")) { o.oldUrgentMessageCount = j.at("oldUrgentMessageCount"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipMwi
#endif // JSONRPC_CPCAPI2_SIPMWI_SIPMWIMESSAGEWAITINGITEM_H
