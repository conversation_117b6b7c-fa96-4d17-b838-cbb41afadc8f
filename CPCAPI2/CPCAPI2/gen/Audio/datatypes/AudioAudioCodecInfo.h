#include "jsonrpc/StringSerialization.h"
#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIOCODECINFO_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIOCODECINFO_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
  class AudioAudioCodecInfo
  {
  public:
    std::optional<cpc::string> codecName;
    std::optional<int32_t> id;
    std::optional<bool> enabled;
    std::optional<int32_t> samplingRate;
    std::optional<int32_t> minBandwidth;
    std::optional<int32_t> maxBandwidth;
    std::optional<int32_t> priority;
    std::optional<int32_t> payloadType;

    AudioAudioCodecInfo() = default;

    AudioAudioCodecInfo(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const AudioAudioCodecInfo& o)
    {
      j = nlohmann::json::object();
       if (o.codecName) { j["codecName"] = *(o.codecName); }
     if (o.id) { j["id"] = *(o.id); }
     if (o.enabled) { j["enabled"] = *(o.enabled); }
     if (o.samplingRate) { j["samplingRate"] = *(o.samplingRate); }
     if (o.minBandwidth) { j["minBandwidth"] = *(o.minBandwidth); }
     if (o.maxBandwidth) { j["maxBandwidth"] = *(o.maxBandwidth); }
     if (o.priority) { j["priority"] = *(o.priority); }
     if (o.payloadType) { j["payloadType"] = *(o.payloadType); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, AudioAudioCodecInfo& o)
    {
     if (j.contains("codecName")) { o.codecName = j.at("codecName"); }
     if (j.contains("id")) { o.id = j.at("id"); }
     if (j.contains("enabled")) { o.enabled = j.at("enabled"); }
     if (j.contains("samplingRate")) { o.samplingRate = j.at("samplingRate"); }
     if (j.contains("minBandwidth")) { o.minBandwidth = j.at("minBandwidth"); }
     if (j.contains("maxBandwidth")) { o.maxBandwidth = j.at("maxBandwidth"); }
     if (j.contains("priority")) { o.priority = j.at("priority"); }
     if (j.contains("payloadType")) { o.payloadType = j.at("payloadType"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIOCODECINFO_H
