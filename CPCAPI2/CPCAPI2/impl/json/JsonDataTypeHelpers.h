#pragma once

#ifndef JSON_DATA_TYPE_HELPERS_H
#define JSON_DATA_TYPE_HELPERS_H

#include "datatypes/SipJsonHelper.h"
#include "datatypes/ConferenceBridgeJsonHelper.h"
#include "datatypes/CloudRelayJsonHelper.h"
#include "datatypes/ConferenceConnectorJsonHelper.h"
#include "datatypes/LicensingJsonHelper.h"
#include "datatypes/MediaJsonHelper.h"
#include "datatypes/PeerConnectionJsonHelper.h"
#include "datatypes/PushEndpointJsonHelper.h"
#include "datatypes/PushNotificationJsonHelper.h"
#include "datatypes/PushToTalkJsonHelper.h"
#include "remotesync/commands/Command.h"
#include "datatypes/RemoteSyncJsonHelper.h"
#include "datatypes/SipAccountJsonHelper.h"
#include "datatypes/SipConversationJsonHelper.h"
#include "datatypes/SipMessageWaitingIndicationJsonHelper.h"
#include "datatypes/VCCSJsonHelper.h"
#include "datatypes/WebSocketJsonHelper.h"
#include "datatypes/XmppAccountJsonHelper.h"
#include "datatypes/XmppAgentJsonHelper.h"
#include "datatypes/XmppChatJsonHelper.h"
#include "datatypes/XmppFileTransferJsonHelper.h"
#include "datatypes/XmppIMCommandJsonHelper.h"
#include "datatypes/XmppMultiUserChatJsonHelper.h"
#include "datatypes/XmppRosterJsonHelper.h"
#include "datatypes/XmppVCardJsonHelper.h"

#endif // JSON_DATA_TYPE_HELPERS_H
