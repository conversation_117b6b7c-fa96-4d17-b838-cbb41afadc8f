#pragma once

#include "cpcapi2defs.h"
#include "event/SipEventManager.h"
#include "event/SipEventSubscriptionHandler.h"
#include "SipEventManagerImpl.h"
#include <map>
#include <resip/dum/AppDialogSet.hxx>
#include <resip/dum/ClientSubscription.hxx>

namespace CPCAPI2
{
namespace SipEvent
{
   class SipEventSubscriptionCreationInfo : public resip::AppDialogSet
   {
   public:
      CPCAPI2::SipAccount::SipAccountHandle account;
      cpc::vector<resip::NameAddr>          targetAddresses;
      resip::ClientSubscriptionHandle  dumClientSubscriptionHandle;
      resip::ServerSubscriptionHandle  dumServerSubscriptionHandle;
      resip::Data                      eventType;
      cpc::vector<Parameter>           eventPackageParams;
      SipEventSubscriptionHandle       sdkh;
      SipSubscriptionState             subscriptionState;
      unsigned int                     expiresSeconds;
      cpc::vector<CPCAPI2::MimeType>   supportedMimeTypes;
      bool                             displaced;
      bool                             sharedCallAppearanceExtensionsEnabled;
      bool                             started;
      // the following is used to disable NOTIFY subscription retry inside SipEventManager when it's handled elsewhere
      bool                             enableNotifyTerminationRetryHandling;

      SipEventSubscriptionCreationInfo(resip::DialogUsageManager& dum, SipEventManagerImpl* evtMgr)
         : AppDialogSet(dum), eventManagerImpl(evtMgr)
      {
         account = 0;
         sdkh = 0;
         dumClientSubscriptionHandle = resip::ClientSubscriptionHandle::NotValid();
         dumServerSubscriptionHandle = resip::ServerSubscriptionHandle::NotValid();
         subscriptionState = SipSubscriptionState_NotStarted;
         expiresSeconds = 3600;
         displaced = false;
         sharedCallAppearanceExtensionsEnabled = false;
         started = false;
         enableNotifyTerminationRetryHandling = true;
      }
         
      virtual ~SipEventSubscriptionCreationInfo()
      {
         if (!displaced)
         {
            eventManagerImpl->removeCreationInfo(sdkh);
         }
      }

      void cleanup()
      {
         if (!mDum.findAppDialogSet(getDialogSetId()).isValid())
         {
            // DUM doesn't know about this, so we have to clean up manually
            delete this;
         } 
         else if (dumClientSubscriptionHandle.isValid())
         {
            // 'immediate' means don't try to un-SUBSCRIBE, just kill the ClientSubscription
            dumClientSubscriptionHandle->end(true);
         } 
         else if (dumServerSubscriptionHandle.isValid())
         {
            dumServerSubscriptionHandle->end(resip::Deactivated);
         }
         else
         {
            // can happen if we are a client but server has not sent NOTIFY response to initial SUBSCRIBE.
            // call end() to help avoid delays on SipAccount disable
            end();
         }
      }

   private:
      SipEventManagerImpl* eventManagerImpl;
   };

   class SipEventSubscriptionHandleFactory
   {
   public:
      static SipEventSubscriptionHandle getNext() { return sNextHandle++; }
   private:
      static SipEventSubscriptionHandle sNextHandle;
   };
}
}
