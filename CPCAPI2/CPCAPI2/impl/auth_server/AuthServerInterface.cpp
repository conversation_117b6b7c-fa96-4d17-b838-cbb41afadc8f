#include "brand_branded.h"

#if (CPCAPI2_BRAND_AUTH_SERVER_MODULE == 1)
#include "cpcapi2utils.h"
#include "AuthServerInterface.h"
#include "../experimental/auth_server/AuthServerHandler.h"
#include "AuthServerJwtUtils.h"
#include "AuthServerDbAccess.h"
#include "AuthServerStrettoProvAccess.h"
#include "phone/PhoneInterface.h"
#include "util/cpc_logger.h"
#include "util/HttpsServerImpl.h"
#include "util/DumFpCommand.h"

// rapidjson
#include <stringbuffer.h>
#include <writer.h>

#include <functional>
#include <iomanip>

using namespace resip;

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

namespace CPCAPI2
{
namespace AuthServer
{
AuthServerInterface::AuthServerInterface(Phone* phone) :
mShutdown(false),
mDropLoginRequests(false),
mDropAddUserRequests(false),
mPhone(dynamic_cast<PhoneInterface*>(phone)),
mAppHandler(NULL),
mServerThread(NULL)
{
}

AuthServerInterface::~AuthServerInterface()
{
   mShutdown = true;
   interruptProcess();
}

void AuthServerInterface::Release()
{
   delete this;
}

int AuthServerInterface::process(unsigned int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return kRemoteControlModuleDisabled;
   }
   ReadCallbackBase* fp = mCallbackFifo.getNext(timeout);
   while(fp)
   {
      (*fp)();
      delete fp;
      if (mShutdown)
      {
         return kRemoteControlModuleDisabled;
      }
      fp = mCallbackFifo.getNext(kBlockingModeNonBlocking);
   }
   return kSuccess;
}

#ifdef CPCAPI2_AUTO_TEST
AutoTestReadCallback* AuthServerInterface::process_test(int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return NULL;
   }
   resip::ReadCallbackBase* rcb = mCallbackFifo.getNext(timeout);
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
   {
      return fpCmd;
   }
   if (rcb != NULL)
   {
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   }
   return NULL;
}
#endif

void AuthServerInterface::post(ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void AuthServerInterface::interruptProcess()
{
   mPhone->getSdkModuleThread().getAsyncProcessHandler()->handleProcessNotification();
}
   
void AuthServerInterface::postCallback(ReadCallbackBase* command)
{
   mCallbackFifo.add(command);
   if (mCbHook) { mCbHook(); }
}

void AuthServerInterface::setCallbackHook(void(*cbHook)(void*), void* context)
{
   mCbHook = std::bind(cbHook, context);
}

int AuthServerInterface::start(const AuthServerConfig& serverConfig)
{
   post(resip::resip_bind(&AuthServerInterface::startImpl, this, serverConfig));
   return kSuccess;
}

int AuthServerInterface::startImpl(const AuthServerConfig& serverConfig)
{
   InfoLog(<< "Auth Server start(): "
      << "\n\tport: " << serverConfig.port
      << "\n\tnumThreads: " << serverConfig.numThreads
      << "\n\tcertificateFilePath: " << serverConfig.certificateFilePath
      << "\n\thttpsCertificateFilePath: " << serverConfig.httpsCertificateFilePath
      << "\n\thttpsPrivateKeyFilePath: " << serverConfig.httpsPrivateKeyFilePath
      << "\n\thttpsDiffieHellmanParamsFilePath: " << serverConfig.httpsDiffieHellmanParamsFilePath
   );

   mConfig = serverConfig;

   if (mServerThread != NULL)
   {
      return 0; // already started
   }

   if (serverConfig.backend == BackendType_StrettoHttpProv)
   {
      mDb.reset(new StrettoProvAccess(mPhone));
      mDb->initialize("");
   }
   else
   {
      mDb.reset(new DbAccess(serverConfig.numThreads));
      mDb->initialize("authserver.db");
   }

   bool useHttps = true;
   if (serverConfig.httpsCertificateFilePath.empty() && serverConfig.httpsPrivateKeyFilePath.empty())
   {
      useHttps = false;
   }

   if (useHttps)
   {
      mHttpsWebServer.reset(new CPCAPI2::HttpsServerImpl(SslCipherOptions(),
                                                     serverConfig.httpsCertificateFilePath.c_str(),
                                                     serverConfig.httpsPrivateKeyFilePath.c_str(),
                                                     serverConfig.httpsDiffieHellmanParamsFilePath.c_str() ));
      startServerTypeImpl(mHttpsWebServer, serverConfig);

      if (mOnPrivilegedAccessCompleted) mOnPrivilegedAccessCompleted();
   }
   else
   {
      mHttpWebServer.reset(new SimpleWeb::Server<SimpleWeb::HTTP>);
      startServerTypeImpl(mHttpWebServer, serverConfig);
   }

   return kSuccess;
}

template <typename T>
int AuthServerInterface::startServerTypeImpl(std::unique_ptr<typename SimpleWeb::Server<T> >& server, const AuthServerConfig& serverConfig)
{
   server->config.port = serverConfig.port;
   server->config.thread_pool_size = serverConfig.numThreads;

   server->resource["^/login_v1"]["POST"] = [&](const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& response, const std::shared_ptr<typename SimpleWeb::Server<T>::Request>& request) {
      try {
         handleLoginRequest<T>(response, request, false);
      }
      catch (const std::exception &e) {
         *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n"
            << e.what();
      }
   };
   server->resource["^/login_v1"]["OPTIONS"] = [&](const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& response, const std::shared_ptr<typename SimpleWeb::Server<T>::Request>& request) {
      try {
         *response << "HTTP/1.1 200 OK\r\n"
            << "Access-Control-Allow-Origin: *\r\n"
            << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
      }
      catch (const std::exception &e) {
         *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n"
            << e.what();
      }
   };

   server->resource["^/addUser"]["POST"] = [&](const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& response, const std::shared_ptr<typename SimpleWeb::Server<T>::Request>& request) {
      try {
         handleAddUserRequest<T>(response, request);
      }
      catch (const std::exception &e) {
         *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n"
            << e.what();
      }
   };
   server->resource["^/addUser"]["OPTIONS"] = [&](const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& response, const std::shared_ptr<typename SimpleWeb::Server<T>::Request>& request) {
      try {
         *response << "HTTP/1.1 200 OK\r\n"
            << "Access-Control-Allow-Origin: *\r\n"
            << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
      }
      catch (const std::exception &e) {
         *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n"
            << e.what();
      }
   };

   server->resource["^/login_v2"]["POST"] = [&](const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& response, const std::shared_ptr<typename SimpleWeb::Server<T>::Request>& request) {
      try {
         handleLoginRequest<T>(response, request, true);
      }
      catch (const std::exception &e) {
         *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n"
            << e.what();
      }
   };
   server->resource["^/login_v2"]["OPTIONS"] = [&](const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& response, const std::shared_ptr<typename SimpleWeb::Server<T>::Request>& request) {
      try {
         *response << "HTTP/1.1 200 OK\r\n"
            << "Access-Control-Allow-Origin: *\r\n"
            << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
      }
      catch (const std::exception &e) {
         *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n"
            << e.what();
      }
   };

   mServerThread = new std::thread([&, serverConfig]() {
      try
      {
         std::function<void(unsigned short)> started = [&](unsigned short /*listenPort*/)
         {
            AuthServerStartedEvent args;
            postCallback(makeFpCommand1(AuthServerHandler::onAuthServerStarted, mAppHandler, args));
         };

         server->start(started);
      }
      catch (const boost::system::system_error& ex)
      {
         ErrLog(<< "error starting auth server: " << ex.what());
      }
   });
   return kSuccess;
}

int AuthServerInterface::shutdown()
{
   mPhone->getSdkModuleThread().execute(resip::resip_bind(&AuthServerInterface::shutdownImpl, this));
   return kSuccess;
}

int AuthServerInterface::shutdownImpl()
{
   InfoLog(<< "Auth Server shutdown()");

   if (mHttpsWebServer.get() != NULL)
   {
      mHttpsWebServer->stop();
   }
   if (mHttpWebServer.get() != NULL)
   {
      mHttpWebServer->stop();
   }

   if (mServerThread != NULL)
   {
      mServerThread->join();
      delete mServerThread;
      mServerThread = NULL;
      mHttpsWebServer.reset();
      mHttpWebServer.reset();
   }
   if (mDb.get() != NULL)
   {
      //mDb->shutdown();
   }
   return kSuccess;
}

int AuthServerInterface::setHandler(AuthServerHandler* handler)
{
   post(resip::resip_bind(&AuthServerInterface::setHandlerImpl, this, handler));
   return kSuccess;
}

int AuthServerInterface::setHandlerImpl(AuthServerHandler* handler)
{
   mAppHandler = handler;
   return kSuccess;
}

int AuthServerInterface::addUser(const cpc::string& username, const cpc::string& password)
{
   post(resip::resip_bind(&AuthServerInterface::addUserImpl, this, username, password));
   return kSuccess;
}

static inline cpc::string sha256Hash(cpc::string input)
{
   std::stringstream passwordHashSs;
   unsigned char hash[SHA256_DIGEST_LENGTH];
   SHA256(reinterpret_cast<const unsigned char*>(input.c_str()), input.size(), hash);
   for (int i = 0; i < SHA256_DIGEST_LENGTH; i++)
   {
      passwordHashSs << std::hex << std::setw(2) << std::setfill('0') << (int)hash[i];
   }
   return cpc::string(passwordHashSs.str().c_str());
}

int AuthServerInterface::addUserImpl(const cpc::string& username, const cpc::string& password)
{
   bool userExists = false;
   cpc::string storedPassword;
   if (mDb->queryUser(username, password, userExists, storedPassword) != 0)
   {
      WarningLog(<< "Error querying auth database");
      return kError;
   }

   if (userExists)
   {
      // simply update the password
      if (mDb->updatePassword(username, sha256Hash(password)) != 0)
      {
         WarningLog(<< "Error updating auth database");
         return kError;
      }
   }
   else
   {
      if (mDb->addUser(username, sha256Hash(password)) != 0)
      {
         WarningLog(<< "Error adding user to auth database");
         return kError;
      }
   }
   return kSuccess;
}

#ifdef CPCAPI2_AUTO_TEST
void AuthServerInterface::dropIncomingLoginRequests(bool enable)
{
   post(resip::resip_bind(&AuthServerInterface::dropIncomingLoginRequestsImpl, this, enable));
}

void AuthServerInterface::dropIncomingAddUserRequests(bool enable)
{
   post(resip::resip_bind(&AuthServerInterface::dropIncomingAddUserRequestsImpl, this, enable));
}
#endif

void AuthServerInterface::dropIncomingLoginRequestsImpl(bool enable)
{
   mDropLoginRequests = enable;
}

void AuthServerInterface::dropIncomingAddUserRequestsImpl(bool enable)
{
   mDropAddUserRequests = enable;
}

inline static
SimpleWeb::CaseInsensitiveMultimap authResponseHeaders()
{
   SimpleWeb::CaseInsensitiveMultimap authRespHeaders;
   authRespHeaders.emplace("Access-Control-Allow-Origin", "*");
   authRespHeaders.emplace("Access-Control-Allow-Headers", "Authorization");
   authRespHeaders.emplace("Cache-Control", "no-cache, no-store, must-revalidate");
   return authRespHeaders;
}

template <typename T>
inline static
void sendResponse(const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& response, SimpleWeb::StatusCode statusCode, std::string message)
{
   response->write(statusCode, message, authResponseHeaders());
}

template <typename T>
bool AuthServerInterface::validateRequest(const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& response, const std::shared_ptr<typename SimpleWeb::Server<T>::Request>& request, bool requireEncryption, std::shared_ptr<rapidjson::Document>& jsonRequest)
{
   resip::Data decryptedRequest;
   bool requestWasEncrypted = false;

   std::string reqContent = request->content.string();
   jsonRequest.reset(new rapidjson::Document);

   jsonRequest->Parse<0>(reqContent.c_str(), reqContent.size());

   if (jsonRequest->HasParseError())
   {
      requestWasEncrypted = true;
      if (JwtUtils::Decrypt(mConfig.certificateFilePath.c_str(), resip::Data(reqContent.data(), reqContent.size()), decryptedRequest) != 0)
      {
         WarningLog(<< "Invalid request format, failed to decrypt");
         sendResponse<T>(response, SimpleWeb::StatusCode::client_error_bad_request, "Invalid request format, failed to decrypt");
         return false;
      }
#ifndef __clang_analyzer__ // warning inside rapidjson: https://github.com/Tencent/rapidjson/issues/1174
      jsonRequest->Parse<0>(decryptedRequest.c_str());
#endif    
   }

   if (jsonRequest->HasParseError())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      sendResponse<T>(response, SimpleWeb::StatusCode::client_error_bad_request, "Invalid request format, parse error occured");
      return false;
   }

   if (!jsonRequest->HasMember("username"))
   {
      WarningLog(<< "Invalid request, missing username");
      sendResponse<T>(response, SimpleWeb::StatusCode::client_error_bad_request, "Invalid request, missing username");
      return false;
   }

   if (!jsonRequest->HasMember("password"))
   {
      WarningLog(<< "Invalid request, missing password");
      sendResponse<T>(response, SimpleWeb::StatusCode::client_error_bad_request, "Invalid request, missing password");
      return false;
   }

   const rapidjson::Value& usernameVal = (*jsonRequest)["username"];
   if (!usernameVal.IsString())
   {
      WarningLog(<< "Invalid request, username is not a string");
      sendResponse<T>(response, SimpleWeb::StatusCode::client_error_bad_request, "Invalid request, username is not a string");
      return false;
   }

   const rapidjson::Value& passwordVal = (*jsonRequest)["password"];
   if (!passwordVal.IsString())
   {
      WarningLog(<< "Invalid request, password is not a string");
      sendResponse<T>(response, SimpleWeb::StatusCode::client_error_bad_request, "Invalid request, password is not a string");
      return false;
   }

   if (requireEncryption && !requestWasEncrypted && !jsonRequest->HasMember("pubKey"))
   {
      WarningLog(<< "Invalid request, unencrypted requests must specify a valid pubKey");
      sendResponse<T>(response, SimpleWeb::StatusCode::client_error_bad_request, "Invalid request, unencrypted requests must specify a valid pubKey");
      return false;
   }

   if (jsonRequest->HasMember("pubKey"))
   {
      const rapidjson::Value& pubKeyVal = (*jsonRequest)["pubKey"];
      if (!pubKeyVal.IsString())
      {
         WarningLog(<< "pubKey provided, but it is not a string");
         sendResponse<T>(response, SimpleWeb::StatusCode::client_error_bad_request, "Invalid request, pubKey is not a string");
         return false;
      }

      resip::Data encryptedTest;
      resip::Data decryptedTest;
      if (JwtUtils::Encrypt(pubKeyVal.GetString(), "teststring", encryptedTest, true) != 0)
      {
         WarningLog(<< "pubKey provided, but it could not be used for encrypt/decrypt test");
         sendResponse<T>(response, SimpleWeb::StatusCode::client_error_bad_request, "Invalid request, pubKey could not be used for encrypt/decrypt test");
         return false;
      }
      if (JwtUtils::Decrypt(mConfig.certificateFilePath.c_str(), encryptedTest, decryptedTest) != 0)
      {
         WarningLog(<< "pubKey provided, but it failed the decrypt test");
         sendResponse<T>(response, SimpleWeb::StatusCode::client_error_bad_request, "Invalid request, pubKey failed the decrypt test");
         return false;
      }
      if (decryptedTest != "teststring")
      {
         WarningLog(<< "pubKey provided, but it failed the decrypt test when comparing against input");
         sendResponse<T>(response, SimpleWeb::StatusCode::client_error_bad_request, "Invalid request, pubKey failed the decrypt test when comparing against input");
         return false;
      }
   }

   return true;
}

template <typename T>
void AuthServerInterface::handleLoginRequest(const std::shared_ptr<typename SimpleWeb::Server<T>::Response> &response, const std::shared_ptr<typename SimpleWeb::Server<T>::Request>& request, bool isV2)
{
#ifdef CPCAPI2_AUTO_TEST
   if (mDropLoginRequests)
   {
      return;
   }
#endif

   std::shared_ptr<rapidjson::Document> jsonRequest;

   if (validateRequest<T>(response, request, isV2, jsonRequest))
   {
      cpc::string usernameVal = (*jsonRequest)["username"].GetString();
      cpc::string passwordVal = (*jsonRequest)["password"].GetString();

      InfoLog(<< "Auth Server loginRequest(): " << "\n\tusername: " << usernameVal);

      bool userExists = false;
      cpc::string storedPassword;
      if (mDb->queryUser(usernameVal, passwordVal, userExists, storedPassword) != 0)
      {
         WarningLog(<< "Error querying auth database");
         sendResponse<T>(response, SimpleWeb::StatusCode::server_error_internal_server_error, "Error querying auth database");
         return;
      }

      if (!isV2 && !userExists)
      {
         WarningLog(<< "Unauthorized access, invalid user");
         sendResponse<T>(response, SimpleWeb::StatusCode::client_error_unauthorized, "Unauthorized access, invalid user");
         return;
      }

      if (isV2) 
      {
         // With V2 API, we don't really care about password matching since client provided the Auth decryption key
         // Also, password is guaranteed to be hashed
         if (userExists ? (mDb->updatePassword(usernameVal, passwordVal) != 0) : (mDb->addUser(usernameVal, passwordVal) != 0))
         {
            WarningLog(<< "Error updating auth database");
            sendResponse<T>(response, SimpleWeb::StatusCode::server_error_internal_server_error, "Error updating auth database");
            return;
         }
      }
      else
      {
         // DB password is guaranteed to be hashed by addUser API
         if (passwordVal != storedPassword && sha256Hash(passwordVal) != storedPassword)
         {
            WarningLog(<< "Unauthorized access, invalid password");
            sendResponse<T>(response, SimpleWeb::StatusCode::client_error_unauthorized, "Unauthorized access, invalid password");
            return;
         }
      }

      std::map<resip::Data, resip::Data> pubClaims;
      pubClaims["cp_user"] = resip::Data(usernameVal, usernameVal.size());

      if (jsonRequest->HasMember("device_uuid"))
      {
         const rapidjson::Value& deviceuuidVal = (*jsonRequest)["device_uuid"];
         if (deviceuuidVal.IsString())
         {
            pubClaims["device_uuid"] = resip::Data(deviceuuidVal.GetString(), deviceuuidVal.GetStringLength());
         }
      }

      if (jsonRequest->HasMember("requested_resources"))
      {
         resip::Data reqResData;
         {
            resip::DataStream dsReqRes(reqResData);
            const rapidjson::Value& reqResVal = (*jsonRequest)["requested_resources"];
            if (reqResVal.IsArray())
            {
               dsReqRes << "[ ";
               for (rapidjson::Value::ConstValueIterator itRR = reqResVal.Begin(); itRR != reqResVal.End(); ++itRR)
               {
                  if (itRR->IsString())
                  {
                     dsReqRes << "\"" << itRR->GetString();
                     if ((itRR + 1) == reqResVal.End())
                     {
                        dsReqRes << "\" ";
                     }
                     else
                     {
                        dsReqRes << "\", ";
                     }
                  }
               }
               dsReqRes << "]";
            }
         }
         pubClaims["requested_resources"] = reqResData;
      }

      resip::Data jwt;
      JwtUtils::GenerateJWT(mConfig.certificateFilePath.c_str(), "CPCAPI2::AuthServer", pubClaims, mConfig.tokenExpiry, jwt);

      rapidjson::Document respJson;
      respJson.SetObject();

      rapidjson::Value jwtVal(jwt.c_str(), respJson.GetAllocator());
      respJson.AddMember("token", jwtVal, respJson.GetAllocator());

      rapidjson::StringBuffer buffer(0, 1024);
      rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
      respJson.Accept(writer);

      response->write(std::string(buffer.GetString(), buffer.GetSize()), authResponseHeaders());
   }
}

template <typename T>
void AuthServerInterface::handleAddUserRequest(const std::shared_ptr< typename SimpleWeb::Server<T>::Response >& response, 
                                               const std::shared_ptr< typename SimpleWeb::Server<T>::Request >& request)
{
#ifdef CPCAPI2_AUTO_TEST
   if (mDropAddUserRequests)
   {
      return;
   }
#endif

   std::shared_ptr<rapidjson::Document> jsonRequest;
   if (validateRequest<T>(response, request, true, jsonRequest))
   {
      cpc::string usernameVal = (*jsonRequest)["username"].GetString();
      cpc::string passwordVal = (*jsonRequest)["password"].GetString();

      InfoLog(<< "Auth Server addUser(): " << "\n\tusername: " << usernameVal);

      bool userExists = false;
      cpc::string storedPassword;
      if (mDb->queryUser(usernameVal, passwordVal, userExists, storedPassword) != 0)
      {
         WarningLog(<< "Error querying auth database");
         sendResponse<T>(response, SimpleWeb::StatusCode::server_error_internal_server_error, "Error querying auth database");
         return;
      }

      if (userExists)
      {
         // simply update the password - user is already authenticated by the auth key
         if (mDb->updatePassword(usernameVal, sha256Hash(passwordVal)) != 0)
         {
            WarningLog(<< "Error updating auth database");
            sendResponse<T>(response, SimpleWeb::StatusCode::server_error_internal_server_error, "Error updating auth database");
            return;
         }
      }
      else
      {
         if (mDb->addUser(usernameVal, sha256Hash(passwordVal)) != 0)
         {
            WarningLog(<< "Error adding user to auth database");
            sendResponse<T>(response, SimpleWeb::StatusCode::server_error_internal_server_error, "Error adding user to auth database");
            return;
         }
      }

      sendResponse<T>(response, SimpleWeb::StatusCode::success_created, "User created");
   }
}
}
}

#endif
