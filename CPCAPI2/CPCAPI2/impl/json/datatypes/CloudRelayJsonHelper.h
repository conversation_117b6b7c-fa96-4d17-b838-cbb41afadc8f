// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef CloudRelay_JSON_HELPER_H
#define CloudRelay_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/experimental/cloudrelay/CloudRelayTypes.h>
#include <interface/experimental/cloudrelay/CloudRelayHandler.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::CloudRelay::BroadcastEvent& v)
    {
      w.StartObject();
      Write(w, "jsonApiUser", v.jsonApiUser);
      Write(w, "msg", v.msg);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::CloudRelay::BroadcastEvent& v)
    {
      Read(json, "jsonApiUser", v.jsonApiUser);
      Read(json, "msg", v.msg);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::CloudRelay::MessageEvent& v)
    {
      w.StartObject();
      Write(w, "jsonApiUser", v.jsonApiUser);
      Write(w, "msg", v.msg);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::CloudRelay::MessageEvent& v)
    {
      Read(json, "jsonApiUser", v.jsonApiUser);
      Read(json, "msg", v.msg);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::CloudRelay::CloudRelayHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::CloudRelay::CloudRelayHandler& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // CloudRelay_JSON_HELPER_H