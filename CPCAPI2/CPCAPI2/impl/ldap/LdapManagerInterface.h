#pragma once

#if !defined(__CPCAPI2_LDAP_MANAGER_INTERFACE_H__)
#define __CPCAPI2_LDAP_MANAGER_INTERFACE_H__

#include "cpcapi2defs.h"
#include "ldap/LdapManager.h"
#include "../phone/PhoneInterface.h"

#include <rutil/Fifo.hxx>
#include <mutex>
#include <thread>

#include <boost/asio.hpp>

namespace CPCAPI2
{
namespace OpenLdap
{

class LdapClientImpl;

class LdapManagerInterface : public PhoneModule,
                             public LdapManager
{
public:
   DEPRECATED LdapManagerInterface(Phone* phone, bool useSeparateThread);
   LdapManagerInterface(Phone* phone);
   virtual ~LdapManagerInterface();
   
   virtual void interruptProcess();
   virtual int setHandler(LdapClientHandle pc, LdapHandler* handler) OVERRIDE;
   
   LdapClientHandle createClient() OVERRIDE;
   
   int applySettings(LdapClientHandle pc,const LdapClientSettings& settings) OVERRIDE;
   int connect(LdapClientHandle handle) OVERRIDE;
   int disconnect(LdapClientHandle handle) OVERRIDE;
   int setDataMap(LdapClientHandle handle,LdapDataMap map) OVERRIDE;
   int search(LdapClientHandle handle, cpc::string searchPattern, cpc::string rootDn, LdapSearchScope scopeSetting, int timeout, int maxsize, bool opErrIgnore=false) OVERRIDE;
   //int destroy(LdapClientHandle handle);
   
   void addSdkObserver(LdapHandler* observer);
   void removeSdkObserver(LdapHandler* observer);
   
   virtual void Release() OVERRIDE;
   virtual int process(unsigned int timeout) OVERRIDE;
   
   void post(resip::ReadCallbackBase* f);
   void postCallback(resip::ReadCallbackBase*);
   
   virtual void setCallbackHook(void (*cbHook)(void*), void* context);
   
private:
   int getImpl(LdapClientHandle pc, LdapClientImpl*& pimpl);
   
   int createClientImpl(LdapClientHandle pc);
   int ApplySettingsImpl(LdapClientHandle pc,const LdapClientSettings settings);
   int SetHandlerImpl(LdapClientHandle pc, LdapHandler* handler);
   int ConnectImpl(LdapClientHandle pc);
   int DisconnectImpl(LdapClientHandle pc);
   int SetDataMapImpl(LdapClientHandle pc,LdapDataMap map);
   int SearchImpl(LdapClientHandle pc, cpc::string searchPattern, cpc::string rootDn, LdapSearchScope scopeSetting, int timeout, int maxsize, bool opErrIgnore=false);
   void setCallbackHookImpl(void (*cbHook)(void*), void* context);
   
private:
   bool mShutdown;
   DEPRECATED bool mUseSeparateThread;
   
   // used for dispatching
   boost::asio::io_service m_IOService;
   std::shared_ptr< boost::asio::io_service::work > m_pWork;
   std::thread *m_ServiceThread;
   
   resip::Fifo<resip::ReadCallbackBase> mCallbackFifo;
   std::set<LdapHandler*> mSdkObservers;
   PhoneInterface* mPhone;
   
   typedef std::map<LdapClientHandle, LdapClientImpl*> LdapMap;
   LdapMap mLdapMap;

   std::function<void(void)> mCbHook;
};

class LdapHandleFactory
{
public:
   static LdapClientHandle getNext() { return sNextHandle++; }
private:
   static LdapClientHandle sNextHandle;
};

}//namespace OpenLdap
}//namespace CPCAPI2

#endif //__CPCAPI2_LDAP_MANAGER_INTERFACE_H__
