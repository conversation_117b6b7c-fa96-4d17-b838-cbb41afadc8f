#include "brand_branded.h"

#if (CPCAPI2_BRAND_CLOUD_SERVICE_CONFIG_MODULE == 1) && (CPCAPI2_BRAND_CLOUD_WATCHDOG_SERVER_MODULE == 1)
#include "cpcapi2utils.h"
#include "CloudWatchdogServiceInterface.h"
#include "push_service/PushNotificationServiceManager.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"
#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

using namespace resip;
using namespace CPCAPI2::JsonApi;
using namespace CPCAPI2::CloudWatchdog;
using namespace CPCAPI2::PushService;

using resip::ReadCallbackBase;

#define CLOUD_WATCHDOG_SERVICE_TIMER_ID 1
#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

namespace CPCAPI2
{

namespace CloudWatchdog
{
   
CloudWatchdogServiceInterface::CloudWatchdogServiceInterface(Phone* phone) :
EventSource<CloudWatchdogHandle, CloudWatchdogHandler, CloudWatchdogSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
mPhone(dynamic_cast<PhoneInterface*>(phone)),
mStartupQuery(true),
mTimer(mPhone->getSdkModuleThread())
{
   DebugLog(<< "CloudWatchdogServiceInterface::CloudWatchdogServiceInterface(): " << this << " phone: " << phone);
   mOrchServer = CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(phone);

   mPhone->addRefImpl();
}

CloudWatchdogServiceInterface::~CloudWatchdogServiceInterface()
{
   StackLog(<< "CloudWatchdogServiceInterface::~CloudWatchdogServiceInterface(): " << this << " phone: " << mPhone);
   mTimer.cancel();
   mPhone->releaseImpl();
}

void CloudWatchdogServiceInterface::Release()
{
   StackLog(<< "CloudWatchdogServiceInterface::Release(): " << this << " phone: " << mPhone);
   delete this;
}

int CloudWatchdogServiceInterface::start(const CloudWatchdogConfig& serverConfig) 
{
   postToSdkThread(resip::resip_bind(&CloudWatchdogServiceInterface::startImpl, this, serverConfig));
   return kSuccess;
}

void CloudWatchdogServiceInterface::startImpl(const CloudWatchdogConfig& serverConfig)
{
   mConfig = serverConfig;

   dynamic_cast<CPCAPI2::OrchestrationServer::OrchestrationServerInterface*>(mOrchServer)->addSdkObserver(this);
   mTimer.expires_from_now(mConfig.monitorFrequency * 1000);
   mTimer.async_wait(this, CLOUD_WATCHDOG_SERVICE_TIMER_ID, NULL);
}

int CloudWatchdogServiceInterface::shutdown() 
{ 
   postToSdkThread(resip::resip_bind(&CloudWatchdogServiceInterface::shutdownImpl, this));
   return kSuccess; 
}

void CloudWatchdogServiceInterface::shutdownImpl()
{
   dynamic_cast<CPCAPI2::OrchestrationServer::OrchestrationServerInterface*>(mOrchServer)->removeSdkObserver(this);
   mTimer.cancel();
}

int CloudWatchdogServiceInterface::setHandler(CloudWatchdogHandler* handler)
{
   setAppHandler(0, handler);
   return kSuccess;
}

void CloudWatchdogServiceInterface::onTimer(unsigned short timerId, void* appState)
{
   StackLog(<< "CloudWatchdogServiceInterface::onTimer()");
   cpc::vector<CloudConnector::ServiceDesc> toMonitor;
   for (const CloudWatchdog::ServerAndRegion& sr : mConfig.serversToMonitor)
   {
      StackLog(<< "CloudWatchdog::onTimer(): query server: service: " << sr.server << " region: " << sr.region);
      CloudConnector::ServiceDesc orchSr;
      orchSr.service = sr.server;
      orchSr.region = sr.region;
      toMonitor.push_back(orchSr);
   }
   mOrchServer->queryServers(toMonitor);
   mTimer.async_wait(this, CLOUD_WATCHDOG_SERVICE_TIMER_ID, NULL);
}

// OrchestrationServerHandler
int CloudWatchdogServiceInterface::onSetServerInfoResult(int requestHandle, const CPCAPI2::OrchestrationServer::SetServerInfoResult& args)
{
   DebugLog(<< "CloudWatchdog::onSetServerInfoResult(): set server info got results: " << CPCAPI2::OrchestrationServer::get_debug_string(args));

   if (mSetInfoServerList.empty())
   {
      DebugLog(<< "CloudWatchdogServiceInterface::onSetServerInfoResult(): set server info list is empty");
      return kSuccess;
   }

   MonitoredServerInfo serverInfo = mSetInfoServerList.front();
   if ((serverInfo.server.server != args.serverInfo.uri) && (serverInfo.server.region != args.serverInfo.region))
   {
      DebugLog(<< "CloudWatchdogServiceInterface::onSetServerInfoResult(): next set server uri: " << serverInfo.server.server << " does not match result uri: " << args.serverInfo.uri);
      return kSuccess;
   }

   mSetInfoServerList.pop_front();

   SetServerInfoResult infoEvt;
   infoEvt.success = true;
   infoEvt.server = serverInfo;
   fireEvent(cpcFunc(CloudWatchdogHandler::onSetServerInfoResult), 0, infoEvt);
   return kSuccess;
}

int CloudWatchdogServiceInterface::onRequestServiceResult(int requestHandle, const CPCAPI2::OrchestrationServer::RequestServiceResult& args)
{
   DebugLog(<< "CloudWatchdog::onRequestServiceResult(): request service got results: " << CPCAPI2::OrchestrationServer::get_debug_string(args));
   return kSuccess;
}
   
int CloudWatchdogServiceInterface::onQueryServersResult(int requestHandle, const CPCAPI2::OrchestrationServer::QueryServersResult& args)
{
   StackLog(<< "CloudWatchdog::onQueryServersResult(): CloudWatchdog queryServers got results (" << args.servers.size() << " servers)");

   ServerList newServerList;
   cpc::vector<MonitoredServerInfo> serversDetected;
   cpc::vector<MonitoredServerInfo> upServers;
   cpc::vector<MonitoredServerInfo> downServers;
   cpc::vector<MonitoredServerInfo> shutdownServers;

   for (CPCAPI2::OrchestrationServer::ServerInfo si : args.servers)
   {
      cpc::string lastServerListKey = si.uri;
      for (cpc::string service : si.services)
      {
         lastServerListKey.append("." + service);
      }

      MonitoredServerInfo server = MonitoredServerInfo(si.uri, si.region, si.services);

      if (si.ttlSeconds <= 0)
      {
         if (si.shutdown)
         {
            InfoLog(<< "CloudWatchdog::onQueryServersResult(): watchdog detected a down server that was triggered by a controlled server shutdown: " << CPCAPI2::OrchestrationServer::get_debug_string(si));
         }
         else
         {
            InfoLog(<< "CloudWatchdog::onQueryServersResult(): watchdog detected a down server: " << CPCAPI2::OrchestrationServer::get_debug_string(si));
         }

         for (cpc::string service : si.services)
         {
            StackLog(<< "CloudWatchdog::onQueryServersResult(): removing service: " << service);
            mOrchServer->removeServer(service, si.region, si.uri);
         }
         
         downServers.push_back(server);
      }
      else if (si.started)
      {
         InfoLog(<< "CloudWatchdog::onQueryServersResult(): watchdog detected a server startup: " << CPCAPI2::OrchestrationServer::get_debug_string(si));

         if (si.shutdown)
         {
            InfoLog(<< "CloudWatchdog::onQueryServersResult(): watchdog detected a server that just started and had undergone a controlled server shutdown: " << CPCAPI2::OrchestrationServer::get_debug_string(si));
            // This should not really happen as the new server startup should have reset the shutdown flag
         }
         else
         {
            InfoLog(<< "CloudWatchdog::onQueryServersResult(): watchdog detected a server that just started: " << CPCAPI2::OrchestrationServer::get_debug_string(si));
         }

         // Reset the shutdown-started flags
         CPCAPI2::OrchestrationServer::ServerInfo newSi = si;
         newSi.started = false;
         newSi.shutdown = false;
         StackLog(<< "CloudWatchdog::onQueryServersResult(): sending update to reset startup and shutdown flags, serverInfo: " << CPCAPI2::OrchestrationServer::get_debug_string(newSi));
         mOrchServer->setServerInfo(newSi);
         mSetInfoServerList.push_back(server);
      }
      else if (si.shutdown)
      {
         InfoLog(<< "CloudWatchdog::onQueryServersResult(): watchdog detected a controlled server shutdown: " << CPCAPI2::OrchestrationServer::get_debug_string(si));
         shutdownServers.push_back(server);
      }
      else
      {
         StackLog(<< "CloudWatchdog::onQueryServersResult(): serverInfo: " << CPCAPI2::OrchestrationServer::get_debug_string(si));
      }

      serversDetected.push_back(server);
      newServerList.insert(std::make_pair(lastServerListKey, si));
      if (mLastServerList.find(lastServerListKey) == mLastServerList.end())
      {
         DebugLog(<< "CloudWatchdog::onQueryServersResult(): did not find server-service: " << lastServerListKey << " in previous server list");
         if (!si.shutdown && (si.ttlSeconds > 0))
         {
            DebugLog(<< "CloudWatchdog::onQueryServersResult(): sending a server user query for: " << lastServerListKey);
            NotificationServerInfo notificationServerInfo(si.uri);
            mNotificationServerList.push_back(notificationServerInfo);
            mOrchServer->queryServerUsers(si.uri);
            upServers.push_back(server);
         }
      }
      else if (si.started)
      {
         DebugLog(<< "CloudWatchdog::onQueryServersResult(): found server-service: " << lastServerListKey << " in previous server list, sending server user query as detected a missed restart");
         NotificationServerInfo notificationServerInfo(si.uri);
         mNotificationServerList.push_back(notificationServerInfo);
         mOrchServer->queryServerUsers(si.uri);
         upServers.push_back(server);
      }
   }

   StackLog(<< "CloudWatchdog::onQueryServersResult(): previous server size: " << mLastServerList.size() << " query response server size: " << args.servers.size() << " new server size: " << newServerList.size());

   // Handle scenario where a server went down but did not go through a removal based on the normal ttl expiry.
   for (ServerList::iterator i = mLastServerList.begin(); i != mLastServerList.end(); ++i)
   {
      cpc::string uri = i->first;
      CPCAPI2::OrchestrationServer::ServerInfo si = i->second;
      ServerList::iterator j = newServerList.find(uri);
      if (j == newServerList.end())
      {
         DebugLog(<< "CloudWatchdog::onQueryServersResult(): server missing as compared to previous list: " << uri);
         if (si.ttlSeconds > 0)
         {
            StackLog(<< "CloudWatchdog::onQueryServersResult(): server missing from list: " << uri << " but did not have ttl expiry");
            downServers.push_back(MonitoredServerInfo(si.uri, si.region, si.services));
         }
      }
   }

   if (mStartupQuery)
   {
      StackLog(<< "CloudWatchdog::onQueryServersResult(): startup query");
      mStartupQuery = false;

      WatchdogStartEvent startEvt;
      startEvt.serverList = serversDetected;
      fireEvent(cpcFunc(CloudWatchdogHandler::onWatchdogStart), 0, startEvt);
   }

   for (cpc::vector<MonitoredServerInfo>::iterator i = upServers.begin(); i != upServers.end(); ++i)
   {
      ServerUpEvent upEvt;
      upEvt.server = (*i);
      fireEvent(cpcFunc(CloudWatchdogHandler::onServerUp), 0, upEvt);
   }
   
   for (cpc::vector<MonitoredServerInfo>::iterator i = downServers.begin(); i != downServers.end(); ++i)
   {
      ServerDownEvent downEvt;
      downEvt.server = (*i);
      fireEvent(cpcFunc(CloudWatchdogHandler::onServerDown), 0, downEvt);
   }

   for (cpc::vector<MonitoredServerInfo>::iterator i = shutdownServers.begin(); i != shutdownServers.end(); ++i)
   {
      ServerShutdownEvent shutdownEvt;
      shutdownEvt.server = (*i);
      fireEvent(cpcFunc(CloudWatchdogHandler::onServerShutdown), 0, shutdownEvt);
   }

   mLastServerList.clear();
   mLastServerList = newServerList;

   if (mConfig.queryResponseEnabled)
   {
      ServerQueryResult queryEvt;
      queryEvt.serverList = serversDetected;
      fireEvent(cpcFunc(CloudWatchdogHandler::onServerQueryResult), 0, queryEvt);
   }

   return kSuccess;
}

/*
int CloudWatchdogServiceInterface::onQueryServersResult(int requestHandle, const CPCAPI2::OrchestrationServer::QueryServersResult& args)
{
   StackLog(<< "CloudWatchdog::onQueryServersResult(): CloudWatchdog queryServers got results (" << args.servers.size() << " servers)");

   bool serverListChangeDetected = false;
   ServerList newServerList;
   cpc::vector<ServerAndRegion> serversDetected;
   for (CPCAPI2::OrchestrationServer::ServerInfo si : args.servers)
   {
      bool addedToNotification = false;
      cpc::string lastServerListKey = si.uri;
      for (cpc::string service : si.services)
      {
         lastServerListKey.append("." + service);
      }

      if (si.ttlSeconds <= 0)
      {
         if (si.shutdown)
         {
            InfoLog(<< "CloudWatchdog::onQueryServersResult(): watchdog detected a down server that was triggered by a controlled server shutdown: " << CPCAPI2::OrchestrationServer::get_debug_string(si));
         }
         else
         {
            InfoLog(<< "CloudWatchdog::onQueryServersResult(): watchdog detected a down server: " << CPCAPI2::OrchestrationServer::get_debug_string(si));
         }
         NotificationServerInfo notificationServerInfo(si.uri, true, false);
         mNotificationServerList.push_back(notificationServerInfo);
         addedToNotification = true;
         mOrchServer->queryServerUsers(si.uri);
         for (cpc::string service : si.services)
         {
            StackLog(<< "CloudWatchdog::onQueryServersResult(): removing service: " << service);
            mOrchServer->removeServer(service, si.region, si.uri);
         }
      }
      else if (si.started)
      {
         InfoLog(<< "CloudWatchdog::onQueryServersResult(): watchdog detected a server startup: " << CPCAPI2::OrchestrationServer::get_debug_string(si));
         // Use previous server list to determine if the push notification has already been sent if the down server had been detected.
         // If the server is already in the list, it indicates that the down server event was missed in between the watchdog TTLs, as
         // such the push notification would not have been sent.
         bool pushNotSent = (mLastServerList.find(lastServerListKey) != mLastServerList.end());
         if (pushNotSent)
         {
            InfoLog(<< "CloudWatchdog::onQueryServersResult(): watchdog detected a missed server down: " << CPCAPI2::OrchestrationServer::get_debug_string(si));
            NotificationServerInfo notificationServerInfo(si.uri, false, true);
            mNotificationServerList.push_back(notificationServerInfo);
            addedToNotification = true;
            mOrchServer->queryServerUsers(si.uri);
         }
         else
         {
            if (si.shutdown)
            {
               InfoLog(<< "CloudWatchdog::onQueryServersResult(): watchdog detected a server that just started and had undergone a controlled server shutdown: " << CPCAPI2::OrchestrationServer::get_debug_string(si));
               // This should not really happen as the new server startup should have reset the shutdown flag
            }
            else
            {
               InfoLog(<< "CloudWatchdog::onQueryServersResult(): watchdog detected a new server that just started: " << CPCAPI2::OrchestrationServer::get_debug_string(si));
            }
         }

         // Reset the shutdown-started flags
         si.started = false;
         si.shutdown = false;
         StackLog(<< "CloudWatchdog::onQueryServersResult(): sending update to reset startup and shutdown flags, serverInfo: " << CPCAPI2::OrchestrationServer::get_debug_string(si));
         mOrchServer->setServerInfo(si);

         SetServerInfoResult args;
         args.success = true;
         fireEvent(cpcFunc(CloudWatchdogHandler::onSetServerInfoResult), 0, args);
      }
      else if (si.shutdown)
      {
         InfoLog(<< "CloudWatchdog::onQueryServersResult(): watchdog detected a controlled server shutdown: " << CPCAPI2::OrchestrationServer::get_debug_string(si));
      }
      else
      {
         StackLog(<< "CloudWatchdog::onQueryServersResult(): serverInfo: " << CPCAPI2::OrchestrationServer::get_debug_string(si));
      }

      serversDetected.push_back(ServerAndRegion(si.uri, si.region));

      newServerList.insert(std::make_pair(lastServerListKey, si));
      if (mLastServerList.find(lastServerListKey) == mLastServerList.end())
      {
         DebugLog(<< "CloudWatchdog::onQueryServersResult(): did not find server-service: " << lastServerListKey << " in previous server list");
         if (!addedToNotification && !si.shutdown)
         {
            NotificationServerInfo notificationServerInfo(si.uri, false, false);
            mNotificationServerList.push_back(notificationServerInfo);
            mOrchServer->queryServerUsers(si.uri);
         }
         serverListChangeDetected = true;
         continue;
      }
   }

   StackLog(<< "CloudWatchdog::onQueryServersResult(): previous server size: " << mLastServerList.size() << " query response server size: " << args.servers.size() << " new server size: " << newServerList.size());

   if (newServerList.size() == mLastServerList.size())
   {
      // Handle scenario where a server went down but a new server came up, i.e. the server list will be same size but not same servers.
      // The new server would have been handled in the previous loop. But the missing server needs to be handled, as it did not go through
      // a removal based on the normal ttl expiry.
      for (ServerList::iterator i = mLastServerList.begin(); i != mLastServerList.end(); ++i)
      {
         cpc::string uri = i->first;
         ServerList::iterator j = newServerList.find(uri);
         if (j == newServerList.end())
         {
            DebugLog(<< "CloudWatchdog::onQueryServersResult(): server missing from list, possible down server: " << uri);
            serverListChangeDetected = true;

            NotificationServerInfo notificationServerInfo(uri, true, false);
            mNotificationServerList.push_back(notificationServerInfo);
            mOrchServer->queryServerUsers(uri);
            continue;
         }
      }
   }
   else
   {
      // If server list is not the same size, new servers and down servers (ttl based) would have been detected in the previous loop.
      // We need to take care of the scenario if server list is empty, but a server may have gone down, i.e. removed from server list
      // but no ttl expiry or possibly server got removed by some other entity, e.g. provisioned out
      serverListChangeDetected = true;

      if (args.servers.size() == 0)
      {
         // Need to send the user query for previous servers, as the new query response server list is empty, so we would not have iterated in the previous loop
         std::map<cpc::string, CPCAPI2::OrchestrationServer::ServerInfo> ServerList;
         std::set<cpc::string> uris;
         for (ServerList::iterator i = mLastServerList.begin(); i != mLastServerList.end(); ++i)
         {
            uris.insert((*i).second.uri);
         }
         for (cpc::string uri : uris)
         {
            StackLog(<< "CloudWatchdog::onQueryServersResult(): sending user query for server: " << uri);
            NotificationServerInfo notificationServerInfo(uri, true, false);
            mNotificationServerList.push_back(notificationServerInfo);
            mOrchServer->queryServerUsers(uri);
         }
      }
   }

   if (mStartupQuery)
   {
      StackLog(<< "CloudWatchdog::onQueryServersResult(): startup query");
      serverListChangeDetected = true;
      mStartupQuery = false;
   }

   mLastServerList.clear();
   mLastServerList = newServerList;

   if (serverListChangeDetected)
   {
      ServerQueryResultsChange resultsChangeEvent;
      resultsChangeEvent.serversDetected = serversDetected;
      fireEvent(cpcFunc(CloudWatchdogHandler::onServerQueryResultsChange), 0, resultsChangeEvent);
   }

   return kSuccess;
}
*/

int CloudWatchdogServiceInterface::onQueryServerTtlResult(int requestHandle, const CPCAPI2::OrchestrationServer::QueryServerTtlResult& args)
{
   return kSuccess;
}

int CloudWatchdogServiceInterface::onQueryServerUsersResult(int requestHandle, const CPCAPI2::OrchestrationServer::QueryServerUsersResult& args)
{
   StackLog(<< "CloudWatchdogServiceInterface::onQueryServerUsersResult(): CloudWatchdog queryServerUsers got results (" << args.endpointIds.size() << " endpoints)");

   if (mNotificationServerList.empty())
   {
      DebugLog(<< "CloudWatchdogServiceInterface::onQueryServerUsersResult(): notification list is empty, ignoring sending push notification for uri: " << args.uri);
      return kSuccess;
   }

   NotificationServerInfo notificationServerInfo = mNotificationServerList.front();
   if (notificationServerInfo.uri != args.uri)
   {
      DebugLog(<< "CloudWatchdogServiceInterface::onQueryServerUsersResult(): next notification uri: " << notificationServerInfo.uri << " does not match, ignoring sending push notification for uri: " << args.uri);
      return kSuccess;
   }

   mNotificationServerList.pop_front();

   NotificationSent notificationSentEvent(args.uri, args.endpointIds);

   CPCAPI2::PushService::PushNotificationServiceManager* pushService = CPCAPI2::PushService::PushNotificationServiceManager::getInterface(mPhone);
   CPCAPI2::PushService::PushNotificationRequest pushRequest;
   pushRequest.apnInfo.usePushKit = mConfig.apnInfo.usePushKit;
   pushRequest.customData.push_back(CustomDataField("event", "service"));
   pushRequest.customData.push_back(CustomDataField("uri", args.uri));
   pushRequest.customData.push_back(CustomDataField("reasonCode", -1));

   if (notificationServerInfo.type == NotificationServerInfo::REGISTER)
   {
      notificationSentEvent.state = NotificationSent::UP;
      notificationSentEvent.type = NotificationSent::REGISTER;
      pushRequest.customData.push_back(CustomDataField("state", "up"));
      pushRequest.customData.push_back(CustomDataField("action", "register"));

      for (cpc::string endpointId : args.endpointIds)
      {
         DebugLog(<< "CloudWatchdogServiceInterface::onQueryServerUsersResult(): send push for endpoint: " << endpointId.c_str() << " for service UP state and REGISTER action");
         pushService->sendPushNotification(endpointId, pushRequest);
      }

      fireEvent(cpcFunc(CloudWatchdogHandler::onNotificationSent), 0, notificationSentEvent);
   }
   else
   {
      DebugLog(<< "CloudWatchdogServiceInterface::onQueryServerUsersResult(): invalid notification type for server: " << args.uri);
   }

   return kSuccess;
}

/*
int CloudWatchdogServiceInterface::onQueryServerUsersResult(int requestHandle, const CPCAPI2::OrchestrationServer::QueryServerUsersResult& args) 
{
   StackLog(<< "CloudWatchdogServiceInterface::onQueryServerUsersResult(): CloudWatchdog queryServerUsers got results (" << args.endpointIds.size() << " endpoints)");

   if (mNotificationServerList.empty())
   {
      DebugLog(<< "CloudWatchdogServiceInterface::onQueryServerUsersResult(): notification list is empty, ignoring sending push notification for uri: " << args.uri);
      return kSuccess;
   }

   NotificationServerInfo notificationServerInfo = mNotificationServerList.front();
   if (notificationServerInfo.uri != args.uri)
   {
      DebugLog(<< "CloudWatchdogServiceInterface::onQueryServerUsersResult(): next notification uri: " << notificationServerInfo.uri << " does not match, ignoring sending push notification for uri: " << args.uri);
      return kSuccess;
   }

   mNotificationServerList.pop_front();

   NotificationSent notificationSentEvent;
   notificationSentEvent.server = args.uri;
   notificationSentEvent.endpointIds = args.endpointIds;

   CPCAPI2::PushService::PushNotificationServiceManager* pushService = CPCAPI2::PushService::PushNotificationServiceManager::getInterface(mPhone);
   CPCAPI2::PushService::PushNotificationRequest pushRequest;
   pushRequest.customData.push_back(CustomDataField("event", "service"));
   pushRequest.customData.push_back(CustomDataField("uri", args.uri));
   pushRequest.customData.push_back(CustomDataField("reasonCode", -1));

   if (notificationServerInfo.serverDown)
   {
      if (notificationServerInfo.missedPush)
      {
         for (cpc::string endpointId : args.endpointIds)
         {
            DebugLog(<< "CloudWatchdogServiceInterface::onQueryServerUsersResult(): send missed push for endpoint: " << endpointId.c_str() << " for service UP state");
            notificationSentEvent.state = NotificationSent::UP;
            pushRequest.customData.push_back(CustomDataField("state", "up"));
            pushService->sendPushNotification(endpointId, pushRequest);
         }
      }
      notificationSentEvent.state = NotificationSent::DOWN;
      pushRequest.customData.push_back(CustomDataField("state", "down"));
   }
   else
   {
      if (notificationServerInfo.missedPush)
      {
         for (cpc::string endpointId : args.endpointIds)
         {
            DebugLog(<< "CloudWatchdogServiceInterface::onQueryServerUsersResult(): send missed push for endpoint: " << endpointId.c_str() << " for service DOWN state");
            notificationSentEvent.state = NotificationSent::DOWN;
            pushRequest.customData.push_back(CustomDataField("state", "down"));
            pushService->sendPushNotification(endpointId, pushRequest);
         }
      }
      notificationSentEvent.state = NotificationSent::UP;
      pushRequest.customData.push_back(CustomDataField("state", "up"));
   }

   for (cpc::string endpointId : args.endpointIds)
   {
      DebugLog(<< "CloudWatchdogServiceInterface::onQueryServerUsersResult(): send push for endpoint: " << endpointId.c_str() << " for service " << (notificationServerInfo.serverDown ? "DOWN" : "UP") << " state");
      pushService->sendPushNotification(endpointId, pushRequest);
   }

   fireEvent(cpcFunc(CloudWatchdogHandler::onNotificationSent), 0, notificationSentEvent);

   return kSuccess;
}
*/

}

}

#endif
