#include "brand_branded.h"

#include "interface/experimental/filetransfer/SipFileTransferManager.h"
#include "interface/experimental/filetransfer/SipFileTransferState.h"

#if (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1)
#include "SipFileTransferManagerInterface.h"
#include "SipFileTransferStateImpl.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace SipFileTransfer
{

SipFileTransferManager* SipFileTransferManager::getInterface( CPCAPI2::Phone* cpcPhone )
{
#if (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<SipFileTransferManagerInterface>(phone, "SipFileTransferManagerInterface");
#else
   return NULL;
#endif
}

SipFileTransferStateManager* SipFileTransferStateManager::getInterface(SipFileTransferManager* cpcConvMan)
{
#if (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1)
   SipFileTransferManagerInterface* parent = dynamic_cast<SipFileTransferManagerInterface*>(cpcConvMan);
   if (parent == NULL) return NULL;
   PhoneInterface* phone = parent->phoneInterface();
   return _GetInterfaceEx<SipFileTransferStateImpl>(phone, "SipFileTransferStateManager", parent);
#else
   return NULL;
#endif
}

}
}
