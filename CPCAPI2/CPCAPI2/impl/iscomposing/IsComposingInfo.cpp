#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_CHAT_MODULE==1 || CPCAPI2_BRAND_IM_MODULE==1 || CPCAPI2_BRAND_XMPP_CHAT_MODULE==1 || CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
#include "IsComposingInfo.h"

namespace CPCAPI2
{
namespace IsComposing
{

IsComposingInfo::IsComposingInfo()
   : composerState(NULL),
     composerIdleTimer(NULL),
     composerIdleInterval(0),
     composerRefreshInterval(0),
     composerRefreshTimer(NULL),
     receiverState(NULL),
     receiverRefreshInterval(0),
     receiverRefreshTimer(NULL)
{
}

IsComposingInfo::~IsComposingInfo()
{
   // Stop and destroy the composer idle timer
   if (composerIdleTimer)
   {
      composerIdleTimer->cancel();
      delete composerIdleTimer;
   }

   // Stop and destroy the composer refresh timer
   if (composerRefreshTimer)
   {
      composerRefreshTimer->cancel();
      delete composerRefreshTimer;
   }

   // Stop and destroy the receiver refresh timer
   if (receiverRefreshTimer)
   {
      receiverRefreshTimer->cancel();
      delete receiverRefreshTimer;
   }
}

}
}
#endif
