#include "jsonrpc/OptionalSerialization.h"
#include <any>
#include "jsonrpc/VectorSerialization.h"
#include <nlohmann/json.hpp>
#include "MediaEncryptionMode.h"
#include "MediaCryptoSuite.h"

#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_MEDIAENCRYPTIONOPTIONS_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_MEDIAENCRYPTIONOPTIONS_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class MediaEncryptionOptions
  {
  public:
    std::optional<MediaEncryptionMode> mediaEncryptionMode;
    std::optional<bool> secureMediaRequired;
    std::optional<cpc::vector<MediaCryptoSuite>> mediaCryptoSuites;

    MediaEncryptionOptions() = default;

    MediaEncryptionOptions(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const MediaEncryptionOptions& o)
    {
      j = nlohmann::json::object();
       if (o.mediaEncryptionMode) { j["mediaEncryptionMode"] = *(o.mediaEncryptionMode); }
     if (o.secureMediaRequired) { j["secureMediaRequired"] = *(o.secureMediaRequired); }
     if (o.mediaCryptoSuites) { j["mediaCryptoSuites"] = *(o.mediaCryptoSuites); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, MediaEncryptionOptions& o)
    {
     if (j.contains("mediaEncryptionMode")) { o.mediaEncryptionMode = j.at("mediaEncryptionMode"); }
     if (j.contains("secureMediaRequired")) { o.secureMediaRequired = j.at("secureMediaRequired"); }
     if (j.contains("mediaCryptoSuites")) { o.mediaCryptoSuites = j.at("mediaCryptoSuites"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_MEDIAENCRYPTIONOPTIONS_H
