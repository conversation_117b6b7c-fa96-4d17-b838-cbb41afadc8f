#pragma once

#if !defined(CPCAPI2_CONFERENCE_CONNECTOR_INTERFACE_H)
#define CPCAPI2_CONFERENCE_CONNECTOR_INTERFACE_H

#include "cpcapi2defs.h"
#include "confconnector/ConferenceConnector.h"
#include "confconnector/ConferenceConnectorInternal.h"
#include "confconnector/ConferenceConnectorHandler.h"
#include "phone/Cpcapi2EventSource.h"
#include "phone/PhoneModule.h"

namespace CPCAPI2
{

class PhoneInterface;
namespace ConferenceConnector
{

class ConferenceConnectorImpl;
class ConferenceConnectorSyncHandler {};
class ConferenceConnectorInterface : public CPCAPI2::EventSource<ConferenceConnectorHandle, ConferenceConnectorHandler, ConferenceConnectorSyncHandler>,
                                     public PhoneModule,
                                     public ConferenceConnectorInternal
{

public:
   ConferenceConnectorInterface(Phone* phone);
   virtual ~ConferenceConnectorInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // ConferenceConnectorManager
   virtual int process(unsigned int timeout) OVERRIDE {
      return CPCAPI2::EventSource<ConferenceConnectorHandle, ConferenceConnectorHandler, ConferenceConnectorSyncHandler>::process(timeout);
   }
   virtual void interruptProcess() OVERRIDE {
      CPCAPI2::EventSource<ConferenceConnectorHandle, ConferenceConnectorHandler, ConferenceConnectorSyncHandler>::interruptProcess();
   }
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE {
      CPCAPI2::EventSource<ConferenceConnectorHandle, ConferenceConnectorHandler, ConferenceConnectorSyncHandler>::setCallbackHook(cbHook, context);
   }

   virtual ConferenceConnectorHandle createConferenceConnector() OVERRIDE;
   virtual int setHandler(ConferenceConnectorHandle conn, ConferenceConnectorHandler* handler) OVERRIDE;
   virtual int destroyConferenceConnector(ConferenceConnectorHandle conn) OVERRIDE;
   virtual int setConnectionSettings(ConferenceConnectorHandle conn, const ConferenceConnectorSettings& settings) OVERRIDE;
   virtual int connectToConferenceService(ConferenceConnectorHandle conn) OVERRIDE;
   virtual int disconnectFromConferenceService(ConferenceConnectorHandle conn) OVERRIDE;
   virtual int queryConferenceList(ConferenceConnectorHandle conn) OVERRIDE;
   virtual int createNewConference(ConferenceConnectorHandle conn, const CloudConferenceSettings& settings) OVERRIDE;
   virtual int queryParticipantList(CloudConferenceHandle conference) OVERRIDE;
   virtual int destroyConference(CloudConferenceHandle conference) OVERRIDE;
   virtual CloudConferenceSessionHandle createConferenceSession(CloudConferenceHandle conference) OVERRIDE;
   virtual CloudConferenceSessionHandle createConferenceSession(CloudConferenceHandle conference, const CloudConferenceSessionSettings& settings) OVERRIDE;
   virtual int setSessionSettings(CloudConferenceSessionHandle session, const CloudConferenceSessionSettings& settings) OVERRIDE;
   virtual int setSessionMediaSettings(CloudConferenceSessionHandle session, const CloudConferenceSessionMediaSettings& settings) OVERRIDE;
   virtual int startSession(CloudConferenceSessionHandle session) OVERRIDE;
   virtual int endSession(CloudConferenceSessionHandle session) OVERRIDE;
   virtual int updateSessionMedia(CloudConferenceSessionHandle session) OVERRIDE;
   virtual int setParticipantPermissions(CloudConferenceSessionHandle session, CloudConferenceParticipantHandle participant, const ConferenceSessionPermissions& permissions) OVERRIDE;
   virtual int pauseSessionPlayout(CloudConferenceSessionHandle session) OVERRIDE;
   virtual int resumeSessionPlayout(CloudConferenceSessionHandle session) OVERRIDE;

   // ConferenceConnectorInternal
   virtual int dropIncomingJsonMessages(ConferenceConnectorHandle conn, bool enable) OVERRIDE;
   virtual int queryMediaStatistics(CloudConferenceSessionHandle session) OVERRIDE;
   virtual int setMediaInactivityMonitor(CloudConferenceSessionHandle session, bool enableMediaInactivityMonitor, int mediaInactivityIntervalMsecs) OVERRIDE;

   PhoneInterface* getThisSdkPhone() const {
      return mPhone;
   }

   void fireConnStatusEvent(ConferenceConnectorHandle conn, const ServiceConnectionStatusEvent& evt);
   void fireConfCreatedEvent(ConferenceConnectorHandle conn, const ConferenceCreatedEvent& evt);
   void fireConfEndedEvent(ConferenceConnectorHandle conn, const ConferenceEndedEvent& evt);
   void fireConfListUpdatedEvent(ConferenceConnectorHandle conn, const ConferenceListUpdatedEvent& evt);
   void firePartListUpdatedEvent(ConferenceConnectorHandle conn, const ConferenceParticipantListUpdatedEvent& args);
   void fireConfSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args);
   void fireConfSessionMediaStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionMediaStatusChangedEvent& args);
   void fireConfConnMediaStatistics(ConferenceConnectorHandle conn, const ConferenceConnectorMediaStatisticsEvent& args);

   void mapConferenceHandleToInst(CloudConferenceHandle conference, const std::shared_ptr<ConferenceConnectorImpl>& inst);
   void unmapConferenceHandle(CloudConferenceHandle conference);
   void unmapSessionHandle(CloudConferenceSessionHandle session);
   int createConferenceSession(CloudConferenceSessionHandle session, CloudConferenceHandle conference, const CloudConferenceSessionSettings& settings);
   int endSession(CloudConferenceSessionHandle session, bool isReconnecting = false);

private:
   void createConferenceSessionImpl(CloudConferenceSessionHandle session, CloudConferenceHandle conference, const CloudConferenceSessionSettings& settings);
   void createConferenceConnectorImpl(ConferenceConnectorHandle conn);
   void destroyConferenceConnectorImpl(ConferenceConnectorHandle conn);
   void setConnectionSettingsImpl(ConferenceConnectorHandle conn, const ConferenceConnectorSettings& settings);
   void connectToConferenceServiceImpl(ConferenceConnectorHandle conn);
   void disconnectFromConferenceServiceImpl(ConferenceConnectorHandle conn);
   void queryConferenceListImpl(ConferenceConnectorHandle conn);
   void createNewConferenceImpl(ConferenceConnectorHandle conn, const CloudConferenceSettings& settings);
   void destroyConferenceImpl(CloudConferenceHandle conference);
   void queryParticipantListImpl(CloudConferenceHandle conference);
   void setSessionSettingsImpl(CloudConferenceSessionHandle session, const CloudConferenceSessionSettings& settings);
   void setSessionMediaSettingsImpl(CloudConferenceSessionHandle session, const CloudConferenceSessionMediaSettings& settings);
   void startSessionImpl(CloudConferenceSessionHandle session);
   void endSessionImpl(CloudConferenceSessionHandle session, bool isReconnecting);
   void updateSessionMediaImpl(CloudConferenceSessionHandle session);
   void setParticipantPermissionsImpl(CloudConferenceSessionHandle session, CloudConferenceParticipantHandle participant, const ConferenceSessionPermissions& permissions);
   void pauseSessionPlayoutImpl(CloudConferenceSessionHandle session);
   void resumeSessionPlayoutImpl(CloudConferenceSessionHandle session);
   void dropIncomingJsonMessagesImpl(ConferenceConnectorHandle conn, bool enable);
   void queryMediaStatisticsImpl(CloudConferenceSessionHandle session);
   void setMediaInactivityMonitorImpl(CloudConferenceSessionHandle session, bool enableMediaInactivityMonitor, int mediaInactivityIntervalMsecs);

private:
   PhoneInterface* mPhone;
   typedef std::map<ConferenceConnectorHandle, std::shared_ptr<ConferenceConnectorImpl> > InstanceMap;
   InstanceMap mInstMap;
   typedef std::map<CloudConferenceHandle, std::shared_ptr<ConferenceConnectorImpl> > ConferenceMap;
   ConferenceMap mConfMap;
   typedef std::map<CloudConferenceSessionHandle, std::shared_ptr<ConferenceConnectorImpl> > SessionMap;
   SessionMap mSessionMap;
};

class ConferenceConnectorHandleFactory
{
public:
   static ConferenceConnectorHandle getNext() { return sNextHandle++; }
private:
   static ConferenceConnectorHandle sNextHandle;
};
class CloudConferenceHandleFactory
{
public:
   static CloudConferenceHandle getNext() { return sNextHandle++; }
private:
   static CloudConferenceHandle sNextHandle;
};
class ConferenceConnectorSessionHandleFactory
{
public:
   static CloudConferenceSessionHandle getNext() { return sNextHandle++; }
private:
   static CloudConferenceSessionHandle sNextHandle;
};

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ServiceConnectionStatus& status);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ServiceConnectionStatusEvent& event);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceCreatedEvent& event);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceEndedEvent& event);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::CloudConferenceType& confType);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::CloudConferenceInfo& info);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceListUpdatedEvent& event);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& info);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceParticipantListUpdatedEvent& event);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::SessionStatus& status);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::MediaDirection& direction);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::SessionMediaType& media);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceSessionMediaInfo& info);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceSessionPermissions& permissions);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceSessionStatusChangedEvent& event);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceSessionMediaStatusChangedEvent& event);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceConnectorMediaStatisticsEvent& event);

}

}

#endif // CPCAPI2_CONFERENCE_CONNECTOR_INTERFACE_H

