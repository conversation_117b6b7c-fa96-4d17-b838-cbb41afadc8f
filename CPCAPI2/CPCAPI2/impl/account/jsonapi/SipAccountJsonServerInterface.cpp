#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACCOUNT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "SipAccountJsonServerInterface.h"
#include "account/SipAccountInterface.h"
#include "phone/PhoneInterface.h"
#include "json/JsonHelper.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define JSON_MODULE "SipAccountManagerJsonProxy"

namespace CPCAPI2
{
namespace SipAccount
{
SipAccountJsonServerInterface::SipAccountJsonServerInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   SipAccountManager* sipAccount = SipAccountManager::getInterface(phone);
   SipAccountInterface* sipAccountIf = dynamic_cast<SipAccountInterface*>(sipAccount);
   sipAccountIf->addSdkObserver(this);

   mFunctionMap["create"] = std::bind(&SipAccountJsonServerInterface::handleCreate, this, std::placeholders::_1);
   mFunctionMap["configureDefaultAccountSettings"] = std::bind(&SipAccountJsonServerInterface::handleConfigureDefaultAccountSettings, this, std::placeholders::_1);
   mFunctionMap["configureTransportAccountSettings"] = std::bind(&SipAccountJsonServerInterface::handleConfigureTransportAccountSettings, this, std::placeholders::_1);
   mFunctionMap["applySettings"] = std::bind(&SipAccountJsonServerInterface::handleApplySettings, this, std::placeholders::_1);
   mFunctionMap["destroy"] = std::bind(&SipAccountJsonServerInterface::handleDestroy, this, std::placeholders::_1);
   mFunctionMap["enable"] = std::bind(&SipAccountJsonServerInterface::handleEnable, this, std::placeholders::_1);
   mFunctionMap["disable"] = std::bind(&SipAccountJsonServerInterface::handleDisable, this, std::placeholders::_1);
   mFunctionMap["requestRegistrationRefresh"] = std::bind(&SipAccountJsonServerInterface::handleRequestRegistrationRefresh, this, std::placeholders::_1);
   mFunctionMap["setNetworkRestriction"] = std::bind(&SipAccountJsonServerInterface::handleSetNetworkRestriction, this, std::placeholders::_1);
   mFunctionMap["requestStateAllAccounts"] = std::bind(&SipAccountJsonServerInterface::handleRequestStateAllAccounts, this, std::placeholders::_1);
   mFunctionMap["setAdornmentHeaders"] = std::bind(&SipAccountJsonServerInterface::handleSetAdornmentHeaders, this, std::placeholders::_1);

   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
}

SipAccountJsonServerInterface::~SipAccountJsonServerInterface()
{
   SipAccountManager* sipAccount = SipAccountManager::getInterface(mPhone);
   SipAccountInterface* sipAccountIf = dynamic_cast<SipAccountInterface*>(sipAccount);
   sipAccountIf->removeSdkObserver(this);
}

void SipAccountJsonServerInterface::Release()
{
   delete this;
}

void SipAccountJsonServerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void SipAccountJsonServerInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int SipAccountJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&SipAccountJsonServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

void SipAccountJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

int SipAccountJsonServerInterface::handleCreate(const rapidjson::Value& functionObjectVal)
{
   SipAccountManager* sipAccountManager = SipAccountManager::getInterface(mPhone);
   SipAccountSettings accountSettings;

   JsonDeserialize(functionObjectVal, JSON_VALUE(accountSettings));

   SipAccountHandle acctHandle = sipAccountManager->create(accountSettings);

   JsonFunctionCall(mTransport, "createResult", "account", acctHandle);
   return kSuccess;
}

int SipAccountJsonServerInterface::handleConfigureDefaultAccountSettings(const rapidjson::Value& functionObjectVal)
{
   SipAccountManager* sipAccountManager = SipAccountManager::getInterface(mPhone);

   SipAccountHandle account = 0;
   SipAccountSettings sipAccountSettings;
   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(sipAccountSettings));
   sipAccountManager->configureDefaultAccountSettings(account, sipAccountSettings);

   return kSuccess;
}

int SipAccountJsonServerInterface::handleConfigureTransportAccountSettings(const rapidjson::Value& functionObjectVal)
{
   SipAccountManager* sipAccountManager = SipAccountManager::getInterface(mPhone);

   SipAccountHandle account = 0;
   SipAccountSettings sipAccountSettings;
   CPCAPI2::NetworkTransport transport = NetworkTransport::TransportNone;
   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(sipAccountSettings), JSON_VALUE(transport));
   sipAccountManager->configureTransportAccountSettings(account, sipAccountSettings, transport);

   return kSuccess;
}

int SipAccountJsonServerInterface::handleApplySettings(const rapidjson::Value& functionObjectVal)
{
   SipAccountManager* sipAccountManager = SipAccountManager::getInterface(mPhone);

   SipAccountHandle account = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(account));
   sipAccountManager->applySettings(account);

   return kSuccess;
}

int SipAccountJsonServerInterface::handleDestroy(const rapidjson::Value& functionObjectVal)
{
   SipAccountManager* sipAccountManager = SipAccountManager::getInterface(mPhone);

   SipAccountHandle account = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(account));
   sipAccountManager->destroy(account);

   return kSuccess;
}

int SipAccountJsonServerInterface::handleEnable(const rapidjson::Value& functionObjectVal)
{
   SipAccountManager* sipAccountManager = SipAccountManager::getInterface(mPhone);

   SipAccountHandle acctHandle = 0;
   JsonDeserialize(functionObjectVal, "account", acctHandle);
   sipAccountManager->enable(acctHandle);

   return kSuccess;
}

int SipAccountJsonServerInterface::handleDisable(const rapidjson::Value& functionObjectVal)
{
   SipAccountManager* sipAccountManager = SipAccountManager::getInterface(mPhone);

   SipAccountHandle acctHandle = 0;
   JsonDeserialize(functionObjectVal, "account", acctHandle);
   sipAccountManager->disable(acctHandle);

   return kSuccess;
}

int SipAccountJsonServerInterface::handleRequestRegistrationRefresh(const rapidjson::Value& functionObjectVal)
{
   SipAccountManager* sipAccountManager = SipAccountManager::getInterface(mPhone);

   SipAccountHandle account = 0;
   unsigned int deadlineSecondsFromNow = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(deadlineSecondsFromNow));
   sipAccountManager->requestRegistrationRefresh(account, deadlineSecondsFromNow);

   return kSuccess;
}

int SipAccountJsonServerInterface::handleSetNetworkRestriction(const rapidjson::Value& functionObjectVal)
{
   SipAccountManager* sipAccountManager = SipAccountManager::getInterface(mPhone);

   SipAccountHandle account = 0;
   NetworkTransport transport = NetworkTransport::TransportNone;
   bool restricted = false;
   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(transport), JSON_VALUE(restricted));
   sipAccountManager->setNetworkRestriction(account, transport, restricted);

   return kSuccess;
}

int SipAccountJsonServerInterface::handleRequestStateAllAccounts(const rapidjson::Value& functionObjectVal)
{
   SipAccountManager* sipAcctManager = SipAccountManager::getInterface(mPhone);
   SipAccountStateManager* acctStateMgr = SipAccountStateManager::getInterface(sipAcctManager);
   cpc::vector<SipAccountState> acctStateVec;
   acctStateMgr->getStateAllAccounts(acctStateVec);

   JsonFunctionCall(mTransport, "onAccountState", "accountStateArray", acctStateVec);
   return kSuccess;
}

int SipAccountJsonServerInterface::handleSetAdornmentHeaders(const rapidjson::Value & functionObjectVal)
{
   SipAccountManager* sipAcctManager = SipAccountManager::getInterface(mPhone);
   JsonDeserialize(functionObjectVal, "account", mAcctHandle, "headers", mAdornHeaders);
   sipAcctManager->setAdornmentHandler(mAcctHandle, mAdornHeaders.empty() ? nullptr : this);

   return kSuccess;
}

int SipAccountJsonServerInterface::onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args)
{
   JsonFunctionCall(mTransport, "onAccountStatusChanged", JSON_VALUE(account), JSON_VALUE(args));
   return kSuccess;
}

int SipAccountJsonServerInterface::onAccountAdornment(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountAdornmentEvent& args)
{
   if (!mAdornHeaders.empty()) {
      SipAccountManager* sipAcctManager = SipAccountManager::getInterface(mPhone);
      sipAcctManager->adornMessage(mAcctHandle, args.adornmentMessageId, mAdornHeaders);
   }

   return kSuccess;
}
}
}
#endif
