#pragma once

#if !defined(CPCAPI2_GENBAND_REST_API_EVENT_HANDLER_H)
#define CPCAPI2_GENBAND_REST_API_EVENT_HANDLER_H
#endif

#include "cpcapi2defs.h"
//#include "GenbandRestAPI.h"
#include "event/SipEventState.h"
//#include "event/SipEventSubscriptionHandler.h"
#include "genband/GenbandRestAPIHandler.h"
#include "../event/SipEventManagerInterface.h"
#include "../account/SipAccountInterface.h"
#include "../account/SipAccountImpl.h"
#include "../util/DumFpCommand.h"


namespace CPCAPI2
{

namespace SipAccount
{
class SipAccountImpl;
};
namespace SipEvent
{
class SipEventManagerInterface;
};

namespace Genband
{

class GenbandRestAPIEventHandler : public SipEvent::SipEventSubscriptionHandler
{
public:
	GenbandRestAPIEventHandler(CPCAPI2::SipAccount::SipAccountImpl* acct, 
							   CPCAPI2::SipEvent::SipEventManagerInterface* sipEventIf); //CPCAPI2::Genband::GenbandRestAPI& genbandRestApi, 
	~GenbandRestAPIEventHandler();
	virtual int onIncomingCallCommand(SipEventSubscriptionHandle subscription, const CPCAPI2::Genband::GenbandNotification& args);
	void setSubscriptionHandler(GenbandRestAPIHandler* handler);

	virtual int onNewSubscription(SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NewSubscriptionEvent& args){return kSuccess;};
    virtual int onSubscriptionEnded(SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionEndedEvent& args){return kSuccess;};
    virtual int onIncomingEventState(SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingEventStateEvent& args){return kSuccess;};
    virtual int onSubscriptionStateChanged(SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionStateChangedEvent& args){return kSuccess;};

    virtual int onNotifySuccess(SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifySuccessEvent& args){return kSuccess;};
    virtual int onNotifyFailure(SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifyFailureEvent& args){return kSuccess;};

    virtual int onError(SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::ErrorEvent& args){return kSuccess;};
private:
	GenbandRestAPIHandler* subscriptionHandler;
	CPCAPI2::SipAccount::SipAccountImpl* account;
	//CPCAPI2::Genband::GenbandRestAPI& genbandRestApi;
	CPCAPI2::SipEvent::SipEventManagerInterface* sipEventIf;
};

}
}
