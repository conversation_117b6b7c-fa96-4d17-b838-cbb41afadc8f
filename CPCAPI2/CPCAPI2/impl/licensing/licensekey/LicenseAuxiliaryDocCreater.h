#pragma once

#if !defined(__CPCAPI2_LICENSE_AUXILARY_DOC_CREATER_H__)
#define __CPCAPI2_LICENSE_AUXILARY_DOC_CREATER_H__

#include <libxml/xmlreader.h>

namespace CPCAPI2
{
namespace Licensing
{

class LicenseAuxiliaryDocCreater
{
public:
   LicenseAuxiliaryDocCreater(time_t accumulatedRunTime, time_t checkTime);
   LicenseAuxiliaryDocCreater(std::string accumulatedRunTime, std::string checkTime);

   std::string getXml() const;

private:
   std::string m_Xml;

   std::string Create(time_t accumulatedRunTime, time_t checkTime);

};

}
}

#endif // __CPCAPI2_LICENSE_AUXILARY_DOC_CREATER_H__