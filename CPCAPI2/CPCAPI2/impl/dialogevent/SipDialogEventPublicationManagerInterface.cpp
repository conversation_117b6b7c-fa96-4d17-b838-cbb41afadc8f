#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_DIALOG_EVENT_MODULE == 1)

#include "SipDialogEventPublicationManagerInterface.h"
#include "SipDialogEventPublicationManagerImpl.h"
#include "DialogInfoDocumentHelper.h"

using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipAccount;

namespace CPCAPI2
{
namespace SipDialogEvent
{

SipDialogEventPublicationManagerInterface::SipDialogEventPublicationManagerInterface(Phone* phone)
   : EventSource2< EventHandler<SipDialogEventPublicationHandler, SipAccount::SipAccountHandle> >(dynamic_cast<PhoneInterface*>(phone)),
     mAccountIf(NULL),
     mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mAccountMapPtr(new AccountMap),
     mAccountMap(*mAccountMapPtr)
{
   mAccountIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(phone));
   mSipPublicationIf = dynamic_cast<SipEventPublicationManagerInterface*>(SipEventPublicationManager::getInterface(phone));
}

SipDialogEventPublicationManagerInterface::~SipDialogEventPublicationManagerInterface()
{
   // Delete all the registered handlers and clear the map
   for (AccountMap::iterator it = mAccountMap.begin(); it != mAccountMap.end(); it++)
   {
      SipDialogEventPublicationManagerImpl* dialogEventManagerImpl = it->second;
      delete dialogEventManagerImpl;
   }   
   mAccountMap.clear();
}

void SipDialogEventPublicationManagerInterface::Release()
{
   delete this;
}

int SipDialogEventPublicationManagerInterface::setHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipDialogEventPublicationHandler* handler)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&SipDialogEventPublicationManagerInterface::setHandlerImpl, this, account, handler);
   
   if (handler == NULL)
   {
      // removing the handler involves two steps:
      // 1. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);

      // 2. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      executeOnSdkThread(f);
   }
   else
   {
      postToSdkThread(f);
   }
   
   return kSuccess;
}

int SipDialogEventPublicationManagerInterface::setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipDialogEventPublicationHandler* handler)
{
   // Get the account
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (!acct)
   {
      mAccountIf->fireError("Invalid account handle for SipDialogEventPublicationManagerInterface::setHandler");
      return kError;
   }

   // Get the associated manager
   SipDialogEventPublicationManagerImpl* dialogEventPublicationManager = getManagerForAccount(account);
   if (!dialogEventPublicationManager)
   {
      // Make sure the account is not enabled at this point
      if (acct->isEnabled())
      {
         mAccountIf->fireError("SipDialogEventPublicationManagerInterface::setHandler was called after account enabled: " + cpc::to_string(account));
         return kSuccess;
      }
      
      // Manager not found for this account
      // Create the manager and keep the association with the account
      dialogEventPublicationManager = new SipDialogEventPublicationManagerImpl(this, mSipPublicationIf, mAccountMapPtr, *acct);
      mAccountMap[account] = dialogEventPublicationManager;
      
      mSipPublicationIf->setHandlerImpl(account, DialogInfoDocumentHelper::EVENT_PACKAGE_NAME, dialogEventPublicationManager);
   }

   auto it = mHandlers.find(account);
   if (mHandlers.end() != it)
   {
      removeAppHandler(it->second, account);
   }

   mHandlers[account] = handler;
   if (nullptr != handler)
   {
      addAppHandler(handler, account);
   }

   return kSuccess;
}

SipDialogEventPublicationHandle SipDialogEventPublicationManagerInterface::createPublication(CPCAPI2::SipAccount::SipAccountHandle account, const SipDialogEventPublicationSettings& settings)
{
   // Create the event settings
   SipEventPublicationSettings eventSettings;
   eventSettings.eventPackage = DialogInfoDocumentHelper::EVENT_PACKAGE_NAME;
   Parameter param(DialogInfoDocumentHelper::SHARED_APPEARANCE_DIALOG_EXTENSIONS_PARAM_NAME, "");
   eventSettings.eventPackageParams.push_back(param);
   eventSettings.expiresSeconds = settings.expiresSeconds;
   eventSettings.supportedMimeTypes.push_back(DialogInfoDocumentHelper::EVENT_MIME_TYPE);

   return mSipPublicationIf->createPublication(account, eventSettings);
}

int SipDialogEventPublicationManagerInterface::setTarget(SipDialogEventPublicationHandle publication, const cpc::string& targetAddress)
{
   return mSipPublicationIf->setTarget(publication, targetAddress);
}

int SipDialogEventPublicationManagerInterface::publish(SipDialogEventPublicationHandle publication, const DialogInfoDocument& dialogInfoDoc)
{
   postToSdkThread(resip::resip_bind(&SipDialogEventPublicationManagerInterface::publishImpl, this, publication, dialogInfoDoc));
   return kSuccess;
}

int SipDialogEventPublicationManagerInterface::end(SipDialogEventPublicationHandle publication)
{
   return mSipPublicationIf->end(publication);
}

int SipDialogEventPublicationManagerInterface::publishImpl(SipEventPublicationHandle publication, const DialogInfoDocument& dialogInfoDoc)
{
   // Create the event state
   SipEventState eventState = DialogInfoDocumentHelper::createEventState(dialogInfoDoc, false, true);

   // Public the dialog-info document
   mSipPublicationIf->publish(publication, eventState);
   return kSuccess;
}

SipDialogEventPublicationManagerImpl* SipDialogEventPublicationManagerInterface::getManagerForAccount(CPCAPI2::SipAccount::SipAccountHandle account)
{
   AccountMap::iterator it = mAccountMap.find(account);
   return (it != mAccountMap.end()) ? it->second : NULL;
}

std::ostream& operator<<(std::ostream& os, const DialogEventPublicationSuccessEvent& evt)
{
   return os << "DialogEventPublicationSuccessEvent";
}

std::ostream& operator<<(std::ostream& os, const DialogEventPublicationFailureEvent& evt)
{
   return os << "DialogEventPublicationFailureEvent";
}

std::ostream& operator<<(std::ostream& os, const DialogEventPublicationRemoveEvent& evt)
{
   return os << "DialogEventPublicationRemoveEvent";
}

std::ostream& operator<<(std::ostream& os, const DialogEventPublicationErrorEvent& evt)
{
   return os << "DialogEventPublicationErrorEvent";
}

}
}

#endif // CPCAPI2_BRAND_SIP_DIALOG_EVENT_MODULE
