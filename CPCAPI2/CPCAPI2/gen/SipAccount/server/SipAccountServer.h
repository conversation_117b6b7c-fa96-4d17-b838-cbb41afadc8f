#include "ISipAccount.h"
#include "impl/jsonrpc/IJsonRpcServerModuleProcessing.h"

#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTSERVER_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTSERVER_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
class SipAccountServer
{
public:
    SipAccountServer(::CPCAPI2::IJsonRpcServerModuleProcessing& transport, ISipAccount& impl) :
        mTransport(transport),
        mImpl(impl)
    {
        mTransport.registerFunction("SipAccount::create", jsonrpccxx::methodHandle(&ISipAccount::create, mImpl), {{ "phoneHandle" }});
	mTransport.registerFunction("SipAccount::destroy", jsonrpccxx::methodHandle(&ISipAccount::destroy, mImpl), {{ "phoneHandle" }, { "sipAccountHandle" }});
	mTransport.registerFunction("SipAccount::configureDefaultAccountSettings", jsonrpccxx::methodHandle(&ISipAccount::configureDefaultAccountSettings, mImpl), {{ "phoneHandle" }, { "sipAccountHandle" }, { "sipAccountSettings" }});
	mTransport.registerFunction("SipAccount::configureTransportAccountSettings", jsonrpccxx::methodHandle(&ISipAccount::configureTransportAccountSettings, mImpl), {{ "phoneHandle" }, { "sipAccountHandle" }, { "sipAccountSettings" }, { "transport" }});
	mTransport.registerFunction("SipAccount::applySettings", jsonrpccxx::methodHandle(&ISipAccount::applySettings, mImpl), {{ "phoneHandle" }, { "sipAccountHandle" }});
	mTransport.registerFunction("SipAccount::enable", jsonrpccxx::methodHandle(&ISipAccount::enable, mImpl), {{ "phoneHandle" }, { "sipAccountHandle" }});
	mTransport.registerFunction("SipAccount::disable", jsonrpccxx::methodHandle(&ISipAccount::disable, mImpl), {{ "phoneHandle" }, { "sipAccountHandle" }});
    }

private:
    ::CPCAPI2::IJsonRpcServerModuleProcessing& mTransport;
    ISipAccount& mImpl;
}; // class SipAccountServer
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTSERVER_H
