#pragma once

#if !defined(CPCAPI2_LDAP_CLIENT_IMPL_H)
#define CPCAPI2_LDAP_CLIENT_IMPL_H

#include "../util/DumFpCommand.h"

#include "cpcapi2defs.h"
#include "LdapManagerInterface.h"
#include "ldap/LdapHandler.h"
#ifdef WIN32
#include <Winldap.h>
#else
#include <ldap.h>
#endif


namespace CPCAPI2
{
  namespace OpenLdap
  {

  class LdapClientImpl
  {
  public:
     LdapClientImpl(LdapClientHandle handle, LdapManagerInterface* itf);
     ~LdapClientImpl();

     void setHandler(LdapHandler* handler);
	 void setSdkObserver(const std::set<LdapHandler*>* observers);
	 
	 int LdapInit();
	 int LdapApplySettings(const LdapClientSettings& settings);
	 int LdapConnect();
	 int LdapDisconnect();
	 void LdapSetDataMap(LdapDataMap map);
	 int LdapSearch(cpc::string searchPattern, cpc::string rootDn, LdapSearchScope scopeSetting,int timeout,int maxsize, bool opErrIgnore);
   
  private:
     LdapClientHandle mHandle;
     LdapManagerInterface* mInterface;
	 
	 LdapHandler* mAppHandler;
	 const std::set<LdapHandler*>* mSdkObservers;

	 LdapState mState;
	 
	 void SetLdapState(LdapState state);
	 void FireError(LdapErrorType type, cpc::string error);
	 
	 void LdapParseResult(cpc::vector<LdapDataEntry>* search_result_list);
	 int LdapSearchCount();
	 int LdapSearchFirstEntry();
	 int LdapSearchNextEntry();
	 const char* LdapSearchFirstAttribute();
	 const char* LdapSearchNextAttribute();
	 const char* LdapSearchAttributeValues();
	 int LdapSearchCleanup();
	 //=========================================================================
	 
    template<typename TFn, typename TEvt> void fireEvent(const char* funcName, TFn func, LdapClientHandle h, const TEvt& args, bool internalOnly = false, bool logging = true)
    {
      if (mSdkObservers != NULL)
      {
         for (std::set<LdapHandler*>::iterator itHandler = mSdkObservers->begin(); itHandler != mSdkObservers->end(); ++itHandler)
         {
            resip::ReadCallbackBase* cb = resip::resip_bind(func, *itHandler, h, args);
            if (dynamic_cast<LdapHandler*>(*itHandler) != NULL)
            {
               (*cb)();
               delete cb;
            }
            else
            {
               mInterface->postCallback(cb);
            }
         }
      }

      //if (logging)
      //   logEvent(funcName, h);

      if (!internalOnly)
      {
         resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, mAppHandler, h, args);
         mInterface->postCallback(cb);
      }
   }
//=========================================================================================
	 
   private:	 
     cpc::string mUserName;
     cpc::string mPassword;
     cpc::string mServerUrl;
  
     LdapEncryption mEncryption;
     LdapCertStrategy mCertStrategy;
     LdapDataMap mDataMap;
     int mTimeout;
     bool mChaseReferrals;
     bool mArecExclusive; // Request only A records (Windows only)
	 
     LDAP* mServerConnection;
     LDAPMessage* mSearchResult;
     LDAPMessage* mCurrentEntry;
     BerElement* mCurrentElement;
     char* currentAttribute;
	 
  };
	
  }//namespace OpenLdap
}//namespace CPCAPI2
#endif
