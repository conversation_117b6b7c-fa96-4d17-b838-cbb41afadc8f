#include "brand_branded.h"

#include "interface/experimental/audiotranscription/AudioTrans.h"

#if (CPCAPI2_BRAND_AUDIO_TRANS_MODULE == 1)
#include "AudioTransInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace AudioTrans
{
   AudioTransManager* AudioTransManager::getInterface(Phone* cpcPhone)
   {
#if (CPCAPI2_BRAND_AUDIO_TRANS_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<AudioTransInterface>(phone, "AudioTransManager");
#else
      return NULL;
#endif
   }

}
}
