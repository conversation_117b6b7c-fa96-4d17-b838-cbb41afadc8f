#include "brand_branded.h"

#ifdef CPCAPI2_GENBAND_MODULE

#include "cpcapi2utils.h"
#include "GenbandRestAPIEventHandler.h"

using namespace CPCAPI2::SipEvent;
using namespace resip;

namespace CPCAPI2
{
namespace Genband
{

GenbandRestAPIEventHandler::GenbandRestAPIEventHandler(CPCAPI2::SipAccount::SipAccountImpl* acct, 
													   CPCAPI2::SipEvent::SipEventManagerInterface* sipEventIf)
   : subscriptionHandler(NULL),
     account(acct),
	 sipEventIf(sipEventIf)
     //genbandRestApi(genbandRestApi), CPCAPI2::Genband::GenbandRestAPI& genbandRestApi,
	 
{
}

GenbandRestAPIEventHandler::~GenbandRestAPIEventHandler()
{
}

void GenbandRestAPIEventHandler::setSubscriptionHandler(GenbandRestAPIHandler* handler)
{
	subscriptionHandler = handler;
}

int GenbandRestAPIEventHandler::onIncomingCallCommand(SipEventSubscriptionHandle subscription, const CPCAPI2::Genband::GenbandNotification& args)
{
	ReadCallbackBase* cb = makeFpCommand(
		GenbandRestAPIHandler::onIncomingCommand,
         subscriptionHandler,
         subscription,
         args);
      account->postCallback(cb);
	  return 1;
}

}
}
#endif