#pragma once

#if !defined(CPCAPI2_SIP_EVENT_PUBLICATION_MANAGER_IMPL_H)
#define CPCAPI2_SIP_EVENT_PUBLICATION_MANAGER_IMPL_H

#include "cpcapi2defs.h"
#include "event/SipPublicationManager.h"
#include "../account/SipAccountImpl.h"
#include "../account/SipAccountAwareFeature.h"
#include "SipEventPublicationCreationInfo.h"
#include "SipEventPublicationManagerInterface.h"

#include <resip/dum/PublicationHandler.hxx>

#include <string>

#include <resip/dum/ClientPublication.hxx>
#include <resip/dum/ServerPublication.hxx>

namespace CPCAPI2
{
namespace SipEvent
{
class SipEventPublicationHandler;

class SipEventPublicationManagerImpl : public CPCAPI2::SipAccount::SipAccountAwareFeature, 
                                       public resip::ClientPublicationHandler
                                       //public resip::ServerPublicationHandler  
{
public:
   
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, SipEventPublicationManagerImpl*> AccountMap;
   SipEventPublicationManagerImpl(std::shared_ptr<AccountMap> parentMap, CPCAPI2::SipAccount::SipAccountImpl & account, CPCAPI2::SipEvent::SipEventPublicationManagerInterface* sipEventIf);
   virtual ~SipEventPublicationManagerImpl();

   void addEventType(const std::string& eventType);

   void fireError(const SipEventPublicationHandle& h, const cpc::string& errorText);

   resip::SharedPtr<resip::DialogUsageManager> getDum() { return mDum; }

   // CreationInfo
   SipEventPublicationCreationInfo* getCreationInfo(const SipEventPublicationHandle& h) const;
   void addCreationInfo(const SipEventPublicationHandle& h, SipEventPublicationCreationInfo* ci);
   void removeCreationInfo(const SipEventPublicationHandle& h); 

   // Publication Settings
   void setPublicationSettings( const SipEventPublicationSettings & settings);
   SipEventPublicationSettings & getPublicationSettings() { return mPublicationSettings;};

   // SipAccountAwareFeature
   virtual int adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile) OVERRIDE { return kSuccess; }
   virtual int addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig) OVERRIDE;
   virtual int onDumBeingDestroyed() OVERRIDE;
   virtual void release() OVERRIDE;
   virtual int onRegistrationSuccess(const SipAccount::SipRegistrationSuccessEvent& args) OVERRIDE;

   // ClientPublicationHandler
   virtual void onSuccess(resip::ClientPublicationHandle h, const resip::SipMessage& status) OVERRIDE;
   virtual void onRemove(resip::ClientPublicationHandle h, const resip::SipMessage& status) OVERRIDE;
   virtual void onFailure(resip::ClientPublicationHandle h, const resip::SipMessage& status) OVERRIDE;
   virtual int onRequestRetry(resip::ClientPublicationHandle h, int retrySeconds, const resip::SipMessage& status) OVERRIDE;

   //
   // ServerPublicationHandler 
/*
   virtual void onInitial(resip::ServerPublicationHandle h,
                           const resip::Data& etag,
                           const resip::SipMessage& pub,
                           const resip::Contents* contents,
                           const resip::SecurityAttributes* attrs,
                           UInt32 expires);
   virtual void onExpired(resip::ServerPublicationHandle h, const resip::Data& etag);
   virtual void onRefresh(resip::ServerPublicationHandle h, const resip::Data& etag,
                           const resip::SipMessage& pub,
                           const resip::Contents* contents,
                           const resip::SecurityAttributes* attrs,
                           UInt32 expires);
   virtual void onUpdate(resip::ServerPublicationHandle h,
                           const resip::Data& etag,
                           const resip::SipMessage& pub,
                           const resip::Contents* contents,
                           const resip::SecurityAttributes* attrs,
                           UInt32 expires);
   virtual void onRemoved(resip::ServerPublicationHandle h,
                           const resip::Data& etag,
                           const resip::SipMessage& pub,
                           UInt32 expires);
*/
private:
   //SipEventPublicationHandle findSipEventPublicationHandleByResipHandle(resip::ServerPublicationHandle h);
   SipEventPublicationHandle findSipEventPublicationHandleByResipHandle(resip::ClientPublicationHandle h);

private:
   
   CPCAPI2::SipEvent::SipEventPublicationManagerInterface* mSipEventIf;
   SipEventState mState;
   CPCAPI2::SipAccount::SipAccountImpl& mAccount;
   struct SEPHandlerInfo
   {
      bool operator<(const SEPHandlerInfo& rhs) const {
         if (eventType == rhs.eventType) {
            return (handler < rhs.handler);
         }
         return (eventType < rhs.eventType);
      }
      resip::Data eventType;
      SipEventPublicationHandler* handler;
   };
   std::set<resip::Data> mEventTypes;
   SipEventPublicationCreationInfoMap mCreationInfo;
   SipEventPublicationSettings mPublicationSettings;
   resip::SharedPtr<resip::DialogUsageManager> mDum;
   std::weak_ptr<AccountMap> mParentMap;

};
}
}
#endif // CPCAPI2_SIP_EVENT_PUBLICATION_MANAGER_IMPL_H
