// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef ConferenceBridge_JSON_HELPER_H
#define ConferenceBridge_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/experimental/confbridge/ConferenceBridgeTypes.h>
#include <interface/experimental/confbridge/ConferenceBridgeHandler.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::ParticipantMediaStream& v)
    {
      w.StartObject();
      Write(w, "mediaType", v.mediaType);
      Write(w, "mediaState", v.mediaState);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::ParticipantMediaStream& v)
    {
      Read(json, "mediaType", v.mediaType);
      Read(json, "mediaState", v.mediaState);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::WebParticipantDetails& v)
    {
      w.StartObject();
      Write(w, "peerConnection", v.peerConnection);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::WebParticipantDetails& v)
    {
      Read(json, "peerConnection", v.peerConnection);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::SIPParticipantDetails& v)
    {
      w.StartObject();
      Write(w, "conversation", v.conversation);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::SIPParticipantDetails& v)
    {
      Read(json, "conversation", v.conversation);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::ParticipantPermissions& v)
    {
      w.StartObject();
      Write(w, "canCreateConference", v.canCreateConference);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::ParticipantPermissions& v)
    {
      Read(json, "canCreateConference", v.canCreateConference);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::ParticipantInfo& v)
    {
      w.StartObject();
      Write(w, "participant", v.participant);
      Write(w, "participantType", v.participantType);
      Write(w, "address", v.address);
      Write(w, "displayName", v.displayName);
      Write(w, "tags", v.tags);
      Write(w, "media", v.media);
      Write(w, "hasFloor", v.hasFloor);
      Write(w, "webParticipantDetails", v.webParticipantDetails);
      Write(w, "sipParticipantDetails", v.sipParticipantDetails);
      Write(w, "permissions", v.permissions);
      Write(w, "joinTimestampMsecs", v.joinTimestampMsecs);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::ParticipantInfo& v)
    {
      Read(json, "participant", v.participant);
      Read(json, "participantType", v.participantType);
      Read(json, "address", v.address);
      Read(json, "displayName", v.displayName);
      Read(json, "tags", v.tags);
      Read(json, "media", v.media);
      Read(json, "hasFloor", v.hasFloor);
      Read(json, "webParticipantDetails", v.webParticipantDetails);
      Read(json, "sipParticipantDetails", v.sipParticipantDetails);
      Read(json, "permissions", v.permissions);
      Read(json, "joinTimestampMsecs", v.joinTimestampMsecs);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::WebParticipantIdentity& v)
    {
      w.StartObject();
      Write(w, "address", v.address);
      Write(w, "displayName", v.displayName);
      Write(w, "owner", v.owner);
      Write(w, "tags", v.tags);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::WebParticipantIdentity& v)
    {
      Read(json, "address", v.address);
      Read(json, "displayName", v.displayName);
      Read(json, "owner", v.owner);
      Read(json, "tags", v.tags);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::ConferenceNatTraversalServerInfo& v)
    {
      w.StartObject();
      Write(w, "natTraversalServerHostname", v.natTraversalServerHostname);
      Write(w, "natTraversalServerUsername", v.natTraversalServerUsername);
      Write(w, "natTraversalServerPassword", v.natTraversalServerPassword);
      Write(w, "natTraversalServerPort", v.natTraversalServerPort);
      Write(w, "natTraversalServerType", v.natTraversalServerType);
      Write(w, "natTraversalServerType2", v.natTraversalServerType2);
      Write(w, "serverPublicIpAddress", v.serverPublicIpAddress);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::ConferenceNatTraversalServerInfo& v)
    {
      Read(json, "natTraversalServerHostname", v.natTraversalServerHostname);
      Read(json, "natTraversalServerUsername", v.natTraversalServerUsername);
      Read(json, "natTraversalServerPassword", v.natTraversalServerPassword);
      Read(json, "natTraversalServerPort", v.natTraversalServerPort);
      Read(json, "natTraversalServerType", v.natTraversalServerType);
      Read(json, "natTraversalServerType2", v.natTraversalServerType2);
      Read(json, "serverPublicIpAddress", v.serverPublicIpAddress);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::ConferenceBitrateConfig& v)
    {
      w.StartObject();
      Write(w, "videoMaxBitrateKbps", v.videoMaxBitrateKbps);
      Write(w, "videoTargetBitrateKbps", v.videoTargetBitrateKbps);
      Write(w, "videoStartBitrateKbps", v.videoStartBitrateKbps);
      Write(w, "videoMinBitrateKbps", v.videoMinBitrateKbps);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::ConferenceBitrateConfig& v)
    {
      Read(json, "videoMaxBitrateKbps", v.videoMaxBitrateKbps);
      Read(json, "videoTargetBitrateKbps", v.videoTargetBitrateKbps);
      Read(json, "videoStartBitrateKbps", v.videoStartBitrateKbps);
      Read(json, "videoMinBitrateKbps", v.videoMinBitrateKbps);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::ConferenceSettings& v)
    {
      w.StartObject();
      Write(w, "participantJoinSound", v.participantJoinSound);
      Write(w, "participantLeaveSound", v.participantLeaveSound);
      Write(w, "mixMode", v.mixMode);
      Write(w, "persistent", v.persistent);
      Write(w, "isPublic", v.isPublic);
      Write(w, "adaptVideoCodecOnRemotePacketLoss", v.adaptVideoCodecOnRemotePacketLoss);
      Write(w, "label", v.label);
      Write(w, "tags", v.tags);
      Write(w, "conferenceToken", v.conferenceToken);
      Write(w, "mixerSettings", v.mixerSettings);
      Write(w, "mediaDscp", v.mediaDscp);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::ConferenceSettings& v)
    {
      Read(json, "participantJoinSound", v.participantJoinSound);
      Read(json, "participantLeaveSound", v.participantLeaveSound);
      Read(json, "mixMode", v.mixMode);
      Read(json, "persistent", v.persistent);
      Read(json, "isPublic", v.isPublic);
      Read(json, "adaptVideoCodecOnRemotePacketLoss", v.adaptVideoCodecOnRemotePacketLoss);
      Read(json, "label", v.label);
      Read(json, "tags", v.tags);
      Read(json, "conferenceToken", v.conferenceToken);
      Read(json, "mixerSettings", v.mixerSettings);
      Read(json, "mediaDscp", v.mediaDscp);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::ConferenceInfo& v)
    {
      w.StartObject();
      Write(w, "label", v.label);
      Write(w, "owner", v.owner);
      Write(w, "tags", v.tags);
      Write(w, "conferenceToken", v.conferenceToken);
      Write(w, "conference", v.conference);
      Write(w, "description", v.description);
      Write(w, "creatorDisplayName", v.creatorDisplayName);
      Write(w, "associatedConferences", v.associatedConferences);
      Write(w, "streamId", v.streamId);
      Write(w, "transcriptionEnabled", v.transcriptionEnabled);
      Write(w, "numParticipants", v.numParticipants);
      Write(w, "natTraversalServerInfo", v.natTraversalServerInfo);
      Write(w, "bitrateConfig", v.bitrateConfig);
      Write(w, "mediaEncryptionMode", v.mediaEncryptionMode);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::ConferenceInfo& v)
    {
      Read(json, "label", v.label);
      Read(json, "owner", v.owner);
      Read(json, "tags", v.tags);
      Read(json, "conferenceToken", v.conferenceToken);
      Read(json, "conference", v.conference);
      Read(json, "description", v.description);
      Read(json, "creatorDisplayName", v.creatorDisplayName);
      Read(json, "associatedConferences", v.associatedConferences);
      Read(json, "streamId", v.streamId);
      Read(json, "transcriptionEnabled", v.transcriptionEnabled);
      Read(json, "numParticipants", v.numParticipants);
      Read(json, "natTraversalServerInfo", v.natTraversalServerInfo);
      Read(json, "bitrateConfig", v.bitrateConfig);
      Read(json, "mediaEncryptionMode", v.mediaEncryptionMode);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::ConferenceBridgeConfig& v)
    {
      w.StartObject();
      Write(w, "userContext", v.userContext);
      Write(w, "httpJoinUrlBase", v.httpJoinUrlBase);
      Write(w, "wsUrlBase", v.wsUrlBase);
      Write(w, "serverUid", v.serverUid);
      Write(w, "useServerUidInJoinUrls", v.useServerUidInJoinUrls);
      Write(w, "natTraversalServerInfo", v.natTraversalServerInfo);
      Write(w, "screenshareBitrateConfig", v.screenshareBitrateConfig);
      Write(w, "cameraBitrateConfig", v.cameraBitrateConfig);
      Write(w, "mediaEncryptionMode", v.mediaEncryptionMode);
      Write(w, "mediaInactivityTimeoutMs", v.mediaInactivityTimeoutMs);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::ConferenceBridgeConfig& v)
    {
      Read(json, "userContext", v.userContext);
      Read(json, "httpJoinUrlBase", v.httpJoinUrlBase);
      Read(json, "wsUrlBase", v.wsUrlBase);
      Read(json, "serverUid", v.serverUid);
      Read(json, "useServerUidInJoinUrls", v.useServerUidInJoinUrls);
      Read(json, "natTraversalServerInfo", v.natTraversalServerInfo);
      Read(json, "screenshareBitrateConfig", v.screenshareBitrateConfig);
      Read(json, "cameraBitrateConfig", v.cameraBitrateConfig);
      Read(json, "mediaEncryptionMode", v.mediaEncryptionMode);
      Read(json, "mediaInactivityTimeoutMs", v.mediaInactivityTimeoutMs);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::ConferenceDetailsResult& v)
    {
      w.StartObject();
      Write(w, "conference", v.conference);
      Write(w, "conferenceToken", v.conferenceToken);
      Write(w, "conferenceInfo", v.conferenceInfo);
      Write(w, "conferenceJoinUrl", v.conferenceJoinUrl);
      Write(w, "persistent", v.persistent);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::ConferenceDetailsResult& v)
    {
      Read(json, "conference", v.conference);
      Read(json, "conferenceToken", v.conferenceToken);
      Read(json, "conferenceInfo", v.conferenceInfo);
      Read(json, "conferenceJoinUrl", v.conferenceJoinUrl);
      Read(json, "persistent", v.persistent);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::ConferenceNotFoundResult& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::ConferenceNotFoundResult& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::ConferenceListResult& v)
    {
      w.StartObject();
      Write(w, "conferences", v.conferences);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::ConferenceListResult& v)
    {
      Read(json, "conferences", v.conferences);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::ConferenceDetailsHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::ConferenceDetailsHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::ConferenceListHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::ConferenceListHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::ParticipantListState& v)
    {
      w.StartObject();
      Write(w, "isPartialUpdate", v.isPartialUpdate);
      Write(w, "isLastChunk", v.isLastChunk);
      Write(w, "participants", v.participants);
      Write(w, "addedParticipants", v.addedParticipants);
      Write(w, "updatedParticipants", v.updatedParticipants);
      Write(w, "removedParticipants", v.removedParticipants);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::ParticipantListState& v)
    {
      Read(json, "isPartialUpdate", v.isPartialUpdate);
      Read(json, "isLastChunk", v.isLastChunk);
      Read(json, "participants", v.participants);
      Read(json, "addedParticipants", v.addedParticipants);
      Read(json, "updatedParticipants", v.updatedParticipants);
      Read(json, "removedParticipants", v.removedParticipants);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::ParticipantListHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::ParticipantListHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::PeerConnectionAnswerEvent& v)
    {
      w.StartObject();
      Write(w, "participant", v.participant);
      Write(w, "sdpAnswer", v.sdpAnswer);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::PeerConnectionAnswerEvent& v)
    {
      Read(json, "participant", v.participant);
      Read(json, "sdpAnswer", v.sdpAnswer);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::PeerConnectionAnswerHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::PeerConnectionAnswerHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::ConferenceTranscriptionEvent& v)
    {
      w.StartObject();
      Write(w, "participant", v.participant);
      Write(w, "transcriptionResult", v.transcriptionResult);
      Write(w, "resultSetId", v.resultSetId);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::ConferenceTranscriptionEvent& v)
    {
      Read(json, "participant", v.participant);
      Read(json, "transcriptionResult", v.transcriptionResult);
      Read(json, "resultSetId", v.resultSetId);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::ConferenceEndedEvent& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::ConferenceEndedEvent& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::WebParticipantCreatedEvent& v)
    {
      w.StartObject();
      Write(w, "success", v.success);
      Write(w, "conference", v.conference);
      Write(w, "participant", v.participant);
      Write(w, "hasFloor", v.hasFloor);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::WebParticipantCreatedEvent& v)
    {
      Read(json, "success", v.success);
      Read(json, "conference", v.conference);
      Read(json, "participant", v.participant);
      Read(json, "hasFloor", v.hasFloor);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::CreateWebParticipantHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::CreateWebParticipantHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceBridge::ConferenceBridgeHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceBridge::ConferenceBridgeHandler& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // ConferenceBridge_JSON_HELPER_H