#include "brand_branded.h"

#if (CPCAPI2_BRAND_GENBAND_SOPI_MODULE == 1)

#include "phone/Phone.h"
#include "phone/PhoneErrorHandler.h"	
#include "../util/DumFpCommand.h"
	
#include "GenbandSopiManagerInterface.h"
#include "GenbandSopiAddressBookHandler.h"	

#include "SopiClientImpl.h"

#include "phone/NetworkChangeManagerImpl.h"

#ifdef ANDROID
#include <android/log.h>
#endif

using CPCAPI2::SipAccount::SipAccountImpl;

namespace CPCAPI2
{
namespace GenbandSopi
{

GenbandSopiManagerInterface::GenbandSopiManagerInterface(Phone* phone) :
   EventSource2<EventHandler<GenbandSopiHandler, GenbandSopiClientHandle> >(dynamic_cast<PhoneInterface*>(phone), "Sopi_Thread"),
   mPhone(dynamic_cast<PhoneInterface*>(phone)),
   mNextHandle(1),
   mGlobalSearchPendingRequest(0)
{
   mAccountIf = dynamic_cast<CPCAPI2::SipAccount::SipAccountInterface*>(CPCAPI2::SipAccount::SipAccountManager::getInterface(mPhone));
   mSipEventIf = dynamic_cast<CPCAPI2::SipEvent::SipEventManagerInterface*>(CPCAPI2::SipEvent::SipEventManager::getInterface(phone));	
}

GenbandSopiManagerInterface::~GenbandSopiManagerInterface()
{
   detachThread();
   executeOnThread(resip::resip_bind(&GenbandSopiManagerInterface::destroySopiClients, this));
   interruptProcess();
}
   
void
GenbandSopiManagerInterface::destroySopiClients()
{
   for (ClientImplMap::iterator it = mClientImplMap.begin(); it != mClientImplMap.end(); ++it)
   {
      delete it->second;
   }
   mClientImplMap.clear();
}

void
GenbandSopiManagerInterface::Release()
{
   delete this;
}

GenbandSopiClientHandle
GenbandSopiManagerInterface::createClient(const GenbandSopiClientSettings& settings)
{
    #ifdef ANDROID
       __android_log_print(ANDROID_LOG_WARN, "GENBANDSOPI", "GenbandSopiManagerInterface::createClient handle = %d", mNextHandle);
    #endif

   GenbandSopiClientHandle handle = mNextHandle++;

   //need to create synchronously because setHandler is called in this thread 
   mClientImplMap[handle] = new SopiClientImpl(handle, settings, *this);

   return handle;
}

void
GenbandSopiManagerInterface::createClientImpl(GenbandSopiClientHandle handle, const GenbandSopiClientSettings& settings)
{
   #ifdef ANDROID
       __android_log_print(ANDROID_LOG_WARN, "GENBANDSOPI", "GenbandSopiManagerInterface::createClientImpl handle = %d", handle);
   #endif
	
   mClientImplMap[handle] = new SopiClientImpl(handle, settings, *this);
}

int 
GenbandSopiManagerInterface::startSubscription(GenbandSopiClientHandle handle,CPCAPI2::SipAccount::SipAccountHandle account)
{
   postToSdkThread(resip::resip_bind(&GenbandSopiManagerInterface::startSubscriptionImpl, this, handle, account));
	return kSuccess;
}

int 
GenbandSopiManagerInterface::stopSubscription(GenbandSopiClientHandle handle)
{
   postToSdkThread(resip::resip_bind(&GenbandSopiManagerInterface::stopSubscriptionImpl, this, handle));
	return kSuccess;
}


int 
GenbandSopiManagerInterface::setSubscriptionHandler(CPCAPI2::SipAccount::SipAccountHandle account)
{
   #ifdef ANDROID
	__android_log_print(ANDROID_LOG_WARN, "GENBANDSOPI", "GenbandSopiManagerInterface::setSubscriptionHandler account = %d", account);
   #endif

   postToSdkThread(resip::resip_bind(&GenbandSopiManagerInterface::setSubscriptionHandlerImpl, this, account));
    return kSuccess;
}



void GenbandSopiManagerInterface::setSubscriptionHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account)
   {

    #ifdef ANDROID
       __android_log_print(ANDROID_LOG_WARN, "GENBANDSOPI", "GenbandSopiManagerInterface::setSubscriptionHandlerImpl account = %d", account);
    #endif
	
    AccountMap::iterator it = mAccountMap.find(account);
    GenbandSopiAddressBookHandler* evtMan = (it == mAccountMap.end() ? NULL : it->second);
    if (evtMan == NULL)
	  {
        if(mAccountIf == NULL)
		{
			 
            #ifdef ANDROID
             __android_log_print(ANDROID_LOG_WARN, "GENBANDSOPI", "setSubscriptionHandlerImpl - account interface null");
            #endif
			return;
		}
         SipAccountImpl* acct = mAccountIf->getAccountImpl(account);

         if (!acct)
		{
			fireError("Invalid account handle for GenbandSopiManagerInterface::setSubscriptionHandlerImpl");
			return;
		}
		 
         evtMan = new GenbandSopiAddressBookHandler(this);
         mAccountMap[account] = evtMan;
	  }
	  
    mSipEventIf->setHandlerImpl(account, "address-book", evtMan);
   
   }

void 
GenbandSopiManagerInterface::startSubscriptionImpl(GenbandSopiClientHandle handle,CPCAPI2::SipAccount::SipAccountHandle account)
{
    #ifdef ANDROID
		__android_log_print(ANDROID_LOG_WARN, "GENBANDSOPI", "GenbandSopiManagerInterface::startSubscriptionImpl account = %d", account);
	#endif
   
    AccountMap::iterator it = mAccountMap.find(account);
    GenbandSopiAddressBookHandler* evtMan = (it == mAccountMap.end() ? NULL : it->second);
	if(evtMan == NULL)
	    return;// false;

	evtMan->setSubscriptionHandler(handle);
		
	mSubscriptionHandle = mSipEventIf->createSubscription(account);
    CPCAPI2::SipEvent::SipEventSubscriptionSettings subscriptionSettings;
    subscriptionSettings.eventPackage = "address-book";
    subscriptionSettings.expiresSeconds = 3600;
    subscriptionSettings.supportedMimeTypes.push_back(MimeType("application", "address-book-notify+xml"));
    mSipEventIf->applySubscriptionSettings(mSubscriptionHandle, subscriptionSettings);

    SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
    if(acct != NULL)
    {
	   cpc::string localUri = "sip:" + acct->getSettings().username + "@" + acct->getSettings().domain;
	   mSipEventIf->addParticipant(mSubscriptionHandle, localUri);
    }else
	{
       #ifdef ANDROID
       __android_log_print(ANDROID_LOG_WARN, "GENBANDSOPI", "startSubscriptionImpl - account interface null");
       #endif
	   return; //false
	}
    mSipEventIf->start(mSubscriptionHandle);

    return;// true
   }
void 
GenbandSopiManagerInterface::stopSubscriptionImpl(GenbandSopiClientHandle handle)
   {
   mSipEventIf->end(mSubscriptionHandle, CPCAPI2::SipEvent::SipSubscriptionTerminateReason_Deactivate); 
}


int
GenbandSopiManagerInterface::setHandler(GenbandSopiClientHandle handle, GenbandSopiHandler* handler)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&GenbandSopiManagerInterface::setHandlerImpl, this, handle, handler);
   if (handler == NULL)
   {
      // removing the handler involves two steps:
      // 1. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);

      // 2. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      executeOnSdkThread(f);
   }
   else
   {
      postToSdkThread(f);
   }
   
   return kSuccess;
}

void
GenbandSopiManagerInterface::setHandlerImpl(GenbandSopiClientHandle handle, GenbandSopiHandler* handler)
{
   #ifdef ANDROID
       __android_log_print(ANDROID_LOG_WARN, "GENBANDSOPI", "GenbandSopiManagerInterface::setHandlerImpl handle = %d",handle);
   #endif
	
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
      auto it = mHandlers.find(handle);
      if (mHandlers.end() != it)
      {
         removeAppHandler(it->second, handle);
      }

      mHandlers[handle] = handler;
      if (nullptr != handler)
      {
         addAppHandler(handler, handle);
      }
   }
   else
   {
      fireError("Invalid client handle for setHandler.");
   }
}

int
GenbandSopiManagerInterface::requestAddressBook(GenbandSopiClientHandle handle)
{
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::requestAddressBookImpl, this, handle));
   return kSuccess;
}

void
GenbandSopiManagerInterface::requestAddressBookImpl(GenbandSopiClientHandle handle)
{
  
   #ifdef ANDROID
       __android_log_print(ANDROID_LOG_WARN, "GENBANDSOPI", "GenbandSopiManagerInterface::requestAddressBookImpl handle = %d",handle);
   #endif
	
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
      client->getAddressbook();
   }
   else
   {
      fireError("Invalid client handle for requestAddressBook.");
   }
}

int
GenbandSopiManagerInterface::addAddressBookEntry(GenbandSopiClientHandle handle, const AddressBookEntry& entry)
{
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::addAddressBookEntryImpl, this, handle, entry));
   return kSuccess;
}

void
GenbandSopiManagerInterface::addAddressBookEntryImpl(GenbandSopiClientHandle handle, const AddressBookEntry& entry)
{
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
      client->addAddressBookEntry(entry);
   }
   else
   {
      fireError("Invalid client handle for addAddressBookEntry.");
   }
}

int
GenbandSopiManagerInterface::updateAddressBookEntry(GenbandSopiClientHandle handle, const cpc::string& entryId, const AddressBookEntry& entry)
{
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::updateAddressBookEntryImpl, this, handle, entryId, entry));
   return kSuccess;
}

void
GenbandSopiManagerInterface::updateAddressBookEntryImpl(GenbandSopiClientHandle handle, const cpc::string& entryId, const AddressBookEntry& entry)
{
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
      client->updateAddressBookEntry(entryId, entry);
   }
   else
   {
      fireError("Invalid client handle for updateAddressBookEntry.");
   }
}

int
GenbandSopiManagerInterface::deleteAddressBookEntry(GenbandSopiClientHandle handle, const cpc::string entryName)
{
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::deleteAddressBookEntryImpl, this, handle, entryName));
   return kSuccess;
}

void
GenbandSopiManagerInterface::deleteAddressBookEntryImpl(GenbandSopiClientHandle handle, const cpc::string entryName)
{
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
      client->deleteAddressBookEntry(entryName);
   }
   else
   {
      fireError("Invalid client handle for deleteAddressBookEntry.");
   }
}

int
GenbandSopiManagerInterface::getAddressBookGroups(GenbandSopiClientHandle handle)
{
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::getAddressBookGroupsImpl, this, handle));
   return kSuccess;
}

void
GenbandSopiManagerInterface::getAddressBookGroupsImpl(GenbandSopiClientHandle handle)
{
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
      client->getAddressBookGroups();
   }
   else
   {
      fireError("Invalid client handle for getAddressBookGroups.");
   }
}

int
GenbandSopiManagerInterface::addAddressBookGroup(GenbandSopiClientHandle handle, const cpc::string& groupName)
{
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::addAddressBookGroupImpl, this, handle, groupName));
   return kSuccess;
}

void
GenbandSopiManagerInterface::addAddressBookGroupImpl(GenbandSopiClientHandle handle, const cpc::string& groupName)
{
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
      client->addAddressBookGroup(groupName);
   }
   else
   {
      fireError("Invalid client handle for addAddressBookGroup.");
   }
}

int
GenbandSopiManagerInterface::updateAddressBookGroup(GenbandSopiClientHandle handle, const cpc::string& oldGroupName, const cpc::string& newGroupName)
{
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::updateAddressBookGroupImpl, this, handle, oldGroupName, newGroupName));
   return kSuccess;
}

void
GenbandSopiManagerInterface::updateAddressBookGroupImpl(GenbandSopiClientHandle handle, const cpc::string& oldGroupName, const cpc::string& newGroupName)
{
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
      client->updateAddressBookGroup(oldGroupName, newGroupName);
   }
   else
   {
      fireError("Invalid client handle for updateAddressBookGroup.");
   }
}

int
GenbandSopiManagerInterface::deleteAddressBookGroup(GenbandSopiClientHandle handle, const cpc::string& groupName)
{
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::deleteAddressBookGroupImpl, this, handle, groupName));
   return kSuccess;
}

void
GenbandSopiManagerInterface::deleteAddressBookGroupImpl(GenbandSopiClientHandle handle, const cpc::string& groupName)
{
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
      client->deleteAddressBookGroup(groupName);
   }
   else
   {
      fireError("Invalid client handle for deleteAddressBookGroup.");
   }
}

int
GenbandSopiManagerInterface::searchGlobalDirectory(GenbandSopiClientHandle handle, const cpc::string& searchQuery, unsigned int maxCount)
{
   setGlobalSearchPendingRequest();
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::searchGlobalDirectoryImpl, this, handle, searchQuery, maxCount));
   return kSuccess;
}

void
GenbandSopiManagerInterface::searchGlobalDirectoryImpl(GenbandSopiClientHandle handle, const cpc::string& searchQuery, unsigned int maxCount)
{
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
	  getGlobalSearchPendingRequest();
      client->searchGlobalDirectory(searchQuery, maxCount);
   }
   else
   {
      fireError("Invalid client handle for searchGlobalDirectory.");
   }
}


int 
GenbandSopiManagerInterface::addAuthorizedUser(GenbandSopiClientHandle handle, const cpc::string& userName)
{
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::addAuthorizedUserImpl, this, handle, userName));
   return kSuccess;
}

void 
GenbandSopiManagerInterface::addAuthorizedUserImpl(GenbandSopiClientHandle handle, const cpc::string& userName)
{
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
      client->addAuthorizedUser(userName);
   }
   else
   {
      fireError("Invalid client handle for addAuthorizedUserImpl.");
   }
}

int 
GenbandSopiManagerInterface::removeAuthorizedUser(GenbandSopiClientHandle handle, const cpc::string& userName)
{
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::removeAuthorizedUserImpl, this, handle, userName));
   return kSuccess;
}

void 
GenbandSopiManagerInterface::removeAuthorizedUserImpl(GenbandSopiClientHandle handle, const cpc::string& userName)
{
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
      client->removeAuthorizedUser(userName);
   }
   else
   {
      fireError("Invalid client handle for removeAuthorizedUserImpl.");
   }
}

int 
GenbandSopiManagerInterface::getAuthorizedUsers(GenbandSopiClientHandle handle)
{
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::getAuthorizedUsersImpl, this, handle));
   return kSuccess;
}

void 
GenbandSopiManagerInterface::getAuthorizedUsersImpl(GenbandSopiClientHandle handle)
{
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
      client->getAuthorizedUsers();
   }
   else
   {
      fireError("Invalid client handle for getAuthorizedUsersImpl.");
   }
}
//==============================================================================================================
int 
GenbandSopiManagerInterface::addBannedUser(GenbandSopiClientHandle handle, const cpc::string& userName)
{
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::addBannedUserImpl, this, handle, userName));
   return kSuccess;
}

void 
GenbandSopiManagerInterface::addBannedUserImpl(GenbandSopiClientHandle handle, const cpc::string& userName)
{
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
      client->addBannedUser(userName);
   }
   else
   {
      fireError("Invalid client handle for addBannedUserImpl.");
   }
}

int 
GenbandSopiManagerInterface::removeBannedUser(GenbandSopiClientHandle handle, const cpc::string& userName)
{
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::removeBannedUserImpl, this, handle, userName));
   return kSuccess;
}

void 
GenbandSopiManagerInterface::removeBannedUserImpl(GenbandSopiClientHandle handle, const cpc::string& userName)
{
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
      client->removeBannedUser(userName);
   }
   else
   {
      fireError("Invalid client handle for removeBannedUserImpl.");
   }
}

int 
GenbandSopiManagerInterface::getBannedUsers(GenbandSopiClientHandle handle)
{
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::getBannedUsersImpl, this, handle));
   return kSuccess;
}

void 
GenbandSopiManagerInterface::getBannedUsersImpl(GenbandSopiClientHandle handle)
{
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
      client->getBannedUsers();
   }
   else
   {
      fireError("Invalid client handle for getBannedUsersImpl.");
   }
}

//==============================================================================================================
int 
GenbandSopiManagerInterface::addPoliteBlockedUser(GenbandSopiClientHandle handle, const cpc::string& userName)
{
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::addPoliteBlockedUserImpl, this, handle, userName));
   return kSuccess;
}

void 
GenbandSopiManagerInterface::addPoliteBlockedUserImpl(GenbandSopiClientHandle handle, const cpc::string& userName)
{
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
      client->addPoliteBlockedUser(userName);
   }
   else
   {
      fireError("Invalid client handle for addPoliteBlockedUserImpl.");
   }
}

int 
GenbandSopiManagerInterface::removePoliteBlockedUser(GenbandSopiClientHandle handle, const cpc::string& userName)
{
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::removePoliteBlockedUserImpl, this, handle, userName));
   return kSuccess;
}

void 
GenbandSopiManagerInterface::removePoliteBlockedUserImpl(GenbandSopiClientHandle handle, const cpc::string& userName)
{
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
      client->removePoliteBlockedUser(userName);
   }
   else
   {
      fireError("Invalid client handle for removePoliteBlockedUserImpl.");
   }
}

int 
GenbandSopiManagerInterface::getPoliteBlockedUsers(GenbandSopiClientHandle handle)
{
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::getPoliteBlockedUsersImpl, this, handle));
   return kSuccess;
}

void 
GenbandSopiManagerInterface::getPoliteBlockedUsersImpl(GenbandSopiClientHandle handle)
{
   SopiClientImpl* client = getClient(handle);
   if (client)
   {
      client->getPoliteBlockedUsers();
   }
   else
   {
      fireError("Invalid client handle for getPoliteBlockedUsersImpl.");
   }
}


int
GenbandSopiManagerInterface::destroy(GenbandSopiClientHandle handle)
{
   postToThread(resip::resip_bind(&GenbandSopiManagerInterface::destroyImpl, this, handle));
   return kSuccess;
}

void
GenbandSopiManagerInterface::destroyImpl(GenbandSopiClientHandle handle)
{
   ClientImplMap::iterator it = mClientImplMap.find(handle);
   if (it != mClientImplMap.end())
   {
      delete it->second;
      mClientImplMap.erase(it);
   }
   else
   {
      fireError("Invalid client handle for destroy");
   }
}

bool GenbandSopiManagerInterface::isGlobalSearchPendingRequest()
{
	mMutex.lock();
	int ret = mGlobalSearchPendingRequest;
	mMutex.unlock();
	return ret>0;
}
void GenbandSopiManagerInterface::setGlobalSearchPendingRequest() 
{
	mMutex.lock();
	mGlobalSearchPendingRequest++;
	mMutex.unlock();
}

void GenbandSopiManagerInterface::getGlobalSearchPendingRequest() 
{
	mMutex.lock();
	mGlobalSearchPendingRequest--; 
	if(mGlobalSearchPendingRequest<0)
	   mGlobalSearchPendingRequest=0;
	mMutex.unlock();
}

bool GenbandSopiManagerInterface::isNetworkAvailable()
{
	NetworkTransport currentTransport = NetworkChangeManager::getInterface(mPhone)->networkTransport();
	if(currentTransport == TransportNone)
		return false;
	else
		return true;
}

void
GenbandSopiManagerInterface::fireError(const cpc::string& errorText)
{
   PhoneErrorEvent evt;
   evt.errorText = errorText;
   
    #ifdef ANDROID
       __android_log_print(ANDROID_LOG_WARN, "GENBANDSOPI", "GenbandSopiManagerInterface::fireError = %s", errorText.c_str());
    #endif
   
   mPhone->fireEvent(cpcEvent(PhoneHandler, onError), cpc::string("GenbandSopiManagerInterface"), evt);
   mPhone->fireEvent(cpcEvent(PhoneErrorHandler, onError), cpc::string("GenbandSopiManagerInterface"), evt);
}

SopiClientImpl*
GenbandSopiManagerInterface::getClient(GenbandSopiClientHandle handle)
{
   ClientImplMap::iterator it = mClientImplMap.find(handle);
   return it == mClientImplMap.end() ? NULL : it->second;
}

std::ostream& operator<<(std::ostream& os, const AddressBookUpdatedEvent& evt)
{
   return os << "AddressBookUpdatedEvent";
}

std::ostream& operator<<(std::ostream& os, const AddressBookGroupsUpdatedEvent& evt)
{
   return os << "AddressBookGroupsUpdatedEvent";
}

std::ostream& operator<<(std::ostream& os, const SearchGlobalAddressBookResultEvent& evt)
{
   return os << "SearchGlobalAddressBookResultEvent";
}

std::ostream& operator<<(std::ostream& os, const GetAuthorizedUsersEvent& evt)
{
   return os << "GetAuthorizedUsersEvent";
}

std::ostream& operator<<(std::ostream& os, const GetBannedUsersEvent& evt)
{
   return os << "GetBannedUsersEvent";
}

std::ostream& operator<<(std::ostream& os, const GetPoliteBlockedUsersEvent& evt)
{
   return os << "GetPoliteBlockedUsersEvent";
}

std::ostream& operator<<(std::ostream& os, const GenbandSopi::ErrorEvent& evt)
{
   return os << "GenbandSopi::ErrorEvent";
}

}
}
#endif
