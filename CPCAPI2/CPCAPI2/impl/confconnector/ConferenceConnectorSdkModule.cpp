#include "brand_branded.h"

#include "interface/experimental/confconnector/ConferenceConnector.h"

#if (CPCAPI2_BRAND_CONFERENCE_CONNECTOR_MODULE == 1)
#include "ConferenceConnectorInterface.h"
#include "interface/experimental/peerconnection/PeerConnectionManager.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace ConferenceConnector
{
   ConferenceConnectorManager* ConferenceConnectorManager::getInterface(Phone* cpcPhone)
   {
#if (CPCAPI2_BRAND_CONFERENCE_CONNECTOR_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      CPCAPI2::PeerConnection::PeerConnectionManager::getInterface(phone); // we depend on PeerConnectionManager
      return _GetInterface<ConferenceConnectorInterface>(phone, "ConferenceConnectorManager");
#else
      return NULL;
#endif
   }

}
}
