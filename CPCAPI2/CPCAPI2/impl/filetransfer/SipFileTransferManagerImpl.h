#pragma once

#ifndef __CPCAPI2_SIP_FILE_TRANSFER_MANAGER_IMPL_H__
#define __CPCAPI2_SIP_FILE_TRANSFER_MANAGER_IMPL_H__

#include <list>
#include <map>

#include "resip/dum/InviteSessionHandler.hxx"

#include "cpcapi2defs.h"
#include "filetransfer/SipFileTransferManagerInterface.h"
#include "../account/SipAccountImpl.h"
#include "../account/SipAccountAwareFeature.h"
//#include "../phone/SdkModuleThread.h"
#include "SipFileTransferInfo.h"

// msrp stack includes
#include <msrp_forward_decl.h>

using resip::InviteSession;
using resip::InviteSessionHandle;
using resip::ClientInviteSessionHandle;
using resip::ServerInviteSessionHandle;
using resip::SipMessage;
using resip::SdpContents;
using resip::Contents;
using resip::ClientSubscriptionHandle;
using resip::ServerSubscriptionHandle;

namespace CPCAPI2
{
   namespace SipFileTransfer
   {
      class SipFileTransferManagerImpl : public SipAccount::SipAccountAwareFeature,
                                         public resip::InviteSessionHandler,
                                         public resip::ReactorEventHandler
      {
      public:
         typedef std::map< SipAccount::SipAccountHandle, SipFileTransferManagerImpl* > AccountMap;
         SipFileTransferManagerImpl(SipFileTransferManagerInterface* iff, std::shared_ptr<AccountMap> parentMap, CPCAPI2::PhoneInterface* cpcPhone, SipAccount::SipAccountImpl& account);
         virtual ~SipFileTransferManagerImpl();

         resip::SharedPtr< resip::DialogUsageManager > getDum() { return mDum; }
         msrp_stack_t *getMSRPStack() { return mStack; }

         // IAccountAware
         virtual int registerSdkDialogSetFactory( CPCAPI2::SipAccount::AppDialogSetFactory& factory ) OVERRIDE;
         virtual int adornMasterProfile( resip::SharedPtr<resip::MasterProfile>& profile ) OVERRIDE;
         virtual int addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig) OVERRIDE;
         virtual int onDumBeingDestroyed() OVERRIDE;
         virtual int onRegistrationSuccess(const SipAccount::SipRegistrationSuccessEvent& args) OVERRIDE;
         virtual void release() OVERRIDE;

         SipFileTransferInfo *getFileTransferInfo( const SipFileTransferHandle& h ) const;
         void setFileTransferInfo( const SipFileTransferHandle& h, SipFileTransferInfo * pInfo );
         void removeFileTransferInfo( const SipFileTransferHandle& h );

         SipFileTransferItemInfoPtr getFileTransferItemInfo( const SipFileTransferItemHandle& h ) const;
         void setFileTransferItemInfo( const SipFileTransferItemHandle& ih, SipFileTransferItemInfoPtr ci );
         void removeFileTransferItemInfo( const SipFileTransferItemHandle& ih );

         resip::Data makeFileTransferSdp( const SipFileTransferHandle& h, const resip::Data& direction, UInt32 localMediaPort,
            const resip::Data destAddr = resip::Data::Empty );

         void fireError( const SipFileTransferHandle& h, const cpc::string& message );
         void fireProgress( const SipFileTransferHandle& h, const SipFileTransferItemHandle& ih, const unsigned short& percent );
         void fireEnded( const SipFileTransferHandle& h, FileTransferEndedEvent evt );
         void fireItemEnded( const SipFileTransferHandle& h, const SipFileTransferItemHandle& ih, const FileTransferItemEndReason endReason );
         void fireNewTransfer( const SipFileTransferHandle& h, const NewFileTransferEvent& evt );
         void fireConfigured( const SipFileTransferHandle& h, const FileTransferConfiguredEvent& evt );

         // InviteSessionHandler methods of interest
         virtual void onNewSession(ClientInviteSessionHandle, InviteSession::OfferAnswerType oat, const SipMessage& msg) OVERRIDE;
         virtual void onNewSession(ServerInviteSessionHandle, InviteSession::OfferAnswerType oat, const SipMessage& msg) OVERRIDE;
         virtual void onTerminated(InviteSessionHandle, InviteSessionHandler::TerminatedReason reason, const SipMessage* related=0) OVERRIDE;
         virtual void onAnswer(InviteSessionHandle, const SipMessage& msg, const SdpContents&) OVERRIDE;
         virtual void onOffer(InviteSessionHandle, const SipMessage& msg, const SdpContents&) OVERRIDE;      
         virtual void onOfferRequired(InviteSessionHandle, const SipMessage& msg) OVERRIDE;
         virtual void onInfo(InviteSessionHandle, const SipMessage& msg) OVERRIDE;

         // Methods from InviteSessionHandler that we're not interested in
         virtual void onConnected(ClientInviteSessionHandle, const SipMessage& msg) OVERRIDE {};
         virtual void onConnected(InviteSessionHandle, const SipMessage& msg) OVERRIDE {};
         virtual void onForkDestroyed(ClientInviteSessionHandle) OVERRIDE {};
         virtual void onRedirected(ClientInviteSessionHandle, const SipMessage& msg) OVERRIDE {};
         virtual void onFailure(ClientInviteSessionHandle, const SipMessage& msg) OVERRIDE {};
         virtual void onEarlyMedia(ClientInviteSessionHandle, const SipMessage&, const SdpContents&) OVERRIDE {};
         virtual void onProvisional(ClientInviteSessionHandle, const SipMessage&) OVERRIDE {};
         virtual void onOfferRejected(InviteSessionHandle, const SipMessage* msg) OVERRIDE {};
         virtual void onInfoSuccess(InviteSessionHandle, const SipMessage& msg) OVERRIDE {};
         virtual void onInfoFailure(InviteSessionHandle, const SipMessage& msg) OVERRIDE {};
         virtual void onMessage(InviteSessionHandle, const SipMessage& msg) OVERRIDE {};
         virtual void onMessageSuccess(InviteSessionHandle, const SipMessage& msg) OVERRIDE {};
         virtual void onMessageFailure(InviteSessionHandle, const SipMessage& msg) OVERRIDE {};
         virtual void onRefer(InviteSessionHandle, ServerSubscriptionHandle, const SipMessage& msg) OVERRIDE {};
         virtual void onReferNoSub(InviteSessionHandle, const SipMessage& msg) OVERRIDE {};
         virtual void onReferRejected(InviteSessionHandle, const SipMessage& msg) OVERRIDE {};
         virtual void onReferAccepted(InviteSessionHandle, ClientSubscriptionHandle, const SipMessage& msg) OVERRIDE {};

         // SdkModuleThreadUser
         virtual void process(resip::ReactorEventHandler::FdSetType& fdset) OVERRIDE;
         virtual void buildFdSet(resip::ReactorEventHandler::FdSetType& fdset) OVERRIDE;
         virtual unsigned int getTimeTillNextProcessMS() OVERRIDE;
         virtual const char* getEventHandlerDesc() const OVERRIDE { return "SipFileTransferManager"; }

         // Causes a hangup/BYE
         void end(const SipFileTransferHandle& fileTransfer);

         // Called from msrp library
         void itemSendComplete( const SipFileTransferHandle& h, const SipFileTransferItemHandle& ih, FileTransferItemEndReason reason );

      private:
         SipFileTransferManagerInterface* mInterface;

         bool is_msrp_lib_initialized;
         msrp_stack_t *mStack;
         unsigned short mListeningPort;
         SipAccount::SipAccountImpl& mAccount;
         resip::SharedPtr<resip::DialogUsageManager> mDum;

         // shared_ptr not required on this collection
         SipFileTransferInfoMap mFileTransferInfo;

         std::map< SipFileTransferItemHandle, SipFileTransferItemInfoPtr > mFileTransferItemInfo;
         CPCAPI2::PhoneInterface* mPhone;

         std::weak_ptr<AccountMap> mParentMap;

         resip::Tuple mRegServer;
      };
   }
}

#endif // __CPCAPI2_SIP_FILE_TRANSFER_MANAGER_IMPL_H__
