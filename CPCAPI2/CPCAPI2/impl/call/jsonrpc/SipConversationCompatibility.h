#include <memory>
#include <optional>

#include "impl/jsonrpc/PhoneInstances.h"
#include "gen/Phone/server/IPhone.h"
#include "gen/SipConversation/server/ISipConversation.h"

namespace CPCAPI2
{
namespace SipConversation
{
class SipConversationManager;
}
}

namespace jsonrpc
{
namespace CPCAPI2
{
namespace SipConversation
{
class SipConversationCompatibility : public jsonrpc::CPCAPI2::SipConversation::ISipConversation
{
public:
	SipConversationCompatibility(const std::shared_ptr<::CPCAPI2::PhoneInstances>& phones);

    virtual void setDefaultSettings(const int64_t phoneHandle, const int64_t account, const SipConversationSettings& settings);

    virtual int64_t createConversation(const int64_t phoneHandle, const int64_t account) override;

    virtual void addParticipant(const int64_t phoneHandle, const int64_t conversation, const cpc::string& targetAddress) override;

    virtual void start(const int64_t phoneHandle, const int64_t conversation) override;

    virtual void end(const int64_t phoneHandle, const int64_t conversation) override;

	virtual void configureMedia(const int64_t phoneHandle, const int64_t conversationHandle, const MediaInfo& mediaDescriptor) override;

	virtual void setMediaEnabled(const int64_t phoneHandle, const int64_t conversationHandle, const MediaType mediaType, const bool enabled) override;

	virtual void setAnonymousMode(const int64_t phoneHandle, const int64_t conversationHandle, const int64_t anonymousMode) override;

	virtual void hold(const int64_t phoneHandle, const int64_t conversationHandle) override;

	virtual void unhold(const int64_t phoneHandle, const int64_t conversationHandle) override;

	virtual void sendMediaChangeRequest(const int64_t phoneHandle, const int64_t conversationHandle) override;

	virtual void redirect(const int64_t phoneHandle, const int64_t conversationHandle, const cpc::string& targetAddress, const cpc::string& reason) override;

	virtual void sendRingingResponse(const int64_t phoneHandle, const int64_t conversationHandle)override;

	virtual void reject(const int64_t phoneHandle, const int64_t conversationHandle, const int64_t rejectReason) override;

	virtual void accept(const int64_t phoneHandle, const int64_t conversationHandle) override;

	virtual void acceptIncomingTransferRequest(const int64_t phoneHandle, const int64_t conversationHandle) override;

	virtual void rejectIncomingTransferRequest(const int64_t phoneHandle, const int64_t conversationHandle) override;

	virtual void transfer(const int64_t phoneHandle, const int64_t transferTargetConversation, const int64_t transfereeConversation) override;

	virtual void transferWithFlag(const int64_t phoneHandle, const int64_t transferTargetConversation, const int64_t transfereeConversation, const bool endTargetConversationOnSuccess) override;

	virtual void transferToAddress(const int64_t phoneHandle, const int64_t transfereeConversation, const cpc::string& targetAddress) override;

	virtual void setDtmfMode(const int64_t phoneHandle, const int64_t account, const int64_t ordinal, const DtmfMode dtmfMode) override;

	virtual void startDtmfTone(const int64_t phoneHandle, const int64_t conversationHandle, const int64_t toneId, const bool playLocally) override;

	virtual void stopDtmfTone(const int64_t phoneHandle) override;

	virtual void playSound(const int64_t phoneHandle, const int64_t conversationHandle, const cpc::string& resourceUri, const bool repeat) override;

	virtual void stopPlaySound(const int64_t phoneHandle, const int64_t conversationHandle) override;

	virtual int64_t getCallCount(const int64_t phoneHandle) override;

	virtual void setCallKitMode(const int64_t phoneHandle, const int64_t conversationHandle) override;

	virtual void setTelecomFrameworkMode(const int64_t phoneHandle, const int64_t conversationHandle) override;
private:
	::CPCAPI2::SipConversation::SipConversationManager* getInstance(const int64_t phoneHandle);
	std::shared_ptr<::CPCAPI2::PhoneInstances> mPhones;
	


}; // class SipConversationCompatibility
} // namepace SipConversation
} // namepace CPCAPI2
} // namepace jsonrpc
