#include "brand_branded.h"

#include "interface/experimental/ldap/LdapManager.h"

#if (CPCAPI2_BRAND_OPEN_LDAP_MODULE == 1)
#include "LdapManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace OpenLdap
{
LdapManager* LdapManager::getInterface(Phone* cpcPhone, bool useSeparateThread)
{
#if (CPCAPI2_BRAND_OPEN_LDAP_MODULE == 1)
   PhoneInterface* pi = dynamic_cast<PhoneInterface*>(cpcPhone);
   LdapManager* convManager = dynamic_cast<LdapManager*>(pi->getInterfaceByName("LdapManagerInterface"));
   if (convManager == NULL)
   {
      LdapManagerInterface* m_if = new LdapManagerInterface(cpcPhone, useSeparateThread);
      pi->registerInterface("LdapManagerInterface", m_if);
      convManager = m_if;
   }
   return convManager;
#else
   return NULL;
#endif
}

LdapManager* LdapManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_OPEN_LDAP_MODULE == 1)
   PhoneInterface* pi = dynamic_cast<PhoneInterface*>(cpcPhone);
   LdapManager* convManager = dynamic_cast<LdapManager*>(pi->getInterfaceByName("LdapManagerInterface"));
   if (convManager == NULL)
   {
      LdapManagerInterface* m_if = new LdapManagerInterface(cpcPhone);
      pi->registerInterface("LdapManagerInterface", m_if);
      convManager = m_if;
   }
   return convManager;
#else
   return NULL;
#endif
}

}
}
