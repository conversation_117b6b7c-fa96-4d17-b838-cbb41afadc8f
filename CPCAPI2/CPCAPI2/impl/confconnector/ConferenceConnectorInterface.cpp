#include "brand_branded.h"

#if (CPCAPI2_BRAND_CONFERENCE_CONNECTOR_MODULE == 1)
#include "ConferenceConnectorInterface.h"
#include "ConferenceConnectorImpl.h"
#include "confconnector/ConferenceConnectorHandler.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::CONF_CONNECTOR

namespace CPCAPI2
{
namespace ConferenceConnector
{
ConferenceConnectorHandle ConferenceConnectorHandleFactory::sNextHandle = 1;
CloudConferenceHandle CloudConferenceHandleFactory::sNextHandle = 1;
CloudConferenceSessionHandle ConferenceConnectorSessionHandleFactory::sNextHandle = 1;

ConferenceConnectorInterface::ConferenceConnectorInterface(Phone* phone)
   : EventSource<ConferenceConnectorHandle, ConferenceConnectorHandler, ConferenceConnectorSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
     mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   StackLog(<< "ConferenceConnectorInterface::ConferenceConnectorInterface(): " << this << " phone: " << phone);
   mPhone->addRefImpl();
}

ConferenceConnectorInterface::~ConferenceConnectorInterface()
{
   StackLog(<< "ConferenceConnectorInterface::~ConferenceConnectorInterface(): " << this << " phone: " << mPhone);
   mPhone->releaseImpl();
}

void ConferenceConnectorInterface::Release()
{
   mInstMap.clear();
   delete this;
}

ConferenceConnectorHandle ConferenceConnectorInterface::createConferenceConnector()
{
   ConferenceConnectorHandle h = ConferenceConnectorHandleFactory::getNext();
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::createConferenceConnectorImpl, this, h));
   return h;
}

void ConferenceConnectorInterface::createConferenceConnectorImpl(ConferenceConnectorHandle conn)
{
   InfoLog(<< "createConferenceConnector - handle: " << conn);
   std::shared_ptr<ConferenceConnectorImpl> pimpl = (ConferenceConnectorImpl::Create(this, conn));
   mInstMap[conn] = std::move(pimpl);
}

int ConferenceConnectorInterface::setHandler(ConferenceConnectorHandle conn, ConferenceConnectorHandler* handler)
{
   setAppHandler(conn, handler);
   return kSuccess;
}

int ConferenceConnectorInterface::destroyConferenceConnector(ConferenceConnectorHandle conn)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::destroyConferenceConnectorImpl, this, conn));
   return kSuccess;
}

void ConferenceConnectorInterface::destroyConferenceConnectorImpl(ConferenceConnectorHandle conn)
{
   InfoLog(<< "ConferenceConnectorInterface::destroyConferenceConnectorImpl(): " << this << " conn: " << conn);

   ConferenceConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it != mInstMap.end())
   {
      InfoLog(<< "ConferenceConnectorInterface::destroyConferenceConnectorImpl(): " << this << " phone: " << mPhone << " conn: " << conn);
      it->second->destroy();
      mInstMap.erase(it);
   }
}

int ConferenceConnectorInterface::setConnectionSettings(ConferenceConnectorHandle conn, const ConferenceConnectorSettings& settings)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::setConnectionSettingsImpl, this, conn, settings));
   return kSuccess;
}

void ConferenceConnectorInterface::setConnectionSettingsImpl(ConferenceConnectorHandle conn, const ConferenceConnectorSettings& settings)
{
   InfoLog(<< "setConnectionSettings - handle: " << conn);
   ConferenceConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it != mInstMap.end())
   {
      it->second->setConnectionSettings(settings);
   }
}

int ConferenceConnectorInterface::connectToConferenceService(ConferenceConnectorHandle conn)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::connectToConferenceServiceImpl, this, conn));
   return kSuccess;
}

void ConferenceConnectorInterface::connectToConferenceServiceImpl(ConferenceConnectorHandle conn)
{
   InfoLog(<< "ConferenceConnectorInterface::connectToConferenceService(): conn: " << conn);
   ConferenceConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it != mInstMap.end())
   {
      it->second->connectToServices();
   }
}

int ConferenceConnectorInterface::disconnectFromConferenceService(ConferenceConnectorHandle conn)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::disconnectFromConferenceServiceImpl, this, conn));
   return kSuccess;
}

void ConferenceConnectorInterface::disconnectFromConferenceServiceImpl(ConferenceConnectorHandle conn)
{
   InfoLog(<< "ConferenceConnectorInterface::disconnectFromConferenceService(): conn: " << conn);

   ConferenceConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it != mInstMap.end())
   {
      InfoLog(<< "ConferenceConnectorInterface::disconnectFromConferenceServiceImpl(): " << this << " phone: " << mPhone << " conn: " << conn);
      it->second->disconnectService();
   }
}

int ConferenceConnectorInterface::queryConferenceList(ConferenceConnectorHandle conn)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::queryConferenceListImpl, this, conn));
   return kSuccess;
}

void ConferenceConnectorInterface::queryConferenceListImpl(ConferenceConnectorHandle conn)
{
   InfoLog(<< "queryConferenceList - handle: " << conn);

   ConferenceConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it != mInstMap.end())
   {
      StackLog(<< "ConferenceConnectorInterface::queryConferenceListImpl(): " << this << " phone: " << mPhone << " conn: " << conn);
      it->second->queryConferenceList();
   }
}

int ConferenceConnectorInterface::createNewConference(ConferenceConnectorHandle conn, const CloudConferenceSettings& settings)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::createNewConferenceImpl, this, conn, settings));
   return kSuccess;
}

void ConferenceConnectorInterface::createNewConferenceImpl(ConferenceConnectorHandle conn, const CloudConferenceSettings& settings)
{
   InfoLog(<< "createNewConference - handle: " << conn << " conference-id: " << settings.conferenceId << " description: " << settings.conferenceDescription);

   ConferenceConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it != mInstMap.end())
   {
      StackLog(<< "ConferenceConnectorInterface::createNewConferenceImpl(): " << this << " phone: " << mPhone << " conn: " << conn);
      it->second->createNewConference(settings);
   }
}

int ConferenceConnectorInterface::destroyConference(CloudConferenceHandle conference)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::destroyConferenceImpl, this, conference));
   return kSuccess;
}

void ConferenceConnectorInterface::destroyConferenceImpl(CloudConferenceHandle conference)
{
   InfoLog(<< "destroyConference - handle: " << conference);

   ConferenceConnectorInterface::ConferenceMap::iterator it = mConfMap.find(conference);
   if (it != mConfMap.end())
   {
      StackLog(<< "ConferenceConnectorInterface::destroyConferenceImpl(): " << this << " phone: " << mPhone << " conference: " << conference);
      it->second->destroyConference(conference);
   }
}


int ConferenceConnectorInterface::queryParticipantList(CloudConferenceHandle conference)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::queryParticipantListImpl, this, conference));
   return kSuccess;
}

void ConferenceConnectorInterface::queryParticipantListImpl(CloudConferenceHandle conference)
{
   InfoLog(<< "queryParticipantList - handle: " << conference);

   ConferenceConnectorInterface::ConferenceMap::iterator it = mConfMap.find(conference);
   if (it != mConfMap.end())
   {
      StackLog(<< "ConferenceConnectorInterface::queryParticipantListImpl(): " << this << " phone: " << mPhone << " conference: " << conference);
      it->second->queryParticipantList(conference);
   }
}

CloudConferenceSessionHandle ConferenceConnectorInterface::createConferenceSession(CloudConferenceHandle conference)
{
   CloudConferenceSessionHandle h = ConferenceConnectorSessionHandleFactory::getNext();
   CloudConferenceSessionSettings settings;
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::createConferenceSessionImpl, this, h, conference, settings));
   return h;
}

CloudConferenceSessionHandle ConferenceConnectorInterface::createConferenceSession(CloudConferenceHandle conference, const CloudConferenceSessionSettings& settings)
{
   CloudConferenceSessionHandle h = ConferenceConnectorSessionHandleFactory::getNext();
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::createConferenceSessionImpl, this, h, conference, settings));
   return h;
}

int ConferenceConnectorInterface::createConferenceSession(CloudConferenceSessionHandle session, CloudConferenceHandle conference, const CloudConferenceSessionSettings& settings)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::createConferenceSessionImpl, this, session, conference, settings));
   return kSuccess;
}

void ConferenceConnectorInterface::createConferenceSessionImpl(CloudConferenceSessionHandle session, CloudConferenceHandle conference, const CloudConferenceSessionSettings& settings)
{
   InfoLog(<< "createConferenceSession - conference handle: " << conference << ", session handle: " << session);

   ConferenceConnectorInterface::ConferenceMap::iterator it = mConfMap.find(conference);
   if (it != mConfMap.end())
   {
      StackLog(<< "ConferenceConnectorInterface::createConferenceSessionImpl(): " << this << " phone: " << mPhone << " conference: " << conference << " session: " << session);
      mSessionMap[session] = it->second;
      it->second->createConferenceSession(conference, session, settings);
   }
}

int ConferenceConnectorInterface::setSessionSettings(CloudConferenceSessionHandle session, const CloudConferenceSessionSettings& settings)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::setSessionSettingsImpl, this, session, settings));
   return kSuccess;
}

void ConferenceConnectorInterface::setSessionSettingsImpl(CloudConferenceSessionHandle session, const CloudConferenceSessionSettings& settings)
{
   InfoLog(<< "setSessionSettings - session handle: " << session);

   ConferenceConnectorInterface::SessionMap::iterator itSess = mSessionMap.find(session);
   if (itSess != mSessionMap.end())
   {
      StackLog(<< "ConferenceConnectorInterface::setSessionSettingsImpl(): " << this << " phone: " << mPhone << " conn: " << itSess->second << " session: " << session);
      itSess->second->setSessionSettings(session, settings);
   }
}

int ConferenceConnectorInterface::setSessionMediaSettings(CloudConferenceSessionHandle session, const CloudConferenceSessionMediaSettings& settings)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::setSessionMediaSettingsImpl, this, session, settings));
   return kSuccess;
}

void ConferenceConnectorInterface::setSessionMediaSettingsImpl(CloudConferenceSessionHandle session, const CloudConferenceSessionMediaSettings& settings)
{
   InfoLog(<< "setSessionMediaSettings - session handle: " << session);

   ConferenceConnectorInterface::SessionMap::iterator itSess = mSessionMap.find(session);
   if (itSess != mSessionMap.end())
   {
      StackLog(<< "ConferenceConnectorInterface::setSessionMediaSettingsImpl(): " << this << " phone: " << mPhone << " conn: " << itSess->second << " session: " << session);
      itSess->second->setSessionMediaSettings(session, settings);
   }
}

int ConferenceConnectorInterface::dropIncomingJsonMessages(ConferenceConnectorHandle conn, bool enable)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::dropIncomingJsonMessagesImpl, this, conn, enable));
   return kSuccess;
}

void ConferenceConnectorInterface::dropIncomingJsonMessagesImpl(ConferenceConnectorHandle conn, bool enable)
{
   InfoLog(<< "ConferenceConnectorInterface::dropIncomingJsonMessagesImpl(): connector handle: " << conn << " drop message enable: " << enable);
   ConferenceConnectorInterface::InstanceMap::iterator it = mInstMap.find(conn);
   if (it != mInstMap.end())
   {
      StackLog(<< "ConferenceConnectorInterface::dropIncomingJsonMessagesImpl(): " << this << " phone: " << mPhone << " conn: " << conn);
      it->second->dropIncomingJsonMessages(enable);
   }
}

int ConferenceConnectorInterface::queryMediaStatistics(CloudConferenceSessionHandle session)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::queryMediaStatisticsImpl, this, session));
   return kSuccess;
}

void ConferenceConnectorInterface::queryMediaStatisticsImpl(CloudConferenceSessionHandle session)
{
   InfoLog(<< "ConferenceConnectorInterface::queryMediaStatisticsImpl(): session handle: " << session);

   ConferenceConnectorInterface::SessionMap::iterator i = mSessionMap.find(session);
   if (i != mSessionMap.end())
   {
      StackLog(<< "ConferenceConnectorInterface::queryMediaStatisticsImpl(): " << this << " phone: " << mPhone << " conn: " << i->second << " session: " << session);
      i->second->queryMediaStatistics(session);
   }
}

int ConferenceConnectorInterface::setMediaInactivityMonitor(CloudConferenceSessionHandle session, bool enableMediaInactivityMonitor, int mediaInactivityIntervalMsecs)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::setMediaInactivityMonitorImpl, this, session, enableMediaInactivityMonitor, mediaInactivityIntervalMsecs));
   return kSuccess;
}

void ConferenceConnectorInterface::setMediaInactivityMonitorImpl(CloudConferenceSessionHandle session, bool enableMediaInactivityMonitor, int mediaInactivityIntervalMsecs)
{
   InfoLog(<< "ConferenceConnectorInterface::setMediaInactivityMonitorImpl(): session handle: " << session
      << " monitor enabled: " << enableMediaInactivityMonitor << " inactivity interval: " << mediaInactivityIntervalMsecs);

   ConferenceConnectorInterface::SessionMap::iterator i = mSessionMap.find(session);
   if (i != mSessionMap.end())
   {
      StackLog(<< "ConferenceConnectorInterface::setMediaInactivityMonitorImpl(): " << this << " phone: " << mPhone << " conn: " << i->second << " session: " << session);
      i->second->setMediaInactivityMonitor(session, enableMediaInactivityMonitor, mediaInactivityIntervalMsecs);
   }
}

int ConferenceConnectorInterface::startSession(CloudConferenceSessionHandle session)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::startSessionImpl, this, session));
   return kSuccess;
}

void ConferenceConnectorInterface::startSessionImpl(CloudConferenceSessionHandle session)
{
   InfoLog(<< "startSession - session handle: " << session);

   ConferenceConnectorInterface::SessionMap::iterator itSess = mSessionMap.find(session);
   if (itSess != mSessionMap.end())
   {
      StackLog(<< "ConferenceConnectorInterface::startSessionImpl(): " << this << " phone: " << mPhone << " conn: " << itSess->second << " session: " << session);
      itSess->second->startSession(session);
   }
}

int ConferenceConnectorInterface::endSession(CloudConferenceSessionHandle session)
{
#if _WIN32 && defined(CPCAPI2_AUTO_TEST)
   std::cout << "endSession - session handle: " << session << " destined for reactor on thread " << mReactor.getThreadId() << std::endl << std::flush;
#endif

   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::endSessionImpl, this, session, false));
   return kSuccess;
}

int ConferenceConnectorInterface::endSession(CloudConferenceSessionHandle session, bool isReconnecting)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::endSessionImpl, this, session, isReconnecting));
   return kSuccess;
}

void ConferenceConnectorInterface::endSessionImpl(CloudConferenceSessionHandle session, bool isReconnecting)
{
   InfoLog(<< "endSessionImpl - session handle: " << session);

   ConferenceConnectorInterface::SessionMap::iterator itSess = mSessionMap.find(session);
   if (itSess != mSessionMap.end())
   {
      StackLog(<< "ConferenceConnectorInterface::endSessionImpl(): " << this << " phone: " << mPhone << " conn: " << itSess->second << " session: " << session << " isReconnecting: " << isReconnecting);
      itSess->second->endSession(session, isReconnecting);
   }
}

int ConferenceConnectorInterface::updateSessionMedia(CloudConferenceSessionHandle session)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::updateSessionMediaImpl, this, session));
   return kSuccess;
}

void ConferenceConnectorInterface::updateSessionMediaImpl(CloudConferenceSessionHandle session)
{
   InfoLog(<< "updateSessionMedia - session handle: " << session);

   ConferenceConnectorInterface::SessionMap::iterator itSess = mSessionMap.find(session);
   if (itSess != mSessionMap.end())
   {
      StackLog(<< "ConferenceConnectorInterface::updateSessionMediaImpl(): " << this << " phone: " << mPhone << " conn: " << itSess->second << " session: " << session);
      itSess->second->updateSessionMedia(session);
   }
}

int ConferenceConnectorInterface::setParticipantPermissions(CloudConferenceSessionHandle session, CloudConferenceParticipantHandle participant, const ConferenceSessionPermissions& permissions)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::setParticipantPermissionsImpl, this, session, participant, permissions));
   return kSuccess;
}

void ConferenceConnectorInterface::setParticipantPermissionsImpl(CloudConferenceSessionHandle session, CloudConferenceParticipantHandle participant, const ConferenceSessionPermissions& permissions)
{
   ConferenceConnectorInterface::SessionMap::iterator itSess = mSessionMap.find(session);
   if (itSess != mSessionMap.end())
   {
      StackLog(<< "ConferenceConnectorInterface::setParticipantPermissionsImpl(): " << this << " phone: " << mPhone << " conn: " << itSess->second << " session: " << session);
      itSess->second->setParticipantPermissions(session, participant, permissions);
   }
}

int ConferenceConnectorInterface::pauseSessionPlayout(CloudConferenceSessionHandle session)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::pauseSessionPlayoutImpl, this, session));
   return kSuccess;
}

void ConferenceConnectorInterface::pauseSessionPlayoutImpl(CloudConferenceSessionHandle session)
{
   ConferenceConnectorInterface::SessionMap::iterator itSess = mSessionMap.find(session);
   if (itSess != mSessionMap.end())
   {
      StackLog(<< "ConferenceConnectorInterface::pauseSessionPlayoutImpl(): " << this << " phone: " << mPhone << " conn: " << itSess->second << " session: " << session);
      itSess->second->pauseSessionPlayout(session);
   }
}

int ConferenceConnectorInterface::resumeSessionPlayout(CloudConferenceSessionHandle session)
{
   postToSdkThread(resip::resip_bind(&ConferenceConnectorInterface::resumeSessionPlayoutImpl, this, session));
   return kSuccess;
}

void ConferenceConnectorInterface::resumeSessionPlayoutImpl(CloudConferenceSessionHandle session)
{
   ConferenceConnectorInterface::SessionMap::iterator itSess = mSessionMap.find(session);
   if (itSess != mSessionMap.end())
   {
      StackLog(<< "ConferenceConnectorInterface::resumeSessionPlayoutImpl(): " << this << " phone: " << mPhone << " conn: " << itSess->second << " session: " << session);
      itSess->second->resumeSessionPlayout(session);
   }
}

/*
   ServiceConnectionStatus_Disconnecting = 0,
   ServiceConnectionStatus_Disconnected = 1,
   ServiceConnectionStatus_Connecting = 2,
   ServiceConnectionStatus_Authenticating = 3,
   ServiceConnectionStatus_Connected = 4,
   ServiceConnectionStatus_ConnFailure = 5,
   ServiceConnectionStatus_AuthFailure = 6
*/
resip::Data serviceConnStatusToString(ServiceConnectionStatus s)
{
   switch (s)
   {
   case ServiceConnectionStatus_Disconnecting:
      return "ServiceConnectionStatus_Disconnecting";
   case ServiceConnectionStatus_Disconnected:
      return "ServiceConnectionStatus_Disconnected";
   case ServiceConnectionStatus_Connecting:
      return "ServiceConnectionStatus_Connecting";
   case ServiceConnectionStatus_Authenticating:
      return "ServiceConnectionStatus_Authenticating";
   case ServiceConnectionStatus_Connected:
      return "ServiceConnectionStatus_Connected";
   case ServiceConnectionStatus_ConnFailure:
      return "ServiceConnectionStatus_ConnFailure";
   case ServiceConnectionStatus_AuthFailure:
      return "ServiceConnectionStatus_AuthFailure";
   default:
      break;
   }
   return "Unknown";
}

void ConferenceConnectorInterface::fireConnStatusEvent(ConferenceConnectorHandle conn, const ServiceConnectionStatusEvent& evt)
{
   InfoLog(<< "ConferenceConnectorHandler::onServiceConnectionStatusChanged - handle: " << conn << ", status=" << serviceConnStatusToString(evt.connectionStatus));
   fireEvent(cpcFunc(ConferenceConnectorHandler::onServiceConnectionStatusChanged), conn, evt);
}

void ConferenceConnectorInterface::fireConfCreatedEvent(ConferenceConnectorHandle conn, const ConferenceCreatedEvent& evt)
{
   InfoLog(<< "ConferenceConnectorHandler::onConferenceCreated - handle: " << conn);
   fireEvent(cpcFunc(ConferenceConnectorHandler::onConferenceCreated), conn, evt);
}

void ConferenceConnectorInterface::fireConfEndedEvent(ConferenceConnectorHandle conn, const ConferenceEndedEvent& evt)
{
   InfoLog(<< "ConferenceConnectorHandler::onConferenceEnded - handle: " << conn);
   fireEvent(cpcFunc(ConferenceConnectorHandler::onConferenceEnded), conn, evt);
}

void ConferenceConnectorInterface::fireConfListUpdatedEvent(ConferenceConnectorHandle conn, const ConferenceListUpdatedEvent& evt)
{
   InfoLog(<< "ConferenceConnectorHandler::onConferenceListUpdated - handle: " << conn);
   fireEvent(cpcFunc(ConferenceConnectorHandler::onConferenceListUpdated), conn, evt);
}

void ConferenceConnectorInterface::firePartListUpdatedEvent(ConferenceConnectorHandle conn, const ConferenceParticipantListUpdatedEvent& args)
{
   InfoLog(<< "ConferenceConnectorHandler::onConferenceParticipantListUpdated - handle: " << conn);
   fireEvent(cpcFunc(ConferenceConnectorHandler::onConferenceParticipantListUpdated), conn, args);
}

/*
   SessionStatus_NotConnected,
   SessionStatus_Connecting,
   SessionStatus_Connected,
   SessionStatus_ConnectionFailed,
   SessionStatus_Reconnecting
*/
resip::Data sessionStatusToString(SessionStatus s)
{
   switch (s)
   {
   case SessionStatus_NotConnected:
      return "SessionStatus_NotConnected";
   case SessionStatus_Connecting:
      return "SessionStatus_Connecting";
   case SessionStatus_Connected:
      return "SessionStatus_Connected";
   case SessionStatus_ConnectionFailed:
      return "SessionStatus_ConnectionFailed";
   case SessionStatus_Reconnecting:
      return "SessionStatus_Reconnecting";
   default:
      break;
   }
   return "Unknown";
}

void ConferenceConnectorInterface::fireConfSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args)
{
   InfoLog(<< "ConferenceConnectorHandler::onConferenceSessionStatusChanged - handle: " << conn << ", status=" << sessionStatusToString(args.sessionStatus));
   fireEvent(cpcFunc(ConferenceConnectorHandler::onConferenceSessionStatusChanged), conn, args);
}

/*
 SessionMediaStatus_None,
 SessionMediaStatus_OfferSent,
 SessionMediaStatus_AnswerReceived
*/
resip::Data sessionMediaStatusToString(SessionMediaStatus s)
{
   switch (s)
   {
   case SessionMediaStatus_None:
      return "SessionMediaStatus_None";
   case SessionMediaStatus_OfferSent:
      return "SessionMediaStatus_OfferSent";
   case SessionMediaStatus_AnswerReceived:
      return "SessionMediaStatus_AnswerReceived";
   default:
      break;
   }
   return "Unknown";
}

void ConferenceConnectorInterface::fireConfSessionMediaStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionMediaStatusChangedEvent& args)
{
   InfoLog(<< "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged - handle: " << conn << ", status=" << sessionMediaStatusToString(args.mediaStatus));
   fireEvent(cpcFunc(ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged), conn, args);
}

void ConferenceConnectorInterface::fireConfConnMediaStatistics(ConferenceConnectorHandle conn, const ConferenceConnectorMediaStatisticsEvent& args)
{
   InfoLog(<< "ConferenceConnectorHandler::onConferenceConnectorMediaStatistics - handle: " << conn);
   fireEvent(cpcFunc(ConferenceConnectorHandler::onConferenceConnectorMediaStatistics), conn, args);
}

void ConferenceConnectorInterface::mapConferenceHandleToInst(CloudConferenceHandle conference, const std::shared_ptr<ConferenceConnectorImpl>& inst)
{
   mConfMap[conference] = inst;
}

void ConferenceConnectorInterface::unmapConferenceHandle(CloudConferenceHandle conference)
{
   mConfMap.erase(conference);
}

void ConferenceConnectorInterface::unmapSessionHandle(CloudConferenceSessionHandle session)
{
   mSessionMap.erase(session);
}
   
cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::ServiceConnectionStatus& status)
{
   switch (status)
   {
      case ServiceConnectionStatus_Disconnecting: return "disconnecting";
      case ServiceConnectionStatus_Disconnected: return "disconnected";
      case ServiceConnectionStatus_Connecting: return "connecting";
      case ServiceConnectionStatus_Authenticating: return "authenticating";
      case ServiceConnectionStatus_Connected: return "connected";
      case ServiceConnectionStatus_ConnFailure: return "connfailure";
      case ServiceConnectionStatus_AuthFailure: return "authfailure";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::ServiceConnectionStatusEvent& event)
{
   std::stringstream ss;
   ss << "serverUri: " << event.serverUri << " connectionStatus: " << get_debug_string(event.connectionStatus) << " statusDesc: " << event.statusDesc;
   // ss << " authToken: " << event.authToken);
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::ConferenceCreatedEvent& event)
{
   std::stringstream ss;
   ss << "conference: " << event.conference;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::ConferenceEndedEvent& event)
{
   std::stringstream ss;
   ss << "conference: " << event.conference;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::CloudConferenceType& confType)
{
   switch (confType)
   {
      case CloudConferenceType_Screenshare: return "screenshare";
      case CloudConferenceType_AudioVideo_MCU: return "audiovideo_mcu";
      case CloudConferenceType_AudioVideo_SFU: return "audiovideo_sfu";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::CloudConferenceInfo& info)
{
   std::stringstream ss;
   ss << "conference: " << info.conference << " conferenceType: " << get_debug_string(info.conferenceType) << " displayName: " << info.displayName << " joinUrl: " << info.joinUrl << " description: " << info.description;
   ss << " tag-count: " << info.tags.size() << " { ";
   for (cpc::vector<int>::const_iterator i = info.tags.begin(); i != info.tags.end(); i++)
   {
      ss  << (*i) << " ";
   }
   ss << "}";
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::ConferenceListUpdatedEvent& event)
{
   std::stringstream ss;
   ss << "conference count: " << event.conferenceList.size();
   for (cpc::vector<CloudConferenceInfo>::const_iterator i = event.conferenceList.begin(); i != event.conferenceList.end(); i++)
   {
      ss << " {" << get_debug_string(*i) << "}";
   }
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& info)
{
   std::stringstream ss;
   ss << "participant: " << info.participant << " address: " << info.address << " displayName: " << info.displayName << " hasFloor: " << info.hasFloor << " peerConnection: " << info.peerConnection << " joinTimestampMsecs: " << info.joinTimestampMsecs;
   ss << " tag-count: " << info.tags.size() << " { ";
   for (cpc::vector<int>::const_iterator i = info.tags.begin(); i != info.tags.end(); i++)
   {
      ss  << (*i) << " ";
   }
   ss << "}";
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::ConferenceParticipantListUpdatedEvent& event)
{
   std::stringstream ss;
   ss << "conference: " << event.conference;
   ss << " participant count: " << event.participantList.size();
   for (cpc::vector<CloudConferenceParticipantInfo>::const_iterator i = event.participantList.begin(); i != event.participantList.end(); i++)
   {
      ss << " {" << get_debug_string(*i) << "}";
   }
   ss << " added-participant count: " << event.addedParticipants.size();
   for (cpc::vector<CloudConferenceParticipantInfo>::const_iterator i = event.addedParticipants.begin(); i != event.addedParticipants.end(); i++)
   {
      ss << " {" << get_debug_string(*i) << "}";
   }
   ss << " updated-participant count: " << event.participantList.size();
   for (cpc::vector<CloudConferenceParticipantInfo>::const_iterator i = event.updatedParticipants.begin(); i != event.updatedParticipants.end(); i++)
   {
      ss << " {" << get_debug_string(*i) << "}";
   }
   ss << " removed-participant count: " << event.participantList.size();
   for (cpc::vector<CloudConferenceParticipantInfo>::const_iterator i = event.removedParticipants.begin(); i != event.removedParticipants.end(); i++)
   {
      ss << " {" << get_debug_string(*i) << "}";
   }
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::SessionStatus& status)
{
   switch (status)
   {
      case SessionStatus_NotConnected: return "notconnected";
      case SessionStatus_Connecting: return "connecting";
      case SessionStatus_Connected: return "connected";
      case SessionStatus_ConnectionFailed: return "connectionfailed";
      case SessionStatus_Reconnecting: return "reconnecting";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::SessionMediaStatus& status)
{
   switch (status)
   {
      case SessionMediaStatus_None: return "none";
      case SessionMediaStatus_OfferSent: return "offersent";
      case SessionMediaStatus_AnswerReceived: return "answerreceived";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::MediaDirection& direction)
{
   switch (direction)
   {
      case MediaDirection_None: return "none";
      case MediaDirection_SendRecv: return "sendrecv";
      case MediaDirection_SendOnly: return "sendonly";
      case MediaDirection_RecvOnly: return "recvonly";
      case MediaDirection_Inactive: return "inactive";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::SessionMediaType& media)
{
   switch (media)
   {
      case SessionMediaType_Audio: return "audio";
      case SessionMediaType_Video: return "video";
      case SessionMediaType_Screenshare: return "screenshare";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::ConferenceSessionMediaInfo& info)
{
   std::stringstream ss;
   ss << "mediaDirection: " << get_debug_string(info.mediaDirection) << " mediaType: " << get_debug_string(info.mediaType) << " mediaStreamId: " << info.mediaStreamId;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::ConferenceSessionPermissions& permissions)
{
   std::stringstream ss;
   ss << "canCreateConference: " << permissions.canCreateConference;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::ConferenceSessionStatusChangedEvent& event)
{
   std::stringstream ss;
   ss << "conference: " << event.conference << " session: " << event.session << " sessionStatus: " << get_debug_string(event.sessionStatus) << " audio: " << get_debug_string(event.audio) << " video: " << get_debug_string(event.video) << " screenshare: " << get_debug_string(event.screenshare) << " permissions: " << get_debug_string(event.permissions) << " peerConnection: " << event.peerConnection;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::ConferenceSessionMediaStatusChangedEvent& event)
{
   std::stringstream ss;
   ss << "conference: " << event.conference << " session: " << event.session << " peerConnection: " << event.peerConnection << " mediaStatus: " << get_debug_string(event.mediaStatus);
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::ConferenceConnectorMediaStatisticsEvent& event)
{
   std::stringstream ss;
//   ss << "conference: " << event.conference << " session: " << event.session << " peerConnection: " << event.peerConnection << " mediaStatus: " << get_debug_string(event.mediaStatus);
   return ss.str().c_str();
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ServiceConnectionStatus& status)
{
   os << CPCAPI2::ConferenceConnector::get_debug_string(status);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ServiceConnectionStatusEvent& event)
{
   os << CPCAPI2::ConferenceConnector::get_debug_string(event);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceCreatedEvent& event)
{
   os << CPCAPI2::ConferenceConnector::get_debug_string(event);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceEndedEvent& event)
{
   os << CPCAPI2::ConferenceConnector::get_debug_string(event);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::CloudConferenceType& confType)
{
   os << CPCAPI2::ConferenceConnector::get_debug_string(confType);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::CloudConferenceInfo& info)
{
   os << CPCAPI2::ConferenceConnector::get_debug_string(info);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceListUpdatedEvent& event)
{
   os << CPCAPI2::ConferenceConnector::get_debug_string(event);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& info)
{
   os << CPCAPI2::ConferenceConnector::get_debug_string(info);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceParticipantListUpdatedEvent& event)
{
   os << CPCAPI2::ConferenceConnector::get_debug_string(event);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::SessionStatus& status)
{
   os << CPCAPI2::ConferenceConnector::get_debug_string(status);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::MediaDirection& direction)
{
   os << CPCAPI2::ConferenceConnector::get_debug_string(direction);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::SessionMediaType& media)
{
   os << CPCAPI2::ConferenceConnector::get_debug_string(media);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceSessionMediaInfo& info)
{
   os << CPCAPI2::ConferenceConnector::get_debug_string(info);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceSessionPermissions& permissions)
{
   os << CPCAPI2::ConferenceConnector::get_debug_string(permissions);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceSessionStatusChangedEvent& event)
{
   os << CPCAPI2::ConferenceConnector::get_debug_string(event);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceSessionMediaStatusChangedEvent& event)
{
   os << CPCAPI2::ConferenceConnector::get_debug_string(event);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceConnectorMediaStatisticsEvent& event)
{
   os << CPCAPI2::ConferenceConnector::get_debug_string(event);
   return os;
}

}

}

#endif // CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE
