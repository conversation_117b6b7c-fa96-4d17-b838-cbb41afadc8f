#include "brand_branded.h"

#if (CPCAPI2_BRAND_CALL_QUALITY_REPORT_MODULE == 1)
#include "CallQualityReporterImpl.h"
#include "CallQualityReportManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "call/SipConversationManager.h"
#include "call/SipAVConversationManagerInterface.h"
#include "media/MediaManager.h"
#include "media/MediaManagerInterface.h"
#include "event/SipEventPublicationManagerInterface.h"
#include "call/SipAVConversationManagerInterface.h"

#include "../util/cpc_logger.h"

#include <MediaStackImpl.hxx>
#include <VQmonHelper.hxx>
#include <MixerImpl.hxx>
#include <RtpStreamImpl.hxx>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::CALL_QUALITY

using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipEvent;

namespace CPCAPI2
{
namespace CallQuality
{
CallQualityReporterImpl::CallQualityReporterImpl(CallQualityReportManagerInterface* ccif, CallQualityReporterHandle h)
   : mInterface(ccif),
     mHandle(h),
     mPubHandle((SipEventPublicationHandle)-1)
{
   mSipEventPublishMgr = CPCAPI2::SipEvent::SipEventPublicationManager::getInterface(ccif->getPhone());
   CPCAPI2::Media::MediaManagerInterface* mm = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mInterface->getPhone()));
   std::shared_ptr<webrtc_recon::MediaStackImpl> mediaStack = mm->media_stack_ptr();
   std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mediaStack->mixer());
   mixer->registerMixerObserver(this);

   mSipConvMgr = CPCAPI2::SipConversation::SipConversationManager::getInterface(ccif->getPhone());
}

CallQualityReporterImpl::~CallQualityReporterImpl()
{
   stopCallQualityReporter();
   CPCAPI2::Media::MediaManagerInterface* mm = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mInterface->getPhone()));
   std::shared_ptr<webrtc_recon::MediaStackImpl> mediaStack = mm->media_stack_ptr();
   std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mediaStack->mixer());
   mixer->unregisterMixerObserver(this);
}

void CallQualityReporterImpl::configureCallQualityReporter(const CallQualityReporterConfig& config)
{
   mConfig = config;
}

void CallQualityReporterImpl::startCallQualityReporter()
{
   InfoLog(<< "CallQualityReporterImpl::startCallQualityReporter()");
   SipConversationManager* convMgr = SipConversationManager::getInterface(mInterface->getPhone());
   if (mSipEventPublishMgr != NULL && convMgr != NULL)
   {
      CPCAPI2::Media::MediaManagerInterface* mm = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mInterface->getPhone()));
      if (mm == NULL)
      {
         WarningLog(<< "CallQualityReporterImpl::startCallQualityReporter() - unable to retrieve MediaManager");
         return;
      }
      mm->setVqIntervalReportingEventInterval(mConfig.reportingIntervalSeconds);

      mSipEventPublishMgr->setHandler(mConfig.sipAccount, "vq-rtcpxr", this);
   }
   else
   {
      WarningLog(<< "CallQualityReporterImpl::startCallQualityReporter() - unable to start " << (mSipEventPublishMgr == NULL ? "SipEventPublicationManager is null" : "") << " " << (convMgr == NULL ? "SipConversationManager is null" : ""));
   }
}

void CallQualityReporterImpl::stopCallQualityReporter()
{
   for (const auto& itRtpStream : mRtpStreams)
   {
      if (std::shared_ptr<webrtc_recon::RtpStreamImpl> rtpStreamImpl = itRtpStream.lock())
      {
         rtpStreamImpl->removeIntervalCallQualityReportCallback(this);
      }
   }
}

int CallQualityReporterImpl::onPublicationSuccess(SipEventPublicationHandle publication, const PublicationSuccessEvent& args)
{
   DebugLog(<< "CallQualityReporterImpl received PublicationSuccessEvent");
   CallQualityReportSuccessEvent evt;
   mInterface->fireCallQualityEvent(cpcFunc(CallQualityReportHandler::onCallQualityReportSuccess), mHandle, evt);
   return kSuccess;
}

int CallQualityReporterImpl::onPublicationFailure(SipEventPublicationHandle publication, const PublicationFailureEvent& args)
{
   WarningLog(<< "CallQualityReporterImpl failed to publish call quality report to " << mConfig.reportingServiceSipUri.c_str() << "; errorDetails=" << args.reason);
   CallQualityReportFailureEvent evt;
   evt.sipResponseCode = 0;
   evt.sipErrorDetails = "";
   evt.errorCode = args.reason;
   mInterface->fireCallQualityEvent(cpcFunc(CallQualityReportHandler::onCallQualityReportFailure), mHandle, evt);
   return kSuccess;
}

int CallQualityReporterImpl::onPublicationRemove(SipEventPublicationHandle publication, const PublicationRemoveEvent & args)
{
   InfoLog(<< "CallQualityReporterImpl received PublicationRemoveEvent");
   return kSuccess;
}

int CallQualityReporterImpl::onError(SipEventPublicationHandle publication, const PublicationErrorEvent& args)
{
   WarningLog(<< "CallQualityReporterImpl received PublicationErrorEvent for " << mConfig.reportingServiceSipUri.c_str() << "; errorDetails=" << args.errorText);
   return kSuccess;
}

int CallQualityReporterImpl::onReady(CPCAPI2::SipAccount::SipAccountHandle sipAccount, const CPCAPI2::SipEvent::PublicationManagerReadyEvent& args)
{
   if (sipAccount == mConfig.sipAccount)
   {
      if (!mConfig.reportingServiceSipUri.empty())
      {
         SipEventPublicationSettings pubSettings;
         pubSettings.eventPackage = "vq-rtcpxr";
         pubSettings.supportedMimeTypes.push_back(CPCAPI2::MimeType("application", "vq-rtcpxr"));
         pubSettings.ignoreFailures = mConfig.ignoreFailures;
         mPubHandle = dynamic_cast<SipEventPublicationManagerInterface*>(mSipEventPublishMgr)->createPublicationSync(mConfig.sipAccount, pubSettings);

         mSipEventPublishMgr->setTarget(mPubHandle, mConfig.reportingServiceSipUri);
      }
      else
      {
         InfoLog(<< "CallQualityReporterImpl::startCallQualityReporter() - no reportingServiceSipUri configured; interval reports will NOT be published");
      }
   }
   return kSuccess;
}

void CallQualityReporterImpl::onRtpStreamAdded(const std::shared_ptr<recon::RtpStream>& ms)
{
   std::shared_ptr<webrtc_recon::RtpStreamImpl> rtpStreamImpl = std::dynamic_pointer_cast<webrtc_recon::RtpStreamImpl>(ms);
   if (dynamic_cast<CPCAPI2::SipConversation::SipAVConversationManagerInterface*>(mSipConvMgr)->getSipAccountHandleForCallId(rtpStreamImpl->getSessionId().c_str()) == mConfig.sipAccount)
   {
      rtpStreamImpl->addIntervalCallQualityReportCallback(mConfig.reportingIntervalSeconds, this, mHandle);
      mRtpStreams.push_back(rtpStreamImpl);
   }
}

void CallQualityReporterImpl::onRtpStreamRemoved(const std::shared_ptr<recon::RtpStream>& ms)
{
}

void CallQualityReporterImpl::onIntervalReport(const std::shared_ptr<recon::RtpStream>& rtpStream, const resip::Data& cqr, int appState)
{
   DebugLog(<< "CallQualityReporterImpl::onIntervalReport(..)");

   if (!mConfig.reportingServiceSipUri.empty())
   {
      SipEventState eventState;
      eventState.eventPackage = "vq-rtcpxr";
      eventState.mimeType = "application";
      eventState.mimeSubType = "vq-rtcpxr";
      eventState.expiresTimeMs = 300;
      eventState.contentLength = cqr.size();
      eventState.contentUTF8 = cqr.c_str();
      mSipEventPublishMgr->publish(mPubHandle, eventState);
   }

   CallQualityReportGeneratedEvent evt;
   evt.callQualityReport = cqr.c_str();
   mInterface->fireCallQualityEvent(cpcFunc(CallQualityReportHandler::onCallQualityReportGenerated), mHandle, evt);
}

}

}

#endif // CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE
