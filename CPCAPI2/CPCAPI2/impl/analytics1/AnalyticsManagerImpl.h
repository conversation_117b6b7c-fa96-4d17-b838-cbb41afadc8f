#pragma once
#ifndef __CPCAPI2_ANAYLTICSMANAGERIMPL_H__
#define __CPCAPI2_ANAYLTICSMANAGERIMPL_H__

#include <map>
#include <deque>
#include <atomic>

#include <curl/curl.h>

#include <analytics1/AnalyticsManager.h>
#include <account/SipAccount.h>
#include <account/SipAccountInterface.h>
#include <account/SipAccountHandlerInternal.h>
#include <xmpp/XmppAccountHandlerInternal.h>
#include <xmpp/XmppAccountSyncHandler.h>
#include <call/SipConversationManager.h>
#include <call/SipConversationState.h>
#include <call/SipConversationHandlerInternal.h>
#include <phone/EventSyncHandler.h>
#include <ptt/PushToTalkManagerInterface.h>
#include <ptt/PushToTalkSyncHandler.h>
#include <ptt/PushToTalkTypesInternal.h>
#include <ptt/PushToTalkManagerInternal.h>
#include <ptt/PushToTalkHandlerInternal.h>
#include <media/video/VideoHandler.h>
#include "../phone/PhoneModule.h"
#include "../util/HttpClient.h"
#include "../util/STAAssertion.h"

struct msrp_tree;

namespace CPCAPI2
{
   // Forward decl's
   namespace SipConversation
   {
      class SipAVConversationManagerInterface;
      class SipConversationStateManager;
   }

   namespace XmppAccount
   {
      class XmppAccountInterface;
   }

   namespace PushToTalk
   {
      class PushToTalkManagerInterface;
   }

   namespace Analytics
   {
      /**
       * per-connection analytics information
       */
      typedef struct AnalyticsInfo
      {
         AnalyticsHandle handle;

         // The "registry" works like this, each "node" in the registry is
         // stored at a hierarchical path. The path coincides with the
         // hierarchical path of the XML document.
         msrp_tree *registry;
         CPCAPI2::HTTPClient* httpClient;
         std::string completeURL;
         std::string httpUsername;
         std::string httpPassword;
      } AnalyticsInfo;

      struct PttSessionState
      {
         PushToTalk::PushToTalkSessionHandle session;
         PushToTalk::PushToTalkServiceHandle service;
         PushToTalk::PttIdentity remoteIdentity;
         PushToTalk::PttSessionStateType currentState;
         std::string channelId;
         PushToTalk::PttSessionError sessionError;
         bool successful;
         unsigned int responsesReceived;

         PttSessionState() : session(0), service(0), currentState(PushToTalk::PttSessionState_Idle), channelId(""), sessionError(PushToTalk::PttSessionError_None), successful(false), responsesReceived(0) {}
      };

      struct SipConversationState
      {
         bool localHold;
         bool remoteHold;
         cpc::vector<SipConversation::MediaInfo> localMediaInfo;
         cpc::vector<SipConversation::MediaInfo> remoteMediaInfo;
         SipConversation::ConversationStatistics statistics;

         SipConversation::ConversationState conversationState;
         SipConversation::ConversationType conversationType;
         SipAccount::SipAccountHandle account;
         cpc::string remoteAddress;

         SipConversationState()
         {
            localHold = false;
            remoteHold = false;
            conversationState = SipConversation::ConversationState_None;
            conversationType = SipConversation::ConversationType_Incoming;
            account = 0;
         }
      };

      class AnalyticsManagerImpl :
          public CPCAPI2::EventSyncHandler<CPCAPI2::SipAccount::SipAccountHandlerInternal>,
          public CPCAPI2::XmppAccount::XmppAccountHandlerInternal,
          public CPCAPI2::XmppAccount::XmppAccountSyncHandler,
          public CPCAPI2::PushToTalk::PushToTalkHandlerInternal,
          public CPCAPI2::PushToTalk::PushToTalkSyncHandler,
          public CPCAPI2::EventSyncHandler<CPCAPI2::SipConversation::SipConversationHandlerInternal>,
          public NetworkChangeHandler,
          public resip::ReactorBinded
      {
      public:

          AnalyticsManagerImpl(
              resip::Fifo<resip::ReadCallbackBase>& callbacks,
              CPCAPI2::SipAccount::SipAccountInterface *acctIf,
              CPCAPI2::XmppAccount::XmppAccountInterface *xmppAcctIf,
              CPCAPI2::PushToTalk::PushToTalkManagerInterface* pttIf,
              CPCAPI2::SipConversation::SipAVConversationManagerInterface* convIf,
              CPCAPI2::SipConversation::SipConversationStateManager* stateIf,
              CPCAPI2::Phone* phone,
              const std::shared_ptr<resip::MultiReactor>& reactor);

         // to be called from main SDK thread
         void Release();

         void setCallbackHook( void (*cbHook)(void*), void* context );

         // AnalyticsManager (NB: methods not exactly the same since we don't implement the interface)
         void setHandler( AnalyticsHandler* handler );
         int open(const AnalyticsSettings& settings, const GeneralStats& stats, const AnalyticsHandle& inHandle);
         int close(const AnalyticsHandle& serverHandle);
         int setPresenceStats( const AnalyticsHandle& serverHandle, const PresenceStats& stats );
         int setProvisioningStats( const AnalyticsHandle& serverHandle, const ProvisioningStats& stats );
         int setStabilityStats( const AnalyticsHandle& serverHandle, const StabilityStats& stats );
         int setSettingsStats( const AnalyticsHandle& serverHandle, const SettingsStats& stats );
         int sendReport( const AnalyticsHandle& serverHandle );

         // internal methods to set data passed in from other modules
         int xmppAccountStatusChangeFired(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& args);
         int pttServiceStatusChangeFired(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PttServiceStatusChangedEvent& args);
         int pttSessionStatusChangeFired(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttSessionStateChangedEvent& args);
         int CurrentVideoDeviceUpdatedFired(CPCAPI2::Media::VideoDeviceInfo devInfo);
         int instantMessageInfoFired(unsigned int accountHandle, const bool incoming, const bool isSip);
         int ConvRecordingStarted(CPCAPI2::SipConversation::SipConversationHandle conversationHandle);

         // SipAccountHandlerInternal
         virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args) OVERRIDE;
         virtual int onAccountStatusChangedImpl(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args);

         virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args) OVERRIDE { return kSuccess; }

         virtual int onAccountConfigured( CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountConfiguredEvent& args ) OVERRIDE;
         virtual int onAccountConfiguredSipImpl( CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountConfiguredEvent& args );

         virtual int onAccountEnabled( CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountEnabledEvent& args ) OVERRIDE;
         virtual int onAccountEnabledSipImpl( CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountEnabledEvent& args );

         virtual int onAccountDisabled( CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountDisabledEvent& args ) OVERRIDE;
         virtual int onAccountDisabledSipImpl( CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountDisabledEvent& args );

         // XmppAccountHandlerInternal
         virtual int onAccountConfigured( CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountConfiguredEvent& args ) OVERRIDE;
         virtual int onAccountConfiguredXmppImpl( CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountConfiguredEvent& args );

         virtual int onAccountEnabled( CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountEnabledEvent& args ) OVERRIDE;
         virtual int onAccountEnabledXmppImpl( CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountEnabledEvent& args );

         virtual int onAccountDisabled( CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountDisabledEvent& args ) OVERRIDE;
         virtual int onAccountDisabledXmppImpl( CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountDisabledEvent& args );

         virtual int onAccountStatusChanged(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& args) OVERRIDE { return kSuccess; }

         virtual int onError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::ErrorEvent& args) OVERRIDE  { return kSuccess; }

         virtual int onLicensingError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::LicensingErrorEvent& args) OVERRIDE  { return kSuccess; }

         virtual int onEntityTime(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityTimeEvent& args) OVERRIDE { return kSuccess; }

         virtual int onEntityFeature(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityFeatureEvent& args) OVERRIDE { return kSuccess; }

         virtual int onStreamManagementState(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::StreamManagementStateEvent& args) OVERRIDE { return kSuccess; }

         // PushToTalkHandlerInternal
         virtual int onPttServiceStatusChanged(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PttServiceStatusChangedEvent& args) OVERRIDE;
         virtual int onPttServiceStatusChangedImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PttServiceStatusChangedEvent& args);

         virtual int onPttEndpointList(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PttEndpointListEvent& args) OVERRIDE;
         virtual int onPttEndpointListImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PttEndpointListEvent& args);

         virtual int onPttServiceConfigured(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PttServiceConfiguredEvent& args) OVERRIDE;
         virtual int onPttServiceConfiguredImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PttServiceConfiguredEvent& args);

         virtual int onPttSessionStateChanged(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttSessionStateChangedEvent& args) OVERRIDE;
         virtual int onPttSessionStateChangedImpl(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttSessionStateChangedEvent& args);

         virtual int onPttIncomingCall(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttIncomingCallEvent& args) OVERRIDE;
         virtual int onPttIncomingCallImpl(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttIncomingCallEvent& args);

         virtual int onPttSessionError(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttSessionErrorEvent& args) OVERRIDE;
         virtual int onPttSessionErrorImpl(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttSessionErrorEvent& args);

         virtual int onPttReceiverDisconnected(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttReceiverDisconnectedEvent& args) OVERRIDE;
         virtual int onPttReceiverDisconnectedImpl(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttReceiverDisconnectedEvent& args);

         // SipConversationHandlerInternal
         virtual int onConversationInitiated(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationInitiatedEvent& args) OVERRIDE { return kSuccess; }

         virtual int onNewConversation(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::NewConversationEvent& args) OVERRIDE;
         virtual int onNewConversationImpl(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::NewConversationEvent& args);

         virtual int onConversationEnded(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationEndedEvent& args) OVERRIDE;
         virtual int onConversationEndedImpl(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationEndedEvent& args);

         virtual int onIncomingTransferRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferRequestEvent& args) OVERRIDE;
         virtual int onIncomingTransferRequestImpl(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferRequestEvent& args);

         virtual int onIncomingRedirectRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::RedirectRequestEvent& args) OVERRIDE { return kSuccess; }

         virtual int onIncomingTargetChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TargetChangeRequestEvent& args) OVERRIDE { return kSuccess; }

         virtual int onIncomingHangupRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::HangupRequestEvent& args) OVERRIDE { return kSuccess; }

         virtual int onIncomingBroadsoftTalkRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftTalkEvent& args) OVERRIDE { return kSuccess; }

         virtual int onIncomingBroadsoftHoldRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftHoldEvent& args) OVERRIDE { return kSuccess; }

         virtual int onTransferProgress(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferProgressEvent& args) OVERRIDE;
         virtual int onTransferProgressImpl(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferProgressEvent& args);

         virtual int onConversationStateChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangeRequestEvent& args) OVERRIDE { return kSuccess; }

         virtual int onConversationStateChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangedEvent& args) OVERRIDE;
         virtual int onConversationStateChangedImpl(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangedEvent& args);

         virtual int onConversationMediaChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent& args) OVERRIDE { return kSuccess; }

         virtual int onConversationMediaChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangedEvent& args) OVERRIDE;
         virtual int onConversationMediaChangedImpl(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangedEvent& args);

         virtual int onConversationStatisticsUpdated(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent& args) OVERRIDE;
         virtual int onConversationStatisticsUpdatedImpl(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent& args);

         virtual int onError(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ErrorEvent& args) OVERRIDE { return kSuccess; }

         // NetworkChangeHandler
         virtual int onNetworkChange(const CPCAPI2::NetworkChangeEvent& event) OVERRIDE;
         virtual int onNetworkChangeImpl(const CPCAPI2::NetworkChangeEvent& event);

         // thread safe function to abort active HTTP requests
         int abortActiveHttpClients();

         // thread safe function to abort all future HTTP requests (useful for around e.g. near shutdown time)
         int abortFutureHttpClients();

      private:

         // thread safe functions to track/abort active HTTP requests
         int pushActiveHttpClient(CPCAPI2::HTTPClient*);
         int popActiveHttpClient();
         
         int handlePttSessionUpdate(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttSessionStateChangedEvent& args);
         int handlePttSessionEnd(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttSessionStateChangedEvent& args);
         int getPttState(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, PttSessionState& outState) const;
         int getState(CPCAPI2::SipConversation::SipConversationHandle handle, SipConversationState& outState) const;

         void updateCallOrigin(CPCAPI2::SipConversation::SipConversationHandle account, CPCAPI2::SipConversation::SipConversationHandle conversation, msrp_tree* registry, std::string itemPath);
         void setAccountSettingsTransport(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipAccountTransportType transportType);
         int removeUnnecessaryAccounts(std::set<CPCAPI2::SipAccount::SipAccountHandle> &removedSipAccountHandles, std::set<CPCAPI2::XmppAccount::XmppAccountHandle>& removedXmppAccountHandles);
         int populateAccounts( );

         cpc::vector<XmppAccount::XmppAccountHandle> getXmppAccountHandles() const;


      protected:

         virtual ~AnalyticsManagerImpl();
         virtual void release() OVERRIDE
         {
            delete this;
         }
         
         void clearSipCalls();
         void clearPttCalls();

      private: // data

         typedef std::map< AnalyticsHandle, AnalyticsInfo* > AnalyticsMap;
         AnalyticsMap mInfoMap;
         typedef std::map< CPCAPI2::XmppAccount::XmppAccountHandle, CPCAPI2::XmppAccount::XmppAccountSettings> XmppSettingsMap;
         XmppSettingsMap mXmppSettingsMap;

         typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, std::map<NetworkTransport, SipAccount::SipAccountSettings> > SipSettingsMap;
         SipSettingsMap mSipSettingsMap;

         typedef std::map<CPCAPI2::PushToTalk::PushToTalkServiceHandle, CPCAPI2::PushToTalk::PushToTalkServiceSettings> PttSettingsMap;
         PttSettingsMap mPttSettingsMap;

         STAAssertion mAnalyticsThreadCheck;
         STAAssertion mMainSdkThreadCheck;

         // For posting events to the handler
         resip::Fifo<resip::ReadCallbackBase>& mCallbacks;
         std::function<void(void)> m_CbHook;

         CPCAPI2::NetworkChangeManagerInterface* mNetworkChangeManagerIf;

         // Handle to the account interface
         CPCAPI2::SipAccount::SipAccountInterface *mAccountInterface;

         // Handle to the XMPP Account Interface
         CPCAPI2::XmppAccount::XmppAccountInterface *mXmppAccountInterface;

         // Handle to the PTT Manager Interface
         CPCAPI2::PushToTalk::PushToTalkManagerInterface* mPttManagerInterface;

         // Handle to the conversation manager interface
         CPCAPI2::SipConversation::SipAVConversationManagerInterface *mConversationManager;

         // Pointer to the handler associated with this manager
         AnalyticsHandler *mHandler;

         // keep track of accounts we have in the report so we can add/remove them as needed before/after sending the report
         std::set<CPCAPI2::SipAccount::SipAccountHandle> mAccumulatedSipAccountList;
         std::set<CPCAPI2::XmppAccount::XmppAccountHandle> mAccumulatedXmppAccountList;
         // keep track of the enabled accounts so we can populate the report
         std::set<CPCAPI2::SipAccount::SipAccountHandle> mEnabledSipAccountList;
         std::set<CPCAPI2::XmppAccount::XmppAccountHandle> mEnabledXmppAccountList;
         // we keep track of accounts with statistics so we can keep them in the report even if the account has been removed
         std::set<CPCAPI2::SipAccount::SipAccountHandle> mSipAccountWithStatsList;
         std::set<CPCAPI2::XmppAccount::XmppAccountHandle> mXmppAccountWithStatsList;

         // Mini clone of PushToTalkServiceStatus
         std::map<CPCAPI2::PushToTalk::PushToTalkServiceHandle, CPCAPI2::PushToTalk::PttServiceStatusChangedEvent::Status> mPttServiceStateMap;

         // Keep track of ptt calls based on PushToTalkSessionStatus
         std::map<CPCAPI2::PushToTalk::PushToTalkSessionHandle, PttSessionState> mPttSessionStateMap;
         std::map<std::string, CPCAPI2::PushToTalk::PushToTalkSessionHandle> mPttSessionCurrentMap;
         std::list<CPCAPI2::PushToTalk::PushToTalkSessionHandle> mPttSessionActiveList;
         std::map<CPCAPI2::PushToTalk::PushToTalkSessionHandle, CPCAPI2::PushToTalk::PttIdentity> mPttSessionIncomingMap;

         // Handles associated with all active calls and whether they have been transfered in any way, probably only if someone transfers a call you are already on
         std::map<CPCAPI2::SipConversation::SipConversationHandle, bool> mConvoTransferredMap;

         // Handles associated with last written (current) conversation to the report if any per account
         std::map<std::string, CPCAPI2::SipConversation::SipConversationHandle> mCurrentConvoMap;

         // Keep track of which current conversations have been at least partially recorded
         std::map<CPCAPI2::SipConversation::SipConversationHandle, bool> mConvoRecordedMap;

         // Keep track of which current conversations that are still active/connected on the phone
         std::list<CPCAPI2::SipConversation::SipConversationHandle> mActiveConvoList;

         // Keep track of which current conversations have been held
         std::map<CPCAPI2::SipConversation::SipConversationHandle, bool> mConvoHeldMap;

         // Mini clone of SipConversationState
         std::map<CPCAPI2::SipConversation::SipConversationHandle, SipConversationState> mConvoStateMap;

         // Pointer to phone
         PhoneInterface* mPhone;

         // Current video device name
         std::string mCurrentVideoDeviceName;

         NetworkChangeManagerImpl::NetworkChangeMangerImplHandle mNetworkChangeManagerImplHandle;
         resip::Data mCurrentIP;
         int64_t mLastIPChangeTime;

         std::deque<CPCAPI2::HTTPClient*> mActiveHttpRequestClients;
         resip::Mutex mActiveHttpRequestClientsMutex;
         std::atomic<bool> mAbortFutureHttpClients;

         std::shared_ptr<resip::MultiReactor> mReactor;

      };
   }
}

#endif //__CPCAPI2_ANAYLTICSMANAGERIMPL_H__
