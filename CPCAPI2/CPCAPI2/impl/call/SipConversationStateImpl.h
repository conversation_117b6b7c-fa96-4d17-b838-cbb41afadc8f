#pragma once

#if !defined(CPCAPI2_SIP_CONVERSATION_STATE_IMPL_H)
#define CPCAPI2_SIP_CONVERSATION_STATE_IMPL_H

#include "cpcapi2defs.h"
#include "call/SipConversationTypes.h"
#include "call/SipConversationManager.h"
#include "call/SipConversationState.h"
#include "SipConversationHandlerInternal.h"
//#include "SipCallCreationInfo.h"
#include "../phone/PhoneModule.h"

#include <map>
#include <rutil/Mutex.hxx>

namespace webrtc_recon
{
class MediaStackImpl;
}

namespace CPCAPI2
{
namespace SipConversation
{
class SipAVConversationManagerInterface;

class SipConversationStateImpl : public SipConversationStateManager,
                                 public PhoneModule,
                                 public SipConversationHandlerInternal
{
public:
   SipConversationStateImpl(SipAVConversationManagerInterface* convManIf);
   virtual ~SipConversationStateImpl();

   virtual int getState(SipConversationHandle h, SipConversationState& conversationState) OVERRIDE;
   virtual int getStateAllConversations(cpc::vector<SipConversationState>& conversationState) OVERRIDE;
   virtual int getStateAllActiveConversations(cpc::vector<SipConversationState>& conversationState) OVERRIDE;
   virtual void Release() OVERRIDE;

   virtual int onNewConversation(SipConversationHandle conversation, const NewConversationEvent& args) OVERRIDE;

   virtual int onConversationEnded(SipConversationHandle conversation, const ConversationEndedEvent& args) OVERRIDE;

   virtual int onIncomingTransferRequest(SipConversationHandle conversation, const TransferRequestEvent& args) OVERRIDE;
   virtual int onIncomingRedirectRequest(SipConversationHandle conversation, const RedirectRequestEvent& args) OVERRIDE;
   virtual int onIncomingTargetChangeRequest(SipConversationHandle conversation, const TargetChangeRequestEvent& args) OVERRIDE;
   virtual int onIncomingHangupRequest(SipConversationHandle conversation, const HangupRequestEvent& args) OVERRIDE;
   virtual int onIncomingBroadsoftTalkRequest(SipConversationHandle conversation, const BroadsoftTalkEvent& args) OVERRIDE;
   virtual int onIncomingBroadsoftHoldRequest(SipConversationHandle conversation, const BroadsoftHoldEvent& args) OVERRIDE;
   virtual int onTransferProgress(SipConversationHandle conversation, const TransferProgressEvent& args) OVERRIDE;

   virtual int onConversationStateChangeRequest(SipConversationHandle conversation, const ConversationStateChangeRequestEvent& args) OVERRIDE;
   virtual int onConversationStateChanged(SipConversationHandle conversation, const ConversationStateChangedEvent& args) OVERRIDE;

   virtual int onConversationMediaChangeRequest(SipConversationHandle conversation, const ConversationMediaChangeRequestEvent& args) OVERRIDE;
   virtual int onConversationMediaChanged(SipConversationHandle conversation, const ConversationMediaChangedEvent& args) OVERRIDE;

   virtual int onConversationStatisticsUpdated(SipConversationHandle conversation, const ConversationStatisticsUpdatedEvent& args) OVERRIDE;

   virtual int onError(SipConversationHandle subscription, const ErrorEvent& args) OVERRIDE;

   // virtual int onConversationAdornment(SipConversationHandle conversation, ConversationAdornmentEvent& args) OVERRIDE;

   virtual int onConversationInitiated(SipConversationHandle conversation, const ConversationInitiatedEvent& args) OVERRIDE;
private:
   void cleanupConv(SipConversationHandle conversation);

private:
   std::map<SipConversationHandle, SipConversationState> mStateMap;
   resip::Mutex mStateLock;
   SipAVConversationManagerInterface* mConvManIf;

};
}
}

#endif // CPCAPI2_SIP_CONVERSATION_STATE_IMPL_H
