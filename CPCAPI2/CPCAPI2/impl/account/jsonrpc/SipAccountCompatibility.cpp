#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE == 1)
#include "SipAccountCompatibility.h"
#include "interface/public/account/SipAccount.h"
#include "jsonrpccxx/common.hpp"


#define ENUM_SWITCH_CASE_LONG(_enum_name, _enum_item_from, _enum_item_to) {               \
                        case _enum_name::_enum_item_from: \
                           return ::CPCAPI2::SipAccount::_enum_name::_enum_name ## _ ## _enum_item_to; \
                   }

#define ENUM_SWITCH_CASE(_enum_name, _enum_item) ENUM_SWITCH_CASE_LONG(_enum_name, _enum_item, _enum_item)

namespace jsonrpc
{
namespace CPCAPI2
{
namespace SipAccount
{

SipAccountCompatibility::SipAccountCompatibility(const std::shared_ptr<::CPCAPI2::PhoneInstances> &phones)
{
   mPhones = phones;
}

::CPCAPI2::SipAccount::SipAccountManager* SipAccountCompatibility::getInstance(const int64_t phoneHandle)
{
   return ::CPCAPI2::SipAccount::SipAccountManager::getInterface(mPhones->get(phoneHandle));
}

int64_t SipAccountCompatibility::create(const int64_t phoneHandle)
{
   return getInstance(phoneHandle)->create();
}

void SipAccountCompatibility::destroy(const int64_t phoneHandle, const int64_t sipAccountHandle)
{
   getInstance(phoneHandle)->destroy(sipAccountHandle);
}

void optionalSet(unsigned int& toSet, const std::optional<SipAccountTransportType> &v)
{
   if (v)
   {
      switch (*v)
      {
      case SipAccountTransportType::Auto:
         toSet = ::CPCAPI2::SipAccount::SipAccountTransport_Auto;
      case SipAccountTransportType::Udp:
         toSet = ::CPCAPI2::SipAccount::SipAccountTransport_UDP;
      case SipAccountTransportType::Tcp:
         toSet = ::CPCAPI2::SipAccount::SipAccountTransport_TCP;
      case SipAccountTransportType::Tls:
         toSet = ::CPCAPI2::SipAccount::SipAccountTransport_TLS;
      case SipAccountTransportType::Unknown:
         toSet = ::CPCAPI2::SipAccount::SipAccountTransport_Unknown;
      default:
         toSet = ::CPCAPI2::SipAccount::SipAccountTransport_Unknown;
      }
   }
}

template <typename T>
void optionalSet(T &toSet, const std::optional<T> opt)
{
   if (opt)
   {
      toSet = *opt;
   }
}

void optionalSet(unsigned int &toSet, const std::optional<int32_t> opt)
{
   if (opt)
   {
      toSet = *opt;
   }
}

std::optional<::CPCAPI2::SipAccount::SipAccountSessionTimerMode> convert(const std::optional<SipAccountSessionTimerMode> item)
{
   if (!item) {
      return std::nullopt;
   }
   switch (*item)
   {
      ENUM_SWITCH_CASE(SipAccountSessionTimerMode, Inactive);
      ENUM_SWITCH_CASE(SipAccountSessionTimerMode, Optional);
      ENUM_SWITCH_CASE(SipAccountSessionTimerMode, Required);
      ENUM_SWITCH_CASE(SipAccountSessionTimerMode, Always);
   }
}

std::optional<::CPCAPI2::SipAccount::IpVersion> convert(const std::optional<IpVersion> item)
{
   if (!item) {
      return std::nullopt;
   }
   switch (*item)
   {
      ENUM_SWITCH_CASE(IpVersion, V4);
      ENUM_SWITCH_CASE(IpVersion, V6);
      ENUM_SWITCH_CASE(IpVersion, Auto);
      ENUM_SWITCH_CASE_LONG(IpVersion, AutoPreferV6, Auto_PreferV6);
   }
}

std::optional<::CPCAPI2::SipAccount::SSLVersion> convert(const std::optional<SslVersion> item)
{
   if (!item) {
      return std::nullopt;
   }
   switch (*item)
   {
   case SslVersion::TlsDefault:
      return ::CPCAPI2::SipAccount::SSLVersion::TLS_DEFAULT;
   case SslVersion::SslNone:
      return ::CPCAPI2::SipAccount::SSLVersion::SSL_NONE;
   case SslVersion::SslV2:
      return ::CPCAPI2::SipAccount::SSLVersion::SSL_V2;
   case SslVersion::SslV3:
      return ::CPCAPI2::SipAccount::SSLVersion::SSL_V3;
   case SslVersion::TlsV1_0:
      return ::CPCAPI2::SipAccount::SSLVersion::TLS_V1_0;
   case SslVersion::TlsV1_1:
      return ::CPCAPI2::SipAccount::SSLVersion::TLS_V1_1;
   case SslVersion::TlsV1_2:
      return ::CPCAPI2::SipAccount::SSLVersion::TLS_V1_2;
   case SslVersion::TlsV1_3:
      return ::CPCAPI2::SipAccount::SSLVersion::TLS_V1_3;
   case SslVersion::SslHighest:
      return ::CPCAPI2::SipAccount::SSLVersion::SSL_HIGHEST;
   case SslVersion::TlsNonDeprecated:
      return ::CPCAPI2::SipAccount::SSLVersion::TLS_NON_DEPRECATED;
   }
}

std::optional<::CPCAPI2::SipAccount::TransportHoldover> convert(const std::optional<TransportHoldover> item)
{
   if (!item) {
      return std::nullopt;
   }
   switch (*item)
   {
      ENUM_SWITCH_CASE(TransportHoldover, None);
      ENUM_SWITCH_CASE(TransportHoldover, All);
      ENUM_SWITCH_CASE(TransportHoldover, V4);
      ENUM_SWITCH_CASE(TransportHoldover, V6);
   }
}

std::optional<::CPCAPI2::SipAccount::StunServerSourceType> convert(const std::optional<StunServerSourceType> item)
{
   if (!item) {
      return std::nullopt;
   }
   switch (*item)
   {
   case StunServerSourceType::None:
      return ::CPCAPI2::SipAccount::StunServerSourceType::StunServerSource_None;
   case StunServerSourceType::Srv:
      return ::CPCAPI2::SipAccount::StunServerSourceType::StunServerSource_SRV;
   case StunServerSourceType::Custom:
      return ::CPCAPI2::SipAccount::StunServerSourceType::StunServerSource_Custom;
   default:
      return ::CPCAPI2::SipAccount::StunServerSourceType::StunServerSource_None;
   }
}

std::optional<::CPCAPI2::SipAccount::KeepAliveMode> convert(const std::optional<KeepAliveMode> item)
{
   if (!item) {
      return std::nullopt;
   }
   switch (*item)
   {
      ENUM_SWITCH_CASE(KeepAliveMode, Default);
      ENUM_SWITCH_CASE(KeepAliveMode, NoKeepAlives);
      ENUM_SWITCH_CASE_LONG(KeepAliveMode, Crlfcrlf, CRLFCRLF);
   }
}

std::optional<cpc::vector<::CPCAPI2::SipParameterType>> convert(const std::optional<cpc::vector<SipParameterType>>& item)
{
   if (!item) {
      return std::nullopt;
   }
   const auto& vec = *item;
   cpc::vector<::CPCAPI2::SipParameterType> result(vec.size());
   for (size_t ind = 0; ind < vec.size(); ++ind) {
      optionalSet(result[ind].name, vec[ind].name);
      optionalSet(result[ind].value, vec[ind].value);
   }
   return result;
}

std::optional<cpc::vector<::CPCAPI2::SipResponseType>> convert(const std::optional<cpc::vector<SipResponseType>>& item)
{
   if (!item) {
      return std::nullopt;
   }
   const auto& vec = *item;
   cpc::vector<::CPCAPI2::SipResponseType> result(vec.size());
   for (size_t ind = 0; ind < vec.size(); ++ind) {
      optionalSet(result[ind].method, vec[ind].method);
      optionalSet(result[ind].responseCode, vec[ind].responseCode);
   }
   return result;
}

std::optional<::CPCAPI2::SipAccount::TunnelType> convert(const std::optional<TunnelType> item)
{
   if (!item) {
      return std::nullopt;
   }
   switch (*item)
   {
      ENUM_SWITCH_CASE_LONG(TunnelType, Tscf, TSCF);
      ENUM_SWITCH_CASE(TunnelType, StrettoTunnel);
      ENUM_SWITCH_CASE(TunnelType, Unknown);
   }
}

std::optional<::CPCAPI2::SipAccount::TunnelTransportType> convert(const std::optional<TunnelTransportType> item)
{
   if (!item) {
      return std::nullopt;
   }
   switch (*item)
   {
      case TunnelTransportType::Auto:
         return ::CPCAPI2::SipAccount::TunnelTransportType::TunnelTransport_Auto;
      case TunnelTransportType::Udp:
         return ::CPCAPI2::SipAccount::TunnelTransportType::TunnelTransport_UDP;
      case TunnelTransportType::Tcp:
         return ::CPCAPI2::SipAccount::TunnelTransportType::TunnelTransport_TCP;
      case TunnelTransportType::Tls:
         return ::CPCAPI2::SipAccount::TunnelTransportType::TunnelTransport_TLS;
      case TunnelTransportType::Dtls:
         return ::CPCAPI2::SipAccount::TunnelTransportType::TunnelTransport_DTLS;
   }
}

std::optional<::CPCAPI2::SipAccount::TunnelMediaTransportType> convert(const std::optional<TunnelMediaTransportType> item)
{
   if (!item) {
      return std::nullopt;
   }
   switch (*item)
   {
   case TunnelMediaTransportType::Default:
      return ::CPCAPI2::SipAccount::TunnelMediaTransportType::TunnelMediaTransport_Default;
   case TunnelMediaTransportType::DatagramPreferred:
      return ::CPCAPI2::SipAccount::TunnelMediaTransportType::TunnelMediaTransport_DatagramPreferred;
   case TunnelMediaTransportType::DatagramOnly:
      return ::CPCAPI2::SipAccount::TunnelMediaTransportType::TunnelMediaTransport_DatagramOnly;
   case TunnelMediaTransportType::StreamPreferred:
      return ::CPCAPI2::SipAccount::TunnelMediaTransportType::TunnelMediaTransport_StreamPreferred;
   case TunnelMediaTransportType::StreamOnly:
      return ::CPCAPI2::SipAccount::TunnelMediaTransportType::TunnelMediaTransport_StreamOnly;
   }
}

std::optional<::CPCAPI2::SipAccount::TunnelConfig> convert(const std::optional<TunnelConfig>& item_opt)
{
   if (!item_opt) {
      return std::nullopt;
   }
   const auto& item = *item_opt;
   ::CPCAPI2::SipAccount::TunnelConfig ret;
   optionalSet(ret.useTunnel, item.useTunnel);
   optionalSet(ret.tunnelType, convert(item.tunnelType));
   optionalSet(ret.server, item.server);
   optionalSet(ret.transportType, convert(item.transportType));
   optionalSet(ret.mediaTransportType, convert(item.mediaTransportType));
   optionalSet(ret.redundancyFactor, item.redundancyFactor);
   optionalSet(ret.doLoadBalancing, item.doLoadBalancing);
   optionalSet(ret.ignoreCertVerification, item.ignoreCertVerification);
   optionalSet(ret.disableNagleAlgorithm, item.disableNagleAlgorithm);
   optionalSet(ret.strettoTunnelURL, item.strettoTunnelUrl);
   optionalSet(ret.strettoTunnelToken, item.strettoTunnelToken);
   optionalSet(ret.strettoTunnelSessionID, item.strettoTunnelSessionId);
   optionalSet(ret.strettoTunnelTestConnection, item.strettoTunnelTestConnection);
   optionalSet(ret.logStrettoTunnelTransportTraces, item.logStrettoTunnelTransportTraces);
   optionalSet(ret.strettoTunnelSkipHandshake, item.strettoTunnelSkipHandshake);
   return ret;
}

::CPCAPI2::SipAccount::SipAccountSettings convert(const SipAccountSettings& sipAccountSettings)
{
   ::CPCAPI2::SipAccount::SipAccountSettings ret;

   optionalSet(ret.username, sipAccountSettings.mUsername);
   optionalSet(ret.domain, sipAccountSettings.mDomain);
   optionalSet(ret.password, sipAccountSettings.mPassword);
   optionalSet(ret.displayName, sipAccountSettings.mDisplayName);
   optionalSet(ret.auth_username, sipAccountSettings.mAuthUsername);
   optionalSet(ret.auth_realm, sipAccountSettings.mAuthRealm);
   optionalSet(ret.useRegistrar, sipAccountSettings.mUseRegistrar);
   optionalSet(ret.outboundProxy, sipAccountSettings.mOutboundProxy);
   optionalSet(ret.alwaysRouteViaOutboundProxy, sipAccountSettings.mAlwaysRouteViaOutboundProxy);
   optionalSet(ret.registrationIntervalSeconds, sipAccountSettings.mRegistrationIntervalSeconds);
   optionalSet(ret.minimumRegistrationIntervalSeconds, sipAccountSettings.mMinimumRegistrationIntervalSeconds);
   optionalSet(ret.maximumRegistrationIntervalSeconds, sipAccountSettings.mMaximumRegistrationIntervalSeconds);
   optionalSet(ret.useRport, sipAccountSettings.mUseRport);
   optionalSet(ret.sipTransportType, sipAccountSettings.mSipTransportType);
   optionalSet(ret.excludeEncryptedTransports, sipAccountSettings.mExcludeEncryptedTransports);
   optionalSet(ret.userAgent, sipAccountSettings.mUserAgent);
   optionalSet(ret.udpKeepAliveTime, sipAccountSettings.mUdpKeepAliveTime);
   optionalSet(ret.tcpKeepAliveTime, sipAccountSettings.mTcpKeepAliveTime);
   optionalSet(ret.useOutbound, sipAccountSettings.mUseOutbound);
   optionalSet(ret.useGruu, sipAccountSettings.mUseGruu);
   optionalSet(ret.otherNonEscapedCharsInUri, sipAccountSettings.mOtherNonEscapedCharsInUri);
   optionalSet(ret.nameServers, sipAccountSettings.mNameServers);
   optionalSet(ret.additionalNameServers, sipAccountSettings.mAdditionalNameServers);
   optionalSet(ret.sessionTimerMode, convert(sipAccountSettings.mSessionTimerMode));
   optionalSet(ret.sessionTimeSeconds, sipAccountSettings.mSessionTimeSeconds);
   optionalSet(ret.stunServerSource, convert(sipAccountSettings.mStunServerSource));
   optionalSet(ret.stunServer, sipAccountSettings.mStunServer);
   optionalSet(ret.ignoreCertVerification, sipAccountSettings.mIgnoreCertVerification);
   optionalSet(ret.additionalCertPeerNames, sipAccountSettings.mAdditionalCertPeerNames);
   optionalSet(ret.acceptedCertPublicKeys, sipAccountSettings.mAcceptedCertPublicKeys);
   optionalSet(ret.requiredCertPublicKeys, sipAccountSettings.mRequiredCertPublicKeys);
   optionalSet(ret.sipQosSettings, sipAccountSettings.mSipQosSettings);
   optionalSet(ret.useImsAuthHeader, sipAccountSettings.mUseImsAuthHeader);
   optionalSet(ret.minSipPort, sipAccountSettings.mMinSipPort);
   optionalSet(ret.maxSipPort, sipAccountSettings.mMaxSipPort);
   optionalSet(ret.defaultSipPort, sipAccountSettings.mDefaultSipPort);
   optionalSet(ret.defaultSipsPort, sipAccountSettings.mDefaultSipsPort);
   optionalSet(ret.useMethodParamInReferTo, sipAccountSettings.mUseMethodParamInReferTo);
   optionalSet(ret.useInstanceId, sipAccountSettings.mUseInstanceId);
   optionalSet(ret.answerModeSupported, sipAccountSettings.mAnswerModeSupported);
   optionalSet(ret.ipVersion, convert(sipAccountSettings.mIpVersion));
   optionalSet(ret.sslVersion, convert(sipAccountSettings.mSslVersion));
   optionalSet(ret.cipherSuite, sipAccountSettings.mCipherSuite);
   optionalSet(ret.enableLegacyServerConnect, sipAccountSettings.mEnableLegacyServerConnect);
   optionalSet(ret.reRegisterOnResponseTypes, convert(sipAccountSettings.mReRegisterOnResponseTypes));
   optionalSet(ret.enableRegeventDeregistration, sipAccountSettings.mEnableRegeventDeregistration);
   optionalSet(ret.enableDNSResetOnRegistrationRefresh, sipAccountSettings.mEnableDnsResetOnRegistrationRefresh);
   optionalSet(ret.enableAuthResetUponDNSReset, sipAccountSettings.mEnableAuthResetUponDnsReset);
   optionalSet(ret.XCAPRoot, sipAccountSettings.mXcapRoot);
   optionalSet(ret.tunnelConfig, convert(sipAccountSettings.mTunnelConfig));
   optionalSet(ret.capabilities, convert(sipAccountSettings.mCapabilities));
   optionalSet(ret.additionalFromParameters, convert(sipAccountSettings.mAdditionalFromParameters));
   optionalSet(ret.sourceAddress, sipAccountSettings.mSourceAddress);
   optionalSet(ret.preferPAssertedIdentity, sipAccountSettings.mPreferPAssertedIdentity);
   optionalSet(ret.autoRetryOnTransportDisconnect, sipAccountSettings.mAutoRetryOnTransportDisconnect);
   optionalSet(ret.keepAliveMode, convert(sipAccountSettings.mKeepAliveMode));
   optionalSet(ret.useRinstance, sipAccountSettings.mUseRinstance);
   optionalSet(ret.enableNat64Support, sipAccountSettings.mEnableNat64Support);
   optionalSet(ret.usePrivacyHeaderOnlyForAnonymous, sipAccountSettings.mUsePrivacyHeaderOnlyForAnonymous);
   optionalSet(ret.transportHoldover, convert(sipAccountSettings.mTransportHoldover));
   optionalSet(ret.useOptionsPing, sipAccountSettings.mUseOptionsPing);
   optionalSet(ret.optionsPingInterval, sipAccountSettings.mOptionsPingInterval);
   optionalSet(ret.userCertificatePEM, sipAccountSettings.mUserCertificatePem);
   optionalSet(ret.userPrivateKeyPEM, sipAccountSettings.mUserPrivateKeyPem);
   optionalSet(ret.forceListenSocket, sipAccountSettings.mForceListenSocket);
   optionalSet(ret.localGroup, sipAccountSettings.mLocalGroup);
   optionalSet(ret.overrideMsecsTimerF, sipAccountSettings.mOverrideMsecsTimerF);
   return ret;
}

::CPCAPI2::NetworkTransport convert(SipAccountNetworkTransport transport)
{
   switch (transport)
   {
   case SipAccountNetworkTransport::None:
      return ::CPCAPI2::NetworkTransport::TransportNone;
   case SipAccountNetworkTransport::WiFi:
      return ::CPCAPI2::NetworkTransport::TransportWiFi;
   case SipAccountNetworkTransport::Wwan:
      return ::CPCAPI2::NetworkTransport::TransportWWAN;
   default:
      return ::CPCAPI2::NetworkTransport::TransportNone;
   }
}

void SipAccountCompatibility::configureDefaultAccountSettings(const int64_t phoneHandle, const int64_t sipAccountHandle, const SipAccountSettings& sipAccountSettings)
{
   getInstance(phoneHandle)->configureDefaultAccountSettings(sipAccountHandle, convert(sipAccountSettings));
}

void SipAccountCompatibility::configureTransportAccountSettings(const int64_t phoneHandle, const int64_t sipAccountHandle, const SipAccountSettings& sipAccountSettings, const SipAccountNetworkTransport transport)
{
   getInstance(phoneHandle)->configureTransportAccountSettings(sipAccountHandle, convert(sipAccountSettings), convert(transport));
}

void SipAccountCompatibility::applySettings(const int64_t phoneHandle, const int64_t sipAccountHandle)
{
   getInstance(phoneHandle)->applySettings(sipAccountHandle);
}

void SipAccountCompatibility::enable(const int64_t phoneHandle, const int64_t sipAccountHandle)
{
   getInstance(phoneHandle)->enable(sipAccountHandle);
}

void SipAccountCompatibility::disable(const int64_t phoneHandle, const int64_t sipAccountHandle)
{
   getInstance(phoneHandle)->disable(sipAccountHandle);
}

} // namepace SipAccount
}    // namepace CPCAPI2
} // namespace jsonrpc
#endif // CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE