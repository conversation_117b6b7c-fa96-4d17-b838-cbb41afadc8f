#pragma once

#if !defined(CPCAPI2_CONFERENCE_REGISTRAR_MANAGER_INTERFACE_H)
#define CPCAPI2_CONFERENCE_REGISTRAR_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"

#include "confbridge/ConferenceRegistrar.h"
#include "../phone/PhoneModule.h"
#include "phone/PhoneInterface.h"
#include "phone/Cpcapi2EventSource.h"

namespace CPCAPI2
{
namespace ConferenceBridge
{
class ConferenceRegistrarManagerImpl;

class ConferenceRegistrarSyncHandler {};

class ConferenceRegistrarManagerInterface : public ConferenceRegistrar,
   public PhoneModule,
   public CPCAPI2::EventSource<ConferenceRegistrarHandle, ConferenceRegistrarHandler, ConferenceRegistrarSyncHandler>
{
public:
   ConferenceRegistrarManagerInterface(Phone* phone);
   virtual ~ConferenceRegistrarManagerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   virtual int process(unsigned int timeout) OVERRIDE {
      return CPCAPI2::EventSource<ConferenceRegistrarHandle, ConferenceRegistrarH<PERSON><PERSON>, ConferenceRegistrarSyncHandler>::process(timeout);
   }
   virtual void interruptProcess() OVERRIDE {
      CPCAPI2::EventSource<ConferenceRegistrarHandle, ConferenceRegistrarHandler, ConferenceRegistrarSyncHandler>::interruptProcess();
   }
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE {
      CPCAPI2::EventSource<ConferenceRegistrarHandle, ConferenceRegistrarHandler, ConferenceRegistrarSyncHandler>::setCallbackHook(cbHook, context);
   }

   virtual int setExternalConferenceRegistrar(ConferenceRegistrar* confRegistrar) OVERRIDE;

   virtual void setHandler(ConferenceRegistrarHandler* handler) OVERRIDE;

   virtual int start(const ConferenceRegistrarConfig& confRegistrarConfig) OVERRIDE;
   virtual int shutdown() OVERRIDE;

   virtual int lookupConference(const cpc::string& joinUrl, LookupConferenceResultHandler* handler) OVERRIDE;
   virtual int listNodes(ListNodesHandler* handler) OVERRIDE;

   virtual bool isInService() OVERRIDE;

private:

private:
   PhoneInterface* mPhone;
   friend class ConferenceRegistrarManagerImpl;
   std::shared_ptr<ConferenceRegistrarManagerImpl> mImpl;
};
}
}

#endif
