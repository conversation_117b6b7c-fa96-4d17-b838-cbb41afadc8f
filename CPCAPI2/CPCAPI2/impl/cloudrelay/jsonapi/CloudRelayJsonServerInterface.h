#pragma once

#if !defined(CPCAPI2_CLOUD_RELAY_JSON_INTERFACE_H)
#define CPCAPI2_CLOUD_RELAY_JSON_INTERFACE_H

#include "interface/experimental/cloudrelay/CloudRelayJsonApi.h"
#include "interface/experimental/cloudrelay/CloudRelayManager.h"
#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "cloudrelay/CloudRelayHandler.h"
#include "cloudrelay/CloudRelaySyncHandler.h"
#include "jsonapi/JsonApiServerModule.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>

namespace CPCAPI2
{
class PhoneInterface;
namespace CloudRelay
{
class CloudRelayJsonServerInterface : public CPCAPI2::CloudRelay::CloudRelayHandler,
                                           public CPCAPI2::CloudRelay::CloudRelayJsonApi,
                                           public CPCAPI2::CloudRelay::CloudRelaySyncHand<PERSON>,
                                           public CPCAPI2::JsonApi::JsonApiServerModule,
                                           public CPCAPI2::PhoneModule
{
public:
   CloudRelayJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~CloudRelayJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE {
      return 0;
   }
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request, const std::shared_ptr<resip::Data>& binaryData) OVERRIDE;
   virtual int handleConnectionClosed(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn) OVERRIDE;

   // Inherited via CloudRelayHandler
   virtual int onBroadcast(CloudRelayHandle relay, const BroadcastEvent& args) OVERRIDE;
   virtual int onMessage(CloudRelayHandle relay, const MessageEvent& args) OVERRIDE;

private:
   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request, const std::shared_ptr<resip::Data>& binaryData);
   void handleConnectionClosedImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn);

   int handleSetHandler(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleGetRelay(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleBroadcast(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);
   int handleSendTo(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal, const std::shared_ptr<resip::Data>& binaryData);

private:
   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(CPCAPI2::JsonApi::JsonApiRequestInfo,const rapidjson::Value&,const std::shared_ptr<resip::Data>&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;
   CPCAPI2::CloudRelay::CloudRelayManager* mConfBridgeMgr;
};
}
}
#endif // CPCAPI2_CLOUD_RELAY_JSON_INTERFACE_H
