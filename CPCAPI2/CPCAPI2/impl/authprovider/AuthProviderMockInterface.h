#pragma once

#if !defined(CPCAPI2_AUTH_PROVIDER_MOCK_INTERFACE_H)
#define CPCAPI2_AUTH_PROVIDER_MOCK_INTERFACE_H

#include "cpcapi2defs.h"
#include "authprovider/mock/AuthProviderMock.h"
#include "phone/PhoneModule.h"

namespace CPCAPI2
{
class Phone;
class PhoneInterface;
class AuthProviderInterface;

namespace Auth
{


class AuthProviderMockInterface : public AuthProviderMock,
                                  public PhoneModule
{
public:
   AuthProviderMockInterface(AuthProviderInterface* authProvider);
   virtual ~AuthProviderMockInterface();
   virtual void Release() OVERRIDE;

   // AuthProviderMock
   virtual cpc::string providerName() OVERRIDE;
   virtual int login(const cpc::string& username) OVERRIDE;

private:
   AuthProviderInterface& mAuthProviderInterface;

};

}
}

#endif // CPCAPI2_AUTH_PROVIDER_MOCK_INTERFACE_H
