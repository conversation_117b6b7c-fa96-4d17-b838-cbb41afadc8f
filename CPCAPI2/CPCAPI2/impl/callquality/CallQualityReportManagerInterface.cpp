#include "brand_branded.h"

#if (CPCAPI2_BRAND_CALL_QUALITY_REPORT_MODULE == 1)
#include "CallQualityReportManagerInterface.h"
#include "CallQualityReporterImpl.h"
#include "callquality/CallQualityReportHandler.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::CALL_QUALITY

namespace CPCAPI2
{
namespace CallQuality
{
CallQualityReporterHandle CallQualityReporterHandleFactory::sNextHandle = 1;

CallQualityReportManagerInterface::CallQualityReportManagerInterface(Phone* phone)
   : EventSource<CallQualityReporterHandle, CallQualityReportHandler, CallQualityReporterSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
     mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   mPhone->addRefImpl();
}

CallQualityReportManagerInterface::~CallQualityReportManagerInterface()
{
   mPhone->releaseImpl();
}

void CallQualityReportManagerInterface::Release()
{
   mInstMap.clear();
   delete this;
}

CallQualityReporterHandle CallQualityReportManagerInterface::createCallQualityReporter()
{
   CallQualityReporterHandle h = CallQualityReporterHandleFactory::getNext();
   postToSdkThread(resip::resip_bind(&CallQualityReportManagerInterface::createCallQualityReporterImpl, this, h));
   return h;
}

void CallQualityReportManagerInterface::createCallQualityReporterImpl(CallQualityReporterHandle cqr)
{
   std::shared_ptr<CallQualityReporterImpl> pimpl(new CallQualityReporterImpl(this, cqr));
   mInstMap[cqr] = std::move(pimpl);
}

int CallQualityReportManagerInterface::setHandler(CallQualityReporterHandle cqr, CallQualityReportHandler* handler) 
{
   return setAppHandler(cqr, handler);
}

int CallQualityReportManagerInterface::configureCallQualityReporter(CallQualityReporterHandle cqr, const CallQualityReporterConfig& config)
{
   postToSdkThread(resip::resip_bind(&CallQualityReportManagerInterface::configureCallQualityReporterImpl, this, cqr, config));
   return kSuccess;
}

void CallQualityReportManagerInterface::configureCallQualityReporterImpl(CallQualityReporterHandle cqr, const CallQualityReporterConfig& config)
{
   CallQualityReportManagerInterface::InstanceMap::iterator it = mInstMap.find(cqr);
   if (it != mInstMap.end())
   {
      it->second->configureCallQualityReporter(config);
   }
}

int CallQualityReportManagerInterface::startCallQualityReporter(CallQualityReporterHandle cqr)
{
   postToSdkThread(resip::resip_bind(&CallQualityReportManagerInterface::startCallQualityReporterImpl, this, cqr));
   return kSuccess;
}

void CallQualityReportManagerInterface::startCallQualityReporterImpl(CallQualityReporterHandle cqr)
{
   CallQualityReportManagerInterface::InstanceMap::iterator it = mInstMap.find(cqr);
   if (it != mInstMap.end())
   {
      it->second->startCallQualityReporter();
   }
}

int CallQualityReportManagerInterface::stopCallQualityReporter(CallQualityReporterHandle cqr)
{
   postToSdkThread(resip::resip_bind(&CallQualityReportManagerInterface::stopCallQualityReporterImpl, this, cqr));
   return kSuccess;
}

void CallQualityReportManagerInterface::stopCallQualityReporterImpl(CallQualityReporterHandle cqr)
{
   CallQualityReportManagerInterface::InstanceMap::iterator it = mInstMap.find(cqr);
   if (it != mInstMap.end())
   {
      it->second->stopCallQualityReporter();
   }
}

void CallQualityReportManagerInterface::logEvent(CallQualityReporterHandle handle, const char* funcName, const char* eventName)
{
   InfoLog(<< "CallQualityReportHandler event callback: " << funcName << ", " << eventName);
}

}

}

#endif // CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE
