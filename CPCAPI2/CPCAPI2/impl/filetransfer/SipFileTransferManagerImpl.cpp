#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1)

#include <string>
#include <stdio.h>
#include <stdarg.h>

#include "SipFileTransferManagerImpl.h"
#include "SipFileTransferInfo.h"
#include "../util/IpHelpers.h"
#include "../util/cpc_logger.h"
#include "cpcapi2utils.h"
#include "../account/SipAccountImpl.h"

// resip includes
#include "resip/stack/SdpContents.hxx"
#include <resip/stack/Headers.hxx>
#include <resip/dum/ServerInviteSession.hxx>
#include <resip/dum/ClientInviteSession.hxx>
#include <rutil/DnsUtil.hxx>
#include <resip/stack/Tuple.hxx>

// MSRP stack includes
#include <utils/msrp_mem.h>
#include <utils/msrp_log.h>
#include <utils/msrp_string.h>
#include <utils/msrp_snprintf.h>
#include <msrp_stack.h>
#include <msrp_uri.h>
#include <msrp_session.h>
#include <msrp_message.h>
#include <msrp_content_type.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_FILETRANSFER

using namespace CPCAPI2::SipFileTransfer;

using resip::Data;
using resip::DataStream;
using resip::Timer;

// static logging function provided to the MSRP lib, to move the logs into the appropriate place
static void msrp_log_callback( msrp_log_level_enum_t level, ... )
{
   // todo: fix warning "passing an object that undergoes default argument promotion to 'va_start' has undefined behavior [-Wvarargs]"
   // va_list args;
   // const char *fmt;
   // static char result_string[ 4096 ];

   // va_start( args, level );
   // fmt = va_arg( args, char * );
   // result_string[ 0 ] = '\0';

   // // Create the string for printing
   // if( vsnprintf( result_string, 4096, fmt, args ) >= 4096 )
   // {
   //    // Add an indicator to show that it was truncated
   //    result_string[ 4092 ] = '.';
   //    result_string[ 4093 ] = '.';
   //    result_string[ 4094 ] = '.';
   //    result_string[ 4095 ] = '\0';
   // }
   // va_end( args );

   // switch( level )
   // {
   // case msrp_log_level_firehose_e:
   //    StackLog( << result_string );
   //    break;
   // case msrp_log_level_debug_e:
   //    DebugLog( << result_string );
   //    break;
   // case msrp_log_level_info_e:
   //    InfoLog( << result_string );
   //    break;
   // case msrp_log_level_warning_e:
   //    WarningLog( << result_string );
   //    break;
   // case msrp_log_level_error_e:
   //    ErrLog( << result_string );
   //    break;
   // case msrp_log_level_wtf_e:
   //    CritLog( << result_string );
   //    break;
   // default:
   //    break;
   // }
}

// struct passed through the msrp stack and back
struct SipFileTransferManagerImpl_CallbackInfo
{
   SipFileTransferManagerImpl *pThis;
   SipFileTransferHandle       fileTransfer;
   SipFileTransferItemHandle   fileTransferItem;
   unsigned short              last_percent;
};

static cpc::string parseFileName(const Data& fileSelectorStart)
{
   const char *cstr = fileSelectorStart.c_str(); // does this make a copy? docs unclear
   const char *tok  = NULL;
   char *tok_copy   = NULL;
   char *saveptr    = NULL;
   char *filename   = NULL;
   cpc::string result;

   // try to find "name:" in the selector
   tok = strstr( cstr, "name:" );
   if( tok == NULL )
      return result;

   // move past the "name:" part
   tok += 5;

   // The grammar guarantees that double quote should be percent-encoded. However
   // it also means that we need to percent decode the string after parsing it out
   tok_copy = strdup( tok );
   if( tok_copy == NULL )
      return result;

   // This has a problem if the name is "". Fortunately that name seems to be illegal
   // according to the RFC grammar (the name must contain something).
   tok = strtok_r( tok_copy, "\"", &saveptr );
   if( tok != NULL )
      filename = msrp_string_percent_decode( tok );

   if( filename != NULL )
      result = ( filename );

   msrp_safe_free(( void ** ) &filename );
   free( tok_copy );
   return result;
}

static uint64_t parseFileSize(const Data& fileSelectorStart)
{
   resip::ParseBuffer pb(fileSelectorStart);
   pb.skipToChars("size:");
   pb.skipN(5);
   return pb.uInt64();
}

static msrp_uri_t *createLocalMSRPURI( const char *ip_addr, unsigned short local_port )
{
   msrp_uri_t *p_local = NULL;
   char *p_sid         = NULL;
   char *p_local_uri   = NULL;
   char buf[ BUFSIZ ];

   if( ip_addr == NULL )
      return NULL;

   msrp_memset( buf, 0, sizeof( buf ));
   snprintf( buf, BUFSIZ, "%d", local_port );

   // Create our local msrp uri
   p_sid = msrp_stack_create_sid();
   msrp_string_append( &p_local_uri, "msrp://" );
   msrp_string_append( &p_local_uri, ip_addr );
   msrp_string_append( &p_local_uri, ":" );
   msrp_string_append( &p_local_uri, buf ); // port number
   msrp_string_append( &p_local_uri, "/" );
   msrp_string_append( &p_local_uri, p_sid );
   msrp_string_append( &p_local_uri, ";tcp" );

   p_local = msrp_uri_parse( p_local_uri, NULL ); // local

   msrp_safe_free(( void ** ) &p_sid );
   msrp_safe_free(( void ** ) &p_local_uri );
   return p_local;
}

// Thunk into the class
static void msrp_message_progress( msrp_message_t *p_message, intptr_t user_data, unsigned short percent )
{
   SipFileTransferManagerImpl_CallbackInfo *pInfo = ( SipFileTransferManagerImpl_CallbackInfo * ) user_data;
   if( pInfo == NULL || pInfo->pThis == NULL )
      return;

   // just report if the percent changed (sometimes if the file is big there could be multiple events
   // for the same percentage levels)
   if( percent != pInfo->last_percent )
   {
      pInfo->last_percent = percent;
      pInfo->pThis->fireProgress( pInfo->fileTransfer, pInfo->fileTransferItem, percent );
   }
}

static void msrp_message_send_complete( msrp_message_t *p_message, intptr_t user_data )
{
   SipFileTransferManagerImpl_CallbackInfo *pInfo = ( SipFileTransferManagerImpl_CallbackInfo * ) user_data;
   if( pInfo == NULL || pInfo->pThis == NULL )
      return;

   pInfo->pThis->itemSendComplete( pInfo->fileTransfer, pInfo->fileTransferItem, FileTransferItemEndReason_Complete );
}

static void msrp_message_cancelled( msrp_message_t *p_message, intptr_t user_data )
{
   SipFileTransferManagerImpl_CallbackInfo *pInfo = ( SipFileTransferManagerImpl_CallbackInfo * ) user_data;
   if( pInfo == NULL || pInfo->pThis == NULL )
      return;

   pInfo->pThis->itemSendComplete( pInfo->fileTransfer, pInfo->fileTransferItem, FileTransferItemEndReason_Interrupted );
}

static void msrp_message_destroyed( msrp_message_t *p_message, intptr_t user_data )
{
   SipFileTransferManagerImpl_CallbackInfo *pInfo = ( SipFileTransferManagerImpl_CallbackInfo * ) user_data;
   if( pInfo == NULL || p_message == NULL )
      return;

   delete pInfo;
   msrp_message_set_user_data( p_message, 0 );
}



SipFileTransferManagerImpl::SipFileTransferManagerImpl(
   SipFileTransferManagerInterface* iff,
   std::shared_ptr<AccountMap> parentMap,
   CPCAPI2::PhoneInterface* cpcPhone, 
   CPCAPI2::SipAccount::SipAccountImpl& account)
   : mInterface(iff),
     mAccount(account),
     mStack(NULL),
     is_msrp_lib_initialized(false),
     mPhone(cpcPhone),
     mParentMap(parentMap)
{
   mAccount.registerAccountAwareFeature(this);
}

SipFileTransferManagerImpl::~SipFileTransferManagerImpl()
{
   mStack = NULL;
   mAccount.unregisterAccountAwareFeature(this);
}

void SipFileTransferManagerImpl::release()
{
   if (std::shared_ptr<AccountMap> parentMap = mParentMap.lock())
   {
      parentMap->erase(mAccount.getHandle());
   }

   msrp_stack_destroy(mStack);
   mPhone->getSdkModuleThread().unregisterEventHandler(this);
   mPhone->getSdkModuleThread().safeDelete(this); // will delete this
}

int SipFileTransferManagerImpl::adornMasterProfile( resip::SharedPtr<resip::MasterProfile>& profile )
{
   if (!profile->isMethodSupported(resip::INVITE)) profile->addSupportedMethod( resip::INVITE );
   if (!profile->isMethodSupported(resip::ACK)) profile->addSupportedMethod( resip::ACK );
   if (!profile->isMethodSupported(resip::CANCEL)) profile->addSupportedMethod( resip::CANCEL );
   if (!profile->isMethodSupported(resip::BYE)) profile->addSupportedMethod( resip::BYE );
   return kSuccess;
}

int SipFileTransferManagerImpl::registerSdkDialogSetFactory( CPCAPI2::SipAccount::AppDialogSetFactory& factory )
{
   class MyAppDialogFactoryDelegate : public CPCAPI2::SipAccount::AppDialogFactoryDelegate
   {
   public:
      MyAppDialogFactoryDelegate( SipFileTransferManagerImpl *pParent )
         : mParent( pParent ) {}

      virtual bool isMyMessage( const resip::SipMessage& msg )
      {
         const SdpContents* sdp = dynamic_cast<const SdpContents*>(msg.getContents());
         if (sdp)
         {
            std::list<SdpContents::Session::Medium>::const_iterator it = sdp->session().media().begin();
            for (; it != sdp->session().media().end(); it++)
            {
               if (it->exists("file-selector"))
                  return true;
            }
         }
         return false;
      }

      virtual resip::AppDialogSet* createAppDialogSet(resip::DialogUsageManager& dum, const resip::SipMessage& msg)
      {
         // the checks are done in the isMyMessage method above
         SipFileTransferHandle sessionHandle = sNextFileTransferHandle++;
         return new SipFileTransferInfo( sessionHandle, mParent->mDum, mParent );
      }
   private:
      SipFileTransferManagerImpl *mParent;
   };

   std::shared_ptr< MyAppDialogFactoryDelegate > pTemp( new MyAppDialogFactoryDelegate( this ));
   factory.addDelegate( pTemp );
   return kSuccess;
}

int SipFileTransferManagerImpl::addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig)
{
   // Remember the dum instance
   mDum = dum;

   return kSuccess;
}

int SipFileTransferManagerImpl::onDumBeingDestroyed()
{
   // DUM is going away. This is a problem because we need it! Terminate all of our known
   // MSRP sessions, free the memory, clean up, etc. Don't fire any events here since
   // we're taking evasive action.

   // Loop over all known file transfers
   SipFileTransferInfoMap::const_iterator infoIter = mFileTransferInfo.begin();
   for( ; infoIter != mFileTransferInfo.end() ; ++infoIter )
   {
      // Loop over all known file transfer items
      SipFileTransferInfo *pInfo( infoIter->second );

      SipFileTransferItemInfoMap::const_iterator itemIter = pInfo->transferItems.begin();
      for( ; itemIter != infoIter->second->transferItems.end() ; ++itemIter )
      {
         SipFileTransferItemInfoWeakPtr pWeak = itemIter->second;
         SipFileTransferItemInfoPtr pItem = pWeak.lock();
         if( pItem != NULL )
            removeFileTransferItemInfo( itemIter->first );
      }
      pInfo->transferItems.clear(); // erase the weak ptr collection

      // Destroy the msrp sessions now
      if( msrp_list_size( pInfo->m_pSessions ) > 0 )
      {
         msrp_session_t *p_session = NULL;
         msrp_list_enum_t *p_enum  = NULL;

         p_enum = msrp_list_enum_create( pInfo->m_pSessions );
         while( msrp_list_enum_next( p_enum, ( void ** ) &p_session ))
         {
            msrp_stack_session_destroy( mStack, p_session );
         }
         msrp_list_enum_destroy( p_enum );
         msrp_list_empty( pInfo->m_pSessions );
      }
   }
   mFileTransferInfo.clear(); // triggers all dtors and cleans up file maps

   mDum.reset();
   return kSuccess;
}

int SipFileTransferManagerImpl::onRegistrationSuccess(const SipAccount::SipRegistrationSuccessEvent& args)
{
   mRegServer = args.server;
   return kSuccess;
}

SipFileTransferInfo *SipFileTransferManagerImpl::getFileTransferInfo( const SipFileTransferHandle& h ) const
{
   SipFileTransferInfoMap::const_iterator it = mFileTransferInfo.find( h );
   if( it != mFileTransferInfo.end() )
      return it->second;

   return NULL;
}

void SipFileTransferManagerImpl::setFileTransferInfo( const SipFileTransferHandle& h, SipFileTransferInfo * pInfo )
{
   assert( mFileTransferInfo[ h ] == NULL || mFileTransferInfo[ h ] == pInfo );
   mFileTransferInfo[ h ] = pInfo;
}

void SipFileTransferManagerImpl::removeFileTransferInfo( const SipFileTransferHandle& h )
{
   mFileTransferInfo.erase( h );
}

SipFileTransferItemInfoPtr SipFileTransferManagerImpl::getFileTransferItemInfo( const SipFileTransferItemHandle& ih ) const
{
   SipFileTransferItemInfoPtr result;
   auto it = mFileTransferItemInfo.find( ih );
   if( it != mFileTransferItemInfo.end() )
      result = it->second;

   return result;
}

void SipFileTransferManagerImpl::setFileTransferItemInfo( const SipFileTransferItemHandle& ih, SipFileTransferItemInfoPtr ci )
{
   assert( mFileTransferItemInfo[ ih ] == NULL || mFileTransferItemInfo[ ih ] == ci );
   mFileTransferItemInfo[ ih ] = ci;
}

void SipFileTransferManagerImpl::removeFileTransferItemInfo( const SipFileTransferItemHandle& ih )
{
   mFileTransferItemInfo.erase( ih );
}

Data SipFileTransferManagerImpl::makeFileTransferSdp( const SipFileTransferHandle& h, const Data& direction, UInt32 localMediaPort, Data remoteAddr )
{
   UInt64 currentTime = Timer::getTimeMicroSec();
   Data localAddr = IpHelpers::getPreferredLocalIpAddress();
   if (!mRegServer.isAnyInterface())
   {
      IpHelpers::getPreferredLocalIpAddress(mRegServer, localAddr);
   }

   Data sdpStr;
   {
      DataStream ds(sdpStr);
      ds << "v=0\r\n" <<
            "o=BriaFileTransfer " << currentTime << " "
                                  << currentTime << " "
                                  << "IN " << ( resip::DnsUtil::isIpV6Address( localAddr ) ? "IP6" : "IP4" ) << " "
                                  << localAddr << "\r\n" <<
            "s= \r\n" <<
            "t=0 0\r\n";

      SipFileTransferInfo *pInfo = mFileTransferInfo[ h ];
      if( pInfo != NULL )
      {
         for( SipFileTransferItemInfoMap::iterator it = pInfo->transferItems.begin() ; it != pInfo->transferItems.end() ; ++it )
         {
            SipFileTransferItemInfoWeakPtr pWeak = it->second;
            SipFileTransferItemInfoPtr pItemInfo = pWeak.lock();
            if( pItemInfo == NULL )
               continue;

            char *file_transfer_id = NULL;
            int port = 0;

            if( pItemInfo->detail.isIncoming && ( pItemInfo->detail.acceptedState == ftitem_accepted ))
               port = localMediaPort;
            else if( !pItemInfo->detail.isIncoming )
               port = localMediaPort;

            ds << "m=message " << port << " TCP/MSRP *\r\n";
            if( port > 0 )
            {
               Data localMediaAddr( localAddr ); // addr for media specific parts

               // Set the connection c= line for this media line
               ds << "c=IN " << ( resip::DnsUtil::isIpV6Address( localMediaAddr ) ? "IP6 " : "IP4 " ) << localMediaAddr << "\r\n";

               // Create (and remember) the local MSRP URI for this item if none is
               // supplied. This isn't always the case as when a file is accepted, the URI
               // will be set already.
               if( pItemInfo->m_pLocal == NULL )
                  pItemInfo->m_pLocal = createLocalMSRPURI( localMediaAddr.c_str(), mListeningPort );

               char *p_local_str = msrp_uri_to_string( pItemInfo->m_pLocal );

               ds << "a=accept-types:message/cpim\r\n" <<
                     "a=path:" << p_local_str << "\r\n" <<
                     "a=" << direction << "\r\n";

               msrp_safe_free(( void ** ) &p_local_str );
            }

            file_transfer_id = msrp_string_new_random( 128 ); // 128 bits works out to 32 chars

            // TODO: we need to track the file transfer IDs
            // TODO: is this application/octet, or application/octet-stream ?!?
            // TODO: the mime type and subtype should be carried in the item info

            std::string fileName( ( pItemInfo->detail.remotefileName ));
            ds << "a=file-selector:name:\"" << fileName << "\" type:application/octet-stream size:" << pItemInfo->detail.fileSizeBytes << "\r\n" <<
                  "a=file-transfer-id:" << file_transfer_id << "\r\n";

            msrp_safe_free(( void ** ) &file_transfer_id );

            // Be explicit about this
            ds << "a=setup:" << ( pInfo->m_isActive ? "active" : "passive" ) << "\r\n" <<
                  "a=connection:new\r\n";
         }
      }
   }

   return sdpStr;
}

void SipFileTransferManagerImpl::end( const SipFileTransferHandle& fileTransfer )
{
   SipFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if( pInfo == NULL )
      return;

   // Check to see if DUM was destroyed. This can happen on forced shutdown.
   // If DUM has been destroyed, then checking session handles for validity
   // results in a crash. In this case everything should have already been
   // cleaned up in the onDumBeingDestroyed callback.
   if( mDum.get() != NULL )
   {
      if( pInfo->clientInviteSessionHandle.isValid() )
         pInfo->clientInviteSessionHandle->end();
      if( pInfo->serverInviteSessionHandle.isValid() )
         pInfo->serverInviteSessionHandle->end( resip::InviteSession::NotSpecified );

      // NB: onTerminated will be called, and most actions/events are fired
      // in that code
   }
}

void SipFileTransferManagerImpl::fireError( const SipFileTransferHandle& h, const cpc::string& message )
{
   ErrorEvent event;
   event.errorText = message;
   mInterface->fireEvent(cpcEvent(SipFileTransferHandler, onError), h, event);
}

void SipFileTransferManagerImpl::fireProgress( const SipFileTransferHandle& h, const SipFileTransferItemHandle& ih, const unsigned short& percent )
{
   FileTransferItemProgressEvent progressEvent;
   progressEvent.fileTransferItem = ih;
   progressEvent.percent          = percent;
   mInterface->fireEvent(cpcEvent(SipFileTransferHandler, onFileTransferItemProgress), h, progressEvent);
}

void SipFileTransferManagerImpl::fireEnded( const SipFileTransferHandle& h, FileTransferEndedEvent evt )
{
   mInterface->fireEvent(cpcEvent(SipFileTransferHandler, onFileTransferEnded), h, evt);
}

void SipFileTransferManagerImpl::fireItemEnded( const SipFileTransferHandle& h, const SipFileTransferItemHandle& ih, const FileTransferItemEndReason endReason )
{
   FileTransferItemEndedEvent evt;
   evt.fileTransferItem = ih;
   evt.endReason = endReason;
   mInterface->fireEvent(cpcEvent(SipFileTransferHandler, onFileTransferItemEnded), h, evt);
}

void SipFileTransferManagerImpl::fireNewTransfer( const SipFileTransferHandle& h, const NewFileTransferEvent& evt )
{
   mInterface->fireEvent(cpcEvent(SipFileTransferHandler, onNewFileTransfer), h, evt);
}

void SipFileTransferManagerImpl::fireConfigured( const SipFileTransferHandle& h, const FileTransferConfiguredEvent& evt )
{
   mInterface->fireEvent(cpcEvent(SipFileTransferHandler, onFileTransferConfigured), h, evt);
}

void SipFileTransferManagerImpl::onNewSession( ClientInviteSessionHandle cish, InviteSession::OfferAnswerType oat, const SipMessage& msg )
{
   SipFileTransferInfo *pInfo = dynamic_cast< SipFileTransferInfo* >( cish->getAppDialogSet().get() );
   if( pInfo == NULL )
      return;

   assert( pInfo->handle != 0 ); // should be set elsewhere

   pInfo->clientInviteSessionHandle = cish;
   pInfo->accountHandle             = mAccount.getHandle();
   pInfo->targetAddress             = resip::NameAddr( msg.header( resip::h_To ).uri() );
   pInfo->m_isActive                = true; // If we make the call, active is initially assumed

   if( !msg.header( resip::h_From ).uri().getAor().empty() )
      pInfo->localSIPURI = cpc::string("sip:") + ( msg.header( resip::h_From ).uri().getAor().c_str() );

   if( !msg.header( resip::h_To ).uri().getAor().empty() )
      pInfo->remoteSIPURI = cpc::string("sip:") + ( msg.header( resip::h_To ).uri().getAor().c_str() );

   // Add the info now
   setFileTransferInfo( pInfo->handle, pInfo );

   // On the outbound client invite session handle version of this method, we fire the new file transfer event
   NewFileTransferEvent nfte;
   nfte.account           = pInfo->accountHandle;
   nfte.fileTransferState = FileTransferState_LocalOriginated;
   nfte.fileTransferType  = FileTransferType_Outgoing;
   nfte.remoteAddress     = pInfo->remoteSIPURI;
   nfte.remoteDisplayName = ""; // FIXME
   fireNewTransfer( pInfo->handle, nfte );

}

void SipFileTransferManagerImpl::onNewSession( ServerInviteSessionHandle sish, InviteSession::OfferAnswerType oat, const SipMessage& msg )
{
   SipFileTransferInfo* pInfo = dynamic_cast< SipFileTransferInfo* >( sish->getAppDialogSet().get() );
   if( pInfo == NULL )
      return;

   assert( pInfo->handle != 0 ); // should be set elsewhere

   pInfo->serverInviteSessionHandle = sish;
   pInfo->accountHandle             = mAccount.getHandle();
   pInfo->targetAddress             = resip::NameAddr( msg.header( resip::h_To ).uri() );;
   pInfo->m_isActive                = false; // If we receive the call, passive is initially assumed

   if( !msg.header( resip::h_To ).uri().getAor().empty() )
      pInfo->localSIPURI = cpc::string("sip:") + ( msg.header( resip::h_To ).uri().getAor().c_str() );

   if( !msg.header( resip::h_From ).uri().getAor().empty() )
      pInfo->remoteSIPURI = cpc::string("sip:") + ( msg.header( resip::h_From ).uri().getAor().c_str() );

   // Add the info now
   setFileTransferInfo( pInfo->handle, pInfo );

   // In the inbound server invite session handle version of this method, we fire the new file transfer event
   // later, in the onOffer method (followed by the state change)
}

void SipFileTransferManagerImpl::onTerminated( InviteSessionHandle ish, InviteSessionHandler::TerminatedReason reason, const SipMessage* related )
{
   SipFileTransferInfo *ads = dynamic_cast< SipFileTransferInfo* >( ish->getAppDialogSet().get() );
   if( ads == NULL )
      return;

   FileTransferEndedEvent evt;
   evt.fileTransferState   = FileTransferState_Ended;
   evt.signallingEndEvent  = "";
   evt.sipResponseCode     = 0;
   evt.signallingEndReason = "";

   if( related != NULL )
   {
      if( related->isResponse() )
         evt.sipResponseCode = related->const_header(resip::h_StatusLine).statusCode();

      evt.signallingEndReason = related->getReason() != NULL ? (related->getReason()->c_str()) : "";
   }

   evt.endReason = FileTransferEndReason_Unknown;
   switch (reason)
   {
   case InviteSessionHandler::TerminatedReason::LocalBye:
   case InviteSessionHandler::TerminatedReason::LocalCancel:
      evt.endReason = FileTransferEndReason_UserTerminatedLocally;
      break;
   case InviteSessionHandler::TerminatedReason::RemoteBye:
   case InviteSessionHandler::TerminatedReason::RemoteCancel:
      evt.endReason = FileTransferEndReason_UserTerminatedRemotely;
      break;
   case InviteSessionHandler::TerminatedReason::Rejected:
      evt.endReason = FileTransferEndReason_ServerRejected;
      break;
   case InviteSessionHandler::TerminatedReason::Error:
      evt.endReason = FileTransferEndReason_ServerError;
      break;
   default:
      break;
   }

   // terminate each file transfer item remaining (if any)
   for( SipFileTransferItemInfoMap::iterator it2 = ads->transferItems.begin() ; it2 != ads->transferItems.end() ; ++it2 )
   {
      SipFileTransferItemInfoWeakPtr pWeak = it2->second;
      SipFileTransferItemInfoPtr pItem = pWeak.lock();
      if( pItem != NULL )
      {
         fireItemEnded( ads->handle, it2->first, FileTransferItemEndReason_Interrupted );
         removeFileTransferItemInfo( it2->first );
      }
   }
   ads->transferItems.clear(); // erase the weak ptr collection

   // terminate the whole transfer
   fireEnded( ads->handle, evt );

   // Also remove the info from the manager for the file transfer (this should trigger
   // it dtor and therefore free the file map as well)
   removeFileTransferInfo( ads->handle );

   // Destroy the msrp sessions now
   if( msrp_list_size( ads->m_pSessions ) > 0 )
   {
      msrp_session_t *p_session = NULL;
      msrp_list_enum_t *p_enum  = NULL;

      p_enum = msrp_list_enum_create( ads->m_pSessions );
      while( msrp_list_enum_next( p_enum, ( void ** ) &p_session ))
      {
         msrp_stack_session_destroy( mStack, p_session );
      }
      msrp_list_enum_destroy( p_enum );
      msrp_list_empty( ads->m_pSessions );
   }
}

void SipFileTransferManagerImpl::itemSendComplete( const SipFileTransferHandle& h, const SipFileTransferItemHandle& ih, FileTransferItemEndReason reason )
{
   SipFileTransferInfo *pInfo = getFileTransferInfo( h );
   if( pInfo == NULL )
      return;

   SipFileTransferItemInfoMap::const_iterator it = pInfo->transferItems.find( ih );
   if( it != pInfo->transferItems.end() )
   {
      pInfo->transferItems.erase( it ); // remove from weak_ptr collection
      removeFileTransferItemInfo( ih ); // remove from shared_ptr collection
      fireItemEnded( h, ih, reason );   // fire event
   }

   // TODO: check if there are no more file transfer items, then automatically END the file transfer.
   if( pInfo->transferItems.size() == 0 )
   {
      // The end method needs to be invoked asynchronously in order to avoid problems
      // deleting the msrp session during the main msrp loop.
      mInterface->postToSdkThread( resip::resip_bind( &SipFileTransferManagerImpl::end, this, h ));
   }
}

// we receive an "answer" from the remote side. start the file transfers since we should have all information now.
void SipFileTransferManagerImpl::onAnswer( InviteSessionHandle ish, const SipMessage& msg, const SdpContents& contents )
{
   SipFileTransferInfo *ads = dynamic_cast< SipFileTransferInfo* >( ish->getAppDialogSet().get() );
   if( ads == NULL )
      return;

   if( contents.session().media().empty() )
      return;

   SipFileTransferItems items;
   SipFileTransferItemInfoMap::iterator itItems = ads->transferItems.begin();
   resip::SdpContents::Session::MediumContainer::const_iterator itMedia = contents.session().media().begin();
   for( ; itItems != ads->transferItems.end() ; ++itItems )
   {
      // Skip the transfer item if it's not pointing to anything
      SipFileTransferItemInfoWeakPtr pWeak = itItems->second;
      SipFileTransferItemInfoPtr pItem = pWeak.lock();
      if( pItem == NULL )
         continue;

      // Skip any media which have no file selector
      if( !itMedia->exists( "file-selector" ))
      {
         ++itMedia;
         continue;
      }

      const std::list<Data>& fileSelectorList = itMedia->getValues( "file-selector" );
      const std::list<Data>& setupList = itMedia->getValues( "setup" ); // might be empty

      // If the remote side is active, then we should be passive.
      // If the remote side is anything but active, we assume active.
      if( setupList.size() > 0 )
         ads->m_isActive = ( strcasecmp( "active", setupList.front().c_str() ) != 0 );

      if( itMedia->port() != 0 && fileSelectorList.size() > 0 )
      {
         cpc::string fileName     = parseFileName( fileSelectorList.front() );
         uint64_t     fileSize     = parseFileSize( fileSelectorList.front() );
         msrp_message_t *p_message = NULL;

         // these should match (not sure if they should be real ASSERTS though)
         assert( pItem->detail.localfileName.size() > 0 );
         assert( pItem->detail.remotefileName == fileName );
         assert( pItem->detail.fileSizeBytes == fileSize );
         assert( pItem->detail.acceptedState != ftitem_accepted );
         assert( pItem->m_pLocal != NULL );
         assert( pItem->m_pRemote == NULL );

         // Mark the item as having been accepted by the remote side
         // (N.B. it could have been rejected already by the sender)
         if( pItem->detail.acceptedState == ftitem_notprocessed )
            pItem->detail.acceptedState = ftitem_accepted;

         if( !FileMap::FileExists( pItem->detail.localfileName, pItem->detail.localfilePath ) ||
             !FileMap::IsRegularFile( pItem->detail.localfileName, pItem->detail.localfilePath ))
            continue;

         // might be empty (hopefully not, 'cause that would just be wrong)
         const std::list<Data>& pathList = itMedia->getValues( "path" );
         if( pathList.size() > 0 )
         {
            msrp_session_t *p_session = NULL;

            pItem->m_pRemote = msrp_uri_parse( pathList.front().c_str(), NULL );

            // .jjg. fix for SBCs that don't understand the MSRP-specific parts of a SDP,
            std::list< resip::SdpContents::Session::Connection > connections = itMedia->getConnections();
            if( connections.size() > 0 )
            {
               Data addressFromCLine = connections.front().getAddress();
               if( addressFromCLine.size() > 0 && 
                     strcmp( pItem->m_pRemote->p_host, addressFromCLine.c_str() ) != 0 )
               {
                  msrp_safe_free(( void ** ) &pItem->m_pRemote->p_host );
                  pItem->m_pRemote->p_host = strdup( addressFromCLine.c_str()) ;
               }
            }

            const int remote_port = atoi( pItem->m_pRemote->p_port );
            if( remote_port != itMedia->port() )
            {
               char temp[ 16 ];
               snprintf(temp, 16, "%d", std::min< unsigned short >( USHRT_MAX, itMedia->port() ));
               msrp_safe_free(( void ** ) &pItem->m_pRemote->p_port );
               pItem->m_pRemote->p_port = strdup( temp );
            }

            // Create the MSRP session
            p_session = msrp_stack_session_create( mStack, pItem->m_pLocal, pItem->m_pRemote, ads->m_isActive );
            msrp_list_push( ads->m_pSessions, p_session );

            // set up the message
            p_message = msrp_message_create( msrp_stack_create_mid(), pItem->detail.fileSizeBytes, MSRP_FALSE );
            msrp_message_set_content_type( p_message, msrp_content_type_create_from_strings( "application", "octet-stream" ));

            msrp_message_callbacks_t callbacks;
            msrp_memset( &callbacks, 0, sizeof( msrp_message_callbacks_t ));
            callbacks.p_message_progress_func      = msrp_message_progress;
            callbacks.p_message_cancelled_func     = msrp_message_cancelled;
            callbacks.p_message_destroyed_func     = msrp_message_destroyed;
            callbacks.p_message_send_complete_func = msrp_message_send_complete;
            msrp_message_install_callbacks( p_message, &callbacks );

            SipFileTransferManagerImpl_CallbackInfo *pInfo     = new SipFileTransferManagerImpl_CallbackInfo; // deallocated in destroyed cbk
            pInfo->pThis            = this;
            pInfo->fileTransfer     = ads->handle;
            pInfo->fileTransferItem = pItem->detail.handle;
            pInfo->last_percent     = 0;
            msrp_message_set_user_data( p_message, ( intptr_t ) pInfo );
               
            // Set the backing store on the outbound message
            pItem->m_Map = new FileMap( pItem->detail.localfileName, pItem->detail.localfilePath );
            if( pItem->m_Map->IsValid() )
            {
               // Set backing store and start the message sending
               pItem->m_pMessage = p_message;
               msrp_message_set_backing_store( p_message, pItem->m_Map->GetData() );
               msrp_session_message_send( p_session, p_message );

               // It might have been rejected before we got the SDP Answer, in that case we need
               // to cancel the msrp message now (we need to tell the remote side)
               if( pItem->detail.acceptedState == ftitem_rejected )
                  msrp_message_cancel( p_message );

               items.push_back( pItem->detail );
            }
         }
      }
      ++itMedia;
   }

   FileTransferConfiguredEvent ftce;
   ftce.fileTransferState = FileTransferState_Connected;
   ftce.fileTransferType  = FileTransferType_Outgoing;
   ftce.fileItems         = items;
   fireConfigured( ads->handle, ftce );
}

// we receive an offer from the remote side. File transfer must first be accepted by the
// user before it can proceed.
void SipFileTransferManagerImpl::onOffer( InviteSessionHandle ish, const SipMessage& msg, const SdpContents& contents )
{
   SipFileTransferInfo* pInfo = dynamic_cast<SipFileTransferInfo*>(ish->getAppDialogSet().get());
   if( pInfo == NULL )
      return;

   // Fire the events to the upper layer now
   NewFileTransferEvent nfte;
   nfte.account           = pInfo->accountHandle;
   nfte.fileTransferType  = FileTransferType_Incoming;
   nfte.fileTransferState = FileTransferState_RemoteOriginated;
   nfte.remoteAddress     = pInfo->remoteSIPURI;
   nfte.remoteDisplayName = ""; // FIXME
   fireNewTransfer( pInfo->handle, nfte );

   SipFileTransferItems items;
   resip::SdpContents::Session::MediumContainer::const_iterator it = contents.session().media().begin();
   for (; it != contents.session().media().end(); ++it)
   {
      if( !it->exists( "file-selector" ))
         continue;

      const std::list< Data >& fileSelectorList = it->getValues("file-selector");
      if( fileSelectorList.size() <= 0 )
         continue;

      const std::list<Data>& setupList = it->getValues( "setup" ); // might be empty

      // If the remote side is active, then we should be passive.
      // If the remote side is anything but active, we assume active.
      if( setupList.size() > 0 )
         pInfo->m_isActive = ( strcasecmp( "active", setupList.front().c_str() ) != 0 );

      cpc::string fileName = parseFileName( fileSelectorList.front() );
      uint64_t     fileSize = parseFileSize( fileSelectorList.front() );

      // Get the remote IP from the SDP
      const char *remote_uri_str = it->getValues("path").front().c_str();

      // Create the item info and add it
      SipFileTransferItemInfoPtr pItem( new SipFileTransferItemInfo( sNextFileTransferItemHandle++ ));
      pItem->detail.remotefileName = fileName;
      pItem->detail.localfileName  = fileName; // Initial suggestion
      pItem->detail.localfilePath  = ".";     // Initial suggestion
      pItem->detail.fileSizeBytes  = fileSize;
      pItem->detail.acceptedState  = ftitem_notprocessed;
      pItem->detail.isIncoming     = true;
      pItem->m_pRemote             = msrp_uri_parse( remote_uri_str, NULL );

      Data localAddr = IpHelpers::getPreferredLocalIpAddress();
      if (!mRegServer.isAnyInterface())
      {
         IpHelpers::getPreferredLocalIpAddress(mRegServer, localAddr);
      }
      pItem->m_pLocal = createLocalMSRPURI( localAddr.c_str(), mListeningPort );

      items.push_back( pItem->detail );

      // Add the item into the transfer as a weak pointer. Also remember it in
      // the manager's collection as a shared pointer.
      pInfo->transferItems[ pItem->detail.handle ] = pItem;
      setFileTransferItemInfo( pItem->detail.handle, pItem );

      if( it->exists( "sendrecv" ) || !it->exists( "sendonly" ))
         pInfo->m_OfferedAsSendRecv = true;
   }

   // Fire the configured event
   FileTransferConfiguredEvent ftce;
   ftce.fileTransferState = FileTransferState_Connected;
   ftce.fileTransferType  = FileTransferType_Incoming;
   ftce.fileItems         = items;
   fireConfigured( pInfo->handle, ftce );
}

void SipFileTransferManagerImpl::onOfferRequired(InviteSessionHandle ish, const SipMessage& msg)
{
   ish->reject( 488 );
}

void SipFileTransferManagerImpl::onInfo(InviteSessionHandle ish, const SipMessage& msg)
{
   // Genband freaking has a bloody INFO message used as a session keep-alive.
   ish->acceptNIT();
}

void SipFileTransferManagerImpl::process(resip::ReactorEventHandler::FdSetType& fdset)
{
   if( !is_msrp_lib_initialized )
   {
      // calls WSAInitialize on windows, best to do this in the same thread that will do
      // all the processing.
      msrp_lib_init( msrp_log_callback );

      mStack = msrp_stack_create();
      mListeningPort = msrp_stack_get_free_port( mStack, "tcp" );
      msrp_stack_set_listening_port( mStack, mListeningPort );
      is_msrp_lib_initialized = true;
   }

   // pump the msrp stack
   msrp_stack_process( mStack );
}

void SipFileTransferManagerImpl::buildFdSet(resip::ReactorEventHandler::FdSetType& fdset)
{
   // NB: this method is called BEFORE process.
   if( !is_msrp_lib_initialized)
      return;

   fd_set readset, writeset, exceptset;
   msrp_list_t *p_allfds = NULL;
   msrp_list_enum_t *p_enum = NULL;
   msrp_socket_t cur = 0;

   // make sure the fd_sets are zeroed
   FD_ZERO( &readset );
   FD_ZERO( &writeset );
   FD_ZERO( &exceptset );

   // get the file descriptors from the msrp stack
   p_allfds = msrp_list_create();
   msrp_stack_get_fd_sets( mStack, p_allfds, &readset, &writeset, &exceptset );

   // fill the parameter "fdset" from the results. As it turns out
   // a "resip::Socket" and an msrp_socket_t are the same thing.
   p_enum = msrp_list_enum_create( p_allfds );
   while( msrp_list_enum_next( p_enum, ( void ** ) &cur ))
   {
      if( FD_ISSET( cur, &readset ))
         fdset.setRead( cur );

      if( FD_ISSET( cur, &writeset ))
         fdset.setWrite( cur );

      if( FD_ISSET( cur, &exceptset  ))
         fdset.setExcept( cur );
   }
   msrp_list_enum_destroy( p_enum );
   msrp_list_destroy(p_allfds);
}

unsigned int SipFileTransferManagerImpl::getTimeTillNextProcessMS()
{
   time_t seconds = msrp_stack_get_min_timeout_seconds( mStack );
   return ( unsigned int )( seconds * 1000 );
}


#endif
