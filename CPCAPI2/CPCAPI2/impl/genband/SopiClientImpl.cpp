#include "brand_branded.h"

#if (CPCAPI2_BRAND_GENBAND_SOPI_MODULE == 1)

#include "SopiClientImpl.h"
#include "genband/GenbandSopiHandler.h"

#include "../util/TimeUtils.h"
#include "../util/DumFpCommand.h"

// gSoap config
#define WITH_OPENSSL
#define WITH_NONAMESPACES

#include "SopiHelper.h"

#define PAB_SERVICE_NAME        "AddressBookUserService"
#define PAB_GROUP_SERVICE_NAME  "AddressBookGroupUserService"
#define GAB_SERVICE_NAME        "GABUserService"
#define PAU_SERVICE_NAME        "PresenceAuthorizationUserService"

#ifdef ANDROID
#include <android/log.h>

#include <resip/stack/ssl/counterpath/AndroidSecurity.hxx>
extern int (*fsslverify_android)(int, X509_STORE_CTX*);

#endif

namespace CPCAPI2
{

namespace GenbandSopi
{

SopiClientImpl::SopiClientImpl(GenbandSopiClientHandle handle, const GenbandSopiClientSettings& settings, GenbandSopiManagerInterface& itf) :
   mClientHandle(handle),
   mInterface(itf),
   mUsername(settings.username),
   mPassword(settings.password),
   mPabSrvProxy(NULL),
   mGrpSrvProxy(NULL),
   mGabSrvProxy(NULL),
   mPAUSrvProxy(NULL)
{
   mPabSoapEndpoint = settings.serverUrl + PAB_SERVICE_NAME;
   mPabGroupSoapEndpoint = settings.serverUrl + PAB_GROUP_SERVICE_NAME;
   mGabSoapEndpoint = settings.serverUrl + GAB_SERVICE_NAME;
   mPAUSoapEndpoint = settings.serverUrl + PAU_SERVICE_NAME;
   mIgnoreCertificatValidation = settings.ignoreCertValidation;
   mUserAgent = settings.userAgent;
}

SopiClientImpl::~SopiClientImpl()
{
   if (mPabSrvProxy)
      mPabSrvProxy->soap_force_close_socket();
   delete mPabSrvProxy;
   mPabSrvProxy = NULL;

   if (mGrpSrvProxy)
      mGrpSrvProxy->soap_force_close_socket();
   delete mGrpSrvProxy;
   mGrpSrvProxy = NULL;

   if (mGabSrvProxy)
      mGabSrvProxy->soap_force_close_socket();
   delete mGabSrvProxy;
   mGabSrvProxy = NULL;
   
   if (mPAUSrvProxy)
      mPAUSrvProxy->soap_force_close_socket();
   delete mPAUSrvProxy;
   mPAUSrvProxy = NULL;
   
   for (auto it = mSoapMap.begin(); it != mSoapMap.end(); ++it)
   {
      soap_free(it->second);
      it->second = NULL;
   }
}

void SopiClientImpl::setUserNameAndPassword(SopiServiceProxyType proxy_type)
{
    SoapMap::iterator it = mSoapMap.find(proxy_type);
    soap* _soap = (it == mSoapMap.end() ? NULL : it->second);
    if(_soap)
    {
       _soap->userid=mUsername.c_str();
       _soap->passwd=mPassword.c_str();

       if(mUserAgent.size()!=0)
         _soap->useragent=mUserAgent.c_str();
       else
         _soap->useragent=NULL;
    }
}

static int64_t secondsSinceStart()
{
   return TimeUtils::millisSinceUnixEpoch() / 1000;
}

void SopiClientImpl::initServiceProxy(SopiServiceProxyType proxy_type)
{	 
    if((proxy_type==SopiServiceProxyType::ESopiServiceProxyPab && mPabSrvProxy)||
	    (proxy_type==SopiServiceProxyType::ESopiServiceProxyGrp && mGrpSrvProxy) ||
	    (proxy_type==SopiServiceProxyType::ESopiServiceProxyGab && mGabSrvProxy) || 
            (proxy_type==SopiServiceProxyType::ESopiServiceProxyPAU && mPAUSrvProxy) )
	 {
		setUserNameAndPassword(proxy_type);
		return;
	 }	 	

    SoapMap::iterator it = mSoapMap.find(proxy_type);
    soap* _soap = (it == mSoapMap.end() ? NULL : it->second);
    if (_soap == NULL)
    {
       _soap = (struct soap *)soap_new();
       mSoapMap[proxy_type] = _soap;
    }
	
    soap_init1(_soap, SOAP_C_UTFSTRING);
  
    setUserNameAndPassword(proxy_type);
	
#ifdef ANDROID 
    
	soap_ssl_init(); 
	
	if(proxy_type==SopiServiceProxyType::ESopiServiceProxyGab || mIgnoreCertificatValidation)
	{
	    fsslverify_android = NULL;
	}else
	{
	    fsslverify_android = resip::AndroidSecurity::verifyCallback;
	}
	   if (soap_ssl_client_context(_soap,
            (proxy_type==SopiServiceProxyType::ESopiServiceProxyGab || mIgnoreCertificatValidation)?SOAP_SSL_NO_AUTHENTICATION:SOAP_SSL_DEFAULT,
            NULL, 
            NULL, 
            NULL, 
            NULL, 
            NULL ))
	   {
          __android_log_print(ANDROID_LOG_WARN, "SOPI Client", " ERROR init ssl context");
		  return;
       }
	
#else
      // DRL While updating this code I added this to match the above as it was previously
      // not checking the flag but we are not sure why the Android version above is checking 
      // the proxy type as well so that part I left out.
      if (/*proxy_type == SopiServiceProxyType::ESopiServiceProxyGab ||*/ mIgnoreCertificatValidation)
      {
         _soap->ssl_flags = SOAP_SSL_NO_AUTHENTICATION;
      }
      else
      {
         _soap->ssl_flags = SOAP_SSL_DEFAULT;
      }

      _soap->fsslauth(_soap);
      SopiHelper::loadRootSSLCerts(_soap->ctx);
#endif

   switch(proxy_type)
   {
      case SopiServiceProxyType::ESopiServiceProxyPab:
	    mPabSrvProxy = new AddressBookUserServiceProxy(_soap,mPabSoapEndpoint.c_str());
	  break;
      case SopiServiceProxyType::ESopiServiceProxyGrp:
	    mGrpSrvProxy = new AddressBookUserServiceProxy(_soap,mPabGroupSoapEndpoint.c_str());
	  break;
      case SopiServiceProxyType::ESopiServiceProxyGab:
        mGabSrvProxy = new GABUserServiceProxy(_soap,mGabSoapEndpoint.c_str());
	  break;
      case SopiServiceProxyType::ESopiServiceProxyPAU:
        mPAUSrvProxy = new PresenceAuthorizationUserServiceProxy(_soap,mPAUSoapEndpoint.c_str());
	  break;
	  default:
	  break;
    }
   
}

void SopiClientImpl::getAddressbook()
{
	ns3__getAddressBookResponse getResp;
	
    initServiceProxy(ESopiServiceProxyPab);
	mPabSrvProxy->getAddressBook(getResp);
	
   if (!sendErrorEvent(ESopiServiceProxyPab, "GetAddressBook") && getResp.getAddressBookReturn)
   {
      AddressBookUpdatedEvent args;
      SopiHelper::copyPABEntryArr(*getResp.getAddressBookReturn, args.entries);
      if (args.entries.size() > 0)
         mInterface.fireEvent(cpcEvent(GenbandSopiHandler, onPersonalAddressBookUpdated), mClientHandle, args);
   }
}

void
SopiClientImpl::addAddressBookEntry(const AddressBookEntry& entry)
{
   ns3__addAddressBookEntryResponse addResp;
   initServiceProxy(ESopiServiceProxyPab);
   ns3__AddressBookEntryDO newAddrBookEntry;
   ns2__AddrBookGroupNaturalKeyDO newGroup;
   newAddrBookEntry.group = &newGroup;
   SopiHelper::makePABEntry(entry, newAddrBookEntry);
   mPabSrvProxy->addAddressBookEntry(&newAddrBookEntry, addResp);
   sendErrorEvent(ESopiServiceProxyPab, "AddAddressBookEntry");
   SopiHelper::freePABEntry(newAddrBookEntry);
}

void
SopiClientImpl::updateAddressBookEntry(const cpc::string& entryId, const AddressBookEntry& entry)
{
	
   ns3__modifyAddressBookEntryResponse modifyResp;
   initServiceProxy(ESopiServiceProxyPab);
   ns3__AddressBookEntryDO modifiedAddrBookEntry;
   ns2__AddrBookGroupNaturalKeyDO group;
   modifiedAddrBookEntry.group = &group;
   ns2__AddrBookEntryNaturalKeyDO entry_id;
   entry_id.name =new std::string(entryId);
   SopiHelper::makePABEntry(entry, modifiedAddrBookEntry);
   mPabSrvProxy->modifyAddressBookEntry(&entry_id, &modifiedAddrBookEntry, modifyResp);
   sendErrorEvent(ESopiServiceProxyPab, "UpdateAddressBookEntry");
   SopiHelper::freePABEntry(modifiedAddrBookEntry);
   delete entry_id.name;
   
}

void
SopiClientImpl::deleteAddressBookEntry(const cpc::string& name)
{
	
   ns3__removeAddressBookEntryResponse delResp;
   initServiceProxy(ESopiServiceProxyPab);
   ns3__AddressBookEntryDO modifiedAddrBookEntry;
   ns2__AddrBookEntryNaturalKeyDO entryId;
   entryId.name = new std::string(name);
   mPabSrvProxy->removeAddressBookEntry(&entryId, delResp);
   sendErrorEvent(ESopiServiceProxyPab, "DeleteAddressBookEntry");
   delete entryId.name;
   
}

void
SopiClientImpl::getAddressBookGroups()
{
	/*
   ns3__getAddressBookGroupsResponse getResp;
   cpc::vector<cpc::string> returnGroups;
   //initServiceProxy((soap**)&mGrpSrvProxy, mPabGroupSoapEndpoint, false);
   initServiceProxy(ESopiServiceProxyGrp);
   mGrpSrvProxy->getAddressBookGroups(getResp);
   
   if (!sendErrorEvent(mGrpSrvProxy, "GetAddressBookGroups") && getResp.getAddressBookGroupsReturn)
   {
      AddressBookGroupsUpdatedEvent args;
      SopiHelper::copyPABGroupsArr(*getResp.getAddressBookGroupsReturn, args.groups);
      if (args.groups.size() > 0)
         mInterface.fireEvent(cpcEvent(GenbandSopiHandler, onPersonalAddressBookGroupsUpdated), mClientHandle, args);
   }
 */  
}

void
SopiClientImpl::addAddressBookGroup(const cpc::string& groupName)
{
	/*
   ns3__addAddressBookGroupResponse addResp;
   initServiceProxy((soap**)&mGrpSrvProxy, mPabGroupSoapEndpoint, false);
   ns3__AddrBookGroupDO newAddrBookGroup;
   newAddrBookGroup.name = new std::string(groupName);
   mGrpSrvProxy->addAddressBookGroup(&newAddrBookGroup, addResp);
   sendErrorEvent(mGrpSrvProxy, "AddAddressBookGroup");
   delete newAddrBookGroup.name;
   */
}

void
SopiClientImpl::updateAddressBookGroup(const cpc::string& oldGroup, const cpc::string& newGroup)
{
	/*
   ns3__modifyAddressBookGroupResponse modifyResp;
   initServiceProxy((soap **)&mGrpSrvProxy, mPabGroupSoapEndpoint, false);
   ns2__AddrBookGroupNaturalKeyDO groupId;
   groupId.name = new std::string(oldGroup);
   ns3__AddrBookGroupDO modifiedAddrBookGrp;
   modifiedAddrBookGrp.name = new std::string(newGroup);
   mGrpSrvProxy->modifyAddressBookGroup(&groupId, &modifiedAddrBookGrp, modifyResp);
   sendErrorEvent(mGrpSrvProxy, "UpdateAddressBookGroup");
   delete groupId.name;
   delete modifiedAddrBookGrp.name;
   */
}

void
SopiClientImpl::deleteAddressBookGroup(const cpc::string& group)
{
	/*
   ns3__removeAddressBookGroupResponse delResp;
   initServiceProxy((soap**)&mGrpSrvProxy, mPabGroupSoapEndpoint);
   ns2__AddrBookGroupNaturalKeyDO groupId;
   groupId.name = new std::string(group);
   mGrpSrvProxy->removeAddressBookGroup(&groupId, delResp);
   sendErrorEvent(mGrpSrvProxy, "DeleteAddressBookGroup");
   delete groupId.name;
   */
}

void
SopiClientImpl::searchGlobalDirectory(const cpc::string& query, unsigned int maxResults)
{
   int64_t res = secondsSinceStart();
   if (query.empty() || maxResults == 0)
      return;
   cpc::vector<AddressBookEntry> results;
   ns4__SearchCriteriaDO srchCrt;
   srchCrt.searchCriteria = new std::string(query);
   
   ns1__StartStopDO range;
   range.start = 0;   // TODO: start/stop should be configurable
   range.stop = maxResults;
   
      #ifdef ANDROID
      __android_log_print(ANDROID_LOG_WARN, "SOPI Client", "searchGlobalDirectory start status = %d",mInterface.isNetworkAvailable());
      #endif

   if(mInterface.isGlobalSearchPendingRequest() || !mInterface.isNetworkAvailable()){
      #ifdef ANDROID
      __android_log_print(ANDROID_LOG_WARN, "SOPI Client", "searchGlobalDirectory interrupted %s fully",query.c_str());
      #endif
      delete srchCrt.searchCriteria;
      return;
   }
   
   initServiceProxy(ESopiServiceProxyGab);
   
   // 1. search by "name"
   ns4__searchGABByNameRangeResponse byNameResp;
   if(mGabSrvProxy->searchGABByNameRange(&srchCrt, &range, byNameResp)!=SOAP_OK)
   {
      #ifdef ANDROID
      __android_log_print(ANDROID_LOG_WARN, "SOPI Client", "searchGlobalDirectory: error searchGABByNameRange");
      #endif
      delete srchCrt.searchCriteria;
      return; 
   }
   
   if (!sendErrorEvent(ESopiServiceProxyGab, "SearchGlobalDirectoryByName") && 
      byNameResp._searchGABByNameRangeReturn && byNameResp._searchGABByNameRangeReturn->__size > 0)
   {
      SopiHelper::copyGABEntryArr(*byNameResp._searchGABByNameRangeReturn, results);
   }
   
   if(mInterface.isGlobalSearchPendingRequest() || !mInterface.isNetworkAvailable()){
	   
       #ifdef ANDROID
           __android_log_print(ANDROID_LOG_WARN, "SOPI Client", "searchGlobalDirectory interrupted %s after search ByName ",query.c_str());
       #endif
       delete srchCrt.searchCriteria;
       sendSearchGlobalAddressBookResult(query,results);
       return;
   }   
   
   // Pierre ToDo 4.2
   initServiceProxy(ESopiServiceProxyGab);
   // 2. search by "user name"
   ns4__searchGABByUserNameRangeResponse byUserNameResp;
   if(mGabSrvProxy->searchGABByUserNameRange(&srchCrt, &range, byUserNameResp)!=SOAP_OK)
   {
      #ifdef ANDROID
      __android_log_print(ANDROID_LOG_WARN, "SOPI Client", "searchGlobalDirectory: error searchGABByUserNameRange");
      #endif
      delete srchCrt.searchCriteria;
      return; 
   }
   
   if (!sendErrorEvent(ESopiServiceProxyGab, "SearchGlobalDirectoryByUsername") && 
      byUserNameResp._searchGABByUserNameRangeReturn && byUserNameResp._searchGABByUserNameRangeReturn->__size > 0)
   {
      SopiHelper::copyGABEntryArr(*byUserNameResp._searchGABByUserNameRangeReturn, results);
   }
   
   if(mInterface.isGlobalSearchPendingRequest() || !mInterface.isNetworkAvailable()){
       #ifdef ANDROID
        __android_log_print(ANDROID_LOG_WARN, "SOPI Client", "searchGlobalDirectory interrupted %s after search by user name",query.c_str());
       #endif
       delete srchCrt.searchCriteria;
       sendSearchGlobalAddressBookResult(query, results);
       return;
   }
   
   initServiceProxy(ESopiServiceProxyGab);
   // 3. search by "phone number"
   ns4__searchGABByPhoneNumberRangeResponse byPhoneNumRes;
   if(mGabSrvProxy->searchGABByPhoneNumberRange(&srchCrt, &range, byPhoneNumRes)!=SOAP_OK)
   {
	  #ifdef ANDROID
      __android_log_print(ANDROID_LOG_WARN, "SOPI Client", "searchGlobalDirectory: error searchGABByPhoneNumberRange");
      #endif
      delete srchCrt.searchCriteria;
      return;
   }
   
   if (!sendErrorEvent(ESopiServiceProxyGab, "SearchGlobalDirectoryByPhoneNumber") &&
      byPhoneNumRes._searchGABByPhoneNumberRangeReturn && byPhoneNumRes._searchGABByPhoneNumberRangeReturn->__size > 0)
   {
      SopiHelper::copyGABEntryArr(*byPhoneNumRes._searchGABByPhoneNumberRangeReturn, results);
   }

   delete srchCrt.searchCriteria;
#ifdef ANDROID 
   __android_log_print(ANDROID_LOG_WARN, "SOPI Client", "searchGlobalDirectory spend time = %d",(unsigned int)secondsSinceStart() - (unsigned int)res);
#endif  
   sendSearchGlobalAddressBookResult(query,results);
   
}

void 
SopiClientImpl::sendSearchGlobalAddressBookResult(cpc::string searchQuery, cpc::vector<AddressBookEntry> results)
{
   SearchGlobalAddressBookResultEvent args;
   args.entries = results;
   args.searchQuery = searchQuery;
   mInterface.fireEvent(cpcEvent(GenbandSopiHandler, onGlobalAddressBookSearchResult), mClientHandle, args);
}

bool
SopiClientImpl::sendErrorEvent(SopiServiceProxyType proxy_type, const cpc::string& requestName)
{
	const char *fault_str;
	switch(proxy_type)
   {
      case SopiServiceProxyType::ESopiServiceProxyPab:
	    if(mPabSrvProxy && mPabSrvProxy->soap->error)
			fault_str = mPabSrvProxy->soap_fault_string();
		else
			return false;
	  break;
      case SopiServiceProxyType::ESopiServiceProxyGrp:
	  if(mGrpSrvProxy && mGrpSrvProxy->soap->error)
		fault_str = mGrpSrvProxy->soap_fault_string();
      else
        return false;
	  break;
      case SopiServiceProxyType::ESopiServiceProxyGab:
	  if(mGabSrvProxy && mGabSrvProxy->soap->error)		  
		fault_str = mGabSrvProxy->soap_fault_string();
	  else
		 return false;
      case SopiServiceProxyType::ESopiServiceProxyPAU:
	  if(mPAUSrvProxy && mPAUSrvProxy->soap->error)		  
		fault_str = mPAUSrvProxy->soap_fault_string();
	  else
		 return false;
	  break;
	  default:
	     return false;
    }
	
      ErrorEvent args;
      args.errorText = "Error in " + requestName + " : " + fault_str;
      mInterface.fireEvent(cpcEvent(GenbandSopiHandler, onError), mClientHandle, args);

	return true;
}

void SopiClientImpl::addAuthorizedUser(const cpc::string& userName)
{
   ns6__addAuthorizedUserResponse getResp;	
   ns6__PresenceAuthorizationUserDO	authUser;
	
   authUser.name = new std::string(userName);
	
   initServiceProxy(ESopiServiceProxyPAU);
   mPAUSrvProxy->addAuthorizedUser(&authUser,getResp);
   
   sendErrorEvent(ESopiServiceProxyPAU, "addAuthorizedUser");
   delete authUser.name;
}

void SopiClientImpl::removeAuthorizedUser(const cpc::string& userName)
{
   ns6__removeAuthorizedUserResponse getResp;
   ns6__PresenceAuthorizationUserDO	authUser;
	
   authUser.name = new std::string(userName);
	
   initServiceProxy(ESopiServiceProxyPAU);
   mPAUSrvProxy->removeAuthorizedUser(&authUser,getResp);
   
   sendErrorEvent(ESopiServiceProxyPAU, "removeAuthorizedUser");
   delete authUser.name;
}

void SopiClientImpl::getAuthorizedUsers()
{
   ns6__getAuthorizedUsersResponse getResp;
   initServiceProxy(ESopiServiceProxyPAU);
   mPAUSrvProxy->getAuthorizedUsers(getResp);
   if (!sendErrorEvent(ESopiServiceProxyPAU, "getAuthorizedUsers") && getResp.getAuthorizedUsersReturn)
   {
      GetAuthorizedUsersEvent args;
      SopiHelper::copyPAUEntryArr(*getResp.getAuthorizedUsersReturn, args.entries);
      if (args.entries.size() > 0)
         mInterface.fireEvent(cpcEvent(GenbandSopiHandler, onGetAuthorizedUsers), mClientHandle, args);
   }
}
   
//=================================================================================

void SopiClientImpl::addBannedUser(const cpc::string& userName)
{
   ns6__addBannedUserResponse getResp;	
   ns6__PresenceAuthorizationUserDO	banUser;
	
   banUser.name = new std::string(userName);
	
   initServiceProxy(ESopiServiceProxyPAU);
   mPAUSrvProxy->addBannedUser(&banUser,getResp);
   
   sendErrorEvent(ESopiServiceProxyPAU, "addBannedUser");
   delete banUser.name;
}

void SopiClientImpl::removeBannedUser(const cpc::string& userName)
{
   ns6__removeBannedUserResponse getResp;
   ns6__PresenceAuthorizationUserDO	banUser;
	
   banUser.name = new std::string(userName);
	
   initServiceProxy(ESopiServiceProxyPAU);
   mPAUSrvProxy->removeBannedUser(&banUser,getResp);
   
   sendErrorEvent(ESopiServiceProxyPAU, "removeBannedUser");
   delete banUser.name;
}

void SopiClientImpl::getBannedUsers()
{
   ns6__getBannedUsersResponse getResp;
   initServiceProxy(ESopiServiceProxyPAU);
   mPAUSrvProxy->getBannedUsers(getResp);
   if (!sendErrorEvent(ESopiServiceProxyPAU, "getBannedUsers") && getResp.getBannedUsersReturn)
   {
      GetBannedUsersEvent args;
      SopiHelper::copyPAUEntryArr(*getResp.getBannedUsersReturn, args.entries);
      if (args.entries.size() > 0)
         mInterface.fireEvent(cpcEvent(GenbandSopiHandler, onGetBannedUsers), mClientHandle, args);
   }
}

//=================================================================================

void SopiClientImpl::addPoliteBlockedUser(const cpc::string& userName)
{
   ns6__addPoliteBlockedUserResponse getResp;	
   ns6__PresenceAuthorizationUserDO	polUser;
	
   polUser.name = new std::string(userName);
	
   initServiceProxy(ESopiServiceProxyPAU);
   mPAUSrvProxy->addPoliteBlockedUser(&polUser,getResp);
   
   sendErrorEvent(ESopiServiceProxyPAU, "addPoliteBlockedUser");
   delete polUser.name;
}

void SopiClientImpl::removePoliteBlockedUser(const cpc::string& userName)
{
   ns6__removePoliteBlockedUserResponse getResp;
   ns6__PresenceAuthorizationUserDO	polUser;
	
   polUser.name = new std::string(userName);
	
   initServiceProxy(ESopiServiceProxyPAU);
   mPAUSrvProxy->removePoliteBlockedUser(&polUser,getResp);
   
   sendErrorEvent(ESopiServiceProxyPAU, "removePoliteBlockedUser");
   delete polUser.name;
}

void SopiClientImpl::getPoliteBlockedUsers()
{
   ns6__getPoliteBlockedUsersResponse getResp;
   initServiceProxy(ESopiServiceProxyPAU);
   mPAUSrvProxy->getPoliteBlockedUsers(getResp);
   if (!sendErrorEvent(ESopiServiceProxyPAU, "getBannedUsers") && getResp.getPoliteBlockedUsersReturn)
   {
      GetPoliteBlockedUsersEvent args;
      SopiHelper::copyPAUEntryArr(*getResp.getPoliteBlockedUsersReturn, args.entries);
      if (args.entries.size() > 0)
         mInterface.fireEvent(cpcEvent(GenbandSopiHandler, onGetPoliteBlockedUsers), mClientHandle, args);
   }
}

   
   
}//namespace GenbandSopi

}


#endif
