#pragma once

#if !defined(CPCAPI2_SIP_CONVERSATION_MANAGER_IMPL_H)
#define CPCAPI2_SIP_CONVERSATION_MANAGER_IMPL_H

#include "cpcapi2defs.h"
#include "call/SipConversationTypes.h"
#include "call/SipConversationManager.h"
#include "call/SipConversationHandler.h"
#include "../account/SipAccountImpl.h"
#include "../account/SipAccountAwareFeature.h"
#include "../account/AppDialogFactoryDelegate.h"
#include "SipCallCreationInfo.h"
#include "SipConversationHandlerInternal.h"
#include "SipAVConversationManagerInterface.h"

#include <ConversationManager.hxx>
#include <vector>

namespace webrtc_recon
{
class MediaStackImpl;
}

namespace CPCAPI2
{
namespace SipConversation
{
class ReconUserAgent;
class ReconConversationManagerImpl;
class SipConversationHandler;
struct ConversationInitiatedEvent;
class SipConversationAdornmentInternalHandler;

class SipAVConversationManagerImpl : public CPCAPI2::SipAccount::SipAccountAwareFeature
{
public:
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, SipAVConversationManagerImpl*> AccountMap;
   SipAVConversationManagerImpl(SipAVConversationManagerInterface* iff,
                                std::shared_ptr<AccountMap> parentMap,
                                CPCAPI2::SipAccount::SipAccountImpl& account,
                                std::shared_ptr<webrtc_recon::MediaStackImpl> ms,
                                LocalLogger* localLogger);
   virtual ~SipAVConversationManagerImpl();

   void setDefaultSettings(const SipConversationSettings& settings, CPCAPI2::NetworkTransport transport);
   bool getDefaultSettings(CPCAPI2::NetworkTransport transport, SipConversationSettings& settings);
   ReconConversationManagerImpl* recon() const { return mReconConvMan; }
   webrtc_recon::MediaStackImpl* media_stack() const;

   void fireError(const SipConversationHandle& h, const cpc::string& errorText);
   void fireCallInitiated(const SipConversationHandle& h, const ConversationInitiatedEvent& args);
   void fireCallsEndedEventFromStarcodeNetworkChange(const SipConversationHandle& h, const ConversationEndedEventFromStarcodeNetworkChange& args);

   void fireConvStateChangedEvent(SipConversationHandle handle, const ConversationStateChangedEvent& args);
   void fireNewConvEvent(SipConversationHandle, const NewConversationEvent& args);
   void fireConvEndedEvent(SipConversationHandle, const ConversationEndedEvent& args);
   void fireIncomingTransferRequest(SipConversationHandle, const TransferRequestEvent& args);
   void fireTransferProgress(SipConversationHandle, const TransferProgressEvent& args);
   void fireTransferResponse(SipConversationHandle, const TransferResponseEvent& args);
   void fireConversationStateChangeRequest(SipConversationHandle, const ConversationStateChangeRequestEvent& args);
   void fireConversationMediaChangeRequest(SipConversationHandle, const ConversationMediaChangeRequestEvent& args);
   void fireConversationMediaChanged(SipConversationHandle, const ConversationMediaChangedEvent& args);
   // TODO: Do we realy need another fireError here?
   void fireError(SipConversationHandle, const ErrorEvent& args);

   template<typename TCls, typename TFn, typename TEvt1, typename TEvt2>
   void fireEvent(const TCls* c, const char* funcName, TFn func, const TEvt1 eventFirstArg, const TEvt2& eventSecondArg)
   {
      mInterface->fireEvent(c, funcName, func, mAccount.getHandle(), eventFirstArg, eventSecondArg);
   }

   //
   // IAccountAware
   //
   virtual int registerSdkDialogSetFactory( CPCAPI2::SipAccount::AppDialogSetFactory& factory ) OVERRIDE;
   virtual int adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile) OVERRIDE;
   virtual int onDumBeingDestroyed() OVERRIDE;
   virtual int addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig) OVERRIDE;
   virtual int onRegistrationSuccess(const SipAccount::SipRegistrationSuccessEvent& args) OVERRIDE;
   virtual void release() OVERRIDE;
   virtual int afterDumDestroyed() OVERRIDE;
   virtual bool canDisable(const SipAccount::AccountDisableCondition& cond) OVERRIDE;
   virtual int onRedirectReceived(const resip::SipMessage& originalRequest, const resip::SipMessage& response, resip::AppDialogSetHandle handle) OVERRIDE;
   virtual bool onRedirectTryingNextTarget(const resip::Uri& target, resip::SipMessage& request, resip::AppDialogSetHandle handle) OVERRIDE;

   SipCallCreationInfo* getCreationInfo(const SipConversationHandle& h) const;
   void addCreationInfo(const SipConversationHandle& h, SipCallCreationInfo* ci);
   void removeCreationInfo(const SipConversationHandle& h);

   std::vector<DtmfMode>& dtmfPreferences() { return mDtmfModePreferentialOrder; }

   void setAdornmentHandler(SipConversationAdornmentInternalHandler* handler);
   void setDtlsSupported(bool dtlsSupported);
   void setAnswerMode(CPCAPI2::SipConversation::AnswerModeSettings answerMode, CPCAPI2::NetworkTransport transport);

   void countNewCall();
   void countEndCall();
   int getCallCount();

   void setReconConvMan(ReconConversationManagerImpl* rcm);

   bool hasCallWithCallId(const cpc::string& callId) const;

   void sendInfo();
   
   void ignoreBindingFilterForStarcodeHandover();

private:
   void logEvent(const char* funcName, SipConversationHandle h, const std::string& argsStr = "");

private:
   SipAVConversationManagerInterface* mInterface;
   friend class MyAppDialogFactoryDelegate;
   std::weak_ptr<AccountMap> mParentMap;
   ReconUserAgent* mReconUA;
   ReconConversationManagerImpl* mReconConvMan;
   CPCAPI2::SipAccount::SipAccountImpl& mAccount;
   std::map<NetworkTransport, SipConversationSettings> mConvSettings;
   std::vector<DtmfMode> mDtmfModePreferentialOrder;
   SipConversationAdornmentInternalHandler* mAdornmentHandler;
   int mCallsInProgress;
   SipAccount::AccountDisableCondition mAccountDisableCondition;
   LocalLogger* mLocalLogger;
   bool mDtlsSupported;
   bool mIgnoreBindingFilter;
};

// this class's methods are executed AFTER addHandlers
class MyAppDialogFactoryDelegate : public CPCAPI2::SipAccount::AppDialogFactoryDelegate
{
public:
   MyAppDialogFactoryDelegate(SipAVConversationManagerImpl* pParent)
      : mImpl(pParent) {};

   // accept anything (NOTE: implies a priority/order)
   virtual bool isMyMessage(const resip::SipMessage& msg);

   virtual resip::AppDialogSet* createAppDialogSet(resip::DialogUsageManager& dum, const resip::SipMessage& msg);

private:
   SipAVConversationManagerImpl* mImpl;
};
}
}

#endif // CPCAPI2_SIP_CONVERSATION_MANAGER_IMPL_H
