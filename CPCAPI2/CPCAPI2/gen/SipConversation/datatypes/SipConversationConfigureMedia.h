#include "jsonrpc/OptionalSerialization.h"
#include <any>
#include "jsonrpc/VectorSerialization.h"
#include "jsonrpc/StringSerialization.h"
#include <nlohmann/json.hpp>
#include "MediaInfo.h"

#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONCONFIGUREMEDIA_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONCONFIGUREMEDIA_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationConfigureMedia
  {
  public:
    int32_t phoneHandle;
    int32_t conversationHandle;
    MediaInfo mediaDescriptor;

    SipConversationConfigureMedia() = default;

    SipConversationConfigureMedia(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationConfigureMedia& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["conversationHandle"] = o.conversationHandle;
     j["mediaDescriptor"] = o.mediaDescriptor;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationConfigureMedia& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.conversationHandle = j.at("conversationHandle");
     o.mediaDescriptor = j.at("mediaDescriptor");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONCONFIGUREMEDIA_H
