// bliu: mCapabilityDiscoveryMap is never freed up?

#include "brand_branded.h"

#if (CPCAPI2_BRAND_CAPABILITY_DISCOVERY_MODULE == 1)
#include "RcsCapabilityDiscoveryInterface.h"
#include "RcsCapabilityDiscoveryImpl.h"
#include "../phone/PhoneInterface.h"
#include "../account/SipAccountInterface.h"
#include "../account/SipAccountImpl.h"

using namespace CPCAPI2::SipAccount;
using resip::ReadCallbackBase;

#define __verify(condition, handler) if (!(condition)) { /* assert(false); */ handler; }

namespace CPCAPI2
{
namespace RcsCapabilityDiscovery
{

RcsCapabilityDiscoveryInterface::RcsCapabilityDiscoveryInterface(Phone* cpcPhone) :
   EventSource2< EventHandler<RcsCapabilityDiscoveryHandler, CPCAPI2::SipAccount::SipAccountHandle> >(dynamic_cast<PhoneInterface*>(cpcPhone)),
   mAccountIf(NULL),
   mTimer(dynamic_cast<PhoneInterface*>(cpcPhone)->getSdkModuleThread()),
   mPhone(dynamic_cast<PhoneInterface*>(cpcPhone)),
   mCapabilityDiscoveryMap(new CapabilityDiscoveryMap)
{
   mAccountIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(cpcPhone));
}

RcsCapabilityDiscoveryInterface::~RcsCapabilityDiscoveryInterface()
{
   mCapabilityDiscoveryMap->clear();
   mTimer.cancel();
}

void RcsCapabilityDiscoveryInterface::Release()
{
   delete this;
}

int RcsCapabilityDiscoveryInterface::setHandler(
      CPCAPI2::SipAccount::SipAccountHandle account,
      RcsCapabilityDiscoveryHandler* handler)
{
   ReadCallbackBase* setHandlerCmd = resip::resip_bind(&RcsCapabilityDiscoveryInterface::setHandlerImpl, this, account, handler);
   
   if (handler == NULL)
   {
      // removing the handler involves two steps:
      // 1. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);

      // 2. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      executeOnSdkThread(setHandlerCmd);
   }
   else
   {
      postToSdkThread(setHandlerCmd);
   }
   
   return kSuccess;
}

void RcsCapabilityDiscoveryInterface::setHandlerImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      RcsCapabilityDiscoveryHandler* handler)
{
   if (SipAccountImpl* acct = mAccountIf->getAccountImpl(account))
   {
      auto ith = mHandlers.find(account);
      if (mHandlers.end() != ith)
      {
         removeAppHandler(ith->second, account);
      }

      mHandlers[account] = handler;
      if (nullptr != handler)
      {
         addAppHandler(handler, account);
      }

      CapabilityDiscoveryMap::iterator it = mCapabilityDiscoveryMap->find(account);
      if (it == mCapabilityDiscoveryMap->end())
      {
         RcsCapabilityDiscoveryImpl* capabilityDiscovery = new RcsCapabilityDiscoveryImpl(mCapabilityDiscoveryMap, *acct, *this);
         (*mCapabilityDiscoveryMap)[account] = std::make_pair(handler, capabilityDiscovery);

         resip::Lock locker (mCacheLock);
         mCache.insert(std::make_pair(account, CacheMap::mapped_type()));
      }
      else
      {
         (*mCapabilityDiscoveryMap)[account].first = handler;
      }
   }
   else
   {
      mAccountIf->fireError("Invalid account handle for RcsCapabilityDiscovery::setHandler");
   }
}

int RcsCapabilityDiscoveryInterface::importToCache(
   CPCAPI2::SipAccount::SipAccountHandle account,
   const cpc::vector<RcsCapabilityStatus>& cache)
{
   resip::Lock locker (mCacheLock);

   if (mCache.find(account) == mCache.end()) return kError;

   cpc::vector<RcsCapabilityStatus> toAdd;

   for (cpc::vector<RcsCapabilityStatus>::const_iterator it = cache.begin(); it != cache.end(); ++it)
   {
      const RcsCapabilityStatus& item = *it;

      std::pair<CacheMap::mapped_type::iterator, bool> insertor = mCache[account].insert(std::make_pair(item.targetAddress, RcsCapabilityStatusEx()));

      if (insertor.second) toAdd.push_back(item);

      RcsCapabilityStatusEx& cached = insertor.first->second;

      if (cached.lastUpdated < item.lastUpdated)
      {
         static_cast<RcsCapabilityStatus&>(cached) = item;
      }

      if (cached.signalingStatusCode == 0) cached.signalingStatusCode = -1; // -1 means this is an imported item
   }

   ReadCallbackBase* cmd = resip::resip_bind(&RcsCapabilityDiscoveryInterface::addContactsImpl, this, account, toAdd);
   postToSdkThread(cmd);

   return kSuccess;
}

void RcsCapabilityDiscoveryInterface::addContactsImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::vector<RcsCapabilityStatus>& cache)
{
   if (RcsCapabilityDiscoveryImpl* impl = getCapabilityDiscoveryImpl(account))
   {
      for (cpc::vector<RcsCapabilityStatus>::const_iterator it = cache.begin(); it != cache.end(); ++it)
      {
         const RcsCapabilityStatus& item = *it;

         impl->addContact(item.targetAddress, std::chrono::system_clock::from_time_t(item.lastUpdated));
      }
   }
   else
   {
      mAccountIf->fireError("Invalid account handle for RcsCapabilityDiscovery::addContacts");
   }
}

int RcsCapabilityDiscoveryInterface::exportFromCache(
   CPCAPI2::SipAccount::SipAccountHandle account,
   cpc::vector<RcsCapabilityStatus>& cache)
{
   __verify(cache.empty(), cache.clear());

   resip::Lock locker (mCacheLock);

   if (mCache.find(account) == mCache.end()) return kError;

   CacheMap::mapped_type& cacheMap = mCache[account];
   for (CacheMap::mapped_type::iterator it = cacheMap.begin(); it != cacheMap.end(); ++it)
   {
      CacheMap::mapped_type::value_type& item = *it;

      cache.push_back(item.second);
   }

   return kSuccess;
}

int RcsCapabilityDiscoveryInterface::addContact(
   CPCAPI2::SipAccount::SipAccountHandle account,
   const cpc::string& targetAddress)
{
   __verify(!targetAddress.empty(), return kSuccess);

   std::set<cpc::string> toAdd;
   toAdd.insert(targetAddress);
   return addContacts(account, toAdd);
}

int RcsCapabilityDiscoveryInterface::removeContact(
   CPCAPI2::SipAccount::SipAccountHandle account,
   const cpc::string& targetAddress)
{
   std::set<cpc::string> toRemove;
   toRemove.insert(targetAddress);
   return removeContacts(account, toRemove);
}

int RcsCapabilityDiscoveryInterface::synchronizeAllContacts(
   CPCAPI2::SipAccount::SipAccountHandle account,
   const cpc::vector<cpc::string>& targetAddresses)
{
   resip::Lock locker (mCacheLock);

   if (mCache.find(account) == mCache.end()) return kError;

   std::set<cpc::string> toRemove;

   CacheMap::mapped_type& cacheMap = mCache[account];
   for (CacheMap::mapped_type::iterator it = cacheMap.begin(); it != cacheMap.end(); ++it)
   {
      CacheMap::mapped_type::value_type& item = *it;

      if (std::find(targetAddresses.begin(), targetAddresses.end(), item.first) == targetAddresses.end()) toRemove.insert(item.first);
   }

   removeContacts(account, toRemove);

   std::set<cpc::string> toAdd;

   for (cpc::vector<cpc::string>::const_iterator it = targetAddresses.begin(); it != targetAddresses.end(); ++it)
   {
      const cpc::string& address = *it;

      if (mCache[account].find(address) == mCache[account].end()) toAdd.insert(address);
   }

   addContacts(account, toAdd);

   return kSuccess;
}

int RcsCapabilityDiscoveryInterface::getContactCapabilityStatus(
   CPCAPI2::SipAccount::SipAccountHandle account,
   const cpc::string& targetAddress,
   RcsCapabilityStatus& status)
{
   resip::Lock locker (mCacheLock);

   if (mCache.find(account) == mCache.end()) return kError;

   CacheMap::mapped_type::iterator it = mCache[account].find(targetAddress);
   if (it == mCache[account].end()) return kError; // need to addContact first

   ReadCallbackBase* cmd = resip::resip_bind(&RcsCapabilityDiscoveryInterface::getContactCapabilityStatusImpl, this, account, targetAddress);
   postToSdkThread(cmd);

   if (it->second.signalingStatusCode != 0) // imported item will have -1 and responsed item will have a positive value
   {
      status = it->second;
      return kCompletedSynchronously;
   }

   return kCompletedAsynchronously;
}

void RcsCapabilityDiscoveryInterface::getContactCapabilityStatusImpl(
   CPCAPI2::SipAccount::SipAccountHandle account,
   const cpc::string& targetAddress)
{
   if (RcsCapabilityDiscoveryImpl* impl = getCapabilityDiscoveryImpl(account))
   {
      impl->requestContactCapabilityStatus(targetAddress, true);
   }
   else
   {
      mAccountIf->fireError("Invalid account handle for RcsCapabilityDiscovery::getContactCapabilityStatus");
   }
}

int RcsCapabilityDiscoveryInterface::setMyCapabilities(
   CPCAPI2::SipAccount::SipAccountHandle account,
   const RcsCapabilitySet& caps)
{
   ReadCallbackBase* cmd = resip::resip_bind(&RcsCapabilityDiscoveryInterface::setMyCapabilitiesImpl, this, account, caps);
   postToSdkThread(cmd);

   return kSuccess;
}

void RcsCapabilityDiscoveryInterface::setMyCapabilitiesImpl(
   CPCAPI2::SipAccount::SipAccountHandle account,
   const RcsCapabilitySet& caps)
{
   if (RcsCapabilityDiscoveryImpl* impl = getCapabilityDiscoveryImpl(account))
   {
      impl->setMyCapabilities(caps);
   }
   else
   {
      mAccountIf->fireError("Invalid account handle for RcsCapabilityDiscovery::setMyCapabilities");
   }
}

int RcsCapabilityDiscoveryInterface::updateSettings(
   CPCAPI2::SipAccount::SipAccountHandle account,
   const RcsCapabilityDiscoverySettings& settings)
{
   ReadCallbackBase* cmd = resip::resip_bind(&RcsCapabilityDiscoveryInterface::updateSettingsImpl, this, account, settings);
   postToSdkThread(cmd);

   return kSuccess;
}

int RcsCapabilityDiscoveryInterface::addContacts(
   CPCAPI2::SipAccount::SipAccountHandle account,
   const std::set<cpc::string>& targetAddresses)
{
   resip::Lock locker (mCacheLock);

   if (mCache.find(account) == mCache.end()) return kError;

   std::map<cpc::string, unsigned int> toAdd;

   for (std::set<cpc::string>::const_iterator it = targetAddresses.begin(); it != targetAddresses.end(); ++it)
   {
      const cpc::string& address = *it;

      bool inserted = mCache[account].insert(std::make_pair(address, RcsCapabilityStatusEx())).second;

      if (!inserted) continue;

      toAdd.insert(std::make_pair(address, 0));
   }

   ReadCallbackBase* cmd = resip::resip_bind(&RcsCapabilityDiscoveryInterface::addContactsImpl2, this, account, toAdd);
   postToSdkThread(cmd);

   return kSuccess;
}

void RcsCapabilityDiscoveryInterface::addContactsImpl2(
   CPCAPI2::SipAccount::SipAccountHandle account,
   const std::map<cpc::string, unsigned int>& toAdd)
{
   if (RcsCapabilityDiscoveryImpl* impl = getCapabilityDiscoveryImpl(account))
   {
      for (std::map<cpc::string, unsigned int>::const_iterator it = toAdd.begin(); it != toAdd.end(); ++it)
      {
         const std::map<cpc::string, unsigned int>::value_type& item = *it;

         impl->addContact(item.first, std::chrono::system_clock::from_time_t(item.second));
      }
   }
   else
   {
      mAccountIf->fireError("Invalid account handle for RcsCapabilityDiscovery::addContacts");
   }
}

int RcsCapabilityDiscoveryInterface::removeContacts(
   CPCAPI2::SipAccount::SipAccountHandle account,
   const std::set<cpc::string>& targetAddresses)
{
   resip::Lock locker (mCacheLock);

   if (mCache.find(account) == mCache.end()) return kError;

   std::set<cpc::string> toRemove;

   for (std::set<cpc::string>::const_iterator it = targetAddresses.begin(); it != targetAddresses.end(); ++it)
   {
      const cpc::string& address = *it;

      if (mCache[account].find(address) == mCache[account].end()) continue;

      mCache[account].erase(address);

      toRemove.insert(address);
   }

   ReadCallbackBase* cmd = resip::resip_bind(&RcsCapabilityDiscoveryInterface::removeContactsImpl, this, account, toRemove);
   postToSdkThread(cmd);

   return kSuccess;
}

void RcsCapabilityDiscoveryInterface::removeContactsImpl(
   CPCAPI2::SipAccount::SipAccountHandle account,
   const std::set<cpc::string>& targetAddresses)
{
   if (RcsCapabilityDiscoveryImpl* impl = getCapabilityDiscoveryImpl(account))
   {
      for (std::set<cpc::string>::const_iterator it = targetAddresses.begin(); it != targetAddresses.end(); ++it)
      {
         const cpc::string& address = *it;

         impl->removeContact(address);
      }
   }
   else
   {
      mAccountIf->fireError("Invalid account handle for RcsCapabilityDiscovery::removeContacts");
   }
}

void RcsCapabilityDiscoveryInterface::updateSettingsImpl(
   CPCAPI2::SipAccount::SipAccountHandle account,
   const RcsCapabilityDiscoverySettings& settings)
{
   mTimer.cancel();
   mTimer.expires_from_now(sTimeoutIntervalMs);
   mTimer.async_wait(this, 0, NULL); // do NOT call onTimer() directly as it must run within the SDK thread

   if (RcsCapabilityDiscoveryImpl* impl = getCapabilityDiscoveryImpl(account))
   {
      impl->updateSettings(settings);
   }
   else
   {
      mAccountIf->fireError("Invalid account handle for RcsCapabilityDiscovery::updateSettings");
   }
}

void RcsCapabilityDiscoveryInterface::onTimer(unsigned short timerId, void* appState)
{
   // must run within the SDK thread

   for (CapabilityDiscoveryMap::iterator it = mCapabilityDiscoveryMap->begin(); it != mCapabilityDiscoveryMap->end(); ++it)
   {
      CapabilityDiscoveryMap::value_type& item = *it;

      RcsCapabilityDiscoveryImpl* capabilityImpl = getCapabilityDiscoveryImpl(item.first);
      capabilityImpl->onTimer();
   }

   mTimer.expires_from_now(sTimeoutIntervalMs);
   mTimer.async_wait(this, timerId, appState);
}

RcsCapabilityDiscoveryImpl* RcsCapabilityDiscoveryInterface::getCapabilityDiscoveryImpl(CPCAPI2::SipAccount::SipAccountHandle account)
{
   CapabilityDiscoveryMap::iterator it = mCapabilityDiscoveryMap->find(account);
   return it == mCapabilityDiscoveryMap->end() ? NULL : it->second.second;
}

int RcsCapabilityDiscoveryInterface::onContactCapabilityStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const RcsOnContactCapabilityStatusChangedEvent& args)
{
   CapabilityDiscoveryMap::iterator it = mCapabilityDiscoveryMap->find(account);
   if (it == mCapabilityDiscoveryMap->end()) return kSuccess;

   if (SipAccountImpl* acct = mAccountIf->getAccountImpl(account))
   {
      fireEvent(cpcEvent(RcsCapabilityDiscoveryHandler, onContactCapabilityStatusChanged), account, args);
      return kSuccess;
   }
   else
   {
      mAccountIf->fireError("Invalid account handle for RcsCapabilityDiscovery::onContactCapabilityStatusChanged");
      return kError;
   }
}

std::ostream& operator<<(std::ostream& os, const RcsOnContactCapabilityStatusChangedEvent& evt)
{
  return os << "RcsOnContactCapabilityStatusChangedEvent";
}

}
}
#endif // CPCAPI2_CAPABILITY_DISCOVERY_MODULE
