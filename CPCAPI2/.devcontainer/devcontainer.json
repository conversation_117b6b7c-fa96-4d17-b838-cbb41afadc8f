{"name": "aws-amazonlinux-2023", "build": {"dockerfile": "aws-amazonlinux", "context": "."}, "runArgs": ["--memory=16gb", "--env-file", ".devcontainer/devcontainer.env", "--sysctl", "net.ipv6.conf.all.disable_ipv6=0"], "customizations": {"vscode": {"extensions": ["ms-vscode.cpptools-extension-pack", "ms-vscode.cpptools", "vadimcn.vscode-lldb"]}}, "containerEnv": {"CPCAPI2_CONAN_USER": "${localEnv:CPCAPI2_CONAN_USER}", "CPCAPI2_CONAN_APIKEY": "${localEnv:CPCAPI2_CONAN_APIKEY}"}, "remoteEnv": {"CPCAPI2_CONAN_USER": "${localEnv:CPCAPI2_CONAN_USER}", "CPCAPI2_CONAN_APIKEY": "${localEnv:CPCAPI2_CONAN_APIKEY}"}}