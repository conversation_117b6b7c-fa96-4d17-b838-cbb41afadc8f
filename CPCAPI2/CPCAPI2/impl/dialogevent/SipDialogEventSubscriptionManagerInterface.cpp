#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_DIALOG_EVENT_MODULE == 1)

#include "SipDialogEventSubscriptionManagerInterface.h"
#include "SipDialogEventSubscriptionManagerImpl.h"
#include "DialogInfoDocumentHelper.h"
#include "../event/SipEventSubscriptionCreationInfo.h"

using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipAccount;

namespace CPCAPI2
{
namespace SipDialogEvent
{

SipDialogEventSubscriptionManagerInterface::SipDialogEventSubscriptionManagerInterface(Phone* phone)
   : CPCAPI2::EventSource2< CPCAPI2::EventHandler<SipDialogEventSubscriptionHandler, SipAccount::SipAccountHandle> >(dynamic_cast<PhoneInterface*>(phone)),
     mAccountIf(NULL),
     mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mAccountMapPtr(new AccountMap),
     mAccountMap(*mAccountMapPtr)
{
   mAccountIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(phone));
   mSipEventIf = dynamic_cast<SipEventManagerInterface*>(SipEventManager::getInterface(phone));
}

SipDialogEventSubscriptionManagerInterface::~SipDialogEventSubscriptionManagerInterface()
{
   // Delete all the registered handlers and clear the map
   for (AccountMap::iterator it = mAccountMap.begin(); it != mAccountMap.end(); it++)
   {
      SipDialogEventSubscriptionManagerImpl* dialogEventSubscriptionManagerImpl = it->second;
      delete dialogEventSubscriptionManagerImpl;
   }   
   mAccountMap.clear();
}

void SipDialogEventSubscriptionManagerInterface::Release()
{
   delete this;
}

int SipDialogEventSubscriptionManagerInterface::setHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipDialogEventSubscriptionHandler* handler)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&SipDialogEventSubscriptionManagerInterface::setHandlerImpl, this, account, handler);
   
   if (handler == NULL)
   {
      // removing the handler involves two steps:
      // 1. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);

      // 2. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      executeOnSdkThread(f);
   }
   else
   {
      postToSdkThread(f);
   }
      
   return kSuccess;
}

int SipDialogEventSubscriptionManagerInterface::setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipDialogEventSubscriptionHandler* handler)
{
   // Get the account
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (!acct)
   {
      mAccountIf->fireError("Invalid account handle for SipDialogEventPublicationManagerInterface::setHandler");
      return kError;
   }

   // Get the associated manager
   SipDialogEventSubscriptionManagerImpl* dialogEventSubscriptionManager = getManagerForAccount(account);
   if (!dialogEventSubscriptionManager)
   {
      // Make sure the account is not enabled at this point
      if (acct->isEnabled())
      {
         mAccountIf->fireError("SipDialogEventSubscriptionManagerInterface::setHandler was called after account enabled: " + cpc::to_string(account));
         return kSuccess;
      }
      
      // Manager not found for this account
      // Create the manager and keep the association with the account
      dialogEventSubscriptionManager = new SipDialogEventSubscriptionManagerImpl(this, mSipEventIf, mAccountMapPtr, *acct);
      mAccountMap[account] = dialogEventSubscriptionManager;
      
      mSipEventIf->setHandlerImpl(account, DialogInfoDocumentHelper::EVENT_PACKAGE_NAME, dialogEventSubscriptionManager);
   }

   auto it = mHandlers.find(account);
   if (mHandlers.end() != it)
   {
     removeAppHandler(it->second, account);
   }

   mHandlers[account] = handler;
   if (nullptr != handler)
   {
      addAppHandler(handler, account);
   }

   return kSuccess;
}

SipDialogEventSubscriptionHandle SipDialogEventSubscriptionManagerInterface::createSubscription(CPCAPI2::SipAccount::SipAccountHandle account)
{
   return mSipEventIf->createSubscription(account);
}

SipDialogEventSubscriptionHandle SipDialogEventSubscriptionManagerInterface::createSubscriptionImpl(CPCAPI2::SipAccount::SipAccountHandle account)
{
   SipDialogEventSubscriptionHandle h = SipEventSubscriptionHandleFactory::getNext();
   mSipEventIf->createSubscriptionImpl(account, h);
   return h;
}

int SipDialogEventSubscriptionManagerInterface::recreateSubscription(CPCAPI2::SipAccount::SipAccountHandle account, SipDialogEventSubscriptionHandle h)
{
   return mSipEventIf->recreateSubscription(account, static_cast<SipEventSubscriptionHandle>(h));
}

int SipDialogEventSubscriptionManagerInterface::applySubscriptionSettings(SipDialogEventSubscriptionHandle subscription, const SipDialogEventSubscriptionSettings& settings)
{
   postToSdkThread(resip::resip_bind(&SipDialogEventSubscriptionManagerInterface::applySubscriptionSettingsImpl, this, subscription, settings));
   return kSuccess;
}

int SipDialogEventSubscriptionManagerInterface::applySubscriptionSettingsImpl(SipDialogEventSubscriptionHandle subscription, const SipDialogEventSubscriptionSettings& settings)
{
   // Create the event settings
   SipEventSubscriptionSettings eventSettings;
   eventSettings.eventPackage = DialogInfoDocumentHelper::EVENT_PACKAGE_NAME;
   if (settings.includeSessionDescription)
   {
      Parameter param(DialogInfoDocumentHelper::INCLUDE_SDP_PARAM_NAME, "");
      eventSettings.eventPackageParams.push_back(param);
   }
   SipEventSubscriptionCreationInfo* ci = mSipEventIf->getCreationInfo(subscription);
   if (ci != NULL)
   {
      if (settings.enableSharedAppearanceDialogExtensions)
         ci->sharedCallAppearanceExtensionsEnabled = true;
      ci->enableNotifyTerminationRetryHandling = settings.enableNotifyTerminationRetryHandling;
   }
   if (!settings.additionalEventParameterName.empty())
   {
      Parameter param(settings.additionalEventParameterName, "");
      eventSettings.eventPackageParams.push_back(param);
   }
   eventSettings.expiresSeconds = settings.expiresSeconds;
   eventSettings.supportedMimeTypes.push_back(DialogInfoDocumentHelper::EVENT_MIME_TYPE);

   // Apply the subscription settings
   return mSipEventIf->applySubscriptionSettingsImpl(subscription, eventSettings);
}

int SipDialogEventSubscriptionManagerInterface::addParticipant(SipDialogEventSubscriptionHandle subscription, const cpc::string& targetAddress)
{
   postToSdkThread(resip::resip_bind(&SipDialogEventSubscriptionManagerInterface::addParticipantImpl, this, subscription, targetAddress, false));
   return kSuccess;
}

int SipDialogEventSubscriptionManagerInterface::addParticipantImpl(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress, bool isEventServer)
{
   // Transform the address specified if needed
   cpc::string transformedAddress = targetAddress; 
   SipEventSubscriptionCreationInfo* ci = mSipEventIf->getCreationInfo(subscription);
   if (ci != NULL)
   {
      AddressTransformer* transformer = mPhone->getAddressTransformer();
      if(transformer != NULL)
      {
         AddressTransformationContext context;
         context.addressUsageType = AddressUsageType::AddressUsageType_SipPresence;

         SipAccount::SipAccountImpl* acct = mAccountIf->getAccountImpl(ci->account);
         if (!acct)
         {
            mAccountIf->fireError("Invalid account handle for SipDialogEventSubscriptionManager::addParticipant");
            return kError;
         }

         context.registrationDomain = acct->getSettings().domain;
         transformer->applyTransformation(targetAddress, context, transformedAddress);
      }
   }

   // Add the participant to the event module
   return mSipEventIf->addParticipantImpl(subscription, transformedAddress, isEventServer);
}

int SipDialogEventSubscriptionManagerInterface::setEventServer(SipDialogEventSubscriptionHandle subscription, const cpc::string& targetAddress)
{
   return mSipEventIf->setEventServer(subscription, targetAddress);
}

int SipDialogEventSubscriptionManagerInterface::start(SipDialogEventSubscriptionHandle subscription)
{
   return mSipEventIf->start(subscription);
}

int SipDialogEventSubscriptionManagerInterface::startImpl(SipDialogEventSubscriptionHandle subscription)
{
   return mSipEventIf->startImpl(subscription);
}

int SipDialogEventSubscriptionManagerInterface::end(SipDialogEventSubscriptionHandle subscription)
{
   postToSdkThread(resip::resip_bind(&SipDialogEventSubscriptionManagerInterface::endImpl, this, subscription));
   return kSuccess;
}

int SipDialogEventSubscriptionManagerInterface::endImpl(SipDialogEventSubscriptionHandle subscription)
{
   return mSipEventIf->endImpl(subscription);
}

int SipDialogEventSubscriptionManagerInterface::reject(SipDialogEventSubscriptionHandle subscription, unsigned int rejectReason)
{
   return mSipEventIf->reject(subscription, rejectReason);
}

int SipDialogEventSubscriptionManagerInterface::accept(SipDialogEventSubscriptionHandle subscription)
{
   postToSdkThread(resip::resip_bind(&SipDialogEventSubscriptionManagerInterface::acceptImpl, this, subscription));
   return kSuccess;
}

int SipDialogEventSubscriptionManagerInterface::acceptImpl(SipDialogEventSubscriptionHandle subscription)
{
   // Get the associated manager
   SipDialogEventSubscriptionManagerImpl *dialogEventSubscriptionManager = getManagerForSubscription(subscription);
   assert(dialogEventSubscriptionManager);

   // Generate an empty dialog-info document to publish
   DialogInfoDocument emptyDialogInfoDoc = dialogEventSubscriptionManager->createEmptyDialogInfo();

   // Create the event state
   SipEventState eventState = dialogEventSubscriptionManager->createEventState(subscription, emptyDialogInfoDoc);

   // Accept the subscription
   mSipEventIf->accept(subscription, eventState);
   return kSuccess;
}

int SipDialogEventSubscriptionManagerInterface::notify(SipDialogEventSubscriptionHandle subscription, const DialogInfoDocument& dialogInfoDoc)
{
   postToSdkThread(resip::resip_bind(&SipDialogEventSubscriptionManagerInterface::notifyImpl, this, subscription, dialogInfoDoc));
   return kSuccess;
}

int SipDialogEventSubscriptionManagerInterface::notifyImpl(SipDialogEventSubscriptionHandle subscription, const DialogInfoDocument& dialogInfoDoc)
{
   // Get the associated manager
   SipDialogEventSubscriptionManagerImpl *dialogEventSubscriptionManager = getManagerForSubscription(subscription);
   assert(dialogEventSubscriptionManager);

   // Create the event state
   SipEventState eventState = dialogEventSubscriptionManager->createEventState(subscription, dialogInfoDoc);

   // Accept the subscription
   mSipEventIf->notify(subscription, eventState);
   return kSuccess;
}

SipDialogEventSubscriptionManagerImpl* SipDialogEventSubscriptionManagerInterface::getManagerForAccount(CPCAPI2::SipAccount::SipAccountHandle account)
{
   AccountMap::iterator it = mAccountMap.find(account);
   return (it != mAccountMap.end()) ? it->second : NULL;
}

SipDialogEventSubscriptionManagerImpl* SipDialogEventSubscriptionManagerInterface::getManagerForSubscription(SipDialogEventSubscriptionHandle subscription)
{
   // Get the subscription's creation info
   SipEventSubscriptionCreationInfo* ci = mSipEventIf->getCreationInfo(subscription);
   assert(ci);

   // Get the manager for the account associated with the subscription
   return getManagerForAccount(ci->account);
}
std::ostream& operator<<(std::ostream& os, const NewDialogEventSubscriptionEvent& evt)
{
   return os << "NewDialogEventSubscriptionEvent";
}

std::ostream& operator<<(std::ostream& os, const DialogEventSubscriptionEndedEvent& evt)
{
   return os << "DialogEventSubscriptionEndedEvent";
}

std::ostream& operator<<(std::ostream& os, const DialogEventSubscriptionStateChangedEvent& evt)
{
   return os << "DialogEventSubscriptionStateChangedEvent";
}

std::ostream& operator<<(std::ostream& os, const IncomingDialogInfoEvent& evt)
{
   return os << "IncomingDialogInfoEvent";
}

std::ostream& operator<<(std::ostream& os, const DialogResourceListEvent& evt)
{
   return os << "DialogResourceListEvent";
}

std::ostream& operator<<(std::ostream& os, const NotifyDialogInfoFailureEvent& evt)
{
   return os << "NotifyDialogInfoFailureEvent";
}

std::ostream& operator<<(std::ostream& os, const SipDialogEvent::ErrorEvent& evt)
{
   return os << "SipDialogEvent::ErrorEvent";
}

std::ostream& operator<<(std::ostream& os, const SipDialogEvent::DialogInfo& di)
{
   return os << "id: " << di.id << ", localParticipant: " << di.localParticipant;
}

std::ostream& operator<<(std::ostream& os, const SipDialogEvent::ParticipantInfo& pi)
{
   os << "identities: ";
   for (cpc::vector<IdentityInfo>::const_iterator it = pi.identities.begin(); it != pi.identities.end(); ++it)
   {
      os << "[" << *it << "]";
   }
   os << ", target: " << pi.target << ", contentType: " << pi.contentType << ", sessionDescription: " << pi.sessionDescription
      << ", cseq: " << pi.cseq;

   return os;
}

std::ostream& operator<<(std::ostream& os, const SipDialogEvent::IdentityInfo& ii)
{
   os << "address: " << ii.address << ", uri: " << ii.uri << ", uriParams: ";
   for (cpc::vector<Parameter>::const_iterator it = ii.uriParams.begin(); it != ii.uriParams.end(); ++it)
   {
      os << "[" << *it << "]";
   }
   os << ", displayName" << ii.displayName;
   return os;
}

std::ostream& operator<<(std::ostream& os, const SipDialogEvent::TargetInfo& ti)
{
   os << "uri: " << ti.uri << ", params: ";
   for (cpc::vector<Parameter>::const_iterator it = ti.params.begin(); it != ti.params.end(); ++it)
   {
      os << "[" << *it << "]";
   }
   return os;
}

std::ostream& operator<<(std::ostream& os, const Parameter& p)
{
   return os << "name: " << p.name << ", value: " << p.value;
}

}
}

#endif // CPCAPI2_BRAND_SIP_DIALOG_EVENT_MODULE
