#pragma once

#if !defined(__CPCAPI2_SIP_BUSY_LAMP_FIELD_MANAGER_INTERFACE_H__)
#define __CPCAPI2_SIP_BUSY_LAMP_FIELD_MANAGER_INTERFACE_H__

#include "cpcapi2defs.h"
#include "busylampfield/SipBusyLampFieldManager.h"
#include "busylampfield/SipBusyLampFieldState.h"
#include "busylampfield/SipBusyLampFieldHandler.h"
#include "dialogevent/SipDialogEventSubscriptionManagerInterface.h"
#include "call/SipAVConversationManagerInterface.h"
#include "account/SipAccountInterface.h"
#include "phone/PhoneModule.h"

#include <rutil/RecursiveMutex.hxx>

namespace CPCAPI2
{
class Phone;

namespace SipBusyLampField
{
class SipBusyLampFieldManagerImpl;
struct SipBusyLampFieldRemoteLineSetInfo;

typedef std::map<SipAccount::SipAccountHandle, SipBusyLampFieldManagerImpl*> AccountMap;

class SipBusyLampFieldManagerInterface : public CPCAPI2::EventSource2<CPCAPI2::EventHandler<SipBusyLampFieldHandler, CPCAPI2::SipAccount::SipAccountHandle> >,
                                         public SipBusyLampFieldManager, 
                                         public PhoneModule
{
public:
   SipBusyLampFieldManagerInterface(Phone* phone);
   virtual ~SipBusyLampFieldManagerInterface();

   FORWARD_EVENT_PROCESSOR(SipBusyLampFieldManagerInterface);

   PhoneInterface* phoneInterface();

   // SipBusyLampFieldManager interface
   virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipBusyLampFieldHandler* handler) OVERRIDE;
   virtual SipBusyLampFieldRemoteLineSetHandle createBusyLampFieldRemoteLineSet(CPCAPI2::SipAccount::SipAccountHandle account, const SipBusyLampFieldRemoteLineSetSettings& settings) OVERRIDE;
   virtual int addRemoteLine(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine) OVERRIDE;
   virtual int start(SipBusyLampFieldRemoteLineSetHandle remoteLineSet) OVERRIDE;
   virtual int end(SipBusyLampFieldRemoteLineSetHandle remoteLineSet) OVERRIDE;
   virtual CPCAPI2::SipConversation::SipConversationHandle answerCall(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, const cpc::string& dialogId, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor) OVERRIDE;
   virtual CPCAPI2::SipConversation::SipConversationHandle joinCall(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine, const cpc::string& dialogId, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor, BargeInMode bargeInMode = BargeInMode_Normal, AlertMode alertMode = AlertMode_Normal) OVERRIDE;

private:
   CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;
   CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionManagerInterface* mSipDialogEventSubscriptionIf;
   CPCAPI2::SipConversation::SipAVConversationManagerInterface* mSipConversationIf;
   SipBusyLampFieldStateManager *mStateManager;
   AccountMap mAccountMap;
   PhoneInterface* mPhone;
   std::map<CPCAPI2::SipAccount::SipAccountHandle, SipBusyLampFieldHandler*> mHandlers;
   resip::RecursiveMutex mMutex;

   // PhoneModule interface
   virtual void Release() OVERRIDE;

   void setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipBusyLampFieldHandler* handler);
   void createBusyLampFieldRemoteLineSetImpl(SipAccount::SipAccountHandle account, SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineSetSettings& settings);
   void addRemoteLineImpl(SipBusyLampFieldRemoteLineSetHandle remoteLineSet, const SipBusyLampFieldRemoteLineHandle& remoteLine);
   void startImpl(SipBusyLampFieldRemoteLineSetHandle remoteLineSet);
   void endImpl(SipBusyLampFieldRemoteLineSetHandle remoteLineSet);
   SipBusyLampFieldManagerImpl* getBusyLampFieldManager(CPCAPI2::SipAccount::SipAccountHandle account) const;
   SipBusyLampFieldRemoteLineSetInfo* getRemoteLineSetInfo(SipBusyLampFieldRemoteLineSetHandle remoteLineSet) const;
};

std::ostream& operator<<(std::ostream& os, const RemoteLineNewSubscriptionEvent& evt);
std::ostream& operator<<(std::ostream& os, const RemoteLineSubscriptionStateChangedEvent& evt);
std::ostream& operator<<(std::ostream& os, const RemoteLineSubscriptionEndedEvent& evt);
std::ostream& operator<<(std::ostream& os, const RemoteLineStateChangedEvent& evt);
std::ostream& operator<<(std::ostream& os, const RemoteLineEmptySubscriptionsEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipBusyLampField::ErrorEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipBusyLampFieldRemoteLineCallInfo& evt);
}
}

#endif // __CPCAPI2_SIP_BUSY_LAMP_FIELD_MANAGER_INTERFACE_H__
