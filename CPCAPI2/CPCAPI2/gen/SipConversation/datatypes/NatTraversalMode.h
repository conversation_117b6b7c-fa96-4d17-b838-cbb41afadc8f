#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_NATTRAVERSALMODE_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_NATTRAVERSALMODE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  enum class NatTraversalMode
  {
    None,
    Auto,
    Stun,
    Turn,
    Ice
  };

  inline std::string to_string(NatTraversalMode value)
  {
    switch (value)
    {
      case NatTraversalMode::None:
        return "None";
      case NatTraversalMode::Auto:
        return "Auto";
      case NatTraversalMode::Stun:
        return "STUN";
      case NatTraversalMode::Turn:
        return "TURN";
      case NatTraversalMode::Ice:
        return "ICE";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, NatTraversalMode value)
  {
    switch (value)
    {
      case NatTraversalMode::None:
        os << "None";
        break;
      case NatTraversalMode::Auto:
        os << "Auto";
        break;
      case NatTraversalMode::Stun:
        os << "STUN";
        break;
      case NatTraversalMode::Turn:
        os << "TURN";
        break;
      case NatTraversalMode::Ice:
        os << "ICE";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const NatTraversalMode& e)
  {
    switch (e)
    {
      case NatTraversalMode::None:
        j = "None";
        break;
      case NatTraversalMode::Auto:
        j = "Auto";
        break;
      case NatTraversalMode::Stun:
        j = "STUN";
        break;
      case NatTraversalMode::Turn:
        j = "TURN";
        break;
      case NatTraversalMode::Ice:
        j = "ICE";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, NatTraversalMode& e)
  {
      if (0 == j.get<std::string>().compare("None"))
    {
      e = NatTraversalMode::None;
    }
    else if (0 == j.get<std::string>().compare("Auto"))
    {
      e = NatTraversalMode::Auto;
    }
    else if (0 == j.get<std::string>().compare("STUN"))
    {
      e = NatTraversalMode::Stun;
    }
    else if (0 == j.get<std::string>().compare("TURN"))
    {
      e = NatTraversalMode::Turn;
    }
    else if (0 == j.get<std::string>().compare("ICE"))
    {
      e = NatTraversalMode::Ice;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum NatTraversalMode"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_NATTRAVERSALMODE_H
