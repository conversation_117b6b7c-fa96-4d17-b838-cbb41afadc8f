#pragma once

#if !defined(__CPCAPI2_IMDN_DOCUMENT_H__)
#define __CPCAPI2_IMDN_DOCUMENT_H__

#include "cpm/CpmTypes.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{
namespace CPM
{

/**
 * XML parser and generator for the IMDN format (RFC 5438)
 */
class ImdnDocument
{
public:
   ImdnDocument();
   ~ImdnDocument();

   cpc::string getMessageId() const { return messageId; }
   cpc::string getDateTime() const { return dateTime; }
   bool isDeliveryNotification() const { return deliveryNotification; }
   bool isDisplayNotification() const { return displayNotification; }
   MessageDeliveryStatus getDeliveryNotificationStatus() { return deliveryNotificationStatus; }
   MessageDisplayStatus getDisplayNotificationStatus() { return displayNotificationStatus; }

   void setMessageId(const cpc::string& messageId) { this->messageId = messageId; }
   void setDateTime(const cpc::string& dateTime) { this->dateTime = dateTime; }
   void setRecipientUri(const cpc::string& recipientUri) { this->recipientUri = recipientUri; }
   void setOriginalRecipientUri(const cpc::string& originalRecipientUri) { this->originalRecipientUri = originalRecipientUri; }
   void setDeliveryNotificationStatus(MessageDeliveryStatus deliveryStatus);
   void setDisplayNotificationStatus(MessageDisplayStatus displayStatus);

   cpc::string toString() const;

   static ImdnDocument parse(const cpc::string& xml);

private:
   cpc::string messageId;
   cpc::string dateTime;
   cpc::string recipientUri;
   cpc::string originalRecipientUri;
   bool deliveryNotification;
   bool displayNotification;
   MessageDeliveryStatus deliveryNotificationStatus;
   MessageDisplayStatus displayNotificationStatus;
};

}
}

#endif // __CPCAPI2_IMDN_DOCUMENT_H__