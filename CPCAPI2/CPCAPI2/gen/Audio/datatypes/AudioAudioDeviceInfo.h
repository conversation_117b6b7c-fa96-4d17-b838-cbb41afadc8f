#include "jsonrpc/StringSerialization.h"
#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>
#include "AudioAudioDeviceRole.h"
#include "AudioAudioDeviceType.h"

#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIODEVICEINFO_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIODEVICEINFO_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
  class AudioAudioDeviceInfo
  {
  public:
    std::optional<cpc::string> friendlyName;
    std::optional<cpc::string> hid;
    std::optional<int32_t> id;
    std::optional<AudioAudioDeviceRole> role;
    std::optional<AudioAudioDeviceType> deviceType;

    AudioAudioDeviceInfo() = default;

    AudioAudioDeviceInfo(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const AudioAudioDeviceInfo& o)
    {
      j = nlohmann::json::object();
       if (o.friendlyName) { j["friendlyName"] = *(o.friendlyName); }
     if (o.hid) { j["hid"] = *(o.hid); }
     if (o.id) { j["id"] = *(o.id); }
     if (o.role) { j["role"] = *(o.role); }
     if (o.deviceType) { j["deviceType"] = *(o.deviceType); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, AudioAudioDeviceInfo& o)
    {
     if (j.contains("friendlyName")) { o.friendlyName = j.at("friendlyName"); }
     if (j.contains("hid")) { o.hid = j.at("hid"); }
     if (j.contains("id")) { o.id = j.at("id"); }
     if (j.contains("role")) { o.role = j.at("role"); }
     if (j.contains("deviceType")) { o.deviceType = j.at("deviceType"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOAUDIODEVICEINFO_H
