#include "AppDialogSetFactory.h"
#include <algorithm>

using namespace CPCAPI2::SipAccount;

resip::AppDialogSet* AppDialogSetFactory::createAppDialogSet( resip::DialogUsageManager& dum, const resip::SipMessage& msg )
{
   for( std::list<AppDialogFactoryDelegatePtr>::iterator it = mDelegates.begin() ; it != mDelegates.end() ; ++it )
   {
      AppDialogFactoryDelegatePtr pDelegate = *it;
      if( pDelegate == NULL )
         continue;

      if( pDelegate->isMyMessage( msg ))
         return pDelegate->createAppDialogSet( dum, msg );
   }
   return NULL; // ?
}

void AppDialogSetFactory::addDelegate( AppDialogFactoryDelegatePtr pDelegate, bool addAtEnd )
{
   if( std::find( mDelegates.begin(), mDelegates.end(), pDelegate ) != mDelegates.end() )
      return;

   if( addAtEnd )
      mDelegates.push_back( pDelegate );
   else
      mDelegates.push_front( pDelegate );
}
