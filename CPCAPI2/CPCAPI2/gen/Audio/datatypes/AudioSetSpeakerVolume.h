#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOSETSPEAKERVOLUME_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOSETSPEAKERVOLUME_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
  class AudioSetSpeakerVolume
  {
  public:
    int32_t phoneHandle;
    int32_t level;

    AudioSetSpeakerVolume() = default;

    AudioSetSpeakerVolume(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const AudioSetSpeakerVolume& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["level"] = o.level;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, AudioSetSpeakerVolume& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.level = j.at("level");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOSETSPEAKERVOLUME_H
