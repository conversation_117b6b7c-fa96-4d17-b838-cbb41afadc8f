#pragma once

#if !defined(CPCAPI2_JSON_API_SERVER_WEBSOCKET_H)
#define CPCAPI2_JSON_API_SERVER_WEBSOCKET_H

#include "cpcapi2defs.h"
#include "json/JsonData.h"
#include "jsonapi/JsonApiServerModule.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerHandler.h"
#include "jsonapi/JsonApiServerInternal.h"

#include <rutil/MultiReactor.hxx>
#include <contrib/folly/ProducerConsumerQueue.h>
#include <rutil/Data.hxx>
#include <rutil/DeadlineTimer.hxx>

#if !defined(DISABLE_WSS_JSON_SERVER)
#include <websocketpp/config/asio.hpp>
#else
#include <websocketpp/config/asio_no_tls.hpp>
#endif

#include <websocketpp/server.hpp>
#include <websocketpp/logger/stub.hpp>

#include <iostream>
#include <set>
#include <thread>
#include <memory>
#include <atomic>
#include <mutex>
#include <condition_variable>

#if !defined(_WIN32) && !defined(ANDROID)
#include <mm_malloc.h>
#endif

namespace CPCAPI2
{
class PhoneInterface;
class LocalLogger;

namespace JsonApi
{
class JsonApiServerInterface;
class JsonApiServerWebSocket;
class JsonApiLogSqliteAccess;
struct JsonApiUserContext;

struct asio_tls_nologs_config : public websocketpp::config::asio_tls
{
   typedef asio_tls_nologs_config type;
   typedef core base;

   typedef base::concurrency_type concurrency_type;

   typedef base::request_type request_type;
   typedef base::response_type response_type;

   typedef base::message_type message_type;
   typedef base::con_msg_manager_type con_msg_manager_type;
   typedef base::endpoint_msg_manager_type endpoint_msg_manager_type;

   // DISABLE all websocketpp logging -- the base implementation makes system calls and locks mutexes
   typedef websocketpp::log::stub alog_type;
   typedef websocketpp::log::stub elog_type;

   typedef base::rng_type rng_type;

   struct transport_config : public base::transport_config {
      typedef type::concurrency_type concurrency_type;
      typedef websocketpp::log::stub alog_type;
      typedef websocketpp::log::stub elog_type;
      typedef type::request_type request_type;
      typedef type::response_type response_type;
      typedef websocketpp::transport::asio::tls_socket::endpoint socket_type;
   };

   typedef websocketpp::transport::asio::endpoint<transport_config>
      transport_type;
};

struct asio_nologs_config : public websocketpp::config::asio
{
   typedef asio_nologs_config type;
   typedef core base;

   typedef base::concurrency_type concurrency_type;

   typedef base::request_type request_type;
   typedef base::response_type response_type;

   typedef base::message_type message_type;
   typedef base::con_msg_manager_type con_msg_manager_type;
   typedef base::endpoint_msg_manager_type endpoint_msg_manager_type;

   // DISABLE all websocketpp logging -- the base implementation makes system calls and locks mutexes
   typedef websocketpp::log::stub alog_type;
   typedef websocketpp::log::stub elog_type;

   typedef base::rng_type rng_type;

   struct transport_config : public base::transport_config {
      typedef type::concurrency_type concurrency_type;
      typedef websocketpp::log::stub alog_type;
      typedef websocketpp::log::stub elog_type;
      typedef type::request_type request_type;
      typedef type::response_type response_type;
      typedef websocketpp::transport::asio::basic_socket::endpoint
         socket_type;
   };

   typedef websocketpp::transport::asio::endpoint<transport_config>
      transport_type;

};

typedef websocketpp::server<asio_tls_nologs_config> server_tls;
typedef websocketpp::server<asio_nologs_config> server;

typedef server::connection_ptr conn_ptr;
typedef server_tls::connection_ptr tls_conn_ptr;
typedef server::message_ptr message_ptr;
typedef server_tls::message_ptr tls_message_ptr;

class JsonApiWebSocketSession : public resip::ReactorEventHandler
{

public:

   JsonApiWebSocketSession(CPCAPI2::PhoneInterface* masterSdkPhone, JsonApiServerWebSocket* transport, boost::asio::io_context& ioContext, const websocketpp::connection_hdl& conn, const std::string& resource, bool isLocal);
   virtual ~JsonApiWebSocketSession();

   void* operator new(size_t i)
   {
#if defined(ANDROID)
      return memalign(folly::hardware_destructive_interference_size, i);
#else
      return _mm_malloc(i, folly::hardware_destructive_interference_size);
#endif
   }

   void operator delete(void* p)
   {
#if defined(ANDROID)
      free(p);
#else
      _mm_free(p);
#endif
   }

   const websocketpp::connection_hdl& websocket_conn_hdl() const {
      return mConn;
   }

   const JsonApiUserContext* json_api_user_context() const {
      return mSdkContext;
   }

   bool is_local_client() const {
      return mIsLoopbackConnection;
   }

   const AuthToken& auth_token() const {
      return mAuthToken;
   }

   const std::string& resource() const {
      return mResource;
   }

   const std::string& remoteUri() const {
      return mRemoteUri;
   }

   const bool isLoggedOut() { return mIsLoggedOut; }

   void close(bool force=false);
   void logout();

   void on_close(server* s, websocketpp::connection_hdl hdl);
   void on_close_tls(server_tls* s, websocketpp::connection_hdl hdl);
   void on_fail(server* s, websocketpp::connection_hdl hdl);
   void on_fail_tls(server_tls* s, websocketpp::connection_hdl hdl);
   void on_termination(server* s, conn_ptr conn);
   void on_termination_tls(server_tls* s, tls_conn_ptr conn);
   void on_message(server* s, websocketpp::connection_hdl hdl, message_ptr msg);
   void on_message_tls(server_tls* s, websocketpp::connection_hdl hdl, tls_message_ptr msg);
   void on_message_base(websocketpp::connection_hdl hdl, const std::string& payloadData, const websocketpp::uri_ptr& requestUri);

   // resip::ReactorEventHandler
   virtual void process(resip::ReactorEventHandler::FdSetType& fdset) OVERRIDE;
   virtual void buildFdSet(resip::ReactorEventHandler::FdSetType& fdset) OVERRIDE;
   virtual unsigned int getTimeTillNextProcessMS() OVERRIDE;
   virtual const char* getEventHandlerDesc() const OVERRIDE;

   void setSdkContext(JsonApiUserContext* sdkContext);
   void setAuthToken(const AuthToken& authToken);

private:

   void handleJsonApiServerMessage(websocketpp::connection_hdl hdl, const std::shared_ptr<rapidjson::Document>& doc);

   struct SdkMessage
   {
      enum SdkMessageType
      {
         FuncObj,
         Close
      };
      SdkMessageType type;
      websocketpp::connection_hdl hdl;
      std::string moduleIdStr;
      std::shared_ptr<rapidjson::Document> jsonDoc;
      std::shared_ptr<resip::Data> binaryData;
      std::string websocketUrlResource;

      SdkMessage(SdkMessageType _type, const websocketpp::connection_hdl& _hdl, const std::string& _moduleId, const std::shared_ptr<rapidjson::Document>& _json, const std::shared_ptr<resip::Data>& _binaryData, const std::string& urlRes)
         : type(_type),
           hdl(_hdl),
           moduleIdStr(_moduleId),
           jsonDoc(_json),
           binaryData(_binaryData),
           websocketUrlResource(urlRes)
      {
      }
   };
   void handleMessage(SdkMessage* sdkMsg);
   void handleClose(SdkMessage* sdkMsg);
   void resetHandlers(server* s, websocketpp::connection_hdl hdl);
   void resetHandlersTls(server_tls* s, websocketpp::connection_hdl hdl);

   void onRecvTimer(const boost::system::error_code& ec);

   void cleanupOnClose();

   std::string mRemoteUri;
   websocketpp::connection_hdl mConn;

   folly::ProducerConsumerQueue<SdkMessage> mMsgQueue;
   CPCAPI2::PhoneInterface* mMasterPhone;
   JsonApiServerInterface* mJsonApiServer;

   resip::SelectInterruptor mSdkThreadInterrupt;

   JsonApiUserContext* mSdkContext;
   JsonApiServerWebSocket* mTransport;
   resip::Data mUserIdentity;
   AuthToken mAuthToken;
   bool mIsLoopbackConnection;
   bool mIsLoggedOut;
   std::string mResource;
   LocalLogger* mLocalLogger;
   boost::asio::deadline_timer mRecvTimer;

};

class JsonApiServerWebSocket
{
public:
   JsonApiServerWebSocket(CPCAPI2::PhoneInterface* phone);
   virtual ~JsonApiServerWebSocket();

   void startServer(const JsonApiServerConfig& serverConfig, std::function<void()> onPrivilegedAccessCompleted);
   void stopServer();
   void waitForShutdown();

   int send(const CPCAPI2::Json::JsonDataPointer& jsonData, const websocketpp::connection_hdl& hdl);
   int send(const std::function<void(std::string&)>& buff_fill_fn, size_t size_hint, const websocketpp::connection_hdl& hdl);
   int closeConnection(const websocketpp::connection_hdl& hdl, bool force=false);

   JsonApiLogSqliteAccess* getApiLog() {
      return mApiLogDb.get();
   }

private:
   typedef std::map<websocketpp::connection_hdl, JsonApiWebSocketSession*, std::owner_less<websocketpp::connection_hdl> > MapConnToWsSession;

   void on_open(server* s, websocketpp::connection_hdl hdl);
   void on_open_tls(server_tls* s, websocketpp::connection_hdl hdl);

   void on_tls_socket_init(websocketpp::connection_hdl, boost::asio::ssl::stream<boost::asio::ip::tcp::socket> & s);
   void on_socket_init(websocketpp::connection_hdl, boost::asio::ip::tcp::socket& s);

   websocketpp::lib::shared_ptr<boost::asio::ssl::context> on_tls_init(server_tls* s, websocketpp::connection_hdl hdl);
   std::string get_password() const;
   message_ptr get_message_ptr(websocketpp::connection_hdl hdl, size_t msgsize);
   tls_message_ptr get_tls_message_ptr(websocketpp::connection_hdl hdl, size_t msgsize);

private:

   std::unique_ptr<server> mWebSockServer;
   std::unique_ptr<server_tls> mSecureWebSockServer;
   std::thread* mServerThread;
   CPCAPI2::PhoneInterface* mPhone;
   JsonApiServerConfig mConfig;
   std::mutex mStopServerDoneMutex;
   std::condition_variable mStopServerDoneCv;
   bool mStopServerDone;

   class LogStream : public std::streambuf
   {
   private:
      std::string buffer;

   protected:
      int overflow(int ch) OVERRIDE;
   };
   LogStream mResipLogStream;
   std::ostream mResipOStream;

   std::unique_ptr<JsonApiLogSqliteAccess> mApiLogDb;
   websocketpp::lib::shared_ptr<boost::asio::ssl::context> mTlsContext;

   std::function<void()> mOnPrivilegedAccessCompleted;

};

}

}

#endif // CPCAPI2_JSON_API_SERVER_WEBSOCKET_H
