#pragma once

#if !defined(CPCAPI2_ACCOUNT_INTERFACE_H)
#define CPCAPI2_ACCOUNT_INTERFACE_H

#include "cpcapi2defs.h"
#include "account/SipAccount.h"
#include "account/SipAccountHandler.h"
#include "account/SipAccountManagerInternal.h"
#include "call/SipConversationHandler.h"
#include "call/SipConversationHandlerInternal.h"
#include "SipAccountHandlerInternal.h"
#include "../util/cpc_thread.h"
#include "../util/DumFpCommand.h"
#include "../phone/NetworkChangeManagerImpl.h"
#include "../phone/PhoneModule.h"

#ifdef CPCAPI2_AUTO_TEST
#include "account/CPDialogDnsResultManager.h"
#include "../interface/experimental/account/SipNetworkProbeHandler.h"
#include "../util/AutoTestProcessor.h"
#endif

#include <map>
#include <list>
#include <optional>

#include <rutil/Condition.hxx>
#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>
#include <rutil/SelectInterruptor.hxx>
#include <resip/dum/DumCommand.hxx>
#include <rutil/SharedPtr.hxx>

namespace CPCAPI2
{
class PhoneInterface;

namespace SipAccount
{
class SipAccountImpl;

struct SipAccountAdornmentInternalEvent : SipAccountAdornmentEvent
{
   cpc::vector<CPCAPI2::SipHeader> customHeaders;
};

class SipAccountAdornmentInternalHandler
{
public:
   virtual int onAccountAdornment(SipAccount::SipAccountHandle account, SipAccountAdornmentInternalEvent& args) = 0;
};

class SipAccountInterface : public EventSource2< EventHandler<SipAccount::SipAccountHandler, SipAccount::SipAccountHandle> >,
                            public CPCAPI2::EventSyncHandler<CPCAPI2::SipConversation::SipConversationHandlerAdaptor>,
                            public SipAccountManager,
                            public PhoneModule,
                            public SipAccountManagerInternal,
                            public SipAccountAdornmentInternalHandler,
                            public CPCAPI2::EventSyncHandler<NetworkChangeHandler>
#ifdef CPCAPI2_AUTO_TEST
                          , public SipNetworkProbeHandler
#endif
{
public:
   SipAccountInterface(Phone* cpcPhone);
   virtual ~SipAccountInterface();

   FORWARD_EVENT_PROCESSOR(SipAccountInterface);

   virtual void PreRelease() OVERRIDE;
   virtual bool PreReleaseCompleted() OVERRIDE;
   virtual void Release() OVERRIDE;

   virtual CPCAPI2::SipAccount::SipAccountHandle create() OVERRIDE;
   virtual CPCAPI2::SipAccount::SipAccountHandle create(CPCAPI2::SipAccount::SipAccountHandle handle) OVERRIDE;
   virtual CPCAPI2::SipAccount::SipAccountHandle create(const SipAccountSettings& accountSettings, CPCAPI2::SipAccount::SipAccountHandle handle) OVERRIDE;
   virtual CPCAPI2::SipAccount::SipAccountHandle create(const SipAccountSettings& accountSettings) OVERRIDE;
   virtual int destroy(CPCAPI2::SipAccount::SipAccountHandle h) OVERRIDE;
   virtual int configureDefaultAccountSettings(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountSettings& sipAccountSettings) OVERRIDE;
   virtual int configureTransportAccountSettings(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountSettings& sipAccountSettings, CPCAPI2::NetworkTransport transport) OVERRIDE;
   virtual int applySettings(CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;

   virtual int enable(CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;
   virtual int disable(CPCAPI2::SipAccount::SipAccountHandle account, bool force = false) OVERRIDE;
   virtual int requestRegistrationRefresh(CPCAPI2::SipAccount::SipAccountHandle account, unsigned int deadline) OVERRIDE;
   virtual int setNetworkRestriction(CPCAPI2::SipAccount::SipAccountHandle account, NetworkTransport transport, bool restricted) OVERRIDE;

   virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipAccountHandler* handler) OVERRIDE;

   void fireError(const cpc::string& errorText);
   void firePhoneError(const cpc::string& errorText, const cpc::string& module);

   virtual void postToProcessThread(void (*pfun)(void*), void* obj) OVERRIDE;

#ifdef CPCAPI2_AUTO_TEST
   virtual int closeTransportConnections(CPCAPI2::SipAccount::SipAccountHandle handle) OVERRIDE;
   virtual int setFakeResponse(CPCAPI2::SipAccount::SipAccountHandle handle, bool enable, cpc::string method, int responseCode, std::string responseReason, int warningCode) OVERRIDE;

   // Probe status 
   virtual int setProbeHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipNetworkProbeHandler* handler) OVERRIDE;
   virtual int setProbeMockDelay(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::IpVersion ipVersion, unsigned int delayMsecs) OVERRIDE;
   virtual int onNetworkProbeStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent& args) OVERRIDE;

   // Dialog DNS result status
   virtual int setDnsHandler(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::DialogDnsResultHandler* handler) OVERRIDE;
   // Set the domain lock string
   virtual int setDomainLockString(const cpc::string& brandingString) OVERRIDE;
   cpc::string getDomainLockString() { return mOverrideDomainLockString; };
   virtual int setIgnoreNetworkChangeStarcodeFilter(CPCAPI2::SipAccount::SipAccountHandle account, bool enabled) OVERRIDE;
   virtual int setDecoratorHandler(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipAccountMessageDecoratorHandler* handler) OVERRIDE;
#endif

   virtual void setCallbackHook(void (*cbHook)(void*), void* context) OVERRIDE {};
   virtual int sendOptionsMessage(SipAccount::SipAccountHandle h, const cpc::string target) OVERRIDE;
   virtual int setSkipResetTransportOnNetworkChange(SipAccount::SipAccountHandle accountHandle, bool skip) OVERRIDE;

   SipAccountImpl* getAccountImpl(CPCAPI2::SipAccount::SipAccountHandle account);
   void accountDestroyed(CPCAPI2::SipAccount::SipAccountHandle h);

   // Adornment
   virtual int setAdornmentHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipAccountAdornmentHandler* handler) OVERRIDE;
   virtual int adornMessage(CPCAPI2::SipAccount::SipAccountHandle account, unsigned int adornmentMessageId, const cpc::vector<SipHeader>& customHeaders) OVERRIDE;

   cpc::vector<SipAccountHandle> getAccountHandles();
   PhoneInterface* phoneInterface() const {
      return mPhone;
   }

   // SIP timers
   virtual void setT1TimerValueMs(unsigned long t1ValueMs) OVERRIDE;
   virtual void setT2TimerValueMs(unsigned long t2ValueMs) OVERRIDE;
   virtual void setT4TimerValueMs(unsigned long t4ValueMs) OVERRIDE;
   virtual void setTDTimerValueMs(unsigned long tDValueMs) OVERRIDE;
   virtual void setTFTimerValueMs(unsigned long tFValueMs) OVERRIDE;
   virtual void setSipTotalTransactionTimeoutMs(unsigned long sipTotalTransactionTimeoutMs) OVERRIDE;
   virtual unsigned long getT1TimerValueMs() OVERRIDE;
   virtual unsigned long getT2TimerValueMs() OVERRIDE;
   virtual unsigned long getT4TimerValueMs() OVERRIDE;
   virtual unsigned long getTDTimerValueMs() OVERRIDE;
   virtual unsigned long getTFTimerValueMs() OVERRIDE;

   // SipAccountManagerInternal
   virtual int setSipAccountUseAlias(CPCAPI2::SipAccount::SipAccountHandle hdl, bool useAlias) OVERRIDE;
   virtual int setCertStorageLoadType(SipAccount::SipAccountHandle h, CertLoadStorageType type) OVERRIDE;
   virtual int setCertStorageFileSystemPath(SipAccount::SipAccountHandle h, const cpc::string& path) OVERRIDE;
   virtual int discardRegistration(CPCAPI2::SipAccount::SipAccountHandle hdl) OVERRIDE;
   virtual int decodeProvisioningResponse(const cpc::string& provisioningResponse, cpc::vector<SipAccountSettings>& outSipAccountSettings) OVERRIDE;

   // SipConversationHandlerAdaptor
   virtual int onConversationEnded(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationEndedEvent& args) OVERRIDE;

private:
   void initImpl();
   int onNetworkChange(const NetworkChangeEvent& params) OVERRIDE;
   int onNetworkChangeImpl(const NetworkChangeEvent& params);
   void handleNetworkChangeEvent(CPCAPI2::SipAccount::SipAccountHandle account, const NetworkChangeEvent& params);

   void createImpl(CPCAPI2::SipAccount::SipAccountHandle h);
   void createImplWithSettings(CPCAPI2::SipAccount::SipAccountHandle h, const SipAccountSettings& sipSipAccountSettings);
   void destroyImpl(CPCAPI2::SipAccount::SipAccountHandle h);
   void enableImpl(CPCAPI2::SipAccount::SipAccountHandle account);
   void disableImpl(CPCAPI2::SipAccount::SipAccountHandle account, bool force);
   void requestRegistrationRefreshImpl(CPCAPI2::SipAccount::SipAccountHandle account, unsigned int deadline);
   void setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipAccountHandler* handler);
   void configureDefaultAccountSettingsImpl(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountSettings& sipAccountSettings);
   void configureTransportAccountSettingsImpl(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountSettings& sipAccountSettings, CPCAPI2::NetworkTransport transport);
   void applySettingsImpl(CPCAPI2::SipAccount::SipAccountHandle account);
   void setNetworkRestrictionImpl(CPCAPI2::SipAccount::SipAccountHandle account, NetworkTransport transport, bool restricted);
   void setSipAccountUseAliasImpl(CPCAPI2::SipAccount::SipAccountHandle hdl, bool useAlias);
   void setCertStorageLoadTypeImpl(SipAccount::SipAccountHandle h, CertLoadStorageType type);
   void setCertStorageFileSystemPathImpl(SipAccount::SipAccountHandle h, const cpc::string& path);
   void discardRegistrationImpl(CPCAPI2::SipAccount::SipAccountHandle hdl);
   void tryCleanupAccount(CPCAPI2::SipAccount::SipAccountHandle h);
   void logNetworkInterfaces() const;
   void sendOptionsMessageImpl(SipAccount::SipAccountHandle h, const cpc::string target);
   void setSkipResetTransportOnNetworkChangeImpl(SipAccount::SipAccountHandle accountHandle, bool skip);
   void onConversationEndedImpl(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationEndedEvent& args);

#ifdef CPCAPI2_AUTO_TEST
   int closeTransportConnectionsImpl(CPCAPI2::SipAccount::SipAccountHandle handle);
   int setFakeResponseImpl(CPCAPI2::SipAccount::SipAccountHandle handle, bool enable, cpc::string method, int responseCode, std::string responseReason, int warningCode);

   // Probe status 
   int setProbeHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipNetworkProbeHandler* handler);
   int setProbeMockDelayImpl(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::IpVersion ipVersion, unsigned int delayMsecs);

   typedef std::map<unsigned int, CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent*> ProbeEventMap;
   ProbeEventMap mProbeEventMap;

   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, CPCAPI2::SipAccount::SipNetworkProbeHandler*> ProbeHandlerMap;
   ProbeHandlerMap mProbeHandlerMap;

   // Dialog DNS result status
   int setDnsHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::DialogDnsResultHandler* handler);

   //domain lock string, used for tests only
   cpc::string mOverrideDomainLockString;
   int setDomainLockStringImpl(const cpc::string& brandingString);
   int setIgnoreNetworkChangeStarcodeFilterImpl(CPCAPI2::SipAccount::SipAccountHandle account, bool enabled);

   int setDecoratorHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipAccountMessageDecoratorHandler* handler);
#endif

   // Adornment
   int setAdornmentHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipAccountAdornmentHandler* handler);
   virtual int onAccountAdornment(CPCAPI2::SipAccount::SipAccountHandle account, SipAccountAdornmentInternalEvent& args) OVERRIDE;

   // PhoneModule
   virtual void onLicensingError() OVERRIDE;

private:
   CPCAPI2::SipAccount::SipAccountHandle mNextSipAccountHandle;
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, SipAccountImpl*> AccountMap;
   AccountMap mAccountMap;
   struct AcccountHandleHolder
   {
      CPCAPI2::SipAccount::SipAccountHandle h;
   };
   std::map<SipAccountHandle, SipAccountHandler*> mHandlers;
   bool mShutdown;
   PhoneInterface* mPhone;
   NetworkChangeManagerInterface* mNetworkChangeManagerIf;
   std::map<CPCAPI2::SipAccount::SipAccountHandle, CPCAPI2::NetworkChangeEvent> mIgnoredNetworkChanges;
   std::function<void(void)> mCbHook;
   int mReleased;
   int mProcessLogTicks;

   typedef std::map<unsigned int, SipAccountAdornmentInternalEvent*> AdornmentEventMap;
   AdornmentEventMap mAdornmentEventMap;

   typedef std::map<SipAccount::SipAccountHandle, SipAccountAdornmentHandler*> AdornmentHandlerMap;
   AdornmentHandlerMap mAdornmentHandlerMap;

   std::optional<NetworkChangeEvent> mLastNetworkChangeEvent;
};

std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountTransportType& transport);
std::ostream& operator<<(std::ostream& os, const SipAccount::IpVersion& ip);
std::ostream& operator<<(std::ostream& os, const SipAccount::SSLVersion& ssl);
std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountStatusChangedEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipAccount::ErrorEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipAccount::LicensingErrorEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountConfiguredEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountEnabledEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountDestroyedEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountClientAuthEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountNetworkChangeEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountDnsResetEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipAccount::SipAccountRegistrationRportUpdateEvent& evt);

}

}

#endif // CPCAPI2_ACCOUNT_INTERFACE_H
