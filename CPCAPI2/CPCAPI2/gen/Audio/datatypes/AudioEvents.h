#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOEVENTS_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOEVENTS_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
  enum class AudioEvents
  {
    AudioDotOnAudioDeviceListUpdated,
    AudioDotOnPlaySoundComplete,
    AudioDotOnPlaySoundFailure,
    AudioDotOnAudioCodecListUpdated,
    AudioDotOnAudioDeviceVolume,
    AudioDotOnAudioDeviceLevelChange
  };

  inline std::string to_string(AudioEvents value)
  {
    switch (value)
    {
      case AudioEvents::AudioDotOnAudioDeviceListUpdated:
        return "Audio.onAudioDeviceListUpdated";
      case AudioEvents::AudioDotOnPlaySoundComplete:
        return "Audio.onPlaySoundComplete";
      case AudioEvents::AudioDotOnPlaySoundFailure:
        return "Audio.onPlaySoundFailure";
      case AudioEvents::AudioDotOnAudioCodecListUpdated:
        return "Audio.onAudioCodecListUpdated";
      case AudioEvents::AudioDotOnAudioDeviceVolume:
        return "Audio.onAudioDeviceVolume";
      case AudioEvents::AudioDotOnAudioDeviceLevelChange:
        return "Audio.onAudioDeviceLevelChange";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, AudioEvents value)
  {
    switch (value)
    {
      case AudioEvents::AudioDotOnAudioDeviceListUpdated:
        os << "Audio.onAudioDeviceListUpdated";
        break;
      case AudioEvents::AudioDotOnPlaySoundComplete:
        os << "Audio.onPlaySoundComplete";
        break;
      case AudioEvents::AudioDotOnPlaySoundFailure:
        os << "Audio.onPlaySoundFailure";
        break;
      case AudioEvents::AudioDotOnAudioCodecListUpdated:
        os << "Audio.onAudioCodecListUpdated";
        break;
      case AudioEvents::AudioDotOnAudioDeviceVolume:
        os << "Audio.onAudioDeviceVolume";
        break;
      case AudioEvents::AudioDotOnAudioDeviceLevelChange:
        os << "Audio.onAudioDeviceLevelChange";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const AudioEvents& e)
  {
    switch (e)
    {
      case AudioEvents::AudioDotOnAudioDeviceListUpdated:
        j = "Audio.onAudioDeviceListUpdated";
        break;
      case AudioEvents::AudioDotOnPlaySoundComplete:
        j = "Audio.onPlaySoundComplete";
        break;
      case AudioEvents::AudioDotOnPlaySoundFailure:
        j = "Audio.onPlaySoundFailure";
        break;
      case AudioEvents::AudioDotOnAudioCodecListUpdated:
        j = "Audio.onAudioCodecListUpdated";
        break;
      case AudioEvents::AudioDotOnAudioDeviceVolume:
        j = "Audio.onAudioDeviceVolume";
        break;
      case AudioEvents::AudioDotOnAudioDeviceLevelChange:
        j = "Audio.onAudioDeviceLevelChange";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, AudioEvents& e)
  {
      if (0 == j.get<std::string>().compare("Audio.onAudioDeviceListUpdated"))
    {
      e = AudioEvents::AudioDotOnAudioDeviceListUpdated;
    }
    else if (0 == j.get<std::string>().compare("Audio.onPlaySoundComplete"))
    {
      e = AudioEvents::AudioDotOnPlaySoundComplete;
    }
    else if (0 == j.get<std::string>().compare("Audio.onPlaySoundFailure"))
    {
      e = AudioEvents::AudioDotOnPlaySoundFailure;
    }
    else if (0 == j.get<std::string>().compare("Audio.onAudioCodecListUpdated"))
    {
      e = AudioEvents::AudioDotOnAudioCodecListUpdated;
    }
    else if (0 == j.get<std::string>().compare("Audio.onAudioDeviceVolume"))
    {
      e = AudioEvents::AudioDotOnAudioDeviceVolume;
    }
    else if (0 == j.get<std::string>().compare("Audio.onAudioDeviceLevelChange"))
    {
      e = AudioEvents::AudioDotOnAudioDeviceLevelChange;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum AudioEvents"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOEVENTS_H
