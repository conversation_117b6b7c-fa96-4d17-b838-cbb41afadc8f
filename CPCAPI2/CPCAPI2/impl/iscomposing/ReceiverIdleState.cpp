#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_CHAT_MODULE==1 || CPCAPI2_BRAND_IM_MODULE==1 || CPCAPI2_BRAND_XMPP_CHAT_MODULE==1 || CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
#include "ReceiverIdleState.h"
#include "ReceiverActiveState.h"
#include "IsComposingManager.h"
#include "IsComposingInfo.h"

namespace CPCAPI2
{
namespace IsComposing
{

ReceiverIdleState ReceiverIdleState::instance;

void ReceiverIdleState::onActiveStateReceived(IsComposingManager* manager, IsComposingInfo* info)
{
   // Invoke the callback with Active state
   manager->onIsComposingMessage(info, IsComposingMessageState_Active, info->receiverContentType, info->receiverLastActive);

   // Transition to the Active state
   manager->transitionTo(info, &ReceiverActiveState::instance);
}

void ReceiverIdleState::onIdleStateReceived(IsComposingManager* manager, IsComposingInfo* info)
{
   // Don't send any notification
   // Stay in this state
}

void ReceiverIdleState::onMessageReceived(IsComposingManager* manager, IsComposingInfo* info)
{
   // Don't send any notification
   // Stay in this state
}

}
}
#endif
