#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSETCALLKITMODE_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSETCALLKITMODE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationSetCallKitMode
  {
  public:
    int32_t phoneHandle;
    int32_t conversationHandle;

    SipConversationSetCallKitMode() = default;

    SipConversationSetCallKitMode(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationSetCallKitMode& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["conversationHandle"] = o.conversationHandle;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationSetCallKitMode& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.conversationHandle = j.at("conversationHandle");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSETCALLKITMODE_H
