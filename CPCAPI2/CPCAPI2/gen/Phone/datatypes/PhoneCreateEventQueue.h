#include "jsonrpc/StringSerialization.h"
#include "jsonrpc/VectorSerialization.h"
#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_PHONE_PHONECREATEEVENTQUEUE_H
#define JSONRPC_CPCAPI2_PHONE_PHONECREATEEVENTQUEUE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  class PhoneCreateEventQueue
  {
  public:
    int32_t handle;
    std::optional<cpc::vector<cpc::string>> events;

    PhoneCreateEventQueue() = default;

    PhoneCreateEventQueue(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const PhoneCreateEventQueue& o)
    {
      j = nlohmann::json::object();
       j["handle"] = o.handle;
     if (o.events) { j["events"] = *(o.events); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, PhoneCreateEventQueue& o)
    {
     o.handle = j.at("handle");
     if (j.contains("events")) { o.events = j.at("events"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_PHONECREATEEVENTQUEUE_H
