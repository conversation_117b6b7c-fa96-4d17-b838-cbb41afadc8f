#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOSETCODECENABLED_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOSETCODECENABLED_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
  class AudioSetCodecEnabled
  {
  public:
    int32_t phoneHandle;
    int32_t codecId;
    bool enabled;

    AudioSetCodecEnabled() = default;

    AudioSetCodecEnabled(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const AudioSetCodecEnabled& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["codecId"] = o.codecId;
     j["enabled"] = o.enabled;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, AudioSetCodecEnabled& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.codecId = j.at("codecId");
     o.enabled = j.at("enabled");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOSETCODECENABLED_H
