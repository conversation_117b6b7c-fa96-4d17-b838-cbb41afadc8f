#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOSTOPPLAYSOUND_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOSTOPPLAYSOUND_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
  class AudioStopPlaySound
  {
  public:
    int32_t phoneHandle;
    int32_t sound;

    AudioStopPlaySound() = default;

    AudioStopPlaySound(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const AudioStopPlaySound& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["sound"] = o.sound;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, AudioStopPlaySound& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.sound = j.at("sound");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOSTOPPLAYSOUND_H
