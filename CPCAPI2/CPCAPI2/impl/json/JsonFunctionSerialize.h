#pragma once

#ifndef JSON_FUNCTION_SERIALIZE_HELPER_H
#define JSON_FUNCTION_SERIALIZE_HELPER_H

#include "cpcapi2defs.h"

#include "jsonapi/JsonApiTransport.h"
#include "json/JsonDataImpl.h"

#include "JsonDataTypeHelpers.h"

#define JsonFunctionCall(transport, ...) CPCAPI2::Json::JsonFunctionCallImpl(transport, false, JSON_MODULE, __VA_ARGS__)
#define JsonFunctionCallNoLog(transport, ...) CPCAPI2::Json::JsonFunctionCallImpl(transport, true, JSON_MODULE, __VA_ARGS__)

namespace CPCAPI2
{
namespace Json
{
   class JsonFunctionSerialize
   {
   public:
      JsonFunctionSerialize(const CPCAPI2::Json::JsonDataPointer& json, bool suppressLog, const char* moduleId, const char* funcName, bool noOverhead = false) :
            finalized(false), simple(noOverhead)
      {
         writer.Reset( ((JsonDataImpl*)json.get())->getStringBuffer() );
         writer.StartObject();

         if (!simple)
         {
            writer.Key("moduleId");
            writer.String(moduleId);
         }

         if (suppressLog)
         {
            writer.Key("suppressLog");
            writer.Bool(suppressLog);
         }

         if (!simple)
         {
            writer.Key("functionObject");
            writer.StartObject();
         }

         writer.Key("functionName");
         writer.String(funcName);
      }

      rapidjson::Writer<rapidjson::StringBuffer>& getWriter()
      {
         return writer;
      }

      template<class T>
      void addValue(const char* name, const T& value)
      {
         if (name)
         {
            Json::Write(writer, name, value);
         }
      }

      void finalize()
      {
         assert(!finalized);

         if (!simple)
         {
            writer.EndObject(); // Function Object
         }

         writer.EndObject(); // Command Object
         finalized = true;
      }

   private:
      rapidjson::Writer<rapidjson::StringBuffer> writer;
      bool finalized;
      bool simple;
   };


   class JsonFunctionCallImpl
   {
   public:
      JsonFunctionCallImpl(CPCAPI2::JsonApi::JsonApiTransport* transport, bool suppressLog, const char* moduleId, const char* functionName)
      {
         JsonDataPointer json = MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, suppressLog, moduleId, functionName);
         serializer.finalize();
         transport->send(json, suppressLog);
      }

      template<
        class T0
      >
      JsonFunctionCallImpl(CPCAPI2::JsonApi::JsonApiTransport* transport, bool suppressLog, const char* moduleId, const char* functionName,
         const char* name0, const T0& arg0
      )
      {
         JsonDataPointer json = MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, suppressLog, moduleId, functionName);
         serializer.addValue(name0, arg0);
         serializer.finalize();
         transport->send(json, suppressLog);
      }

      template<
        class T0, class T1
      >
      JsonFunctionCallImpl(CPCAPI2::JsonApi::JsonApiTransport* transport, bool suppressLog, const char* moduleId, const char* functionName,
         const char* name0, const T0& arg0,
         const char* name1, const T1& arg1
      )
      {
         JsonDataPointer json = MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, suppressLog, moduleId, functionName);
         serializer.addValue(name0, arg0);
         serializer.addValue(name1, arg1);
         serializer.finalize();
         transport->send(json, suppressLog);
      }

      template<
        class T0, class T1, class T2
      >
      JsonFunctionCallImpl(CPCAPI2::JsonApi::JsonApiTransport* transport, bool suppressLog, const char* moduleId, const char* functionName,
         const char* name0, const T0& arg0,
         const char* name1, const T1& arg1,
         const char* name2, const T2& arg2
      )
      {
         JsonDataPointer json = MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, suppressLog, moduleId, functionName);
         serializer.addValue(name0, arg0);
         serializer.addValue(name1, arg1);
         serializer.addValue(name2, arg2);
         serializer.finalize();
         transport->send(json, suppressLog);
      }

      template<
        class T0, class T1, class T2, class T3
      >
      JsonFunctionCallImpl(CPCAPI2::JsonApi::JsonApiTransport* transport, bool suppressLog, const char* moduleId, const char* functionName,
         const char* name0, const T0& arg0,
         const char* name1, const T1& arg1,
         const char* name2, const T2& arg2,
         const char* name3, const T3& arg3
      )
      {
         JsonDataPointer json = MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, suppressLog, moduleId, functionName);
         serializer.addValue(name0, arg0);
         serializer.addValue(name1, arg1);
         serializer.addValue(name2, arg2);
         serializer.addValue(name3, arg3);
         serializer.finalize();
         transport->send(json, suppressLog);
      }

      template<
        class T0, class T1, class T2, class T3, class T4
      >
      JsonFunctionCallImpl(CPCAPI2::JsonApi::JsonApiTransport* transport, bool suppressLog, const char* moduleId, const char* functionName,
         const char* name0, const T0& arg0,
         const char* name1, const T1& arg1,
         const char* name2, const T2& arg2,
         const char* name3, const T3& arg3,
         const char* name4, const T4& arg4
      )
      {
         JsonDataPointer json = MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, suppressLog, moduleId, functionName);
         serializer.addValue(name0, arg0);
         serializer.addValue(name1, arg1);
         serializer.addValue(name2, arg2);
         serializer.addValue(name3, arg3);
         serializer.addValue(name4, arg4);
         serializer.finalize();
         transport->send(json, suppressLog);
      }

      template<
        class T0, class T1, class T2, class T3, class T4,
        class T5
      >
      JsonFunctionCallImpl(CPCAPI2::JsonApi::JsonApiTransport* transport, bool suppressLog, const char* moduleId, const char* functionName,
         const char* name0, const T0& arg0,
         const char* name1, const T1& arg1,
         const char* name2, const T2& arg2,
         const char* name3, const T3& arg3,
         const char* name4, const T4& arg4,
         const char* name5, const T5& arg5
      )
      {
         JsonDataPointer json = MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, suppressLog, moduleId, functionName);
         serializer.addValue(name0, arg0);
         serializer.addValue(name1, arg1);
         serializer.addValue(name2, arg2);
         serializer.addValue(name3, arg3);
         serializer.addValue(name4, arg4);
         serializer.addValue(name5, arg5);
         serializer.finalize();
         transport->send(json, suppressLog);
      }

      template<
        class T0, class T1, class T2, class T3, class T4,
        class T5, class T6
      >
      JsonFunctionCallImpl(CPCAPI2::JsonApi::JsonApiTransport* transport, bool suppressLog, const char* moduleId, const char* functionName,
         const char* name0, const T0& arg0,
         const char* name1, const T1& arg1,
         const char* name2, const T2& arg2,
         const char* name3, const T3& arg3,
         const char* name4, const T4& arg4,
         const char* name5, const T5& arg5,
         const char* name6, const T6& arg6
      )
      {
         JsonDataPointer json = MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, suppressLog, moduleId, functionName);
         serializer.addValue(name0, arg0);
         serializer.addValue(name1, arg1);
         serializer.addValue(name2, arg2);
         serializer.addValue(name3, arg3);
         serializer.addValue(name4, arg4);
         serializer.addValue(name5, arg5);
         serializer.addValue(name6, arg6);
         serializer.finalize();
         transport->send(json, suppressLog);
      }

      template<
        class T0, class T1, class T2, class T3, class T4,
        class T5, class T6, class T7
      >
      JsonFunctionCallImpl(CPCAPI2::JsonApi::JsonApiTransport* transport, bool suppressLog, const char* moduleId, const char* functionName,
         const char* name0, const T0& arg0,
         const char* name1, const T1& arg1,
         const char* name2, const T2& arg2,
         const char* name3, const T3& arg3,
         const char* name4, const T4& arg4,
         const char* name5, const T5& arg5,
         const char* name6, const T6& arg6,
         const char* name7, const T7& arg7
      )
      {
         JsonDataPointer json = MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, suppressLog, moduleId, functionName);
         serializer.addValue(name0, arg0);
         serializer.addValue(name1, arg1);
         serializer.addValue(name2, arg2);
         serializer.addValue(name3, arg3);
         serializer.addValue(name4, arg4);
         serializer.addValue(name5, arg5);
         serializer.addValue(name6, arg6);
         serializer.addValue(name7, arg7);
         serializer.finalize();
         transport->send(json, suppressLog);
      }

      template<
        class T0, class T1, class T2, class T3, class T4,
        class T5, class T6, class T7, class T8
      >
      JsonFunctionCallImpl(CPCAPI2::JsonApi::JsonApiTransport* transport, bool suppressLog, const char* moduleId, const char* functionName,
         const char* name0, const T0& arg0,
         const char* name1, const T1& arg1,
         const char* name2, const T2& arg2,
         const char* name3, const T3& arg3,
         const char* name4, const T4& arg4,
         const char* name5, const T5& arg5,
         const char* name6, const T6& arg6,
         const char* name7, const T7& arg7,
         const char* name8, const T8& arg8
      )
      {
         JsonDataPointer json = MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, suppressLog, moduleId, functionName);
         serializer.addValue(name0, arg0);
         serializer.addValue(name1, arg1);
         serializer.addValue(name2, arg2);
         serializer.addValue(name3, arg3);
         serializer.addValue(name4, arg4);
         serializer.addValue(name5, arg5);
         serializer.addValue(name6, arg6);
         serializer.addValue(name7, arg7);
         serializer.addValue(name8, arg8);
         serializer.finalize();
         transport->send(json, suppressLog);
      }

      template<
        class T0, class T1, class T2, class T3, class T4,
        class T5, class T6, class T7, class T8, class T9
      >
      JsonFunctionCallImpl(CPCAPI2::JsonApi::JsonApiTransport* transport, bool suppressLog, const char* moduleId, const char* functionName,
         const char* name0, const T0& arg0,
         const char* name1, const T1& arg1,
         const char* name2, const T2& arg2,
         const char* name3, const T3& arg3,
         const char* name4, const T4& arg4,
         const char* name5, const T5& arg5,
         const char* name6, const T6& arg6,
         const char* name7, const T7& arg7,
         const char* name8, const T8& arg8,
         const char* name9, const T9& arg9
      )
      {
         JsonDataPointer json = MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, suppressLog, moduleId, functionName);
         serializer.addValue(name0, arg0);
         serializer.addValue(name1, arg1);
         serializer.addValue(name2, arg2);
         serializer.addValue(name3, arg3);
         serializer.addValue(name4, arg4);
         serializer.addValue(name5, arg5);
         serializer.addValue(name6, arg6);
         serializer.addValue(name7, arg7);
         serializer.addValue(name8, arg8);
         serializer.addValue(name9, arg9);
         serializer.finalize();
         transport->send(json, suppressLog);
      }

      template<
        class T0, class T1, class T2, class T3, class T4,
        class T5, class T6, class T7, class T8, class T9,
        class T10
      >
      JsonFunctionCallImpl(CPCAPI2::JsonApi::JsonApiTransport* transport, bool suppressLog, const char* moduleId, const char* functionName,
         const char* name0, const T0& arg0,
         const char* name1, const T1& arg1,
         const char* name2, const T2& arg2,
         const char* name3, const T3& arg3,
         const char* name4, const T4& arg4,
         const char* name5, const T5& arg5,
         const char* name6, const T6& arg6,
         const char* name7, const T7& arg7,
         const char* name8, const T8& arg8,
         const char* name9, const T9& arg9,
         const char* name10, const T10& arg10
      )
      {
         JsonDataPointer json = MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, suppressLog, moduleId, functionName);
         serializer.addValue(name0, arg0);
         serializer.addValue(name1, arg1);
         serializer.addValue(name2, arg2);
         serializer.addValue(name3, arg3);
         serializer.addValue(name4, arg4);
         serializer.addValue(name5, arg5);
         serializer.addValue(name6, arg6);
         serializer.addValue(name7, arg7);
         serializer.addValue(name8, arg8);
         serializer.addValue(name9, arg9);
         serializer.addValue(name10, arg10);
         serializer.finalize();
         transport->send(json, suppressLog);
      }

      template<
        class T0, class T1, class T2, class T3, class T4,
        class T5, class T6, class T7, class T8, class T9,
        class T10, class T11
      >
      JsonFunctionCallImpl(CPCAPI2::JsonApi::JsonApiTransport* transport, bool suppressLog, const char* moduleId, const char* functionName,
         const char* name0, const T0& arg0,
         const char* name1, const T1& arg1,
         const char* name2, const T2& arg2,
         const char* name3, const T3& arg3,
         const char* name4, const T4& arg4,
         const char* name5, const T5& arg5,
         const char* name6, const T6& arg6,
         const char* name7, const T7& arg7,
         const char* name8, const T8& arg8,
         const char* name9, const T9& arg9,
         const char* name10, const T10& arg10,
         const char* name11, const T11& arg11
      )
      {
         JsonDataPointer json = MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, suppressLog, moduleId, functionName);
         serializer.addValue(name0, arg0);
         serializer.addValue(name1, arg1);
         serializer.addValue(name2, arg2);
         serializer.addValue(name3, arg3);
         serializer.addValue(name4, arg4);
         serializer.addValue(name5, arg5);
         serializer.addValue(name6, arg6);
         serializer.addValue(name7, arg7);
         serializer.addValue(name8, arg8);
         serializer.addValue(name9, arg9);
         serializer.addValue(name10, arg10);
         serializer.addValue(name11, arg11);
         serializer.finalize();
         transport->send(json, suppressLog);
      }

      template<
        class T0, class T1, class T2, class T3, class T4,
        class T5, class T6, class T7, class T8, class T9,
        class T10, class T11, class T12
      >
      JsonFunctionCallImpl(CPCAPI2::JsonApi::JsonApiTransport* transport, bool suppressLog, const char* moduleId, const char* functionName,
         const char* name0, const T0& arg0,
         const char* name1, const T1& arg1,
         const char* name2, const T2& arg2,
         const char* name3, const T3& arg3,
         const char* name4, const T4& arg4,
         const char* name5, const T5& arg5,
         const char* name6, const T6& arg6,
         const char* name7, const T7& arg7,
         const char* name8, const T8& arg8,
         const char* name9, const T9& arg9,
         const char* name10, const T10& arg10,
         const char* name11, const T11& arg11,
         const char* name12, const T12& arg12
      )
      {
         JsonDataPointer json = MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, suppressLog, moduleId, functionName);
         serializer.addValue(name0, arg0);
         serializer.addValue(name1, arg1);
         serializer.addValue(name2, arg2);
         serializer.addValue(name3, arg3);
         serializer.addValue(name4, arg4);
         serializer.addValue(name5, arg5);
         serializer.addValue(name6, arg6);
         serializer.addValue(name7, arg7);
         serializer.addValue(name8, arg8);
         serializer.addValue(name9, arg9);
         serializer.addValue(name10, arg10);
         serializer.addValue(name11, arg11);
         serializer.addValue(name12, arg12);
         serializer.finalize();
         transport->send(json, suppressLog);
      }

      template<
        class T0, class T1, class T2, class T3, class T4,
        class T5, class T6, class T7, class T8, class T9,
        class T10, class T11, class T12, class T13
      >
      JsonFunctionCallImpl(CPCAPI2::JsonApi::JsonApiTransport* transport, bool suppressLog, const char* moduleId, const char* functionName,
         const char* name0, const T0& arg0,
         const char* name1, const T1& arg1,
         const char* name2, const T2& arg2,
         const char* name3, const T3& arg3,
         const char* name4, const T4& arg4,
         const char* name5, const T5& arg5,
         const char* name6, const T6& arg6,
         const char* name7, const T7& arg7,
         const char* name8, const T8& arg8,
         const char* name9, const T9& arg9,
         const char* name10, const T10& arg10,
         const char* name11, const T11& arg11,
         const char* name12, const T12& arg12,
         const char* name13, const T13& arg13
      )
      {
         JsonDataPointer json = MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, suppressLog, moduleId, functionName);
         serializer.addValue(name0, arg0);
         serializer.addValue(name1, arg1);
         serializer.addValue(name2, arg2);
         serializer.addValue(name3, arg3);
         serializer.addValue(name4, arg4);
         serializer.addValue(name5, arg5);
         serializer.addValue(name6, arg6);
         serializer.addValue(name7, arg7);
         serializer.addValue(name8, arg8);
         serializer.addValue(name9, arg9);
         serializer.addValue(name10, arg10);
         serializer.addValue(name11, arg11);
         serializer.addValue(name12, arg12);
         serializer.addValue(name13, arg13);
         serializer.finalize();
         transport->send(json, suppressLog);
      }

      template<
        class T0, class T1, class T2, class T3, class T4,
        class T5, class T6, class T7, class T8, class T9,
        class T10, class T11, class T12, class T13, class T14
      >
      JsonFunctionCallImpl(CPCAPI2::JsonApi::JsonApiTransport* transport, bool suppressLog, const char* moduleId, const char* functionName,
         const char* name0, const T0& arg0,
         const char* name1, const T1& arg1,
         const char* name2, const T2& arg2,
         const char* name3, const T3& arg3,
         const char* name4, const T4& arg4,
         const char* name5, const T5& arg5,
         const char* name6, const T6& arg6,
         const char* name7, const T7& arg7,
         const char* name8, const T8& arg8,
         const char* name9, const T9& arg9,
         const char* name10, const T10& arg10,
         const char* name11, const T11& arg11,
         const char* name12, const T12& arg12,
         const char* name13, const T13& arg13,
         const char* name14, const T14& arg14
      )
      {
         JsonDataPointer json = MakeJsonDataPointer();
         CPCAPI2::Json::JsonFunctionSerialize serializer(json, suppressLog, moduleId, functionName);
         serializer.addValue(name0, arg0);
         serializer.addValue(name1, arg1);
         serializer.addValue(name2, arg2);
         serializer.addValue(name3, arg3);
         serializer.addValue(name4, arg4);
         serializer.addValue(name5, arg5);
         serializer.addValue(name6, arg6);
         serializer.addValue(name7, arg7);
         serializer.addValue(name8, arg8);
         serializer.addValue(name9, arg9);
         serializer.addValue(name10, arg10);
         serializer.addValue(name11, arg11);
         serializer.addValue(name12, arg12);
         serializer.addValue(name13, arg13);
         serializer.addValue(name14, arg14);
         serializer.finalize();
         transport->send(json, suppressLog);
      }
   };

} // namespace Json
} // namespace CPCAPI2

#endif
