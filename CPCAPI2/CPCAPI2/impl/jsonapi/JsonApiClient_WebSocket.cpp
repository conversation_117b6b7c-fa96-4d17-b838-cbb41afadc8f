#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "JsonApiClient_WebSocket.h"
#include "cpcapi2utils.h"
#include "jsonapi/JsonApiClientModule.h"
#include "jsonapi/JsonApiClient_WebSocketHandler.h"
#include "../util/FileUtils.h"
#include "../util/cpc_logger.h"
#include "../phone/PhoneInterface.h"
#include "websocket/json/WebSocketCommand.h"
#include "websocket/json/JsonDataWebSocketCommand.h"
#include "websocket/states/StateConnected.h"
#include "websocket/states/StateConnecting.h"
#include "websocket/states/StateDisconnected.h"
#include "websocket/states/StateFailed.h"
#include "websocket/states/StateSuspended.h"

#include <stdio.h>
#include <ostream>
#include <fstream>
#include <locale>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#include <webrtc/base/platform_thread.h>


#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

using namespace resip;
using namespace CPCAPI2::WebSocket;
using CPCAPI2::WebSocket::StateFailed;

namespace CPCAPI2
{

namespace JsonApi
{

struct JsonApiClientWebSocketCallbackStruct
{
   std::weak_ptr<JsonApiClientWebSocket> weak_this;
   JsonApiClientWebSocketCallbackStruct(std::weak_ptr<JsonApiClientWebSocket> weak_this_) : weak_this(weak_this_) {}
};

void sdkCallOnAppReleaseHook(void* context)
{
   StackLog(<< "JsonApiClientWebSocket::sdkCallOnAppReleaseHook(): " << context << " phone being released");
   JsonApiClientWebSocketCallbackStruct* cb = (JsonApiClientWebSocketCallbackStruct*)context;
   if (cb)
   {
      JsonApiClientWebSocket::phoneReleased(cb->weak_this);
   }
   delete cb;
   cb = NULL;
}

JsonApiClientWebSocket::JsonApiClientWebSocket(CPCAPI2::Phone* phone) :
mPhone(phone),
mHandler(NULL),
mDropMessages(false)
{
   StackLog(<< "JsonApiClientWebSocket::JsonApiClientWebSocket(): " << this << " phone: " << phone);
   ((NetworkChangeManagerInterface*)NetworkChangeManager::getInterface(phone))->addSdkObserver(this);
}

JsonApiClientWebSocket::~JsonApiClientWebSocket()
{
   StackLog(<< "JsonApiClientWebSocket::~JsonApiClientWebSocket(): " << this << " phone: " << mPhone);
   using msecs = std::chrono::milliseconds;
   if (mReleaseMutex.try_lock_for(msecs(100)))
   {
      if (mPhone)
      {
         NetworkChangeManagerInterface* netMgr = dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(mPhone));
         if (netMgr)
         {
            netMgr->removeSdkObserver(this);
         }
      }

      mPhone = NULL;
      mHandler = NULL;

      if (mWsStateMachine.get())
      {
         StackLog(<< "JsonApiClientWebSocket::~JsonApiClientWebSocket(): " << this << " phone: " << mPhone << " checking web socket state");
         const char* stateID(mWsStateMachine->getCurrentStateID());
         if (strcmp(stateID , STATE_DISCONNECTED_ID) != 0)
         {
            mWsStateMachine->setCurrentState(STATE_DISCONNECTED_ID);
         }
      }

      mWork.reset();
      mIoService.stop();

      if (mThread.get() != NULL)
         mThread->join();

      mThread.reset();
      mReleaseMutex.unlock();
   }
   else
   {
      InfoLog(<< "JsonApiClientWebSocket::~JsonApiClientWebSocket(): " << this << " phone: " << mPhone << " mutex timed-out");
   }
}


void JsonApiClientWebSocket::phoneReleased(std::weak_ptr<JsonApiClientWebSocket> weakSelf)
{
   StackLog(<< "JsonApiClientWebSocket::phoneReleased(): thread-id: " << resip::ThreadIf::selfId());

   if (std::shared_ptr<JsonApiClientWebSocket> self = weakSelf.lock())
   {
      using msecs = std::chrono::milliseconds;
      if (self->mReleaseMutex.try_lock_for(msecs(100)))
      {
         if (self->mPhone)
         {
            NetworkChangeManagerInterface* netMgr = dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(self->mPhone));
            if (netMgr)
            {
               netMgr->removeSdkObserver(self.get());
            }

            self->mPhone = NULL;
         }

         self->mReleaseMutex.unlock();
      }
      else
      {
         InfoLog(<< "JsonApiClientWebSocket::phoneReleased(): " << self.get() << " phone: " << self->mPhone << " mutex timed-out");
      }
   }
   else
   {
      StackLog(<< "JsonApiClientWebSocket::phoneReleased(): invalid web socket");
   }
}

int JsonApiClientWebSocket::setHandler(JsonApiClientWebSocketHandler* handler)
{
   if (!mThread.get())
   {
      mWork.reset(new boost::asio::io_service::work(mIoService));
      mThread.reset(new std::thread([&]() { mIoService.run(); }));
   }

   if (mHandlerMtx.try_lock())
   {
      setHandlerImpl(handler);
      mHandlerMtx.unlock();
   }
   else
   {
      return kError;
   }

   return kSuccess;
}

int JsonApiClientWebSocket::setHandlerImpl(JsonApiClientWebSocketHandler* handler)
{
   StackLog(<< "JsonApiClientWebSocket::setHandlerImpl(): " << this << " phone: " << mPhone << " existing handle: " << mHandler << " new handle: " << handler);
   using msecs = std::chrono::milliseconds;
   if (mReleaseMutex.try_lock_for(msecs(100)))
   {
      mHandler = handler;
      if (mPhone)
         dynamic_cast<PhoneInternal*>(mPhone)->setCallOnAppReleaseFn(sdkCallOnAppReleaseHook, new JsonApiClientWebSocketCallbackStruct(shared_from_this()));
      mReleaseMutex.unlock();
   }
   else
   {
      InfoLog(<< "JsonApiClientWebSocket::setHandlerImpl(): " << this << " phone: " << mPhone << " mutex timed-out");
   }
   return kSuccess;
}

int JsonApiClientWebSocket::shutdown()
{
   mWebsocketClosed.reset(new std::promise<int>());
   std::future<int> fuShutdown = mWebsocketClosed->get_future();
   mIoService.post(std::bind(&JsonApiClientWebSocket::shutdownImpl, this));
   auto fs = fuShutdown.wait_for(std::chrono::milliseconds(1000));
   if (fs == std::future_status::ready)
   {
      fuShutdown.get();
   }
   StackLog(<< "JsonApiClientWebSocket::shutdown(): " << this);
   return kSuccess;
}

void JsonApiClientWebSocket::shutdownImpl()
{
   StackLog(<< "JsonApiClientWebSocket::shutdownImpl()");
   if (mWsStateMachine.get())
   {
      const char* stateID(mWsStateMachine->getCurrentStateID());
      if (strcmp(stateID , STATE_DISCONNECTED_ID) != 0)
      {
         mWsStateMachine->setCurrentState(STATE_DISCONNECTED_ID);
      }
      else
      {
         try
         {
            mWebsocketClosed->set_value(1);
         }
         catch (std::future_error)
         {
         }
      }
   }
   else
   {
      try
      {
         mWebsocketClosed->set_value(1);
      }
      catch (std::future_error)
      {
      }
   }
}

int JsonApiClientWebSocket::send(const CPCAPI2::Json::JsonDataPointer& jsonData, bool /*suppressLog*/)
{
   mIoService.post(std::bind(&JsonApiClientWebSocket::sendImpl, this, jsonData));
   return kSuccess;
}

void JsonApiClientWebSocket::sendImpl(const CPCAPI2::Json::JsonDataPointer& jsonData)
{
   // StackLog(<< "JsonApiClientWebSocket::sendImpl(): this: " << this << " phone: " << mPhone << " json:\n\t" << jsonData->getMessageData());
   if (mWsStateMachine.get() == NULL)
   {
      return;
   }

   JsonDataWebSocketCommand cmd(jsonData);

   std::string outErrMessage;
   bool success = mWsStateMachine->sendCommand(0, cmd, outErrMessage, true);
   if (!success)
   {
      ErrLog(<< "Failed to send to websocket: " << cmd );
   }
}

int JsonApiClientWebSocket::configureSettings(const JsonApiClientWebSocketSettings& settings)
{
   if (!mThread.get())
   {
      mWork.reset(new boost::asio::io_service::work(mIoService));
      mThread.reset(new std::thread([&]() { mIoService.run(); }));
   }

   mIoService.post(std::bind(&JsonApiClientWebSocket::configureSettingsImpl, this, settings));
   return kSuccess;
}

int JsonApiClientWebSocket::configureSettingsImpl(const JsonApiClientWebSocketSettings& settings)
{
   rtc::SetCurrentThreadName("JsonApiClientWebSocket");

   mSettings = settings;

   mWebSocketSettings.isLoginRequired = false;
   mWebSocketSettings.webSocketURL = settings.serverUri.c_str();
   mWebSocketSettings.pingIntervalSeconds = 45;
   mWebSocketSettings.sendImmediatePing = false;
   mWebSocketSettings.logPayload = false;
   mWebSocketSettings.certMode = settings.ignoreCertVerification ? CertVerificationMode_None : CertVerificationMode_Peer;

   if (mWsStateMachine.get() == NULL)
   {
      // warning: WebSocketStateMachine stores a reference to the settings, so they must
      // exist for the lifetime of this WebSocketStateMachine instance.
      using msecs = std::chrono::milliseconds;
      if (mReleaseMutex.try_lock_for(msecs(100)))
      {
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(mPhone);
         if (phone)
         {
            if (TLS_DEFAULT == mWebSocketSettings.tlsVersion) mWebSocketSettings.tlsVersion = phone->getSslCipherOptions().getTLSVersion(SslCipherUsageWebSockets);
            if (mWebSocketSettings.cipherSuite.empty()) mWebSocketSettings.cipherSuite = phone->getSslCipherOptions().getCiphers(SslCipherUsageWebSockets);
            mWsStateMachine.reset(new WebSocket::WebSocketStateMachine(RESIPROCATE_SUBSYSTEM, mIoService, mWebSocketSettings, "JSON Api Client"));

            mWsStateMachine->addListener(this); // TODO: Passing this ?

            StackLog(<< "JsonApiClientWebSocket::configureSettingsImpl(): client " << this << " phone: " << mPhone << " owns web socket: " << mWsStateMachine.get());
         }
         mReleaseMutex.unlock();
      }
      else
      {
         InfoLog(<< "JsonApiClientWebSocket::configureSettingsImpl(): " << this << " phone: " << mPhone << " mutex timed-out");
      }
   }

   return kSuccess;
}

int JsonApiClientWebSocket::dropIncomingJsonMessages(bool enable)
{
   mIoService.post(std::bind(&JsonApiClientWebSocket::dropIncomingJsonMessagesImpl, this, enable));
   return kSuccess;
}

void JsonApiClientWebSocket::dropIncomingJsonMessagesImpl(bool enable)
{
   // StackLog(<< "JsonApiClientWebSocket::dropIncomingJsonMessagesImpl(): this: " << this << " phone: " << mPhone << " drop message enable: " << enable);
   mDropMessages = enable;
}

void JsonApiClientWebSocket::onStateChange(const CPCAPI2::AbstractStatePtr oldState, const CPCAPI2::AbstractStatePtr newState, const CPCAPI2::AbstractStateReasonCode reason )
{
   // warning: appears to sometimes be caled on PhoneInterface thread, sometimes called on mThread's thread

   if (newState == NULL)
   {
      return;
   }

   mIoService.post(std::bind(&JsonApiClientWebSocket::onStateChangeImpl, this, oldState, newState, reason));
}

void JsonApiClientWebSocket::onStateChangeImpl(const CPCAPI2::AbstractStatePtr oldState, const CPCAPI2::AbstractStatePtr newState, const CPCAPI2::AbstractStateReasonCode reason )
{
   StackLog(<< "JsonApiClientWebSocket::onStateChangeImpl(): " << this << " phone: " << mPhone << " oldState: " << oldState->getName() << " newState: " << newState->getName());

   std::unique_lock<std::mutex> scopeLock(mHandlerMtx);

   if (mHandler == NULL)
   {
      StackLog(<< "JsonApiClientWebSocket::onStateChangeImpl(): NULL handler");
      return;
   }

   const char* stateID = newState->getUniqueID();

   if (strcmp(stateID, STATE_CONNECTED_ID) == 0)
   {
      mHandler->onStateChanged(JsonApiClientWebSocketHandler::State_Connected);
   }
   else if (strcmp(stateID, STATE_CONNECTING_ID) == 0)
   {
      mHandler->onStateChanged(JsonApiClientWebSocketHandler::State_Connecting);
   }
   else if (strcmp(stateID, STATE_DISCONNECTED_ID) == 0)
   {
      mHandler->onStateChanged(JsonApiClientWebSocketHandler::State_Disconnected);
   }
   else if (strcmp(stateID, STATE_FAILED_ID) == 0)
   {
      // state machine will retry at some point in the future
      mHandler->onStateChanged(JsonApiClientWebSocketHandler::State_Failed);
   }
}

void JsonApiClientWebSocket::onMessage(const std::string& inDocument)
{
   std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
   jsonRequest->Parse<0>(inDocument.c_str());

   if (jsonRequest->HasParseError())
   {
      WarningLog(<< "JsonApiClientWebSocket::onMessage(): Invalid request format, parse error occured:" << jsonRequest->GetParseError() << " aborting decode.");
      return;
   }

   if (!jsonRequest->HasMember("suppressLog"))
   {
      InfoLog(<< "JsonApiClientWebSocket::onMessage(): " << this << " phone: " << mPhone << "\n"
              "========= Received from websocket ======\n" <<
              inDocument << "\n" <<
              "=====================================" );
   }

#ifdef CPCAPI2_AUTO_TEST
   if (mDropMessages)
   {
      InfoLog(<< "JsonApiClientWebSocket::onMessage(): " << this << " phone: " << mPhone << " dropping message");
      return;
   }
#endif

   if (!jsonRequest->HasMember("moduleId"))
   {
      WarningLog(<< "JsonApiClientWebSocket::onMessage(): Node missing: module-id. Aborting decode.");
      return;
   }

   const rapidjson::Value& moduleIdVal = (*jsonRequest)["moduleId"];
   if (!moduleIdVal.IsString())
   {
      WarningLog(<< "JsonApiClientWebSocket::onMessage(): Invalid module-id format. Aborting decode.");
      return;
   }

   if (mPhone == NULL)
   {
      WarningLog(<< "JsonApiClientWebSocket::onMessage(): Dropping message during shutdown");
      return;
   }

   using msecs = std::chrono::milliseconds;
   if (mReleaseMutex.try_lock_for(msecs(100)))
   {
      PhoneInterface* pi = dynamic_cast<PhoneInterface*>(mPhone);
      JsonApiClientModule* pm = NULL;
      if (pi)
      {
         pm = dynamic_cast<JsonApiClientModule*>(pi->getInterfaceByName(moduleIdVal.GetString()));
      }

      if (pm != NULL)
      {
         if (!jsonRequest->HasMember("functionObject"))
         {
            WarningLog(<< "JsonApiClientWebSocket::onMessage(): Node missing: functionObject.");
            return;
         }

         const rapidjson::Value& functionObjVal = (*jsonRequest)["functionObject"];
         if (!functionObjVal.IsObject())
         {
            WarningLog(<< "JsonApiClientWebSocket::onMessage(): Invalid functionObject");
            return;
         }

         StackLog(<< "JsonApiClientWebSocket::onMessage(): processing message");
         pm->processIncoming(jsonRequest);
      }
      mReleaseMutex.unlock();
   }
   else
   {
      InfoLog(<< "JsonApiClientWebSocket::onMessage(): " << this << " phone: " << mPhone << " mutex timed-out");
   }
}

void JsonApiClientWebSocket::onPing(websocketpp::connection_hdl hWebSock)
{
   mWsStateMachine->sendPing();
}

void JsonApiClientWebSocket::onClose()
{
   if (mWebsocketClosed.get() != NULL)
   {
      try
      {
         mWebsocketClosed->set_value(1);
      }
      catch (std::future_error)
      {
      }
   }
}

bool JsonApiClientWebSocket::onReconnect( uint16_t code )
{
   return true;
}

int JsonApiClientWebSocket::connect()
{
   if (!mThread.get())
   {
      mWork.reset(new boost::asio::io_service::work(mIoService));
      mThread.reset(new std::thread([&]() { mIoService.run(); }));
   }

   mIoService.post(std::bind(&JsonApiClientWebSocket::connectImpl, this));
   return kSuccess;
}

int JsonApiClientWebSocket::connectImpl()
{
   rtc::SetCurrentThreadName("JsonApiClientWebSocket");

   if (!mWsStateMachine.get())
   {
      ErrLog(<< "Must call configureSettings(..) first");
      return kError;
   }

   mWsStateMachine->setCurrentState(STATE_CONNECTING_ID);

   return kSuccess;
}

int JsonApiClientWebSocket::onNetworkChange(const NetworkChangeEvent& args)
{
   mIoService.post(std::bind(&JsonApiClientWebSocket::onNetworkChangeImpl, this, args));
   return kSuccess;
}

int JsonApiClientWebSocket::onNetworkChangeImpl(const NetworkChangeEvent& args)
{
   if (mWsStateMachine.get() == NULL)
      return kError;

   const char *stateID( mWsStateMachine->getCurrentStateID() );

   // we don't want to re-awake the state machine if it was previously disconnected
   if( strcmp( stateID , STATE_DISCONNECTED_ID ) == 0 )
      return kSuccess;

   // reset the failed state expiry time, to account for network instability.
   std::shared_ptr< StateFailed > pFailedState( std::dynamic_pointer_cast< StateFailed >( mWsStateMachine->getState( STATE_FAILED_ID )));
   if (pFailedState)
      pFailedState->resetExpiryTime();

   mWsStateMachine->setCurrentState(( strcmp( stateID, STATE_FAILED_ID ) == 0 ) ? STATE_CONNECTING_ID : STATE_FAILED_ID );
   return kSuccess;
}

}

}

#endif
