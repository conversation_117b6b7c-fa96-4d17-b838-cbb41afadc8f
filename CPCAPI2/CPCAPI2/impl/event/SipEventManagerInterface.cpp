#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_EVENT_MODULE == 1)
#include "cpcapi2utils.h"
#include "SipEventManagerInterface.h"
#include "../phone/PhoneInterface.h"
#include "../account/SipAccountInterface.h"
#include "SipEventManagerImpl.h"
#include "SipEventSubscriptionCreationInfo.h"
#include "../util/ResipConv.h"
#include "../util/cpc_logger.h"

#include <resip/stack/PlainContents.hxx>
#include <resip/stack/Mime.hxx>
#include <resip/dum/ClientSubscription.hxx>
#include <resip/stack/ExtensionParameter.hxx>

using namespace CPCAPI2::SipAccount;
using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_PRESENCE

namespace CPCAPI2
{
namespace SipEvent
{

SipEventSubscriptionHandle SipEventSubscriptionHandleFactory::sNextHandle = 1;

SipEventManagerInterface::SipEventManagerInterface(Phone* phone)
   : CPCAPI2::EventSource2< CPCAPI2::EventHandler<SipEventSubscriptionHandler, CPCAPI2::SipAccount::SipAccountHandle, std::string> >(dynamic_cast<PhoneInterface*>(phone)),
     mAccountIf(NULL),
     mAccountMap(new AccountMap),
     mNextSubscriptionHandle(1),
     mEowMap(new EowMap)
{
   mAccountIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(phone));
   mPhone = dynamic_cast<PhoneInterface*>(phone);
}

SipEventManagerInterface::~SipEventManagerInterface()
{
   mAccountMap->clear();
   mEowMap->clear();
}

void SipEventManagerInterface::Release()
{
   delete this;
}

int SipEventManagerInterface::reject(SipEventSubscriptionHandle subscription, unsigned int rejectReason)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << rejectReason);
   postToSdkThread(resip::resip_bind(&SipEventManagerInterface::rejectImpl, this, subscription, rejectReason));
   return kSuccess;
}

int SipEventManagerInterface::rejectImpl(SipEventSubscriptionHandle subscription, unsigned int rejectReason)
{
   SipEventSubscriptionCreationInfo* ci = getCreationInfo(subscription);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         SipEventManagerImpl* acct = itAcct->second;
         if (ci->dumServerSubscriptionHandle.isValid())
         {
            ci->dumServerSubscriptionHandle->setSubscriptionState(resip::Terminated);
            SubscriptionStateChangedEvent stateChangeArgs;
            stateChangeArgs.subscriptionState = SipSubscriptionState_Terminated;
            acct->fireSubscriptionStateChanged(subscription, stateChangeArgs, ci->eventType);
            resip::SharedPtr<resip::SipMessage> reject = ci->dumServerSubscriptionHandle->reject(rejectReason);
            ci->dumServerSubscriptionHandle->send(reject);
         }
      }
   }
   return kSuccess;
}

int SipEventManagerInterface::accept(SipEventSubscriptionHandle subscription, const SipEventState& eventState)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << eventState);
   postToSdkThread(resip::resip_bind(&SipEventManagerInterface::acceptImpl, this, subscription, eventState, false));
   return kSuccess;
}

int SipEventManagerInterface::accept(SipEventSubscriptionHandle subscription, const SipEventState& eventState, bool suppressNotify)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << eventState << ", " << suppressNotify);
   postToSdkThread(resip::resip_bind(&SipEventManagerInterface::acceptImpl, this, subscription, eventState, suppressNotify));
   return kSuccess;
}

int SipEventManagerInterface::acceptImpl(SipEventSubscriptionHandle subscription, const SipEventState& eventState, bool suppressNotify)
{
   SipEventSubscriptionCreationInfo* ci = getCreationInfo(subscription);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         SipEventManagerImpl* acct = itAcct->second;
         if (ci->dumServerSubscriptionHandle.isValid())
         {
            resip::SharedPtr<resip::SipMessage> ok = ci->dumServerSubscriptionHandle->accept(200);
            ok->header(resip::h_Expires).value() = std::min<unsigned int>(eventState.expiresTimeMs, ci->expiresSeconds);
            ci->dumServerSubscriptionHandle->send(ok);

            ci->dumServerSubscriptionHandle->setSubscriptionState(resip::Active);

            if (false == suppressNotify)
            {
               std::unique_ptr<resip::PlainContents> contents = std::unique_ptr<resip::PlainContents>(new resip::PlainContents(
                  resip::Data(eventState.contentUTF8.c_str(), eventState.contentLength),
                  resip::Mime((eventState.mimeType).c_str(), (eventState.mimeSubType).c_str())));
               resip::SharedPtr<resip::SipMessage> notify = ci->dumServerSubscriptionHandle->update(contents.get());
               notify->header(resip::h_SubscriptionState).param(resip::p_expires) = std::min<unsigned int>(eventState.expiresTimeMs, ci->expiresSeconds);
               ci->dumServerSubscriptionHandle->send(notify);
            }

            SubscriptionStateChangedEvent stateChangeArgs;
            stateChangeArgs.subscriptionState = SipSubscriptionState_Active;
            acct->fireSubscriptionStateChanged(subscription, stateChangeArgs, ci->eventType);
         }
      }
   }
   return kSuccess;
}

int SipEventManagerInterface::provisionalAccept(SipEventSubscriptionHandle subscription, const SipEventState& eventState)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << eventState);
   postToSdkThread(resip::resip_bind(&SipEventManagerInterface::provisionalAcceptImpl, this, subscription, eventState));
   return kSuccess;
}

int SipEventManagerInterface::provisionalAcceptImpl(SipEventSubscriptionHandle subscription, const SipEventState& eventState)
{
   SipEventSubscriptionCreationInfo* ci = getCreationInfo(subscription);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         SipEventManagerImpl* acct = itAcct->second;
         if (ci->dumServerSubscriptionHandle.isValid())
         {
            resip::SharedPtr<resip::SipMessage> ok = ci->dumServerSubscriptionHandle->accept(200);
            ok->header(resip::h_Expires).value() = std::min<unsigned int>(eventState.expiresTimeMs, ci->expiresSeconds);
            ci->dumServerSubscriptionHandle->send(ok);

            ci->dumServerSubscriptionHandle->setSubscriptionState(resip::Pending);

            resip::SharedPtr<resip::SipMessage> notify = ci->dumServerSubscriptionHandle->neutralNotify();
            notify->header(resip::h_SubscriptionState).param(resip::p_expires) = std::min<unsigned int>(eventState.expiresTimeMs, ci->expiresSeconds);
            ci->dumServerSubscriptionHandle->send(notify);

            SubscriptionStateChangedEvent stateChangeArgs;
            stateChangeArgs.subscriptionState = SipSubscriptionState_Pending;
            acct->fireSubscriptionStateChanged(subscription, stateChangeArgs, ci->eventType);
         }
      }
   }
   return kSuccess;
}

void SipEventManagerInterface::addEventType(const CPCAPI2::SipAccount::SipAccountHandle& account, const std::string& eventType)
{
   DebugLog(<< __FUNCTION__ << " " << account << ", " << eventType);
   AccountMap::iterator it = mAccountMap->find(account);
   SipEventManagerImpl* evtMan = (it == mAccountMap->end() ? NULL : it->second);
   if (evtMan == NULL)
   {
      if (SipAccountImpl* acct = mAccountIf->getAccountImpl(account))
      {
         evtMan = new SipEventManagerImpl(mAccountMap, *acct, this);
         (*mAccountMap)[account] = evtMan;
      }
      else
      {
         mAccountIf->fireError("Invalid account handle for SipEventManager::setHandler");
         return;
      }
   }
   evtMan->addEventType(eventType);
}

void SipEventManagerInterface::addAppHandler(SipEventSubscriptionHandler* handler, CPCAPI2::SipAccount::SipAccountHandle handle, std::string filter)
{
   addEventType(handle, filter);
   CPCAPI2::EventHandler<SipEventSubscriptionHandler, CPCAPI2::SipAccount::SipAccountHandle, std::string>::addAppHandler(handler, handle, filter);
}

void SipEventManagerInterface::addSdkObserver(SipEventSubscriptionHandler* handler, CPCAPI2::SipAccount::SipAccountHandle handle, std::string filter)
{
   addEventType(handle, filter);
   CPCAPI2::EventHandler<SipEventSubscriptionHandler, CPCAPI2::SipAccount::SipAccountHandle, std::string>::addSdkObserver(handler, handle, filter);
}

void SipEventManagerInterface::addSdkObserverSafe(const std::shared_ptr<SipEventSubscriptionHandler>& handler, CPCAPI2::SipAccount::SipAccountHandle handle, std::string filter)
{
   addEventType(handle, filter);
   CPCAPI2::EventHandler<SipEventSubscriptionHandler, CPCAPI2::SipAccount::SipAccountHandle, std::string>::addSdkObserverSafe(handler, handle, filter);
}


int SipEventManagerInterface::setHandler(
   CPCAPI2::SipAccount::SipAccountHandle account,
   const cpc::string& eventType,
   SipEventSubscriptionHandler* handler)
{
   ReadCallbackBase* setHandlerCmd = resip::resip_bind(&SipEventManagerInterface::setHandlerImpl, this, account, eventType, handler);
   if (handler == NULL)
   {
      // removing the handler involves two steps:
      // 1. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);

      // 2. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      executeOnSdkThread(setHandlerCmd);
   }
   else
   {
      postToSdkThread(setHandlerCmd);
   }

   return kSuccess;
}

int SipEventManagerInterface::setHandlerImpl(
   CPCAPI2::SipAccount::SipAccountHandle account,
   const cpc::string& eventType,
   SipEventSubscriptionHandler* handler)
{
   std::tuple<SipAccount::SipAccountHandle, std::string> i = std::make_tuple(account, eventType.c_str());
   auto it = mHandlers.find(i);
   if (mHandlers.end() != it)
   {
      removeAppHandler(it->second, account, eventType.c_str());
   }

   mHandlers[i] = handler;
   if (nullptr != handler)
   {
      addAppHandler(handler, account, eventType.c_str());
   }
   return kSuccess;
}

int SipEventManagerInterface::refresh(SipEventSubscriptionHandle subscription)
{
   DebugLog(<< __FUNCTION__ << " " << subscription);
   postToSdkThread(resip::resip_bind(&SipEventManagerInterface::refreshImpl, this, subscription));
   return kSuccess;
}

int SipEventManagerInterface::refreshImpl(SipEventSubscriptionHandle subscription)
{
   SipEvent::SipEventSubscriptionCreationInfo* ci = getCreationInfo(subscription);
   if (ci != NULL && ci->dumClientSubscriptionHandle.isValid())
   {
      DebugLog(<< "Refreshing subscription " << subscription << "(" << ci->eventType << ")");
      ci->dumClientSubscriptionHandle->requestRefresh();
   }
   else
   {
      ErrLog(<< "Unable to refresh subscription " << subscription << ". ci: " << ci);
   }

   return kSuccess;
}

SipEventSubscriptionHandle SipEventManagerInterface::createSubscription(CPCAPI2::SipAccount::SipAccountHandle account)
{
   DebugLog(<< __FUNCTION__ << " " << account);
   SipEventSubscriptionHandle h = SipEventSubscriptionHandleFactory::getNext();
   ReadCallbackBase* cmd = resip::resip_bind(&SipEventManagerInterface::createSubscriptionImpl, this, account, h);
   postToSdkThread(cmd);
   return h;
}

SipEventSubscriptionHandle SipEventManagerInterface::createSubscription(CPCAPI2::SipAccount::SipAccountHandle account, SipEventSubscriptionHandle h)
{
   DebugLog(<< __FUNCTION__ << " " << account << ", " << h);
   ReadCallbackBase* cmd = resip::resip_bind(&SipEventManagerInterface::createSubscriptionImpl, this, account, h);
   postToSdkThread(cmd);
   return h;
}

int SipEventManagerInterface::createSubscriptionImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipEventSubscriptionHandle h)
{
   AccountMap::iterator it = mAccountMap->find(account);
   if (it != mAccountMap->end())
   {
      SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
      if (!acct)
      {
         cpc::string msg = cpc::string("SipEventManagerInterface::createSubscription called with invalid account handle: ") + cpc::to_string(account) +
         " SipEventSubscriptionHandle invalid: " + cpc::to_string(h);
         mAccountIf->fireError(msg);
      }
      else if(!acct->isEnabled())
      {
         cpc::string msg = cpc::string("SipEventManagerInterface::createSubscription called before account enabled: ") + cpc::to_string(account) +
         ", SipEventSubscriptionHandle invalid: " + cpc::to_string(h);
         mAccountIf->fireError(msg);
      }
      else if (!it->second->getDum())
      {
         cpc::string msg = cpc::string("SipEventManagerInterface::createSubscription called but DUM is not available: ") + cpc::to_string(account) +
         ", SipEventSubscriptionHandle invalid: " + cpc::to_string(h);
         mAccountIf->fireError(msg);
      }
      else
      {
         SipEventSubscriptionCreationInfo* ci = new SipEventSubscriptionCreationInfo(*it->second->getDum(), it->second);
         ci->account = account;
         it->second->addCreationInfo(h, ci);
      }
      return kSuccess;
   }

   // Invalid account handle specified
   cpc::string msg = cpc::string("SipEventManagerInterface::createSubscription called with invalid account handle: ") + cpc::to_string(account) +
   ", SipEventSubscriptionHandle invalid: " + cpc::to_string(h);
   mAccountIf->fireError(msg);

   return kError;
}

int SipEventManagerInterface::applySubscriptionSettings(SipEventSubscriptionHandle subscription, const SipEventSubscriptionSettings& settings)
{
   DebugLog(<< __FUNCTION__ << " " << subscription);
   postToSdkThread(resip::resip_bind(&SipEventManagerInterface::applySubscriptionSettingsImpl, this, subscription, settings));
   return kSuccess;
}

int SipEventManagerInterface::applySubscriptionSettingsImpl(SipEventSubscriptionHandle subscription, const SipEventSubscriptionSettings& settings)
{
   SipEventSubscriptionCreationInfo* ci = getCreationInfo(subscription);
   if (ci != NULL)
   {
      ci->eventType = (settings.eventPackage).c_str();
      ci->eventPackageParams = settings.eventPackageParams;
      ci->expiresSeconds = settings.expiresSeconds;
      ci->supportedMimeTypes = settings.supportedMimeTypes;
   }
   return kSuccess;
}

int SipEventManagerInterface::start(SipEventSubscriptionHandle subscription)
{
   DebugLog(<< __FUNCTION__ << " " << subscription);
   postToSdkThread(resip::resip_bind(&SipEventManagerInterface::startImpl, this, subscription));
   return kSuccess;
}

int SipEventManagerInterface::startImpl(SipEventSubscriptionHandle subscription)
{
   SipEventSubscriptionCreationInfo* ci = getCreationInfo(subscription);
   if (ci != NULL)
   {
      if (ci->started)
      {
         ErrLog(<< "Subscription " << subscription << " (" << ci->eventType << ") already started");
         return kSuccess;
      }

      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         ci->sdkh = subscription;
         SipEventManagerImpl* acct = itAcct->second;
         if (acct->isDumShutdown())
         {
            acct->fireError(subscription, "Cannot start subscription. DUM is shutdown", ci->eventType);
            return kSuccess;
         }
         if (ci->targetAddresses.empty())
         {
            acct->fireError(subscription, "Cannot start subscription. No participants have been added", ci->eventType);
            return kSuccess;
         }
         resip::NameAddr targetAddr = ci->targetAddresses[0];
         resip::SharedPtr<resip::SipMessage> subscribe = acct->getDum()->makeSubscription(targetAddr, ci->eventType, ci->expiresSeconds, ci);
         for (cpc::vector<Parameter>::const_iterator it = ci->eventPackageParams.begin(); it != ci->eventPackageParams.end(); it++)
         {
            resip::ExtensionParameter eventPackageParam(it->name.c_str());
            subscribe->header(resip::h_Event).param(eventPackageParam) = it->value;
         }
         cpc::vector<CPCAPI2::MimeType>::const_iterator itMimes = ci->supportedMimeTypes.begin();
         for (; itMimes != ci->supportedMimeTypes.end(); ++itMimes)
         {
            resip::Mime mime(
                              itMimes->mimeType.c_str(),
                              itMimes->mimeSubType.c_str());

            if (!subscribe->header(resip::h_Accepts).find(mime))
            {
               subscribe->header(resip::h_Accepts).push_back(mime);
            }
         }
         // Support for the GENBAND "X-nt-eow" extension
         if (mEowMap->find(subscription) != mEowMap->end())
         {
            // Add "X-nt-eow" tag to Supported header
            subscribe->header(resip::h_Supporteds).push_back(resip::Token("X-nt-eow"));
         }
         acct->getDum()->send(subscribe);
         ci->started = true;
      }
   }
   return kSuccess;
}

int SipEventManagerInterface::addParticipant(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << targetAddress);
   postToSdkThread(resip::resip_bind(&SipEventManagerInterface::addParticipantImpl, this, subscription, targetAddress, false));
   return kSuccess;
}

int SipEventManagerInterface::addParticipantImpl(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress, bool isEventServer)
{
   SipEventSubscriptionCreationInfo* ci = getCreationInfo(subscription);
   if (ci != NULL)
   {
      AccountMap::iterator it = mAccountMap->find(ci->account);
      SipEventManagerImpl* evtMan = (it == mAccountMap->end() ? NULL : it->second);
      resip::NameAddr targetNameAddr;
      if (!ResipConv::stringToAddr(targetAddress, targetNameAddr))
      {
         cpc::string msg = "Failed to parse participant URI '" + targetAddress + "'";
         // fire to specific subscription event handler if we can
         if (evtMan)
         {
            evtMan->fireError(subscription, msg, ci->eventType);
         }
         else
         {
            // if no event handler available, fall back on account handler
            mAccountIf->fireError(msg);
         }
         return kSuccess;
      }
      ci->targetAddresses.push_back(targetNameAddr);

      if (isEventServer && evtMan && evtMan->getDum() != NULL)
      {
         // add support for eventlist
         evtMan->getDum()->getMasterProfile()->addSupportedOptionTag(resip::Token(resip::Symbols::eventlist), resip::SUBSCRIBE);
         ci->supportedMimeTypes.push_back(MimeType("application", "rlmi+xml"));
         ci->supportedMimeTypes.push_back(MimeType("multipart", "related"));
      }
   }
   return kSuccess;
}

int SipEventManagerInterface::notify(SipEventSubscriptionHandle subscription, const SipEventState& eventState)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << eventState);
   postToSdkThread(resip::resip_bind(&SipEventManagerInterface::notifyImpl, this, subscription, eventState, (const std::string*)NULL));
   return kSuccess;
}

int SipEventManagerInterface::notifyImpl(SipEventSubscriptionHandle subscription, const SipEventState& eventState, const std::string* contents)
{
   SipEventSubscriptionCreationInfo* ci = getCreationInfo(subscription);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         //SipEventManagerImpl* acct = itAcct->second;
         if (ci->dumServerSubscriptionHandle.isValid())
         {
            std::unique_ptr<resip::PlainContents> plainContents;
            if (contents != NULL)
            {
               plainContents = std::unique_ptr<resip::PlainContents>(new resip::PlainContents(
                  resip::Data(resip::Data::Share, contents->c_str(), eventState.contentLength),
                  resip::Mime((eventState.mimeType).c_str(), (eventState.mimeSubType).c_str())));
            }
            else
            {
               plainContents = std::unique_ptr<resip::PlainContents>(new resip::PlainContents(
                  resip::Data(eventState.contentUTF8.c_str(), eventState.contentLength),
                  resip::Mime((eventState.mimeType).c_str(), (eventState.mimeSubType).c_str())));
            }
            resip::SharedPtr<resip::SipMessage> notify = ci->dumServerSubscriptionHandle->update(plainContents.get());
            ci->dumServerSubscriptionHandle->send(notify);
         }
      }
   }
   return kSuccess;
}

int SipEventManagerInterface::end(SipEventSubscriptionHandle subscription, SipSubscriptionTerminateReason reason, int retryAfter)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << reason << ", " << retryAfter);
   postToSdkThread(resip::resip_bind(&SipEventManagerInterface::endImpl, this, subscription, reason, retryAfter));
   return kSuccess;
}

int SipEventManagerInterface::endImpl(SipEventSubscriptionHandle subscription, SipSubscriptionTerminateReason reason, int retryAfter)
{
   SipEventSubscriptionCreationInfo* ci = getCreationInfo(subscription);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         //SipEventManagerImpl* acct = itAcct->second;
         if (ci->dumClientSubscriptionHandle.isValid())
         {
            ci->dumClientSubscriptionHandle->end();
         }
         if (ci->dumServerSubscriptionHandle.isValid())
         {
            resip::TerminateReason terminateReason;
            switch(reason)
            {
               case SipSubscriptionTerminateReason_Deactivate:
                  terminateReason = resip::Deactivated;
                  break;
               case SipSubscriptionTerminateReason_Probation:
                  terminateReason = resip::Probation;
                  break;
               case SipSubscriptionTerminateReason_Rejected:
                  terminateReason = resip::Rejected;
                  break;
               case SipSubscriptionTerminateReason_Timeout:
                  terminateReason = resip::Timeout;
                  break;
               case SipSubscriptionTerminateReason_Giveup:
                  terminateReason = resip::Giveup;
                  break;
               case SipSubscriptionTerminateReason_NoResource:
                  terminateReason = resip::NoResource;
                  break;
               case SipSubscriptionTerminateReason_NoReason:
                  terminateReason = resip::NoReason;
                  break;
               default:
                  assert(false);
            }
            if (mEowMap->find(subscription) != mEowMap->end())
            {
               mEowMap->erase(subscription);
            }
            ci->dumServerSubscriptionHandle->end(terminateReason, NULL, retryAfter);
         }
      }
   }
   return kSuccess;
}

int SipEventManagerInterface::addSupportedEowHeader(SipEventSubscriptionHandle h)
{
   DebugLog(<< __FUNCTION__ << " " << h);
   postToSdkThread(resip::resip_bind(&SipEventManagerInterface::addSupportedEowHeaderImpl, this, h));
   return kSuccess;
}

int SipEventManagerInterface::addSupportedEowHeaderImpl(SipEventSubscriptionHandle h)
{
   SipEventSubscriptionCreationInfo* ci = getCreationInfo(h);
   if (ci != NULL)
   {
      (*mEowMap)[h] = true;
   }
   return kSuccess;
}

bool SipEventManagerInterface::recreateSubscription(CPCAPI2::SipAccount::SipAccountHandle account, SipEventSubscriptionHandle h)
{
   DebugLog(<< __FUNCTION__ << " " << account << ", " << h);
   DebugLog(<< "SipEventManagerInterface::recreateSubscription(): recreate subscription, account: " << account << ", subscription: " << h);
   AccountMap::iterator it = mAccountMap->find(account);
   if (it != mAccountMap->end())
   {
      SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
      if (acct && acct->isEnabled() && it->second->getDum())
      {
         SipEventSubscriptionCreationInfo* ci = new SipEventSubscriptionCreationInfo(*it->second->getDum(), it->second);
         ci->account = account;
         SipEventSubscriptionCreationInfo* ciOld = getCreationInfo(h);
         if (ciOld != NULL)
         {
            ciOld->displaced = true;

            ci->eventType = ciOld->eventType;
            ci->expiresSeconds = ciOld->expiresSeconds;
            ci->supportedMimeTypes = ciOld->supportedMimeTypes;
            ci->eventPackageParams = ciOld->eventPackageParams;
            it->second->removeCreationInfo(h);
         }
         it->second->addCreationInfo(h, ci);
         return true;
      }
   }

   DebugLog(<< "SipEventManagerInterface::recreateSubscription(): error recreating subscription, account: " << account << ", subscription: " << h);
   return false;
}

int SipEventManagerInterface::setEventServer(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << targetAddress);
   postToSdkThread(resip::resip_bind(&SipEventManagerInterface::addParticipantImpl, this, subscription, targetAddress, true));
   return kSuccess;
}

SipEventSubscriptionCreationInfo* SipEventManagerInterface::getCreationInfo(SipEventSubscriptionHandle h)
{
   assert(mPhone->getSdkModuleThread().isCurrentThread());
   AccountMap::iterator itAcct = mAccountMap->begin();
   for (; itAcct != mAccountMap->end(); ++itAcct)
   {
      SipEventSubscriptionCreationInfo* ci = itAcct->second->getCreationInfo(h);
      if (ci != NULL)
      {
         return ci;
      }
   }
   return NULL;
}

cpc::vector<SipEventSubscriptionHandle> SipEventManagerInterface::getSubscriptions(CPCAPI2::SipAccount::SipAccountHandle account)
{
   cpc::vector<SipEventSubscriptionHandle> subscriptions;

   // Get all the subscriptions for the specified account
   AccountMap::iterator itAcct = mAccountMap->find(account);
   if (itAcct != mAccountMap->end())
   {
      subscriptions = itAcct->second->getSubscriptions();
   }

   return subscriptions;
}

std::ostream& operator<<(std::ostream& os, const NewSubscriptionEvent& evt)
{
   return os << "NewSubscriptionEvent";
}

std::ostream& operator<<(std::ostream& os, const SubscriptionEndedEvent& evt)
{
   return os << "SubscriptionEndedEvent";
}

std::ostream& operator<<(std::ostream& os, const IncomingEventStateEvent& evt)
{
   return os << "IncomingEventStateEvent";
}

std::ostream& operator<<(std::ostream& os, const IncomingResourceListEvent& evt)
{
   return os << "IncomingResourceListEvent";
}

std::ostream& operator<<(std::ostream& os, const SubscriptionStateChangedEvent& evt)
{
   return os << "SubscriptionStateChangedEvent";
}

std::ostream& operator<<(std::ostream& os, const NotifySuccessEvent& evt)
{
   return os << "NotifySuccessEvent";
}

std::ostream& operator<<(std::ostream& os, const NotifyFailureEvent& evt)
{
   return os << "NotifyFailureEvent";
}

std::ostream& operator<<(std::ostream& os, const SipEvent::ErrorEvent& evt)
{
   return os << "SipEvent::ErrorEvent";
}

std::ostream& operator<<(std::ostream& os, const SipEventState& evt)
{
   return os << "SipEventState";
}

}
}
#endif
