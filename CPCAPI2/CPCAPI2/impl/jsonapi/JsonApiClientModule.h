#pragma once

#if !defined(CPCAPI2_JSON_API_CLIENT_MODULE_H)
#define CPCAPI2_JSON_API_CLIENT_MODULE_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiTransport.h"

#include <memory>
#include <document.h>

namespace CPCAPI2
{
namespace JsonApi
{
/**
*/
class JsonApiClientModule
{
public:
   virtual void setTransport(JsonApiTransport* transport) = 0;
   virtual int processIncoming(const std::shared_ptr<rapidjson::Document>& request) = 0;
};

}
}

#endif // CPCAPI2_JSON_API_CLIENT_MODULE_H
