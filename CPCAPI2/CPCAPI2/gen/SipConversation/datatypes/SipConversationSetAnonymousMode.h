#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSETANONYMOUSMODE_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSETANONYMOUSMODE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationSetAnonymousMode
  {
  public:
    int32_t phoneHandle;
    int32_t conversationHandle;
    int32_t anonymousMode;

    SipConversationSetAnonymousMode() = default;

    SipConversationSetAnonymousMode(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationSetAnonymousMode& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["conversationHandle"] = o.conversationHandle;
     j["anonymousMode"] = o.anonymousMode;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationSetAnonymousMode& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.conversationHandle = j.at("conversationHandle");
     o.anonymousMode = j.at("anonymousMode");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSETANONYMOUSMODE_H
