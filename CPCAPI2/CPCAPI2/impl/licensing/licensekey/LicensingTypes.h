#pragma once

#if !defined(__CPCAPI2_LICENSING_TYPES_H__)
#define __CPCAPI2_LICENSING_TYPES_H__

#include "licensing/LicensingClientTypes.h"
#include <string>
#include <vector>
#include <stdint.h>
#include <cpcstl/string.h>

namespace CPCAPI2
{
namespace Licensing
{
   struct License
   {
      std::string key;
      std::string type;
      time_t expiry;
      int64_t gracePeriod;
      std::vector<std::string> features;
   };

   struct InvalidLicense : public License
   {
      std::string id;
      int code;
      std::string message;
   };

   typedef std::vector<License> LicenseList;
   typedef std::vector<InvalidLicense> InvalidLicenseList;
}
}

#endif // __CPCAPI2_LICENSING_TYPES_H__
