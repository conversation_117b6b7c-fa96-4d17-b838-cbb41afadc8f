#pragma once

#if !defined(__CPCAPI2_SIP_CHAT_MANAGER_INTERFACE_H__)
#define __CPCAPI2_SIP_CHAT_MANAGER_INTERFACE_H__

#include "cpcapi2defs.h"
#include "SipChatInfo.h"
#include "../phone/PhoneModule.h"
#include "../account/SipAccountInterface.h"
#include "chat/SipChatManager.h"
#include "chat/SipChatHandler.h"

#include <ctime>

namespace CPCAPI2
{
class Phone;

namespace SipChat
{
class SipChatManagerImpl;

typedef std::map<SipAccount::SipAccountHandle, SipChatManagerImpl*> AccountMap;

class SipChatManagerInterface : public CPCAPI2::EventSource2<CPCAPI2::EventHandler<SipChatHandler, CPCAPI2::SipAccount::SipAccountHandle> >,
                                public SipChatManager,
                                public PhoneModule
{
public:
   SipChatManagerInterface(Phone* phone);
   virtual ~SipChatManagerInterface();

   FORWARD_EVENT_PROCESSOR(SipChatManagerInterface);

   // PhoneModule Interface
   virtual void Release() OVERRIDE;

   // SipChatManager Interface
   virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipChatHandler* handler) OVERRIDE;
   virtual SipChatHandle createChat(SipAccount::SipAccountHandle account) OVERRIDE;
   virtual int addParticipant(SipChatHandle chat, const cpc::string& participantSipAddress) OVERRIDE;
   virtual int start(SipChatHandle chat) OVERRIDE;
   virtual int end(SipChatHandle chat) OVERRIDE;
   virtual SipChatMessageHandle sendMessage(SipChatHandle chat, const cpc::string& messageContent, CPM::MimeType mimeType = MimeType_TextPlain, struct std::tm* datetime = 0, const cpc::vector<DispositionNotificationType>& dispositionNotifications = cpc::vector<DispositionNotificationType>()) OVERRIDE;
   virtual int accept(SipChatHandle chat) OVERRIDE;
   virtual int reject(SipChatHandle chat, unsigned int reasonCode) OVERRIDE;
   virtual SipChatMessageHandle notifyMessageDelivered(SipChatHandle chat, SipChatMessageHandle message, MessageDeliveryStatus messageDeliveryStatus) OVERRIDE;
   virtual SipChatMessageHandle notifyMessageDisplayed(SipChatHandle chat, SipChatMessageHandle message, MessageDisplayStatus messageDisplayStatus) OVERRIDE;
   virtual int setIsComposingMessage(SipChatHandle chat, CPM::MimeType mimeType = MimeType_TextPlain, struct std::tm* datetime = 0, int refreshInterval = 90, int idleInterval = 15) OVERRIDE;

private:
   SipAccount::SipAccountInterface* mAccountIf;
   PhoneInterface* mPhone;
   AccountMap accountMap;
   std::map<CPCAPI2::SipAccount::SipAccountHandle, SipChatHandler*> mHandlers;

   SipChatInfo* getChatInfo(SipChatHandle handle);
   SipChatManagerImpl* getChatManager(CPCAPI2::SipAccount::SipAccountHandle account);
   void setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipChatHandler* handler);
   void createChatImpl(SipAccount::SipAccountHandle account, SipChatHandle sessionHandle);
   void addParticipantImpl(SipChatHandle chat, const cpc::string& participantSipAddress);
   void startImpl(SipChatHandle chat);
   void endImpl(SipChatHandle chat);
   void sendMessageImpl(SipChatInfo* chatInfo, const SipChatMessageHandle& message, const cpc::string& messageContent, CPM::MimeType mimeType, struct std::tm* datetime, const cpc::vector<DispositionNotificationType>& dispositionNotifications);
   void acceptImpl(SipChatHandle chat);
   void rejectImpl(SipChatHandle chat, unsigned int reasonCode);
   void notifyMessageDeliveredImpl(SipChatHandle chat, const SipChatMessageHandle& message, const SipChatMessageHandle& origMessage, MessageDeliveryStatus messageDeliveryStatus);
   void notifyMessageDisplayedImpl(SipChatHandle chat, const SipChatMessageHandle& message, const SipChatMessageHandle& origMessage, MessageDisplayStatus messageDeliveryStatus);
   void setIsComposingMessageImpl(SipChatHandle chat, CPM::MimeType mimeType, struct std::tm datetime, int refreshInterval, int idleInterval);
};

std::ostream& operator<<(std::ostream& os, const NewChatEvent& evt);
std::ostream& operator<<(std::ostream& os, const IsComposingMessageEvent& evt);
std::ostream& operator<<(std::ostream& os, const NewMessageEvent& evt);
std::ostream& operator<<(std::ostream& os, const MessageDeliveredEvent& evt);
std::ostream& operator<<(std::ostream& os, const MessageDisplayedEvent& evt);
std::ostream& operator<<(std::ostream& os, const SendMessageSuccessEvent& evt);
std::ostream& operator<<(std::ostream& os, const SendMessageFailureEvent& evt);
std::ostream& operator<<(std::ostream& os, const NotifyMessageDeliveredSuccessEvent& evt);
std::ostream& operator<<(std::ostream& os, const NotifyMessageDisplayedSuccessEvent& evt);
std::ostream& operator<<(std::ostream& os, const SetIsComposingMessageSuccessEvent& evt);
std::ostream& operator<<(std::ostream& os, const ChatEndedEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipChat::ErrorEvent& evt);

}
}

#endif // __CPCAPI2_SIP_CHAT_MANAGER_INTERFACE_H__
