#include <nlohmann/json.hpp>
#include "DtmfMode.h"

#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSETDTMFMODE_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSETDTMFMODE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationSetDtmfMode
  {
  public:
    int32_t phoneHandle;
    int32_t account;
    int32_t ordinal;
    DtmfMode dtmfMode;

    SipConversationSetDtmfMode() = default;

    SipConversationSetDtmfMode(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationSetDtmfMode& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["account"] = o.account;
     j["ordinal"] = o.ordinal;
     j["dtmfMode"] = o.dtmfMode;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationSetDtmfMode& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.account = j.at("account");
     o.ordinal = j.at("ordinal");
     o.dtmfMode = j.at("dtmfMode");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSETDTMFMODE_H
