#include "CPInviteHandlerSelector.h"
#include "CPInviteHandlerSelectorDialogSet.h"

#include "resip/dum/InviteSession.hxx"

namespace CPCAPI2
{
namespace SipAccount
{

CPInviteHandlerSelector::CPInviteHandlerSelector() :
   defaultHandler(NULL)
{
}

CPInviteHandlerSelector::~CPInviteHandlerSelector()
{
}

resip::InviteSessionHandler* CPInviteHandlerSelector::operator()(resip::InviteSessionHandle session)
{ 
   // Check to see if we are dealing with a custom dialog set
   CPInviteHandlerSelectorDialogSet* ds = dynamic_cast<CPInviteHandlerSelectorDialogSet*>(session->getAppDialogSet().get());
   if (ds)
   {
      // Custom dialog set found
      // Use the handler associated with the dialog set
      return ds->getHandler();
   }
   else
   {
      // Standard dialog set found
      // Use the default registered handler
      return defaultHandler;
   }
}

}
}