#include "brand_branded.h"

#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_MODULE == 1)
#include "cpcapi2utils.h"
#include "interface/public/media/video/Video.h"
#include "confbridge/ConferenceBridgeHandler.h"
#include "ConferenceBridgeManagerImpl.h"
#include "ConferenceBridgeHandlerInternal.h"
#include "ConferenceWebParticipantImpl.h"
#include "../phone/PhoneInterface.h"
#include "call/SipConversationManager.h"
#include "../util/cpc_logger.h"
#include "../media/VideoFrameSnapshotHelper.h"
#include "../media/MediaManagerInterface.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "jsonapi/JsonApiServerSendTransportInterface.h"
#include "jsonapi/JsonApiServer_HTTP.h"
#if (CPCAPI2_BRAND_AUDIO_TRANS_MODULE == 1)
#include "audiotranscription/AudioTransInterface.h"
#endif
#include <MediaStackImpl.hxx>
#include <MixerImpl.hxx>

#include <rutil/Random.hxx>

#include <curlpp/cURLpp.hpp>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

#include <time.h>
#include <chrono>

using namespace resip;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::VideoStreaming;
using namespace CPCAPI2::AudioTrans;

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER
#define CONFBRIDGE_MANAGER_IMPL_SNAPSHOT_TIMER_ID 1

namespace CPCAPI2
{
namespace ConferenceBridge
{
ConferenceBridgeManagerImpl::ConferenceBridgeManagerImpl(ConferenceHandle h, CPCAPI2::Phone* phone, ConferenceBridgeManagerInterface* confBridgeMgrIf)
   : mHandle(h), mPhone(phone), mConvMgr(NULL), mMediaMgr(NULL), mConfBridgeMgrIf(confBridgeMgrIf), mVideoStreamMgr(NULL), mAudioTransMgr(NULL), mShutdown(false),
     mAppHandler(NULL), mParentConference(0xFFFF), mSfuMixSenderChannelAudio(-1), mSfuMixSenderChannelVideo(-1), mVideoStreamHandle((unsigned int)-1), mStreamingEnabled(false), mStreamingStarted(false), mRecordingHandle((unsigned int)-1), mRecordingEnabled(false)
{
   mMediaMgr = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone));
   mVideoStreamMgr = CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(mPhone);
   mMp4RecordingMgr = CPCAPI2::Mp4Recording::Mp4RecordingManager::getInterface(mPhone);
   mAudioTransMgr = CPCAPI2::AudioTrans::AudioTransManager::getInterface(mPhone);

   mConferenceToken = resip::Random::getCryptoRandom(4).base64encode(true).substr(0, 4).uppercase().c_str();

   mInfo.conference = mHandle;
   mInfo.conferenceToken = mConferenceToken;
   mInfo.streamId = -1;
   mInfo.transcriptionEnabled = false;
   mInfo.numParticipants = 0;
   mInfo.natTraversalServerInfo = confBridgeMgrIf->getConferenceBridgeConfig().natTraversalServerInfo;
   mInfo.mediaEncryptionMode = confBridgeMgrIf->getConferenceBridgeConfig().mediaEncryptionMode;

#if (CPCAPI2_BRAND_AUDIO_TRANS_MODULE == 1)
   if (mAudioTransMgr != NULL)
   {
      dynamic_cast<CPCAPI2::AudioTrans::AudioTransInterface*>(mAudioTransMgr)->addSdkObserver(this);
   }
#endif

   mVideoStreamHandle = mVideoStreamMgr->createVideoStream();
   mVideoStreamMgr->setVideoStreamHandler(mVideoStreamHandle, this);
   mInfo.streamId = mVideoStreamHandle;

   mCreateTime = std::chrono::system_clock::now();
}

ConferenceBridgeManagerImpl::~ConferenceBridgeManagerImpl()
{
   mParticipantMediaQueries.clear();
   mParticipantMediaStats.clear();

   if (mMp4RecordingMgr != NULL && mRecordingHandle != (unsigned int)-1)
   {
      mMp4RecordingMgr->stopRecording(mRecordingHandle);
   }

#if (CPCAPI2_BRAND_AUDIO_TRANS_MODULE == 1)
   if (mAudioTransMgr != NULL)
   {
      dynamic_cast<CPCAPI2::AudioTrans::AudioTransInterface*>(mAudioTransMgr)->removeSdkObserver(this);
   }
#endif

   mShutdown = true;
}

void ConferenceBridgeManagerImpl::setSdkObservers(const std::set<ConferenceBridgeHandler*>* observers)
{
   mSdkObservers = observers;
}

int ConferenceBridgeManagerImpl::setHandler(ConferenceBridgeHandler* handler)
{
   mAppHandler = handler;
   return 0;
}

int ConferenceBridgeManagerImpl::getNumParticipants() const
{
   return mSipParticipantInfo.size() + mWebParticipants.size();
}

std::chrono::system_clock::time_point ConferenceBridgeManagerImpl::getCreateTime() const
{
   return mCreateTime;
}

const std::vector<cpc::string> ConferenceBridgeManagerImpl::getParticipantInfoSummary() const
{
   std::vector<cpc::string> retVal;
   auto it = mWebParticipants.begin();
   for (; it != mWebParticipants.end(); ++it)
   {
      if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = it->second)
      {
         retVal.push_back(cwp->participantInfo().address);
      }
   }
   return retVal;
}

void ConferenceBridgeManagerImpl::setSFUSenderChannelVideo(int channel)
{
   mSfuMixSenderChannelVideo = channel;

   if (mStreamingEnabled)
   {
      if (mVideoStreamMgr != NULL && !mStreamingStarted)
      {
         VideoStreamSettings settings;
         settings.videoReceiveChannel = mSfuMixSenderChannelVideo;
         mVideoStreamMgr->setVideoStreamSettings(mVideoStreamHandle, settings);
         mVideoStreamMgr->startVideoStream(mVideoStreamHandle);
         mStreamingStarted = true;
      }
   }

   if (mRecordingEnabled && mRecordingHandle == (unsigned int)-1)
   {
      setRecordingEnabled(true);
   }
}

int ConferenceBridgeManagerImpl::getSFUSenderChannelVideo() const
{
   return mSfuMixSenderChannelVideo;
}

std::vector<std::weak_ptr<ConferenceWebParticipantImpl> >& ConferenceBridgeManagerImpl::getSFUParticipantChannelsVideo()
{
   return mSfuMixParticipantChannelsVideo;
}

void ConferenceBridgeManagerImpl::setSFUSenderChannelAudio(int channel)
{
   mSfuMixSenderChannelAudio = channel;
}

int ConferenceBridgeManagerImpl::getSFUSenderChannelAudio() const
{
   return mSfuMixSenderChannelAudio;
}

std::vector<int>& ConferenceBridgeManagerImpl::getSFUParticipantChannelsAudio()
{
   return mSfuMixParticipantChannelsAudio;
}

int ConferenceBridgeManagerImpl::getNumVideoStreams() const
{
   if (mVideoStreamMgr != NULL)
   {
      return mVideoStreamMgr->getStreamCount(mVideoStreamHandle);
   }
   return 0;
}

int convertMixMode(ConferenceMixMode mixMode)
{
   if (mixMode == ConferenceMixMode_MCU)
   {
      return 1;
   }
   if (mixMode == ConferenceMixMode_NoMixing)
   {
      return 2;
   }
   if (mixMode == ConferenceMixMode_SFU || mixMode == ConferenceMixMode_SFU_BiDi)
   {
      return 2; // frames are just forwarded, so treat this as no mixing from the perspective of MixerImpl
   }
   return 0;
}

int ConferenceBridgeManagerImpl::setConferenceSettings(const ConferenceSettings& conferenceSettings)
{
   mSettings = conferenceSettings;
   if (!conferenceSettings.conferenceToken.empty())
   {
      InfoLog(<< "conference token for conference " << mHandle << " updated from " << mConferenceToken << " to " << conferenceSettings.conferenceToken);
      mConferenceToken = conferenceSettings.conferenceToken;
      mInfo.conferenceToken = conferenceSettings.conferenceToken;
   }
   mInfo.label = conferenceSettings.label;
   mInfo.owner = (ConferenceParticipantHandle)-1;
   mInfo.tags = conferenceSettings.tags;
   bool isScreenshare = (std::find(conferenceSettings.tags.begin(), conferenceSettings.tags.end(), 0x2000) != conferenceSettings.tags.end() ? true : false);
   mInfo.bitrateConfig = (isScreenshare ? mConfBridgeMgrIf->getConferenceBridgeConfig().screenshareBitrateConfig : mConfBridgeMgrIf->getConferenceBridgeConfig().cameraBitrateConfig);
   
   std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mMediaMgr->media_stack_ptr()->mixer());
   mixer->setMixModeForMix(mHandle, convertMixMode(conferenceSettings.mixMode));
   mixer->setMixerSettings(conferenceSettings.mixerSettings);
   return 0;
}

int ConferenceBridgeManagerImpl::destroyConference()
{
   InfoLog(<< "ConferenceBridgeManagerImpl::destroyConference(): mHandle: " << mHandle);
   std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> >::iterator it = mWebParticipants.begin();
   for (; it != mWebParticipants.end(); ++it)
   {
      if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = it->second)
      {
         cwp->destroyWebParticipant();
      }
   }
   mMonitoredSipAccounts.clear();

   mVideoStreamMgr->stopVideoStream(mVideoStreamHandle);
   mVideoStreamMgr->destroyVideoStream(mVideoStreamHandle);
   mStreamingStarted = false;

   ConferenceEndedEvent evt;
   fireEvent(cpcFunc(ConferenceBridgeHandler::onConferenceEnded), evt);

   mParticipantMediaQueries.clear();
   mParticipantMediaStats.clear();

   return 0;
}

void ConferenceBridgeManagerImpl::postCallback(resip::ReadCallbackBase* rcb)
{
   mConfBridgeMgrIf->postCallback(rcb);
}

int ConferenceBridgeManagerImpl::addSipEndpoint(CPCAPI2::SipAccount::SipAccountHandle sipAccount)
{
   return 0;
}

int ConferenceBridgeManagerImpl::queryConferenceDetails(ConferenceDetailsHandler* handler)
{
   ConferenceDetailsResult args;
   getConferenceDetails(args);

   if (handler != NULL)
   {
      fireConfDetailsEvent(handler, cpcFunc(ConferenceDetailsHandler::onConferenceDetails), mHandle, args);
   }
   else
   {
      fireEvent(cpcFunc(ConferenceBridgeHandler::onConferenceDetails), args);
   }
   return 0;
}

int ConferenceBridgeManagerImpl::getConferenceDetails(ConferenceDetailsResult& confDetails) const
{
   confDetails.conference = mHandle;
   confDetails.conferenceToken = getConferenceToken();
   confDetails.conferenceInfo = mInfo;
   confDetails.persistent = mSettings.persistent;

   resip::Data confJoinUrl;
   {
      resip::DataStream ds(confJoinUrl);
      resip::Data configuredJoinUrl = mConfBridgeMgrIf->getConferenceBridgeConfig().httpJoinUrlBase.c_str();
      if (configuredJoinUrl.postfix("/"))
      {
         configuredJoinUrl = configuredJoinUrl.substr(0, configuredJoinUrl.size() - 1);
      }

      ds << configuredJoinUrl;
      ds << "/screenshare/";
      if (mConfBridgeMgrIf->getConferenceBridgeConfig().useServerUidInJoinUrls)
      {
         ds << mConfBridgeMgrIf->getConferenceBridgeConfig().serverUid;
         ds << "/";
      }
      ds << mConfBridgeMgrIf->getConferenceUrlRandomness() << "/conf/" << curlpp::escape(confDetails.conferenceToken.c_str());
   }

   confDetails.conferenceJoinUrl = confJoinUrl.c_str();

   std::set<ConferenceHandle>::const_iterator itAssoc = mAssociatedConferences.begin();
   for (; itAssoc != mAssociatedConferences.end(); ++itAssoc)
   {
      ConferenceBridgeManagerImpl* assocConf = mConfBridgeMgrIf->getImpl(*itAssoc);
      if (assocConf != NULL)
      {
         ConferenceBridge::ConferenceInfo assocInfo = assocConf->getConferenceInfo();
         confDetails.conferenceInfo.associatedConferences.push_back(assocInfo);
      }
   }
   return 0;
}

bool ConferenceBridgeManagerImpl::doesFloorOwnerExist() const
{
   for (std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> >::const_iterator i = mWebParticipants.begin(); i != mWebParticipants.end(); ++i)
   {
      if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = i->second)
      {
         if (cwp->participantInfo().hasFloor)
         {
            return true;
         }
      }
   }

   return false;
}

bool ConferenceBridgeManagerImpl::isFloorOwner(ConferenceParticipantHandle participant) const
{
   auto it = mWebParticipants.find(participant);
   if (it != mWebParticipants.end())
   {
      if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = it->second)
      {
         return cwp->participantInfo().hasFloor;
      }
   }
   return false;
}

int ConferenceBridgeManagerImpl::createWebParticipant(ConferenceParticipantHandle webParticipant, bool isContextOwner, const WebParticipantIdentity& identityInfo, bool addToFloor)
{
   if (!mDidAddRefCamera)
   {
      if (std::find(mSettings.tags.begin(), mSettings.tags.end(), 0x4000) == mSettings.tags.end())
      {
         mConfBridgeMgrIf->addCameraUsage();
         mDidAddRefCamera = true;
      }
   }

   std::shared_ptr<ConferenceWebParticipantImpl> cwp(new ConferenceWebParticipantImpl(webParticipant, mPhone, this));
   mWebParticipants[webParticipant] = cwp;
   cwp->participantInfo().permissions.canCreateConference = isContextOwner;
   cwp->participantInfo().displayName = identityInfo.displayName.empty() ? "unknown participant" : identityInfo.displayName;
   cwp->participantInfo().address = identityInfo.address.empty() ? "unknown@unknown" : identityInfo.address;
   cwp->participantInfo().tags = identityInfo.tags;

   DebugLog(<< "ConferenceBridgeManagerImpl::createWebParticipant(): web participant: " << webParticipant << " displayName: " << identityInfo.displayName << " identityInfo.owner: " << identityInfo.owner << " addToFloor: " << addToFloor);

   if (identityInfo.owner || (mSettings.mixMode == ConferenceMixMode_SFU && isContextOwner))
   {
      mInfo.owner = webParticipant;
   }

   mInfo.numParticipants = mWebParticipants.size();

   if (addToFloor || (mSettings.mixMode == ConferenceMixMode_SFU && isContextOwner))
   {
      // this will fail as expected if we're in SFU mode and someone already has the floor
      cwp->addToFloor(true);
   }

   cpc::vector<ParticipantInfo> addedParticipants;
   addedParticipants.push_back(cwp->participantInfo());
   fireParticipantListState(addedParticipants, cpc::vector<ParticipantInfo>(), cpc::vector<ParticipantInfo>());

   return 0;
}

int ConferenceBridgeManagerImpl::destroyWebParticipant(ConferenceParticipantHandle webParticipant)
{
   DebugLog(<< "ConferenceBridgeManagerImpl::destroyWebParticipant(): mWebParticipants.size(): " << mWebParticipants.size() << " participant: " << webParticipant);
   std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> >::iterator it = mWebParticipants.find(webParticipant);
   if (it != mWebParticipants.end())
   {
      if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = it->second)
      {
         bool wasConferenceOwner = (mInfo.owner == it->first);
         std::string displayName = cwp->participantInfo().displayName.c_str();
         //ConferenceParticipantHandle participant = it->second->participantInfo().participant;

         cpc::vector<ParticipantInfo> removedParticipants;
         removedParticipants.push_back(cwp->participantInfo());

         // DebugLog(<< "ConferenceBridgeManagerImpl::destroyWebParticipant(): mWebParticipants.size(): " << mWebParticipants.size() << " num-participants before destroy: " << getNumParticipants() << " destroying participant: " << displayName << " participant: " << participant);

         cwp->destroyWebParticipant();
         mWebParticipants.erase(it);
         mInfo.numParticipants--;

         fireParticipantListState(cpc::vector<ParticipantInfo>(), cpc::vector<ParticipantInfo>(), removedParticipants);
         int numParticipants = getNumParticipants();

         // DebugLog(<< "ConferenceBridgeManagerImpl::destroyWebParticipant(): number of participants after destroy: " << numParticipants << " conference mInfo.owner: " << mInfo.owner << " wasConferenceOwner: " << wasConferenceOwner << " web-participant: " << webParticipant << " settings persistence: " << mSettings.persistent);

         if ((numParticipants == 0) || wasConferenceOwner)
         {
            if (!mSettings.persistent)
            {
               InfoLog(<< "ConferenceBridgeManagerImpl::destroyWebParticipant(): destroying conference: " << mHandle << " as persistence is disabled, web-participant: " << webParticipant);
               mConfBridgeMgrIf->destroyConference(mHandle);
            }
            mSfuMixSenderChannelAudio = -1;
            mSfuMixSenderChannelVideo = -1;
            mVideoStreamMgr->stopVideoStream(mVideoStreamHandle);
            mStreamingStarted = false;

            if (mDidAddRefCamera)
            {
               mDidAddRefCamera = false;
               mConfBridgeMgrIf->removeCameraUsage();
            }

            // participants might stay in the conference, so we need to make sure they get re-connected
            auto itOtherPart = mWebParticipants.begin();
            for (; itOtherPart != mWebParticipants.end(); ++itOtherPart)
            {
               if (std::shared_ptr<ConferenceWebParticipantImpl> ocwp = itOtherPart->second)
               {
                  if (itOtherPart->first != webParticipant)
                  {
                     ocwp->connectParticipantStreams();
                  }
               }
            }
         }

         if (getNumParticipants() == 0)
         {
            std::set<ConferenceHandle>::const_iterator itAssoc = mAssociatedConferences.begin();
            for (; itAssoc != mAssociatedConferences.end(); ++itAssoc)
            {
               InfoLog(<< "ConferenceBridgeManagerImpl::destroyWebParticipant(): destroying participant: " << cwp->participantInfo().displayName << " destroying associated conference: " << *itAssoc << " as owner-conference: " << mHandle << " is being destroyed");
               mConfBridgeMgrIf->destroyConference(*itAssoc);
            }
         }
      }
   }
   return 0;
}

int ConferenceBridgeManagerImpl::setParticipantPermissions(ConferenceParticipantHandle participant, const ParticipantPermissions& permissionsInfo)
{
   std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> >::iterator it = mWebParticipants.find(participant);
   if (it != mWebParticipants.end())
   {
      if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = it->second)
      {
         cwp->participantInfo().permissions.canCreateConference = permissionsInfo.canCreateConference;

         cpc::vector<ParticipantInfo> updated;
         updated.push_back(cwp->participantInfo());
         fireParticipantListState(cpc::vector<ParticipantInfo>(), updated, cpc::vector<ParticipantInfo>());
      }
   }
   return 0;
}

int ConferenceBridgeManagerImpl::setWebParticipantIdentity(ConferenceParticipantHandle webParticipant, const WebParticipantIdentity& identityInfo, bool isContextOwner)
{
   std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> >::iterator it = mWebParticipants.find(webParticipant);
   if (it != mWebParticipants.end())
   {
      if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = it->second)
      {
         bool didUpdate = false;

         if (cwp->participantInfo().address != identityInfo.address)
         {
            cwp->participantInfo().address = identityInfo.address;
            didUpdate = true;
         }
         if (cwp->participantInfo().displayName != identityInfo.displayName)
         {
            cwp->participantInfo().displayName = identityInfo.displayName;
            didUpdate = true;
         }
         if (!(cwp->participantInfo().tags == identityInfo.tags))
         {
            cwp->participantInfo().tags = identityInfo.tags;
            didUpdate = true;
         }

         if (identityInfo.owner || (mSettings.mixMode == ConferenceMixMode_SFU && isContextOwner))
         {
            DebugLog(<< "ConferenceBridgeManagerImpl::setWebParticipantIdentity(): webParticipant: " << webParticipant << " owner: " << identityInfo.owner);
            mInfo.owner = webParticipant;
         }

         if (didUpdate)
         {
            cpc::vector<ParticipantInfo> updated;
            updated.push_back(cwp->participantInfo());
            fireParticipantListState(cpc::vector<ParticipantInfo>(), updated, cpc::vector<ParticipantInfo>());
         }
      }
   }
   return 0;
}

int ConferenceBridgeManagerImpl::sendPeerConnectionOffer(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpOffer, PeerConnectionAnswerHandler* handler)
{
   CPCAPI2::Media::Video* video = CPCAPI2::Media::Video::getInterface(mMediaMgr);
   video->setPreferredResolution(0, CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> >::iterator it = mWebParticipants.find(webParticipant);
   if (it != mWebParticipants.end())
   {
      if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = it->second)
      {
         cwp->sendPeerConnectionOffer(sdpOffer, handler);
      }
   }
   return 0;
}

int ConferenceBridgeManagerImpl::sendPeerConnectionAnswer(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpAnswer)
{
   CPCAPI2::Media::Video* video = CPCAPI2::Media::Video::getInterface(mMediaMgr);
   video->setPreferredResolution(0, CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> >::iterator it = mWebParticipants.find(webParticipant);
   if (it != mWebParticipants.end())
   {
      if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = it->second)
      {
         cwp->sendPeerConnectionAnswer(sdpAnswer);
      }
   }
   return 0;
}

int ConferenceBridgeManagerImpl::generateLocalOffer(ConferenceParticipantHandle webParticipant, PeerConnectionAnswerHandler* handler)
{
   CPCAPI2::Media::Video* video = CPCAPI2::Media::Video::getInterface(mMediaMgr);
   video->setPreferredResolution(0, CPCAPI2::Media::VideoCaptureResolution_MaxSupported);

   std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> >::iterator it = mWebParticipants.find(webParticipant);
   if (it != mWebParticipants.end())
   {
      if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = it->second)
      {
         cwp->generateLocalOffer(handler);
      }
   }
   return 0;
}

int ConferenceBridgeManagerImpl::takeParticipantSnapshot(ConferenceParticipantHandle participant)
{
#if (CPCAPI2_BRAND_MOZJPEG == 1)
   std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> >::iterator itWeb = mWebParticipants.find(participant);
   if (itWeb != mWebParticipants.end())
   {
      itWeb->second->takeParticipantSnapshot();
   }
   else
   {
      std::map<SipConversationHandle, ConferenceSipParticipantImpl*>::iterator itSip = mSipParticipantInfo.begin();
      for (; itSip != mSipParticipantInfo.end(); ++itSip)
      {
         if (itSip->second->participantInfo.participant == participant)
         {
            cpc::vector<CPCAPI2::SipConversation::MediaInfo>::const_iterator it = itSip->second->remoteMediaInfo.begin();
            for (; it != itSip->second->remoteMediaInfo.end(); ++it)
            {
               if (it->mediaType == SipConversation::MediaType_Video)
               {
                  std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mMediaMgr->media_stack_ptr()->mixer());
                  resip::Data filenamestr;
                  {
                     resip::DataStream ds(filenamestr);
                     ds << "snapshots/";
                     ds << getConferenceHandle() << "_";
                     ds << it->mediaStreamId << ".jpg";
                  }
                  cpc::string filenameUtf8 = filenamestr.c_str();
                  resip::ReadCallbackBase* completionHandler = resip::resip_bind(&ConferenceBridgeManagerImpl::onSnapshotComplete, this, itSip->first, filenameUtf8);
                  CPCAPI2::Media::VideoFrameSnapshotHelper* snapshotHelper = new CPCAPI2::Media::VideoFrameSnapshotHelper(mPhone, completionHandler, filenameUtf8, mixer, it->mediaStreamId);
               }
            }

            break;
         }
      }
   }
#endif
   return 0;
}

int ConferenceBridgeManagerImpl::setParticipantPhoto(ConferenceParticipantHandle participant, const cpc::string& photoFileNameUtf8)
{
   std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> >::iterator itWeb = mWebParticipants.find(participant);
   if (itWeb != mWebParticipants.end())
   {
      if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = itWeb->second)
      {
         cwp->setParticipantPhoto(photoFileNameUtf8);
      }
   }
   else
   {
      std::map<SipConversationHandle, ConferenceSipParticipantImpl*>::iterator itSip = mSipParticipantInfo.begin();
      for (; itSip != mSipParticipantInfo.end(); ++itSip)
      {
         if (itSip->second->participantInfo.participant == participant)
         {
            cpc::vector<CPCAPI2::SipConversation::MediaInfo>::const_iterator it = itSip->second->remoteMediaInfo.begin();
            for (; it != itSip->second->remoteMediaInfo.end(); ++it)
            {
               if (it->mediaType == SipConversation::MediaType_Video)
               {
                  std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mMediaMgr->media_stack_ptr()->mixer());
                  mixer->setParticipantPhoto(it->mediaStreamId, photoFileNameUtf8.c_str());
               }
            }

            break;
         }
      }
   }
   return 0;
}

int ConferenceBridgeManagerImpl::addAssociatedConference(ConferenceHandle conference)
{
   mAssociatedConferences.insert(conference);
   queryConferenceDetails();
   return 0;
}

int ConferenceBridgeManagerImpl::removeAssociatedConference(ConferenceHandle conference)
{
   mAssociatedConferences.erase(conference);
   queryConferenceDetails();
   return 0;
}

int ConferenceBridgeManagerImpl::setParentConference(ConferenceHandle conference)
{
   mParentConference = conference;
   return 0;
}

int ConferenceBridgeManagerImpl::addToFloor(ConferenceParticipantHandle participant)
{
   std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> >::iterator itWeb = mWebParticipants.find(participant);
   if (itWeb != mWebParticipants.end())
   {
      if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = itWeb->second)
      {
         cwp->addToFloor();
      }
   }
   else
   {
      std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mMediaMgr->media_stack_ptr()->mixer());
      std::map<SipConversationHandle, ConferenceSipParticipantImpl*>::iterator itSip = mSipParticipantInfo.begin();
      for (; itSip != mSipParticipantInfo.end(); ++itSip)
      {
         if (itSip->second->participantInfo.participant == participant)
         {
            cpc::vector<CPCAPI2::SipConversation::MediaInfo>::const_iterator it = itSip->second->remoteMediaInfo.begin();
            for (; it != itSip->second->remoteMediaInfo.end(); ++it)
            {
               if (it->mediaType == SipConversation::MediaType_Audio)
               {
                  mixer->addToAudioFloor(it->mediaStreamId);
               }
               else if (it->mediaType == SipConversation::MediaType_Video)
               {
                  mixer->addToVideoFloor(it->mediaStreamId);
               }
            }

            itSip->second->participantInfo.hasFloor = true;

            cpc::vector<ParticipantInfo> updatedParticipants;
            updatedParticipants.push_back(itSip->second->participantInfo);

            fireParticipantListState(cpc::vector<ParticipantInfo>(), updatedParticipants, cpc::vector<ParticipantInfo>());

            break;
         }
      }
   }
   return 0;
}

int ConferenceBridgeManagerImpl::removeFromFloor(ConferenceParticipantHandle participant)
{
   std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> >::iterator itWeb = mWebParticipants.find(participant);
   if (itWeb != mWebParticipants.end())
   {
      if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = itWeb->second)
      {
         cwp->removeFromFloor();
      }
   }
   else
   {
      std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mMediaMgr->media_stack_ptr()->mixer());
      std::map<SipConversationHandle, ConferenceSipParticipantImpl*>::iterator itSip = mSipParticipantInfo.begin();
      for (; itSip != mSipParticipantInfo.end(); ++itSip)
      {
         if (itSip->second->participantInfo.participant == participant)
         {
            cpc::vector<CPCAPI2::SipConversation::MediaInfo>::const_iterator it = itSip->second->remoteMediaInfo.begin();
            for (; it != itSip->second->remoteMediaInfo.end(); ++it)
            {
               if (it->mediaType == SipConversation::MediaType_Audio)
               {
                  mixer->removeFromAudioFloor(it->mediaStreamId);
               }
               else if (it->mediaType == SipConversation::MediaType_Video)
               {
                  mixer->removeFromVideoFloor(it->mediaStreamId);
               }
            }
            itSip->second->participantInfo.hasFloor = false;

            cpc::vector<ParticipantInfo> updatedParticipants;
            updatedParticipants.push_back(itSip->second->participantInfo);

            fireParticipantListState(cpc::vector<ParticipantInfo>(), updatedParticipants, cpc::vector<ParticipantInfo>());
            break;
         }
      }
   }
   return 0;
}

int ConferenceBridgeManagerImpl::setVideoLayout(VideoLayout layout)
{
   std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mMediaMgr->media_stack_ptr()->mixer());
   mixer->setVideoLayout(mHandle, (int)layout);
   return 0;
}

int ConferenceBridgeManagerImpl::setStreamingEnabled(bool enabled)
{
   if (enabled)
   {
      mStreamingEnabled = true;
      if (!mStreamingStarted)
      {
         if (mSettings.mixMode == ConferenceMixMode_SFU && (mSfuMixSenderChannelVideo >= 0))
         {
            if (mVideoStreamMgr != NULL)
            {
               VideoStreamSettings settings;
               settings.videoReceiveChannel = mSfuMixSenderChannelVideo;
               mVideoStreamMgr->setVideoStreamSettings(mVideoStreamHandle, settings);
               mVideoStreamMgr->startVideoStream(mVideoStreamHandle);
               mStreamingStarted = true;
            }
         }
         else if (mSettings.mixMode == ConferenceMixMode_MCU)
         {
            std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mMediaMgr->media_stack_ptr()->mixer());
            // !jjg! disable for now, since we don't want an 'extra' instance of the shared encoder to be kicking around
            // and actively using the opus encoder when there are no participants in the conference
            //mixer->startAudioMcuMixChannel(mHandle);
            int audioSendChannel = mixer->getAudioMcuMixChannel(mHandle);
            mixer->startVideoMcuMixChannel(mHandle);
            int videoSendChannel = mixer->getVideoMcuMixChannel(mHandle);
            if (mVideoStreamMgr != NULL)
            {
               VideoStreamSettings settings;
               settings.audioSendChannel = audioSendChannel;
               settings.videoSendChannel = videoSendChannel;
               mVideoStreamMgr->setVideoStreamSettings(mVideoStreamHandle, settings);
               mVideoStreamMgr->startVideoStream(mVideoStreamHandle);
               mStreamingStarted = true;
            }
         }
      }
   }
   else
   {
      mStreamingEnabled = false;
   }
   return 0;
}

cpc::string getRecordingFilename(const cpc::string& label)
{
   resip::Data filenameData;
   {
      resip::DataStream fds(filenameData);
      fds << label;
      fds << "_";
      fds << resip::Random::getCryptoRandom(4).base64encode(true).substr(0, 4).uppercase();
      fds << ".mp4";
   }
   return filenameData.c_str();
}

int ConferenceBridgeManagerImpl::setRecordingEnabled(bool enabled)
{
   if (enabled)
   {
      mRecordingEnabled = true;
      if (mRecordingHandle == (CPCAPI2::Mp4Recording::Mp4RecordingHandle)-1)
      {
         if (mSettings.mixMode == ConferenceMixMode_SFU && (mSfuMixSenderChannelVideo >= 0))
         {
            if (mMp4RecordingMgr != NULL)
            {
               mRecordingHandle = mMp4RecordingMgr->createRecordingSession();
               mMp4RecordingMgr->setMp4RecordingHandler(mRecordingHandle, this);
               CPCAPI2::Mp4Recording::Mp4RecordingSettings settings;
               settings.videoReceiveChannel = mSfuMixSenderChannelVideo;
               settings.filenameUtf8 = getRecordingFilename(mSettings.label);
               mMp4RecordingMgr->setMp4RecordingSettings(mRecordingHandle, settings);
               mMp4RecordingMgr->startRecording(mRecordingHandle);
            }
         }
         else if (mSettings.mixMode == ConferenceMixMode_MCU)
         {
            std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mMediaMgr->media_stack_ptr()->mixer());
            mixer->startAudioMcuMixChannel(mHandle);
            int audioSendChannel = mixer->getAudioMcuMixChannel(mHandle);
            mixer->startVideoMcuMixChannel(mHandle);
            int videoSendChannel = mixer->getVideoMcuMixChannel(mHandle);
            if (mMp4RecordingMgr != NULL)
            {
               mRecordingHandle = mMp4RecordingMgr->createRecordingSession();
               mMp4RecordingMgr->setMp4RecordingHandler(mRecordingHandle, this);
               CPCAPI2::Mp4Recording::Mp4RecordingSettings settings;
               settings.audioSendChannel = audioSendChannel;
               settings.videoSendChannel = videoSendChannel;
               settings.filenameUtf8 = getRecordingFilename(mSettings.label);
               mMp4RecordingMgr->setMp4RecordingSettings(mRecordingHandle, settings);
               mMp4RecordingMgr->startRecording(mRecordingHandle);
            }
         }
      }
   }
   else
   {
      mRecordingEnabled = false;
   }
   return 0;
}

int ConferenceBridgeManagerImpl::setParticipantTranscriptionEnabled(ConferenceParticipantHandle participant, bool enabled)
{
   if (enabled)
   {
      std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> >::iterator itWeb = mWebParticipants.find(participant);
      if (itWeb != mWebParticipants.end())
      {
         if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = itWeb->second)
         {
            cwp->setTranscriptionEnabled(enabled);
         }
      }
      else
      {
         std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mMediaMgr->media_stack_ptr()->mixer());
         std::map<SipConversationHandle, ConferenceSipParticipantImpl*>::iterator itSip = mSipParticipantInfo.begin();
         for (; itSip != mSipParticipantInfo.end(); ++itSip)
         {
            if (itSip->second->participantInfo.participant == participant)
            {
               itSip->second->transcriptionEnabled = true;
               cpc::vector<CPCAPI2::SipConversation::MediaInfo>::const_iterator it = itSip->second->remoteMediaInfo.begin();
               for (; it != itSip->second->remoteMediaInfo.end(); ++it)
               {
                  if (it->mediaType == SipConversation::MediaType_Audio)
                  {
                     itSip->second->audioTransHandle = mAudioTransMgr->createAudioTranscriptionSession();
                     AudioTransSettings settings;
                     settings.audioRecvChannel = it->mediaStreamId;
                     mAudioTransMgr->setAudioTransSettings(itSip->second->audioTransHandle, settings);
                     mAudioTransMgr->startTranscription(itSip->second->audioTransHandle);
                     break;
                  }
               }

               cpc::vector<ParticipantInfo> updatedParticipants;
               updatedParticipants.push_back(itSip->second->participantInfo);

               fireParticipantListState(cpc::vector<ParticipantInfo>(), updatedParticipants, cpc::vector<ParticipantInfo>());

               break;
            }
         }
      }
   }
   else
   {
      std::map<SipConversationHandle, ConferenceSipParticipantImpl*>::iterator itSip = mSipParticipantInfo.begin();
      for (; itSip != mSipParticipantInfo.end(); ++itSip)
      {
         if (itSip->second->participantInfo.participant == participant)
         {
            itSip->second->transcriptionEnabled = false;
            mAudioTransMgr->stopTranscription(itSip->second->audioTransHandle);
            itSip->second->audioTransHandle = (CPCAPI2::AudioTrans::AudioTransHandle) - 1;
            break;
         }
      }
#if (CPCAPI2_BRAND_AUDIO_TRANS_MODULE == 1)
      dynamic_cast<CPCAPI2::AudioTrans::AudioTransInterface*>(mAudioTransMgr)->removeSdkObserver(this);
#endif
   }
   return 0;
}

int ConferenceBridgeManagerImpl::setTranscriptionEnabled(bool enabled)
{
   if (enabled)
   {
      std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> >::iterator itWeb = mWebParticipants.begin();
      for (; itWeb != mWebParticipants.end(); ++itWeb)
      {
         if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = itWeb->second)
         {
            cwp->setTranscriptionEnabled(enabled);
         }
      }

      cpc::vector<ParticipantInfo> updatedParticipants;
      std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mMediaMgr->media_stack_ptr()->mixer());
      std::map<SipConversationHandle, ConferenceSipParticipantImpl*>::iterator itSip = mSipParticipantInfo.begin();
      for (; itSip != mSipParticipantInfo.end(); ++itSip)
      {
         itSip->second->transcriptionEnabled = true;
         cpc::vector<CPCAPI2::SipConversation::MediaInfo>::const_iterator it = itSip->second->remoteMediaInfo.begin();
         for (; it != itSip->second->remoteMediaInfo.end(); ++it)
         {
            if (it->mediaType == SipConversation::MediaType_Audio)
            {
               itSip->second->audioTransHandle = mAudioTransMgr->createAudioTranscriptionSession();
               AudioTransSettings settings;
               settings.audioRecvChannel = it->mediaStreamId;
               mAudioTransMgr->setAudioTransSettings(itSip->second->audioTransHandle, settings);
               mAudioTransMgr->startTranscription(itSip->second->audioTransHandle);
               break;
            }
         }

         updatedParticipants.push_back(itSip->second->participantInfo);
      }

      mInfo.transcriptionEnabled = true;
      fireParticipantListState(cpc::vector<ParticipantInfo>(), updatedParticipants, cpc::vector<ParticipantInfo>());
   }
   else
   {
      std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> >::iterator itWeb = mWebParticipants.begin();
      for (; itWeb != mWebParticipants.end(); ++itWeb)
      {
         if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = itWeb->second)
         {
            cwp->setTranscriptionEnabled(enabled);
         }
      }

      std::map<SipConversationHandle, ConferenceSipParticipantImpl*>::iterator itSip = mSipParticipantInfo.begin();
      for (; itSip != mSipParticipantInfo.end(); ++itSip)
      {
         itSip->second->transcriptionEnabled = false;
         mAudioTransMgr->stopTranscription(itSip->second->audioTransHandle);
         itSip->second->audioTransHandle = (CPCAPI2::AudioTrans::AudioTransHandle)-1;
      }

      mInfo.transcriptionEnabled = false;
   }

   ConferenceDetailsResult args;
   args.conference = mHandle;
   args.conferenceToken = getConferenceToken();
   args.conferenceInfo = mInfo;
   args.persistent = mSettings.persistent;
   fireEvent(cpcFunc(ConferenceBridgeHandler::onConferenceDetails), args);
   return 0;
}

int ConferenceBridgeManagerImpl::queryMediaStatistics()
{
#ifdef CPCAPI2_AUTO_TEST
   if (mParticipantMediaQueries.size() > 0)
   {
      DebugLog(<< "ConferenceBridgeManagerImpl::queryMediaStatistics(): conference: " << mHandle << " - ignore media query as one already in progress");
      return 0;
   }

   for (std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> >::iterator i = mWebParticipants.begin(); i != mWebParticipants.end(); ++i)
   {
      if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = i->second)
      {
         const ParticipantInfo& info = cwp->participantInfo();
         cwp->queryMediaStatistics();
         mParticipantMediaQueries.insert(info.participant);
         // StackLog(<< "ConferenceBridgeManagerImpl::queryMediaStatistics(): sending query for conference bridge: " << mHandle << " query participant: " << info.displayName << " (" << info.participant << ")");
      }
   }

   DebugLog(<< "ConferenceBridgeManagerImpl::queryMediaStatistics(): conference: " << mHandle << " with " << mWebParticipants.size() << " participants, total queries: " << mParticipantMediaQueries.size());

   if (mParticipantMediaQueries.size() == 0)
   {
      DebugLog(<< "ConferenceBridgeManagerImpl::queryMediaStatistics(): conference: " << mHandle << " - no existing media queries");
      ConferenceBridgeMediaStatisticsEvent args;
      fireInternalEvent(cpcFunc(ConferenceBridgeHandlerInternal::onConferenceBridgeMediaStatistics), mHandle, args);
   }
#endif

   return 0;
}

void ConferenceBridgeManagerImpl::addWebParticipantMediaStatistics(ConferenceParticipantHandle participant, cpc::vector<ConferenceParticipantMediaStatistics>& stats)
{
#ifdef CPCAPI2_AUTO_TEST
   std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> >::iterator i = mWebParticipants.find(participant);
   if (i == mWebParticipants.end())
   {
      InfoLog(<< "ConferenceBridgeManagerImpl::addWebParticipantMediaStatistics(): conference: " << mHandle << " media stats received for invalid participant: " << participant);
      return;
   }

   if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = i->second)
   {
      DebugLog(<< "ConferenceBridgeManagerImpl::addWebParticipantMediaStatistics(): media stats received for conference: " << mHandle << " participant: " << participant << " participant media-queries: " << mParticipantMediaQueries.size() << " participant media-stats: " << mParticipantMediaStats.size());
      const ParticipantInfo& info = cwp->participantInfo();

      ConferenceBridgeMediaStatistics bridgeMediaStats;
      bridgeMediaStats.conference = mHandle;
      bridgeMediaStats.participant = participant;
      bridgeMediaStats.owner = mInfo.owner;
      bridgeMediaStats.peerConnection = info.webParticipantDetails.peerConnection;
      bridgeMediaStats.label = mInfo.label;
      bridgeMediaStats.address = info.address;
      bridgeMediaStats.displayName = info.displayName;
      bridgeMediaStats.hasFloor = info.hasFloor;
      bridgeMediaStats.streamStats = stats;
      mParticipantMediaStats[participant] = bridgeMediaStats;

      mParticipantMediaQueries.erase(participant);

      // StackLog(<< "ConferenceBridgeManagerImpl::addWebParticipantMediaStatistics(): conference: " << mHandle << " participant media-queries pending: " << mParticipantMediaQueries.size() << " participant media-stats received: " << mParticipantMediaStats.size());
      if (mParticipantMediaQueries.size() == 0)
      {
         DebugLog(<< "ConferenceBridgeManagerImpl::addWebParticipantMediaStatistics(): " << mHandle << " response received for all participant media queries: " << mParticipantMediaStats.size());
         ConferenceBridgeMediaStatisticsEvent args;
         for (std::map<ConferenceParticipantHandle, ConferenceBridgeMediaStatistics>::iterator j = mParticipantMediaStats.begin(); j != mParticipantMediaStats.end(); ++j)
         {
            args.bridgeMediaStats.push_back(j->second);
         }

         fireInternalEvent(cpcFunc(ConferenceBridgeHandlerInternal::onConferenceBridgeMediaStatistics), mHandle, args);
      }
   }
#endif
}

bool isSameParticipant(const ParticipantInfo& lhs, const ParticipantInfo& rhs)
{
   return (lhs.participant == rhs.participant &&
      lhs.participantType == rhs.participantType &&
      lhs.address == rhs.address &&
      lhs.displayName == rhs.displayName &&
      lhs.tags == rhs.tags &&
      lhs.hasFloor == rhs.hasFloor);
}

bool isSameParticipantList(const cpc::vector<ParticipantInfo>& lhs, const cpc::vector<ParticipantInfo>& rhs)
{
   if (lhs.size() != rhs.size())
      return false;

   for (const ParticipantInfo& pil : lhs)
   {
      bool found = false;
      for (const ParticipantInfo& pir : rhs)
      {
         if (isSameParticipant(pil, pir))
         {
            found = true;
            break;
         }
      }
      if (!found)
      {
         return false;
      }
   }
   return true;
}


void ConferenceBridgeManagerImpl::fireParticipantListState(const cpc::vector<ParticipantInfo>& added, const cpc::vector<ParticipantInfo>& updated, const cpc::vector<ParticipantInfo>& removed)
{
   ParticipantListState args;
   std::map<ConferenceParticipantHandle, std::shared_ptr<ConferenceWebParticipantImpl> >::const_iterator itWebPart = mWebParticipants.begin();
   for (; itWebPart != mWebParticipants.end(); ++itWebPart)
   {
      if (std::shared_ptr<ConferenceWebParticipantImpl> cwp = itWebPart->second)
      {
         args.participants.push_back(cwp->participantInfo());
      }
   }
   std::map<SipConversationHandle, ConferenceSipParticipantImpl*>::const_iterator itSipPart = mSipParticipantInfo.begin();
   for (; itSipPart != mSipParticipantInfo.end(); ++itSipPart)
   {
      args.participants.push_back(itSipPart->second->participantInfo);
   }

#if 0
   // added 
   auto itCurr = args.participants.begin();
   for (; itCurr != args.participants.end(); ++itCurr)
   {
      bool bFound = false;
      auto itPrev = mPrevParticipantListEvent.participants.begin();
      for (; itPrev != mPrevParticipantListEvent.participants.end(); ++itPrev)
      {
         if (itCurr->participant == itPrev->participant)
         {
            bFound = true;
         }
      }
      if (!bFound)
      {
         args.addedParticipants.push_back(*itCurr);
      }
   }
   // updated
   itCurr = args.participants.begin();
   for (; itCurr != args.participants.end(); ++itCurr)
   {
      bool bFound = false;
      auto itPrev = mPrevParticipantListEvent.participants.begin();
      for (; itPrev != mPrevParticipantListEvent.participants.end(); ++itPrev)
      {
         if (itCurr->participant == itPrev->participant)
         {
            bFound = true;
            break;
         }
      }
      if (bFound)
      {
         if (!(itPrev->address == itCurr->address &&
            itPrev->displayName == itCurr->displayName &&
            itPrev->hasFloor == itCurr->hasFloor &&
            itPrev->participantType == itCurr->participantType &&
            itPrev->permissions.canCreateConference == itCurr->permissions.canCreateConference &&
            itPrev->tags.size() == itCurr->tags.size() &&
            itPrev->webParticipantDetails.peerConnection == itCurr->webParticipantDetails.peerConnection))
         {
            args.updatedParticipants.push_back(*itCurr);
         }
      }
   }
   // removed 
   auto itPrev = mPrevParticipantListEvent.participants.begin();
   for (; itPrev != mPrevParticipantListEvent.participants.end(); ++itPrev)
   {
      bool bFound = false;
      auto itCurr = args.participants.begin();
      for (; itCurr != args.participants.end(); ++itCurr)
      {
         if (itCurr->participant == itPrev->participant)
         {
            bFound = true;
         }
      }
      if (!bFound)
      {
         args.removedParticipants.push_back(*itPrev);
      }
   }
#endif

   if (args.participants.size() > 25)
   {
      mUseLiteParticipantListEvents = true;
   }

   if (mUseLiteParticipantListEvents)
   {
      mNextParticipantListEvents.clear();

      cpc::vector<ParticipantInfo> floorParticipants;
      for (const ParticipantInfo& pi : args.participants)
      {
         if (pi.hasFloor)
         {
            floorParticipants.push_back(pi);
         }
      }

      if (!isSameParticipantList(floorParticipants, mLastParticipantFloorSet))
      {
         ParticipantListState partListStateEvt;
         partListStateEvt.participants = floorParticipants;
         mNextParticipantListEvents.push_back(partListStateEvt);
         mParticipantListEventPending = true;
         mLastParticipantFloorSet = floorParticipants;
         fireParticipantListStateThrottled(mConfBridgeMgrIf, mHandle);
      }
      else
      {
         // used only to update the summary
         if (!mParticipantListEventPending)
         {
            mParticipantListEventPending = true;
            dynamic_cast<PhoneInterface*>(mPhone)->getSdkModuleThread().postMS(resip::resip_static_bind(&ConferenceBridgeManagerImpl::fireParticipantListStateThrottled, mConfBridgeMgrIf, mHandle), 1000);
         }
      }
   }
   else
   {
      mNextParticipantListEvents.clear();
      ParticipantListState partListStateEvt;
      partListStateEvt.participants = args.participants;
      mNextParticipantListEvents.push_back(partListStateEvt);

      if (args.participants.size() <= 5)
      {
         mParticipantListEventPending = true;
         fireParticipantListStateThrottled(mConfBridgeMgrIf, mHandle);
      }
      else
      {
         if (!mParticipantListEventPending)
         {
            mParticipantListEventPending = true;
            dynamic_cast<PhoneInterface*>(mPhone)->getSdkModuleThread().postMS(resip::resip_static_bind(&ConferenceBridgeManagerImpl::fireParticipantListStateThrottled, mConfBridgeMgrIf, mHandle), 200);
         }
      }
   }
}

void ConferenceBridgeManagerImpl::fireParticipantListStateThrottled(ConferenceBridgeManagerInterface* confBridgeMgrIf, ConferenceHandle conf)
{
   ConferenceBridgeManagerImpl* impl = confBridgeMgrIf->getImpl(conf);
   if (impl != NULL)
   {
      if (impl->mParticipantListEventPending)
      {
         impl->mParticipantListEventPending = false;
         auto it = impl->mNextParticipantListEvents.begin();
         for (; it != impl->mNextParticipantListEvents.end(); ++it)
         {
            it->isPartialUpdate = (impl->mNextParticipantListEvents.size() > 1);
            it->isLastChunk = ((it + 1) == impl->mNextParticipantListEvents.end());
            impl->fireEvent(cpcFunc(ConferenceBridgeHandler::onParticipantListState), *it);
         }
         confBridgeMgrIf->updateConferenceSummaryTs();
      }
   }
}

///////////////////////////////////////////////////////////////////////////////
// SipConversationHandler
int ConferenceBridgeManagerImpl::onNewConversation(SipConversationHandle conversation, const NewConversationEvent& args)
{
#if 0
   InfoLog(<< "ConferenceBridgeManagerImpl::onNewConversation: " << conversation << " from " << args.remoteAddress);

   CPCAPI2::SipAccount::SipAccountHandle acct = mConvMgr->getSipAccountHandle(conversation);
   if (mMonitoredSipAccounts.count(acct) == 0)
   {
      return 0;
   }

   if (args.conversationType == CPCAPI2::SipConversation::ConversationType_Incoming)
   {
      mConvMgr->setMediaEnabled(conversation, SipConversation::MediaType_Audio, true);
      mConvMgr->setMediaEnabled(conversation, SipConversation::MediaType_Video, true);

      SipConversation::MediaInfo miAudio;
      miAudio.mediaType = SipConversation::MediaType_Audio;
      miAudio.mediaDirection = SipConversation::MediaDirection_SendReceive;
      miAudio.conferenceMixId = this->mHandle;
      mConvMgr->configureMedia(conversation, miAudio);

      SipConversation::MediaInfo miVideo;
      miVideo.mediaType = SipConversation::MediaType_Video;
      miVideo.mediaDirection = SipConversation::MediaDirection_SendReceive;
      miVideo.conferenceMixId = this->mHandle;
      mConvMgr->configureMedia(conversation, miVideo);

      mConvMgr->accept(conversation);
   }

   ParticipantInfo partInfo;
   partInfo.participant = ConferenceParticipantHandleFactory::getNext();
   partInfo.address = args.remoteAddress.c_str();
   partInfo.displayName = args.remoteDisplayName.c_str();
   partInfo.participantType = ParticipantType_SIP;
   partInfo.sipParticipantDetails.conversation = conversation;
   auto msecs = std::chrono::time_point_cast<std::chrono::milliseconds>(std::chrono::high_resolution_clock::now()).time_since_epoch();
   partInfo.joinTimestampMsecs = std::chrono::duration_cast<std::chrono::milliseconds>(msecs).count();

   cpc::vector<CPCAPI2::SipConversation::MediaInfo>::const_iterator itMedia = args.remoteMediaInfo.begin();
   for (; itMedia != args.remoteMediaInfo.end(); ++itMedia)
   {
      if (itMedia->mediaType == CPCAPI2::SipConversation::MediaType_Audio)
      {
         //partInfo.media.push_back({ ParticipantMediaStreamType_Audio, ParticipantMediaState_SendRecv });
      }
      else if (itMedia->mediaType == CPCAPI2::SipConversation::MediaType_Video)
      {
         //partInfo.media.push_back({ ParticipantMediaStreamType_Video, ParticipantMediaState_SendRecv });
      }
   }
   ConferenceSipParticipantImpl* sipPart = new ConferenceSipParticipantImpl();
   sipPart->participantInfo = partInfo;
   mSipParticipantInfo[conversation] = sipPart;
   mInfo.numParticipants++;
   mConfBridgeMgrIf->addParticipantMapping(partInfo.participant, mHandle);

   cpc::vector<ParticipantInfo> addedParticipants;
   addedParticipants.push_back(partInfo);
   fireParticipantListState(addedParticipants, cpc::vector<ParticipantInfo>(), cpc::vector<ParticipantInfo>());
#endif
   return 0;
}

int ConferenceBridgeManagerImpl::onConversationEnded(SipConversationHandle conversation, const ConversationEndedEvent& args)
{
#if 0
   InfoLog(<< "ConferenceBridgeManagerImpl::onConversationEnded: Conversation " << conversation << "ended");
   CPCAPI2::SipAccount::SipAccountHandle acct = mConvMgr->getSipAccountHandle(conversation);
   if (mMonitoredSipAccounts.count(acct) == 0)
   {
      return 0;
   }

   mConvMgr->refreshConversationStatistics(conversation, true, true, true);

   std::map<SipConversationHandle, ConferenceSipParticipantImpl*>::iterator it = mSipParticipantInfo.find(conversation);

   if (it != mSipParticipantInfo.end())
   {
      mConfBridgeMgrIf->removeParticipantMapping(it->second->participantInfo.participant);
      if (mAudioTransMgr != NULL)
      {
         mAudioTransMgr->stopTranscription(it->second->audioTransHandle);
      }
      cpc::vector<ParticipantInfo> removedParticipants;
      removedParticipants.push_back(it->second->participantInfo);
      delete it->second;
      mSipParticipantInfo.erase(it);
      mInfo.numParticipants--;

      fireParticipantListState(cpc::vector<ParticipantInfo>(), cpc::vector<ParticipantInfo>(), removedParticipants);
   }
#endif
   return 0;
}

int ConferenceBridgeManagerImpl::onIncomingTransferRequest(SipConversationHandle conversation, const TransferRequestEvent& args)
{
   return 0;
}

int ConferenceBridgeManagerImpl::onIncomingRedirectRequest(SipConversationHandle conversation, const RedirectRequestEvent& args)
{
   return 0;
}

int ConferenceBridgeManagerImpl::onIncomingTargetChangeRequest(SipConversationHandle conversation, const TargetChangeRequestEvent& args)
{
   return 0;
}

int ConferenceBridgeManagerImpl::onIncomingHangupRequest(SipConversationHandle conversation, const HangupRequestEvent& args)
{
   return 0;
}

int ConferenceBridgeManagerImpl::onIncomingBroadsoftTalkRequest(SipConversationHandle conversation, const BroadsoftTalkEvent& args)
{
   return 0;
}

int ConferenceBridgeManagerImpl::onIncomingBroadsoftHoldRequest(SipConversationHandle conversation, const BroadsoftHoldEvent& args)
{
   return 0;
}

int ConferenceBridgeManagerImpl::onTransferProgress(SipConversationHandle conversation, const TransferProgressEvent& args)
{
   return 0;
}

int ConferenceBridgeManagerImpl::onConversationStateChangeRequest(SipConversationHandle conversation, const ConversationStateChangeRequestEvent& args)
{
   return 0;
}

int ConferenceBridgeManagerImpl::onConversationStateChanged(SipConversationHandle conversation, const ConversationStateChangedEvent& args)
{
#if 0
   CPCAPI2::SipAccount::SipAccountHandle acct = mConvMgr->getSipAccountHandle(conversation);
   if (mMonitoredSipAccounts.count(acct) == 0)
   {
      return 0;
   }

   if (args.conversationState == CPCAPI2::SipConversation::ConversationState_Connected)
   {
      if (mSettings.participantJoinSound.size() > 0)
      {
         mConvMgr->playSound(conversation, mSettings.participantJoinSound, false);
      }
   }
#endif
   return 0;
}

int ConferenceBridgeManagerImpl::onConversationMediaChangeRequest(SipConversationHandle conversation, const ConversationMediaChangeRequestEvent& args)
{
#if 0
   CPCAPI2::SipAccount::SipAccountHandle acct = mConvMgr->getSipAccountHandle(conversation);
   if (mMonitoredSipAccounts.count(acct) == 0)
   {
      return 0;
   }

   mConvMgr->accept(conversation);
#endif
   return 0;
}

int ConferenceBridgeManagerImpl::onConversationMediaChanged(SipConversationHandle conversation, const ConversationMediaChangedEvent& args)
{
#if 0
   CPCAPI2::SipAccount::SipAccountHandle acct = mConvMgr->getSipAccountHandle(conversation);
   if (mMonitoredSipAccounts.count(acct) == 0)
   {
      return 0;
   }

   std::map<SipConversationHandle, ConferenceSipParticipantImpl*>::iterator itSipPart = mSipParticipantInfo.find(conversation);
   if (itSipPart != mSipParticipantInfo.end())
   {
      itSipPart->second->remoteMediaInfo = args.remoteMediaInfo;
      if (itSipPart->second->transcriptionEnabled && itSipPart->second->audioTransHandle == (CPCAPI2::AudioTrans::AudioTransHandle)-1)
      {
         setParticipantTranscriptionEnabled(itSipPart->second->participantInfo.participant, true);
      }
   }
   
#if (CPCAPI2_BRAND_MOZJPEG == 1)
   cpc::vector<CPCAPI2::SipConversation::MediaInfo>::const_iterator it = args.remoteMediaInfo.begin();
   for (; it != args.remoteMediaInfo.end(); ++it)
   {
      if (it->mediaType == SipConversation::MediaType_Video)
      {
         std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mMediaMgr->media_stack_ptr()->mixer());
         resip::Data filenamestr;
         {
            resip::DataStream ds(filenamestr);
            ds << "snapshots/";
            ds << it->mediaStreamId << ".jpg";
         }
         cpc::string filenameUtf8 = filenamestr.c_str();
         resip::ReadCallbackBase* completionHandler = resip::resip_bind(&ConferenceBridgeManagerImpl::onSnapshotComplete, this, conversation, filenameUtf8);
         CPCAPI2::Media::VideoFrameSnapshotHelper* snapshotHelper = new CPCAPI2::Media::VideoFrameSnapshotHelper(mPhone, completionHandler, filenameUtf8, mixer, it->mediaStreamId);
      }
   }
#endif
#endif
   return 0;
}

int ConferenceBridgeManagerImpl::onConversationStatisticsUpdated(SipConversationHandle conversation, const ConversationStatisticsUpdatedEvent& args)
{
#if 0
   DebugLog(<< "=========== CONVERSATION STATISTICS =============");
   CPCAPI2::SipAccount::SipAccountHandle acct = mConvMgr->getSipAccountHandle(conversation);
   if (mMonitoredSipAccounts.count(acct) == 0)
   {
      return 0;
   }

   CPCAPI2::SipConversation::ConversationStatistics conversationStatistics = args.conversationStatistics;
   if (conversationStatistics.audioChannels.size() > 0)
   {
      DebugLog(<< "---------- LOCAL ----------"
         << "cumulativeLost:      " << conversationStatistics.audioChannels[0].streamStatistics.cumulativeLost << std::endl
         << "fractionLost:        " << conversationStatistics.audioChannels[0].streamStatistics.fractionLost << std::endl
         << "jitterSamples:       " << conversationStatistics.audioChannels[0].streamStatistics.jitterSamples << std::endl
         << "rttMs:               " << conversationStatistics.audioChannels[0].streamStatistics.rttMs << std::endl
         << "packetsReceived:     " << conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived << std::endl
         << "packetsSent:         " << conversationStatistics.audioChannels[0].streamDataCounters.packetsSent << std::endl
         << "decoder.plname:      " << conversationStatistics.audioChannels[0].decoder.plname << std::endl
         << "encoder.plname:      " << conversationStatistics.audioChannels[0].encoder.plname << std::endl
         << "localEndpoint:       " << conversationStatistics.audioChannels[0].endpoint.ipAddress << ":" << conversationStatistics.audioChannels[0].endpoint.port);
   }
   if (conversationStatistics.videoChannels.size() > 0)
   {
      DebugLog(<< "---------- LOCAL (video) ----------"
         << "cumulativeLost:       " << conversationStatistics.videoChannels[0].streamStatistics.cumulativeLost << std::endl
         << "fractionLost:         " << conversationStatistics.videoChannels[0].streamStatistics.fractionLost << std::endl
         << "jitterSamples:        " << conversationStatistics.videoChannels[0].streamStatistics.jitterSamples << std::endl
         << "rttMs:                " << conversationStatistics.videoChannels[0].streamStatistics.rttMs << std::endl
         << "packetsReceived:      " << conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived << std::endl
         << "packetsSent:          " << conversationStatistics.videoChannels[0].streamDataCounters.packetsSent << std::endl
         << "decoder.plname:       " << conversationStatistics.videoChannels[0].decoder.plName << std::endl
         << "encoder.plname:       " << conversationStatistics.videoChannels[0].encoder.plName << std::endl
         << "currentTargetBitrate: " << conversationStatistics.videoChannels[0].currentTargetBitrate << std::endl
         << "localEndpoint:        " << conversationStatistics.videoChannels[0].endpoint.ipAddress << ":" << conversationStatistics.videoChannels[0].endpoint.port);
   }
   if (conversationStatistics.remoteAudioChannels.size() > 0)
   {
      DebugLog(<< "---------- REMOTE ----------"
         << "cumulativeLost:      " << conversationStatistics.remoteAudioChannels[0].streamStatistics.cumulativeLost << std::endl
         << "fractionLost:        " << conversationStatistics.remoteAudioChannels[0].streamStatistics.fractionLost << std::endl
         << "jitterSamples:       " << conversationStatistics.remoteAudioChannels[0].streamStatistics.jitterSamples << std::endl
         << "rttMs:               " << conversationStatistics.remoteAudioChannels[0].streamStatistics.rttMs << std::endl
         << "remoteEndpoint:      " << conversationStatistics.remoteAudioChannels[0].endpoint.ipAddress << ":" << conversationStatistics.remoteAudioChannels[0].endpoint.port);
   }
   if (conversationStatistics.remoteVideoChannels.size() > 0)
   {
      DebugLog(<< "---------- REMOTE (video) ----------" << std::endl
         << "cumulativeLost:      " << conversationStatistics.remoteVideoChannels[0].streamStatistics.cumulativeLost << std::endl
         << "fractionLost:        " << conversationStatistics.remoteVideoChannels[0].streamStatistics.fractionLost << std::endl
         << "jitterSamples:       " << conversationStatistics.remoteVideoChannels[0].streamStatistics.jitterSamples << std::endl
         << "rttMs:               " << conversationStatistics.remoteVideoChannels[0].streamStatistics.rttMs << std::endl
         << "remoteEndpoint:      " << conversationStatistics.remoteVideoChannels[0].endpoint.ipAddress << ":" << conversationStatistics.remoteVideoChannels[0].endpoint.port);
   }
   DebugLog(<< "======> Call Quality: " << conversationStatistics.callQuality);
#endif
   return 0;
}

int ConferenceBridgeManagerImpl::onError(SipConversationHandle conversation, const CPCAPI2::SipConversation::ErrorEvent& args)
{
   return 0;
}

void ConferenceBridgeManagerImpl::onSnapshotComplete(CPCAPI2::SipConversation::SipConversationHandle conversation, const cpc::string& filenameUtf8)
{
   std::map<SipConversationHandle, ConferenceSipParticipantImpl*>::iterator it = mSipParticipantInfo.find(conversation);
   //it->second->participantInfo.snapshotFilenameUtf8 = filenameUtf8;

   cpc::vector<ParticipantInfo> updatedParticipants;
   updatedParticipants.push_back(it->second->participantInfo);

   fireParticipantListState(cpc::vector<ParticipantInfo>(), updatedParticipants, cpc::vector<ParticipantInfo>());
}

int ConferenceBridgeManagerImpl::onTranscriptionResult(CPCAPI2::AudioTrans::AudioTransHandle audioTransSession, const CPCAPI2::AudioTrans::AudioTranscriptionResult& evt)
{
   std::map<SipConversationHandle, ConferenceSipParticipantImpl*>::iterator itSipPart = mSipParticipantInfo.begin();
   for (; itSipPart != mSipParticipantInfo.end(); ++itSipPart)
   {
      if (itSipPart->second->audioTransHandle == audioTransSession)
      {
         ConferenceTranscriptionEvent args;
         args.participant = itSipPart->second->participantInfo.participant;
         args.transcriptionResult = evt.result;
         args.resultSetId = evt.resultSetId;
         fireEvent(cpcFunc(ConferenceBridgeHandler::onConferenceTranscriptionResult), args);
         break;
      }
   }
   return 0;
}

}
}

#endif
