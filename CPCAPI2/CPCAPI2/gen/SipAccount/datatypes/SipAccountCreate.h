#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTCREATE_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTCREATE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  class SipAccountCreate
  {
  public:
    int32_t phoneHandle;

    SipAccountCreate() = default;

    SipAccountCreate(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipAccountCreate& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipAccountCreate& o)
    {
     o.phoneHandle = j.at("phoneHandle");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_SIPACCOUNTCREATE_H
