// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef XmppFileTransfer_JSON_HELPER_H
#define XmppFileTransfer_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/public/xmpp/XmppFileTransferManager.h>
#include <interface/public/xmpp/XmppFileTransferHandler.h>
#include <interface/public/xmpp/XmppFileTransferState.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppFileTransfer::XmppFileTransferItemDetail& v)
    {
      w.StartObject();
      Write(w, "handle", v.handle);
      Write(w, "localfilePath", v.localfilePath);
      Write(w, "localfileName", v.localfileName);
      Write(w, "remotefileName", v.remotefileName);
      Write(w, "transferType", v.transferType);
      Write(w, "fileSizeBytes", v.fileSizeBytes);
      Write(w, "isIncoming", v.isIncoming);
      Write(w, "acceptedState", v.acceptedState);
      Write(w, "percentComplete", v.percentComplete);
      Write(w, "streamTypes", v.streamTypes);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppFileTransfer::XmppFileTransferItemDetail& v)
    {
      Read(json, "handle", v.handle);
      Read(json, "localfilePath", v.localfilePath);
      Read(json, "localfileName", v.localfileName);
      Read(json, "remotefileName", v.remotefileName);
      Read(json, "transferType", v.transferType);
      Read(json, "fileSizeBytes", v.fileSizeBytes);
      Read(json, "isIncoming", v.isIncoming);
      Read(json, "acceptedState", v.acceptedState);
      Read(json, "percentComplete", v.percentComplete);
      Read(json, "streamTypes", v.streamTypes);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppFileTransfer::XmppFileTransferManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppFileTransfer::XmppFileTransferManager& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppFileTransfer::XmppFileTransferHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppFileTransfer::XmppFileTransferHandler& v)
    {
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppFileTransfer::ServiceAvailabilityEvent& v)
    {
      w.StartObject();
      Write(w, "XEP0363_available", v.XEP0363_available);
      Write(w, "XEP0363_service", v.XEP0363_service);
      Write(w, "XEP0363_maxFileSize", v.XEP0363_maxFileSize);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppFileTransfer::ServiceAvailabilityEvent& v)
    {
      Read(json, "XEP0363_available", v.XEP0363_available);
      Read(json, "XEP0363_service", v.XEP0363_service);
      Read(json, "XEP0363_maxFileSize", v.XEP0363_maxFileSize);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppFileTransfer::NewFileTransferEvent& v)
    {
      w.StartObject();
      Write(w, "fileTransferState", v.fileTransferState);
      Write(w, "fileTransferType", v.fileTransferType);
      Write(w, "remoteAddress", v.remoteAddress);
      Write(w, "remoteDisplayName", v.remoteDisplayName);
      Write(w, "account", v.account);
      Write(w, "fileItems", v.fileItems);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppFileTransfer::NewFileTransferEvent& v)
    {
      Read(json, "fileTransferState", v.fileTransferState);
      Read(json, "fileTransferType", v.fileTransferType);
      Read(json, "remoteAddress", v.remoteAddress);
      Read(json, "remoteDisplayName", v.remoteDisplayName);
      Read(json, "account", v.account);
      Read(json, "fileItems", v.fileItems);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppFileTransfer::FileTransferEndedEvent& v)
    {
      w.StartObject();
      Write(w, "fileTransferState", v.fileTransferState);
      Write(w, "endReason", v.endReason);
      Write(w, "xmppResponseCode", v.xmppResponseCode);
      Write(w, "signallingEndEvent", v.signallingEndEvent);
      Write(w, "signallingEndReason", v.signallingEndReason);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppFileTransfer::FileTransferEndedEvent& v)
    {
      Read(json, "fileTransferState", v.fileTransferState);
      Read(json, "endReason", v.endReason);
      Read(json, "xmppResponseCode", v.xmppResponseCode);
      Read(json, "signallingEndEvent", v.signallingEndEvent);
      Read(json, "signallingEndReason", v.signallingEndReason);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppFileTransfer::FileTransferItemEndedEvent& v)
    {
      w.StartObject();
      Write(w, "fileTransferItem", v.fileTransferItem);
      Write(w, "endReason", v.endReason);
      Write(w, "streamTypeAttempted", v.streamTypeAttempted);
      Write(w, "remoteFileURI", v.remoteFileURI);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppFileTransfer::FileTransferItemEndedEvent& v)
    {
      Read(json, "fileTransferItem", v.fileTransferItem);
      Read(json, "endReason", v.endReason);
      Read(json, "streamTypeAttempted", v.streamTypeAttempted);
      Read(json, "remoteFileURI", v.remoteFileURI);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppFileTransfer::FileTransferItemProgressEvent& v)
    {
      w.StartObject();
      Write(w, "fileTransferItem", v.fileTransferItem);
      Write(w, "percent", v.percent);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppFileTransfer::FileTransferItemProgressEvent& v)
    {
      Read(json, "fileTransferItem", v.fileTransferItem);
      Read(json, "percent", v.percent);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppFileTransfer::ErrorEvent& v)
    {
      w.StartObject();
      Write(w, "errorText", v.errorText);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppFileTransfer::ErrorEvent& v)
    {
      Read(json, "errorText", v.errorText);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppFileTransfer::XmppFileTransferState& v)
    {
      w.StartObject();
      Write(w, "account", v.account);
      Write(w, "fileTransferState", v.fileTransferState);
      Write(w, "fileTransferType", v.fileTransferType);
      Write(w, "remoteAddress", v.remoteAddress);
      Write(w, "remoteDisplayName", v.remoteDisplayName);
      Write(w, "endReason", v.endReason);
      Write(w, "fileItems", v.fileItems);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppFileTransfer::XmppFileTransferState& v)
    {
      Read(json, "account", v.account);
      Read(json, "fileTransferState", v.fileTransferState);
      Read(json, "fileTransferType", v.fileTransferType);
      Read(json, "remoteAddress", v.remoteAddress);
      Read(json, "remoteDisplayName", v.remoteDisplayName);
      Read(json, "endReason", v.endReason);
      Read(json, "fileItems", v.fileItems);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::XmppFileTransfer::XmppFileTransferStateManager& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::XmppFileTransfer::XmppFileTransferStateManager& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // XmppFileTransfer_JSON_HELPER_H