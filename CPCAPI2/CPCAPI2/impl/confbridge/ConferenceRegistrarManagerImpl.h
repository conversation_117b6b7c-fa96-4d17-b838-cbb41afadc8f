#pragma once

#if !defined(CPCAPI2_CONFERENCE_REGISTRAR_MANAGER_IMPL_H)
#define CPCAPI2_CONFERENCE_REGISTRAR_MANAGER_IMPL_H

#include "cpcapi2defs.h"

#include "confbridge/ConferenceRegistrar.h"
#include "phone/PhoneInterface.h"

#include <webrtc/system_wrappers/interface/rw_lock_wrapper.h>

#include <rutil/DeadlineTimer.hxx>

#include <soci/soci.h>

// Simple-Http-Server
#include <server_https.hpp>

namespace CPCAPI2
{
namespace ConferenceBridge
{
class ConferenceRegistrarManagerInterface;

class ConferenceRegistrarManagerImpl : public resip::DeadlineTimerHandler,
   public std::enable_shared_from_this<ConferenceRegistrarManagerImpl>
{
public:
   ConferenceRegistrarManagerImpl(Phone* phone, ConferenceRegistrarManagerInterface* crmint);
   virtual ~ConferenceRegistrarManagerImpl();

   int start(const ConferenceRegistrarConfig& confRegistrarConfig);
   int shutdown();

   int addFollower(int nodeId, const cpc::string& ipAndPort);
   int registerConference(const cpc::string& joinUrl, int nodeId, bool overrideExisting, RegisterConferenceResultHandler* handler);
   int lookupConference(const cpc::string& joinUrl, int querySourceNodeId, LookupConferenceResultHandler* handler);
   int unregisterConference(const cpc::string& joinUrl);
   int bulkUpdate(const cpc::vector<ConferenceRegistrationInfo>& conferencesToAdd, const cpc::vector<cpc::string>& conferencesToRemove, RegisterConferenceResultHandler* handler);
   int listNodes(ListNodesHandler* handler);
   bool isPartOfCluster();

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

private:
   void addQueryContextHandler();
   template <typename T>
   void handleQueryContext(std::shared_ptr<typename SimpleWeb::Server<T>::Response> response,
      std::shared_ptr<typename SimpleWeb::Server<T>::Request> request);
   template <typename T>
   void handleQueryContextAsLeader(std::shared_ptr<typename SimpleWeb::Server<T>::Response> response,
      std::shared_ptr<typename SimpleWeb::Server<T>::Request> request);
   template <typename T>
   void handleQueryContextAsFollower(std::shared_ptr<typename SimpleWeb::Server<T>::Response> response,
      std::shared_ptr<typename SimpleWeb::Server<T>::Request> request);

   void addJoinClusterHandler();
   template <typename T>
   void handleJoinClusterRequest(std::shared_ptr<typename SimpleWeb::Server<T>::Response> response,
      std::shared_ptr<typename SimpleWeb::Server<T>::Request> request);
   int doAuth(const std::function<void(int, const resip::Data&, const cpc::string&)>& resultCb);
   int doJoinClusterHTTP(const cpc::string& joinClusterUrl);
   int doLeaderHello(int nodeId, const cpc::string& ipAndPort, const std::map<cpc::string, cpc::string>& recentUrlMappings);
   void addLeaderHelloHandler();
   template <typename T>
   void handleLeaderHelloRequest(std::shared_ptr<typename SimpleWeb::Server<T>::Response> response,
      std::shared_ptr<typename SimpleWeb::Server<T>::Request> request);
   void addRegisterContextHandler();
   template <typename T>
   void handleRegisterContextRequest(std::shared_ptr<typename SimpleWeb::Server<T>::Response> response,
      std::shared_ptr<typename SimpleWeb::Server<T>::Request> request);
   int doRegisterConferenceHTTP(const cpc::string& joinUrl, int nodeId, bool overrideExisting, RegisterConferenceResultHandler* handler);
   int doLookupConferenceHTTP(const cpc::string& joinUrl, const std::function<void(int, const std::string)>& resultCb, LookupConferenceResultHandler* handler);
   struct LookupCacheItem
   {
      std::string path;
      std::string wsUrl;
      std::chrono::system_clock::time_point entryTime;
   };
   bool lookupInCache(const std::string& url, LookupCacheItem& result);
   bool localIsLeader();
   int lookupConferenceAsLeader(const cpc::string& joinUrl, int querySourceNodeId, LookupConferenceResultHandler* handler);
   int lookupConferenceAsFollower(const cpc::string& joinUrl, int querySourceNodeId, LookupConferenceResultHandler* handler);

   int initializeDb(const cpc::string& dbfile);
   int upsertUrlMappingDb(const cpc::string& joinUrl, const cpc::string& wsBaseUrl);
   int queryJoinUrl(const cpc::string& joinUrl, bool& urlExists, cpc::string& wsBaseUrl);
   int appendRandomUrlMappingsFromDb(std::map<cpc::string, cpc::string>& urlMappings);
   int flushUrlMapDb();

private:
   PhoneInterface* mPhone;
   ConferenceRegistrarManagerInterface* mInterface;
   static std::atomic_int sNextNodeId;
   ConferenceRegistrarConfig mLocalConfig;
   resip::DeadlineTimer<resip::MultiReactor> mCheckinTimer;

   resip::MultiReactor mWebRequestThread;
   resip::MultiReactor mLookupWebRequestThread;

   struct FollowerInfo
   {
      cpc::string ipAndPort;
      cpc::string wsUrlBase;
      bool isOK = false;
   };

   struct LeaderConnectionState
   {
      bool mLocalIsLeader = false;
      std::map<int, FollowerInfo> mFollowerMap;
      std::map<cpc::string, cpc::string> mRecentUrlMappings;
   };
   LeaderConnectionState mLeaderState;
   int mNodeId = -1;
   resip::Data mAuthToken;

   std::shared_ptr<webrtc::RWLockWrapper> mStateLock;
   resip::Data mQueryContextUrl;
   resip::Data mJoinClusterUrl;
   std::chrono::system_clock::time_point mLastHelloTime;

   int mLeaderNodeId = -1;
   std::atomic_bool mInService;
   std::atomic_bool mIsPartOfCluster;
   std::vector<LookupCacheItem> mFollowerLookupCache;
   std::shared_ptr<webrtc::RWLockWrapper> mFollowerCacheLock;

   soci::connection_pool mDbPool;
   // Writes lock the sqlite3 db. We can issue parallel reads, but not writes
   std::unique_ptr<webrtc::RWLockWrapper> mDbLock; // Replace with std::shared_mutex once we can use C++17
};
}
}
#endif // CPCAPI2_CONFERENCE_REGISTRAR_MANAGER_IMPL_H
