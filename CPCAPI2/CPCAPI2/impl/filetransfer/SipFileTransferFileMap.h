#pragma once
#ifndef __CPCAPI2_SIP_FILE_TRANSFER_FILE_MAP_H__
#define __CPCAPI2_SIP_FILE_TRANSFER_FILE_MAP_H__

#ifdef _WIN32
#include <WinSock2.h>
#include <Windows.h>
#include <io.h>
#else
#endif

#include <cpcstl/string.h>
#include <stdint.h>

namespace CPCAPI2
{
   namespace SipFileTransfer
   {
      class FileMap
      {
      public:

         // opens a new filemap for reading
         FileMap( const cpc::string& fileName, const cpc::string& filePath );

         // creates a new filemap for writing
         FileMap( const cpc::string& fileName, const cpc::string& filePath, const uint64_t& fileSizeBytes );

         // closes file and releases map
         ~FileMap();

         // returns true if the mapping is valid
         bool IsValid() const;

         // returns access to data within map
         uint8_t& operator[]( int index );

         // returns pointer to head of data
         uint8_t *GetData() { return m_Data; };

         // Some file utilities
         static bool FileExists( const cpc::string& fileName, const cpc::string& filePath );
         static bool IsRegularFile( const cpc::string& fileName, const cpc::string& filePath );
         static uint64_t GetFileSize( const cpc::string& fileName, const cpc::string& filePath );

      private:

         bool m_IsValid;
         uint8_t *m_Data;

#ifdef _WIN32
         HANDLE m_hFile;
         HANDLE m_hMapping;
#else
         int m_hFD; // file descriptor
         size_t m_FileSize;
#endif
      };
   }
}

#endif // __CPCAPI2_SIP_FILE_TRANSFER_FILE_MAP_H__
