#pragma once
#ifndef __CPCAPI2_BIEVENTS_MANAGER_IMPL_H__
#define __CPCAPI2_BIEVENTS_MANAGER_IMPL_H__

#include <atomic>
#include <map>
#include <thread>
#include <list>
#include <vector>

#include <stdint.h>

#include <boost/asio.hpp>

#include "../util/HttpClient.h"

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>

#include <util/strettorpc/RPCRequest.h>

#include "cpcapi2defs.h"
#include "../phone/PhoneModule.h"
#include "../util/AutoTestProcessor.h"
#include "../util/DumFpCommand.h"

#include <bievents/BIEventsManager.h>
#include "BIEventsInternalTypes.h"
#include "BIEventsSyncHandler.h"

namespace CPCAPI2
{
   class Phone;
   class RPCAuthResponse;

   namespace BIEvents
   {
      class BIEventCache;

      typedef enum BIEventsTimer_e
      {
         BIEventsTimer_OnDelta,
         BIEventsTimer_OnMax
      } BIEventsTimer_e;

      class BIEventsManagerImpl : public std::enable_shared_from_this< BIEventsManagerImpl >
      {
      public:
         BIEventsManagerImpl( BIEventsHandle hEvents,
            boost::asio::io_service& ioService,
            resip::Fifo< resip::ReadCallbackBase >& callbackFifo );
         virtual ~BIEventsManagerImpl( void );

         // BIEventManager Implementation
         int configureSettings( const struct BIEventsSettings& settings );
         int setHandler( BIEventsHandler * notificationHandler );
         int enable( void );
         int disable( void );
         void setCallbackHook(void (*cbHook)(void*), void* context);
         int postEvent( const BIEventHeader& eventHeader, const BIEventBody& eventBody, const RPCIdentifier& eventID );
         int purgeCache( void );

         // Should be called prior to destruction (can be called in another thread)
         void abort( void );

         // ASIO timer callbacks for servicing the queue
         void restartTimers( void );
         void onDeltaTimer( const boost::system::error_code& e );
         void onMaxTimer( const boost::system::error_code& e );

         // Funky macro for firing events
         template<typename TFn, typename TEvt> void fireEvent(
            const char* funcName,
            TFn func,
            const TEvt& args)
         {
            if( m_Handler == NULL )
               return;

            resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, m_Handler, m_hEvents, args);
            if (m_Handler != (void*)0xDEADBEEF && dynamic_cast<CPCAPI2::BIEvents::BIEventsSyncHandler*>( m_Handler ) != NULL)
            {
               // Invoke the callback synchronously in these cases
               ( *cb )();
               delete cb;
            }
            else
            {
               // Object "cb" should be deleted by whomever is processing this FIFO
               m_CallbackFifo.add( cb );
               if (m_CbHook) { m_CbHook(); }
            }
         }

      private: // methods

         void uploadEvents( void ); // upload a batch of events to server

         // Send ~auth~ message exchange for initial session auth, or for
         // session refreshes.
         bool performAuthorization( void );

      private: // data

         boost::asio::io_service& m_IOService; // owned by Interface
         resip::Fifo< resip::ReadCallbackBase >& m_CallbackFifo; // owned by Interface

         const BIEventsHandle m_hEvents;

         // Pointer to the settings for this account (owned)
         BIEventsSettings m_Settings;

         // Application handler for account related activity
         BIEventsHandler *m_Handler;
         std::function<void(void)> m_CbHook;

         BIEventCache *m_EventCache;

         // Authentication context retrieved from server (if available)
         RPCAuthResponse *m_AuthContext;

         // HTTP Client used to communicate to the BI server
         HTTPClient* m_HttpClient;

         // Used to toggle sending of information
         bool m_IsEnabled;

         // The (cached) value of the "druid" (a unique ID) which is provided
         // to the server and cached in between sessions. May be empty.
         std::string m_Druid;

         // Asio timers used to service the event cache
         boost::asio::deadline_timer m_DeltaTimer;
         boost::asio::deadline_timer *m_MaxTimer;
      };

      /**
       * This class is required because asio insists on calling the timers when
       * cancel() is called, which results in a race condition between the
       * point in time when the object is deleted and the timer is cancelled.
       *
       * As a result I am creating a timer functor which contains weak
       * references to the parent.
       */
      class BIEventsManagerTimer
      {
      public:
         BIEventsManagerTimer( std::shared_ptr< BIEventsManagerImpl > pParent, BIEventsTimer_e timerType )
         {
            m_Parent = pParent;
            m_TimerType = timerType;
         }

         void operator()( const boost::system::error_code& e )
         {
            std::shared_ptr< BIEventsManagerImpl > pParent( m_Parent.lock() );
            if( pParent.get() )
            {
               switch( m_TimerType )
               {
               case BIEventsTimer_OnDelta:
                  pParent->onDeltaTimer( e );
                  break;
               case BIEventsTimer_OnMax:
                  pParent->onMaxTimer( e );
                  break;
               default:
                  assert( 0 && "Unknown timer type" );
                  break;
               }
            }

            delete this; // cleanup after calling parent
         }

      private:
         BIEventsTimer_e m_TimerType;
         std::weak_ptr< BIEventsManagerImpl > m_Parent;
      };
   }
}
#endif // __CPCAPI2_BIEVENTS_MANAGER_IMPL_H__
