#include "../datatypes/NetworkChangeManagerNetworkTransport.h"

namespace jsonrpc {
namespace CPCAPI2 {
namespace NetworkChangeManager {
NetworkTransport NetworkChangeManager::networkTransport(int64_t handle)
{
    NetworkChangeManagerNetworkTransport params = new NetworkChangeManagerNetworkTransport(handle);
    nlohmann::json j = this.transport.request("networkTransport", params.marshal());
    return j.get<NetworkTransport>();
};
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace NetworkChangeManager