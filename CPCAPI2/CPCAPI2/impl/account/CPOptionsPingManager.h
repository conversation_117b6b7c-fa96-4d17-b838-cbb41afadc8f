#pragma once

#ifndef __CPCAPI2_CP_OPTIONS_PING_MANGER_H__
#define __CPCAPI2_CP_OPTIONS_PING_MANGER_H__

#include "cpcapi2defs.h"

#include "SipAccountAwareFeature.h"
#include "SipAccountHandlerInternal.h"
#include "SipAccountImpl.h"

#include <resip/dum/OutOfDialogHandler.hxx>

#include <map>
#include <unordered_set>

namespace CPCAPI2
{
namespace SipAccount
{
class CPOptionsPingManager : public CPCAPI2::SipAccount::SipAccountAwareFeature,
                             public CPCAPI2::EventSyncHandler<CPCAPI2::SipAccount::SipAccountHandlerInternal>,
                             public resip::OutOfDialogHandler,
                             public resip::DeadlineTimerHandler
{
public:
  CPOptionsPingManager(CPCAPI2::Phone* cpcPhone, CPCAPI2::SipAccount::SipAccountImpl* account);
  virtual ~CPOptionsPingManager();

  // resip::DeadlineTimerHandler
  virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

  // SipAccountHandlerInternal
  virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args) OVERRIDE;
  virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args) OVERRIDE { return kSuccess; }
  virtual int onAccountConfigured(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountConfiguredEvent& args) OVERRIDE;
  virtual int onAccountEnabled(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountEnabledEvent& args) OVERRIDE { return kSuccess; }

  // Client Handlers
  virtual bool onSuccess(resip::ClientOutOfDialogReqHandle h, const resip::SipMessage& successResponse) OVERRIDE;
  virtual bool onFailure(resip::ClientOutOfDialogReqHandle h, const resip::SipMessage& errorResponse) OVERRIDE;

  // Server Handlers
  virtual bool onReceivedRequest(resip::ServerOutOfDialogReqHandle h, const resip::SipMessage& request) OVERRIDE;

  // SipAccountAwareFeature
  virtual int adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile) OVERRIDE;
  virtual int addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig) OVERRIDE;
  virtual int onDumBeingDestroyed() OVERRIDE;
  virtual void release() OVERRIDE;

private:
  bool sendPing();

  resip::MultiReactor& mReactor;
  CPCAPI2::SipAccount::SipAccountImpl* mAccount;
  CPCAPI2::SipAccount::SipAccountHandle mAccountHandle;
  resip::SharedPtr<resip::DialogUsageManager> mDum;
  resip::DeadlineTimer<resip::MultiReactor> mTimer;

  bool mPingEnabled;
  bool mRegistered;
  unsigned int mInterval;
  resip::Tuple mTarget;
  std::string mInFlightCallID; // The Call ID of the OPTIONS ping we have sent. "" if we aren't waiting for a response.

};

}
}

#endif // __CPCAPI2_CP_OPTIONS_PING_MANGER_H__
