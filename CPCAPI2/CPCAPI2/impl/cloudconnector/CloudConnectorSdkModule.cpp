#include "brand_branded.h"

#include "interface/experimental/cloudconnector/CloudConnector.h"

#if (CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE == 1)
#include "CloudConnectorInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace CloudConnector
{
   CloudConnectorManager* CloudConnectorManager::getInterface(Phone* cpcPhone)
   {
#if (CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<CloudConnectorInterface>(phone, "CloudConnectorManager");
#else
      return NULL;
#endif
   }

}
}
