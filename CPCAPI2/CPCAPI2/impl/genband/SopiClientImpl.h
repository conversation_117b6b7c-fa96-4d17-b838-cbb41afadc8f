#pragma once

#if !defined(CPCAPI2_SOPI_CLIENT_IMPL_H)
#define CPCAPI2_SOPI_CLIENT_IMPL_H

#include "cpcapi2defs.h"
#include "GenbandSopiManagerInterface.h"

// Forward declaration from sopi lib
class AddressBookUserServiceProxy;
class GABUserServiceProxy;
class PresenceAuthorizationUserServiceProxy;
struct soap;

namespace CPCAPI2
{
namespace GenbandSopi
{
	
  enum SopiServiceProxyType
  {
    ESopiServiceProxyPab,
    ESopiServiceProxyGrp,
    ESopiServiceProxyGab,
    ESopiServiceProxyPAU
  };
	
class SopiClientImpl
{
public:
   SopiClientImpl(GenbandSopiClientHandle handle, const GenbandSopiClientSettings& settings, GenbandSopiManagerInterface& itf);

   ~SopiClientImpl();

   void getAddressbook();
   void addAddressBookEntry(const AddressBookEntry& entry);
   void updateAddressBookEntry(const cpc::string& entryId, const AddressBookEntry& updatedEntry);
   void deleteAddressBookEntry(const cpc::string& name);
   void getAddressBookGroups();
   void addAddressBookGroup(const cpc::string& group);
   void updateAddressBookGroup(const cpc::string& oldGroup, const cpc::string& newGroup);
   void deleteAddressBookGroup(const cpc::string& group);
   void searchGlobalDirectory(const cpc::string& query, unsigned int maxResults);

   void addAuthorizedUser(const cpc::string& userName);
   void removeAuthorizedUser(const cpc::string& userName);
   void getAuthorizedUsers();

   void addBannedUser(const cpc::string& userName);
   void removeBannedUser(const cpc::string& userName);
   void getBannedUsers();
   
   void addPoliteBlockedUser(const cpc::string& userName);
   void removePoliteBlockedUser(const cpc::string& userName);
   void getPoliteBlockedUsers();

private:
   void initServiceProxy(SopiServiceProxyType proxy_type);
   void setUserNameAndPassword(SopiServiceProxyType proxy_type);
   bool sendErrorEvent(SopiServiceProxyType proxy_type, const cpc::string& requestName);
   
   void sendSearchGlobalAddressBookResult(cpc::string searchQuery,cpc::vector<AddressBookEntry> results);
   
private:
   GenbandSopiClientHandle mClientHandle;
   GenbandSopiManagerInterface& mInterface;
   cpc::string mUsername;
   cpc::string mPassword;
   cpc::string mPabSoapEndpoint;
   cpc::string mPabGroupSoapEndpoint;
   cpc::string mGabSoapEndpoint;
   cpc::string mPAUSoapEndpoint;
   cpc::string mUserAgent;
   
   
   typedef std::map<SopiServiceProxyType, soap *> SoapMap;
   SoapMap mSoapMap;

   AddressBookUserServiceProxy* mPabSrvProxy;
   AddressBookUserServiceProxy* mGrpSrvProxy;
   GABUserServiceProxy* mGabSrvProxy;
   PresenceAuthorizationUserServiceProxy  *mPAUSrvProxy;
   
   bool mIgnoreCertificatValidation;
};
}
}

#endif
