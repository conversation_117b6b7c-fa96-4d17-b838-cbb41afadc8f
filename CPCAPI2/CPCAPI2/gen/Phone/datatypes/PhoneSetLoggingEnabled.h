#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_PHONE_PHONESETLOGGINGENABLED_H
#define JSONRPC_CPCAPI2_PHONE_PHONESETLOGGINGENABLED_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  class PhoneSetLoggingEnabled
  {
  public:
    int32_t handle;
    bool enabled;

    PhoneSetLoggingEnabled() = default;

    PhoneSetLoggingEnabled(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const PhoneSetLoggingEnabled& o)
    {
      j = nlohmann::json::object();
       j["handle"] = o.handle;
     j["enabled"] = o.enabled;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, PhoneSetLoggingEnabled& o)
    {
     o.handle = j.at("handle");
     o.enabled = j.at("enabled");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_PHONESETLOGGINGENABLED_H
