#include "brand_branded.h"

#include "interface/experimental/chat/SipChatManager.h"

#if (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)
#include "SipChatManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace SipChat
{
   SipChatManager* SipChatManager::getInterface(CPCAPI2::Phone* cpcPhone)
   {
#if (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<SipChatManagerInterface>(phone, "SipChatManagerInterface");
#else
      return NULL;
#endif
   }

}
}
