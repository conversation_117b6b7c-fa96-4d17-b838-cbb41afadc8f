#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_CHAT_MODULE==1 || CPCAPI2_BRAND_IM_MODULE==1 || CPCAPI2_BRAND_XMPP_CHAT_MODULE==1 || CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
#include "cpcapi2utils.h"
#include "ImdnDocument.h"
#include "CpmHelper.h"

#include <libxml/parser.h>

#include <assert.h>

namespace CPCAPI2
{
namespace CPM
{

ImdnDocument::ImdnDocument() 
   : deliveryNotification(false),
     deliveryNotificationStatus(MessageDeliveryStatus_Error),
     displayNotification(false), 
     displayNotificationStatus(MessageDisplayStatus_Error)
{
}

ImdnDocument::~ImdnDocument()
{
   // do not call here; can cause crashes. see LibxmlSharedUsage.h
   //::xmlCleanupParser();
}

void ImdnDocument::setDeliveryNotificationStatus(MessageDeliveryStatus deliveryStatus) 
{ 
   this->deliveryNotification = true; 
   this->deliveryNotificationStatus = deliveryStatus;
}

void ImdnDocument::setDisplayNotificationStatus(MessageDisplayStatus displayStatus) 
{ 
   this->displayNotification = true; 
   this->displayNotificationStatus = displayStatus; 
}

cpc::string ImdnDocument::toString() const
{
   // Populate the XML tree with the basic information
   xmlDocPtr doc = xmlNewDoc(BAD_CAST "1.0");
   xmlNodePtr rootNode = xmlNewNode(NULL, BAD_CAST "imdn");
   xmlNewProp(rootNode, BAD_CAST "xmlns", BAD_CAST "urn:ietf:params:xml:ns:imdn");
   xmlDocSetRootElement(doc, rootNode);
   xmlNewChild(rootNode, NULL, BAD_CAST "message-id", BAD_CAST messageId.c_str());
   xmlNewChild(rootNode, NULL, BAD_CAST "datetime", BAD_CAST dateTime.c_str());
   xmlNewChild(rootNode, NULL, BAD_CAST "recipient-uri", BAD_CAST recipientUri.c_str());
   xmlNewChild(rootNode, NULL, BAD_CAST "original-recipient-uri", BAD_CAST originalRecipientUri.c_str());

   // Populate the XML tree with the notification information
   if (deliveryNotification)
   {
      xmlNodePtr deliveryNotificationNode = xmlNewChild(rootNode, NULL, BAD_CAST "delivery-notification", NULL);
      xmlNodePtr deliveryNotificationStatusNode = xmlNewChild(deliveryNotificationNode, NULL, BAD_CAST "status", NULL);
      const char *deliveryNotificationNodeStatusName = NULL;
      switch(deliveryNotificationStatus)
      {
         case MessageDeliveryStatus_Delivered:
            deliveryNotificationNodeStatusName = "delivered";
            break;
         case MessageDeliveryStatus_Failed:
            deliveryNotificationNodeStatusName = "failed";
            break;
         case MessageDeliveryStatus_Forbidden:
            deliveryNotificationNodeStatusName = "forbidden";
            break;
         case MessageDeliveryStatus_Error:
            deliveryNotificationNodeStatusName = "error";
            break;
         default:
            // Unsupported delivery status
            assert(false);
      }
      xmlNewChild(deliveryNotificationStatusNode, NULL, BAD_CAST deliveryNotificationNodeStatusName, NULL);
   } 
   else if (displayNotification)
   {
      // Display notification
      xmlNodePtr displayNotificationNode = xmlNewChild(rootNode, NULL, BAD_CAST "display-notification", NULL);
      xmlNodePtr displayNotificationStatusNode = xmlNewChild(displayNotificationNode, NULL, BAD_CAST "status", NULL);
      const char* displayNotificationStatusName = NULL;
      switch(displayNotificationStatus)
      {
      case MessageDisplayStatus_Displayed:
         displayNotificationStatusName = "displayed";
         break;
      case MessageDisplayStatus_Forbidden:
         displayNotificationStatusName = "forbidden";
         break;
      case MessageDisplayStatus_Error:
         displayNotificationStatusName = "error";
         break;
      default:
         // Unsupported display status
         assert(false);
      }
      xmlNewChild(displayNotificationStatusNode, NULL, BAD_CAST displayNotificationStatusName, NULL);
   }
   else
   {
      // Unsupported notification type
      assert(0);
   }

   // Convert the XML tree to a string
   xmlChar* out = NULL;
   int size;
   xmlDocDumpFormatMemoryEnc(doc, &out, &size, "UTF-8", 1);
   cpc::string xml((const char *) out);
   xmlFree(out);

   // Destroy the XML tree
   xmlFreeDoc(doc);

   return xml;
}

ImdnDocument ImdnDocument::parse(const cpc::string& content)
{
   ImdnDocument imdnDocument;

   // Parse the basic information and build a XML tree
   xmlDocPtr doc = ::xmlReadMemory(content.c_str(), content.size(), "noname.xml", NULL, 0);
   xmlNodePtr rootNode = xmlDocGetRootElement(doc);
   imdnDocument.messageId = CpmHelper::getChildNodeText(rootNode, "message-id");
   imdnDocument.dateTime = CpmHelper::getChildNodeText(rootNode, "datetime");
   imdnDocument.recipientUri = CpmHelper::getChildNodeText(rootNode, "recipient-uri");
   imdnDocument.originalRecipientUri = CpmHelper::getChildNodeText(rootNode, "original-recipient-uri");

   // Parse the notification information and add it to the tree
   xmlNodePtr deliveryNotificationNode = CpmHelper::getChildNode(rootNode, "delivery-notification");
   if (deliveryNotificationNode)
   {
      // Delivery notification

      imdnDocument.deliveryNotification = true;
      xmlNodePtr deliveryNotificationNodeStatusNode = CpmHelper::getChildNode(deliveryNotificationNode, "status");
      xmlNodePtr deliveryNotificationNodeStatusTypeNode = CpmHelper::getChildNode(deliveryNotificationNodeStatusNode, NULL);
      const char* deliveryNotificationNodeStatusName = (const char *) deliveryNotificationNodeStatusTypeNode->name;
      if (!strcmp(deliveryNotificationNodeStatusName, "delivered"))
      {
         imdnDocument.deliveryNotificationStatus = MessageDeliveryStatus_Delivered;
      }
      else if (!strcmp(deliveryNotificationNodeStatusName, "failed"))
      {
         imdnDocument.deliveryNotificationStatus = MessageDeliveryStatus_Failed;
      }
      else if (!strcmp(deliveryNotificationNodeStatusName, "forbidden"))
      {
         imdnDocument.deliveryNotificationStatus = MessageDeliveryStatus_Forbidden;
      }
      else if (!strcmp(deliveryNotificationNodeStatusName, "error"))
      {
         imdnDocument.deliveryNotificationStatus = MessageDeliveryStatus_Error;
      }
      else
      {
         // Unsupported delivery status
         assert(false);
      }
   }
   else
   {
      xmlNodePtr displayNotificationNode = CpmHelper::getChildNode(rootNode, "display-notification");
      if (displayNotificationNode)
      {  
         // Display notification

         imdnDocument.displayNotification = true;
         xmlNodePtr displayNotificationNodeStatusNode = CpmHelper::getChildNode(displayNotificationNode, "status");
         xmlNodePtr displayNotificationNodeStatusTypeNode = CpmHelper::getChildNode(displayNotificationNodeStatusNode, NULL);
         const char* displayNotificationNodeStatusName = (const char *) displayNotificationNodeStatusTypeNode->name;
         if (!strcmp(displayNotificationNodeStatusName, "displayed"))
         {
            imdnDocument.displayNotificationStatus = MessageDisplayStatus_Displayed;
         }
         else if (!strcmp(displayNotificationNodeStatusName, "forbidden"))
         {
            imdnDocument.displayNotificationStatus = MessageDisplayStatus_Forbidden;
         }
         else if (!strcmp(displayNotificationNodeStatusName, "error"))
         {
            imdnDocument.displayNotificationStatus = MessageDisplayStatus_Error;
         }
         else
         {
            // Unsupported display status
            assert(false);
         }
      }
      else
      {
         // Unsupported notification
         assert(false);
      }
   }

   // Destroy the XML tree
   xmlFree(doc);

   return imdnDocument;
}

}
}
#endif
