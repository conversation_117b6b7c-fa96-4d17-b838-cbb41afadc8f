#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_MEDIAENCRYPTIONMODE_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_MEDIAENCRYPTIONMODE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  enum class MediaEncryptionMode
  {
    Unencrypted,
    SrtpSdesEncrypted,
    SrtpDtlsEncrypted
  };

  inline std::string to_string(MediaEncryptionMode value)
  {
    switch (value)
    {
      case MediaEncryptionMode::Unencrypted:
        return "Unencrypted";
      case MediaEncryptionMode::SrtpSdesEncrypted:
        return "SRTP_SDES_Encrypted";
      case MediaEncryptionMode::SrtpDtlsEncrypted:
        return "SRTP_DTLS_Encrypted";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, MediaEncryptionMode value)
  {
    switch (value)
    {
      case MediaEncryptionMode::Unencrypted:
        os << "Unencrypted";
        break;
      case MediaEncryptionMode::SrtpSdesEncrypted:
        os << "SRTP_SDES_Encrypted";
        break;
      case MediaEncryptionMode::SrtpDtlsEncrypted:
        os << "SRTP_DTLS_Encrypted";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const MediaEncryptionMode& e)
  {
    switch (e)
    {
      case MediaEncryptionMode::Unencrypted:
        j = "Unencrypted";
        break;
      case MediaEncryptionMode::SrtpSdesEncrypted:
        j = "SRTP_SDES_Encrypted";
        break;
      case MediaEncryptionMode::SrtpDtlsEncrypted:
        j = "SRTP_DTLS_Encrypted";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, MediaEncryptionMode& e)
  {
      if (0 == j.get<std::string>().compare("Unencrypted"))
    {
      e = MediaEncryptionMode::Unencrypted;
    }
    else if (0 == j.get<std::string>().compare("SRTP_SDES_Encrypted"))
    {
      e = MediaEncryptionMode::SrtpSdesEncrypted;
    }
    else if (0 == j.get<std::string>().compare("SRTP_DTLS_Encrypted"))
    {
      e = MediaEncryptionMode::SrtpDtlsEncrypted;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum MediaEncryptionMode"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_MEDIAENCRYPTIONMODE_H
