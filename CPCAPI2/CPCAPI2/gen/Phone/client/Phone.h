#include "../datatypes/LicenseInfo.h"
#include "../datatypes/SslCipherOptions.h"
#include "../datatypes/LogLevel.h"
#include "../datatypes/PhoneEvent.h"

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
class Phone
{
public:
    Phone(ITransport& transport)
    {
        this.transport = transport;
    }

    int64_t create();

	void release(int64_t handle);

	cpc::string getVersion();

	void initialize(int64_t handle, LicenseInfo licenseInfo, bool useNetworkChangeManager, std::optional<SslCipherOptions> ciphers);

	void setLoggingEnabled(int64_t handle, bool enabled);

	void setFileLoggingEnabled(int64_t handle, bool enabled, std::optional<cpc::string> id);

	void setLogDirectory(int64_t handle, cpc::string directory);

	void setLogLevel(int64_t handle, LogLevel level);

	cpc::string getInstanceId(int64_t handle);

	int64_t createEventQueue(int64_t handle, std::optional<cpc::vector<cpc::string>> events);

	void destroyEventQueue(int64_t handle, int64_t eventQueueHandle);

	PhoneEvent getEvent(int64_t handle, int64_t eventQueueHandle, int64_t timeout);

private:
    ITransport* transport;
}; // class Phone
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
