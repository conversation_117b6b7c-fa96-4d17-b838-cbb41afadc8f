#pragma once

#if !defined(CPCAPI2_JSON_API_CLIENT_WEBSOCKET_H)
#define CPCAPI2_JSON_API_CLIENT_WEBSOCKET_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiClientModule.h"
#include "jsonapi/JsonApiTransport.h"
#include "websocket/WebSocketStateMachine.h"
#include "phone/NetworkChangeManagerImpl.h"

#define USE_WSS_JSON_CLIENT 0


#if (USE_WSS_JSON_CLIENT == 1)
#include <websocketpp/config/asio.hpp>
#else
#include <websocketpp/config/asio_no_tls.hpp>
#endif

#include <iostream>
#include <set>
#include <thread>
#include <memory>
#include <mutex>
#include <condition_variable>
#include <future>

#include <rutil/RecursiveMutex.hxx>

#include <websocketpp/client.hpp>

namespace CPCAPI2
{

namespace JsonApi
{

class JsonApiClientWebSocketHandler;

struct JsonApiClientWebSocketSettings
{
   std::string serverUri;
   bool ignoreCertVerification;

   JsonApiClientWebSocketSettings()
   {
      ignoreCertVerification = false;
   }
};

class JsonApiClientWebSocket : public CPCAPI2::JsonApi::JsonApiTransport,
                               public CPCAPI2::WebSocket::WebSocketStateMachineListener,
                               public CPCAPI2::EventSyncHandler<CPCAPI2::NetworkChangeHandler>,
                               public std::enable_shared_from_this<JsonApiClientWebSocket>
{

public:

   JsonApiClientWebSocket(CPCAPI2::Phone* phone);
   virtual ~JsonApiClientWebSocket();

   virtual int setHandler(JsonApiClientWebSocketHandler*);

   // JsonApiTransport
   virtual int send(const CPCAPI2::Json::JsonDataPointer& jsonData, bool suppressLog = false) OVERRIDE;
   virtual int configureSettings(const JsonApiClientWebSocketSettings& settings);
   virtual int connect();
   virtual int shutdown();
   virtual int dropIncomingJsonMessages(bool enable);

   // WebSocketStateMachineListener
   virtual void onStateChange(
      const CPCAPI2::AbstractStatePtr oldState,
      const CPCAPI2::AbstractStatePtr newState,
      const CPCAPI2::AbstractStateReasonCode reason ) OVERRIDE;
   virtual void onLogin( void ) OVERRIDE {}
   virtual void onLoginResponse( const std::string& inDocument ) OVERRIDE {}
   virtual void onLogout( void ) OVERRIDE {}
   virtual void onMessage( const std::string& inDocument ) OVERRIDE;
   virtual void onPing( websocketpp::connection_hdl hWebSock ) OVERRIDE;
   virtual void onClose() OVERRIDE;
   virtual bool onReconnect( uint16_t code ) OVERRIDE;

   // NetworkChangeHandler
   virtual int onNetworkChange(const NetworkChangeEvent& args) OVERRIDE;

   static void phoneReleased(std::weak_ptr<JsonApiClientWebSocket> weakSelf);

private:

   void sendImpl(const CPCAPI2::Json::JsonDataPointer& jsonData);
   int setHandlerImpl(JsonApiClientWebSocketHandler*);
   void onStateChangeImpl(const CPCAPI2::AbstractStatePtr oldState, const CPCAPI2::AbstractStatePtr newState, const CPCAPI2::AbstractStateReasonCode reason );
   int configureSettingsImpl(const JsonApiClientWebSocketSettings& settings);
   int connectImpl();
   int onNetworkChangeImpl(const NetworkChangeEvent& args);

   void shutdownImpl();
   void dropIncomingJsonMessagesImpl(bool enable);

private:

   CPCAPI2::Phone* mPhone;

   boost::asio::io_service mIoService;
   std::unique_ptr<std::thread> mThread;
   std::unique_ptr<boost::asio::io_service::work> mWork;

   std::unique_ptr<WebSocket::WebSocketStateMachine> mWsStateMachine;

   JsonApiClientWebSocketHandler* mHandler;
   std::mutex mHandlerMtx;

   JsonApiClientWebSocketSettings mSettings;
   WebSocket::WebSocketSettings mWebSocketSettings;
   std::recursive_timed_mutex mReleaseMutex;
   bool mDropMessages; // For testing only

   std::unique_ptr<std::promise<int>> mWebsocketClosed;
};

}

}

#endif // CPCAPI2_JSON_API_CLIENT_WEBSOCKET_H
