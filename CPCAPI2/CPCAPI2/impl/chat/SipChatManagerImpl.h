#pragma once

#if !defined(__CPCAPI2_SIP_CHAT_MANAGER_IMPL_H__)
#define __CPCAPI2_SIP_CHAT_MANAGER_IMPL_H__

#include "cpcapi2defs.h"
#include "SipChatInfo.h"
#include "SipChatHelper.h"
#include "SipSimpleMessageProcessor.h"
#include "chat/SipChatManagerInterface.h"
#include "chat/SipChatHandler.h"
#include "../cpm/SipMsrpMessagingManager.h"
#include "../iscomposing/IsComposingManager.h"
#include "../account/SipAccountImpl.h"

namespace CPCAPI2
{
class Phone;

namespace SipChat
{

class SipChatManagerImpl : public SipMsrpMessagingManager, public IsComposingManager
{
public:
   SipChatManagerImpl(SipChatManagerInterface* iff, PhoneInterface* cpcPhone, CPCAPI2::SipAccount::SipAccountImpl& account);
   virtual ~SipChatManagerImpl();

   void releaseImpl() OVERRIDE;

   // Invoked by the interface
   SipChatInfo* getChatInfo(SipChatHandle handle) const { return dynamic_cast<SipChatInfo*>(getSessionInfo(handle)); }
   void addChatInfo(SipChatHandle handle, SipChatInfo* chatInfo) { addSessionInfo(handle, chatInfo); }
   void startChat(SipChatInfo* chatInfo);
   void endChat(SipChatInfo *chatInfo);
   void sendMessage(SipChatInfo *chatInfo, const SipChatMessageHandle& messageId, const tm& datetime, const resip::Mime& contentType, const resip::Data& messageContent, const cpc::vector<DispositionNotificationType>& dispositionNotifications);
   void acceptChat(SipChatInfo* chatInfo);
   void rejectChat(SipChatInfo* chatInfo, unsigned int reasonCode);
   void fireError(SipChatHandle chat, const cpc::string& msg);
   void notifyMessageDelivered(SipChatInfo* chatInfo, const SipChatMessageHandle& message, const SipChatMessageHandle& origMessage, MessageDeliveryStatus messageDeliveryStatus);
   void notifyMessageDisplayed(SipChatInfo* chatInfo, const SipChatMessageHandle& message, const SipChatMessageHandle& origMessage, MessageDisplayStatus messageDisplayStatus);
   SipChatMessageHandle getNextMessageId(SipChatInfo* chatInfo);
   void setIsComposingMessage(IsComposingInfo* info, const resip::Mime& contentType, const tm& datetime, int refreshInterval, int idleInterval);

   static const cpc::string CPM_FEATURE_TAG;

private:
   SipSimpleMessageProcessor sipSimpleMessageProcessor;
   SipChatManagerInterface* mInterface;

   // SipMsrpMessagingManager
   virtual int registerSdkDialogSetFactory(CPCAPI2::SipAccount::AppDialogSetFactory& factory) OVERRIDE;
   virtual void onNewSession(ClientInviteSessionHandle, InviteSession::OfferAnswerType oat, const SipMessage& msg) OVERRIDE;
   virtual void onTerminated(InviteSessionHandle, InviteSessionHandler::TerminatedReason reason, const SipMessage* related=0) OVERRIDE;
   virtual void onAnswer(InviteSessionHandle, const SipMessage& msg, const SdpContents&) OVERRIDE;
   virtual void onOffer(InviteSessionHandle, const SipMessage& msg, const SdpContents&) OVERRIDE;      
   virtual void onMsrpMessageSendComplete(msrp_message_t* msrpMessage, int chat, const cpc::string& message, MessageType messageType) OVERRIDE;
   virtual void onMsrpMessageRecvComplete(msrp_message_t* msrpMessage, int chat, uint8_t* data, uint64_t dataLength) OVERRIDE;
   virtual resip::Data buildSdp(msrp_uri_t* localUri, UInt32 localMediaPort, const resip::Mime& cpimContentType, bool isActive) OVERRIDE;

   // IsComposingManager
   virtual void sendIsComposingMessageNotification(IsComposingInfo* info, IsComposingMessageState state, const resip::Mime& contentType, int refreshInterval, const tm& lastActive) OVERRIDE;
   virtual void onIsComposingMessage(IsComposingInfo* info, IsComposingMessageState state, const resip::Mime& contentType, const tm& lastActive) OVERRIDE;

   // Implementation methods
   void addMessageInfo(SipChatHandle chat, const SipChatMessageHandle& message, MessageType messageType, bool outgoing, const cpc::string& datetimeString, const SipChatMessageHandle& origMessage, IsComposingMessageState isComposingState);
   void notifyMessageStatus(SipChatInfo* chatInfo, const SipChatMessageHandle& message, const SipChatMessageHandle& origMessage, MessageType messageType, int messageStatus);
   void fireNewChat(SipChatHandle chat, const NewChatEvent& event);
   void fireNewMessage(SipChatHandle chat, const NewMessageEvent& event);
   void fireSendMessageSuccess(SipChatHandle chat, const SendMessageSuccessEvent& event);
   void fireChatEnded(SipChatHandle chat, const ChatEndedEvent& event);
   void fireError(SipChatHandle chat, const ErrorEvent& event);
   void fireMessageDelivered(SipChatHandle chat, const MessageDeliveredEvent& event);
   void fireMessageDisplayed(SipChatHandle chat, const MessageDisplayedEvent& event);
   void fireIsComposingMessage(SipChatHandle chat, const IsComposingMessageEvent& event);
   void fireSendMessageFailure(SipChatHandle chat, const SendMessageFailureEvent& event);
   void fireNotifyMessageDeliveredSuccess(SipChatHandle chat, const NotifyMessageDeliveredSuccessEvent& event);
   void fireNotifyMessageDisplayedSuccess(SipChatHandle chat, const NotifyMessageDisplayedSuccessEvent& event);
   void fireSetIsComposingMessageSuccess(SipChatHandle chat, const SetIsComposingMessageSuccessEvent& event);
};

}
}

#endif // __CPCAPI2_SIP_CHAT_MANAGER_IMPL_H__
