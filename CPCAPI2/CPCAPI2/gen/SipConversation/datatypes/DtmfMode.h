#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_DTMFMODE_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_DTMFMODE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  enum class DtmfMode
  {
    Rfc2833,
    InBand,
    SipInfo,
    Rfc2833InBand,
    Rfc2833SipInfo,
    InBandSipInfo,
    Everything
  };

  inline std::string to_string(DtmfMode value)
  {
    switch (value)
    {
      case DtmfMode::Rfc2833:
        return "RFC2833";
      case DtmfMode::InBand:
        return "InBand";
      case DtmfMode::SipInfo:
        return "SIP_INFO";
      case DtmfMode::Rfc2833InBand:
        return "RFC2833_InBand";
      case DtmfMode::Rfc2833SipInfo:
        return "RFC2833_SIP_INFO";
      case DtmfMode::InBandSipInfo:
        return "InBand_SIP_INFO";
      case DtmfMode::Everything:
        return "Everything";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, DtmfMode value)
  {
    switch (value)
    {
      case DtmfMode::Rfc2833:
        os << "RFC2833";
        break;
      case DtmfMode::InBand:
        os << "InBand";
        break;
      case DtmfMode::SipInfo:
        os << "SIP_INFO";
        break;
      case DtmfMode::Rfc2833InBand:
        os << "RFC2833_InBand";
        break;
      case DtmfMode::Rfc2833SipInfo:
        os << "RFC2833_SIP_INFO";
        break;
      case DtmfMode::InBandSipInfo:
        os << "InBand_SIP_INFO";
        break;
      case DtmfMode::Everything:
        os << "Everything";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const DtmfMode& e)
  {
    switch (e)
    {
      case DtmfMode::Rfc2833:
        j = "RFC2833";
        break;
      case DtmfMode::InBand:
        j = "InBand";
        break;
      case DtmfMode::SipInfo:
        j = "SIP_INFO";
        break;
      case DtmfMode::Rfc2833InBand:
        j = "RFC2833_InBand";
        break;
      case DtmfMode::Rfc2833SipInfo:
        j = "RFC2833_SIP_INFO";
        break;
      case DtmfMode::InBandSipInfo:
        j = "InBand_SIP_INFO";
        break;
      case DtmfMode::Everything:
        j = "Everything";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, DtmfMode& e)
  {
      if (0 == j.get<std::string>().compare("RFC2833"))
    {
      e = DtmfMode::Rfc2833;
    }
    else if (0 == j.get<std::string>().compare("InBand"))
    {
      e = DtmfMode::InBand;
    }
    else if (0 == j.get<std::string>().compare("SIP_INFO"))
    {
      e = DtmfMode::SipInfo;
    }
    else if (0 == j.get<std::string>().compare("RFC2833_InBand"))
    {
      e = DtmfMode::Rfc2833InBand;
    }
    else if (0 == j.get<std::string>().compare("RFC2833_SIP_INFO"))
    {
      e = DtmfMode::Rfc2833SipInfo;
    }
    else if (0 == j.get<std::string>().compare("InBand_SIP_INFO"))
    {
      e = DtmfMode::InBandSipInfo;
    }
    else if (0 == j.get<std::string>().compare("Everything"))
    {
      e = DtmfMode::Everything;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum DtmfMode"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_DTMFMODE_H
