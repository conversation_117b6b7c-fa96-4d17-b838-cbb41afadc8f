#pragma once

#if !defined(CPCAPI2_SIP_ACCOUNT_JSON_PROXY_INTERFACE_H)
#define CPCAPI2_SIP_ACCOUNT_JSON_PROXY_INTERFACE_H

#include "account/SipAccountJsonProxy.h"
#include "jsonapi/JsonApiClientModule.h"
#include "phone/PhoneModule.h"
#include "util/AutoTestProcessor.h"

#include <rutil/Reactor.hxx>
#include <rutil/Fifo.hxx>

#include <mutex>
#include <condition_variable>

namespace CPCAPI2
{

class PhoneInterface;

namespace SipAccount
{
class SipAccountJsonProxyInterface : public CPCAPI2::SipAccount::SipAccountManagerJsonProxy,
                                     public CPCAPI2::JsonApi::JsonApiClientModule,
#ifdef CPCAPI2_AUTO_TEST
                                     public AutoTestProcessor,
#endif
                                     public CPCAPI2::PhoneModule
{
public:
   SipAccountJsonProxyInterface(CPCAPI2::Phone* phone);
   virtual ~SipAccountJsonProxyInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // SipAccountManagerJsonProxy
   virtual int setStateHandler(SipAccountJsonProxyStateHandler* handler) OVERRIDE;
   virtual int requestStateAllAccounts() OVERRIDE;

   // JsonApiClientModule
   virtual void setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport) OVERRIDE;
   virtual int processIncoming(const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // AutoTestProcessor
#ifdef CPCAPI2_AUTO_TEST
   virtual AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif

   // SipAccountManager
   virtual CPCAPI2::SipAccount::SipAccountHandle create() OVERRIDE;

   /**
   * DEPRECATED - do not use
   */
   virtual CPCAPI2::SipAccount::SipAccountHandle create(const SipAccountSettings& sipAccountSettings) OVERRIDE;

   virtual int configureDefaultAccountSettings(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountSettings& sipAccountSettings) OVERRIDE;
   virtual int configureTransportAccountSettings(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountSettings& sipAccountSettings, CPCAPI2::NetworkTransport transport) OVERRIDE;
   virtual int applySettings(CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;
   virtual int destroy(CPCAPI2::SipAccount::SipAccountHandle handle) OVERRIDE;
   virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipAccountHandler* handler) OVERRIDE;
   virtual int enable(CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;
   virtual int disable(CPCAPI2::SipAccount::SipAccountHandle account, bool force = false) OVERRIDE;
   virtual int requestRegistrationRefresh(CPCAPI2::SipAccount::SipAccountHandle account, unsigned int deadlineSecondsFromNow) OVERRIDE;
   virtual int setNetworkRestriction(CPCAPI2::SipAccount::SipAccountHandle account, NetworkTransport transport, bool restricted) OVERRIDE;
   virtual void setT1TimerValueMs(unsigned long t1ValueMs) OVERRIDE;
   virtual void setT2TimerValueMs(unsigned long t2ValueMs) OVERRIDE;
   virtual void setT4TimerValueMs(unsigned long t4ValueMs) OVERRIDE;
   virtual void setTDTimerValueMs(unsigned long tDValueMs) OVERRIDE;
   virtual void setTFTimerValueMs(unsigned long tFValueMs) OVERRIDE;
   virtual void setSipTotalTransactionTimeoutMs(unsigned long sipTotalTransactionTimeoutMs) OVERRIDE;
   virtual unsigned long getT1TimerValueMs() OVERRIDE;
   virtual unsigned long getT2TimerValueMs() OVERRIDE;
   virtual unsigned long getT4TimerValueMs() OVERRIDE;
   virtual unsigned long getTDTimerValueMs() OVERRIDE;
   virtual unsigned long getTFTimerValueMs() OVERRIDE;

   // timeout values

   /**
   * The blocking modes for the process() function. See that function
   * for details.
   */
   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;

   // constants
   static const int kAccountModuleDisabled = -1;

   /**
   * Allows the application code to receive callback notifications from the SDK.
   *
   * These callbacks will happen synchronously, in the same thread of execution as that in which
   * %process() is invoked.  Depending on the application threading model,
   * process() can be used in two different ways:
   * <ol>
   * <li>blocking mode ?Typically in this mode, %process() is called by the
   * application from a background (worker) thread.  The call to process()
   * blocks until a callback function needs to be invoked.
   * <li>non-blocking mode ?In this mode, %process() is called by the application
   * from the main (GUI) thread of the application, typically from the
   * main message/event loop.  In this mode, %process() returns immediately
   * and so must be called frequently enough that the application can receive
   * its callback notifications in a timely manner.
   *
   * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
   *                representing the time the call to process(..) will block waiting for a callback
   *                from the SDK
   * </ol>
   */
   virtual int process(unsigned int timeout) OVERRIDE;

   /**
   * Posts a functor to the SDK callback queue; this allows the application to "unblock" the
   * thread calling SipAccount::process(..) if it is blocked waiting for an SDK callback.
   * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
   */
   virtual void interruptProcess() OVERRIDE {};

   virtual void postToProcessThread(void (*pfun)(void*), void* obj) OVERRIDE;

   virtual int setAdornmentHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipAccountAdornmentHandler* handler) OVERRIDE;
   virtual int adornMessage(SipAccountHandle account, unsigned int adornmentMessageId, const cpc::vector<SipHeader>& customHeaders) OVERRIDE;

   virtual int decodeProvisioningResponse(const cpc::string& provisioningResponse, cpc::vector<SipAccountSettings>& outSipAccountSettings) OVERRIDE;

   void postCallback(resip::ReadCallbackBase* fp);

private:
   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request);

   void createImpl(const SipAccountSettings& accountSettings);
   void setHandlerImpl(SipAccount::SipAccountHandle account, SipAccount::SipAccountHandler* handler);
   int enableImpl(SipAccountHandle account);
   int disableImpl(SipAccountHandle account, bool force);
   void setStateHandlerImpl(SipAccountJsonProxyStateHandler* handler);
   void requestStateAllAccountsImpl();

   int handleCreateResult(const rapidjson::Value& functionObjectVal);
   int handleAccountStatusChanged(const rapidjson::Value& functionObjectVal);
   int handleAccountState(const rapidjson::Value& functionObjectVal);

private:
   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiTransport* mTransport;
   std::map<SipAccount::SipAccountHandle, SipAccount::SipAccountHandler*> mAppHandlers;
   resip::Fifo<resip::ReadCallbackBase> mCallbacks;
   std::function<void(void)> mCbHook;
   std::mutex mMutex;
   std::condition_variable mCondCreated;
   SipAccountHandle mServerCreatedHandle;
   SipAccountJsonProxyStateHandler* mStateHandler;
};
}
}
#endif // CPCAPI2_SIP_ACCOUNT_JSON_PROXY_INTERFACE_H
