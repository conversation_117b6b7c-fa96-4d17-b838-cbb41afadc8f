#pragma once

#ifndef JSON_SERIALIZE_HELPER_H
#define JSON_SERIALIZE_HELPER_H

#include "JsonDataTypeHelpers.h"

namespace CPCAPI2
{
namespace Json
{
   template<typename Writer, typename T, size_t size>
   void Serialize(Writer& w, const T (&v)[size])
   {
      w.StartArray();

      for(int i = 0; i < size; i++)
      {
         Serialize(w, v[i]);
      }

      w.<PERSON>rray();
   }

   template<typename Writer, typename T>
   void Serialize(Writer& w, const cpc::vector<T>& v)
   {
      w.StartArray();

      for(auto it = v.begin(), end = v.end(); it != end; ++it)
      {
         Serialize(w, *it);
      }

      w.EndArray();
   }

   template<typename Writer, typename T, typename U>
   void Serialize(Writer& w, const std::map<T, U>& v)
   {
      w.StartArray();

      for(auto it = v.begin(), end = v.end(); it != end; ++it)
      {
         w.StartObject();

         w.<PERSON>("key");
         Serialize(w, it->first);

         w.Key("value");
         Serialize(w, it->second);

         w.EndObject();
      }

      w.EndArray();
   }

   template<typename Writer, typename T>
   void Write(Writer& w, const char* name, const T& v)
   {
      w.Key(name);
      Serialize(w, v);
   }

   template<typename Writer,
            class T0
   >
   void WriteObject(Writer& writer, const char* obj_name,
      const char* name0, const T0& arg0
   )
   {
      writer.Key(obj_name);
      writer.StartObject();
      CPCAPI2::Json::Write(writer, name0, arg0);
      writer.EndObject();
   }

   template<typename Writer,
            class T0, class T1
   >
   void WriteObject(Writer& writer, const char* obj_name,
      const char* name0, const T0& arg0,
      const char* name1, const T1& arg1
   )
   {
      writer.Key(obj_name);
      writer.StartObject();
      CPCAPI2::Json::Write(writer, name0, arg0);
      CPCAPI2::Json::Write(writer, name1, arg1);
      writer.EndObject();
   }

   template<typename Writer,
            class T0, class T1, class T2
   >
   void WriteObject(Writer& writer, const char* obj_name,
      const char* name0, const T0& arg0,
      const char* name1, const T1& arg1,
      const char* name2, const T2& arg2
   )
   {
      writer.Key(obj_name);
      writer.StartObject();
      CPCAPI2::Json::Write(writer, name0, arg0);
      CPCAPI2::Json::Write(writer, name1, arg1);
      CPCAPI2::Json::Write(writer, name2, arg2);
      writer.EndObject();
   }

   template<typename Writer,
            class T0, class T1, class T2, class T3
   >
   void WriteObject(Writer& writer, const char* obj_name,
      const char* name0, const T0& arg0,
      const char* name1, const T1& arg1,
      const char* name2, const T2& arg2,
      const char* name3, const T3& arg3
   )
   {
      writer.Key(obj_name);
      writer.StartObject();
      CPCAPI2::Json::Write(writer, name0, arg0);
      CPCAPI2::Json::Write(writer, name1, arg1);
      CPCAPI2::Json::Write(writer, name2, arg2);
      CPCAPI2::Json::Write(writer, name3, arg3);
      writer.EndObject();
   }

   template<typename Writer,
            class T0, class T1, class T2, class T3, class T4
   >
   void WriteObject(Writer& writer, const char* obj_name,
      const char* name0, const T0& arg0,
      const char* name1, const T1& arg1,
      const char* name2, const T2& arg2,
      const char* name3, const T3& arg3,
      const char* name4, const T4& arg4
   )
   {
      writer.Key(obj_name);
      writer.StartObject();
      CPCAPI2::Json::Write(writer, name0, arg0);
      CPCAPI2::Json::Write(writer, name1, arg1);
      CPCAPI2::Json::Write(writer, name2, arg2);
      CPCAPI2::Json::Write(writer, name3, arg3);
      CPCAPI2::Json::Write(writer, name4, arg4);
      writer.EndObject();
   }

   template<typename Writer,
            class T0, class T1, class T2, class T3, class T4,
            class T5
   >
   void WriteObject(Writer& writer, const char* obj_name,
      const char* name0, const T0& arg0,
      const char* name1, const T1& arg1,
      const char* name2, const T2& arg2,
      const char* name3, const T3& arg3,
      const char* name4, const T4& arg4,
      const char* name5, const T5& arg5
   )
   {
      writer.Key(obj_name);
      writer.StartObject();
      CPCAPI2::Json::Write(writer, name0, arg0);
      CPCAPI2::Json::Write(writer, name1, arg1);
      CPCAPI2::Json::Write(writer, name2, arg2);
      CPCAPI2::Json::Write(writer, name3, arg3);
      CPCAPI2::Json::Write(writer, name4, arg4);
      CPCAPI2::Json::Write(writer, name5, arg5);
      writer.EndObject();
   }

   template<typename Writer,
            class T0, class T1, class T2, class T3, class T4,
            class T5, class T6
   >
   void WriteObject(Writer& writer, const char* obj_name,
      const char* name0, const T0& arg0,
      const char* name1, const T1& arg1,
      const char* name2, const T2& arg2,
      const char* name3, const T3& arg3,
      const char* name4, const T4& arg4,
      const char* name5, const T5& arg5,
      const char* name6, const T6& arg6
   )
   {
      writer.Key(obj_name);
      writer.StartObject();
      CPCAPI2::Json::Write(writer, name0, arg0);
      CPCAPI2::Json::Write(writer, name1, arg1);
      CPCAPI2::Json::Write(writer, name2, arg2);
      CPCAPI2::Json::Write(writer, name3, arg3);
      CPCAPI2::Json::Write(writer, name4, arg4);
      CPCAPI2::Json::Write(writer, name5, arg5);
      CPCAPI2::Json::Write(writer, name6, arg6);
      writer.EndObject();
   }

   template<typename Writer,
            class T0, class T1, class T2, class T3, class T4,
            class T5, class T6, class T7
   >
   void WriteObject(Writer& writer, const char* obj_name,
      const char* name0, const T0& arg0,
      const char* name1, const T1& arg1,
      const char* name2, const T2& arg2,
      const char* name3, const T3& arg3,
      const char* name4, const T4& arg4,
      const char* name5, const T5& arg5,
      const char* name6, const T6& arg6,
      const char* name7, const T7& arg7
   )
   {
      writer.Key(obj_name);
      writer.StartObject();
      CPCAPI2::Json::Write(writer, name0, arg0);
      CPCAPI2::Json::Write(writer, name1, arg1);
      CPCAPI2::Json::Write(writer, name2, arg2);
      CPCAPI2::Json::Write(writer, name3, arg3);
      CPCAPI2::Json::Write(writer, name4, arg4);
      CPCAPI2::Json::Write(writer, name5, arg5);
      CPCAPI2::Json::Write(writer, name6, arg6);
      CPCAPI2::Json::Write(writer, name7, arg7);
      writer.EndObject();
   }

   template<typename Writer,
            class T0, class T1, class T2, class T3, class T4,
            class T5, class T6, class T7, class T8
   >
   void WriteObject(Writer& writer, const char* obj_name,
      const char* name0, const T0& arg0,
      const char* name1, const T1& arg1,
      const char* name2, const T2& arg2,
      const char* name3, const T3& arg3,
      const char* name4, const T4& arg4,
      const char* name5, const T5& arg5,
      const char* name6, const T6& arg6,
      const char* name7, const T7& arg7,
      const char* name8, const T8& arg8
   )
   {
      writer.Key(obj_name);
      writer.StartObject();
      CPCAPI2::Json::Write(writer, name0, arg0);
      CPCAPI2::Json::Write(writer, name1, arg1);
      CPCAPI2::Json::Write(writer, name2, arg2);
      CPCAPI2::Json::Write(writer, name3, arg3);
      CPCAPI2::Json::Write(writer, name4, arg4);
      CPCAPI2::Json::Write(writer, name5, arg5);
      CPCAPI2::Json::Write(writer, name6, arg6);
      CPCAPI2::Json::Write(writer, name7, arg7);
      CPCAPI2::Json::Write(writer, name8, arg8);
      writer.EndObject();
   }

   template<typename Writer,
            class T0, class T1, class T2, class T3, class T4,
            class T5, class T6, class T7, class T8, class T9
   >
   void WriteObject(Writer& writer, const char* obj_name,
      const char* name0, const T0& arg0,
      const char* name1, const T1& arg1,
      const char* name2, const T2& arg2,
      const char* name3, const T3& arg3,
      const char* name4, const T4& arg4,
      const char* name5, const T5& arg5,
      const char* name6, const T6& arg6,
      const char* name7, const T7& arg7,
      const char* name8, const T8& arg8,
      const char* name9, const T9& arg9
   )
   {
      writer.Key(obj_name);
      writer.StartObject();
      CPCAPI2::Json::Write(writer, name0, arg0);
      CPCAPI2::Json::Write(writer, name1, arg1);
      CPCAPI2::Json::Write(writer, name2, arg2);
      CPCAPI2::Json::Write(writer, name3, arg3);
      CPCAPI2::Json::Write(writer, name4, arg4);
      CPCAPI2::Json::Write(writer, name5, arg5);
      CPCAPI2::Json::Write(writer, name6, arg6);
      CPCAPI2::Json::Write(writer, name7, arg7);
      CPCAPI2::Json::Write(writer, name8, arg8);
      CPCAPI2::Json::Write(writer, name9, arg9);
      writer.EndObject();
   }

} // namespace Json
} // namespace CPCAPI2

#endif // JSON_SERIALIZE_HELPER_H
