#pragma once

#if !defined(CPCAPI2_AUTH_SERVER_INTERFACE_H)
#define CPCAPI2_AUTH_SERVER_INTERFACE_H

#include "cpcapi2defs.h"
#include "auth_server/AuthServer.h"
#include "AuthServerBackend.h"
#include "AuthServerInternal.h"

#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"
#include "../util/AutoTestProcessor.h"

// Simple-Http-Server
#include <server_https.hpp>
// rapidjson
#include <document.h>

#include <rutil/Fifo.hxx>

#include <map>
#include <thread>

namespace CPCAPI2
{
class PhoneInterface;

namespace AuthServer
{
class AuthServerModule;
class DbAccess;

class AuthServerInterface : public AuthServer,
                            public AuthServerInternal,
                            public PhoneModule
#ifdef CPCAPI2_AUTO_TEST
                          , public AutoTestProcessor
#endif
{
public:
   AuthServerInterface(Phone* phone);
   virtual ~AuthServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   virtual int process(unsigned int timeout) OVERRIDE;
#ifdef CPCAPI2_AUTO_TEST
   virtual AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif

   virtual void interruptProcess() OVERRIDE;

   // AuthServer
   virtual int start(const AuthServerConfig& serverConfig = AuthServerConfig()) OVERRIDE;
   virtual int shutdown() OVERRIDE;
   virtual int setHandler(AuthServerHandler* handler) OVERRIDE;
   virtual int addUser(const cpc::string& username, const cpc::string& password) OVERRIDE;

   // AuthServerInternal
#ifdef CPCAPI2_AUTO_TEST
   virtual void dropIncomingLoginRequests(bool enable) OVERRIDE;
   virtual void dropIncomingAddUserRequests(bool enable) OVERRIDE;
#endif

   void post(resip::ReadCallbackBase* f);
   void postCallback(resip::ReadCallbackBase*);
   void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE;
   void setOnPrivilegedAccessCompleted(void(*onPrivilegedAccessCompleted)(void*), void* context) OVERRIDE { mOnPrivilegedAccessCompleted = std::bind(onPrivilegedAccessCompleted, context); }

private:
   int startImpl(const AuthServerConfig& serverConfig);

   int shutdownImpl();
   int setHandlerImpl(AuthServerHandler* handler);

   int addUserImpl(const cpc::string& username, const cpc::string& password);

   void dropIncomingLoginRequestsImpl(bool enable);
   void dropIncomingAddUserRequestsImpl(bool enable);

   template <typename T>
   void handleLoginRequest(const std::shared_ptr<typename SimpleWeb::Server<T>::Response> &response,
                           const std::shared_ptr<typename SimpleWeb::Server<T>::Request> &request, bool isV2);

   template <typename T>
   void handleAddUserRequest(const std::shared_ptr< typename SimpleWeb::Server<T>::Response >& response,
                             const std::shared_ptr< typename SimpleWeb::Server<T>::Request >& request);
   template <typename T>
   int startServerTypeImpl(std::unique_ptr<typename SimpleWeb::Server<T> >& server, const AuthServerConfig& serverConfig);

   template <typename T>
   bool validateRequest(const std::shared_ptr<typename SimpleWeb::Server<T>::Response>& response, const std::shared_ptr<typename SimpleWeb::Server<T>::Request>& request, bool requireEncryption, std::shared_ptr<rapidjson::Document>& jsonRequest);

private:
   std::function<void(void)> mCbHook;
   bool mShutdown;
   bool mDropLoginRequests;
   bool mDropAddUserRequests;
   resip::Fifo<resip::ReadCallbackBase> mCallbackFifo;
   PhoneInterface* mPhone;
   AuthServerHandler* mAppHandler;
   AuthServerConfig mConfig;
   std::thread* mServerThread;
   std::unique_ptr<SimpleWeb::Server<SimpleWeb::HTTPS> > mHttpsWebServer;
   std::unique_ptr<SimpleWeb::Server<SimpleWeb::HTTP> > mHttpWebServer;
   std::unique_ptr<AuthServerBackend> mDb;

   std::function<void()> mOnPrivilegedAccessCompleted;
};

}
}

#endif // CPCAPI2_AUTH_SERVER_INTERFACE_H
