#include "brand_branded.h"

#include <confconnector/ConferenceConnectorJsonProxy.h>
#include <confconnector/ConferenceConnectorJsonApi.h>

#if (CPCAPI2_BRAND_CONFERENCE_CONNECTOR_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "phone/PhoneInterface.h"
#include "ConferenceConnectorJsonServerInterface.h"
#endif
#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "phone/PhoneInterface.h"
#include "ConferenceConnectorJsonProxyInterface.h"
#endif

namespace CPCAPI2
{
namespace ConferenceConnector
{
   ConferenceConnectorJsonApi* ConferenceConnectorJsonApi::getInterface(Phone* cpcPhone)
   {
      if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_CONFERENCE_CONNECTOR_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<ConferenceConnectorJsonServerInterface>(phone, "ConferenceConnectorJsonApi");
#else
      return NULL;
#endif
   }

   ConferenceConnectorManagerJsonProxy* ConferenceConnectorManagerJsonProxy::getInterface(Phone* cpcPhone)
   {
      if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<ConferenceConnectorJsonProxyInterface>(phone, "ConferenceConnectorManagerJsonProxy");
#else
      return NULL;
#endif
   }
}
}
