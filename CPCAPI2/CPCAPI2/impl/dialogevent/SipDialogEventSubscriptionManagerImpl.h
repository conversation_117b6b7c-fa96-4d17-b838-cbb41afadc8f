#pragma once

#if !defined(CPCAPI2_SIP_DIALOG_EVENT_SUBSCRIPTION_MANAGER_IMPL_H)
#define CPCAPI2_SIP_DIALOG_EVENT_SUBSCRIPTION_MANAGER_IMPL_H

#include "cpcapi2defs.h"
#include "dialogevent/SipDialogEventSubscriptionManagerInterface.h"
#include "event/SipEventState.h"
#include "event/SipEventSubscriptionHandler.h"
#include "../event/SipEventManagerInterface.h"
#include "../account/SipAccountImpl.h"
#include "../account/SipAccountAwareFeature.h"

#include <resip/dum/DialogEventHandler.hxx>

namespace CPCAPI2
{
namespace SipDialogEvent
{
class SipDialogEventSubscriptionHandler;

class SipDialogEventSubscriptionManagerImpl : public CPCAPI2::SipAccount::SipAccountAwareFeature,
                                              public CPCAPI2::EventSyncHandler<CPCAPI2::SipEvent::SipEventSubscriptionHandler>,
                                              public resip::DialogEventHandler
{
public:
   typedef std::map<SipAccount::SipAccountHandle, SipDialogEventSubscriptionManagerImpl*> AccountMap;
   SipDialogEventSubscriptionManagerImpl(SipDialogEventSubscriptionManagerInterface* iff, CPCAPI2::SipEvent::SipEventManagerInterface* sipEventIf, std::shared_ptr<AccountMap> parentMap, CPCAPI2::SipAccount::SipAccountImpl& account);
   virtual ~SipDialogEventSubscriptionManagerImpl();

   // Invoked by SipDialogEventSubscriptionManagerInterface
   DialogInfoDocument createEmptyDialogInfo();
   CPCAPI2::SipEvent::SipEventState createEventState(SipDialogEventSubscriptionHandle subscription, const DialogInfoDocument& dialogInfoDoc);

private:
   SipDialogEventSubscriptionManagerInterface* mInterface;
   CPCAPI2::SipAccount::SipAccountImpl& mAccount;
   int mDocVersion;
   resip::DialogEventStateManager* mDialogEventStateManager;
   CPCAPI2::SipEvent::SipEventManagerInterface* mSipEventIf;
   bool mIncludeSessionDescription;
   resip::SharedPtr<resip::DialogUsageManager> mDum;
   std::weak_ptr<AccountMap> mParentMap;

   // SipAccountAwareFeature
   virtual int adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile) OVERRIDE { return kSuccess; }
   virtual int addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig) OVERRIDE;
   virtual int onDumBeingDestroyed() OVERRIDE;
   virtual void release() OVERRIDE;

   // SipEventSubscriptionHandler interface
   virtual int onNewSubscription(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NewSubscriptionEvent& args) OVERRIDE;
   virtual int onSubscriptionEnded(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionEndedEvent& args) OVERRIDE;
   virtual int onIncomingEventState(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingEventStateEvent& args) OVERRIDE;
   virtual int onIncomingResourceList(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingResourceListEvent& args) OVERRIDE;
   virtual int onSubscriptionStateChanged(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionStateChangedEvent& args) OVERRIDE;
   virtual int onNotifySuccess(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifySuccessEvent& args) OVERRIDE;
   virtual int onNotifyFailure(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifyFailureEvent& args) OVERRIDE;
   virtual int onError(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::ErrorEvent& args) OVERRIDE;

   // DialogEventHandler interface
   virtual void onTrying(const resip::TryingDialogEvent& evt) OVERRIDE;
   virtual void onProceeding(const resip::ProceedingDialogEvent& evt) OVERRIDE;
   virtual void onEarly(const resip::EarlyDialogEvent& evt) OVERRIDE;
   virtual void onConfirmed(const resip::ConfirmedDialogEvent& evt) OVERRIDE;
   virtual void onTerminated(const resip::TerminatedDialogEvent& evt) OVERRIDE;
   virtual void onMultipleEvents(const resip::MultipleEventDialogEvent& evt) OVERRIDE;

   cpc::string getEntity();
   DialogInfoDocument createFullStateDialogInfo();
   void sendDialogInfo(const DialogInfoDocument& doc);
   bool isSharedAppearanceDialogExtensionsEnabled(SipDialogEventSubscriptionHandle subscription);
};

}
}

#endif // CPCAPI2_SIP_DIALOG_EVENT_SUBSCRIPTION_MANAGER_IMPL_H
