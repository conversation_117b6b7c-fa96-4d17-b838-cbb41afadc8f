#pragma once

#if !defined(__CPCAPI2_IS_COMPOSING_HELPER_H__)
#define __CPCAPI2_IS_COMPOSING_HELPER_H__

#include "IsComposingDocument.h"
#include "../cpm/CpmHelper.h"
#include "iscomposing/IsComposingTypes.h"

using namespace CPCAPI2::CPM;

namespace CPCAPI2
{
namespace IsComposing
{

class IsComposingHelper : public CpmHelper
{
public:
   static IsComposingDocument createIsComposingDocument(IsComposingMessageState state, const resip::Mime& contentType, int refresh, const tm& lastActive);
   static void extractIsComposingDocument(const IsComposingDocument& isComposingDocument, IsComposingMessageState& state, resip::Mime& contentType, int& refreshInterval, tm& lastActive);

   static const resip::Mime IS_COMPOSING_CONTENT_TYPE;
};

}
}

#endif // __CPCAPI2_IS_COMPOSING_HELPER_H__