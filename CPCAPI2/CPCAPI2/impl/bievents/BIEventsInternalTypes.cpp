#include <brand_branded.h>
#include <assert.h>
#include <boost/algorithm/string.hpp>

#include "BIEventsInternalTypes.h"

#if (CPCAPI2_BRAND_BIEVENTS_MODULE == 1)

using namespace CPCAPI2::BIEvents;

BIEventRecord::BIEventRecord( void )
   : m_SummaryText( NULL ), m_Labels( NULL ), m_Variable( NULL )
{
   memset( &m_Fixed, 0, sizeof( fixed_t ));
}

BIEventRecord::BIEventRecord( const BIEventRecord & that )
   : m_SummaryText( NULL ), m_Labels( NULL ), m_Variable( NULL )
{
   m_Fixed = that.m_Fixed;

   if(( that.m_Fixed.SummarySize > 0 ) && ( that.m_SummaryText != NULL ))
   {
      m_SummaryText = new uint8_t[ that.m_Fixed.SummarySize ];
      memcpy( m_SummaryText, that.m_SummaryText, that.m_Fixed.SummarySize );
   }

   if(( that.m_Fixed.LabelSize > 0 ) && ( that.m_Labels != NULL ))
   {
      m_Labels = new uint8_t[ that.m_Fixed.LabelSize ];
      memcpy( m_Labels, that.m_Labels, that.m_Fixed.LabelSize );
   }

   if(( that.m_Fixed.VariableSize > 0 ) && ( that.m_Variable != NULL ))
   {
      m_Variable = new uint8_t[ that.m_Fixed.VariableSize ];
      memcpy( m_Variable, that.m_Variable, that.m_Fixed.VariableSize );
   }
}

BIEventRecord::BIEventRecord( const CPCAPI2::RPCIdentifier & id, const BIEventHeader& header, const BIEventBody& body )
   : m_SummaryText( NULL ), m_Labels( NULL ), m_Variable( NULL )
{
   memset( &m_Fixed, 0, sizeof( fixed_t ));

   SetID( id );
   strncpy( m_Fixed.Source, header.Source, 5 );
   strncpy( m_Fixed.Type, header.Type, 5 );
   m_Fixed.Group = header.Group;
   m_Fixed.Context = header.Context;
   m_Fixed.MillisSinceEpoch = header.MillisSinceEpoch;
   m_Fixed.SummarySize = header.Summary.size();

   // Correct time timestamp if needed
   if( m_Fixed.MillisSinceEpoch == 0 )
   {
      RPCTimestamp curTime;
      curTime.toMillis( m_Fixed.MillisSinceEpoch );
   }

   if( m_Fixed.SummarySize > 0 )
   {
      m_SummaryText = new uint8_t[ m_Fixed.SummarySize ];
      memcpy( m_SummaryText, header.Summary.c_str(), m_Fixed.SummarySize );
   }

   // Calculate the required label size.
   m_Fixed.LabelSize = 0;
   for( cpc::vector< cpc::string >::const_iterator iter = header.Labels.begin() ;
        iter != header.Labels.end() ; ++iter )
   {
      m_Fixed.LabelSize += sizeof( size_t ); // Add room for the size of the string
      m_Fixed.LabelSize += iter->size(); // doesn't include null
   }

   // Copy the labels from the header
   if( m_Fixed.LabelSize > 0 )
   {
      m_Labels = new uint8_t[ m_Fixed.LabelSize ];
      size_t offset = 0;
      for( cpc::vector< cpc::string >::const_iterator iter = header.Labels.begin() ;
           iter != header.Labels.end() ; ++iter )
      {
         // ensure they are uppercase
         std::string uppercase( iter->c_str() );
         boost::to_upper( uppercase );

         size_t stringSize( uppercase.size() );
         memcpy( m_Labels + offset, &stringSize, sizeof( size_t ));
         offset += sizeof( size_t );

         memcpy( m_Labels + offset, uppercase.c_str(), stringSize );
         offset += stringSize;
      }
   }

   // First, calculate the overall size required for allocation
   m_Fixed.VariableSize = 0;
   for( size_t i = 0 ; i < body.size() ; ++i )
   {
      BIPair pair( body[ i ] );

      // Each string is a size_t indicating the size, followed by
      // the actual string bytes (not including null)
      //size_t nameSize( pair.Name.size() );
      m_Fixed.VariableSize += sizeof( size_t );
      m_Fixed.VariableSize += pair.Name.size();

      // Include the size for the value type
      m_Fixed.VariableSize += sizeof( BIValueType );

      // The size of the value depends on the type
      switch( pair.Value.ValueType )
      {
      case BIValueType_bool:
         m_Fixed.VariableSize += sizeof( bool );
         break;
      case BIValueType_int:
         m_Fixed.VariableSize += sizeof( int64_t );
         break;
      case BIValueType_double:
         m_Fixed.VariableSize += sizeof( double );
         break;
      case BIValueType_string:
         m_Fixed.VariableSize += sizeof( size_t );
         m_Fixed.VariableSize += pair.Value.StringValue.size();
         break;
      default:
         assert( 0 && "Unknown BIValueType" );
         break;
      }
   }

   // Now allocate the size and copy in the data
   if( m_Fixed.VariableSize > 0 )
   {
      m_Variable = new uint8_t[ m_Fixed.VariableSize ];
      size_t offset = 0;

      for( size_t i = 0 ; i < body.size() ; ++i )
      {
         BIPair pair( body[ i ] );
         size_t temp;

         // Each string is a size_t indicating the size, followed by
         // the actual string bytes (not including null)
         temp = pair.Name.size();
         memcpy( m_Variable + offset, &temp, sizeof( size_t ));
         offset += sizeof( size_t );
         if( temp > 0 )
         {
            memcpy( m_Variable + offset, pair.Name.c_str(), temp );
            offset += temp;
         }

         // Write the type of the value
         memcpy( m_Variable + offset, &pair.Value.ValueType, sizeof( BIValueType ));
         offset += sizeof( BIValueType );

         // The size of the value depends on the type
         switch( pair.Value.ValueType )
         {
         case BIValueType_bool:
            memcpy( m_Variable + offset, &pair.Value.BoolValue, sizeof( bool ));
            offset += sizeof( bool );
            break;
         case BIValueType_int:
            memcpy( m_Variable + offset, &pair.Value.IntValue, sizeof( int64_t ));
            offset += sizeof( int64_t );
            break;
         case BIValueType_double:
            memcpy( m_Variable + offset, &pair.Value.DoubleValue, sizeof( double ));
            offset += sizeof( double );
            break;
         case BIValueType_string:
            temp = pair.Value.StringValue.size();
            memcpy( m_Variable + offset, &temp, sizeof( size_t ));
            offset += sizeof( size_t );
            if( temp > 0 )
            {
               memcpy( m_Variable + offset, pair.Value.StringValue.c_str(), temp );
               offset += temp;
            }
            break;
         default:
            assert( 0 && "Unknown BIValueType" );
            break;
         }
      }
   }
}

BIEventRecord::~BIEventRecord( void )
{
   delete [] m_SummaryText;
   delete [] m_Labels;
   delete [] m_Variable;
}

void BIEventRecord::SetID( const CPCAPI2::RPCIdentifier & id )
{
   memset( m_Fixed.ID, 0, 64 );
   strncpy( m_Fixed.ID, id.c_str(), 64 );
}

bool BIEventRecord::Deserialize( BIEventHeader & outHeader, BIEventBody & outBody ) const
{
   outHeader.Source.assign( m_Fixed.Source, 5 );
   outHeader.Type.assign( m_Fixed.Type, 5 );
   outHeader.Group = m_Fixed.Group;
   outHeader.Context = m_Fixed.Context;
   outHeader.MillisSinceEpoch = m_Fixed.MillisSinceEpoch;

   if( m_Fixed.SummarySize > 0 && m_SummaryText != NULL )
      outHeader.Summary.assign(( const char * ) m_SummaryText, m_Fixed.SummarySize );

   if( m_Fixed.LabelSize > 0 && m_Labels != NULL )
   {
      size_t offset = 0;
      size_t strSize = 0;
      cpc::string str;

      while( offset < m_Fixed.LabelSize )
      {
         // Read the label size
         memcpy( &strSize, m_Labels + offset, sizeof( size_t ));
         offset += sizeof( size_t );

         // Parse the label out if need be
         if( strSize > 0 )
         {
            str.assign(( const char * )( m_Labels + offset ), strSize );
            offset += strSize;
            outHeader.Labels.push_back( str ); // success! append
         }
      }
   }

   outBody.clear();

   if( m_Fixed.VariableSize > 0 && m_Variable != NULL )
   {
      // We have to parse out the contents and separate them into strings
      BIPair pair;
      size_t offset = 0;
      size_t temp = 0;

      while( offset < m_Fixed.VariableSize )
      {
         // Read the string size for the name
         memcpy( &temp, m_Variable + offset, sizeof( size_t ));
         offset += sizeof( size_t );

         // Read the string for the name
         pair.Name.assign(( const char * )( m_Variable + offset ), temp );
         offset += temp;

         // Read the type of the value of the pair
         memcpy( &pair.Value.ValueType, m_Variable + offset, sizeof( BIValueType ));
         offset += sizeof( BIValueType );

         switch( pair.Value.ValueType )
         {
         case BIValueType_bool:
            memcpy( &pair.Value.BoolValue, m_Variable + offset, sizeof( bool ));
            offset += sizeof( bool );
            break;
         case BIValueType_int:
            memcpy( &pair.Value.IntValue, m_Variable + offset, sizeof( int64_t ));
            offset += sizeof( int64_t );
            break;
         case BIValueType_double:
            memcpy( &pair.Value.DoubleValue, m_Variable + offset, sizeof( double ));
            offset += sizeof( double );
            break;
         case BIValueType_string:
            memcpy( &temp, m_Variable + offset, sizeof( size_t ));
            offset += sizeof( size_t );
            pair.Value.StringValue.assign(( const char * ) m_Variable + offset, temp );
            offset += temp;
            break;
         default:
            assert( 0 && "Unknown BIValueType" );
            break;
         }

         outBody.push_back( pair );
      }
   }

   return true;
}

BIEventRecord & BIEventRecord::operator=( const BIEventRecord & that )
{
   if( this == &that )
      return *this;

   this->m_Fixed = that.m_Fixed; // all basic types, should be OK
   // TODO: test this assumption in the debugger for the arrays

   if( m_SummaryText != that.m_SummaryText )
   {
      delete [] m_SummaryText;
      if(( that.m_Fixed.SummarySize > 0 ) && ( that.m_SummaryText != NULL ))
      {
         m_SummaryText = new uint8_t[ that.m_Fixed.SummarySize ];
         memcpy( m_SummaryText, that.m_SummaryText, that.m_Fixed.SummarySize );
      }
   }

   if( m_Variable != that.m_Variable )
   {
      delete [] m_Variable;

      if(( that.m_Fixed.VariableSize > 0 ) && ( that.m_Variable != NULL ))
      {
         m_Variable = new uint8_t[ that.m_Fixed.VariableSize ];
         memcpy( m_Variable, that.m_Variable, that.m_Fixed.VariableSize );
      }
   }

   return *this;
}

bool BIEventRecord::operator==( const BIEventRecord & that )
{
   if( this == &that )
      return true;

   // Compare the primitives first for speed advantage
   if( this->m_Fixed.Group != that.m_Fixed.Group )
      return false;

   if( this->m_Fixed.Context != that.m_Fixed.Context )
      return false;

   if( this->m_Fixed.MillisSinceEpoch != that.m_Fixed.MillisSinceEpoch )
      return false;

   if( this->m_Fixed.LabelSize != that.m_Fixed.LabelSize )
      return false;

   if( this->m_Fixed.VariableSize != that.m_Fixed.VariableSize )
      return false;

   if( this->m_Fixed.SummarySize != that.m_Fixed.SummarySize )
      return false;

   // use memcmp since we might have UTF-8 strings etc
   if( memcmp( this->m_Fixed.ID, that.m_Fixed.ID, 64 ) != 0 )
      return false;

   if( memcmp( this->m_Fixed.Source, that.m_Fixed.Source, 5 ) != 0 )
      return false;

   if( memcmp( this->m_Fixed.Type, that.m_Fixed.Type, 5 ) != 0 )
      return false;

   // Check the summary text
   if(( this->m_SummaryText == NULL && that.m_SummaryText != NULL ) ||
      ( this->m_SummaryText != NULL && that.m_SummaryText == NULL ))
      return false;

   if( memcmp( this->m_SummaryText, that.m_SummaryText, this->m_Fixed.SummarySize ) != 0 )
      return false;

   // Check the labels
   if(( this->m_Labels == NULL && that.m_Labels != NULL ) ||
      ( this->m_Labels != NULL && that.m_Labels == NULL ))
      return false;

   if( memcmp( this->m_Labels, that.m_Labels, this->m_Fixed.LabelSize ) != 0 )
      return false;

   // Check the variable data
   if(( this->m_Variable == NULL && that.m_Variable != NULL ) ||
      ( this->m_Variable != NULL && that.m_Variable == NULL ))
      return false;

   if( memcmp( this->m_Variable, that.m_Variable, this->m_Fixed.VariableSize ) != 0 )
      return false;

   // Everything passed.
   return true;
}

#endif //  (CPCAPI2_BRAND_BIEVENTS_MODULE == 1)
