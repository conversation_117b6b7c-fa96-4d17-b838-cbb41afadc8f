#include "brand_branded.h"

#if (CPCAPI2_BRAND_OPEN_LDAP_MODULE == 1)

#ifdef WIN32
#include <windows.h>
#include <Winldap.h>
#else
#include <ldap.h>
#endif

#include "LdapClientImpl.h"

#include "../util/DumFpCommand.h"
#include "../util/cpc_logger.h"

#ifdef ANDROID
#include <android/log.h>
#endif

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::LDAP

#define SEARCH_PAGE_SIZE 500

namespace CPCAPI2
{
  namespace OpenLdap
  {

#ifdef WIN32

     wchar_t* allocAndCopyString(const char* str)
     {
        // Need to convert from char* to wchar_t*. Use cpc::string to do the conversion -> to wstring
        auto sz = strlen(str) + 1;
        wchar_t* ret = new wchar_t[sz];
        cpc::string toConvert(str);
        std::wstring converted = toConvert; // wstring operator will properly handle encoding issues
        wcscpy_s(ret, sz, converted.c_str());
        return ret;
     }

#endif

  LdapClientImpl::LdapClientImpl(LdapClientHandle handle, LdapManagerInterface* itf) :
     mHandle(handle),
     mInterface(itf),
     mAppHandler(NULL),
     mUserName(""),
     mPassword(""),
     mServerUrl(""),
     mEncryption(LdapEncryption_None),
     mCertStrategy(LdapCertStrategy_Never),
     mTimeout(0),
     mState(LdapDisconnected),
     mSearchResult(NULL),
     mCurrentEntry(NULL),
     mCurrentElement(NULL),
     currentAttribute(NULL),
     mChaseReferrals(true),
     mArecExclusive(false)
  {

  }

  LdapClientImpl::~LdapClientImpl()
  {
     LdapDisconnect();
  }

  int LdapClientImpl::LdapApplySettings(const LdapClientSettings& settings)
  {
      DebugLog( << "LdapApplySettings: mUserName = " << settings.username);
      //DebugLog( << "LdapApplySettings: mPassword = " << settings.password);
      DebugLog( << "LdapApplySettings: mServerUrl = " << settings.serverUrl);
      DebugLog( << "LdapApplySettings: encryption = " << settings.encryption);
      DebugLog( << "LdapApplySettings: cert_strategy = " << settings.cert_strategy);
      DebugLog( << "LdapApplySettings: connection_timeout = " << settings.connection_timeout);


      mUserName = settings.username;
      mPassword = settings.password;
      mServerUrl = settings.serverUrl;
      mEncryption = settings.encryption;
      mCertStrategy = settings.cert_strategy;
      mTimeout = settings.connection_timeout;
      mChaseReferrals = settings.chase_referrals;
      mArecExclusive = settings.arec_exclusive;

      return kSuccess;
  }

  void
  LdapClientImpl::setHandler(LdapHandler* handler)
  {
     mAppHandler = handler;
  }

  void LdapClientImpl::setSdkObserver(const std::set<LdapHandler*>* observers)
  {
     mSdkObservers = observers;
  }

  void LdapClientImpl::FireError(LdapErrorType type, cpc::string error)
  {
     ErrorEvent evt;
     evt.errorType = type;
     evt.errorText = error;
     fireEvent(cpcFunc( LdapHandler::onError ), mHandle, evt );
  }

  void LdapClientImpl::SetLdapState(LdapState state)
  {
     DebugLog( << "SetLdapState: old = " << mState << " new ="<<state );
     if(state != mState)
     {
       OnStateChangedEvent evt;
       evt.state = state;
       fireEvent(cpcFunc( LdapHandler::onStateChanged ), mHandle, evt );

       mState = state;
     }
  }

  int
  LdapClientImpl::LdapInit()
  {
    DebugLog( << "LdapInit" );

#ifndef WIN32

    // set option for secure connections
    int err = ldap_set_option(NULL, LDAP_OPT_X_TLS_REQUIRE_CERT, &(mCertStrategy));
    if (err != LDAP_OPT_SUCCESS) {
       ErrLog( << "LdapInit: unable to set require cert option: " << ldap_err2string(err));
       FireError(LdapConnectionError,ldap_err2string(err));
    }

    int debugLevel = 7;
    int ldap_debug = 3;

    switch (debugLevel)
    {
        case 3:
            ldap_debug = 3; //LDAP_LEVEL_ERR
        break;
        case 4:
            ldap_debug = 4; //LDAP_LEVEL_WARNING
        break;
        case 6:
            ldap_debug = 6; //LDAP_LEVEL_INFO
        break;
        default:
            ldap_debug = 7; //LDAP_LEVEL_DEBUG
        break;
    }

    err = ldap_set_option(NULL, LDAP_OPT_DEBUG_LEVEL, &ldap_debug);
    if (err != LDAP_OPT_SUCCESS)
    {
        ErrLog( << "LdapInit: unable to set require cert option: " << ldap_err2string(err));
        FireError(LdapConnectionError,ldap_err2string(err));
    }

    err = ldap_set_option(NULL, LDAP_OPT_REFERRALS, mChaseReferrals ? LDAP_OPT_ON : LDAP_OPT_OFF);
    if (err != LDAP_OPT_SUCCESS)
    {
       ErrLog(<< "LdapInit: unable to turn off referrals");
       FireError(LdapConnectionError, ldap_err2string(err));
    }
#endif
    return kSuccess;
  }

  int
  LdapClientImpl::LdapConnect()
  {
    InfoLog(<< "Connect " );

    DebugLog( << "LdapConnect: mServerUrl = " << mServerUrl);
    DebugLog( << "LdapConnect: mUserName = " << mUserName);
    //DebugLog( << "LdapConnect: mPassword = " << mPassword);
#ifdef WIN32
    ULONG err;
    std::wstring wServer = mServerUrl;
    // Explicitly call the initW function to properly use Unicode encoding. 
    mServerConnection = ldap_sslinitW((wchar_t*)wServer.c_str(), mEncryption == LdapEncryption_Ldaps ? LDAP_SSL_PORT : LDAP_PORT, mEncryption == LdapEncryption_Ldaps);
#else
    int err = ldap_initialize(&mServerConnection, mServerUrl.c_str());
    if (err != LDAP_SUCCESS) {
        ErrLog( << "Unable to initialize: " << ldap_err2string(err));
        FireError(LdapConnectionError,ldap_err2string(err));
        return kError;
    }
#endif

    int version = LDAP_VERSION3;
    err = ldap_set_option(mServerConnection, LDAP_OPT_PROTOCOL_VERSION, &version);
    if (err != LDAP_SUCCESS) {
#ifdef WIN32
       ldap_unbind_s(mServerConnection);
#else
        ldap_unbind_ext_s(mServerConnection, NULL, NULL);
#endif
        ErrLog(<< "Unable to set version: " << ldap_err2string(err));
        FireError(LdapConnectionError,ldap_err2string(err));
        return kError;
    }

#ifdef WIN32
    // Windows cannot set options without a server handle, set the OPT_REFERRAL value here
    err = ldap_set_option(mServerConnection, LDAP_OPT_REFERRALS, mChaseReferrals ? LDAP_OPT_ON : LDAP_OPT_OFF);
    if (err != LDAP_SUCCESS)
    {
       ldap_unbind_s(mServerConnection);
       ErrLog(<< "Unable to set refferal option: " << ldap_err2string(err));
       FireError(LdapConnectionError, ldap_err2string(err));
       return kError;
    }

    DebugLog(<< "LdapConnect mArecExclusive: " << (mArecExclusive ? "true" : "false"));
    err = ldap_set_option(mServerConnection, LDAP_OPT_AREC_EXCLUSIVE, mArecExclusive ? LDAP_OPT_ON : LDAP_OPT_OFF);
    if (err != LDAP_SUCCESS)
    {
       ldap_unbind_s(mServerConnection);
       ErrLog(<< "Unable to set arec_exclusive option: " << ldap_err2string(err));
       FireError(LdapConnectionError, ldap_err2string(err));
       return kError;
    }
#endif

#ifndef WIN32
    if(mTimeout>0)
    {
        struct timeval tv;
        tv.tv_sec = mTimeout;
        tv.tv_usec = 0;
        err = ldap_set_option(mServerConnection, LDAP_OPT_NETWORK_TIMEOUT, (const void *)&tv);
        if (err != LDAP_SUCCESS) {
            ldap_unbind_ext_s(mServerConnection, NULL, NULL);
            ErrLog(<< "Unable to set network timout: " << ldap_err2string(err));
            FireError(LdapConnectionError,ldap_err2string(err));
            return kError;
        }
    }
#endif

    if (mEncryption == 2) { //STARTTLS
        //CERT Strategy: 0 - never, 1 - hard, 2 - demand, 3 - allow, 4 - try
        //err = ldap_set_option(mServerConnection, LDAP_OPT_X_TLS_REQUIRE_CERT, &cert_strategy);
#ifndef WIN32
        err = ldap_set_option(mServerConnection, LDAP_OPT_X_TLS_REQUIRE_CERT, &(mCertStrategy));
        if (err == LDAP_SUCCESS)
        {
            int is_server = 0;
            err = ldap_set_option(mServerConnection, LDAP_OPT_X_TLS_NEWCTX, &is_server);
        }
#endif
       if (err != LDAP_SUCCESS) {
#ifdef WIN32
          ldap_unbind_s(mServerConnection);
#else
            ldap_unbind_ext_s(mServerConnection, NULL, NULL);
#endif
            ErrLog(<< "Unable to set Certifiaction Strategy: "<< ldap_err2string(err));
            FireError(LdapConnectionError,ldap_err2string(err));
            return kError;
       } else {
            InfoLog(<< "certifiaction strategy set properly");
       }

#ifdef WIN32
       ULONG serverResponse;
       ULONG rc = ldap_start_tls_s(mServerConnection, &serverResponse, NULL, NULL, NULL);
#else
       int rc = ldap_start_tls_s(mServerConnection, NULL, NULL);
#endif
       if (rc != LDAP_SUCCESS) {
           ErrLog(<< "Unable to ldap_start_tls: "<< ldap_err2string(rc));
           FireError(LdapConnectionError,ldap_err2string(rc));
        return kError;
       }
    }

    SetLdapState(LdapConnecting);


    if(!mUserName.empty() && !mPassword.empty()) {
        BerValue cred, *servercredp = NULL;
        cred.bv_val = strdup((char *)mPassword.c_str());
        cred.bv_len = strlen(mPassword.c_str());

        //InfoLog(<< "PASS: "<<  cred.bv_val << " size = " << cred.bv_len);
#ifdef WIN32
#define LDAP_SASL_SIMPLE ""
#define LDAP_SASL_NULL NULL
        std::wstring un = mUserName;
        std::wstring pw = mPassword;

        // Explicitly use the simple_bind_sW function to support Unicode username/password
        err = ldap_simple_bind_sW(mServerConnection, (wchar_t*)un.c_str(), (wchar_t*)pw.c_str());
        
#else
        err = ldap_sasl_bind_s(mServerConnection, mUserName.c_str(), LDAP_SASL_SIMPLE, &cred, NULL, NULL, &servercredp);
#endif

        InfoLog(<< "Login with pass " );
    } else {
#ifdef WIN32
        err = ldap_simple_bind_sW(mServerConnection, NULL, NULL);
#else
        err = ldap_sasl_bind_s(mServerConnection, NULL, LDAP_SASL_NULL, NULL, NULL, NULL, NULL); // Anonymous Auth
#endif
        InfoLog(<< "Anonymous " );
    }
    if (err != LDAP_SUCCESS) {
        ErrLog(<< "Unable to login: "<< ldap_err2string(err));
#ifdef WIN32
        ldap_unbind_s(mServerConnection);
#else
        ldap_unbind_ext_s(mServerConnection, NULL, NULL);
#endif
        FireError(LdapConnectionError,ldap_err2string(err));
        SetLdapState(LdapDisconnected);
        return kError;
    }

    InfoLog(<< "Successfully connected to server. " );
    SetLdapState(LdapConnected);

    return kSuccess;
  }

  int
  LdapClientImpl::LdapDisconnect()
  {
    InfoLog(<< "Disconnect " );
    if(mState==LdapConnected)
    {
#ifdef WIN32
       ldap_unbind_s(mServerConnection);
#else
      ldap_unbind_ext_s(mServerConnection, NULL, NULL);
#endif
      SetLdapState(LdapDisconnected);
    }
    return kSuccess;
  }


  void LdapClientImpl::LdapSetDataMap(LdapDataMap map)
  {
     InfoLog(<< "setDataMap " );
     mDataMap = map;
  }

  int LdapClientImpl::LdapSearch(cpc::string searchPattern, cpc::string rootDn, LdapSearchScope scopeSetting,int timeout,int maxsize, bool opErrIgnore)
  {
#ifdef WIN32
    #define LDAPControl LDAPControlW
    #define PLDAPControl LDAPControl*
    #define ber_memalloc malloc
    #define ber_int_t ULONG
    #define ldap_create_page_control ldap_create_page_controlW
    #define ldap_parse_result ldap_parse_resultW
    #define ldap_control_free ldap_control_freeW
    #define ldap_controls_free ldap_controls_freeW
    #define MSG_ID_T ULONG
    #define ERR_CODE_T ULONG
#else
    #define PLDAPControl LDAPControl*
    #define MSG_ID_T int
    #define ERR_CODE_T int
#endif
        
    InfoLog(<< "Search: <" << searchPattern << ">");
    InfoLog(<< "Search: timeout = " << timeout << " max entry = " << maxsize);

    if (mState != LdapConnected)
    {
        InfoLog(<< "Error: Not Connected");
        return kError;
    }
     
    // Set the maximum number of entries returned.
    ldap_set_option(mServerConnection, LDAP_OPT_SIZELIMIT, (void *)&maxsize );

    //Set the maximum number of seconds to wait.
    ldap_set_option( mServerConnection, LDAP_OPT_TIMELIMIT, (void *)&timeout );

    // since the maxsize setting doesn't seem to be working I reduce the page
    // size if needed so we're not fetch unnecessary content from the server
    int pageSize = SEARCH_PAGE_SIZE;
    if (maxsize && maxsize < pageSize)
        pageSize = maxsize;
      
    int result = 0;
    berval* cookie = NULL;
    bool morePages = false;
    cpc::vector<LdapDataEntry> search_result_list;

    do 
    {
        // Set page size
        LDAPControl* pageControl = NULL;
        result = ldap_create_page_control(mServerConnection, pageSize, cookie, 1, &pageControl);
        assert(result == LDAP_SUCCESS);
        LDAPControl* controlList[2] = {pageControl, NULL};

        char* attribs[] = { (char*)mDataMap.displayName.c_str(), (char*)(mDataMap.firstName.c_str()), (char*)(mDataMap.lastName.c_str()), (char*)(mDataMap.mobilePhone.c_str()),
                           (char*)(mDataMap.officePhone.c_str()), (char*)(mDataMap.email.c_str()), (char*)(mDataMap.workPhone.c_str()), (char*)(mDataMap.homePhone.c_str()), (char*)(mDataMap.softphone.c_str()),
                           (char*)(mDataMap.company.c_str()), (char*)(mDataMap.department.c_str()), (char*)(mDataMap.jabber.c_str()), (char*)(mDataMap.jobTitle.c_str()), (char*)(mDataMap.street.c_str()),
                           (char*)(mDataMap.city.c_str()), (char*)(mDataMap.state.c_str()), (char*)(mDataMap.zip.c_str()), (char*)(mDataMap.country.c_str()), NULL };

        MSG_ID_T msgId = 0;
#ifdef WIN32
        // Filter out any empty strings (389 DS in particular will return a protocol error if provided attributes array has more than one empty string)
        int ldapDataMapMaxAttribs = sizeof(attribs) / sizeof(char*);
        wchar_t** filteredAttribs = new wchar_t* [ldapDataMapMaxAttribs];
        int j = 0;
        for (int i = 0; i < ldapDataMapMaxAttribs - 1; i++) // -1 here to account for the null terminated array
        {
            if (strlen(attribs[i]) > 0)
            {
                filteredAttribs[j++] = allocAndCopyString(attribs[i]);
            }
        }
        filteredAttribs[j] = NULL;

        // Use cpc::string wstring operator to handle encoding issues
        std::wstring wRoot = rootDn;
        std::wstring wSearch = searchPattern;
       
        // Explictly use the ldap_search_ext_sW function to support Unicode
        result = ldap_search_extW(mServerConnection, (wchar_t*)wRoot.c_str(), scopeSetting, (wchar_t*)wSearch.c_str(), filteredAttribs, 0, controlList, NULL, timeout, maxsize, &msgId);

        for (int i = 0; i < j; i++)
        {
            delete[] filteredAttribs[i];
        }
#else
        // Filter out any empty strings (389 DS in particular will return a protocol error if provided attributes array has more than one empty string)
        int ldapDataMapMaxAttribs = sizeof(attribs) / sizeof(char*);
        char** filteredAttribs = new char* [ldapDataMapMaxAttribs];
        int j = 0;
        for (int i = 0; i < ldapDataMapMaxAttribs - 1; i++) // -1 here to account for the null terminated array
        {
            if (strlen(attribs[i]) > 0)
            {
                filteredAttribs[j++] = attribs[i];
            }
        }
        filteredAttribs[j] = NULL;
        timeval tv;
        tv.tv_sec = timeout;
        tv.tv_usec = 0;
        result = ldap_search_ext(mServerConnection, rootDn.c_str(), (int) scopeSetting, searchPattern.c_str(), filteredAttribs, 0, controlList, NULL, &tv, maxsize, &msgId);
#endif
        assert(result == LDAP_SUCCESS || result == LDAP_PARTIAL_RESULTS);

        delete[] filteredAttribs;
        filteredAttribs = NULL;

        ldap_control_free(pageControl);
        pageControl = NULL;

        ber_bvfree(cookie);
        cookie = NULL;

        InfoLog(<< "search result: " << result);

        if (result == LDAP_NO_SUCH_OBJECT)
        {
            LdapSearchCleanup();
            FireError(LdapNoResults,"No such objects");
            return kSuccess;
        }
        else if (result != LDAP_SUCCESS && result != LDAP_SIZELIMIT_EXCEEDED &&
            !(opErrIgnore && result == LDAP_OPERATIONS_ERROR))
        {
            LdapSearchCleanup();
            FireError(LdapSearchError,ldap_err2string(result));
            return kSuccess;
        }

        result = ldap_result(mServerConnection, msgId, LDAP_MSG_ALL, nullptr, &mSearchResult);
        if (result == 0 || result == -1)
        {
            if (mSearchResult != nullptr)
            {
                ldap_msgfree(mSearchResult);
                mSearchResult = nullptr;
            }
            LdapSearchCleanup();
            FireError(LdapNoResults,"ldap_result error");
            return kSuccess;
        }
        
        ERR_CODE_T errcode = 0;
        PLDAPControl *returnedControls = NULL;
        result = ldap_parse_result(mServerConnection, mSearchResult, &errcode, NULL, NULL, NULL, &returnedControls, 0);
        assert(result == LDAP_SUCCESS);

        ber_int_t totalCount;
#ifdef WIN32
        // on Windows the cookie is allocated for us
        result = ldap_parse_page_controlW(mServerConnection, returnedControls, &totalCount, &cookie);
#else
        cookie = (berval*)ber_memalloc(sizeof(berval));
        result = ldap_parse_pageresponse_control(mServerConnection, *returnedControls, &totalCount, cookie);
#endif
        assert(result == LDAP_SUCCESS);

        morePages = cookie && cookie->bv_val != nullptr && (strlen(cookie->bv_val) > 0);

        if (returnedControls != NULL)
        {
            ldap_controls_free(returnedControls);
            returnedControls = nullptr;
        }

        LdapParseResult(&search_result_list);
        InfoLog(<< "accumulated result: " << search_result_list.size());

        LdapSearchCleanup();
        
        // check here because maxsize seems to be ignored even though it's set in two places above
    } while (morePages && (maxsize==0 || search_result_list.size() < maxsize));
      
    ber_bvfree(cookie);

    if (search_result_list.size() > 0)
    {
        LdapDataEvent evt;
        evt.entries = search_result_list;
        fireEvent(cpcFunc( LdapHandler::onSearchCompleted ), mHandle, evt );

        FireError(LdapNoError,ldap_err2string(LDAP_SUCCESS));
    }
    else
    {
        FireError(LdapNoResults,"No Results");
    }

    return kSuccess;
}


int LdapClientImpl::LdapSearchCount()
{
    int count = ldap_count_entries(mServerConnection, mSearchResult);
    return count;
}

int LdapClientImpl::LdapSearchFirstEntry()
{
    int result = 0;

    mCurrentEntry = ldap_first_entry(mServerConnection, mSearchResult);

    if (mCurrentEntry == NULL) {
        result = -1;
    }

    return result;
}

int LdapClientImpl::LdapSearchNextEntry()
{
    int result = 0;
    mCurrentEntry = ldap_next_entry(mServerConnection, mCurrentEntry);

   if (mCurrentEntry == NULL) {
        result = -1;
   }

   return result;
}

const char* LdapClientImpl::LdapSearchFirstAttribute()
{
    if (currentAttribute != NULL) {
        ldap_memfree(currentAttribute);
    }

    currentAttribute = ldap_first_attribute(mServerConnection, mCurrentEntry, &mCurrentElement);

    if (currentAttribute != NULL) {
        return currentAttribute;
    }

    return ""; // error
}

const char* LdapClientImpl::LdapSearchNextAttribute()
{
    if (currentAttribute != NULL) {
        ldap_memfree(currentAttribute);
    }
    currentAttribute = ldap_next_attribute(mServerConnection, mCurrentEntry, mCurrentElement);

   if (currentAttribute != NULL) {
       return currentAttribute;
    }

    return ""; // error
}

const char* LdapClientImpl::LdapSearchAttributeValues() {
    struct berval ** vals;
    static char values[1024]; // SWIG bugfix (static), not multithread safe
    char tmp_val[1024];

    if (currentAttribute == NULL) {
        return ""; // error
    }

    if ((vals = ldap_get_values_len(mServerConnection, mCurrentEntry, currentAttribute))) {
        int i;
        int total_len = 0;
        for (i=0; vals[i]; i++) {
            //DebugLog(<< "Value < " << vals[i]->bv_val << "> Key <" << currentAttribute << ">");
            total_len += vals[i]->bv_len;
            if (vals[i]->bv_len > 0 ) {
                total_len++;
            }
        }

        // For '\0'
        total_len++;
        if (total_len >= sizeof(values)) {
            ErrLog(<< "Total length exceeds the capacity ");
            return ""; // error
        }

        values[0] = 0;
        for (i=0; vals[i]; i++) {
            //DebugLog(<< "Value < " << vals[i]->bv_val << "> Key <" << currentAttribute << ">");
            if (strlen(values) > 0 ) {
                strcat(values, "|");
            }
            snprintf(tmp_val, 1024, "%s", vals[i]->bv_val);
            strcat(values, tmp_val);
        }

        ldap_value_free_len(vals);

        return values;
    } else {
         return ""; // error
    }
}

int LdapClientImpl::LdapSearchCleanup()
{
   InfoLog(<< "LdapSearchCleanup " );
   if(mSearchResult != NULL)
   {
      ldap_msgfree(mSearchResult);
      mSearchResult = NULL;
   }

   return 0;
}


void LdapClientImpl::LdapParseResult(cpc::vector<LdapDataEntry> *search_result_list)
{
  InfoLog(<< "LdapParseResult " );

  int searchCount = LdapSearchCount();
  InfoLog(<< "Matched " << searchCount);

  if (searchCount > 0) {

    int validEntry = LdapSearchFirstEntry();
    while (validEntry >= 0) {
       std::string nextAttr = LdapSearchFirstAttribute();

       LdapDataEntry entry;

       while (!nextAttr.empty()) {
            //DebugLog( << "attribute: <" << nextAttr << ">");
            if (!mDataMap.displayName.empty() && nextAttr.compare(mDataMap.displayName)== 0) {
                entry.displayName = LdapSearchAttributeValues();
                //DebugLog( << "Search: Display Name " + entry.displayName);
            }
            
            if (!mDataMap.firstName.empty() && nextAttr.compare(mDataMap.firstName)== 0) {
                entry.firstName = LdapSearchAttributeValues();
                //DebugLog( << "Search: First Name " + entry.firstName);
            }
            
            if (!mDataMap.lastName.empty() && nextAttr.compare(mDataMap.lastName)== 0) {
                entry.lastName = LdapSearchAttributeValues();
                //DebugLog( << "Search: Last Name " + entry.lastName);
            }
            
            if (!mDataMap.mobilePhone.empty() && nextAttr.compare(mDataMap.mobilePhone)== 0) {
                entry.mobilePhone = LdapSearchAttributeValues();
                //DebugLog( << "Search: Mobile phone " + entry.mobilePhone);
            }
            
            if (!mDataMap.officePhone.empty() && nextAttr.compare(mDataMap.officePhone)== 0) {
                entry.officePhone = LdapSearchAttributeValues();
                //DebugLog( << "Search: Office phone " + entry.officePhone);
            }
            
            if (!mDataMap.email.empty() && nextAttr.compare(mDataMap.email)== 0) {
                entry.email = LdapSearchAttributeValues();
                //DebugLog( << "Search: email " + entry.email);
            }
            
            if (!mDataMap.workPhone.empty() && nextAttr.compare(mDataMap.workPhone)== 0) {
                entry.workPhone = LdapSearchAttributeValues();
                //DebugLog( << "Search: Work Phone " + entry.workPhone);
            }
            
            if (!mDataMap.homePhone.empty() && nextAttr.compare(mDataMap.homePhone)== 0) {
                entry.homePhone = LdapSearchAttributeValues();
                //DebugLog( << "Search: Home Phone " + entry.homePhone);
            }
            
            if (!mDataMap.softphone.empty() && nextAttr.compare(mDataMap.softphone)== 0) {
                entry.softphone = LdapSearchAttributeValues();
                //DebugLog( << "Search: Soft Phone " + entry.softphone);
            }
            
            if (!mDataMap.company.empty() && nextAttr.compare(mDataMap.company)== 0) {
                entry.company = LdapSearchAttributeValues();
                //DebugLog( << "Search: Company " + entry.company);
            }
            
            if (!mDataMap.department.empty() && nextAttr.compare(mDataMap.department)== 0) {
                entry.department = LdapSearchAttributeValues();
                //DebugLog( << "Search: Department " + entry.department);
            }
            
            if (!mDataMap.jabber.empty() && nextAttr.compare(mDataMap.jabber)== 0) {
                entry.jabber = LdapSearchAttributeValues();
                //DebugLog( << "Search: Jabber " + entry.jabber);
            }
            
            if (!mDataMap.jobTitle.empty() && nextAttr.compare(mDataMap.jobTitle)== 0) {
                entry.jobTitle = LdapSearchAttributeValues();
                //DebugLog( << "Search: Job Title " + entry.jobTitle);
            }
            
            if (!mDataMap.street.empty() && nextAttr.compare(mDataMap.street)== 0) {
                entry.street = LdapSearchAttributeValues();
                //DebugLog( << "Search: Street " + entry.street);
            }
            
            if (!mDataMap.city.empty() && nextAttr.compare(mDataMap.city)== 0) {
               entry.city = LdapSearchAttributeValues();
               //DebugLog( << "Search: City " + entry.city);
            }
            
            if (!mDataMap.state.empty() && nextAttr.compare(mDataMap.state)== 0) {
               entry.state = LdapSearchAttributeValues();
               //DebugLog( << "Search: State " + entry.state);
            }
            
            if (!mDataMap.zip.empty() && nextAttr.compare(mDataMap.zip)== 0) {
               entry.zip = LdapSearchAttributeValues();
               //DebugLog( << "Search: ZIP " + entry.zip);
            }
            
            if (!mDataMap.country.empty() && nextAttr.compare(mDataMap.country)== 0) {
               entry.country = LdapSearchAttributeValues();
               //DebugLog( << "Search: Country " + entry.country);
            }

          nextAttr = LdapSearchNextAttribute();
       }

       search_result_list->push_back(entry);

       validEntry = LdapSearchNextEntry();
    }
  }
}



  }//namespace OpenLdap
}//namespace CPCAPI2
#endif
