#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_DIALOG_EVENT_MODULE == 1)

#include "SipDialogEventPublicationManagerImpl.h"
#include "dialogevent/SipDialogEventPublicationHandler.h"

using namespace CPCAPI2::SipEvent;

namespace CPCAPI2
{
namespace SipDialogEvent
{

SipDialogEventPublicationManagerImpl::SipDialogEventPublicationManagerImpl(SipDialogEventPublicationManagerInterface* iff, CPCAPI2::SipEvent::SipEventPublicationManagerInterface* sipPublicationIf, std::shared_ptr<AccountMap> parentMap, CPCAPI2::SipAccount::SipAccountImpl& account) :
     mInterface(iff),
     mSipPublicationIf(sipPublicationIf),
     mParentMap(parentMap),
     mAccount(account)
{
   account.registerAccountAwareFeature(this);
}

SipDialogEventPublicationManagerImpl::~SipDialogEventPublicationManagerImpl()
{
   mAccount.unregisterAccountAwareFeature(this);
}

void SipDialogEventPublicationManagerImpl::release()
{
   if (std::shared_ptr<AccountMap> parentMap = mParentMap.lock())
   {
      parentMap->erase(mAccount.getHandle());
   }

   delete this;
}


int SipDialogEventPublicationManagerImpl::onPublicationSuccess(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationSuccessEvent& args)
{
   // Create the event
   DialogEventPublicationSuccessEvent dialogEventArgs;

   // Publish the event
   mInterface->fireEvent(cpcEvent(SipDialogEventPublicationHandler, onPublicationSuccess), mAccount.getHandle(), publication, dialogEventArgs);

   return kSuccess;
}

int SipDialogEventPublicationManagerImpl::onPublicationFailure(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationFailureEvent& args)
{
   // Create the event
   DialogEventPublicationFailureEvent dialogEventArgs;

   // Publish the event
   mInterface->fireEvent(cpcEvent(SipDialogEventPublicationHandler, onPublicationFailure), mAccount.getHandle(), publication, dialogEventArgs);

   return kSuccess;
}

int SipDialogEventPublicationManagerImpl::onPublicationRemove(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationRemoveEvent & args)
{
   // Create the event
   DialogEventPublicationRemoveEvent dialogEventArgs;

   // Publish the event
   mInterface->fireEvent(cpcEvent(SipDialogEventPublicationHandler, onPublicationRemove), mAccount.getHandle(), publication, dialogEventArgs);

   return kSuccess;
}

int SipDialogEventPublicationManagerImpl::onError(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationErrorEvent& args)
{
   // Create the event
   DialogEventPublicationErrorEvent dialogEventArgs;

   // Publish the event
   mInterface->fireEvent(cpcEvent(SipDialogEventPublicationHandler, onError), mAccount.getHandle(), publication, dialogEventArgs);

   return kSuccess;
}

}
}

#endif // CPCAPI2_BRAND_SIP_DIALOG_EVENT_MODULE
