#include "../datatypes/SipConversationSetDefaultSettings.h"
#include "../datatypes/SipConversationSettings.h"
#include "../datatypes/SipConversationCreateConversation.h"
#include "../datatypes/SipConversationAddParticipant.h"
#include "../datatypes/SipConversationConfigureMedia.h"
#include "../datatypes/MediaInfo.h"
#include "../datatypes/SipConversationSetMediaEnabled.h"
#include "../datatypes/MediaType.h"
#include "../datatypes/SipConversationSetAnonymousMode.h"
#include "../datatypes/SipConversationStart.h"
#include "../datatypes/SipConversationHold.h"
#include "../datatypes/SipConversationUnhold.h"
#include "../datatypes/SipConversationSendMediaChangeRequest.h"
#include "../datatypes/SipConversationEnd.h"
#include "../datatypes/SipConversationRedirect.h"
#include "../datatypes/SipConversationSendRingingResponse.h"
#include "../datatypes/SipConversationReject.h"
#include "../datatypes/SipConversationAccept.h"
#include "../datatypes/SipConversationAcceptIncomingTransferRequest.h"
#include "../datatypes/SipConversationRejectIncomingTransferRequest.h"
#include "../datatypes/SipConversationTransfer.h"
#include "../datatypes/SipConversationTransferWithFlag.h"
#include "../datatypes/SipConversationTransferToAddress.h"
#include "../datatypes/SipConversationSetDtmfMode.h"
#include "../datatypes/DtmfMode.h"
#include "../datatypes/SipConversationStartDtmfTone.h"
#include "../datatypes/SipConversationStopDtmfTone.h"
#include "../datatypes/SipConversationPlaySound.h"
#include "../datatypes/SipConversationStopPlaySound.h"
#include "../datatypes/SipConversationGetCallCount.h"
#include "../datatypes/SipConversationSetCallKitMode.h"
#include "../datatypes/SipConversationSetTelecomFrameworkMode.h"

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
void SipConversation::setDefaultSettings(int64_t phoneHandle, int64_t accountHandle, SipConversationSettings settings)
{
    SipConversationSetDefaultSettings params = new SipConversationSetDefaultSettings(phoneHandle, accountHandle, settings);
    nlohmann::json j = this.transport.request("setDefaultSettings", params.marshal());
    
};

int64_t SipConversation::createConversation(int64_t phoneHandle, int64_t accountHandle)
{
    SipConversationCreateConversation params = new SipConversationCreateConversation(phoneHandle, accountHandle);
    nlohmann::json j = this.transport.request("createConversation", params.marshal());
    return j.get<int64_t>();
};

void SipConversation::addParticipant(int64_t phoneHandle, int64_t conversationHandle, cpc::string targetAddress)
{
    SipConversationAddParticipant params = new SipConversationAddParticipant(phoneHandle, conversationHandle, targetAddress);
    nlohmann::json j = this.transport.request("addParticipant", params.marshal());
    
};

void SipConversation::configureMedia(int64_t phoneHandle, int64_t conversationHandle, MediaInfo mediaDescriptor)
{
    SipConversationConfigureMedia params = new SipConversationConfigureMedia(phoneHandle, conversationHandle, mediaDescriptor);
    nlohmann::json j = this.transport.request("configureMedia", params.marshal());
    
};

void SipConversation::setMediaEnabled(int64_t phoneHandle, int64_t conversationHandle, MediaType mediaType, bool enabled)
{
    SipConversationSetMediaEnabled params = new SipConversationSetMediaEnabled(phoneHandle, conversationHandle, mediaType, enabled);
    nlohmann::json j = this.transport.request("setMediaEnabled", params.marshal());
    
};

void SipConversation::setAnonymousMode(int64_t phoneHandle, int64_t conversationHandle, int64_t anonymousMode)
{
    SipConversationSetAnonymousMode params = new SipConversationSetAnonymousMode(phoneHandle, conversationHandle, anonymousMode);
    nlohmann::json j = this.transport.request("setAnonymousMode", params.marshal());
    
};

void SipConversation::start(int64_t phoneHandle, int64_t conversationHandle)
{
    SipConversationStart params = new SipConversationStart(phoneHandle, conversationHandle);
    nlohmann::json j = this.transport.request("start", params.marshal());
    
};

void SipConversation::hold(int64_t phoneHandle, int64_t conversationHandle)
{
    SipConversationHold params = new SipConversationHold(phoneHandle, conversationHandle);
    nlohmann::json j = this.transport.request("hold", params.marshal());
    
};

void SipConversation::unhold(int64_t phoneHandle, int64_t conversationHandle)
{
    SipConversationUnhold params = new SipConversationUnhold(phoneHandle, conversationHandle);
    nlohmann::json j = this.transport.request("unhold", params.marshal());
    
};

void SipConversation::sendMediaChangeRequest(int64_t phoneHandle, int64_t conversationHandle)
{
    SipConversationSendMediaChangeRequest params = new SipConversationSendMediaChangeRequest(phoneHandle, conversationHandle);
    nlohmann::json j = this.transport.request("sendMediaChangeRequest", params.marshal());
    
};

void SipConversation::end(int64_t phoneHandle, int64_t conversationHandle)
{
    SipConversationEnd params = new SipConversationEnd(phoneHandle, conversationHandle);
    nlohmann::json j = this.transport.request("end", params.marshal());
    
};

void SipConversation::redirect(int64_t phoneHandle, int64_t conversationHandle, cpc::string targetAddress, cpc::string reason)
{
    SipConversationRedirect params = new SipConversationRedirect(phoneHandle, conversationHandle, targetAddress, reason);
    nlohmann::json j = this.transport.request("redirect", params.marshal());
    
};

void SipConversation::sendRingingResponse(int64_t phoneHandle, int64_t conversationHandle)
{
    SipConversationSendRingingResponse params = new SipConversationSendRingingResponse(phoneHandle, conversationHandle);
    nlohmann::json j = this.transport.request("sendRingingResponse", params.marshal());
    
};

void SipConversation::reject(int64_t phoneHandle, int64_t conversationHandle, int64_t rejectReason)
{
    SipConversationReject params = new SipConversationReject(phoneHandle, conversationHandle, rejectReason);
    nlohmann::json j = this.transport.request("reject", params.marshal());
    
};

void SipConversation::accept(int64_t phoneHandle, int64_t conversationHandle)
{
    SipConversationAccept params = new SipConversationAccept(phoneHandle, conversationHandle);
    nlohmann::json j = this.transport.request("accept", params.marshal());
    
};

void SipConversation::acceptIncomingTransferRequest(int64_t phoneHandle, int64_t conversationHandle)
{
    SipConversationAcceptIncomingTransferRequest params = new SipConversationAcceptIncomingTransferRequest(phoneHandle, conversationHandle);
    nlohmann::json j = this.transport.request("acceptIncomingTransferRequest", params.marshal());
    
};

void SipConversation::rejectIncomingTransferRequest(int64_t phoneHandle, int64_t conversationHandle)
{
    SipConversationRejectIncomingTransferRequest params = new SipConversationRejectIncomingTransferRequest(phoneHandle, conversationHandle);
    nlohmann::json j = this.transport.request("rejectIncomingTransferRequest", params.marshal());
    
};

void SipConversation::transfer(int64_t phoneHandle, int64_t transferTargetConversation, int64_t transfereeConversation)
{
    SipConversationTransfer params = new SipConversationTransfer(phoneHandle, transferTargetConversation, transfereeConversation);
    nlohmann::json j = this.transport.request("transfer", params.marshal());
    
};

void SipConversation::transferWithFlag(int64_t phoneHandle, int64_t transferTargetConversation, int64_t transfereeConversation, bool endTargetConversationOnSuccess)
{
    SipConversationTransferWithFlag params = new SipConversationTransferWithFlag(phoneHandle, transferTargetConversation, transfereeConversation, endTargetConversationOnSuccess);
    nlohmann::json j = this.transport.request("transferWithFlag", params.marshal());
    
};

void SipConversation::transferToAddress(int64_t phoneHandle, int64_t transfereeConversation, cpc::string targetAddress)
{
    SipConversationTransferToAddress params = new SipConversationTransferToAddress(phoneHandle, transfereeConversation, targetAddress);
    nlohmann::json j = this.transport.request("transferToAddress", params.marshal());
    
};

void SipConversation::setDtmfMode(int64_t phoneHandle, int64_t account, int64_t ordinal, DtmfMode dtmfMode)
{
    SipConversationSetDtmfMode params = new SipConversationSetDtmfMode(phoneHandle, account, ordinal, dtmfMode);
    nlohmann::json j = this.transport.request("setDtmfMode", params.marshal());
    
};

void SipConversation::startDtmfTone(int64_t phoneHandle, int64_t conversationHandle, int64_t toneId, bool playLocally)
{
    SipConversationStartDtmfTone params = new SipConversationStartDtmfTone(phoneHandle, conversationHandle, toneId, playLocally);
    nlohmann::json j = this.transport.request("startDtmfTone", params.marshal());
    
};

void SipConversation::stopDtmfTone(int64_t phoneHandle)
{
    SipConversationStopDtmfTone params = new SipConversationStopDtmfTone(phoneHandle);
    nlohmann::json j = this.transport.request("stopDtmfTone", params.marshal());
    
};

void SipConversation::playSound(int64_t phoneHandle, int64_t conversationHandle, cpc::string resourceUri, bool repeat)
{
    SipConversationPlaySound params = new SipConversationPlaySound(phoneHandle, conversationHandle, resourceUri, repeat);
    nlohmann::json j = this.transport.request("playSound", params.marshal());
    
};

void SipConversation::stopPlaySound(int64_t phoneHandle, int64_t conversationHandle)
{
    SipConversationStopPlaySound params = new SipConversationStopPlaySound(phoneHandle, conversationHandle);
    nlohmann::json j = this.transport.request("stopPlaySound", params.marshal());
    
};

int64_t SipConversation::getCallCount(int64_t phoneHandle)
{
    SipConversationGetCallCount params = new SipConversationGetCallCount(phoneHandle);
    nlohmann::json j = this.transport.request("getCallCount", params.marshal());
    return j.get<int64_t>();
};

void SipConversation::setCallKitMode(int64_t phoneHandle, int64_t conversationHandle)
{
    SipConversationSetCallKitMode params = new SipConversationSetCallKitMode(phoneHandle, conversationHandle);
    nlohmann::json j = this.transport.request("setCallKitMode", params.marshal());
    
};

void SipConversation::setTelecomFrameworkMode(int64_t phoneHandle, int64_t conversationHandle)
{
    SipConversationSetTelecomFrameworkMode params = new SipConversationSetTelecomFrameworkMode(phoneHandle, conversationHandle);
    nlohmann::json j = this.transport.request("setTelecomFrameworkMode", params.marshal());
    
};
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation