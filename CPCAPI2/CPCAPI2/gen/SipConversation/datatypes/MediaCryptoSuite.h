#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_MEDIACRYPTOSUITE_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_MEDIACRYPTOSUITE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  enum class MediaCryptoSuite
  {
    None,
    AesCm_128HmacSha1_32,
    AesCm_128HmacSha1_80,
    AesCm_256HmacSha1_32,
    AesCm_256HmacSha1_80,
    AesCm_192HmacSha1_32,
    AesCm_192HmacSha1_80,
    AeadAes_128Gcm,
    AeadAes_256Gcm
  };

  inline std::string to_string(MediaCryptoSuite value)
  {
    switch (value)
    {
      case MediaCryptoSuite::None:
        return "None";
      case MediaCryptoSuite::AesCm_128HmacSha1_32:
        return "AES_CM_128_HMAC_SHA1_32";
      case MediaCryptoSuite::AesCm_128HmacSha1_80:
        return "AES_CM_128_HMAC_SHA1_80";
      case MediaCryptoSuite::AesCm_256HmacSha1_32:
        return "AES_CM_256_HMAC_SHA1_32";
      case MediaCryptoSuite::AesCm_256HmacSha1_80:
        return "AES_CM_256_HMAC_SHA1_80";
      case MediaCryptoSuite::AesCm_192HmacSha1_32:
        return "AES_CM_192_HMAC_SHA1_32";
      case MediaCryptoSuite::AesCm_192HmacSha1_80:
        return "AES_CM_192_HMAC_SHA1_80";
      case MediaCryptoSuite::AeadAes_128Gcm:
        return "AEAD_AES_128_GCM";
      case MediaCryptoSuite::AeadAes_256Gcm:
        return "AEAD_AES_256_GCM";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, MediaCryptoSuite value)
  {
    switch (value)
    {
      case MediaCryptoSuite::None:
        os << "None";
        break;
      case MediaCryptoSuite::AesCm_128HmacSha1_32:
        os << "AES_CM_128_HMAC_SHA1_32";
        break;
      case MediaCryptoSuite::AesCm_128HmacSha1_80:
        os << "AES_CM_128_HMAC_SHA1_80";
        break;
      case MediaCryptoSuite::AesCm_256HmacSha1_32:
        os << "AES_CM_256_HMAC_SHA1_32";
        break;
      case MediaCryptoSuite::AesCm_256HmacSha1_80:
        os << "AES_CM_256_HMAC_SHA1_80";
        break;
      case MediaCryptoSuite::AesCm_192HmacSha1_32:
        os << "AES_CM_192_HMAC_SHA1_32";
        break;
      case MediaCryptoSuite::AesCm_192HmacSha1_80:
        os << "AES_CM_192_HMAC_SHA1_80";
        break;
      case MediaCryptoSuite::AeadAes_128Gcm:
        os << "AEAD_AES_128_GCM";
        break;
      case MediaCryptoSuite::AeadAes_256Gcm:
        os << "AEAD_AES_256_GCM";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const MediaCryptoSuite& e)
  {
    switch (e)
    {
      case MediaCryptoSuite::None:
        j = "None";
        break;
      case MediaCryptoSuite::AesCm_128HmacSha1_32:
        j = "AES_CM_128_HMAC_SHA1_32";
        break;
      case MediaCryptoSuite::AesCm_128HmacSha1_80:
        j = "AES_CM_128_HMAC_SHA1_80";
        break;
      case MediaCryptoSuite::AesCm_256HmacSha1_32:
        j = "AES_CM_256_HMAC_SHA1_32";
        break;
      case MediaCryptoSuite::AesCm_256HmacSha1_80:
        j = "AES_CM_256_HMAC_SHA1_80";
        break;
      case MediaCryptoSuite::AesCm_192HmacSha1_32:
        j = "AES_CM_192_HMAC_SHA1_32";
        break;
      case MediaCryptoSuite::AesCm_192HmacSha1_80:
        j = "AES_CM_192_HMAC_SHA1_80";
        break;
      case MediaCryptoSuite::AeadAes_128Gcm:
        j = "AEAD_AES_128_GCM";
        break;
      case MediaCryptoSuite::AeadAes_256Gcm:
        j = "AEAD_AES_256_GCM";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, MediaCryptoSuite& e)
  {
      if (0 == j.get<std::string>().compare("None"))
    {
      e = MediaCryptoSuite::None;
    }
    else if (0 == j.get<std::string>().compare("AES_CM_128_HMAC_SHA1_32"))
    {
      e = MediaCryptoSuite::AesCm_128HmacSha1_32;
    }
    else if (0 == j.get<std::string>().compare("AES_CM_128_HMAC_SHA1_80"))
    {
      e = MediaCryptoSuite::AesCm_128HmacSha1_80;
    }
    else if (0 == j.get<std::string>().compare("AES_CM_256_HMAC_SHA1_32"))
    {
      e = MediaCryptoSuite::AesCm_256HmacSha1_32;
    }
    else if (0 == j.get<std::string>().compare("AES_CM_256_HMAC_SHA1_80"))
    {
      e = MediaCryptoSuite::AesCm_256HmacSha1_80;
    }
    else if (0 == j.get<std::string>().compare("AES_CM_192_HMAC_SHA1_32"))
    {
      e = MediaCryptoSuite::AesCm_192HmacSha1_32;
    }
    else if (0 == j.get<std::string>().compare("AES_CM_192_HMAC_SHA1_80"))
    {
      e = MediaCryptoSuite::AesCm_192HmacSha1_80;
    }
    else if (0 == j.get<std::string>().compare("AEAD_AES_128_GCM"))
    {
      e = MediaCryptoSuite::AeadAes_128Gcm;
    }
    else if (0 == j.get<std::string>().compare("AEAD_AES_256_GCM"))
    {
      e = MediaCryptoSuite::AeadAes_256Gcm;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum MediaCryptoSuite"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_MEDIACRYPTOSUITE_H
