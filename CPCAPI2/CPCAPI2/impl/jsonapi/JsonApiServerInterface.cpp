#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "cpcapi2utils.h"
#include "JsonApiServerInterface.h"
#include "JsonApiServerSendTransportInternal.h"
#include "JsonApiServer_WebSocket.h"
#include "JsonApiServer_HTTP.h"
#include "json/JsonHelper.h"
#include "auth_server/AuthServerJwtUtils.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"
#include "log/LocalLogger.h"

#include <rutil/Random.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

using namespace resip;

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL
#define CP_LOCAL_LOGGER_VAR mLocalLogger
#define JSON_SERVER_LOGOUT_TIMEOUT_MSECS 10000

namespace CPCAPI2
{

namespace JsonApi
{

JsonApiUserHandle JsonApiUserHandleFactory::sNextHandle = 0;

JsonApiUserHandle JsonApiUserHandleFactory::getNext()
{
   if (sNextHandle == 0)
   {
      // this is used as input to a pairing function executed on the client side
      // to generate unique handles for the client;
      // we init to random to decrease the chance of handle collisions when
      // (e.g.) the server is restarted
      sNextHandle = (JsonApiUserHandle)resip::Random::getCryptoRandom();
   }
   return sNextHandle++;
}

JsonApiServerInterface::JsonApiServerInterface(Phone* phone) :
EventSource<JsonApiUserHandle, JsonApiServerHandler, JsonApiServerSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
mPhone(dynamic_cast<PhoneInterface*>(phone)),
mInMemUser(-1),
mInMemUserContext(NULL),
mWebsocketSendTransport(NULL),
mLocalLogger(static_cast<PhoneInterface*>(phone)->localLogger())
{
   mWebsocketTransport.reset(new JsonApiServerWebSocket(mPhone));
   mWebsocketSendTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);

   mHttpTransport.reset(new JsonApiServerHTTP(mPhone));

   mFunctionMap["login"] = std::bind(&JsonApiServerInterface::handleLogin, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["logout"] = std::bind(&JsonApiServerInterface::handleLogout, this, std::placeholders::_1, std::placeholders::_2);
   LocalMaxLog("JsonApiServerInterface::JsonApiServerInterface(): {} phone: {}", static_cast<void*>(this), static_cast<void*>(mPhone));
}

JsonApiServerInterface::~JsonApiServerInterface()
{
   interruptProcess();
   LocalMaxLog("JsonApiServerInterface::~JsonApiServerInterface(): {} phone: {}", static_cast<void*>(this), static_cast<void*>(mPhone));
}

void JsonApiServerInterface::Release()
{
   LocalMaxLog("JsonApiServerInterface::Release(): {} phone: {}", static_cast<void*>(this), static_cast<void*>(mPhone));
   delete this;
}

void JsonApiServerInterface::addEventObserver(JsonApiServerHandler* handler)
{
   addSdkObserver(handler);
}

JsonApiServerInternal* JsonApiServerInternal::getInternalInterface(CPCAPI2::Phone* cpcPhone)
{
   return static_cast<JsonApiServerInternal*>(JsonApiServer::getInterface(cpcPhone));
}

int JsonApiServerInterface::start(const JsonApiServerConfig& serverConfig)
{
   postToSdkThread(resip::resip_bind(&JsonApiServerInterface::startImpl, this, serverConfig));
   return kSuccess;
}

int JsonApiServerInterface::startImpl(const JsonApiServerConfig& serverConfig)
{
   mConfig = serverConfig;
   LocalDebugLog("JsonApiServerInterface::startImpl(): phone: {} websocketPort: {} httpPort: {}",
                 static_cast<void*>(mPhone), mConfig.websocketPort, mConfig.httpPort);
   
   assert(mConfig.websocketPort >= 0 || mConfig.httpPort >= 0 || mConfig.outgoingJsonHandler != NULL);

   if (mConfig.websocketPort >= 0)
   {
      mWebsocketTransport->startServer(serverConfig, mOnPrivilegedAccessCompleted);
   }
   if (mConfig.httpPort >= 0)
   {
      mHttpTransport->startServer(serverConfig, mOnPrivilegedAccessCompleted);
   }
   return kSuccess;
}

int JsonApiServerInterface::shutdown()
{
   mPhone->getSdkModuleThread().execute(resip::resip_bind(&JsonApiServerInterface::shutdownImpl, this));
   return kSuccess;
}

int JsonApiServerInterface::shutdownImpl()
{
   LocalDebugLog("JsonApiServerInterface::shutdownImpl(): phone: {}", static_cast<void*>(mPhone));
   if (mConfig.websocketPort >= 0)
   {
      mWebsocketTransport->stopServer();
      MapUserToWsSession::iterator it = mMapUserToWsSession.begin();
      for (; it != mMapUserToWsSession.end(); ++it)
      {
         LocalMaxLog("JsonApiServerInterface::shutdownImpl(): closing connection for JsonApiUserHandle: {}", it->first);
         it->second->close();
      }
      mWebsocketTransport->waitForShutdown();
   }

   if (mConfig.httpPort >= 0)
   {
      mHttpTransport->stopServer();
   }

   return kSuccess;
}

int JsonApiServerInterface::setJsonApiUserContext(JsonApiUserHandle jsonApiUser, CPCAPI2::Phone* sdkContext, const cpc::vector<cpc::string>& sdkModules)
{
   postToSdkThread(resip::resip_bind(&JsonApiServerInterface::setJsonApiUserContextImpl, this, jsonApiUser, sdkContext, sdkModules));
   return kSuccess;
}

std::set<std::string> toStdSet(const cpc::vector<cpc::string>& cpcVec)
{
   std::set<std::string> ret;
   cpc::vector<cpc::string>::const_iterator it = cpcVec.begin();
   for (; it != cpcVec.end(); ++it)
   {
      ret.insert(it->c_str());
   }
   return ret;
}

int JsonApiServerInterface::setJsonApiUserContextImpl(JsonApiUserHandle jsonApiUser, CPCAPI2::Phone* sdkContext, const cpc::vector<cpc::string>& sdkModules)
{
   if (sdkContext)
   {
      if (jsonApiUser == mInMemUser)
      {
         mInMemUserContext = new JsonApiUserContext();
         mInMemUserContext->phone = sdkContext;
         mInMemUserContext->permissions = toStdSet(sdkModules);
         mInMemUserContext->handle = jsonApiUser;
      }
      else
      {
         MapUserToWsSession::iterator it = mMapUserToWsSession.find(jsonApiUser);
         if (it != mMapUserToWsSession.end())
         {
            LocalDebugLog("setJsonApiUserContextImpl - websocket: jsonApiUser {}", jsonApiUser);
            JsonApiUserContext* jsonUserContext = new JsonApiUserContext();
            jsonUserContext->phone = sdkContext;
            jsonUserContext->permissions = toStdSet(sdkModules);
            jsonUserContext->handle = jsonApiUser;
            it->second->setSdkContext(jsonUserContext);
            JsonApiServerSendTransportInternal* jsonSendTransport = dynamic_cast<JsonApiServerSendTransportInternal*>(JsonApiServerSendTransport::getInterface(sdkContext));
            JsonApiRequestInfo conn(it->second->websocket_conn_hdl());
            jsonSendTransport->setConnection(conn);
         }
         else
         {
            LocalDebugLog("setJsonApiUserContextImpl - http or https: jsonApiUser {}", jsonApiUser);
            MapUserToReq::iterator itReq = mMapUserToReq.find(jsonApiUser);
            if (itReq != mMapUserToReq.end())
            {
               JsonApiUserContext* jsonUserContext = new JsonApiUserContext();
               jsonUserContext->phone = sdkContext;
               jsonUserContext->permissions = toStdSet(sdkModules);
               jsonUserContext->handle = jsonApiUser;
               mHttpTransport->handleRequestInContext(itReq->second, jsonUserContext);
               mMapUserToReq.erase(itReq);
            }
            else
            {
               LocalDebugLog("setJsonApiUserContextImpl - could not find jsonApiUser {}", jsonApiUser);
            }
         }
      }
   }
   else
   {
      LocalDebugLog("setJsonApiUserContextImpl - sdk context not initialized for jsonApiUser {}", jsonApiUser);
   }

   // createTestAccount(sdkContext);
   return kSuccess;
}

int JsonApiServerInterface::sendLoginResult(JsonApiUserHandle jsonApiUser, LoginResultEvent loginResult)
{
   postToSdkThread(resip::resip_bind(&JsonApiServerInterface::sendLoginResultImpl, this, jsonApiUser, loginResult));
   return kSuccess;
}

int JsonApiServerInterface::sendLoginResultImpl(JsonApiUserHandle jsonApiUser, LoginResultEvent loginResult)
{
   InfoLog(<< "JsonApiServerInterface::sendLoginResultImpl(): jsonApiUser=" << jsonApiUser << "; result=" << loginResult.success);
   LocalDebugLog("json api user: {}", jsonApiUser);

   rapidjson::Document eventJson;
   eventJson.SetObject();
   
   eventJson.AddMember("moduleId", "JsonApiClient", eventJson.GetAllocator());

   rapidjson::Value functionObj(rapidjson::kObjectType);
   functionObj.AddMember("functionName", "onLoginResult", eventJson.GetAllocator());
   functionObj.AddMember("success", loginResult.success, eventJson.GetAllocator());
   functionObj.AddMember("serverGeneratedHandle", jsonApiUser, eventJson.GetAllocator());
   functionObj.AddMember("jsonApiUser", jsonApiUser, eventJson.GetAllocator());
   functionObj.AddMember("jsonApiVersion", mConfig.jsonApiVersion, eventJson.GetAllocator());
   eventJson.AddMember("functionObject", functionObj, eventJson.GetAllocator());

   if (jsonApiUser == mInMemUser)
   {
      std::string stdjsonstr;
      Json::StdStringBuffer jsonbuffer(stdjsonstr);
      Json::StdStringWriter jsonwriter(jsonbuffer);
      eventJson.Accept(jsonwriter);
      cpc::string cpcjsonstr(stdjsonstr.c_str(), stdjsonstr.size());
      mConfig.outgoingJsonHandler->onJson(cpcjsonstr);
   }

   MapUserToWsSession::iterator it = mMapUserToWsSession.find(jsonApiUser);
   if (it != mMapUserToWsSession.end())
   {
      DebugLog(<< "JsonApiServerInterface::sendLoginResultImpl(): sending login result: jsonApiUser=" << jsonApiUser << "; result=" << loginResult.success);
      mWebsocketTransport->send([&eventJson](std::string& buff) {
            Json::StdStringBuffer jsonbuffer(buff);
            Json::StdStringWriter jsonwriter(jsonbuffer);
            if (!eventJson.Accept(jsonwriter)) {
               WarningLog(<< "JsonApiServerInterface::sendLoginResultImpl(): rapidjson::Document::Accept(..) failed");
            }
         }, 
         1024, it->second->websocket_conn_hdl()
      );
   }
   else
   {
      MapUserToReq::iterator itReq = mMapUserToReq.find(jsonApiUser);
      // No response is required for the login request over the http transport, only flag the warning otherwise
      if (!((itReq != mMapUserToReq.end()) && (itReq->second == loginResult.requestId)))
      {
         LocalWarningLog("JsonApiServerInterface::sendLoginResultImpl(): Couldn't find connection for user handle: {} to send login result for request: {}",
                         jsonApiUser, loginResult.requestId);
      }
   }

   return kSuccess;
}

int JsonApiServerInterface::sendLogoutResult(JsonApiUserHandle jsonApiUser, LogoutResultEvent logoutResult)
{
   postToSdkThread(resip::resip_bind(&JsonApiServerInterface::sendLogoutResultImpl, this, jsonApiUser, logoutResult));
   return kSuccess;
}

int JsonApiServerInterface::sendLogoutResultImpl(JsonApiUserHandle jsonApiUser, LogoutResultEvent logoutResult)
{
   LocalDebugLog("JsonApiServerInterface::sendLogoutResultImpl(): json api user: {}", jsonApiUser);

   rapidjson::Document eventJson;
   eventJson.SetObject();

   eventJson.AddMember("moduleId", "JsonApiClient", eventJson.GetAllocator());

   rapidjson::Value functionObj(rapidjson::kObjectType);
   functionObj.AddMember("functionName", "onLogoutResult", eventJson.GetAllocator());
   functionObj.AddMember("success", logoutResult.success, eventJson.GetAllocator());
   functionObj.AddMember("serverGeneratedHandle", jsonApiUser, eventJson.GetAllocator());
   functionObj.AddMember("jsonApiUser", jsonApiUser, eventJson.GetAllocator());
   functionObj.AddMember("jsonApiVersion", mConfig.jsonApiVersion, eventJson.GetAllocator());
   eventJson.AddMember("functionObject", functionObj, eventJson.GetAllocator());

   MapUserToWsSession::iterator i = mMapUserToWsSession.find(jsonApiUser);
   if (i != mMapUserToWsSession.end())
   {
      mWebsocketTransport->send([&eventJson](std::string& buff) {
            Json::StdStringBuffer jsonbuffer(buff);
            Json::StdStringWriter jsonwriter(jsonbuffer);
#ifndef __clang_analyzer__ // warning inside rapidjson: https://github.com/Tencent/rapidjson/issues/1812
            eventJson.Accept(jsonwriter);
#endif
         },
         1024, i->second->websocket_conn_hdl()
      );
   }
   else
   {
      MapUserToReq::iterator j = mMapUserToReq.find(jsonApiUser);
      // No response is required for the logout request over the http transport, only flag the warning otherwise
      if (!((j != mMapUserToReq.end()) && (j->second == logoutResult.requestId)))
      {
         LocalWarningLog("JsonApiServerInterface::sendLogoutResultImpl(): Couldn't find connection for user handle: {} to send logout result for request: {}", jsonApiUser, logoutResult.requestId);
      }
   }

   return kSuccess;
}

int JsonApiServerInterface::proceedWithLogout(JsonApiUserHandle jsonApiUser, LogoutProceedEvent proceedEvent)
{
   postToSdkThread(resip::resip_bind(&JsonApiServerInterface::proceedWithLogoutImpl, this, jsonApiUser, proceedEvent));
   return kSuccess;
}

int JsonApiServerInterface::proceedWithLogoutImpl(JsonApiUserHandle jsonApiUser, LogoutProceedEvent proceedEvent)
{
   LocalDebugLog("JsonApiServerInterface::proceedWithLogoutImpl(): json api user: {} observer: {}", jsonApiUser, static_cast<void*>(proceedEvent.observer));

   observerhandlermap_iterator loggedOutIter = mLoggedOutUserToHandlerMap.find(jsonApiUser);
   if (loggedOutIter == mLoggedOutUserToHandlerMap.end())
   {
      LocalDebugLog("JsonApiServerInterface::proceedWithLogoutImpl(): json api user: {} not found in logout list", jsonApiUser);
      // TODO: should we ignore it rather than sending a logout result
      CPCAPI2::JsonApi::LogoutResultEvent logoutResult;
      logoutResult.success = false;
      sendLogoutResultImpl(jsonApiUser, logoutResult);
      return 0;
   }

   // Need to ensure that all the handlers have had an opportunity to prep for logout
   loggedOutIter->second.erase(proceedEvent.observer);
   if (loggedOutIter->second.size() == 0)
   {
      LocalDebugLog("JsonApiServerInterface::proceedWithLogoutImpl(): json api user: {} has no additional observers, proceeding with logout", jsonApiUser);
      mLoggedOutUserToHandlerMap.erase(jsonApiUser);

      MapUserToWsSession::iterator i = mMapUserToWsSession.find(jsonApiUser);
      if (i != mMapUserToWsSession.end())
      {
         JsonApiWebSocketSession* session = i->second;
         LogoutEvent args;
         args.authToken = cpc::string(session->auth_token().authToken.c_str(), session->auth_token().authToken.size());;
         args.userIdentity = cpc::string(session->auth_token().cp_user.c_str(), session->auth_token().cp_user.size());

         LocalMaxLog("JsonApiServerInterface::proceedWithLogoutImpl(): removing user: {} from user to web socket mapping, userIdentity: {}", jsonApiUser, args.userIdentity);
         session->logout();

         fireEvent(cpcFunc(JsonApiServerHandler::onLogout), 0, jsonApiUser, args);
         // The final logout result event to the remote client will be triggered by the json server handler (e.g. SdkManager)
      }
      else
      {
         LocalDebugLog("JsonApiServerInterface::proceedWithLogoutImpl(): invalid json api user: {}", jsonApiUser);

         CPCAPI2::JsonApi::LogoutResultEvent logoutResult;
         logoutResult.success = false;
         sendLogoutResultImpl(jsonApiUser, logoutResult);
      }
   }
   else
   {
      LocalDebugLog("JsonApiServerInterface::proceedWithLogoutImpl(): json api user: {} still has additional observers, holding off on proceeding with logout", jsonApiUser);
      return 0;
   }

   return kSuccess;
}

int JsonApiServerInterface::destroyUser(JsonApiUserHandle jsonApiUser)
{
   postToSdkThread(resip::resip_bind(&JsonApiServerInterface::destroyUserImpl, this, jsonApiUser));
   return kSuccess;
}

int JsonApiServerInterface::destroyUserImpl(JsonApiUserHandle jsonApiUser)
{
   LocalDebugLog("JsonApiServerInterface::destroyUserImpl(): JsonApiUserHandle {}", jsonApiUser);

   MapUserToWsSession::const_iterator it = mMapUserToWsSession.find(jsonApiUser);
   if (it != mMapUserToWsSession.end())
   {
      LocalMaxLog("JsonApiServerInterface::destroyUserImpl(): removing from user to web socket mapping JsonApiUserHandle {}", jsonApiUser);
      ConnectionClosedEvent args;
      args.authToken = it->second->auth_token().authToken;
      args.userIdentity = it->second->auth_token().cp_user;
      mLoggedOutUserToHandlerMap.erase(jsonApiUser);

      it->second->close();
      mMapUserToWsSession.erase(it); // prevents this user from getting added to mMapUserToDisconnectTime

      fireEvent(cpcFunc(JsonApiServerHandler::onConnectionClosed), 0, jsonApiUser, args);
   }

   return kSuccess;
}

int JsonApiServerInterface::addRequestHandler(const cpc::string& path, HttpServerRequestHandler* handler)
{
   SimpleWeb::CaseInsensitiveMultimap respHeaders;
   respHeaders.emplace("Access-Control-Allow-Origin", "*");
   respHeaders.emplace("Access-Control-Allow-Headers", "Authorization");
   respHeaders.emplace("Cache-Control", "no-cache, no-store, must-revalidate");

   if (mHttpTransport->useHttps())
   {
      auto https_handler = [&, handler, respHeaders](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>& request) {
         try {
            AuthTokenInfo authTokenInfo;
            if (handler->requiresAuth())
            {
               resip::Data authTokenStr;
               JsonApiServerHTTP::getAuthTokenFromRequest<SimpleWeb::HTTPS>(request, authTokenStr);

               resip::Data userIdentity, deviceId;
               std::map<resip::Data, resip::Data> publicClaims;
               std::vector<resip::Data> requestedResources;
               if (validateAuthToken(authTokenStr, userIdentity, deviceId, publicClaims, requestedResources) == 0)
               {
                  authTokenInfo.isValid = true;
                  authTokenInfo.userIdentity = userIdentity.c_str();
                  authTokenInfo.deviceId = deviceId.c_str();
                  for (const resip::Data& rr : requestedResources)
                  {
                     authTokenInfo.requestedResources.push_back(rr.c_str());
                  }
               }
            }

            cpc::vector<QueryStringParam> queryStringParams;
            SimpleWeb::CaseInsensitiveMultimap queryString = request->parse_query_string();
            auto itQs = queryString.begin();
            for (; itQs != queryString.end(); ++itQs)
            {
               QueryStringParam qsp;
               qsp.name = itQs->first.c_str();
               qsp.value = itQs->second.c_str();
               queryStringParams.push_back(qsp);
            }

            cpc::string requestContent(request->content.string().c_str());
            handler->processRequest(request->path.c_str(), requestContent, authTokenInfo, queryStringParams);
            SimpleWeb::StatusCode respCode = (SimpleWeb::StatusCode)handler->response_code();
            response->write(respCode, handler ? handler->output().c_str() : "invalid request handler", respHeaders);
         }
         catch (const std::exception &e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n"
               << e.what();
         }
      };

      mHttpTransport->addHttpsResourceHandler("POST", path.c_str(), https_handler);
      mHttpTransport->addHttpsResourceHandler("GET", path.c_str(), https_handler);
   }
   else
   {
      auto http_handler = [&, handler, respHeaders](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Request>& request) {
         try {
            SimpleWeb::StatusCode respCode = (SimpleWeb::StatusCode)handler->response_code();
            response->write(respCode, handler ? handler->output().c_str() : "invalid request handler", respHeaders);
         }
         catch (const std::exception &e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n"
               << e.what();
         }
      };

      mHttpTransport->addHttpResourceHandler("POST", path.c_str(), http_handler);
      mHttpTransport->addHttpResourceHandler("GET", path.c_str(), http_handler);
   }

   return kSuccess;
}

int JsonApiServerInterface::processIncoming(JsonApiWebSocketSession* session, const std::shared_ptr<rapidjson::Document>& request)
{
   processIncomingImpl(session, request);
   return kSuccess;
}

void JsonApiServerInterface::processIncomingImpl(JsonApiWebSocketSession* session, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   LocalMaxLog("JsonApiServerInterface::processIncomingImpl(): funcName: {}", funcName);

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal, session);
   }
}

int JsonApiServerInterface::processIncomingInMem(const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   LocalMaxLog("JsonApiServerInterface::processIncomingImpl(): funcName: {}", funcName);

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal, NULL);
   }
   return kSuccess;
}

int JsonApiServerInterface::processIncomingHttp(unsigned int requestId, const AuthToken& authToken, const cpc::string& resource, bool isOrchestration)
{
   JsonApiUserHandle jsonApiUserHdl = JsonApiUserHandleFactory::getNext();

   if (isOrchestration)
   {
      const int MAX_CONN_ATTEMPTS = 20;
      if (authToken.cp_user != cpc::string("counterpath_guest"))
      {
         size_t colonPos = authToken.remoteAddress.find(":");
         cpc::string userAndIp = authToken.cp_user;
         if (colonPos != cpc::string::npos)
         {
            userAndIp += authToken.remoteAddress.substr(0, colonPos);
         }
         auto itConnAttempt = mMapUserToConnAttempt.find(userAndIp);
         if (itConnAttempt != mMapUserToConnAttempt.end())
         {
            auto tpNow = std::chrono::system_clock::now();
            itConnAttempt->second.connAttempts++;
            if (itConnAttempt->second.connAttempts > MAX_CONN_ATTEMPTS &&
               itConnAttempt->second.firstConnTime + std::chrono::seconds(60) > tpNow)
            {
               WarningLog(<< "Too many connection attempts from user " << userAndIp << "; closing connection");
               if (itConnAttempt->second.connAttempts == MAX_CONN_ATTEMPTS + 1)
               {
                  itConnAttempt->second.firstConnTime = itConnAttempt->second.firstConnTime + std::chrono::minutes(10);
               }
               return -1;
            }
            else
            {
               itConnAttempt->second.connAttempts = 1;
            }
         }
         else
         {
            mMapUserToConnAttempt[userAndIp].connAttempts = 1;
            mMapUserToConnAttempt[userAndIp].firstConnTime = std::chrono::system_clock::now();
         }
      }

      JsonApiUserContext* jsonUserContext = new JsonApiUserContext();
      jsonUserContext->phone = mPhone;
      jsonUserContext->permissions.insert("*");
      jsonUserContext->handle = jsonApiUserHdl;
      mHttpTransport->handleRequestInContext(requestId, jsonUserContext);

      return 0;
   }

   NewLoginEvent args;
   args.authToken = authToken.authToken.c_str();
   args.userIdentity = cpc::string(authToken.cp_user.c_str(), authToken.cp_user.size());
   args.userIdentityAnonymized = doEncrypt(authToken.cp_user).c_str();
   args.requestId = requestId;
   args.isHttp = true;
   args.isOrchestration = isOrchestration;
   args.resource = resource;

   if (mPhone->getSdkModuleThread().isCurrentThread())
   {
      // LocalInfoLog("JsonApiServerInterface::processIncomingHttp(): login event for handle: {} requestId: {} remoteAddress: {} triggering processingIncomingHttpImpl", jsonApiUserHdl, requestId, args.remoteAddress.c_str());
      processIncomingHttpImpl(requestId, jsonApiUserHdl);
   }
   else
   {
      // LocalInfoLog("JsonApiServerInterface::processIncomingHttp(): login event for handle: {} requestId: {} remoteAddress: {} posting JsonApiServerInterface::processingIncomingHttpImpl", jsonApiUserHdl, requestId, args.remoteAddress.c_str());
      postToSdkThread(resip::resip_bind(&JsonApiServerInterface::processIncomingHttpImpl, this, requestId, jsonApiUserHdl));
   }

   fireEvent(cpcFunc(JsonApiServerHandler::onNewLogin), 0, jsonApiUserHdl, args);
   return kSuccess;
}

void JsonApiServerInterface::processIncomingHttpImpl(unsigned int requestId, JsonApiUserHandle jsonApiUser)
{
   // StackLog(<< "JsonApiServerInterface::processIncomingHttpImpl(): updating user-to-request map for handle: " << jsonApiUser << " requestId: " << requestId);
   mMapUserToReq[jsonApiUser] = requestId;
}

int JsonApiServerInterface::handleLogin(const rapidjson::Value& functionObjectVal, JsonApiWebSocketSession* session)
{
   cpc::string authTokenStr;
   JsonDeserialize(functionObjectVal, "authToken", authTokenStr);

   resip::Data userIdentity;
   resip::Data deviceId;
   std::map<resip::Data, resip::Data> pubClaims;
   std::vector<resip::Data> requestedResources;
   bool validateSuccess = (validateAuthToken(resip::Data(authTokenStr.c_str(), authTokenStr.size()), userIdentity, deviceId, pubClaims, requestedResources) == 0);
   if (session == NULL || (session && session->is_local_client()) || validateSuccess)
   {
      NewLoginEvent args;
      args.authToken = authTokenStr;
      args.userIdentity = cpc::string(userIdentity.c_str(), userIdentity.size());
      args.userIdentityAnonymized = doEncrypt(userIdentity.c_str()).c_str();
      if (session != NULL)
      {
         args.resource = cpc::string(session->resource().c_str(), session->resource().size());
      }

      for (const resip::Data& reqRes : requestedResources)
      {
         args.requestedResources.push_back(cpc::string(reqRes.c_str(), reqRes.size()));
      }

      // used for branded join URLs
      JsonDeserialize(functionObjectVal, "realm", args.realm);

      JsonApiUserHandle jsonApiUserHdl = JsonApiUserHandleFactory::getNext();
      if (session == NULL)
      {
         mInMemUser = jsonApiUserHdl;
      }
      else
      {
         mMapUserToWsSession[jsonApiUserHdl] = session;
         AuthToken authToken;
         authToken.authToken = cpc::string(authTokenStr.c_str(), authTokenStr.size());
         authToken.cp_user = userIdentity.c_str();
         authToken.device_uuid = deviceId.c_str();
         authToken.requestedResources = args.requestedResources;
         session->setAuthToken(authToken);
      }

      InfoLog(<< "JsonApiServerInterface::handleLogin(): firing onNewLogin: jsonApiUser=" << jsonApiUserHdl << " resource=" << args.resource << " realm=" << args.realm);
      LocalDebugLog("JsonApiServerInterface::handleLogin(): updating user-to-connection map for handle: {} auth-token: {}", jsonApiUserHdl, authTokenStr);
      fireEvent(cpcFunc(JsonApiServerHandler::onNewLogin), 0, jsonApiUserHdl, args);
   }
   else
   {
      WarningLog(<< "JsonApiServerInterface::handleLogin(): auth token failed validation: " << authTokenStr);
      LocalInfoLog("JsonApiServerInterface::handleLogin(): auth token failed validation: {}", authTokenStr);
      if (session != NULL)
      {
         session->close(true);
      }
   }

   return 0;
}

int JsonApiServerInterface::handleLogout(const rapidjson::Value& functionObjectVal, JsonApiWebSocketSession* session)
{
   JsonApiUserHandle jsonApiUser = (-1);
   JsonDeserialize(functionObjectVal, "jsonApiUser", jsonApiUser);

   observerhandlermap_iterator loggedOutIter = mLoggedOutUserToHandlerMap.find(jsonApiUser);
   if (loggedOutIter != mLoggedOutUserToHandlerMap.end())
   {
      LocalDebugLog("JsonApiServerInterface::handleLogout(): logout has already been triggered for json api user: {}", jsonApiUser);
      CPCAPI2::JsonApi::LogoutResultEvent logoutResult;
      logoutResult.success = false;
      sendLogoutResultImpl(jsonApiUser, logoutResult);
      return 0;
   }

   MapUserToWsSession::iterator i = mMapUserToWsSession.find(jsonApiUser);
   if (i != mMapUserToWsSession.end())
   {
      SdkObservers observers = getSdkObservers(jsonApiUser);
      if (observers.size() == 0)
      {
         LogoutEvent args;
         args.authToken = cpc::string(session->auth_token().authToken.c_str(), session->auth_token().authToken.size());;
         args.userIdentity = cpc::string(session->auth_token().cp_user.c_str(), session->auth_token().cp_user.size());

         // Proceed with logout as there are no observers
         LocalMaxLog("JsonApiServerInterface::handleLogout(): initiating logout immediately as there are no observers for user: {} from user to web socket mapping, userIdentity: {}",
                     jsonApiUser, args.userIdentity);
         i->second->logout();

         fireEvent(cpcFunc(JsonApiServerHandler::onLogout), 0, jsonApiUser, args);
         // The logout result event to the remote client will be triggered by the json server handler (e.g. SdkManager)
      }
      else
      {
         mLoggedOutUserToHandlerMap[jsonApiUser] = getSdkObservers(jsonApiUser);
         // The actual logout will be triggered once all the observers have confirmed completion of their pre logout handling
      }
   }
   else
   {
      LocalDebugLog("JsonApiServerInterface::handleLogout(): invalid json api user: {}", jsonApiUser);

      CPCAPI2::JsonApi::LogoutResultEvent logoutResult;
      logoutResult.success = false;
      sendLogoutResultImpl(jsonApiUser, logoutResult);
   }

   return 0;
}

int JsonApiServerInterface::processIncomingJson(const cpc::string& json)
{
   postToSdkThread(resip::resip_bind(&JsonApiServerInterface::processIncomingJsonImpl, this, json));
   return 0;
}

int JsonApiServerInterface::processIncomingJsonImpl(const cpc::string& json)
{
   size_t jsonPayloadSize = std::strlen(json.c_str());

   std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
   jsonRequest->Parse<0>(json.c_str());

   if (jsonRequest->HasParseError())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << " Aborting decode. Contents: " << json);
      return -1;
   }

   if (!jsonRequest->HasMember("moduleId"))
   {
      WarningLog(<< "Missing moduleId. Aborting decode.");
      return -1;
   }

   const rapidjson::Value& moduleIdVal = (*jsonRequest)["moduleId"];
   if (!moduleIdVal.IsString())
   {
      WarningLog(<< "moduleId is not a string. Aborting decode.");
      return -1;
   }

   resip::Data moduleIdStr(resip::Data::Share, moduleIdVal.GetString(), moduleIdVal.GetStringLength());
   StackLog(<< "Handle request for module: " << moduleIdStr << " Contents: " << json);

   if (moduleIdStr == "JsonApiServer")
   {
      processIncomingInMem(jsonRequest);
   }
   else
   {
      std::shared_ptr<resip::Data> binaryData;
      if (json.size() > jsonPayloadSize)
      {
         binaryData.reset(new resip::Data(&json[jsonPayloadSize + 1], json.size() - jsonPayloadSize - 1));
      }

      cpc::string moduleId(moduleIdVal.GetString(), moduleIdVal.GetStringLength());

      if (NULL == mInMemUserContext)
      {
         WarningLog(<< "Login has not happened yet.");
         return -1;
      }

      PhoneInterface* pi = dynamic_cast<PhoneInterface*>(mInMemUserContext->phone);
      JsonApiServerModule* pm = dynamic_cast<JsonApiServerModule*>(pi->getInterfaceByName(moduleId));
      if ((mInMemUserContext->permissions.size() == 1 && mInMemUserContext->permissions.count("*") > 0) ||
         (mInMemUserContext->permissions.count(moduleId.c_str()) > 0))
      {
         if (pm != NULL)
         {
            if (!jsonRequest->HasMember("functionObject"))
            {
               return -1;
            }

            const rapidjson::Value& functionObjVal = (*jsonRequest)["functionObject"];
            if (!functionObjVal.IsObject())
            {
               return -1;
            }

            {
               JsonApiRequestInfo connId;
               connId.transportType = JsonApiRequestInfo::JsonApiTransportType_InMem;
               connId.jsonUserHandle = mInMemUserContext->handle;
               pm->processIncoming(connId, jsonRequest, binaryData);
            }
         }
      }
   }
   return 0;
}

std::shared_ptr<JsonApiServerWebSocket> JsonApiServerInterface::getJsonApiWsTransport() const
{
   return mWebsocketTransport;
}

std::shared_ptr<JsonApiServerHTTP> JsonApiServerInterface::getJsonApiHttpTransport() const
{
   return mHttpTransport;
}

OutgoingJsonHandler* JsonApiServerInterface::getOutgoingJsonHandler() const
{
   return mConfig.outgoingJsonHandler;
}

websocketpp::connection_hdl JsonApiServerInterface::getWebsocketConnHdl(JsonApiUserHandle jsonApiUser) const
{
   MapUserToWsSession::const_iterator it = mMapUserToWsSession.find(jsonApiUser);
   if (it != mMapUserToWsSession.end())
   {
      return it->second->websocket_conn_hdl();
   }
   return websocketpp::connection_hdl();
}

AuthToken JsonApiServerInterface::getAuthTokenForConnection(JsonApiUserHandle jsonApiUser) const
{
   MapUserToWsSession::const_iterator it = mMapUserToWsSession.find(jsonApiUser);
   if (it != mMapUserToWsSession.end())
   {
      return it->second->auth_token();
   }
   static AuthToken authToken;
   return authToken;
}

int JsonApiServerInterface::validateAuthToken(const AuthToken& authToken, bool& isValid, std::map<resip::Data, resip::Data>* pubClaims, std::vector<resip::Data>* requestedResources)
{
   //LocalMaxLog("JsonApiServerInterface::validateAuthToken(): {} mPhone: {} certificate file path: {}",
   //            this, static_cast<void*>(mPhone), mConfig.certificateFilePath);
   return CPCAPI2::AuthServer::JwtUtils::VerifyJWT(mConfig.certificateFilePath.c_str(), authToken.authToken.c_str(), isValid, pubClaims, requestedResources);
}

int JsonApiServerInterface::validateAuthToken(const resip::Data& authToken, resip::Data& userIdentity, resip::Data& deviceId, std::map<resip::Data, resip::Data>& publicClaims, std::vector<resip::Data>& requestedResources)
{
   bool isValid = false;
   if (AuthServer::JwtUtils::VerifyJWT(mConfig.certificateFilePath.c_str(), authToken, isValid, &publicClaims, &requestedResources) == 0)
   {
      if (isValid)
      {
         std::map<resip::Data, resip::Data>::const_iterator it = publicClaims.find("cp_user");
         if (it != publicClaims.end())
         {
            userIdentity = it->second;
         }
         else
         {
            WarningLog(<< "no cp_user in pub claims");
            return -1;
         }

         it = publicClaims.find("device_uuid");
         if (it != publicClaims.end())
         {
            deviceId = it->second;
         }
         else
         {
            WarningLog(<< "no device_uuid in pub claims");
            return -1;
         }

         return 0;
      }
      else
      {
         WarningLog(<< "JWT is not valid");
      }
   }
   else
   {
      ErrLog(<< "VerifyJWT returned error");
      InfoLog(<< "certificate: " << mConfig.certificateFilePath.c_str());
   }
   return -1;
}

void JsonApiServerInterface::handleConnectionClosed(JsonApiUserHandle jsonApiUser, JsonApiWebSocketSession* session, const std::function<void(JsonApiWebSocketSession*)>& closedCallback)
{
   postToSdkThread(resip::resip_bind(&JsonApiServerInterface::handleConnectionClosedImpl, this, jsonApiUser, session, closedCallback));
}

void JsonApiServerInterface::handleConnectionClosedImpl(JsonApiUserHandle jsonApiUser, JsonApiWebSocketSession* session, const std::function<void(JsonApiWebSocketSession*)>& closedCallback)
{
   LocalDebugLog("JsonApiServerInterface::handleConnectionClosedImpl(): phone: {} session: {} JsonApiUserHandle {}", static_cast<void*>(mPhone), (void*)session, jsonApiUser);
   MapUserToWsSession::const_iterator i = mMapUserToWsSession.find(jsonApiUser);
   if (i != mMapUserToWsSession.end())
   {
      LocalDebugLog("JsonApiServerInterface::handleConnectionClosedImpl(): phone: {} mapped session: {} JsonApiUserHandle {}", static_cast<void*>(mPhone), (void*)(i->second), jsonApiUser);
      ConnectionClosedEvent args;
      args.authToken = i->second->auth_token().authToken;
      args.userIdentity = i->second->auth_token().cp_user;
      mMapUserToWsSession.erase(i);
      mLoggedOutUserToHandlerMap.erase(jsonApiUser);

      fireEvent(cpcFunc(JsonApiServerHandler::onConnectionClosed), 0, jsonApiUser, args);
   }

   closedCallback(session);
}

const JsonApiServerConfig& JsonApiServerInterface::getConfig() const
{
   return mConfig;
}

static const char vigfwd[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                               'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
                               'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z' };
static int vigrev[] = { -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
                        -1, -1, -1, -1, -1, -1, -1, 0, 1, 2,
                        3, 4, 5, 6, 7, 8, 9, -1, -1, -1,
                        -1, -1, -1, -1, 10, 11, 12, 13, 14, 15,
                        16, 17, 18, 19, 20, 21, 22, 23, 24, 25,
                        26, 27, 28, 29, 30, 31, 32, 33, 34, 35,
                        -1, -1, -1, -1, -1, -1, 36, 37, 38, 39,
                        40, 41, 42, 43, 44, 45, 46, 47, 48, 49,
                        50, 51, 52, 53, 54, 55, 56, 57, 58, 59,
                        60, 61 };

class Vig {
public:
   Vig(const std::string& kin) {
      for (int i = 0; i < kin.size(); ++i) {
         if (isValid(kin[i])) {
            this->k += kin[i];
         }
      }
   }
   bool isValid(const char c) {
      if (c >= 'A' && c <= 'Z' ||
         c >= 'a' && c <= 'z' ||
         c >= '0' && c <= '9')
      {
         return true;
      }
      return false;
   }

   std::string encryption(const std::string& t) {
      std::string output;
      for (int i = 0, j = 0; i < t.length(); ++i) {
         char c = t[i];
         if (!isValid(c))
            continue;

         int cidx = vigrev[c]; //std::distance(alphabet.begin(), alphabet.find(c));
         int kjidx = vigrev[k[j]]; //std::distance(alphabet.begin(), alphabet.find(k[j]));

         int outcharidx = (cidx + kjidx) % alphabet_size;
         char itbeg = vigfwd[outcharidx]; //std::next(alphabet.begin(), outcharidx);
         output += itbeg;
         j = (j + 1) % k.length();
      }
      return output;
   }
   std::string decryption(const std::string& t) {
      std::string output;
      for (int i = 0, j = 0; i < t.length(); ++i) {
         char c = t[i];
         if (!isValid(c))
            continue;

         int cidx = vigrev[c]; //std::distance(alphabet.begin(), alphabet.find(c));
         int kjidx = vigrev[k[j]]; //std::distance(alphabet.begin(), alphabet.find(k[j]));

         int outcharidx = (cidx - kjidx + alphabet_size) % alphabet_size;
         char itbeg = vigfwd[outcharidx]; //std::next(alphabet.begin(), outcharidx);
         output += itbeg;
         j = (j + 1) % k.length();
      }
      return output;
   }
private:
   std::string k;
   const int alphabet_size = 62;
};

resip::Data JsonApiServerInterface::doEncrypt(const cpc::string& input)
{
   Vig v("FlyffyBunNy");
   std::string outDataStr = v.encryption(input.c_str());
   return outDataStr.c_str();
}

}

}

#endif
