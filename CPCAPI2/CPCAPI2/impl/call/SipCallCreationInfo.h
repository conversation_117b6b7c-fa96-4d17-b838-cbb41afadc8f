#pragma once

#include "cpcapi2defs.h"
#include "call/SipConversationManager.h"
#include "call/SipConversationManagerExt.h"
#include "recording/RecordingHandler.h"

#include <ConversationManager.hxx>

#include <webrtc/common_types.h>

namespace webrtc_recon
{
class RtpStreamImpl;
}

namespace CPCAPI2
{
namespace SipConversation
{
   enum BestEffortMediaEncryptionState
   {
      BestEffortMediaEncryptionState_Initial,
      BestEffortMediaEncryptionState_SAVP_Call,
      BestEffortMediaEncryptionState_SAVP_Call_Rejected,
      BestEffortMediaEncryptionState_AVP_Call
   };

   struct AddrName
   {
      cpc::string address;
      cpc::string displayName;

      bool empty() const
      {
         return address.empty() && displayName.empty();
      }
   };

   struct SipCallCreationInfo
   {
      CPCAPI2::SipAccount::SipAccountHandle                     account;
      cpc::vector<resip::NameAddr>                              targetAddresses;
      cpc::vector<MediaInfo>                                    localMediaInfo;
      cpc::vector<MediaInfo>                                    remoteMediaInfo;
      cpc::vector<MediaInfo>                                    configuredLocalMediaInfo;
      unsigned int                                              anonymousMode;
      bool                                                      bestEffortMediaEncryption;
      BestEffortMediaEncryptionState                            bestEffortMediaEncryptionState;
      recon::ConversationHandle                                 reconConversation;
      recon::ParticipantHandle                                  reconRemoteParticipant;
      recon::ParticipantHandle                                  reconOriginalRemoteParticipant;
      bool                                                      hasPendingRequest;
      recon::ParticipantHandle                                  reconMediaParticipant;
      SipConversationHandle                                     conversationToReplace;
      SipConversationHandle                                     originalConversation;
      SipConversationHandle                                     conversationToReplaceWithStarcode;
      std::vector<std::weak_ptr<webrtc_recon::RtpStreamImpl>>   rtpStreams;
      resip::NameAddrs                                          redirectedAddrs;
      bool                                                      wasRedirected;
      bool                                                      wasForked;
      resip::DialogId                                           replacesDialogId;
      resip::DialogId                                           joinDialogId;
      cpc::vector<Parameter>                                    joinParams;
      bool                                                      sipDTMFFailure;
      cpc::string                                               fromAddress; // user-specified value for From header, used (e.g.) in shared call appearance
      cpc::vector<CPCAPI2::SipHeader>                           customHeaders;
      bool                                                      shouldEndOriginalCallAfterAttendedTransferSuccess;
      std::shared_ptr<resip::NameAddr>                          potentialTargetAddress; // the new value of the From header we receive in an incoming re-INVITE
      ConversationState                                         currentConversationState;
      bool                                                      localEncryptionOptionsConfigured;
      resip::Data                                               callId;
      int64_t                                                   numNonZeroAudioInputLevelsSampled;
      bool                                                      callKitMode;
      void*                                                     incomingVideoRenderSurface;
      webrtc::VideoRenderSurfaceType                            incomingVideoRenderSurfaceType;
      bool                                                      callInitiatedFromPush;
      bool                                                      mediaBypassMode;
      SipConversation::NackSettings                             videoNackSettings;
      cpc::string                                               forceTarget;
      bool                                                      sentRemoteRinging;  // whether or not ConversationStateChangedEvent was sent with ConversationState_RemoteRinging
      ConversationStateChangedEvent                             lastRemoteRingingEvent;
      CPCAPI2::SipConversation::AnswerModeSettings              answerMode;
      AddrName                                                  lastRemoteAddr; // last remoteDisplayName and remoteAddress fired to app

      // Used for detecting stalled RTP/RTCP streams while calculating call quality indicator
      unsigned int                                              previousPacketsReceived;
      uint32_t                                                  previousAudioRTCPSenderReportTimestamp;
      unsigned int                                              numIntervalsWithoutUpdate;
      std::set<Recording::RecorderHandle>                       recordings;
      int                                                       audioDeviceCloseDelay;
      unsigned int                                              responseTimeMs;

      SipCallCreationInfo()
         : replacesDialogId(resip::Data::Empty, resip::Data::Empty, resip::Data::Empty),
           joinDialogId(resip::Data::Empty, resip::Data::Empty, resip::Data::Empty)
      {
         MediaInfo defaultMedia = MediaInfo(MediaType_Audio, MediaDirection_SendReceive);
         localMediaInfo.push_back(defaultMedia);
         configuredLocalMediaInfo = localMediaInfo;
         anonymousMode = 0;
         reconConversation = 0;
         reconRemoteParticipant = 0;
         reconOriginalRemoteParticipant = 0;
         hasPendingRequest = false;
         reconMediaParticipant = 0;
         conversationToReplace = 0;
         originalConversation = 0;
         wasForked = false;
         wasRedirected = false;
         sipDTMFFailure = false;
         bestEffortMediaEncryption = false;
         bestEffortMediaEncryptionState = BestEffortMediaEncryptionState_Initial;
         shouldEndOriginalCallAfterAttendedTransferSuccess = true;
         currentConversationState = ConversationState_None;
         localEncryptionOptionsConfigured = false;
         numNonZeroAudioInputLevelsSampled = -1;
         callKitMode = false;
         previousPacketsReceived = 0;
         previousAudioRTCPSenderReportTimestamp = 0;
         numIntervalsWithoutUpdate = 0;
         incomingVideoRenderSurface = NULL;
         incomingVideoRenderSurfaceType = webrtc::kRenderSurfaceDefault;
         callInitiatedFromPush = false;
         mediaBypassMode = false;
         sentRemoteRinging = false;
         answerMode.mode = CPCAPI2::SipConversation::AnswerMode_Disabled;
         audioDeviceCloseDelay = 0;
         conversationToReplaceWithStarcode = 0;
         responseTimeMs = 0;
      }
   };

   typedef std::map<SipConversationHandle, SipCallCreationInfo*> SipCallCreationInfoMap;

   class SipConversationHandleFactory
   {
   public:
      static SipConversationHandle getNext() { return sNextConversationHandle++; }
   private:
      static SipConversationHandle sNextConversationHandle;
   };
}
}
