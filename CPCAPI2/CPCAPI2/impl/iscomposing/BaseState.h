#pragma once

#if !defined(__CPCAPI2_BASE_STATE_H__)
#define __CPCAPI2_BASE_STATE_H__

namespace CPCAPI2
{
namespace IsComposing
{
class IsComposingManager;
struct IsComposingInfo;

class BaseState
{
public:
   virtual void onEnterState(IsComposingManager* manager, IsComposingInfo* info) {}
   virtual void onLeaveState(IsComposingManager* manager, IsComposingInfo* info) {}
};

}
}

#endif // __CPCAPI2_BASE_STATE_H__