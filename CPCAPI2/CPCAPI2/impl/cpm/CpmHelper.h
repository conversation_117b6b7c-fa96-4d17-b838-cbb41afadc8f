#pragma once

#if !defined(__CPCAPI2_CPM_HELPER_H__)
#define __CPCAPI2_CPM_HELPER_H__

#include "CpimMessage.h"
#include "cpm/CpmTypes.h"
#include "../account/SipAccountImpl.h"

#include <msrp_forward_decl.h>
#include <utils/msrp_log.h>

#include <resip/stack/Uri.hxx>
#include <resip/stack/SdpContents.hxx>

#include <libxml/tree.h>

#include <string.h>
#include <time.h>

namespace CPCAPI2
{
namespace CPM
{

enum MessageType
{
   MessageType_MessageDeliveredNotification,
   MessageType_MessageDisplayedNotification,
   MessageType_IsComposingNotification,
   MessageType_Message
};

class CpmHelper
{
public:
   static tm getCurrentDateTime();
   static std::list<resip::Data> getLines(const resip::Data& str, const resip::Data& delim);
   static xmlNodePtr getChildNode(xmlNodePtr node, const char* name);
   static cpc::string getChildNodeText(xmlNodePtr parent, const char* name);
   static resip::Uri getUri(const CPCAPI2::SipAccount::SipAccountImpl &account);
   static resip::NameAddr getNameAddr(const CPCAPI2::SipAccount::SipAccountImpl &account);
   static resip::SdpContents::Session::Medium getMedium(const resip::SdpContents& contents, const char* mediumName);
   static cpc::string createDateTimeString(const tm& datetime);
   static CpimMessage createCpimMessage(const cpc::string& messageId, const resip::Uri& fromUri, const resip::Uri& toUri, const tm& datetime, const resip::Mime& contentType, const resip::Data& messageContent, const cpc::vector<DispositionNotificationType>& dispositionNotifications);
   static CpimMessage createCpimMessage(const cpc::string& messageId, const resip::Uri& fromUri, const resip::Uri& toUri, const cpc::string& origMessageId, const cpc::string& origDateTimeStr, MessageType notificationType, int notificationStatus);
   static void extractCpimMessage(CpimMessage cpimMessage, cpc::string& messageId, cpc::string& fromUriStr, cpc::string& toUriStr, cpc::string& dateTimeValue, tm& datetime, resip::Mime& contentType, resip::Data& messageContent, cpc::vector<DispositionNotificationType>& dispositionNotifications);
   static void extractCpimMessage(CpimMessage cpimMessage, cpc::string& messageId, cpc::string& origMessage, cpc::string& datetimeString, MessageType& notificationType, int& notificationStatus);
   static tm extractDateTime(const cpc::string& dateTimeValue);
   static bool isImdnNotification(CpimMessage cpimMessage);
   static bool acceptsCpimMimeType(const resip::SipMessage& msg);
   static void logMsrpMessage(msrp_log_level_enum_t level, const char* fmt, const va_list& args);
   static msrp_uri_t* createLocalMsrpUri(const resip::Data& localIp, unsigned short local_port);
   static resip::Data buildSdp(msrp_uri_t* localUri, UInt32 localMediaPort, const resip::Mime& cpimContentType, bool isActive, const std::list<resip::Mime>& additionalAcceptedwrappedTypes = std::list<resip::Mime>());
   static bool contains3gppFeatureTag(const resip::SipMessage& msg, const cpc::string& featureTagValueSuffix);
   static bool containsFeatureTag(const resip::SipMessage& msg, const cpc::string& featureTag);
   static bool containsNoFeatureTag(const resip::SipMessage& msg);
   static void set3gppFeatureTag(resip::SipMessage& msg, const cpc::string& featureTagValueSuffix);
   static void setActiveMsrpEndpoint(const resip::SdpContents& contents, bool& isActive);
   static void logWarning(const std::string& message);
   static resip::Mime mimeTypeToContentType(CPM::MimeType mimeType);
   static CPM::MimeType contentTypeToMimeType(const resip::Mime& contentType);
   static resip::Mime stringToContentType(const cpc::string& contentTypeStr);
   static cpc::string contentTypeToString(const resip::Mime& contentType);
   static NameAddress resipNameAddrToNameAddr(const resip::NameAddr& nameAddr);
   static void initializeDateTime(tm& dateTime);

   static const resip::Mime CPIM_CONTENT_TYPE;
   static const resip::Mime SDP_CONTENT_TYPE;
   static const char* MESSAGE_MEDIA_TYPE;
   static const char* ACCEPT_TYPES_ATTRIBUTE;
   static const char* MSRP_PATH;

protected:
   static const cpc::string IMDN_NS;
   static const resip::Mime IMDN_CONTENT_TYPE;
   static const resip::Mime TEXT_PLAIN_CONTENT_TYPE;

private:
   static tm createDateTime(int year, int month, int day, int hour, int minute, int second, char offsetPrefix, int offsetHour, int offsetMin);
   static CpimHeader createDateTimeHeader(const tm& datetime);
   static CpimHeader createContentTypeHeader(const resip::Mime& contentType);
   static CpimHeader createDispositionNotificationHeader(const cpc::vector<DispositionNotificationType>& dispositionNotifications);
   static cpc::vector<DispositionNotificationType> extractDispositionNotificationHeader(CpimHeader imdnDispositionNotificationHeader);
   static resip::Mime extractContentTypeHeader(CpimHeader contentTypeHeader);
   static void extractDateTimeHeader(CpimHeader dateTimeHeader, cpc::string& dateTimeValue, tm& datetime);

   static const char* CPM_FEATURE_TAG_NAME;
   static const char* CPM_FEATURE_TAG_VALUE_PREFIX;
   static const cpc::string IMDN_POSITIVE_DELIVERY;
   static const cpc::string IMDN_NEGATIVE_DELIVERY;
   static const cpc::string IMDN_DISPLAY;
   static const resip::Mime TEXT_XML_CONTENT_TYPE;
   static const resip::Mime TEXT_HTML_CONTENT_TYPE;
   static const resip::Mime IMAGE_JPEG_CONTENT_TYPE;
   static const resip::Mime IMAGE_PNG_CONTENT_TYPE;
};

}
}

#endif // __CPCAPI2_CPM_HELPER_H__