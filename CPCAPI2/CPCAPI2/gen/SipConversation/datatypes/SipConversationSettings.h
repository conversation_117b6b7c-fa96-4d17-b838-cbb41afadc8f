#include "jsonrpc/StringSerialization.h"
#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>
#include "NatTraversalMode.h"
#include "NatTraversalServerSourceType.h"
#include "NatTraversalServerType.h"
#include "HoldMode.h"
#include "PrackMode.h"
#include "AnswerModeSettings.h"
#include "NetworkChangeHandoverMode.h"

#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSETTINGS_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSETTINGS_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationSettings
  {
  public:
    std::optional<cpc::string> sessionName;
    std::optional<NatTraversalMode> natTraversalMode;
    std::optional<NatTraversalServerSourceType> natTraversalServerSource;
    std::optional<cpc::string> natTraversalServer;
    std::optional<NatTraversalServerType> natTraversalServerType;
    std::optional<HoldMode> holdMode;
    std::optional<PrackMode> prackMode;
    std::optional<AnswerModeSettings> answerMode;
    std::optional<NetworkChangeHandoverMode> networkChangeHandoverMode;
    std::optional<cpc::string> networkChangeHandoverStarcode;
    std::optional<int32_t> minRtpPort;
    std::optional<int32_t> maxRtpPort;
    std::optional<int32_t> minRtpPortAudio;
    std::optional<int32_t> maxRtpPortAudio;
    std::optional<int32_t> minRtpPortVideo;
    std::optional<int32_t> maxRtpPortVideo;
    std::optional<cpc::string> turnUsername;
    std::optional<cpc::string> turnPassword;
    std::optional<bool> includePPreferredIdentity;
    std::optional<bool> includePAssertedIdentity;
    std::optional<bool> includeAttribsForStaticPLs;
    std::optional<bool> adornTransferMessages;

    SipConversationSettings() = default;

    SipConversationSettings(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationSettings& o)
    {
      j = nlohmann::json::object();
       if (o.sessionName) { j["sessionName"] = *(o.sessionName); }
     if (o.natTraversalMode) { j["natTraversalMode"] = *(o.natTraversalMode); }
     if (o.natTraversalServerSource) { j["natTraversalServerSource"] = *(o.natTraversalServerSource); }
     if (o.natTraversalServer) { j["natTraversalServer"] = *(o.natTraversalServer); }
     if (o.natTraversalServerType) { j["natTraversalServerType"] = *(o.natTraversalServerType); }
     if (o.holdMode) { j["holdMode"] = *(o.holdMode); }
     if (o.prackMode) { j["prackMode"] = *(o.prackMode); }
     if (o.answerMode) { j["answerMode"] = *(o.answerMode); }
     if (o.networkChangeHandoverMode) { j["networkChangeHandoverMode"] = *(o.networkChangeHandoverMode); }
     if (o.networkChangeHandoverStarcode) { j["networkChangeHandoverStarcode"] = *(o.networkChangeHandoverStarcode); }
     if (o.minRtpPort) { j["minRtpPort"] = *(o.minRtpPort); }
     if (o.maxRtpPort) { j["maxRtpPort"] = *(o.maxRtpPort); }
     if (o.minRtpPortAudio) { j["minRtpPortAudio"] = *(o.minRtpPortAudio); }
     if (o.maxRtpPortAudio) { j["maxRtpPortAudio"] = *(o.maxRtpPortAudio); }
     if (o.minRtpPortVideo) { j["minRtpPortVideo"] = *(o.minRtpPortVideo); }
     if (o.maxRtpPortVideo) { j["maxRtpPortVideo"] = *(o.maxRtpPortVideo); }
     if (o.turnUsername) { j["turnUsername"] = *(o.turnUsername); }
     if (o.turnPassword) { j["turnPassword"] = *(o.turnPassword); }
     if (o.includePPreferredIdentity) { j["includePPreferredIdentity"] = *(o.includePPreferredIdentity); }
     if (o.includePAssertedIdentity) { j["includePAssertedIdentity"] = *(o.includePAssertedIdentity); }
     if (o.includeAttribsForStaticPLs) { j["includeAttribsForStaticPLs"] = *(o.includeAttribsForStaticPLs); }
     if (o.adornTransferMessages) { j["adornTransferMessages"] = *(o.adornTransferMessages); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationSettings& o)
    {
     if (j.contains("sessionName")) { o.sessionName = j.at("sessionName"); }
     if (j.contains("natTraversalMode")) { o.natTraversalMode = j.at("natTraversalMode"); }
     if (j.contains("natTraversalServerSource")) { o.natTraversalServerSource = j.at("natTraversalServerSource"); }
     if (j.contains("natTraversalServer")) { o.natTraversalServer = j.at("natTraversalServer"); }
     if (j.contains("natTraversalServerType")) { o.natTraversalServerType = j.at("natTraversalServerType"); }
     if (j.contains("holdMode")) { o.holdMode = j.at("holdMode"); }
     if (j.contains("prackMode")) { o.prackMode = j.at("prackMode"); }
     if (j.contains("answerMode")) { o.answerMode = j.at("answerMode"); }
     if (j.contains("networkChangeHandoverMode")) { o.networkChangeHandoverMode = j.at("networkChangeHandoverMode"); }
     if (j.contains("networkChangeHandoverStarcode")) { o.networkChangeHandoverStarcode = j.at("networkChangeHandoverStarcode"); }
     if (j.contains("minRtpPort")) { o.minRtpPort = j.at("minRtpPort"); }
     if (j.contains("maxRtpPort")) { o.maxRtpPort = j.at("maxRtpPort"); }
     if (j.contains("minRtpPortAudio")) { o.minRtpPortAudio = j.at("minRtpPortAudio"); }
     if (j.contains("maxRtpPortAudio")) { o.maxRtpPortAudio = j.at("maxRtpPortAudio"); }
     if (j.contains("minRtpPortVideo")) { o.minRtpPortVideo = j.at("minRtpPortVideo"); }
     if (j.contains("maxRtpPortVideo")) { o.maxRtpPortVideo = j.at("maxRtpPortVideo"); }
     if (j.contains("turnUsername")) { o.turnUsername = j.at("turnUsername"); }
     if (j.contains("turnPassword")) { o.turnPassword = j.at("turnPassword"); }
     if (j.contains("includePPreferredIdentity")) { o.includePPreferredIdentity = j.at("includePPreferredIdentity"); }
     if (j.contains("includePAssertedIdentity")) { o.includePAssertedIdentity = j.at("includePAssertedIdentity"); }
     if (j.contains("includeAttribsForStaticPLs")) { o.includeAttribsForStaticPLs = j.at("includeAttribsForStaticPLs"); }
     if (j.contains("adornTransferMessages")) { o.adornTransferMessages = j.at("adornTransferMessages"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSETTINGS_H
