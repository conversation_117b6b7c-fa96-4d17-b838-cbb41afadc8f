#include "brand_branded.h"

#include "interface/experimental/cloudserviceconfig/CloudServiceConfig.h"

#if (CPCAPI2_BRAND_CLOUD_SERVICE_CONFIG_MODULE == 1)
#include "CloudServiceConfigInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace CloudServiceConfig
{
   CloudServiceConfigManager* CloudServiceConfigManager::getInterface(Phone* cpcPhone)
   {
#if (CPCAPI2_BRAND_CLOUD_SERVICE_CONFIG_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<CloudServiceConfigInterface>(phone, "CloudServiceConfigManager");
#else
      return NULL;
#endif
   }

}
}
