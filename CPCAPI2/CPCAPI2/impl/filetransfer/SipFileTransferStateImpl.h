#pragma once

#if !defined(CPCAPI2_SIP_FILE_TRANSFER_STATE_IMPL_H)
#define CPCAPI2_SIP_FILE_TRANSFER_STATE_IMPL_H

#include "cpcapi2defs.h"
#include "filetransfer/SipFileTransferManager.h"
#include "filetransfer/SipFileTransferState.h"
#include "filetransfer/SipFileTransferHandler.h"
#include "SipFileTransferInfo.h"
#include "../phone/PhoneModule.h"

namespace CPCAPI2
{
   namespace SipFileTransfer
   {
      class SipFileTransferManagerInterface;

      class SipFileTransferStateImpl : public SipFileTransferStateManager,
                                       public PhoneModule,
                                       public SipFileTransferHandler

      {
      public:
         SipFileTransferStateImpl(SipFileTransferManagerInterface* convManIf);
         virtual ~SipFileTransferStateImpl();

         virtual int getState(SipFileTransferHandle h, SipFileTransferState& conversationState) OVERRIDE;
         virtual void Release() OVERRIDE;

         virtual int onNewFileTransfer( const SipFileTransferHandle& fileTransfer, const NewFileTransferEvent& event ) OVERRIDE;
         virtual int onFileTransferConfigured( const SipFileTransferHandle& fileTransfer, const FileTransferConfiguredEvent& event ) OVERRIDE;
         virtual int onFileTransferEnded( const SipFileTransferHandle& fileTransfer, const FileTransferEndedEvent& event ) OVERRIDE;
         virtual int onFileTransferItemProgress( const SipFileTransferHandle& fileTransfer, const FileTransferItemProgressEvent& event ) OVERRIDE;
         virtual int onFileTransferItemEnded( const SipFileTransferHandle& fileTransfer, const FileTransferItemEndedEvent& event ) OVERRIDE;
         virtual int onError( const SipFileTransferHandle& fileTransfer, const ErrorEvent& event ) OVERRIDE;

      private:
         std::map<SipFileTransferHandle, SipFileTransferState> mStateMap;
         SipFileTransferManagerInterface* mConvManIf;
      };
   }
}

#endif // CPCAPI2_SIP_FILE_TRANSFER_STATE_IMPL_H
