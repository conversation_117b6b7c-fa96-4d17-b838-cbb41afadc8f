#include <assert.h>
#include <sstream>
#include <string.h>

#include <utils/msrp_tree.h>

#include <libxml/parser.h>
#include <libxml/tree.h>

#include "DocumentTemplate.h"
#include "DocumentBuilder.h"
#include "RegistryUtils.h"

#define DOC_VERSION "1.0"

using namespace CPCAPI2::Analytics;

DocumentBuilder::DocumentBuilder( msrp_tree* pRegistry )
   : mRegistry( pRegistry )
{
}

DocumentBuilder::~DocumentBuilder()
{
}

// Recursive method which builds the document on the fly from the data which 
// is stored in the registry (at the current position).
static bool buildDoc(
   msrp_tree *registry,         // Pointer to the registry
   const std::string& regPath,  // Path inside the registry to the correct data
   xmlNodePtr inNode,           // XML node from the template
   xmlNodePtr outNode )         // XML node being built
{
   const xmlChar *tmp = NULL;

   if( regPath.size() == 0 )
      return false;

   assert( xmlStrcmp( inNode->name, outNode->name ) == 0 );
   if( xmlStrcmp( inNode->name, outNode->name ) != 0 )
      return false;

   // check that the path exists in the registry, otherwise skip this node.
   if( !RegistryUtils::exists( registry, regPath.c_str() ))
      return true; // NB: not an error condition

   if( xmlStrcmp( BAD_CAST "data", inNode->name ) == 0 )
   {
      // Process "data" nodes specially.
      //
      // TODO: Move this code into RegistryUtils or find a cleaner way to
      // structure it; DocumentBuilder shouldn't know registry internals.
      std::string propValue; // end result is always a string

      RegistryNode *pRegNode = ( RegistryNode * ) msrp_tree_get( registry, regPath.c_str() );
      switch( pRegNode->type )
      {
      case( RegistryNodeType_string ):
      {
         // Read  the size of the string
         assert( pRegNode->bytes.size() >= sizeof( size_t ));
         if( pRegNode->bytes.size() < sizeof( size_t ))
            return false;

         size_t *length = ( size_t * )&( pRegNode->bytes[ 0 ] );
         if( length == NULL )
            return false;

         // Validate the size
         assert( pRegNode->bytes.size() >= ( sizeof( size_t ) + *length + 1 ));
         char *data = ( char * )&( pRegNode->bytes[ sizeof( size_t ) ] ); // NULL terminated
         propValue = data;
      } break;
      case( RegistryNodeType_bool ):
      {
         bool *value = ( bool * )( &( pRegNode->bytes[ 0 ] ));
         propValue = ( *value ) ? "true" : "false";
      } break;
      case( RegistryNodeType_int ):
      {
         int *value = ( int * )( &( pRegNode->bytes[ 0 ] ));
         std::ostringstream strs;
         strs << *value;
         propValue = strs.str();
      } break;
      case( RegistryNodeType_int64 ):
      {
         int64_t *value = ( int64_t * )( &( pRegNode->bytes[ 0 ] ));
         std::ostringstream strs;
         strs << *value;
         propValue = strs.str();
      } break;
      case( RegistryNodeType_double ):
      {
         double *value = ( double * )( &( pRegNode->bytes[ 0 ] ));
         std::ostringstream strs;
         strs << *value;
         propValue = strs.str();
      } break;
      case( RegistryNodeType_list ): // NB: lists should not be present under 'data' node
      default:
         return false;
         break;
      }

      xmlChar *propName = xmlGetProp(inNode, BAD_CAST "name");
      // Set the values as a properties on the outNode
      xmlNewProp( outNode, BAD_CAST "name", propName );
      xmlNewProp( outNode, BAD_CAST "value", BAD_CAST propValue.c_str() );
      xmlFree( propName );
   }
   else if( xmlStrcmp( BAD_CAST "vqm_report", inNode->name ) == 0 )
   {
      // The VQM report is somewhat special. It is the only node in the template
      // whose data is stored as CDATA, instead of the usual "data" name/value
      // pairs. Possibly because of the format of the data. In any case, build this
      // in a similar manner as the template.
      //
      // TODO: check that the name of the text node is excluded from generated doc
      std::string report;
      if (RegistryUtils::getString(registry, regPath.c_str(), report))
      {
          xmlNodePtr tempNode = xmlNewCDataBlock(outNode->doc, BAD_CAST report.c_str(), report.length());
          xmlAddChild(outNode, tempNode);
      }

      return true;
   }
   else
   {
      // Regular node processing

      // Fetch attributes from the current node, copy them into the out node
      xmlAttr* attribute = inNode->properties;
      while( attribute && attribute->name )
      {
         // Get the (string) value from the registry
         std::string attrValue;
         if( RegistryUtils::getAttribute(
            registry,
            regPath.c_str(),
            ( const char * ) attribute->name,
            attrValue ))
         {
            // Add the same attribute to the out document
            xmlNewProp( outNode, attribute->name, BAD_CAST attrValue.c_str() );
         }

         attribute = attribute->next;
      }

      for( xmlNode* inChild = inNode->children ; inChild != NULL ; inChild = inChild->next )
      {
          //do a manual check for special tags that are in template but not in registry so need to be skipped
          //data and list tags already have this mechanism but special tags dont
          //special means videoOut,videoIn,vqm_report
          if (xmlStrcmp(BAD_CAST "videoOut", inChild->name) == 0 || xmlStrcmp(BAD_CAST "videoIn", inChild->name) == 0 || xmlStrcmp(BAD_CAST "vqm_report", inChild->name) == 0) {
              std::string pathToTest = regPath.c_str();
              pathToTest += SEP;
              pathToTest += (char *)inChild->name;
              if (!RegistryUtils::exists(registry, pathToTest.c_str())) continue;
          }
          
         if( xmlIsBlankNode( inChild ))
         {
            // Skip blank nodes from the template
         }
         else if( xmlStrcmp( BAD_CAST "data", inChild->name ) == 0 )
         {
            // Get the attribute "name"
            xmlChar *propName = xmlGetProp( inChild, BAD_CAST "name" );

            // Build the lookup path for the registry
            std::string childRegPath( regPath );
            childRegPath += SEP;
            childRegPath += ( const char * ) propName;

            xmlFree(propName);

            // Only proceed if there is data present for this item
            if( RegistryUtils::hasData( registry, childRegPath.c_str() ))
            {
               xmlNodePtr outChild = xmlNewNode( NULL, inChild->name );
               xmlAddChild( outNode, outChild );

               // Recursive call with correct registry path
               if( !buildDoc( registry, childRegPath, inChild, outChild ))
                  return false;
            }
         }
         else if(( tmp = xmlStrstr( inChild->name, BAD_CAST "_list" )) != NULL )
         {
            // A list is special because the template idea falls apart here.
            //
            // The template ONLY specifies an example of things which MAY be
            // included in a list, it doesn't necessarily have an entry for
            // each thing which might ACTUALLY be part of the list.

            // Always add the "xyz_list" node to the output document
            xmlNodePtr outChild = xmlNewNode( NULL, inChild->name );

            // Process list nodes: This branch goes a little deeper because
            // of manipulations that happen to the registry path
            std::string itemName;
            std::string listRoot;

            // Parse the itemName from the xml node
            itemName.assign(( const char * ) inChild->name, ( tmp - inChild->name ));

            // Take the first child under "inChild" which matches the itemName
            // as the input list item node.
            xmlNode *inItem = NULL;
            for( inItem = inChild->children ; inItem != NULL ; inItem = inItem->next )
            {
               // skip skippable nodes
               if( xmlIsBlankNode( inItem ))
                  continue;

               if( xmlStrcmp( inItem->name, BAD_CAST itemName.c_str() ) == 0 )
                  break;
            }

            // If there's nothing in the template at this spot, bail.
            assert( inItem != NULL );
            if( inItem == NULL )
               return false;

            bool isChildAdded = false;

            // Get the listRoot and check if it exists.
            if( RegistryUtils::listExists( registry, regPath.c_str(), itemName.c_str(), listRoot ))
            {
               // Iterate over the list. Note: inItem is invariant in this
               // loop, but outItem is not.
               size_t count = RegistryUtils::getListSize( registry, listRoot.c_str() );
               for( size_t i = 0 ; i < count ; ++i )
               {
                  std::string itemRegPath;
                  RegistryUtils::getListItem( registry, listRoot.c_str(), i, itemRegPath );

                  
                  bool exists = RegistryUtils::exists(registry, itemRegPath.c_str());
                  if (!exists && listRoot.compare("cpc_usage_report/activity_data/account_list") == 0) continue;

                  xmlNodePtr outItem = xmlNewNode( NULL, BAD_CAST itemName.c_str() );
                  xmlAddChild( outChild, outItem );
                  isChildAdded = true;

                  

                  if( !buildDoc( registry, itemRegPath, inItem, outItem ))
                     return false;
               }
            }

            // Only add the _list node if there was actually something in the list.
            if( !isChildAdded )
               xmlFreeNode( outChild );
            else
               xmlAddChild( outNode, outChild );
         }
         else
         {
            // Non-blank, Non-data, non-list node
            std::string childRegPath( regPath );
            childRegPath += SEP;
            childRegPath += ( const char * ) inChild->name;

            xmlNodePtr outChild = xmlNewNode( NULL, inChild->name );
            xmlAddChild( outNode, outChild );

            if( !buildDoc( registry, childRegPath, inChild, outChild ))
               return false;
         }
      }
   }

   return true;
}

bool DocumentBuilder::toDocument( std::string& outDocStr )
{
   const std::string regRoot( "cpc_usage_report" );
   xmlDocPtr inDoc = NULL;
   xmlDocPtr outDoc = NULL;
   bool result = false;

   if( mRegistry == NULL )
      return result;

   LIBXML_TEST_VERSION;

   // Parse the template into an XML document
   size_t templateSize = strlen( Template );
   inDoc = xmlReadMemory( Template, templateSize, "CP Analytics", NULL, 0 );
   if( inDoc == NULL )
      goto cleanup;

   {
      // Get the template root node
      xmlNodePtr inRootNode = xmlDocGetRootElement( inDoc );
      if( inRootNode == NULL )
         goto cleanup;

      // Creates a new document, a node and set it as a root node
      outDoc = xmlNewDoc( BAD_CAST "1.0" );
      if( outDoc == NULL )
         goto cleanup;

      {
         xmlNodePtr outRootNode = xmlNewNode( NULL, BAD_CAST regRoot.c_str() );
         xmlDocSetRootElement( outDoc, outRootNode );

         // This is set here since the property depends on the format of the document,
         // not on the data model itself
         xmlNewProp( outRootNode, BAD_CAST "version", BAD_CAST DOC_VERSION );

         // Perform a depth-first traversal of the tree. At each node, fetch the
         // information related to each slot in the registry. A new XML document will
         // be built with the information at the same time as the template is
         // traversed.
         result = buildDoc( mRegistry, regRoot, inRootNode, outRootNode );
         if( result )
         {
            // Put the string form of the new XML document into outDocStr
            xmlChar *text = NULL;
            int size = 0;
            xmlDocDumpMemory( outDoc, &text, &size );
            outDocStr.assign(( const char * ) text, size );

            if( text != NULL )
               xmlFree( text );
         }
      }
   }
   
cleanup:

   if( inDoc != NULL )
   {
      xmlFreeDoc( inDoc );
      inDoc = NULL;
   }

   if( outDoc != NULL )
   {
      xmlFreeDoc( outDoc );
      outDoc = NULL;
   }

   return result;
}

