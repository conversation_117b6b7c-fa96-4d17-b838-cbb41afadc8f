#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_DIALOG_EVENT_MODULE == 1)

#include "SipDialogEventSubscriptionManagerImpl.h"
#include "DialogInfoDocumentHelper.h"
#include "dialogevent/SipDialogEventSubscriptionHandler.h"
#include "../event/SipEventSubscriptionCreationInfo.h"
#include "../cpm/CpmHelper.h"
#include "../util/DumFpCommand.h"
#include "../util/cpc_logger.h"


#include <resip/dum/DialogEventStateManager.hxx>

using namespace CPCAPI2::SipEvent;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_DIALOGEVENT

namespace CPCAPI2
{
namespace SipDialogEvent
{

SipDialogEventSubscriptionManagerImpl::SipDialogEventSubscriptionManagerImpl(SipDialogEventSubscriptionManagerInterface* iff, CPCAPI2::SipEvent::SipEventManagerInterface* sipEventIf, std::shared_ptr<AccountMap> parentMap, CPCAPI2::SipAccount::SipAccountImpl& account)
   : mAccount(account),
     mDocVersion(0),
     mDialogEventStateManager(NULL),
     mSipEventIf(sipEventIf),
     mIncludeSessionDescription(false),
     mParentMap(parentMap),
     mInterface(iff)
{
   account.registerAccountAwareFeature(this);
}

SipDialogEventSubscriptionManagerImpl::~SipDialogEventSubscriptionManagerImpl()
{
   mAccount.unregisterAccountAwareFeature(this);
}

int SipDialogEventSubscriptionManagerImpl::addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig)
{
   InfoLog(<< "SipDialogEventSubscriptionManagerImpl::addHandlers(..)");
   mDum = dum;

   return kSuccess;
}

int SipDialogEventSubscriptionManagerImpl::onDumBeingDestroyed()
{
   InfoLog(<< "SipDialogEventSubscriptionManagerImpl::onDumBeingDestroyed(..)");
   // Destroy the event state manager if created
   if (mDialogEventStateManager)
   {
      mDum->createDialogEventStateManager(NULL);
      mDialogEventStateManager = NULL;
   }

   mDum.reset();

   return kSuccess;
}

void SipDialogEventSubscriptionManagerImpl::release()
{
   if (std::shared_ptr<AccountMap> parentMap = mParentMap.lock())
   {
      parentMap->erase(mAccount.getHandle());
   }

   delete this;
}

int SipDialogEventSubscriptionManagerImpl::onNewSubscription(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NewSubscriptionEvent& args)
{
   if (args.subscriptionType == SipSubscriptionType_Incoming)
   {
      // Clean up the old state manager if there is one, this would be the case if we didn't get an onSubscriptionEnded
      if (mDialogEventStateManager)
      {
         mDum->createDialogEventStateManager(NULL);
         mDialogEventStateManager = NULL;
      }

      // Create the state manager
      assert(!mDialogEventStateManager);
      mDialogEventStateManager = mDum->createDialogEventStateManager(this);

      // Check whether the 'include-session-description' parameter has been specified in the Event header
      for (cpc::vector<Parameter>::const_iterator it = args.eventPackageParams.begin(); it != args.eventPackageParams.end(); it++)
      {
         if (it->name == DialogInfoDocumentHelper::INCLUDE_SDP_PARAM_NAME)
         {
            mIncludeSessionDescription = true;
            break;
         }
      }
   }

   // Create the event
   NewDialogEventSubscriptionEvent dialogEventArgs;
   dialogEventArgs.account = args.account;
   dialogEventArgs.remoteAddress = args.remoteAddress;
   dialogEventArgs.subscriptionType = args.subscriptionType;

   mInterface->fireEvent(cpcEvent(SipDialogEventSubscriptionHandler, onNewSubscription), mAccount.getHandle(), subscription, dialogEventArgs);

   return kSuccess;
}

int SipDialogEventSubscriptionManagerImpl::onSubscriptionEnded(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionEndedEvent& args)
{
   // Remove the state manager for incoming subscriptions only
   if (args.subscriptionType == SipSubscriptionType_Incoming)
   {
      mDum->createDialogEventStateManager(NULL);
      mDialogEventStateManager = NULL;
   }

   // Create the event
   DialogEventSubscriptionEndedEvent dialogEventArgs;
   dialogEventArgs.endReason = args.endReason;
   dialogEventArgs.subscriptionType = args.subscriptionType;
   dialogEventArgs.statusCode = args.statusCode;
   dialogEventArgs.retryAfter = args.retryAfter;
   dialogEventArgs.initialSubscribe = args.initialSubscribe;
   dialogEventArgs.isNotifyTerminated = args.isNotifyTerminated;
   dialogEventArgs.remoteAddress = args.remoteAddress;
   dialogEventArgs.reason = args.reason;

   mInterface->fireEvent(cpcEvent(SipDialogEventSubscriptionHandler, onSubscriptionEnded), mAccount.getHandle(), subscription, dialogEventArgs);

   return kSuccess;
}

int SipDialogEventSubscriptionManagerImpl::onIncomingEventState(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingEventStateEvent& args)
{
   bool sharedAppearanceDialogExtensionsEnabled = isSharedAppearanceDialogExtensionsEnabled(subscription);

   IncomingDialogInfoEvent dialogEventArgs;
   DialogInfoDocumentHelper::fromXml(args.eventState.contentUTF8.c_str(), dialogEventArgs.dialogInfoDoc, sharedAppearanceDialogExtensionsEnabled);

   mInterface->fireEvent(cpcEvent(SipDialogEventSubscriptionHandler, onIncomingDialogInfo), mAccount.getHandle(), subscription, dialogEventArgs);

   return kSuccess;
}

int SipDialogEventSubscriptionManagerImpl::onIncomingResourceList(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingResourceListEvent& args)
{
   bool sharedAppearanceDialogExtensionsEnabled = isSharedAppearanceDialogExtensionsEnabled(subscription);

   DialogResourceListEvent dialogEventArgs;
   cpc::vector<SipEventResource>::const_iterator itRes = args.resources.begin();
   for (; itRes != args.resources.end(); itRes++)
   {
      SipEventResource eventResource = *itRes;
      DialogResourceState resourceState;
      resourceState.uri = eventResource.uri;

      if (!eventResource.instances.empty())
      {
         // we don't support forking yet; use first instance
         resourceState.state = eventResource.instances[0].subscriptionState;
         DialogInfoDocumentHelper::fromXml(eventResource.instances[0].contentUtf8, resourceState.dialogInfoDoc, sharedAppearanceDialogExtensionsEnabled);
      }
      dialogEventArgs.resources.push_back(resourceState);
   }

   if (!dialogEventArgs.resources.empty())
   {
      mInterface->fireEvent(cpcEvent(SipDialogEventSubscriptionHandler, onDialogResourceListUpdated), mAccount.getHandle(), subscription, dialogEventArgs);
   }

   return kSuccess;
}

int SipDialogEventSubscriptionManagerImpl::onSubscriptionStateChanged(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionStateChangedEvent& args)
{
   // Create the event
   DialogEventSubscriptionStateChangedEvent dialogEventArgs;
   dialogEventArgs.subscriptionState = args.subscriptionState;

   mInterface->fireEvent(cpcEvent(SipDialogEventSubscriptionHandler, onSubscriptionStateChanged), mAccount.getHandle(), subscription, dialogEventArgs);

   return kSuccess;
}

int SipDialogEventSubscriptionManagerImpl::onNotifySuccess(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifySuccessEvent& args)
{
   return kSuccess;
}

int SipDialogEventSubscriptionManagerImpl::onNotifyFailure(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifyFailureEvent& args)
{
   // Create the event
   NotifyDialogInfoFailureEvent dialogEventArgs;
   dialogEventArgs.sipResponseCode = args.sipResponseCode;

   mInterface->fireEvent(cpcEvent(SipDialogEventSubscriptionHandler, onNotifyDialogInfoFailure), mAccount.getHandle(), subscription, dialogEventArgs);

   return kSuccess;
}

int SipDialogEventSubscriptionManagerImpl::onError(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::ErrorEvent& args)
{
   // Create the event
   ErrorEvent evt;
   evt.errorText = args.errorText;

   mInterface->fireEvent(cpcEvent(SipDialogEventSubscriptionHandler, onError), mAccount.getHandle(), subscription, evt);

   return kSuccess;
}

void SipDialogEventSubscriptionManagerImpl::onTrying(const resip::TryingDialogEvent& evt)
{
   // Create the dialog-info document with full state info
   DialogInfoDocument doc = createFullStateDialogInfo();

   // Send the dialog-info document
   sendDialogInfo(doc);
}

void SipDialogEventSubscriptionManagerImpl::onProceeding(const resip::ProceedingDialogEvent& evt)
{
   // Create the dialog-info document with full state info
   DialogInfoDocument doc = createFullStateDialogInfo();

   // Send the dialog-info document
   sendDialogInfo(doc);
}

void SipDialogEventSubscriptionManagerImpl::onEarly(const resip::EarlyDialogEvent& evt)
{
   // Create the dialog-info document with full state info
   DialogInfoDocument doc = createFullStateDialogInfo();

   // Send the dialog-info document
   sendDialogInfo(doc);
}

void SipDialogEventSubscriptionManagerImpl::onConfirmed(const resip::ConfirmedDialogEvent& evt)
{
   // Create the dialog-info document with full state info
   DialogInfoDocument doc = createFullStateDialogInfo();

   // Send the dialog-info document
   sendDialogInfo(doc);
}

void SipDialogEventSubscriptionManagerImpl::onTerminated(const resip::TerminatedDialogEvent& evt)
{
   // Create the dialog-info document with full state info
   DialogInfoDocument doc = createFullStateDialogInfo();

   // Set the dialog state's reason and code attributes in the document
   DialogInfoDocumentHelper::setDialogStateReasonAndCode(doc, evt);

   // Send the dialog-info document
   sendDialogInfo(doc);
}

void SipDialogEventSubscriptionManagerImpl::onMultipleEvents(const resip::MultipleEventDialogEvent& evt)
{
   // Create the dialog-info document with full state info
   DialogInfoDocument doc = createFullStateDialogInfo();

   // Set the dialog state's reason and code attributes in the document for all terminated dialogs
   DialogInfoDocumentHelper::setDialogStateReasonAndCode(doc, evt);

   // Send the dialog-info document
   sendDialogInfo(doc);
}

DialogInfoDocument SipDialogEventSubscriptionManagerImpl::createEmptyDialogInfo()
{
   return DialogInfoDocumentHelper::createEmptyDocument(mDocVersion++, getEntity());
}

SipEventState SipDialogEventSubscriptionManagerImpl::createEventState(SipDialogEventSubscriptionHandle subscription, const DialogInfoDocument& dialogInfoDoc)
{
   bool sharedAppearanceDialogExtensionsEnabled = isSharedAppearanceDialogExtensionsEnabled(subscription);
   return DialogInfoDocumentHelper::createEventState(dialogInfoDoc, mIncludeSessionDescription, sharedAppearanceDialogExtensionsEnabled);
}

DialogInfoDocument SipDialogEventSubscriptionManagerImpl::createFullStateDialogInfo()
{
   // Generate the dialog-info document to publish
   resip::DialogEventStateManager::DialogEventInfos dialogInfos = mDialogEventStateManager->getDialogEventInfo();
   DialogInfoDocument dialogInfoDoc;
   DialogInfoDocumentHelper::fromDialogEventInfo(mDocVersion++, getEntity(), DialogInfoDocumentState_Full, dialogInfos, dialogInfoDoc);
   return dialogInfoDoc;
}

void SipDialogEventSubscriptionManagerImpl::sendDialogInfo(const DialogInfoDocument& dialogInfoDoc)
{
   // Notify all the subscribers with the dialog-info document
   cpc::vector<SipEventSubscriptionHandle> subscriptions = mSipEventIf->getSubscriptions(mAccount.getHandle());
   for (cpc::vector<SipEventSubscriptionHandle>::const_iterator it = subscriptions.begin(); it != subscriptions.end(); it++)
   {
      SipDialogEventSubscriptionHandle subscription = *it;

      // Create the event state
      SipEventState eventState = createEventState(subscription, dialogInfoDoc);

      // Notify
      mSipEventIf->notify(subscription, eventState);
   }
}

cpc::string SipDialogEventSubscriptionManagerImpl::getEntity()
{
   return CPM::CpmHelper::getUri(mAccount).toString().c_str();
}

bool SipDialogEventSubscriptionManagerImpl::isSharedAppearanceDialogExtensionsEnabled(SipDialogEventSubscriptionHandle subscription)
{
   // Get the creation info associated with the subscription
   SipEventSubscriptionCreationInfo* creationInfo = mSipEventIf->getCreationInfo(subscription);   

   if (creationInfo != NULL)
   {
      return creationInfo->sharedCallAppearanceExtensionsEnabled;
   }

   return false;
}   

}
}

#endif // CPCAPI2_BRAND_SIP_DIALOG_EVENT_MODULE
