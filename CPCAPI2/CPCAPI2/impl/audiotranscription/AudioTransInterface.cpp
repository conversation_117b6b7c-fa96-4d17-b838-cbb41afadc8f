#include "brand_branded.h"

#if (CPCAPI2_BRAND_AUDIO_TRANS_MODULE == 1)
#include "cpcapi2utils.h"
#include "AudioTransInterface.h"
#include "AudioTransImpl.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

using namespace resip;

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

namespace CPCAPI2
{
namespace AudioTrans
{
std::atomic<AudioTransHandle> AudioTransHandleFactory::sNextHandle(1);

AudioTransInterface::AudioTransInterface(Phone* phone)
   : EventSource<AudioTransHandle, AudioTransHandler, AudioTransSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
     mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   mPhone->addRefImpl();
}

AudioTransInterface::~AudioTransInterface()
{
   mPhone->releaseImpl();
}

void AudioTransInterface::Release()
{
   delete this;
}

void AudioTransInterface::post(ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void AudioTransInterface::fireEventInternal(AudioTransHandle h, const AudioTranscriptionResult& evt)
{
   fireEvent(cpcFunc(AudioTransHandler::onTranscriptionResult), h, evt);
}

int AudioTransInterface::startAudioTransServer()
{
   postToSdkThread(resip::resip_bind(&AudioTransInterface::startAudioTransServerImpl, this));
   return 0;
}

void AudioTransInterface::startAudioTransServerImpl()
{
}

int AudioTransInterface::stopAudioTransServer()
{
   postToSdkThread(resip::resip_bind(&AudioTransInterface::stopAudioTransServerImpl, this));
   return 0;
}

void AudioTransInterface::stopAudioTransServerImpl()
{
}

int AudioTransInterface::setAudioTransServer(AudioTransManager* masterAudioTransManager)
{
   postToSdkThread(resip::resip_bind(&AudioTransInterface::setAudioTransServerImpl, this, masterAudioTransManager));
   return 0;
}

void AudioTransInterface::setAudioTransServerImpl(AudioTransManager* masterMgr)
{
}

AudioTransHandle AudioTransInterface::createAudioTranscriptionSession()
{
   AudioTransHandle h = AudioTransHandleFactory::getNext();
   postToSdkThread(resip::resip_bind(&AudioTransInterface::createAudioTransImpl, this, h));
   return h;
}

void AudioTransInterface::createAudioTransImpl(AudioTransHandle videoStream)
{
   AudioTransImpl* pimpl = new AudioTransImpl(mPhone, this, videoStream);
   mInstMap[videoStream] = pimpl;
}

int AudioTransInterface::setAudioTransSettings(AudioTransHandle videoStream, const AudioTransSettings& settings)
{
   postToSdkThread(resip::resip_bind(&AudioTransInterface::setAudioTransSettingsImpl, this, videoStream, settings));
   return 0;
}

void AudioTransInterface::setAudioTransSettingsImpl(AudioTransHandle videoStream, const AudioTransSettings& settings)
{
   AudioTransInterface::InstanceMap::iterator it = mInstMap.find(videoStream);
   if (it != mInstMap.end())
   {
      it->second->setAudioTransSettings(settings);
   }
}

int AudioTransInterface::setAudioTransHandler(AudioTransHandle videoStream, AudioTransHandler* handler)
{
   return setAppHandler(videoStream, handler);
}

int AudioTransInterface::startTranscription(AudioTransHandle videoStream)
{
   postToSdkThread(resip::resip_bind(&AudioTransInterface::startAudioTransImpl, this, videoStream));
   return 0;
}

void AudioTransInterface::startAudioTransImpl(AudioTransHandle videoStream)
{
   AudioTransInterface::InstanceMap::iterator it = mInstMap.find(videoStream);
   if (it != mInstMap.end())
   {
      it->second->startTranscription();
   }
}

int AudioTransInterface::stopTranscription(AudioTransHandle videoStream)
{
   postToSdkThread(resip::resip_bind(&AudioTransInterface::stopAudioTransImpl, this, videoStream));
   return 0;
}

void AudioTransInterface::stopAudioTransImpl(AudioTransHandle videoStream)
{
   AudioTransInterface::InstanceMap::iterator it = mInstMap.find(videoStream);
   if (it != mInstMap.end())
   {
      it->second->stopTranscription();
      delete it->second;
      mInstMap.erase(it);
   }
}



}
}

#endif
