#pragma once

#if !defined(CPCAPI2_CLOUD_SERVICE_CONFIG_INTERFACE_H)
#define CPCAPI2_CLOUD_SERVICE_CONFIG_INTERFACE_H

#include "cpcapi2defs.h"
#include "orchestration_server/OrchestrationServer.h"
#include "cloudserviceconfig/CloudServiceConfig.h"
#include "phone/Cpcapi2EventSource.h"

#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>
#include <rutil/DeadlineTimer.hxx>

#include <map>
#include <thread>

#if !defined(_WIN32) && !defined(ANDROID)
#include <mm_malloc.h>
#endif

namespace CPCAPI2
{

class PhoneInterface;

namespace CloudServiceConfig
{

class CloudServiceConfigSyncHandler {};

class CloudServiceConfigInterface : public CloudServiceConfigManager,
                                    public PhoneModule,
                                    public CPCAPI2::EventSource<CloudServiceConfigHandle, CloudServiceConfigHandler, CloudServiceConfigSyncHandler>,
                                    public resip::DeadlineTimerHandler
{

public:

   CloudServiceConfigInterface(Phone* phone);
   virtual ~CloudServiceConfigInterface();

   void* operator new(size_t i)
   {
#if defined(ANDROID)
      return memalign(folly::hardware_destructive_interference_size, i);
#else
      return _mm_malloc(i, folly::hardware_destructive_interference_size);
#endif
   }

   void operator delete(void* p)
   {
#if defined(ANDROID)
      free(p);
#else
      _mm_free(p);
#endif
   }

   // PhoneModule
   virtual void Release() OVERRIDE;

   // CloudServiceConfigManager
   virtual int setServerInfo(const ServiceConfigSettings& serviceConfigSettings, const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo) OVERRIDE;
   virtual int setServerTtl(const ServiceConfigSettings& serviceConfigSettings, const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo) OVERRIDE;
   virtual int setHandler(CloudServiceConfigHandler* handler) OVERRIDE;
   
#ifdef CPCAPI2_AUTO_TEST
   virtual int stopTtlService() OVERRIDE;
   virtual int removeServerInfo(const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo) OVERRIDE;
#endif

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   void post(resip::ReadCallbackBase* f);

private:

   int setServerInfoImpl(const ServiceConfigSettings& serviceConfigSettings, const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo);
   int setServerTtlImpl(const ServiceConfigSettings& serviceConfigSettings, const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo);
   
#ifdef CPCAPI2_AUTO_TEST
   int stopTtlServiceImpl();
   int removeServerInfoImpl(const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo);
#endif

   int doOrchSetServerInfo_Local(const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo);
   int doOrchSetServerTtl_Local(const ServiceConfigSettings& serviceConfigSettings, const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo);

private:

   PhoneInterface* mPhone;
   CPCAPI2::OrchestrationServer::OrchestrationServer* mOrchServer;
   resip::DeadlineTimer<resip::MultiReactor> mTimer;

   struct ServerRegInfo
   {
      ServiceConfigSettings settings;
      CPCAPI2::OrchestrationServer::ServerInfo serverInfo;
   };
   std::vector<ServerRegInfo> mServerRegistrations;

};

std::ostream& operator<<(std::ostream& os, const CPCAPI2::CloudServiceConfig::ServiceConfigSettings& settings);

}

}

#endif // CPCAPI2_CLOUD_SERVICE_CONFIG_INTERFACE_H
