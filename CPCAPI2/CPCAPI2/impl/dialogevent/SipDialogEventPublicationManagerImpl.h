#pragma once

#if !defined(CPCAPI2_SIP_DIALOG_EVENT_PUBLICATION_MANAGER_IMPL_H)
#define CPCAPI2_SIP_DIALOG_EVENT_PUBLICATION_MANAGER_IMPL_H

#include "cpcapi2defs.h"
#include "dialogevent/SipDialogEventPublicationManagerInterface.h"
#include "event/SipEventPublicationHandler.h"
#include "../account/SipAccountImpl.h"
#include "../account/SipAccountAwareFeature.h"

namespace CPCAPI2
{
namespace SipDialogEvent
{
class SipDialogEventPublicationHandler;

class SipDialogEventPublicationManagerImpl : public CPCAPI2::SipAccount::SipAccountAwareFeature,
                                             public CPCAPI2::EventSyncHandler<CPCAPI2::SipEvent::SipEventPublicationHandler>
{
public:
   typedef std::map<SipAccount::SipAccountHandle, SipDialogEventPublicationManagerImpl*> AccountMap;
   SipDialogEventPublicationManagerImpl(SipDialogEventPublicationManagerInterface* iff, CPCAPI2::SipEvent::SipEventPublicationManagerInterface* sipPublicationIf, std::shared_ptr<AccountMap> parentMap, CPCAPI2::SipAccount::SipAccountImpl& account);
   virtual ~SipDialogEventPublicationManagerImpl();

private:
   CPCAPI2::SipDialogEvent::SipDialogEventPublicationManagerInterface* mInterface;
   CPCAPI2::SipEvent::SipEventPublicationManagerInterface* mSipPublicationIf;
   std::weak_ptr<AccountMap> mParentMap;
   CPCAPI2::SipAccount::SipAccountImpl& mAccount;

   // SipAccountAwareFeature
   virtual int adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile) OVERRIDE { return kSuccess; }
   virtual int addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig) OVERRIDE { return kSuccess; }
   virtual int onDumBeingDestroyed() OVERRIDE { return kSuccess; }
   virtual void release() OVERRIDE;

   // SipEventPublicationHandler interface
   virtual int onPublicationSuccess(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationSuccessEvent& args) OVERRIDE;
   virtual int onPublicationFailure(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationFailureEvent& args) OVERRIDE;
   virtual int onPublicationRemove(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationRemoveEvent & args) OVERRIDE;
   virtual int onError(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationErrorEvent& args) OVERRIDE;
};

}
}

#endif // CPCAPI2_SIP_DIALOG_EVENT_PUBLICATION_MANAGER_IMPL_H
