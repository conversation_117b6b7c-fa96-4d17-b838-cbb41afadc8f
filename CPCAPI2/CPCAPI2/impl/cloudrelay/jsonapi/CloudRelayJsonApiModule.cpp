#include "brand_branded.h"

#include <interface/experimental/cloudrelay/CloudRelayJsonProxy.h>
#include <interface/experimental/cloudrelay/CloudRelayJsonApi.h>

#if (CPCAPI2_BRAND_CLOUD_RELAY_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "phone/PhoneInterface.h"
#include "CloudRelayJsonServerInterface.h"
#endif
#if (CPCAPI2_BRAND_CLOUD_RELAY_JSON_PROXY_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "phone/PhoneInterface.h"
#include "CloudRelayJsonProxyInterface.h"
#endif

namespace CPCAPI2
{
   namespace CloudRelay
   {
      CloudRelayJsonApi* CloudRelayJsonApi::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_CLOUD_RELAY_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<CloudRelayJsonServerInterface>(phone, "CloudRelayJsonApi");
#else
         return NULL;
#endif
      }

      CloudRelayManagerJsonProxy* CloudRelayManagerJsonProxy::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_CLOUD_RELAY_JSON_PROXY_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<CloudRelayJsonProxyInterface>(phone, "CloudRelayManagerJsonProxy");
#else
         return NULL;
#endif
      }
   }
}
