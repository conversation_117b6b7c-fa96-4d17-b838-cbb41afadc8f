#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_CHAT_MODULE==1 || CPCAPI2_BRAND_IM_MODULE==1 || CPCAPI2_BRAND_XMPP_CHAT_MODULE==1 || CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
#include "cpcapi2utils.h"
#include "CpmHelper.h"
#include "ImdnDocument.h"
#include "../util/cpc_logger.h"
#include "../util/IpHelpers.h"

#include <resip/stack/ExtensionParameter.hxx>
#include <rutil/Timer.hxx>
#include <rutil/DnsUtil.hxx>

#include <msrp_uri.h>
#include <msrp_stack.h>
#include <utils/msrp_string.h>
#include <utils/msrp_mem.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_CHAT

namespace CPCAPI2
{
namespace CPM
{

const char* CpmHelper::MESSAGE_MEDIA_TYPE = "message";
const char* CpmHelper::ACCEPT_TYPES_ATTRIBUTE = "accept-types";
const char* CpmHelper::MSRP_PATH = "path";
const char* CpmHelper::CPM_FEATURE_TAG_NAME = "+g.3gpp.icsi-ref";
const char* CpmHelper::CPM_FEATURE_TAG_VALUE_PREFIX = "urn%3Aurn-7%3A3";
const cpc::string CpmHelper::IMDN_POSITIVE_DELIVERY = "positive-delivery";
const cpc::string CpmHelper::IMDN_NEGATIVE_DELIVERY = "negative-delivery";
const cpc::string CpmHelper::IMDN_DISPLAY = "display";
const cpc::string CpmHelper::IMDN_NS = "imdn <urn:ietf:params:imdn>";
const resip::Mime CpmHelper::SDP_CONTENT_TYPE = resip::Mime("application", "sdp");
const resip::Mime CpmHelper::CPIM_CONTENT_TYPE = resip::Mime("message", "cpim");
const resip::Mime CpmHelper::IMDN_CONTENT_TYPE = resip::Mime("message", "imdn+xml");
const resip::Mime CpmHelper::TEXT_PLAIN_CONTENT_TYPE = resip::Mime("text", "plain");
const resip::Mime CpmHelper::TEXT_XML_CONTENT_TYPE = resip::Mime("text", "xml");
const resip::Mime CpmHelper::TEXT_HTML_CONTENT_TYPE = resip::Mime("text", "html");
const resip::Mime CpmHelper::IMAGE_JPEG_CONTENT_TYPE = resip::Mime("image", "jpeg");
const resip::Mime CpmHelper::IMAGE_PNG_CONTENT_TYPE = resip::Mime("image", "png");

tm CpmHelper::getCurrentDateTime()
{
   time_t rawtime;
   time(&rawtime);
   tm timeinfo;
#ifdef WIN32
   timeinfo = *localtime(&rawtime);
#else
   localtime_r(&rawtime, &timeinfo);
#endif
   return timeinfo;
   
}

CpimMessage CpmHelper::createCpimMessage(const cpc::string& messageId, const resip::Uri& fromUri, const resip::Uri& toUri, const tm& datetime, const resip::Mime& contentType, const resip::Data& messageContent, const cpc::vector<DispositionNotificationType>& dispositionNotifications)
{   
   HeaderList headers;

   // Set the From header
   cpc::string fromUriStr = fromUri.toString().c_str();
   CpimHeader fromHeader(CpimHeader::FROM, fromUriStr);
   headers.push_back(fromHeader);

   // Set the To header
   cpc::string toUriStr = toUri.toString().c_str();
   CpimHeader toHeader(CpimHeader::TO, toUriStr);
   headers.push_back(toHeader);

   // Set the IMDN NS header
   CpimHeader imdnNsHeader(CpimHeader::NS, IMDN_NS);
   headers.push_back(imdnNsHeader);

   // Set the IMDN Message-ID header
   CpimHeader imdnMessageIdHeader(CpimHeader::IMDN_MESSAGE_ID, messageId);
   headers.push_back(imdnMessageIdHeader);

   // Set the DateTime header
   CpimHeader dateTimeHeader = createDateTimeHeader(datetime);
   headers.push_back(dateTimeHeader);

   // Set the IMDN Disposition-Notification header
   CpimHeader disposNotifHeader = createDispositionNotificationHeader(dispositionNotifications);
   headers.push_back(disposNotifHeader);

   HeaderList contentHeaders;

   // Set the Content-Type header
   CpimHeader contentTypeHeader = createContentTypeHeader(contentType);
   contentHeaders.push_back(contentTypeHeader);

   // Create the CPIM message
   CpimMessage cpimMessage(headers, contentHeaders, messageContent);
   return cpimMessage;
}

CpimMessage CpmHelper::createCpimMessage(const cpc::string& messageId, const resip::Uri& fromUri, const resip::Uri& toUri, const cpc::string& origMessageId, const cpc::string& origDateTimeStr, MessageType notificationType, int notificationStatus)
{   
   HeaderList headers;

   // Set the From header
   cpc::string fromUriStr = fromUri.toString().c_str();
   CpimHeader fromHeader(CpimHeader::FROM, fromUriStr);
   headers.push_back(fromHeader);

   // Set the To header
   cpc::string toUriStr = toUri.toString().c_str();
   CpimHeader toHeader(CpimHeader::TO, toUriStr);
   headers.push_back(toHeader);

   // Set the IMDN NS header
   CpimHeader imdnNsHeader(CpimHeader::NS, IMDN_NS);
   headers.push_back(imdnNsHeader);

   // Set the IMDN Message-ID header
   CpimHeader imdnMessageIdHeader(CpimHeader::IMDN_MESSAGE_ID, messageId);
   headers.push_back(imdnMessageIdHeader);

   HeaderList contentHeaders;

   // Set the Content-Type header
   CpimHeader contentTypeHeader(CpimHeader::CONTENT_TYPE, "message/imdn+xml");
   contentHeaders.push_back(contentTypeHeader);

   // Set the Content-Disposition header
   CpimHeader contentDispositionHeader(CpimHeader::CONTENT_DISPOSITION, "notification");
   contentHeaders.push_back(contentDispositionHeader);

   // Create the content (IMDN)
   ImdnDocument imdnDocument;
   imdnDocument.setMessageId(origMessageId);
   imdnDocument.setDateTime(origDateTimeStr);
   imdnDocument.setRecipientUri(fromUriStr);
   imdnDocument.setOriginalRecipientUri(fromUriStr);
   if (notificationType == MessageType_MessageDeliveredNotification)
   {
      MessageDeliveryStatus deliveryStatus = (MessageDeliveryStatus) notificationStatus;
      imdnDocument.setDeliveryNotificationStatus(deliveryStatus);
   }
   else if (notificationType == MessageType_MessageDisplayedNotification)
   {
      MessageDisplayStatus displayStatus = (MessageDisplayStatus) notificationStatus;
      imdnDocument.setDisplayNotificationStatus(displayStatus);
   }
   else
   {
      // Unsupported notification type
      assert(false);
   }

   // Generate the XML content
   cpc::string content = imdnDocument.toString();

   // Create the CPIM message
   resip::Data contentBytes = content.c_str();
   CpimMessage cpimMessage(headers, contentHeaders, contentBytes);
   return cpimMessage;
}

std::list<resip::Data> CpmHelper::getLines(const resip::Data& str, const resip::Data& delim)
{
   std::list<resip::Data> lines;

   size_t begin = 0;
   while(begin < str.size())
   {
      // Find the end of the line
      resip::Data::size_type end = str.find(delim, begin);
      if (end == resip::Data::npos)
      {
         end = str.size();
      }

      // Extract the content of the line
      resip::Data line = str.substr(begin, end - begin);
      lines.push_back(line);

      // Move to the next line
      begin = end + delim.size();
   }
   
   return lines;
}

void CpmHelper::extractCpimMessage(CpimMessage cpimMessage, cpc::string& messageId, cpc::string& origMessageId, cpc::string& datetimeString, MessageType& notificationType, int& notificationStatus)
{
   // the IMDN Message-ID header
   CpimHeader imdnMessageIdHeader = cpimMessage.getHeader(CpimHeader::IMDN_MESSAGE_ID);
   messageId = imdnMessageIdHeader.getValue();

   // Retrieve the XML content
   resip::Data cpimContentBytes = cpimMessage.getContent();
   cpc::string cpimContent = cpimContentBytes.c_str();
   ImdnDocument imdnDocument = ImdnDocument::parse(cpimContent);

   // Extract the original message ID
   origMessageId = imdnDocument.getMessageId();

   // Get the original date/time
   datetimeString = imdnDocument.getDateTime();

   // Check the type of notification
   if (imdnDocument.isDeliveryNotification())
   {
      // IMDN Delivery notification
      notificationType = MessageType_MessageDeliveredNotification;

      // Get the delivery notification status
      notificationStatus = imdnDocument.getDeliveryNotificationStatus();
   }
   else if (imdnDocument.isDisplayNotification())
   {      
      // IMDN Display notification
      notificationType = MessageType_MessageDisplayedNotification;

      // Get the display notification status
      notificationStatus = imdnDocument.getDisplayNotificationStatus();
   }
   else
   {
      // Unsupported notification type
      assert(false);
   }
}

void CpmHelper::extractCpimMessage(CpimMessage cpimMessage, cpc::string& messageId, cpc::string& fromUriStr, cpc::string& toUriStr, cpc::string& dateTimeValue, tm& datetime, resip::Mime& contentType, resip::Data& messageContent, cpc::vector<DispositionNotificationType>& dispositionNotifications)
{
   // Get the From header
   CpimHeader fromHeader = cpimMessage.getHeader(CpimHeader::FROM);
   fromUriStr = fromHeader.getValue();

   // Get the To header
   CpimHeader toHeader = cpimMessage.getHeader(CpimHeader::TO);
   toUriStr = toHeader.getValue();

   // the IMDN Message-ID header
   CpimHeader imdnMessageIdHeader = cpimMessage.getHeader(CpimHeader::IMDN_MESSAGE_ID);
   messageId = imdnMessageIdHeader.getValue();

   // Get the DateTime header
   CpimHeader dateTimeHeader = cpimMessage.getHeader(CpimHeader::DATETIME);
   extractDateTimeHeader(dateTimeHeader, dateTimeValue, datetime);
   
   // Get the Content-Type header
   CpimHeader contentTypeHeader = cpimMessage.getContentHeader(CpimHeader::CONTENT_TYPE);
   contentType = extractContentTypeHeader(contentTypeHeader);

   // Get the message
   messageContent = cpimMessage.getContent();

   // Get the requested disposition notifications
   CpimHeader imdnDispositionNotificationHeader = cpimMessage.getHeader(CpimHeader::IMDN_DISPOSITION_NOTIFICATION);
   dispositionNotifications = extractDispositionNotificationHeader(imdnDispositionNotificationHeader);
}

CpimHeader CpmHelper::createDateTimeHeader(const tm& datetime)
{
   cpc::string dateTimeStr = createDateTimeString(datetime);
   return CpimHeader(CpimHeader::DATETIME, dateTimeStr);
}

CpimHeader CpmHelper::createDispositionNotificationHeader(const cpc::vector<DispositionNotificationType>& dispositionNotifications)
{
   cpc::string imdnDispositionNotificationValue;

   for (cpc::vector<DispositionNotificationType>::const_iterator iter = dispositionNotifications.begin(); iter != dispositionNotifications.end(); iter++)
   {
      DispositionNotificationType dispositionNotification = *iter;
      switch (dispositionNotification)
      {
      case DispositionNotificationType_PositiveDelivery:
         imdnDispositionNotificationValue.append(IMDN_POSITIVE_DELIVERY);
         break;
      case DispositionNotificationType_NegativeDelivery:
         imdnDispositionNotificationValue.append(IMDN_NEGATIVE_DELIVERY);
         break;
      case DispositionNotificationType_Display:
         imdnDispositionNotificationValue.append(IMDN_DISPLAY);
         break;
      default:
         // Unsupported disposition notification type
         assert(false);
         break;
      }
   }

   return CpimHeader(CpimHeader::IMDN_DISPOSITION_NOTIFICATION, imdnDispositionNotificationValue);
}

cpc::vector<DispositionNotificationType> CpmHelper::extractDispositionNotificationHeader(CpimHeader imdnDispositionNotificationHeader)
{
   cpc::vector<DispositionNotificationType> dispositionNotifications;

   cpc::string imdnDispositionNotificationValue = imdnDispositionNotificationHeader.getValue();
   if (imdnDispositionNotificationValue.find(IMDN_POSITIVE_DELIVERY) != cpc::string::npos)
   {
      dispositionNotifications.push_back(DispositionNotificationType_PositiveDelivery);
   }

   if (imdnDispositionNotificationValue.find(IMDN_NEGATIVE_DELIVERY) != cpc::string::npos)
   {
      dispositionNotifications.push_back(DispositionNotificationType_NegativeDelivery);
   }

   if (imdnDispositionNotificationValue.find(IMDN_DISPLAY) != cpc::string::npos)
   {
      dispositionNotifications.push_back(DispositionNotificationType_Display);
   }

   return dispositionNotifications;
}

resip::Mime CpmHelper::extractContentTypeHeader(CpimHeader contentTypeHeader)
{
   cpc::string contentTypeStr = contentTypeHeader.getValue();
   return stringToContentType(contentTypeStr);
}

void CpmHelper::extractDateTimeHeader(CpimHeader dateTimeHeader, cpc::string& dateTimeValue, tm& datetime)
{
   dateTimeValue = dateTimeHeader.getValue();
   datetime = extractDateTime(dateTimeValue); 
}

tm CpmHelper::extractDateTime(const cpc::string& dateTimeValue)
{
   int year, month, day, hour, minute, second, offsetHour, offsetMin;
   char offsetPrefix;
   sscanf(dateTimeValue.c_str(), "%d-%d-%dT%d:%d:%d%c%d:%d", &year, &month, &day, &hour, &minute, &second, &offsetPrefix, &offsetHour, &offsetMin);
   return createDateTime(year, month, day, hour, minute, second, offsetPrefix, offsetHour, offsetMin);
}

cpc::string CpmHelper::createDateTimeString(const tm& datetime)
{
   char timeOffsetStr[128];
   // TODO: timezone on ThreadX
#if _MSC_VER >= 1900
   long timezone = 0;
   _get_timezone(&timezone);
#endif
#ifndef __THREADX
   if (timezone == 0)
   {
      snprintf(timeOffsetStr, 128, "Z");
   }
   else if (timezone > 0)
   {
      snprintf(timeOffsetStr, 128, "-%02d:%02d", ((int)timezone / 60) / 60, ((int)timezone / 60) % 60);
   }
   else if (timezone < 0)
   {
      snprintf(timeOffsetStr, 128, "+%02d:%02d", (-(int)timezone / 60) / 60, (-(int)timezone / 60) % 60);
   }
#endif
   char datetimeStr[128];
   snprintf(datetimeStr, 128, "%04d-%02d-%02dT%02d:%02d:%02d%s", datetime.tm_year + 1900, datetime.tm_mon + 1, datetime.tm_mday, datetime.tm_hour, datetime.tm_min, datetime.tm_sec, timeOffsetStr);

   return datetimeStr;
}

CpimHeader CpmHelper::createContentTypeHeader(const resip::Mime& contentType)
{
   cpc::string contentTypeStr = contentTypeToString(contentType);
   return CpimHeader(CpimHeader::CONTENT_TYPE, contentTypeStr);
}

xmlNodePtr CpmHelper::getChildNode(xmlNodePtr node, const char* name)
{
   for (xmlNodePtr child = node->children; child; child = child->next)
   {
      if (child->type == XML_ELEMENT_NODE && (!name || !strcmp((const char *) child->name, name)))
      {
         return child;
      }
   }

   return NULL;
}

cpc::string CpmHelper::getChildNodeText(xmlNodePtr parent, const char* name)
{
   xmlNodePtr node = getChildNode(parent, name);
   if (!node)
   {
      return "";
   }

   for (xmlNodePtr child = node->children; child; child = child->next)
   {
      if (child->type == XML_TEXT_NODE)
      {
         if (child->content)
         {
            return (const char*) child->content;
         }
         else
         {
            return "";
         }
      }
   }

   return "";
}

resip::Mime CpmHelper::stringToContentType(const cpc::string& mimeTypeStr)
{
   // Extract the content type (i.e. type/sub-type) from the MIME type specified
   const size_t splitIdx = mimeTypeStr.find("/");
   if (splitIdx == std::string::npos)
   {
      // Could not find the content type separator (/)
      assert(false);
   }

   // Return the extracted content type
   cpc::string type = mimeTypeStr.substr(0, splitIdx);
   cpc::string subtype = mimeTypeStr.substr(splitIdx+1);
   return resip::Mime(type.c_str(), subtype.c_str());
}

CPM::MimeType CpmHelper::contentTypeToMimeType(const resip::Mime& contentType)
{
   CPM::MimeType mimeType = MimeType_TextPlain;

   if (contentType == TEXT_PLAIN_CONTENT_TYPE)
   {
      mimeType = MimeType_TextPlain;
   } 
   else if (contentType == TEXT_XML_CONTENT_TYPE)
   {
      mimeType = MimeType_TextXml;
   }
   else if (contentType == TEXT_HTML_CONTENT_TYPE)
   {
      mimeType = MimeType_TextHtml;
   }
   else if (contentType == IMAGE_JPEG_CONTENT_TYPE)
   {
      mimeType = MimeType_ImageJpeg;
   }
   else if (contentType == IMAGE_PNG_CONTENT_TYPE)
   {
      mimeType = MimeType_ImagePng;
   }
   else
   {
      // MIME type not supported
      assert(false);
   }

   return mimeType;
}

resip::Mime CpmHelper::mimeTypeToContentType(CPM::MimeType mimeType)
{
   resip::Mime contentType;

   if (mimeType == MimeType_TextPlain)
   {
      contentType = TEXT_PLAIN_CONTENT_TYPE;
   } 
   else if (mimeType == MimeType_TextXml)
   {
      contentType = TEXT_XML_CONTENT_TYPE;
   }
   else if (mimeType == MimeType_TextHtml)
   {
      contentType = TEXT_HTML_CONTENT_TYPE;
   }
   else if (mimeType == MimeType_ImageJpeg)
   {
      contentType = IMAGE_JPEG_CONTENT_TYPE;
   }
   else if (mimeType == MimeType_ImagePng)
   {
      contentType = IMAGE_PNG_CONTENT_TYPE;
   }
   else
   {
      // MIME type not supported
      assert(false);
   }

   return contentType;
}

tm CpmHelper::createDateTime(int year, int month, int day, int hour, int minute, int second, char offsetPrefix, int offsetHour, int offsetMin)
{
   // Fill the time structure
   tm rawDateTime;
   rawDateTime.tm_year = year - 1900;
   rawDateTime.tm_mon = month - 1;
   rawDateTime.tm_mday = day;
   rawDateTime.tm_hour = hour;
   rawDateTime.tm_min = minute;
   rawDateTime.tm_sec = second;
   rawDateTime.tm_wday = 0;
   rawDateTime.tm_yday = 0;
   rawDateTime.tm_isdst = 0;

   // Adjust the retrieved time to the local timezone
   time_t rawtime = mktime(&rawDateTime);
   long offset = 0;
   // TODO: timezone on ThreadX
#if _MSC_VER >= 1900
   long timezone = 0;
   _get_timezone(&timezone);
#endif
#ifndef __THREADX
   if (offsetPrefix == 'Z')
   {
      offset = -timezone;
   }
   else if (offsetPrefix == '-')
   {
      offset = -timezone + ((offsetHour * 60 + offsetMin) * 60);
   }
   else if (offsetPrefix == '+')
   {
      offset = -timezone - ((offsetHour * 60 + offsetMin) * 60);
   }
#endif
   time_t adjustedTime = rawtime + offset;

   tm adjustedDateTime;
#ifdef WIN32
   adjustedDateTime = *localtime(&adjustedTime);
#else
   localtime_r(&adjustedTime, &adjustedDateTime);
#endif

   return adjustedDateTime;
}

cpc::string CpmHelper::contentTypeToString(const resip::Mime& contentType)
{
   return cpc::string(contentType.type().c_str()) + "/" + contentType.subType().c_str();
}

bool CpmHelper::isImdnNotification(CpimMessage cpimMessage)
{
   return cpimMessage.hasContentHeader(CpimHeader::CONTENT_DISPOSITION) &&
          stringToContentType(cpimMessage.getContentHeader(CpimHeader::CONTENT_TYPE).getValue()) == IMDN_CONTENT_TYPE;
}

resip::Uri CpmHelper::getUri(const CPCAPI2::SipAccount::SipAccountImpl& account)
{
   resip::Uri uri;
   uri.user() = account.getSettings().username;
   uri.host() = account.getSettings().domain;
   return uri;
}

resip::NameAddr CpmHelper::getNameAddr(const CPCAPI2::SipAccount::SipAccountImpl& account)
{
   resip::NameAddr nameAddr;
   nameAddr.displayName() = account.getSettings().displayName;
   nameAddr.uri() = getUri(account);
   return nameAddr;
}

bool CpmHelper::acceptsCpimMimeType(const resip::SipMessage& msg)
{
   const resip::SdpContents* sdp = dynamic_cast<const resip::SdpContents*>(msg.getContents());
   if (sdp)
   {
      // Scan the SDP to find a medium that supports the CPIM format
      for (std::list<resip::SdpContents::Session::Medium>::const_iterator it = sdp->session().media().begin(); it != sdp->session().media().end(); it++)
      {
         resip::SdpContents::Session::Medium medium = *it;
         if (medium.name() == MESSAGE_MEDIA_TYPE)
         {
            // Make sure it is not a file transfer SDP
            if (medium.exists("file-transfer-id"))
            {
               // File transfer related SDP, don't allow this SDP
               return false;
            }

            // m=message found
            if (medium.exists(ACCEPT_TYPES_ATTRIBUTE))
            {
               // a=accept-types found
               std::list<resip::Data> types = medium.getValues(ACCEPT_TYPES_ATTRIBUTE);
               for (std::list<resip::Data>::iterator iter = types.begin(); iter != types.end(); iter++)
               {
                  resip::Data mimeType = *iter;

                  // Build the CPIM MIME type for comparison purposes
                  resip::Mime contentType = stringToContentType(mimeType.c_str());
                  if (contentType == CPIM_CONTENT_TYPE)
                  {
                     // a=accept-types=message/cpim found
                     return true;
                  }
               }
            }
         }
      }
   }

   return false;
}

void CpmHelper::logMsrpMessage(msrp_log_level_enum_t level, const char* fmt, const va_list& args)
{
   static char result_string[4096];
   result_string[0] = '\0';

   // Create the string for printing
   if (vsnprintf(result_string, 4096, fmt, const_cast< va_list& >( args )) >= 4096)
   {
      // Add an indicator to show that it was truncated
      result_string[4092] = '.';
      result_string[4093] = '.';
      result_string[4094] = '.';
      result_string[4095] = '\0';
   }

   switch (level)
   {
   case msrp_log_level_firehose_e:
      StackLog(<< result_string);
      break;
   case msrp_log_level_debug_e:
      DebugLog(<< result_string);
      break;
   case msrp_log_level_info_e:
      InfoLog(<< result_string);
      break;
   case msrp_log_level_warning_e:
      WarningLog(<< result_string);
      break;
   case msrp_log_level_error_e:
      ErrLog(<< result_string);
      break;
   case msrp_log_level_wtf_e:
      CritLog(<< result_string);
      break;
   default:
      break;
   }
}

msrp_uri_t* CpmHelper::createLocalMsrpUri(const resip::Data& localIp, unsigned short local_port)
{
   msrp_uri_t *local = NULL;
   // prevent dependency on libmsrp if only using this for SIP SIMPLE
#if (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1) || (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)
   char *sid = NULL;
   char *local_uri = NULL;
   char buf[BUFSIZ];

   msrp_memset( buf, 0, sizeof( buf ));
   snprintf(buf, BUFSIZ, "%d", local_port);

   // Create our local msrp uri
   sid = msrp_stack_create_sid();
   msrp_string_append(&local_uri, "msrp://");
   msrp_string_append(&local_uri, localIp.c_str());
   msrp_string_append(&local_uri, ":");
   msrp_string_append(&local_uri, buf); // port number
   msrp_string_append(&local_uri, "/");
   msrp_string_append(&local_uri, sid);
   msrp_string_append(&local_uri, ";tcp");

   local = msrp_uri_parse( local_uri, NULL); // local

   msrp_safe_free((void **) &sid);
   msrp_safe_free((void **) &local_uri);
#endif
   return local;
}

resip::Data CpmHelper::buildSdp(msrp_uri_t* localUri, UInt32 localMediaPort, const resip::Mime& cpimContentType, bool isActive, const std::list<resip::Mime>& additionalAcceptedWrappedTypes)
{
   assert(localUri);

   resip::Data sdpStr;
   // prevent dependency on libmsrp if only using this for SIP SIMPLE
#if (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1) || (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)
   {
      resip::DataStream ds(sdpStr);

      // Create the SDP header
      UInt64 currentTime = resip::Timer::getTimeMicroSec();
      resip::Data localIpAddr(localUri->p_host);
      ds << "v=0\r\n";
      ds << "o=BriaChat " << currentTime << " "
                          << currentTime << " "
                          << "IN " << (resip::DnsUtil::isIpV6Address(localIpAddr) ? "IP6" : "IP4") << " "
                          << localIpAddr << "\r\n";
      ds << "s= \r\n";
      ds << "t=0 0\r\n";

      // Add the media line to the SDP
      ds << "m=message " << localMediaPort << " TCP/MSRP *\r\n";

      // Add the connection info
      if (localMediaPort > 0)
      {
         ds << "c=IN " << (resip::DnsUtil::isIpV6Address(localIpAddr) ? "IP6" : "IP4" ) << " " << localIpAddr << "\r\n";
      }

      // Specify the accepted types used in SIP messages
      ds << "a=accept-types:" << cpimContentType.type() << "/" << cpimContentType.subType() << "\r\n";

      // Build the complete list of accepted types in MSRP messages, including the additional ones
      std::list<resip::Mime> acceptedWrappedTypes;
      acceptedWrappedTypes.push_back(TEXT_PLAIN_CONTENT_TYPE);
      acceptedWrappedTypes.push_back(TEXT_HTML_CONTENT_TYPE);
      acceptedWrappedTypes.push_back(TEXT_XML_CONTENT_TYPE);
      acceptedWrappedTypes.push_back(IMAGE_JPEG_CONTENT_TYPE);
      acceptedWrappedTypes.push_back(IMAGE_PNG_CONTENT_TYPE);
      acceptedWrappedTypes.push_back(IMDN_CONTENT_TYPE);
      for (std::list<resip::Mime>::const_iterator iter = additionalAcceptedWrappedTypes.begin(); iter != additionalAcceptedWrappedTypes.end(); iter++)
      {
         resip::Mime additionalAcceptedWrappedType = *iter;
         acceptedWrappedTypes.push_back(additionalAcceptedWrappedType);
      }

      // Specify the accepted types used in MSRP messages
      ds << "a=accept-wrapped-types:";
      for (std::list<resip::Mime>::const_iterator iter = acceptedWrappedTypes.begin(); iter != acceptedWrappedTypes.end(); iter++)
      {
         resip::Mime acceptedWrappedType = *iter;
         ds << (contentTypeToString(acceptedWrappedType).c_str()) << " ";
      }
      ds << "\r\n";

      // Add the MSRP related info
      if (localMediaPort > 0)
      {
         // Add the attributes to the SDP
         char *localUriStr = msrp_uri_to_string(localUri);
         ds << "a=path:" << localUriStr << "\r\n";
         ds << "a=sendrecv\r\n";
         msrp_safe_free((void **) &localUriStr);
      }

      // Add MSRP connection information
      ds << "a=setup:" << (isActive ? "active" : "passive") << "\r\n";
      ds << "a=connection:new\r\n";
   }
#endif
   return sdpStr;
}

resip::SdpContents::Session::Medium CpmHelper::getMedium(const resip::SdpContents& contents, const char* mediumName)
{   
   resip::SdpContents::Session::Medium mediumFound;
   for (std::list<resip::SdpContents::Session::Medium>::const_iterator it = contents.session().media().begin(); it != contents.session().media().end(); ++it)
   {
      resip::SdpContents::Session::Medium medium = *it;
      if (medium.name() == mediumName)
      {
         mediumFound = medium;
         break;
      }
   }

   return mediumFound;
}

bool CpmHelper::contains3gppFeatureTag(const resip::SipMessage& msg, const cpc::string& featureTagValueSuffix)
{  
   // Make sure that the message has an Accept-Contact header
   if (!msg.exists(resip::h_AcceptContacts))
   {
      return false;
   }

   // Build the expected feature tag name and value
   resip::ExtensionParameter featureTag(CPM_FEATURE_TAG_NAME);
   cpc::string featureTagValue = CPM_FEATURE_TAG_VALUE_PREFIX + featureTagValueSuffix;

   // Make sure the Accept-Contact header contains a feature tag that is the same as the expected one
   resip::NameAddrs nameAddrs = msg.header(resip::h_AcceptContacts);
   resip::NameAddr nameAddr = nameAddrs.front();
   return (nameAddr.param(featureTag) == featureTagValue.c_str());
}

bool CpmHelper::containsFeatureTag(const resip::SipMessage& msg, const cpc::string& featureTag)
{  
   // Check if the feature tag is in the Accept-Contacts header
   if (msg.exists(resip::h_AcceptContacts))
   {
      resip::NameAddrs nameAddrs = msg.header(resip::h_AcceptContacts);
      resip::NameAddr nameAddr = nameAddrs.front();
      resip::ExtensionParameter featureTagStr(featureTag.c_str());
      return (nameAddr.exists(featureTagStr));
   }

   // Check if the feature tag is in the Contacts header
   if (msg.exists(resip::h_Contacts))
   {
      resip::NameAddrs nameAddrs = msg.header(resip::h_Contacts);
      resip::NameAddr nameAddr = nameAddrs.front();
      resip::ExtensionParameter featureTagStr(featureTag.c_str());
      return (nameAddr.exists(featureTagStr));
   }

   return false;
}

bool CpmHelper::containsNoFeatureTag(const resip::SipMessage& msg)
{
   // Check if there is a feature tag in the Accept-Contacts header
   if (msg.exists(resip::h_AcceptContacts))
   {
      resip::NameAddrs nameAddrs = msg.header(resip::h_AcceptContacts);
      resip::NameAddr nameAddr = nameAddrs.front();
      return (nameAddr.numUnknownParams() == 0);
   }

   return true;
}

void CpmHelper::set3gppFeatureTag(resip::SipMessage& msg, const cpc::string& featureTagValueSuffix)
{
   // Build the feature tag name and value
   resip::ExtensionParameter featureTag(CPM_FEATURE_TAG_NAME);
   cpc::string featureTagValue = CPM_FEATURE_TAG_VALUE_PREFIX + featureTagValueSuffix;

   // Set the Accept-Contact header with the feature tag
   resip::NameAddr nameAddr;
   nameAddr.setAllContacts();
   nameAddr.param(featureTag) = featureTagValue.c_str();
   resip::NameAddrs nameAddrs;
   nameAddrs.push_front(nameAddr);
   msg.header(resip::h_AcceptContacts) = nameAddrs;
}

void CpmHelper::setActiveMsrpEndpoint(const resip::SdpContents& contents, bool& isActive)
{
   for (resip::SdpContents::Session::MediumContainer::const_iterator it = contents.session().media().begin(); it != contents.session().media().end(); it++)
   {
      const std::list<resip::Data>& setupList = it->getValues("setup");
      if (setupList.size() > 0)
      {
         // The SDP provided contains the state of the other endpoint
         //   - If the other endpoint is active, this one will be passive.
         //   - If the other endpoint is actpass, this one will be active.
         isActive = (strcasecmp("active", setupList.front().c_str()) != 0);
      }
   }
}

void CpmHelper::logWarning(const std::string& message)
{
   WarningLog(<< message);
}

NameAddress CpmHelper::resipNameAddrToNameAddr(const resip::NameAddr& nameAddr)
{
   NameAddress ret;

   ret.displayName = nameAddr.displayName().c_str();
   ret.address = nameAddr.uri().getAOR(false).c_str();

   return ret;
}

void CpmHelper::initializeDateTime(tm& dateTime)
{
   dateTime.tm_hour = 0;
   dateTime.tm_min = 0;
   dateTime.tm_sec = 0;
   dateTime.tm_mday = 0;
   dateTime.tm_mon = 0;
   dateTime.tm_year = 0;
   dateTime.tm_wday = 0;
   dateTime.tm_yday = 0;
   dateTime.tm_isdst = 0;
}

}
}
#endif
