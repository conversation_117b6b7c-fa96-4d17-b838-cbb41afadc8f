#pragma once

#if !defined(CPCAPI2_JSON_API_SERVER_INTERFACE_H)
#define CPCAPI2_JSON_API_SERVER_INTERFACE_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerSendTransport.h"
#include "jsonapi/JsonApiServerModule.h"
#include "jsonapi/JsonApiClientHandler.h"
#include "jsonapi/HttpServerInternal.h"
#include "jsonapi/JsonApiServerInternal.h"

#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"
#include "../util/AutoTestProcessor.h"
#include "phone/Cpcapi2EventSource.h"

#include <websocketpp/server.hpp>

#include <rutil/Fifo.hxx>

#include <map>

namespace CPCAPI2
{

class PhoneInterface;
class LocalLogger;

namespace JsonApi
{

class JsonApiServerWebSocket;
class JsonApiWebSocketSession;
class JsonApiServerHTTP;
class JsonApiServerModule;

struct JsonApiUserContext
{
   JsonApiUserContext()
   {
      handle = 0;
      phone = NULL;
   }

   JsonApiUserHandle handle;
   CPCAPI2::Phone* phone;
   std::set<std::string> permissions;

};

class JsonApiServerSyncHandler {};

class JsonApiServerInterface : public JsonApiServerInternal,
                               public JsonApiServerModule,
                               public HttpServerInternal,
                               public PhoneModule,
                               public CPCAPI2::EventSource<JsonApiUserHandle, JsonApiServerHandler, JsonApiServerSyncHandler>
{

public:

   JsonApiServerInterface(Phone* phone);
   virtual ~JsonApiServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE
   {
      return kError;
   }

   virtual int process(unsigned int timeout) OVERRIDE
   {
      return CPCAPI2::EventSource<JsonApiUserHandle, JsonApiServerHandler, JsonApiServerSyncHandler>::process(timeout);
   }

   virtual void interruptProcess() OVERRIDE
   {
      EventSource<JsonApiUserHandle, JsonApiServerHandler, JsonApiServerSyncHandler>::interruptProcess();
   }

   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE
   {
      EventSource<JsonApiUserHandle, JsonApiServerHandler, JsonApiServerSyncHandler>::setCallbackHook(cbHook, context);
   }

   virtual void setOnPrivilegedAccessCompleted(void(*onPrivilegedAccessCompleted)(void*), void* context) OVERRIDE { mOnPrivilegedAccessCompleted = std::bind(onPrivilegedAccessCompleted, context); }

   // JsonApiServer
   virtual int start(const JsonApiServerConfig& serverConfig) OVERRIDE;
   virtual int shutdown() OVERRIDE;
   virtual int setHandler(JsonApiServerHandler* handler) OVERRIDE { return setAppHandler(0, handler); }
   virtual int setJsonApiUserContext(JsonApiUserHandle jsonApiUser, CPCAPI2::Phone* sdkContext, const cpc::vector<cpc::string>& sdkModules) OVERRIDE;
   virtual int sendLoginResult(JsonApiUserHandle jsonApiUser, LoginResultEvent loginResult) OVERRIDE;
   virtual int sendLogoutResult(JsonApiUserHandle jsonApiUser, LogoutResultEvent logoutResult) OVERRIDE;
   virtual int proceedWithLogout(JsonApiUserHandle jsonApiUser, LogoutProceedEvent proceedEvent) OVERRIDE;
   virtual int destroyUser(JsonApiUserHandle jsonApiUser) OVERRIDE;
   virtual int processIncomingJson(const cpc::string& json) OVERRIDE;

   // JsonApiServerInternal
   virtual void addEventObserver(JsonApiServerHandler* handler) OVERRIDE;

   // HttpServerInternal
   virtual int addRequestHandler(const cpc::string& path, HttpServerRequestHandler* handler) OVERRIDE;

   // internal use
   int processIncoming(JsonApiWebSocketSession* session, const std::shared_ptr<rapidjson::Document>& request);
   int processIncomingHttp(unsigned int requestId, const AuthToken& authToken, const cpc::string& resource, bool isOrchestration);
   int processIncomingInMem(const std::shared_ptr<rapidjson::Document>& request);
   std::shared_ptr<JsonApiServerWebSocket> getJsonApiWsTransport() const;
   std::shared_ptr<JsonApiServerHTTP> getJsonApiHttpTransport() const;
   OutgoingJsonHandler* getOutgoingJsonHandler() const;
   websocketpp::connection_hdl getWebsocketConnHdl(JsonApiUserHandle jsonApiUser) const;
   AuthToken getAuthTokenForConnection(JsonApiUserHandle jsonApiUser) const;
   void handleConnectionClosed(JsonApiUserHandle jsonApiUser, JsonApiWebSocketSession* session, const std::function<void(JsonApiWebSocketSession*)>& closedCallback);
   const JsonApiServerConfig& getConfig() const;
   int validateAuthToken(const AuthToken& authToken, bool& isValid, std::map<resip::Data, resip::Data>* pubClaims, std::vector<resip::Data>* requestedResources);
   int validateAuthToken(const resip::Data& authToken, resip::Data& userIdentity, resip::Data& deviceId, std::map<resip::Data, resip::Data>& publicClaims, std::vector<resip::Data>& requestedResources);
   static resip::Data doEncrypt(const cpc::string& input);

private:

   int startImpl(const JsonApiServerConfig& serverConfig);
   int shutdownImpl();
   int setJsonApiUserContextImpl(JsonApiUserHandle jsonApiUser, CPCAPI2::Phone* sdkContext, const cpc::vector<cpc::string>& sdkModules);
   int sendLoginResultImpl(JsonApiUserHandle jsonApiUser, LoginResultEvent loginResult);
   int sendLogoutResultImpl(JsonApiUserHandle jsonApiUser, LogoutResultEvent logoutResult);
   int proceedWithLogoutImpl(JsonApiUserHandle jsonApiUser, LogoutProceedEvent proceedEvent);
   int destroyUserImpl(JsonApiUserHandle jsonApiUser);
   int processIncomingJsonImpl(const cpc::string& json);
   void handleConnectionClosedImpl(JsonApiUserHandle jsonApiUser, JsonApiWebSocketSession* session, const std::function<void(JsonApiWebSocketSession*)>& closedCallback);

   void processIncomingImpl(JsonApiWebSocketSession* session, const std::shared_ptr<rapidjson::Document>& request);
   void processIncomingHttpImpl(unsigned int requestId, JsonApiUserHandle jsonApiUser);
   int handleLogin(const rapidjson::Value& functionObjectVal, JsonApiWebSocketSession* session);
   int handleLogout(const rapidjson::Value& functionObjectVal, JsonApiWebSocketSession* session);

private:

   PhoneInterface* mPhone;
   JsonApiServerConfig mConfig;

   // for in-memory API message passing
   JsonApiUserHandle mInMemUser;
   JsonApiUserContext* mInMemUserContext;

   // for Websocket transport
   std::shared_ptr<JsonApiServerWebSocket> mWebsocketTransport;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&, JsonApiWebSocketSession*)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mWebsocketSendTransport;
   typedef std::map<JsonApiUserHandle, JsonApiWebSocketSession*> MapUserToWsSession;
   MapUserToWsSession mMapUserToWsSession;
   ObserverHandlerMap mLoggedOutUserToHandlerMap;
   struct ConnAttemptInfo
   {
      std::chrono::system_clock::time_point firstConnTime;
      int connAttempts = 0;
   };
   std::map<cpc::string, ConnAttemptInfo> mMapUserToConnAttempt;

   // for HTTP and HTTPS transport
   std::shared_ptr<JsonApiServerHTTP> mHttpTransport;
   typedef std::map<JsonApiUserHandle, unsigned int> MapUserToReq;
   MapUserToReq mMapUserToReq;
   LocalLogger* mLocalLogger;

   std::function<void()> mOnPrivilegedAccessCompleted;
};

class JsonApiUserHandleFactory
{

public:

   static JsonApiUserHandle getNext();

private:

   static JsonApiUserHandle sNextHandle;

};

}

}

#endif // CPCAPI2_JSON_API_SERVER_INTERFACE_H
