#include "brand_branded.h"

#ifdef CPCAPI2_GENBAND_MODULE
#include "impl/phone/PhoneInterface.h"
#include "GenbandRestAPI.h"

namespace CPCAPI2
{
namespace Genband
{

GenbandRestAPIInterface* GenbandRestAPIInterface::getInterface(Phone* cpcPhone)
{
#ifdef CPCAPI2_GENBAND_MODULE
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<GenbandRestAPI>(phone, "GenbandRestAPIInterface");
#else
   return NULL;
#endif
}

}

#endif
