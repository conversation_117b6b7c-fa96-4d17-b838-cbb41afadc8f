#pragma once

#if !defined(CPCAPI2_SIP_CONVERSATION_JSON_PROXY_INTERFACE_H)
#define CPCAPI2_SIP_CONVERSATION_JSON_PROXY_INTERFACE_H

#include "interface/experimental/call/SipConversationJsonProxy.h"
#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "call/SipConversationJsonProxy.h"
#include "jsonapi/JsonApiClientModule.h"
#include "phone/PhoneModule.h"
#include "peerconnection/PeerConnectionManager.h"
#include "peerconnection/PeerConnectionHandler.h"
#include "peerconnection/PeerConnectionSyncHandler.h"

#include <rutil/Reactor.hxx>
#include <rutil/Fifo.hxx>

#include <mutex>
#include <condition_variable>

namespace CPCAPI2
{
class PhoneInterface;
namespace SipConversation
{
class SipConversationJsonProxyInterface : public CPCAPI2::SipConversation::SipConversationManagerJsonProxy,
                                          public CPCAPI2::JsonApi::JsonApiClientModule,
                                          public CPCAPI2::PeerConnection::PeerConnectionHandler,
                                          public CPCAPI2::PeerConnection::PeerConnectionSyncHandler,
                                          public CPCAPI2::PhoneModule
{
public:
   SipConversationJsonProxyInterface(CPCAPI2::Phone* phone);
   virtual ~SipConversationJsonProxyInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiClientModule
   virtual void setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport) OVERRIDE;
   virtual int processIncoming(const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // SipConversationManagerJsonProxy
   virtual int setStateHandler(SipConversationJsonProxyStateHandler* handler) OVERRIDE;
   virtual int joinRemoteConversation(SipConversationHandle conversation, RemoteMediaMode remoteMediaMode) OVERRIDE;
   virtual int requestStateAllConversations() OVERRIDE;

   // Inherited via SipConversationManager
   virtual int setHandler(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipConversationHandler* handler) OVERRIDE;
   virtual int setDefaultSettings(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const CPCAPI2::SipConversationSettings& settings) OVERRIDE;
   virtual int setDefaultSettings(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const CPCAPI2::SipConversationSettings& settings,
      CPCAPI2::NetworkTransport transport) OVERRIDE;
   virtual int setCallInitiatedFromPush(SipConversationHandle conversation) OVERRIDE;
   virtual SipConversationHandle createConversation(CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;
   virtual int addParticipant(SipConversationHandle conversation, const cpc::string& targetAddress) OVERRIDE;
   virtual int configureMedia(SipConversationHandle conversation, const MediaInfo& mediaDescriptor) OVERRIDE;
   virtual int setMediaEnabled(SipConversationHandle conversation, MediaType mediaType, bool enabled) OVERRIDE;
   virtual int setMediaEnabledByDirection(SipConversationHandle conversation, CPCAPI2::SipConversation::MediaType mediaType, bool enabled) OVERRIDE;
   virtual int setMediaCryptoSuites(SipConversationHandle conversation, cpc::vector<MediaCryptoSuite> cryptoSuites) OVERRIDE;
   virtual int setCryptoSuitesForMedia(SipConversationHandle conversation, CPCAPI2::SipConversation::MediaType mediaType, cpc::vector<MediaCryptoSuite> cryptoSuites) OVERRIDE;
   virtual int setAnonymousMode(SipConversationHandle conversation, unsigned int anonymousMode) OVERRIDE;
   virtual int setBestEffortMediaEncryption(SipConversationHandle conversation, bool enabled) OVERRIDE;
   virtual int start(SipConversationHandle conversation) OVERRIDE;
   virtual int hold(SipConversationHandle conversation) OVERRIDE;
   virtual int unhold(SipConversationHandle conversation) OVERRIDE;
   virtual int sendMediaChangeRequest(SipConversationHandle conversation) OVERRIDE;
   virtual int end(SipConversationHandle conversation) OVERRIDE;
   virtual int redirect(SipConversationHandle conversation, const cpc::string& targetAddress, const cpc::string& reason) OVERRIDE;
   virtual int sendRingingResponse(SipConversationHandle conversation) OVERRIDE;
   virtual int reject(SipConversationHandle conversation, unsigned int rejectReason = 0) OVERRIDE;
   virtual int accept(SipConversationHandle conversation) OVERRIDE;
   virtual int acceptIncomingTransferRequest(SipConversationHandle transferTargetConversation) OVERRIDE;
   virtual int rejectIncomingTransferRequest(SipConversationHandle transferTargetConversation) OVERRIDE;
   virtual int transfer(SipConversationHandle transferTargetConversation, SipConversationHandle transfereeConversation) OVERRIDE;
   virtual int transfer(SipConversationHandle transferTargetConversation, SipConversationHandle transfereeConversation, bool endTargetConversationOnSuccess) OVERRIDE;
   virtual int transfer(SipConversationHandle transfereeConversation, const cpc::string& targetAddress) OVERRIDE;
   virtual int setDtmfMode(CPCAPI2::SipAccount::SipAccountHandle account, unsigned int ordinal, DtmfMode dtmfMode) OVERRIDE;
   virtual int startDtmfTone(SipConversationHandle conversation, unsigned int toneId, bool playLocally) OVERRIDE;
   virtual int stopDtmfTone() OVERRIDE;
   virtual int setAnswerMode(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipConversation::AnswerModeSettings answerMode, CPCAPI2::NetworkTransport transport) OVERRIDE;
   virtual int refreshConversationStatistics(SipConversationHandle conversation) OVERRIDE;
   virtual int refreshConversationStatistics(SipConversationHandle conversation, bool includeNetworkStatistics, bool includeJitterStatistics, bool includeRemoteStatistics) OVERRIDE;
   virtual int setAdornmentHandler(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipConversationAdornmentHandler* handler) OVERRIDE { return 0; }
   virtual int adornMessage(SipConversationHandle conversation, unsigned int adornmentMessageId, const cpc::vector<SipHeader>& customHeaders) OVERRIDE;
   virtual int playSound(SipConversationHandle conversation, const cpc::string& resourceUri, bool repeat) OVERRIDE;
   virtual int playSound(SipConversationHandle conversation, PlaySoundStream* playSoundStream, bool repeat) OVERRIDE { return 0; };
   virtual int stopPlaySound(SipConversationHandle conversation) OVERRIDE;
   virtual int startMonitoringAudioDeviceLevels(SipConversationHandle conversation) OVERRIDE;
   virtual int stopMonitoringAudioDeviceLevels(SipConversationHandle conversation) OVERRIDE;
   virtual int decodeProvisioningResponse(const cpc::string& provisioningResponse, cpc::vector<SipConversationSettings>& outConversationSettings) OVERRIDE { return 0; }
   virtual int getCallCount() OVERRIDE { return 0; }
   virtual int setCallKitMode(SipConversationHandle conversation) OVERRIDE;
   virtual int setTelecomFrameworkMode(SipConversationHandle conversation) OVERRIDE;
   virtual int setIncomingVideoRenderTarget(SipConversationHandle conversation, void* surface, Media::VideoSurfaceType type) OVERRIDE;
   virtual int setAudioDeviceCloseDelay(SipConversationHandle conversation, int audioDeviceCloseDelay) OVERRIDE;

   virtual int setVideoNackSettings(SipConversationHandle conversation, const NackSettings& nackSettings) OVERRIDE;

private:
   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);
   void postCallback(resip::ReadCallbackBase* fp);

   void processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request);

   int handleCreateConversationResult(const rapidjson::Value& functionObjectVal);
   int handleNewConversation(const rapidjson::Value& functionObjectVal);
   int handleConversationEnded(const rapidjson::Value& functionObjectVal);
   int handleConversationStateChangeRequest(const rapidjson::Value& functionObjectVal);
   int handleConversationStateChanged(const rapidjson::Value& functionObjectVal);
   int handleConversationMediaChangeRequest(const rapidjson::Value & functionObjectVal);
   int handleConversationMediaChanged(const rapidjson::Value & functionObjectVal);
   int handleConversationState(const rapidjson::Value& functionObjectVal);

   int handleSdpOfferAnswer(const rapidjson::Value& functionObjectVal);
   int handleLocalSdpOffer(const rapidjson::Value& functionObjectVal);
   int handleLocalSdpAnswer(const rapidjson::Value& functionObjectVal);

   int handleConversationInitiated(const rapidjson::Value& functionObjectVal);
   int handleIncomingTransferRequest(const rapidjson::Value& functionObjectVal);
   int handleIncomingRedirectRequest(const rapidjson::Value& functionObjectVal);
   int handleIncomingTargetChangeRequest(const rapidjson::Value& functionObjectVal);
   int handleIncomingHangupRequest(const rapidjson::Value& functionObjectVal);
   int handleIncomingBroadsoftTalkRequest(const rapidjson::Value& functionObjectVal);
   int handleIncomingBroadsoftHoldRequest(const rapidjson::Value& functionObjectVal);
   int handleTransferProgress(const rapidjson::Value& functionObjectVal);
   int handleConversationStatisticsUpdated(const rapidjson::Value& functionObjectVal);
   int handleAudioDeviceLevelChange(const rapidjson::Value& functionObjectVal);
   int handleError(const rapidjson::Value& functionObjectVal);

   void setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipConversationHandler* handler);
   void setDefaultSettingsImpl(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipConversationSettings& settings);
   void setDefaultSettingsImpl(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipConversationSettings& settings, CPCAPI2::NetworkTransport transport);
   void setCallInitiatedFromPushImpl(SipConversationHandle conversation);
   void createConversationImpl(SipAccount::SipAccountHandle account);
   void addParticipantImpl(SipConversationHandle conversation, const cpc::string& participantAddress);
   void startImpl(SipConversationHandle conversation);
   void endImpl(SipConversationHandle conversation);
   void redirectImpl(SipConversationHandle conversation, const cpc::string& targetAddress, const cpc::string& reason);
   void sendRingingResponseImpl(SipConversationHandle conversation);
   void acceptImpl(SipConversationHandle conversation);
   void rejectImpl(SipConversationHandle conversation, unsigned int rejectReason);
   void configureMediaImpl(SipConversationHandle conversation, const MediaInfo& mediaDescriptor);
   void setMediaCryptoSuitesImpl(SipConversationHandle conversation, cpc::vector<MediaCryptoSuite> cryptoSuites);
   void setCryptoSuitesForMediaImpl(SipConversationHandle conversation, CPCAPI2::SipConversation::MediaType mediaType, cpc::vector<MediaCryptoSuite> cryptoSuites);
   void setAnonymousModeImpl(SipConversationHandle conversation, unsigned int anonymousMode);
   void setBestEffortMediaEncryptionImpl(SipConversationHandle conversation, bool enabled);
   void setMediaEnabledImpl(SipConversationHandle conversation, MediaType mediaType, bool enabled);
   void setMediaEnabledByDirectionImpl(SipConversationHandle conversation, CPCAPI2::SipConversation::MediaType mediaType, bool enabled);
   void sendMediaChangeRequestImpl(SipConversationHandle conversation);
   void holdImpl(SipConversationHandle conversation);
   void unholdImpl(SipConversationHandle conversation);
   void acceptIncomingTransferRequestImpl(SipConversationHandle transferTargetConversation);
   void rejectIncomingTransferRequestImpl(SipConversationHandle transferTargetConversation);
   void transferImpl(SipConversationHandle transferTargetConversation, SipConversationHandle transfereeConversation);
   void transferImpl(SipConversationHandle transferTargetConversation, SipConversationHandle transfereeConversation, bool endTargetConversationOnSuccess);
   void transferImpl(SipConversationHandle transfereeConversation, const cpc::string& targetAddress);
   void setDtmfModeImpl(CPCAPI2::SipAccount::SipAccountHandle account, unsigned int ordinal, DtmfMode dtmfMode);
   void startDtmfToneImpl(SipConversationHandle conversation, unsigned int toneId, bool playLocally);
   void stopDtmfToneImpl();
   void setAnswerModeImpl(SipConversationHandle conversation, AnswerModeSettings answerMode, CPCAPI2::NetworkTransport transport);
   void refreshConversationStatisticsImpl(SipConversationHandle conversation);
   void refreshConversationStatisticsImpl(SipConversationHandle conversation, bool includeNetworkStatistics, bool includeJitterStatistics, bool includeRemoteStatistics);
   void setVideoNackSettingsImpl(SipConversationHandle conversation, const NackSettings& nackSettings);

   void setStateHandlerImpl(SipConversationJsonProxyStateHandler* handler);
   void joinRemoteConversationImpl(SipConversationHandle conversation, RemoteMediaMode remoteMediaMode);
   void requestStateAllConversationsImpl();

   void adornMessageImpl(SipConversationHandle conversation, unsigned int adornmentMessageId, const cpc::vector<SipHeader>& customHeaders);
   void playSoundImpl(SipConversationHandle conversation, const cpc::string& resourceUri, bool repeat);
   void stopPlaySoundImpl(SipConversationHandle conversation);
   void startMonitoringAudioDeviceLevelsImpl(SipConversationHandle conversation);
   void stopMonitoringAudioDeviceLevelsImpl(SipConversationHandle conversation);
   //void decodeProvisioningResponseImpl(const cpc::string& provisioningResponse, cpc::vector<SipConversationSettings>& outConversationSettings);
   //int getCallCountImpl();
   void setCallKitModeImpl(SipConversationHandle conversation);
   void setTelecomFrameworkModeImpl(SipConversationHandle conversation);
   void setIncomingVideoRenderTargetImpl(SipConversationHandle conversation, void* surface, Media::VideoSurfaceType type);
   void setAudioDeviceCloseDelayImpl(SipConversationHandle conversation, int audioDeviceCloseDelay);

   CPCAPI2::PeerConnection::PeerConnectionHandle initPeerConnection(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::PeerConnection::SessionDescription* sdpOffer);

   enum RemoteConvOfferAnswerState
   {
      RemoteConvOfferAnswerState_None,
      RemoteConvOfferAnswerState_HaveRemoteOffer_CreatingLocalAnswer,
      RemoteConvOfferAnswerState_CreatingLocalOffer,
      RemoteConvOfferAnswerState_HaveLocalOffer_WaitingForRemoteAnswer,
      RemoteConvOfferAnswerState_Stable
   };

   class RemoteConversationStateInfo
   {
   public:
      RemoteConversationStateInfo() : mPC(-1), mOAS(RemoteConvOfferAnswerState_None) {}
      ~RemoteConversationStateInfo() {}
      CPCAPI2::PeerConnection::PeerConnectionHandle peerConnection() const
      {
         return mPC;
      }
      CPCAPI2::PeerConnection::PeerConnectionHandle& peerConnection()
      {
         return mPC;
      }
      RemoteConvOfferAnswerState offerAnswerState() const
      {
         return mOAS;
      }
      void setOfferAnswerState(RemoteConvOfferAnswerState newState)
      {
         mOAS = newState;
      }
      bool hasRemoteOffer() const
      {
         return (mRemoteOffer.get() != NULL);
      }
      CPCAPI2::PeerConnection::SessionDescription remoteOffer() const
      {
         return *mRemoteOffer;
      }
      void setRemoteOffer(const CPCAPI2::PeerConnection::SessionDescription& sdpOffer)
      {
         mRemoteOffer.reset(new CPCAPI2::PeerConnection::SessionDescription(sdpOffer));
      }
      void resetRemoteOffer()
      {
         mRemoteOffer.reset();
      }
   private:
      CPCAPI2::PeerConnection::PeerConnectionHandle mPC;
      std::unique_ptr<CPCAPI2::PeerConnection::SessionDescription> mRemoteOffer;
      RemoteConvOfferAnswerState mOAS;
   };
   typedef std::map<CPCAPI2::SipConversation::SipConversationHandle, RemoteConversationStateInfo> RemoteConvStateMap;

   // PeerConnectionHandler
   virtual int onSignalingStateChange(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SignalingStateChangeEvent& args) OVERRIDE;
   virtual int onCreateOfferResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateOfferResult& args) OVERRIDE;
   virtual int onCreateAnswerResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args) OVERRIDE;
   virtual int onSetLocalSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetLocalSessionDescriptionResult& args) OVERRIDE;
   virtual int onSetRemoteSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetRemoteSessionDescriptionResult& args) OVERRIDE;
   virtual int onWebVideoServerReady(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::WebVideoServerReadyEvent& args) OVERRIDE {
      return kSuccess;
   }
   virtual int onError(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::ErrorEvent& args) OVERRIDE;

private:
   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiTransport* mTransport;
   std::map<SipAccount::SipAccountHandle, SipConversation::SipConversationHandler*> mAppHandlers;
   std::map<SipConversation::SipConversationHandle, SipAccount::SipAccountHandle> mMapConvHandleToAccountHandle;
   std::map<SipAccount::SipAccountHandle, CPCAPI2::SipConversationSettings> mMapAcctToConvSettings;
   std::mutex mMutex;
   std::condition_variable mCondCreated;
   SipConversation::SipConversationHandle mServerCreatedConvHandle;
   CPCAPI2::PeerConnection::PeerConnectionManager* mPeerConnMgr;
   SipConversationJsonProxyStateHandler* mStateHandler;
   RemoteConvStateMap mRemoteConvStateMap;
};
}
}
#endif // CPCAPI2_SIP_CONVERSATION_JSON_PROXY_INTERFACE_H
