#include "brand_branded.h"

#include "BroadsoftCallControlMonitor.h"
#include "../util/cpc_logger.h"

#include <resip/dum/DialogUsageManager.hxx>
#include <resip/dum/DumFeature.hxx>
#include <resip/dum/DumFeatureMessage.hxx>
#include <resip/dum/MasterProfile.hxx>
#include <resip/dum/OutgoingEvent.hxx>

#include <rutil/Logger.hxx>


#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_CALL

using namespace resip;

namespace CPCAPI2
{
namespace SipConversation
{
const resip::Data BroadsoftCallControlMonitor::TalkEvent = "talk";
const resip::Data BroadsoftCallControlMonitor::HoldEvent = "hold";
   
BroadsoftCallControlMonitor::BroadsoftCallControlMonitor(DialogUsageManager& dum)
{
   // These will be destroyed with DUM
   dum.addIncomingFeature(SharedPtr<DumFeature>(new BroadsoftCallControlMonitor::Incoming(*this, dum)));
   dum.addOutgoingFeature(SharedPtr<DumFeature>(new BroadsoftCallControlMonitor::Outgoing(*this, dum)));
}
   
void BroadsoftCallControlMonitor::watch(const SipMessage& msg)
{
   mWatching.insert(msg.getTransactionId());
}

bool BroadsoftCallControlMonitor::claim(const SipMessage &msg)
{
   WatchedTransactions::iterator it = mWatching.find(msg.getTransactionId());
   
   if (it != mWatching.end())
   {
      mWatching.erase(it);
      return true;
   }
   else
   {
      return false;
   }
}

BroadsoftCallControlMonitor::Incoming::Incoming(BroadsoftCallControlMonitor& monitor, DialogUsageManager& dum)
   : DumFeature(dum, dum.dumIncomingTarget()),
     mMonitor(monitor)
{
}

DumFeature::ProcessingResult BroadsoftCallControlMonitor::Incoming::process(Message* msg)
{
   SipMessage* request = dynamic_cast<SipMessage*>(msg);
   
   if (!request || !request->isRequest() || request->method() != NOTIFY || !request->exists(h_Event))
   {
      return FeatureDone;
   }

   const Data& event = request->header(h_Event).value();
   if (resip::isEqualNoCase(event, TalkEvent) || resip::isEqualNoCase(event, HoldEvent))
   {
      resip::DialogId id(*request);
      if (mDum.findInviteSession(id).isValid())
      {
         mMonitor.watch(*request);
         request->header(h_RequestLine).method() = INFO;
         request->header(h_CSeq).method() = INFO;
         DebugLog(<< "BroadWorks 3PCC " << event << " matched existing dialog");
      }
      else
      {
         InfoLog(<< "BroadWorks 3PCC " << event << " event did not match existing dialog");
      }
   }
   
   return FeatureDone;
}

BroadsoftCallControlMonitor::Outgoing::Outgoing(BroadsoftCallControlMonitor& monitor, DialogUsageManager& dum)
   : DumFeature(dum, dum.dumOutgoingTarget()),
     mMonitor(monitor)
{
}
   
DumFeature::ProcessingResult BroadsoftCallControlMonitor::Outgoing::process(Message* msg)
{
   OutgoingEvent* event = dynamic_cast<OutgoingEvent*>(msg);
   
   if (!event)
   {
      return FeatureDone;
   }
   
   SharedPtr<SipMessage> response = event->message();
   
   if (response->isResponse())
   {
      if (response->method() == INVITE &&
          response->header(h_StatusLine).responseCode() > 100 &&
          response->header(h_StatusLine).responseCode() < 200)
      {
         response->header(h_AllowEvents) = mDum.getMasterProfile()->getAllowedEvents();
      }
      else if (mMonitor.claim(*response))
      {
         assert(response->method() == INFO);
         response->header(h_CSeq).method() = NOTIFY;
      }
   }

   return FeatureDone;
}
}
}