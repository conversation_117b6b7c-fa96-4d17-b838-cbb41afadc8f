#pragma once

#if !defined(__CPCAPI2_BUSY_LAMP_FIELD_HELPER_H__)
#define __CPCAPI2_BUSY_LAMP_FIELD_HELPER_H__

#include "cpcapi2defs.h"
#include "busylampfield/SipBusyLampFieldHandler.h"
#include "dialogevent/SipDialogEventModel.h"

namespace CPCAPI2
{
namespace SipBusyLampField
{

class SipBusyLampFieldHelper
{
public:
   static bool isHeld(const CPCAPI2::SipDialogEvent::DialogInfo& dialogInfo);
   static bool isParked(const CPCAPI2::SipDialogEvent::DialogInfo& dialogInfo);
   static cpc::string getParameterValue(const CPCAPI2::SipDialogEvent::ParticipantInfo& participant, const cpc::string& name);
   static cpc::string getBargeInModeString(BargeInMode bargeInMode);
   static bool isCall(const CPCAPI2::SipDialogEvent::DialogInfo& dialogInfo);

   static const cpc::string SIP_RENDERING_PARAM;
   static const cpc::string SIP_CALL_PARK_PARAM;
   static const cpc::string BARGE_MODE_PARAM;
   static const cpc::string ALERT_MODE_PARAM;
   static const cpc::string SILENT_ALERT_MODE;

private:
   SipBusyLampFieldHelper() {}
};

}
}

#endif // __CPCAPI2_BUSY_LAMP_FIELD_HELPER_H__
