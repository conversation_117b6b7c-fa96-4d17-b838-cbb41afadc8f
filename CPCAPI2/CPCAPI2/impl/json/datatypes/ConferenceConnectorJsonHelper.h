// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef ConferenceConnector_JSON_HELPER_H
#define ConferenceConnector_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/experimental/confconnector/ConferenceConnectorTypes.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceConnector::ConferenceConnectorSettings& v)
    {
      w.StartObject();
      Write(w, "username", v.username);
      Write(w, "password", v.password);
      Write(w, "regionCode", v.regionCode);
      Write(w, "authServerUrl", v.authServerUrl);
      Write(w, "authServerApiKey", v.authServerApiKey);
      Write(w, "authType", v.authType);
      Write(w, "orchestrationServerUrl", v.orchestrationServerUrl);
      Write(w, "joinUrl", v.joinUrl);
      Write(w, "ignoreCertVerification", v.ignoreCertVerification);
      Write(w, "authenticationTimeoutSeconds", v.authenticationTimeoutSeconds);
      Write(w, "orchestrationTimeoutSeconds", v.orchestrationTimeoutSeconds);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceConnector::ConferenceConnectorSettings& v)
    {
      Read(json, "username", v.username);
      Read(json, "password", v.password);
      Read(json, "regionCode", v.regionCode);
      Read(json, "authServerUrl", v.authServerUrl);
      Read(json, "authServerApiKey", v.authServerApiKey);
      Read(json, "authType", v.authType);
      Read(json, "orchestrationServerUrl", v.orchestrationServerUrl);
      Read(json, "joinUrl", v.joinUrl);
      Read(json, "ignoreCertVerification", v.ignoreCertVerification);
      Read(json, "authenticationTimeoutSeconds", v.authenticationTimeoutSeconds);
      Read(json, "orchestrationTimeoutSeconds", v.orchestrationTimeoutSeconds);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceConnector::CloudConferenceSettings& v)
    {
      w.StartObject();
      Write(w, "conferenceId", v.conferenceId);
      Write(w, "conferenceDescription", v.conferenceDescription);
      Write(w, "conferenceType", v.conferenceType);
      Write(w, "parentConference", v.parentConference);
      Write(w, "persistent", v.persistent);
      Write(w, "conferenceTags", v.conferenceTags);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceConnector::CloudConferenceSettings& v)
    {
      Read(json, "conferenceId", v.conferenceId);
      Read(json, "conferenceDescription", v.conferenceDescription);
      Read(json, "conferenceType", v.conferenceType);
      Read(json, "parentConference", v.parentConference);
      Read(json, "persistent", v.persistent);
      Read(json, "conferenceTags", v.conferenceTags);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceConnector::CloudConferenceSessionSettings& v)
    {
      w.StartObject();
      Write(w, "role", v.role);
      Write(w, "address", v.address);
      Write(w, "displayName", v.displayName);
      Write(w, "tags", v.tags);
      Write(w, "addToFloor", v.addToFloor);
      Write(w, "mediaDscp", v.mediaDscp);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceConnector::CloudConferenceSessionSettings& v)
    {
      Read(json, "role", v.role);
      Read(json, "address", v.address);
      Read(json, "displayName", v.displayName);
      Read(json, "tags", v.tags);
      Read(json, "addToFloor", v.addToFloor);
      Read(json, "mediaDscp", v.mediaDscp);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceConnector::CloudConferenceSessionMediaSettings& v)
    {
      w.StartObject();
      Write(w, "audioDirection", v.audioDirection);
      Write(w, "videoDirection", v.videoDirection);
      Write(w, "remoteVideoRenderSurface", v.remoteVideoRenderSurface);
      Write(w, "remoteVideoRenderSurfaceType", v.remoteVideoRenderSurfaceType);
      Write(w, "screenCaptureMaxFrameRate", v.screenCaptureMaxFrameRate);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceConnector::CloudConferenceSessionMediaSettings& v)
    {
      Read(json, "audioDirection", v.audioDirection);
      Read(json, "videoDirection", v.videoDirection);
      Read(json, "remoteVideoRenderSurface", v.remoteVideoRenderSurface);
      Read(json, "remoteVideoRenderSurfaceType", v.remoteVideoRenderSurfaceType);
      Read(json, "screenCaptureMaxFrameRate", v.screenCaptureMaxFrameRate);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceConnector::CloudConferenceInfo& v)
    {
      w.StartObject();
      Write(w, "conference", v.conference);
      Write(w, "conferenceType", v.conferenceType);
      Write(w, "displayName", v.displayName);
      Write(w, "joinUrl", v.joinUrl);
      Write(w, "description", v.description);
      Write(w, "conferenceId", v.conferenceId);
      Write(w, "tags", v.tags);
      Write(w, "persistent", v.persistent);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceConnector::CloudConferenceInfo& v)
    {
      Read(json, "conference", v.conference);
      Read(json, "conferenceType", v.conferenceType);
      Read(json, "displayName", v.displayName);
      Read(json, "joinUrl", v.joinUrl);
      Read(json, "description", v.description);
      Read(json, "conferenceId", v.conferenceId);
      Read(json, "tags", v.tags);
      Read(json, "persistent", v.persistent);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& v)
    {
      w.StartObject();
      Write(w, "participant", v.participant);
      Write(w, "address", v.address);
      Write(w, "displayName", v.displayName);
      Write(w, "hasFloor", v.hasFloor);
      Write(w, "tags", v.tags);
      Write(w, "peerConnection", v.peerConnection);
      Write(w, "joinTimestampMsecs", v.joinTimestampMsecs);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& v)
    {
      Read(json, "participant", v.participant);
      Read(json, "address", v.address);
      Read(json, "displayName", v.displayName);
      Read(json, "hasFloor", v.hasFloor);
      Read(json, "tags", v.tags);
      Read(json, "peerConnection", v.peerConnection);
      Read(json, "joinTimestampMsecs", v.joinTimestampMsecs);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceConnector::ConferenceSessionMediaInfo& v)
    {
      w.StartObject();
      Write(w, "mediaDirection", v.mediaDirection);
      Write(w, "mediaType", v.mediaType);
      Write(w, "mediaStreamId", v.mediaStreamId);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceConnector::ConferenceSessionMediaInfo& v)
    {
      Read(json, "mediaDirection", v.mediaDirection);
      Read(json, "mediaType", v.mediaType);
      Read(json, "mediaStreamId", v.mediaStreamId);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::ConferenceConnector::ConferenceSessionPermissions& v)
    {
      w.StartObject();
      Write(w, "canCreateConference", v.canCreateConference);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::ConferenceConnector::ConferenceSessionPermissions& v)
    {
      Read(json, "canCreateConference", v.canCreateConference);
    }
  }
}
#pragma warning(pop)
#endif // ConferenceConnector_JSON_HELPER_H