﻿#if __APPLE__
#include "TargetConditionals.h"
#endif

#include "BIMachineIdentification.h"
#include <cpcapi2utils.h>
#ifdef _WIN32
#include "../util/RegistryHelpers.h"
#include "../util/HDDiskId.h"
#endif
#include "../util/DeviceInfo.h"
#include "../util/IpHelpers.h"

#include <vector>
#include <algorithm>

#if _WIN32 && !defined(WP8)
  #include <ObjBase.h>
  #include <atlbase.h>
  #include <comutil.h>
  #include <codecvt>
  #if !defined(WinRT)
    #include <Wbemidl.h>
  #endif
static std::string wstringToUtf8(const wchar_t* in)
{
   std::string ret;
   try
   {
      std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>,wchar_t> convert;
      ret = convert.to_bytes(in);
   }
   catch(...)
   {
   }
   return ret;
}
static std::string wstringToUtf8(const std::wstring& in)
{
   return wstringToUtf8(in.c_str());
}
#endif

#if __APPLE__
#if TARGET_OS_IPHONE
#else
#include "../util/Mac/CMacSecurityAccess.h"
#include <fcntl.h>
#include <unistd.h>
#include <sys/stat.h>
#include "rutil/GenericIPAddress.hxx"
#include <net/if.h>
#include <sys/ioctl.h>
#include <sys/sockio.h>
#include <IOKit/IOKitLib.h>
#include <IOKit/network/IOEthernetInterface.h>
#include <IOKit/network/IOEthernetController.h>
#include <IOKit/network/IONetworkInterface.h>
#include <SystemConfiguration/SystemConfiguration.h>
#endif
#endif

#ifdef ANDROID
#include "JniHelper.h"
#endif

#if defined(__linux__)
#include <sys/ioctl.h>
#include <sys/utsname.h>
#include <net/if.h>
#if !defined(ANDROID)
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <ifaddrs.h>
#include <netpacket/packet.h>
#endif
#endif

#include <rutil/ssl/SHA1Stream.hxx>
#include <rutil/Random.hxx>

using namespace CPCAPI2::BIEvents;

#if _WIN32

class ScopedCoInitialize
{
public:
   // !jjg! NOTE: There's a nasty dependency here -- WebRTC's audio_device_core_win.h 
   //             also calls ::CoInitializeEx(..) on the same thread, and so we need
   //             to make sure both spots are using COINIT_MULTITHREADED.
   ScopedCoInitialize() { hr = ::CoInitializeEx(NULL, COINIT_MULTITHREADED); }
   ~ScopedCoInitialize() { if SUCCEEDED(hr) ::CoUninitialize(); }
private:
   HRESULT hr;
};

#endif //_WIN32
    
bool MachineIdentification::GetHardwareIds( std::vector< std::pair< std::string, std::string > >& idList )
{
   std::string idstring;
   std::vector<std::string> allMacs;

   idList.clear();

#if _WIN32
   // Get the MAC addresses and sort them
   allMacs = GetAllMACs_Win32();
   std::sort( allMacs.begin(), allMacs.end() );
   for( std::vector< std::string >::size_type i = 0 ; i < allMacs.size() ; ++i )
   {
      char buf[ 16 ];
      std::pair< std::string, std::string > item;
      snprintf(buf, 16, "MAC%02d", i);
      item.first  = buf;
      item.second = allMacs[ i ];
      idList.push_back( item );
   }

   // Get the HDD IDs and sort
   std::vector< std::string > allHdds( CPCAPI2::HDDiskId::GetAllHddInfo() );
   std::sort( allHdds.begin(), allHdds.end() );
   for( std::vector< std::string >::size_type i = 0 ; i < allHdds.size() ; ++i )
   {
      char buf[ 16 ];
      std::pair< std::string, std::string > item;
      snprintf(buf, 16, "HDD%02d", i);
      item.first  = buf;
      item.second = allHdds[ i ];
      idList.push_back( item );
   }

#elif defined(ANDROID)
   cpc::string cpcId;
   DeviceInfo::getPlatformUniqueId(cpcId);
   std::pair< std::string, std::string > item;
   item.first = "DeviceId"; // NB this name should change if getPlatformUniqueId implementation changes
   item.second = cpcId;
   idList.push_back( item );
#elif __APPLE__
   
#if TARGET_OS_IPHONE
   cpc::string cpcidstr;
   DeviceInfo::getPlatformUniqueId(cpcidstr);
   std::pair< std::string, std::string > item;
   item.first = "identifierForVendor"; // NB this name should change if getPlatformUniqueId implementation changes
   item.second = cpcidstr;
   idList.push_back( item );
#else
   std::pair< std::string, std::string > item;
   item.first = "IOPlatformUUID";
   item.second = CSecurityAccess::instance()->getHardwareID();
   idList.push_back( item );
#endif
	
#elif __linux__
   // use list of all MAC addresses on the system
   // this is the best we can do on Linux as other info requires root
   allMacs = GetAllLinuxMACs();
   std::sort( allMacs.begin(), allMacs.end() );
   for( std::vector< std::string >::size_type i = 0 ; i < allMacs.size() ; ++i )
   {
      char buf[ 16 ];
      std::pair< std::string, std::string > item;
      snprintf(buf, 16, "MAC%02zu", i);
      item.first  = buf;
      item.second = allMacs[ i ];
      idList.push_back( item );
   }

#endif

   return true;
}

#if defined(WinRT)
std::vector<std::string> MachineIdentification::GetAllMACs_Win32(){
	// Not actually a MAC, but it identifies a single adapter like a MAC
	
	std::vector<std::string> allMACs;
	std::string mac = "";

	Windows::Foundation::Collections::IVectorView<Windows::Networking::Connectivity::ConnectionProfile^>^ adapters = Windows::Networking::Connectivity::NetworkInformation::GetConnectionProfiles();
	for (int i = 0; i < adapters->Size; i++) {
		Windows::Networking::Connectivity::ConnectionProfile^ adapter = adapters->GetAt(i);
		
    Platform::String^ id = adapter->NetworkAdapter->NetworkAdapterId.ToString();
    int length = WideCharToMultiByte(CP_ACP, 0, id->Begin(), id->Length(), NULL, 0, 0, 0) + 2;
    LPSTR mbstr = new char[length];
    WideCharToMultiByte(CP_ACP, 0, id->Begin(), id->Length(), mbstr, length, 0, 0);

    mac.assign(mbstr);
		
    allMACs.push_back(mac);
	}

	return allMACs;
}
#elif _WIN32

std::vector<std::string> MachineIdentification::GetAllMACs_Win32( )
{   
   std::vector<std::string> allMACs;
   std::string mac = "";

   ScopedCoInitialize comInit;

   IWbemLocator *pLoc = 0;
   HRESULT hr;
   
   // connect to WMI
   hr = CoCreateInstance(CLSID_WbemLocator, 0, 
      CLSCTX_INPROC_SERVER, IID_IWbemLocator, (LPVOID *) &pLoc);

   if ( FAILED( hr ) )
   {
      return allMACs;
   }

   // connect to local service with current credentials
   IWbemServices *pSvc = 0;

   hr =  pLoc->ConnectServer(
      BSTR(L"root\\cimv2"),
      NULL, NULL, 0, NULL, 0, 0, &pSvc);

   if (FAILED(hr))
   {
      pLoc->Release();
      return allMACs;
   }
   if ( SUCCEEDED( hr ) )
   {
      //set security

      hr = CoSetProxyBlanket(pSvc,
         RPC_C_AUTHN_WINNT,
         RPC_C_AUTHZ_NONE,
         NULL,
         RPC_C_AUTHN_LEVEL_CALL,
         RPC_C_IMP_LEVEL_IMPERSONATE,
         NULL,
         EOAC_NONE
         );

      if (FAILED(hr))
      {
         pSvc->Release();
         pLoc->Release();     
         return allMACs;      // Program has failed.
      }

      // execute a query Get all Physical adapters.
      IEnumWbemClassObject *enumerator = 0;
      hr = pSvc->ExecQuery(
         BSTR(L"WQL"), 
         BSTR(L"SELECT * FROM Win32_NetworkAdapter"), 
         WBEM_FLAG_FORWARD_ONLY | WBEM_FLAG_RETURN_IMMEDIATELY, 
         NULL, 
         &enumerator);
      if ( hr == 0  )
      {
         IWbemClassObject *networkadapter = NULL;
         ULONG retcnt;
         std::string pnpID;

         while (enumerator)
         {
            retcnt = 0;
            hr = enumerator->Next(WBEM_INFINITE, 1L, &networkadapter, &retcnt);

            if (FAILED(hr) || (hr == S_FALSE) || retcnt == 0)
            {
               break;
            }

            //get PNP id and check for USB or PCI
            VARIANT var_val_netpnpdeviceid;
            hr = networkadapter->Get(L"PNPDeviceID", 0, &var_val_netpnpdeviceid, NULL, NULL);
            if ( SUCCEEDED( hr ) )
            {
               if (V_VT(&var_val_netpnpdeviceid) != VT_NULL)
               {
                  std::wstring wpnpID(var_val_netpnpdeviceid.bstrVal, SysStringLen(var_val_netpnpdeviceid.bstrVal));
                  pnpID = wstringToUtf8(wpnpID);
               }
            }
            VariantClear(&var_val_netpnpdeviceid);
            std::string prefix = pnpID.substr(0,3);
            resip::SHA1Stream cHash;
            // check for PCI, USB, PCMCIA connected MAC addresses. First one is primary.
            if (prefix == "PCI" || prefix == "USB" || prefix == "PCM") //get the MAC now
            {
               VARIANT var_val_mac;
               hr = networkadapter->Get(L"MACAddress", 0, &var_val_mac, NULL, NULL);
               if ( SUCCEEDED( hr ) )
               {
                  if (V_VT(&var_val_mac) != VT_NULL)
                  {   
                     std::wstring wmac(var_val_mac.bstrVal, var_val_mac.bstrVal == NULL ? 0 : SysStringLen(var_val_mac.bstrVal) + 1);
                     mac = wstringToUtf8(wmac);
                     if (!mac.empty())
                     {
                        // WRITE MAC TO XML HERE!
                        allMACs.push_back(mac);
                     }
                  }
               }
               VariantClear(&var_val_mac);
            }

            networkadapter->Release();
         }
         enumerator->Release();
      }
   }

   pSvc->Release();
   pLoc->Release();  

   return allMACs;
}
#endif

#if defined(__linux__) && !defined(ANDROID)
std::vector<std::string>
MachineIdentification::GetAllLinuxMACs()
{
   std::vector<std::string> allMACs;
   struct ifaddrs *pIfaddrs = NULL;
   struct sockaddr_ll *sll = NULL;

   if (getifaddrs(&pIfaddrs) < 0)
      return allMACs;

   struct ifaddrs *pIface = pIfaddrs;
   for (; pIface; pIface = pIface->ifa_next)
   {     
      // ignore some useless interfaces
      if (!pIface->ifa_addr ||
          (pIface->ifa_flags & IFF_LOOPBACK) ||
          (pIface->ifa_addr->sa_family != AF_PACKET))
         continue;

      sll = (struct sockaddr_ll *)pIface->ifa_addr;
      if (sll)
      {
         std::string mac;
         char str[1024];
         snprintf(str, 1024, "%02X-%02X-%02X-%02X-%02X-%02X",
                    sll->sll_addr[0],
                    sll->sll_addr[1],
                    sll->sll_addr[2],
                    sll->sll_addr[3],
                    sll->sll_addr[4],
                    sll->sll_addr[5]);
         mac = str;
         allMACs.push_back(mac);
      }
   }

   freeifaddrs(pIfaddrs);

   return allMACs;
}
#endif // __linux__
