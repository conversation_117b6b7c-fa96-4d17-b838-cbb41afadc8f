#include "brand_branded.h"

#include "interface/public/account/SipAccount.h"
#include "interface/public/account/SipAccountState.h"

#if (CPCAPI2_BRAND_ACCOUNT_MODULE == 1)
#include "SipAccountInterface.h"
#include "SipAccountStateImpl.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace SipAccount
{

SipAccountManager* SipAccountManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_ACCOUNT_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<SipAccountInterface>(phone, "SipAccountInterface");
#else
   return NULL;
#endif
}

SipAccountStateManager* SipAccountStateManager::getInterface(SipAccountManager* cpcAcctMan)
{
#if (CPCAPI2_BRAND_ACCOUNT_MODULE == 1) && (CPCAPI2_BRAND_ACCOUNT_STATE_MODULE == 1)
   SipAccountInterface* parent = dynamic_cast<SipAccountInterface*>(cpcAcctMan);
   if (parent == NULL) return NULL;
   PhoneInterface* phone = parent->phoneInterface();
   return _GetInterfaceEx<SipAccountStateImpl>(phone, "SipAccountStateManager", parent);
#else
   return NULL;
#endif
}

}
}
