#include "jsonrpc/OptionalSerialization.h"
#include "jsonrpc/StringSerialization.h"
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONTRANSFERRESPONSEEVENT_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONTRANSFERRESPONSEEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationTransferResponseEvent
  {
  public:
    std::optional<int32_t> conversationHandle;
    std::optional<int32_t> sipResponseCode;
    std::optional<cpc::string> warningHeader;

    SipConversationTransferResponseEvent() = default;

    SipConversationTransferResponseEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationTransferResponseEvent& o)
    {
      j = nlohmann::json::object();
       if (o.conversationHandle) { j["conversationHandle"] = *(o.conversationHandle); }
     if (o.sipResponseCode) { j["sipResponseCode"] = *(o.sipResponseCode); }
     if (o.warningHeader) { j["warningHeader"] = *(o.warningHeader); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationTransferResponseEvent& o)
    {
     if (j.contains("conversationHandle")) { o.conversationHandle = j.at("conversationHandle"); }
     if (j.contains("sipResponseCode")) { o.sipResponseCode = j.at("sipResponseCode"); }
     if (j.contains("warningHeader")) { o.warningHeader = j.at("warningHeader"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONTRANSFERRESPONSEEVENT_H
