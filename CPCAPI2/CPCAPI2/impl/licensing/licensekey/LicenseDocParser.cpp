#include <openssl/bio.h>
#include <openssl/evp.h>

#include <sstream>

#include "LicenseDocParser.h"
#include "../../util/libXmlHelper.h"

//==============================================================================

namespace CPCAPI2
{
namespace Licensing
{

//==============================================================================

LicenseDocParser::LicenseDocParser(const std::string& document)
   : m_DocVersion(8),
     m_Type(Error),
     m_CheckDelay(0),
     m_MinInterval(0),
     m_EndPercent(0),
     m_Expiry(0),
     m_StartTime(0),
     m_Duration(0),
     m_ErrorCode(0)
{
   Parse(document);
}


void
LicenseDocParser::Parse(const std::string& s)
{
   int ret = 0;

   // Now parse the content of this document
   xmlTextReaderPtr reader = xmlReaderForMemory(s.data(), (int)s.size(), NULL, "UTF-8", 0);

   if (reader != NULL)
   {
      ret = xmlTextReaderRead(reader);
      while (ret == 1)
      {
			std::string nodeName = xmlString(xmlTextReaderName(reader));
         if (nodeName == "clientLicense" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            if (xmlTextReaderHasAttributes(reader) == 1)
            {
					std::string ver = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"version"));
               m_DocVersion = atol(ver.c_str());

					std::string state = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"state"));
               if (state == "Valid")      { m_Type = Valid; }
               else if (state == "Grace") { m_Type = Grace; }    // Only two states are defined, if it is neither
               else                       { m_Type = Error; }             // then this is bad data
               break;
            }
         }
         else if (nodeName == "validateLicenseResponse" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            if (xmlTextReaderHasAttributes(reader) == 1)
            {
               std::string ver = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"version"));
               m_DocVersion = atol(ver.c_str());
               m_Type = Valid;
               break;
            }
         }
         else if (nodeName == "error" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            ParseGlobalError(reader);
         }

     //    /*else if (nodeName == "licenseType" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
     //    {
     //       m_LicenseType = xmlGetElementText(reader);
     //    }*/
     //    else if (nodeName == "refresh" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
     //    {
     //       if (xmlTextReaderHasAttributes(reader) == 1)
     //       {
					//std::string delay = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"initialCheckDelaySeconds"));
     //          m_CheckDelay = atol(delay.c_str());

					//std::string interval = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"minimumCheckIntervalSeconds"));
     //          m_MinInterval = atol(interval.c_str());

					//std::string range = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"checkIntervalRangePercent"));
     //          m_EndPercent = atol(range.c_str());
     //       }
     //    }
     //    else if (nodeName == "expires" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
     //    {
     //       m_Expiry = atol(xmlGetElementText(reader).c_str());
     //    }
     //    else if (nodeName == "clientTime" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
     //    {
     //       m_StartTime = atol(xmlGetElementText(reader).c_str());
     //    }
     //    else if (nodeName == "duration" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
     //    {
     //       m_Duration = atol(xmlGetElementText(reader).c_str());
     //    }
     //    else if (nodeName == "machineHash" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
     //    {
     //       m_MachineHash = xmlGetElementText(reader);
     //    }
     //    else if (nodeName == "instanceKey" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
     //    {
     //       m_InstanceKey = xmlGetElementText(reader);
     //    }
     //    /*else if (nodeName == "key" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
     //    {
     //       m_Key = xmlGetElementText(reader);
     //    }*/
     //    else if (nodeName == "userPortionAor" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
     //    {
     //       m_UserPortion = xmlGetElementText(reader);
     //    }
     //    else if (nodeName == "message" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
     //    {
     //       ParseMessage(reader);
     //    }
     //    /*else if (nodeName == "error" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
     //    {
     //       ParseError(reader);
     //    }*/
     //    /*else if (nodeName == "harddiskSn" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
     //    {
     //       m_PrimaryHdd = xmlGetElementText(reader);
     //    }
     //    else if (nodeName == "macAddress" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
     //    {
     //       m_PrimaryMac = xmlGetElementText(reader);
     //    }*/
     //    else if (nodeName == "machineInformation" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
     //    {
     //       ParseMachineInformation(reader);
     //    }
     //    /*else if (nodeName == "item" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
     //    {
     //       if (xmlTextReaderHasAttributes(reader) == 1)
     //       {
     //          std::string type = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"type"));
     //          if (type == "HardDrive")
     //          {
     //             XsLib::CXsString drive(xmlGetElementText(reader).c_str());
     //             m_AllHdds.push_back( drive );
     //          }
     //          else if (type == "MacAddress")
     //          {
     //             XsLib::CXsString mac(xmlGetElementText(reader).c_str());
     //             m_AllMacs.push_back( mac );
     //          }

     //       }
     //    }*/
     //    /*else if (nodeName == "feature" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
     //    {
     //       if (xmlTextReaderHasAttributes(reader) == 1)
     //       {
     //          std::string name = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"name"));
     //          std::string enabled = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"enabled"));
     //          m_Features.push_back(FeatureItem(name, enabled=="true"? true : false));
     //       }
     //    }*/
     //    else if (nodeName == "licenses" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
     //    {
     //       ParseLicenses(reader);
     //    }
     //    else if (nodeName == "errors" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
     //    {
     //       ParseErrors(reader);
     //    }
          ret = xmlTextReaderRead(reader);
      }

      if (m_Type != Error)
      {
         m_DocVersion < 8 ? ParseVersionPre8(reader) : ParseVersion8(reader);
      }
      xmlFreeTextReader(reader);
   }
}

void
LicenseDocParser::ParseMessage(xmlTextReaderPtr reader)
{
   if (xmlTextReaderRead(reader) == 1)
   {
      bool inErrorElement = true;
      while (inErrorElement)
      {
			std::string elementName = xmlString(xmlTextReaderName(reader));
         if (elementName == "text" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            m_Message = xmlGetElementText(reader);;
         }
         else if (elementName == "url" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            m_Url = xmlGetElementText(reader);;
         }
         else if (elementName == "urlDescription" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            m_UrlDescription = xmlGetElementText(reader);;
         }
         else if (elementName == "errorCode" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            m_ErrorCode = atol(xmlGetElementText(reader).c_str());
         }
         else if (elementName == "message" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_END_ELEMENT)
         {
            inErrorElement = false;
         }

         inErrorElement = inErrorElement && (xmlTextReaderNext(reader) == 1);
      }
   }
}

void
LicenseDocParser::ParseErrorPre8(xmlTextReaderPtr reader)
{
   if (xmlTextReaderRead(reader) == 1)
   {
      bool inMessageElement = true;
      while (inMessageElement)
      {
			std::string elementName = xmlString(xmlTextReaderName(reader));
         if (elementName == "text" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            m_Message = xmlGetElementText(reader);;
         }
         else if (elementName == "url" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            m_Url = xmlGetElementText(reader);;
         }
         else if (elementName == "urlDescription" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            m_UrlDescription = xmlGetElementText(reader);
         }
         else if (elementName == "errorCode" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            m_ErrorCode = atol(xmlGetElementText(reader).c_str());
         }
         else if (elementName == "error" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_END_ELEMENT)
         {
            inMessageElement = false;
         }

         inMessageElement = inMessageElement && (xmlTextReaderNext(reader) == 1);
      }
   }
}



void LicenseDocParser::ParseMachineInformation(xmlTextReaderPtr reader)
{
   if (xmlTextReaderRead(reader) == 1)
   {
      bool inErrorElement = true;
      while (inErrorElement)
      {
			std::string elementName = xmlString(xmlTextReaderName(reader));
         if (elementName == "harddiskSn" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            m_PrimaryHdd = xmlGetElementText(reader);
         }
         else if (elementName == "macAddress" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            m_PrimaryMac = xmlGetElementText(reader);
         }
         else if (elementName == "machineInformation" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_END_ELEMENT)
         {
            inErrorElement = false;
         }

         inErrorElement = inErrorElement && (xmlTextReaderNext(reader) == 1);
      }
   }
}

void LicenseDocParser::ParseLicenses(xmlTextReaderPtr reader)
{
   if (xmlTextReaderRead(reader) == 1)
   {
      bool inErrorNode = true;
      while (inErrorNode)
      {
         std::string nodeName = xmlString(xmlTextReaderName(reader));
         if (nodeName == "license" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            m_Licenses.push_back(ParseLicense(reader));
         }
         else if (nodeName == "licenses" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_END_ELEMENT)
         {
            inErrorNode = false;
         }
         inErrorNode = inErrorNode && (xmlTextReaderNext(reader) == 1);
      }
   }
}

License LicenseDocParser::ParseLicense(xmlTextReaderPtr reader)
{
   License license;
   license.expiry = -1;
   license.gracePeriod = -1;

   if (xmlTextReaderRead(reader) == 1)
   {
      bool inErrorElement = true;
      while (inErrorElement)
      {
			std::string elementName = xmlString(xmlTextReaderName(reader));
         if (elementName == "key" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            license.key = xmlGetElementText(reader);
         }
         else if (elementName == "type" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            license.type = xmlGetElementText(reader);
         }
         //else if (elementName == "subscription" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         //{
         //   if (xmlTextReaderHasAttributes(reader) == 1)
         //   {
         //      license.expiry = atol(xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"expires")).c_str());
         //      license.gracePeriod = atol(xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"gracePeriod")).c_str());
         //   }
         //}
         //else if (elementName == "trial" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         //{
         //   if (xmlTextReaderHasAttributes(reader) == 1)
         //   {
         //      license.expiry = atol(xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"expires")).c_str());
         //   }
         //}
         else if (elementName == "expires" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            license.expiry = atol(xmlGetElementText(reader).c_str());
         }
         else if (elementName == "graceDuration" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            license.gracePeriod = atol(xmlGetElementText(reader).c_str());
         }
         else if (elementName == "features" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            license.features = parseFeatures(reader);
         }
         else if (elementName == "license" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_END_ELEMENT)
         {
            inErrorElement = false;
         }

         inErrorElement = inErrorElement && (xmlTextReaderNext(reader) == 1);
      }
   }
   return license;
}

std::vector<std::string> LicenseDocParser::parseFeatures(xmlTextReaderPtr reader)
{
   std::vector<std::string> features;
   if (xmlTextReaderRead(reader) == 1)
   {
      bool inErrorElement = true;
      while (inErrorElement)
      {
			std::string elementName = xmlString(xmlTextReaderName(reader));
         if (elementName == "feature" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            if (xmlTextReaderHasAttributes(reader) == 1)
            {
               features.push_back(xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"name")));
            }
         }
         else if (elementName == "features" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_END_ELEMENT)
         {
            inErrorElement = false;
         }

         inErrorElement = inErrorElement && (xmlTextReaderNext(reader) == 1);
      }
   }
   return features;
}

void LicenseDocParser::ParseGlobalError(xmlTextReaderPtr reader)
{
   if (xmlTextReaderRead(reader) == 1)
   {
      bool inErrorElement = true;
      while (inErrorElement)
      {
			std::string elementName = xmlString(xmlTextReaderName(reader));
         if (elementName == "message" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            m_Message = xmlGetElementText(reader);;
         }
         else if (elementName == "code" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            m_ErrorCode = atol(xmlGetElementText(reader).c_str());
         }
         else if (elementName == "text" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            m_Message = xmlGetElementText(reader);;
         }
         else if (elementName == "url" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            m_Url = xmlGetElementText(reader);;
         }
         else if (elementName == "urlDescription" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            m_UrlDescription = xmlGetElementText(reader);
         }
         else if (elementName == "errorCode" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            m_ErrorCode = atol(xmlGetElementText(reader).c_str());
         }
         else if (elementName == "error" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_END_ELEMENT)
         {
            inErrorElement = false;
         }

         inErrorElement = inErrorElement && (xmlTextReaderNext(reader) == 1);
      }
   }
}

void LicenseDocParser::ParseErrors(xmlTextReaderPtr reader)
{
   if (xmlTextReaderRead(reader) == 1)
   {
      bool inErrorNode = true;
      while (inErrorNode)
      {
         std::string nodeName = xmlString(xmlTextReaderName(reader));
         if (nodeName == "error" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            ParseError(reader);
         }
         else if (nodeName == "errors" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_END_ELEMENT)
         {
            inErrorNode = false;
         }
         inErrorNode = inErrorNode && (xmlTextReaderNext(reader) == 1);
      }
   }
}

void LicenseDocParser::ParseError(xmlTextReaderPtr reader)
{
   if (xmlTextReaderRead(reader) == 1)
   {
      InvalidLicense invalidLicense;
      invalidLicense.expiry = -1;
      invalidLicense.gracePeriod = -1;
      bool inErrorElement = true;
      bool keep = false;
      while (inErrorElement)
      {
			std::string elementName = xmlString(xmlTextReaderName(reader));
         if (elementName == "id" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            invalidLicense.id = xmlGetElementText(reader);
         }
         else if (elementName == "code" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            invalidLicense.code = atol(xmlGetElementText(reader).c_str());
         }
         else if (elementName == "message" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            invalidLicense.message = xmlGetElementText(reader);
         }
         else if (elementName == "license" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            License license = ParseLicense(reader);
            invalidLicense.key = license.key;
            invalidLicense.type = license.type;
            invalidLicense.expiry = license.expiry;
            invalidLicense.gracePeriod = license.gracePeriod;
            invalidLicense.features = license.features;
         }
         /*else if (elementName == "key" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            invalidLicense.key = xmlGetElementText(reader);
         }
         else if (elementName == "type" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            invalidLicense.type = xmlGetElementText(reader);
         }
         else if (elementName == "features" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            invalidLicense.features = parseFeatures(reader);
         }
         else if (elementName == "subscription" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
         {
            if (xmlTextReaderHasAttributes(reader) == 1)
            {
               invalidLicense.expiry = atol(xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"expires")).c_str());
               invalidLicense.gracePeriod = atol(xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"gracePeriod")).c_str());
            }
         }*/
         else if (elementName == "error" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_END_ELEMENT)
         {
            keep = true;
            inErrorElement = false;
         }

         inErrorElement = inErrorElement && (xmlTextReaderNext(reader) == 1);
      }

      if (keep)
      {
         m_InvalidLicenses.push_back(invalidLicense);
      }
   }
}
//
//LicenseDocParser::FeatureList LicenseDocParser::features() const
//{
//   FeatureList features;
//   for (auto it = m_Licenses.begin(); it != m_Licenses.end(); ++it)
//   {
//      features.insert(features.end(), (*it).features.begin(), (*it).features.end());
//   }
//   return features;
//}
//

void LicenseDocParser::ParseVersionPre8(xmlTextReaderPtr reader)
{
   std::vector<std::string> features;
   int ret = xmlTextReaderRead(reader);
   std::string key;
   while (ret == 1)
   {
      std::string nodeName = xmlString(xmlTextReaderName(reader));
      if (nodeName == "licenseType" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         m_LicenseType = xmlGetElementText(reader);
      }
      else if (nodeName == "refresh" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         if (xmlTextReaderHasAttributes(reader) == 1)
         {
				std::string delay = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"initialCheckDelaySeconds"));
            m_CheckDelay = atol(delay.c_str());

				std::string interval = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"minimumCheckIntervalSeconds"));
            m_MinInterval = atol(interval.c_str());

				std::string range = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"checkIntervalRangePercent"));
            m_EndPercent = atol(range.c_str());
         }
      }
	  else if (nodeName == "brand" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
	  {
		  m_Brand = xmlGetElementText(reader);
	  }
      else if (nodeName == "expires" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         m_Expiry = atol(xmlGetElementText(reader).c_str());
      }
      else if (nodeName == "clientTime" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         m_StartTime = atol(xmlGetElementText(reader).c_str());
      }
      else if (nodeName == "duration" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         m_Duration = atol(xmlGetElementText(reader).c_str());
      }
      else if (nodeName == "machineHash" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         m_MachineHash = xmlGetElementText(reader);
      }
      else if (nodeName == "instanceKey" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         m_InstanceKey = xmlGetElementText(reader);
      }
      else if (nodeName == "key" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         key = xmlGetElementText(reader);
      }
      else if (nodeName == "userPortionAor" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         m_UserPortion = xmlGetElementText(reader);
      }
      else if (nodeName == "message" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         ParseMessage(reader);
      }
      /*else if (nodeName == "error" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         ParseErrorPre8(reader);
      }*/
      else if (nodeName == "harddiskSn" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         m_PrimaryHdd = xmlGetElementText(reader);
      }
      else if (nodeName == "macAddress" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         m_PrimaryMac = xmlGetElementText(reader);
      }
      else if (nodeName == "item" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         if (xmlTextReaderHasAttributes(reader) == 1)
         {
            std::string type = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"type"));
            if (type == "HardDrive")
            {
               m_AllHdds.push_back(xmlGetElementText(reader));
            }
            else if (type == "MacAddress")
            {
               m_AllMacs.push_back(xmlGetElementText(reader));
            }

         }
      }
      else if (nodeName == "feature" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         if (xmlTextReaderHasAttributes(reader) == 1)
         {
            std::string name = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"name"));
            std::string enabled = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"enabled"));
            if (enabled == "true") features.push_back(name);
         }
      }
      ret = xmlTextReaderRead(reader);
   }

   if (!key.empty())
   {
      License license;
      license.key = key;
      license.features = features;
      license.type = m_LicenseType;
      m_Licenses.push_back(license);
   }
}

void LicenseDocParser::ParseVersion8(xmlTextReaderPtr reader)
{
   int ret = xmlTextReaderRead(reader);
   while (ret == 1)
   {
      std::string nodeName = xmlString(xmlTextReaderName(reader));
      /*if (nodeName == "refresh" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         if (xmlTextReaderHasAttributes(reader) == 1)
         {
				std::string delay = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"initialCheckDelaySeconds"));
            m_CheckDelay = atol(delay.c_str());

				std::string interval = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"minimumCheckIntervalSeconds"));
            m_MinInterval = atol(interval.c_str());

				std::string range = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"checkIntervalRangePercent"));
            m_EndPercent = atol(range.c_str());
         }
      }*/
      if (nodeName == "document" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         if (xmlTextReaderHasAttributes(reader) == 1)
         {
				std::string expires = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"expires"));
            m_Expiry = atol(expires.c_str());

				std::string refresh = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"refresh"));
            m_MinInterval = atol(refresh.c_str());

            std::string duration = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"duration"));
            m_Duration = atol(duration.c_str());
         }
      }
      else if (nodeName == "certificateId")
      {
         m_CertificateId = xmlGetElementText(reader);
      }
      else if (nodeName == "clientTime" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         m_StartTime = atol(xmlGetElementText(reader).c_str());
      }
      else if (nodeName == "machineHash" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         m_MachineHash = xmlGetElementText(reader);
      }
      else if (nodeName == "instanceKey" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         m_InstanceKey = xmlGetElementText(reader);
      }
      else if (nodeName == "userPortionAor" &&  xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         m_UserPortion = xmlGetElementText(reader);
      }
      else if (nodeName == "message" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         ParseMessage(reader);
      }
     /* else if (nodeName == "machineInformation" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         ParseMachineInformation(reader);
      }*/
      else if (nodeName == "harddiskSn" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         m_PrimaryHdd = xmlGetElementText(reader);
      }
      else if (nodeName == "macAddress" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
      {
         m_PrimaryMac = xmlGetElementText(reader);
      }
      else if (nodeName == "licenses" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT && !xmlTextReaderIsEmptyElement(reader))
      {
         ParseLicenses(reader);
      }
	  else if (nodeName == "brand" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT)
	  {
		  m_Brand = xmlGetElementText(reader);
	  }
      else if (nodeName == "errors" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_ELEMENT && !xmlTextReaderIsEmptyElement(reader))
      {
         ParseErrors(reader);
      }
      ret = xmlTextReaderRead(reader);
   }
}

}
}
