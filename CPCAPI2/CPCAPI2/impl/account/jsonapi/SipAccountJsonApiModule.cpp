#include "brand_branded.h"

#include "interface/experimental/account/SipAccountJsonProxy.h"
#include "interface/experimental/account/SipAccountJsonApi.h"

#if (CPCAPI2_BRAND_ACCOUNT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "SipAccountJsonServerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif
#if (CPCAPI2_BRAND_ACCOUNT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "SipAccountJsonProxyInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace SipAccount
   {
      SipAccountJsonApi* SipAccountJsonApi::getInterface(Phone* cpcPhone)
      {
         if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_ACCOUNT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<SipAccountJsonServerInterface>(phone, "SipAccountJsonApi");
#else
         return NULL;
#endif
      }

      SipAccountManagerJsonProxy* SipAccountManagerJsonProxy::getInterface(Phone* cpcPhone)
      {
         if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_ACCOUNT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<SipAccountJsonProxyInterface>(phone, "SipAccountManagerJsonProxy");
#else
         return NULL;
#endif
      }
   }
}
