#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_NETWORKCHANGEMANAGER_NETWORKTRANSPORT_H
#define JSONRPC_CPCAPI2_NETWORKCHANGEMANAGER_NETWORKTRANSPORT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace NetworkChangeManager {
  enum class NetworkTransport
  {
    None,
    Wifi,
    WWan
  };

  inline std::string to_string(NetworkTransport value)
  {
    switch (value)
    {
      case NetworkTransport::None:
        return "None";
      case NetworkTransport::Wifi:
        return "Wifi";
      case NetworkTransport::WWan:
        return "WWan";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, NetworkTransport value)
  {
    switch (value)
    {
      case NetworkTransport::None:
        os << "None";
        break;
      case NetworkTransport::Wifi:
        os << "Wifi";
        break;
      case NetworkTransport::WWan:
        os << "WWan";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const NetworkTransport& e)
  {
    switch (e)
    {
      case NetworkTransport::None:
        j = "None";
        break;
      case NetworkTransport::Wifi:
        j = "Wifi";
        break;
      case NetworkTransport::WWan:
        j = "WWan";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, NetworkTransport& e)
  {
      if (0 == j.get<std::string>().compare("None"))
    {
      e = NetworkTransport::None;
    }
    else if (0 == j.get<std::string>().compare("Wifi"))
    {
      e = NetworkTransport::Wifi;
    }
    else if (0 == j.get<std::string>().compare("WWan"))
    {
      e = NetworkTransport::WWan;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum NetworkTransport"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace NetworkChangeManager
#endif // JSONRPC_CPCAPI2_NETWORKCHANGEMANAGER_NETWORKTRANSPORT_H
