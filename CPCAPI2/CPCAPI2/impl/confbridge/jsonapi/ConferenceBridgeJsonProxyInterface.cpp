#include "brand_branded.h"

#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_JSON_PROXY_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "ConferenceBridgeJsonProxyInterface.h"
//include "confbridge/ConferenceBridgeManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiClient.h"
#include "jsonapi/JsonApiClientInterface.h"
#include "json/JsonHelper.h"
#include "util/LogSubsystems.h"
#include "util/IpHelpers.h"

#include <rutil/Logger.hxx>
#include <rutil/Random.hxx>
#include <rutil/ParseBuffer.hxx>
#include <resip/stack/SdpContents.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_CALL
#define JSON_MODULE "ConferenceBridgeJsonApi"

namespace CPCAPI2
{
namespace ConferenceBridge
{
ConferenceBridgeJsonProxyInterface::ConferenceBridgeJsonProxyInterface(Phone* phone)
   : CPCAPI2::EventSource<ConferenceHandle, ConferenceBridgeHandler, ConferenceBridgeSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
     mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mTransport(NULL),
     mHandlePrefix(0),
     mNextConferenceHandle(1),
     mNextParticipantHandle(1),
     mQueryConferenceListHandler(NULL)
{
   mFunctionMap["onConferenceDetails"] = std::bind(&ConferenceBridgeJsonProxyInterface::handleConferenceDetails, this, std::placeholders::_1);
   mFunctionMap["onConferenceEnded"] = std::bind(&ConferenceBridgeJsonProxyInterface::handleConferenceEnded, this, std::placeholders::_1);
   mFunctionMap["onConferenceList"] = std::bind(&ConferenceBridgeJsonProxyInterface::handleConferenceList, this, std::placeholders::_1);
   mFunctionMap["onParticipantListState"] = std::bind(&ConferenceBridgeJsonProxyInterface::handleParticipantListState, this, std::placeholders::_1);
   mFunctionMap["onPeerConnectionAnswer"] = std::bind(&ConferenceBridgeJsonProxyInterface::handlePeerConnectionAnswer, this, std::placeholders::_1);
   mFunctionMap["onWebParticipantCreated"] = std::bind(&ConferenceBridgeJsonProxyInterface::handleWebParticipantCreated, this, std::placeholders::_1);

   JsonApi::JsonApiClientInterface* jsonApiClientIf = dynamic_cast<JsonApi::JsonApiClientInterface*>(JsonApi::JsonApiClient::getInterface(phone));
   setTransport(jsonApiClientIf->getTransport());

   mHandlePrefix = resip::Random::getCryptoRandom();
   while ((mHandlePrefix & 0xFFFF0000) == 0)
   {
      mHandlePrefix = (mHandlePrefix + 1) << 1;
   }
   mHandlePrefix = (mHandlePrefix & 0xFFFF0000);

   mProcessCheck.reset(new int(0));
}

ConferenceBridgeJsonProxyInterface::~ConferenceBridgeJsonProxyInterface()
{
}

void ConferenceBridgeJsonProxyInterface::Release()
{
   reactorSafeReleaseAfter(&mReactor);
}

void ConferenceBridgeJsonProxyInterface::release()
{
   delete this;
}

// JsonApiClientModule
void ConferenceBridgeJsonProxyInterface::setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport)
{
   mTransport = transport;
}

int ConferenceBridgeJsonProxyInterface::processIncoming(const std::shared_ptr<rapidjson::Document>& request)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::processIncomingImpl, this, request, std::weak_ptr<int>(mProcessCheck)));
   return kSuccess;
}

void ConferenceBridgeJsonProxyInterface::processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request, const std::weak_ptr<int>& processCheck)
{
   if (std::shared_ptr<int> pc = processCheck.lock())
   {
      const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
      const char* funcName = functionObjectVal["functionName"].GetString();

      FunctionMap::iterator it = mFunctionMap.find(funcName);
      if (it != mFunctionMap.end())
      {
         it->second(functionObjectVal);
      }
   }
}

ConferenceHandle ConferenceBridgeJsonProxyInterface::createConference(const ConferenceSettings& conferenceSettings)
{
   ConferenceHandle accountHandle = mHandlePrefix | mNextConferenceHandle++;
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::createConferenceImpl, this, accountHandle, conferenceSettings, (ConferenceBridgeHandler*)NULL));
   return accountHandle;
}

ConferenceHandle ConferenceBridgeJsonProxyInterface::createConference(const ConferenceSettings& conferenceSettings, ConferenceBridgeHandler* handler)
{
   ConferenceHandle accountHandle = mHandlePrefix | mNextConferenceHandle++;
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::createConferenceImpl, this, accountHandle, conferenceSettings, handler));
   return accountHandle;
}

int ConferenceBridgeJsonProxyInterface::createConferenceImpl(ConferenceHandle conference, const ConferenceSettings& conferenceSettings, ConferenceBridgeHandler* handler)
{
   JsonFunctionCall(mTransport, "createConference", JSON_VALUE(conference), JSON_VALUE(conferenceSettings));
   if (handler != NULL)
   {
      setAppHandler(conference, handler);
   }
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::setHandler(ConferenceHandle conference, ConferenceBridgeHandler* handler)
{
   setAppHandler(conference, handler);
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::setHandler(ConferenceBridgeHandler* handler)
{
   addSdkObserver(handler);
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::start(const ConferenceBridgeConfig& bridgeConfig)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::startImpl, this, bridgeConfig));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::startImpl(const ConferenceBridgeConfig& bridgeConfig)
{
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::addSipEndpoint(ConferenceHandle conference, CPCAPI2::SipAccount::SipAccountHandle sipAccount)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::addSipEndpointImpl, this, conference, sipAccount));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::addSipEndpointImpl(ConferenceHandle conference, CPCAPI2::SipAccount::SipAccountHandle sipAccount)
{
   JsonFunctionCall(mTransport, "addSipEndpoint", JSON_VALUE(conference), JSON_VALUE(sipAccount));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::queryConferenceDetails(ConferenceHandle conference)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::queryConferenceDetailsImpl, this, conference, (ConferenceDetailsHandler*)NULL));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::queryConferenceDetailsImpl(ConferenceHandle conference, ConferenceDetailsHandler* handler)
{
   JsonFunctionCall(mTransport, "queryConferenceDetails", JSON_VALUE(conference));
   if (handler != NULL)
   {
      mQueryConferenceDetailsHandlers[resip::Data::from(conference).c_str()] = handler;
   }
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::queryConferenceDetails(ConferenceHandle conference, ConferenceDetailsHandler* handler)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::queryConferenceDetailsImpl, this, conference, handler));
   return kSuccess;
}

void ConferenceBridgeJsonProxyInterface::clearQueryConferenceListHandler()
{
   executeOnSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::clearQueryConferenceListHandlerImpl, this));
}

void ConferenceBridgeJsonProxyInterface::clearQueryConferenceListHandlerImpl()
{
   mQueryConferenceListHandler = NULL;
   mQueryConferenceDetailsHandlers.clear();
}

int ConferenceBridgeJsonProxyInterface::queryConferenceList(ConferenceListHandler* handler)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::queryConferenceListImpl, this, handler));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::queryConferenceListImpl(ConferenceListHandler* handler)
{
   mQueryConferenceListHandler = handler;
   JsonFunctionCall(mTransport, "queryConferenceList");
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::queryConferenceDetails(const cpc::string& conferenceToken, ConferenceDetailsHandler* handler)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::queryConferenceDetailsImpl2, this, conferenceToken, handler));
   return kSuccess;
}

bool extractConfToken(cpc::string& confToken, const cpc::string& conferenceUrl)
{
   resip::Data url(conferenceUrl.c_str(), conferenceUrl.size());
   const char* scratch = NULL;
   resip::ParseBuffer pb(url);
   pb.skipToChars("confToken=");
   pb.skipN(10);
   scratch = pb.position();
   pb.skipToEnd();
   confToken = pb.data(scratch).c_str();
   return true;
}

int ConferenceBridgeJsonProxyInterface::queryConferenceDetailsImpl2(const cpc::string& conferenceToken, ConferenceDetailsHandler* handler)
{
   cpc::string extractedToken = conferenceToken;
   if (conferenceToken.substr(0, 4) == "http")
   {
      extractConfToken(extractedToken, conferenceToken);
   }
   mQueryConferenceDetailsHandlers[std::string(extractedToken.c_str())] = handler;

   JsonFunctionCall(mTransport, "queryConferenceDetails2", JSON_VALUE(conferenceToken));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::destroyConference(ConferenceHandle conference)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::destroyConferenceImpl, this, conference));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::destroyConferenceImpl(ConferenceHandle conference)
{
   JsonFunctionCall(mTransport, "destroyConference", JSON_VALUE(conference));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::queryParticipantList(ConferenceHandle conference, ParticipantListHandler* handler)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::queryParticipantListImpl, this, conference, handler));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::queryParticipantListImpl(ConferenceHandle conference, ParticipantListHandler* handler)
{
   mQueryParticipantListHandlers[conference] = handler;
   JsonFunctionCall(mTransport, "queryParticipantList", JSON_VALUE(conference));
   return kSuccess;
}

ConferenceParticipantHandle ConferenceBridgeJsonProxyInterface::createWebParticipant(ConferenceHandle conference)
{
   ConferenceParticipantHandle participant = mHandlePrefix | mNextParticipantHandle++;
   WebParticipantIdentity identityInfo;
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::createWebParticipantImpl, this, participant, conference, identityInfo, false, (CreateWebParticipantHandler*)NULL));
   return participant;
}

ConferenceParticipantHandle ConferenceBridgeJsonProxyInterface::createWebParticipant(ConferenceHandle conference, CreateWebParticipantHandler* handler)
{
   ConferenceParticipantHandle participant = mHandlePrefix | mNextParticipantHandle++;
   WebParticipantIdentity identityInfo;
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::createWebParticipantImpl, this, participant, conference, identityInfo, false, handler));
   return participant;
}

ConferenceParticipantHandle ConferenceBridgeJsonProxyInterface::createWebParticipant(ConferenceHandle conference, const WebParticipantIdentity& identityInfo, bool addToFloor, CreateWebParticipantHandler* handler)
{
   ConferenceParticipantHandle participant = mHandlePrefix | mNextParticipantHandle++;
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::createWebParticipantImpl, this, participant, conference, identityInfo, addToFloor, handler));
   return participant;
}

int ConferenceBridgeJsonProxyInterface::createWebParticipantImpl(ConferenceParticipantHandle participant, ConferenceHandle conference, const WebParticipantIdentity& identityInfo, bool addToFloor, CreateWebParticipantHandler* handler)
{
   if (handler != NULL)
   {
      mCreateWebParticipantHandlers[conference] = handler;
   }
   JsonFunctionCall(mTransport, "createWebParticipant", JSON_VALUE(participant), JSON_VALUE(conference), JSON_VALUE(identityInfo), JSON_VALUE(addToFloor));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::destroyWebParticipant(ConferenceParticipantHandle participant)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::destroyWebParticipantImpl, this, participant));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::destroyWebParticipantImpl(ConferenceParticipantHandle webParticipant)
{
   DebugLog(<< "ConferenceBridgeJsonProxyInterface::destroyWebParticipantImpl(" << webParticipant << ")");
   JsonFunctionCall(mTransport, "destroyWebParticipant", JSON_VALUE(webParticipant));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::setParticipantPermissions(ConferenceParticipantHandle participant, const ParticipantPermissions& permissionsInfo)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::setParticipantPermissionsImpl, this, participant, permissionsInfo));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::setParticipantPermissionsImpl(ConferenceParticipantHandle participant, const ParticipantPermissions& permissions)
{
   JsonFunctionCall(mTransport, "setParticipantPermissions", JSON_VALUE(participant), JSON_VALUE(permissions));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::setWebParticipantIdentity(ConferenceParticipantHandle webParticipant, const WebParticipantIdentity& identityInfo)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::setWebParticipantIdentityImpl, this, webParticipant, identityInfo));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::setWebParticipantIdentityImpl(ConferenceParticipantHandle webParticipant, const WebParticipantIdentity& identityInfo)
{
   JsonFunctionCall(mTransport, "setWebParticipantIdentity", JSON_VALUE(webParticipant), "address", identityInfo.address, "displayName", identityInfo.displayName, "owner", identityInfo.owner);
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::sendPeerConnectionOffer(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpOffer, PeerConnectionAnswerHandler* handler)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::sendPeerConnectionOfferImpl, this, webParticipant, sdpOffer, handler));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::sendPeerConnectionOfferImpl(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpOffer, PeerConnectionAnswerHandler* handler)
{
   mPeerConnectionAnswerHandlers[webParticipant] = handler;
   JsonFunctionCall(mTransport, "sendPeerConnectionOffer", JSON_VALUE(webParticipant), "sdpOffer", sdpOffer.sdpString);
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::sendPeerConnectionAnswer(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpAnswer)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::sendPeerConnectionAnswerImpl, this, webParticipant, sdpAnswer));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::sendPeerConnectionAnswerImpl(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpAnswer)
{
   JsonFunctionCall(mTransport, "sendPeerConnectionAnswer", JSON_VALUE(webParticipant), "sdpOffer", sdpAnswer.sdpString);
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::addAssociatedConference(ConferenceHandle conference, ConferenceHandle associatedConference)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::addAssociatedConferenceImpl, this, conference, associatedConference));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::addAssociatedConferenceImpl(ConferenceHandle conference, ConferenceHandle associatedConference)
{
   JsonFunctionCall(mTransport, "addAssociatedConference", JSON_VALUE(conference), JSON_VALUE(associatedConference));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::requestFloor(ConferenceParticipantHandle requestor)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::requestFloorImpl, this, requestor));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::requestFloorImpl(ConferenceParticipantHandle requestor)
{
   JsonFunctionCall(mTransport, "requestFloor", JSON_VALUE(requestor));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::addToFloor(ConferenceParticipantHandle participant)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::addToFloorImpl, this, participant));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::addToFloorImpl(ConferenceParticipantHandle participant)
{
   JsonFunctionCall(mTransport, "addToFloor", JSON_VALUE(participant));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::removeFromFloor(ConferenceParticipantHandle participant)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::removeFromFloorImpl, this, participant));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::removeFromFloorImpl(ConferenceParticipantHandle participant)
{
   JsonFunctionCall(mTransport, "removeFromFloor", JSON_VALUE(participant));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::setVideoLayout(ConferenceHandle conference, VideoLayout layout)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::setVideoLayoutImpl, this, conference, layout));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::setVideoLayoutImpl(ConferenceHandle conference, VideoLayout layout)
{
   JsonFunctionCall(mTransport, "setVideoLayout", JSON_VALUE(conference), JSON_VALUE(layout));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::setStreamingEnabled(ConferenceHandle conference, bool enabled)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::setStreamingEnabledImpl, this, conference, enabled));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::setStreamingEnabledImpl(ConferenceHandle conference, bool enabled)
{
   JsonFunctionCall(mTransport, "setStreamingEnabled", JSON_VALUE(conference), JSON_VALUE(enabled));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::setRecordingEnabled(ConferenceHandle conference, bool enabled)
{
   postToSdkThread(resip::resip_safe_bind(&ConferenceBridgeJsonProxyInterface::setRecordingEnabledImpl, this, conference, enabled));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::setRecordingEnabledImpl(ConferenceHandle conference, bool enabled)
{
   JsonFunctionCall(mTransport, "setRecordingEnabled", JSON_VALUE(conference), JSON_VALUE(enabled));
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::handleConferenceDetails(const rapidjson::Value& functionObjectVal)
{
   ConferenceDetailsResult args;
   JsonDeserialize(functionObjectVal, "conference", args.conference, "conferenceToken", args.conferenceToken, "conferenceInfo", args.conferenceInfo, "persistent", args.persistent);

   std::map<std::string, ConferenceDetailsHandler*>::iterator itQry = mQueryConferenceDetailsHandlers.find(std::string(args.conferenceToken.c_str()));
   if (itQry != mQueryConferenceDetailsHandlers.end())
   {
      postCallback(makeFpCommand(ConferenceDetailsHandler::onConferenceDetails, itQry->second, args.conference, args));
      mQueryConferenceDetailsHandlers.erase(itQry);
   }
   else
   {
      itQry = mQueryConferenceDetailsHandlers.find(resip::Data::from(args.conference).c_str());
      if (itQry != mQueryConferenceDetailsHandlers.end())
      {
         postCallback(makeFpCommand(ConferenceDetailsHandler::onConferenceDetails, itQry->second, args.conference, args));
         mQueryConferenceDetailsHandlers.erase(itQry);
      }
      else
      {
         fireEvent(cpcFunc(ConferenceBridgeHandler::onConferenceDetails), args.conference, args);
      }
   }

   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::handleConferenceEnded(const rapidjson::Value& functionObjectVal)
{
   ConferenceHandle conf = -1;
   ConferenceEndedEvent args;
   JsonDeserialize(functionObjectVal, "conference", conf);
   fireEvent(cpcFunc(ConferenceBridgeHandler::onConferenceEnded), conf, args);

   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::handleConferenceList(const rapidjson::Value& functionObjectVal)
{
   if (mQueryConferenceListHandler != NULL)
   {
      ConferenceListResult confListRes;
      JsonDeserialize(functionObjectVal, "conferences", confListRes.conferences);

      mQueryConferenceListHandler->onConferenceList(confListRes);
      mQueryConferenceListHandler = NULL;
   }
   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::handleParticipantListState(const rapidjson::Value& functionObjectVal)
{
   ConferenceHandle conference = -1;
   ParticipantListState args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(conference), "isPartialUpdate", args.isPartialUpdate, "isLastChunk", args.isLastChunk, "participantsArray", args.participants, "addedParticipantsArray", args.addedParticipants,
      "updatedParticipantsArray", args.updatedParticipants, "removedParticipantsArray", args.removedParticipants);

   auto it = mQueryParticipantListHandlers.find(conference);
   if (it != mQueryParticipantListHandlers.end())
   {
      postCallback(makeFpCommand(ParticipantListHandler::onParticipantListState, it->second, conference, args));
      mQueryParticipantListHandlers.erase(it);
   }
   else
   {
      fireEvent(cpcFunc(ConferenceBridgeHandler::onParticipantListState), conference, args);
   }

   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::handlePeerConnectionAnswer(const rapidjson::Value& functionObjectVal)
{
   ConferenceHandle conf = -1;
   PeerConnectionAnswerEvent args;
   cpc::string sdpStr;

   JsonDeserialize(functionObjectVal, "conference", conf, "participant", args.participant, "sdpAnswer", sdpStr);

   CPCAPI2::PeerConnection::SessionDescription sdp;
   sdp.sdpLen = (unsigned short)sdpStr.size();
   sdp.sdpString = sdpStr;
   sdp.sdpType = CPCAPI2::PeerConnection::SessionDescription::SessionDescriptionType_Answer;
   args.sdpAnswer = sdp;

   auto it = mPeerConnectionAnswerHandlers.find(args.participant);
   if (it != mPeerConnectionAnswerHandlers.end())
   {
      it->second->onPeerConnectionAnswer(conf, args);
      mPeerConnectionAnswerHandlers.erase(it);
   }

   return kSuccess;
}

int ConferenceBridgeJsonProxyInterface::handleWebParticipantCreated(const rapidjson::Value& functionObjectVal)
{
   ConferenceHandle conf = -1;
   WebParticipantCreatedEvent evt;

   JsonDeserialize(functionObjectVal, "conference", conf, "participant", evt.participant, "success", evt.success, "hasFloor", evt.hasFloor);
   evt.conference = conf;

   auto it = mCreateWebParticipantHandlers.find(evt.conference);
   if (it != mCreateWebParticipantHandlers.end())
   {
      it->second->onWebParticipantCreated(evt.conference, evt);
      mCreateWebParticipantHandlers.erase(it);
   }
   return kSuccess;
}
}
}
#endif
