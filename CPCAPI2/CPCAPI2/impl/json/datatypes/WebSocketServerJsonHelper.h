// Generated file - Do not edit
// To regenerate this file run langauge_wrapper/JSON/JsonDataTypes

#pragma once

#ifndef WebSocketServer_JSON_HELPER_H
#define WebSocketServer_JSON_HELPER_H

#pragma warning(push)
#pragma warning(disable: 4003)

#include <interface/experimental/websocket_server/WebSocketServerTypes.h>
#include <interface/experimental/websocket_server/WebSocketServerHandler.h>

#include "../SerializationHelperMacros.h"

namespace CPCAPI2
{
  namespace Json
  {
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::WebSocketServer::WebSocketServerSettings& v)
    {
      w.StartObject();
      Write(w, "certificatePem", v.certificatePem);
      Write(w, "privateKeyPem", v.privateKeyPem);
      Write(w, "ciphers", v.ciphers);
      Write(w, "url", v.url);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::WebSocketServer::WebSocketServerSettings& v)
    {
      Read(json, "certificatePem", v.certificatePem);
      Read(json, "privateKeyPem", v.privateKeyPem);
      Read(json, "ciphers", v.ciphers);
      Read(json, "url", v.url);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::WebSocketServer::ConnectionOpenedEvent& v)
    {
      w.StartObject();
      Write(w, "connection", v.connection);
      Write(w, "requestHost", v.requestHost);
      Write(w, "requestPort", v.requestPort);
      Write(w, "requestResource", v.requestResource);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::WebSocketServer::ConnectionOpenedEvent& v)
    {
      Read(json, "connection", v.connection);
      Read(json, "requestHost", v.requestHost);
      Read(json, "requestPort", v.requestPort);
      Read(json, "requestResource", v.requestResource);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::WebSocketServer::ConnectionClosedEvent& v)
    {
      w.StartObject();
      Write(w, "connection", v.connection);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::WebSocketServer::ConnectionClosedEvent& v)
    {
      Read(json, "connection", v.connection);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::WebSocketServer::MessageReceivedEvent& v)
    {
      w.StartObject();
      Write(w, "connection", v.connection);
      Write(w, "data", v.data);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::WebSocketServer::MessageReceivedEvent& v)
    {
      Read(json, "connection", v.connection);
      Read(json, "data", v.data);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::WebSocketServer::ServerStateChangeEvent& v)
    {
      w.StartObject();
      Write(w, "currentState", v.currentState);
      Write(w, "message", v.message);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::WebSocketServer::ServerStateChangeEvent& v)
    {
      Read(json, "currentState", v.currentState);
      Read(json, "message", v.message);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::WebSocketServer::ErrorEvent& v)
    {
      w.StartObject();
      Write(w, "connection", v.connection);
      Write(w, "errorText", v.errorText);
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::WebSocketServer::ErrorEvent& v)
    {
      Read(json, "connection", v.connection);
      Read(json, "errorText", v.errorText);
    }
    template<class Writer>
    inline void Serialize(Writer& w, const CPCAPI2::WebSocketServer::WebSocketServerHandler& v)
    {
      w.StartObject();
      w.EndObject();
    }
    inline void Deserialize(const rapidjson::Value& json, CPCAPI2::WebSocketServer::WebSocketServerHandler& v)
    {
    }
  }
}
#pragma warning(pop)
#endif // WebSocketServer_JSON_HELPER_H