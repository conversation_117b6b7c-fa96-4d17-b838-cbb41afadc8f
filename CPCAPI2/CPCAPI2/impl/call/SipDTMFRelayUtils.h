#pragma once

#if !defined(CPCAPI2_SIPDTMFRELAYUTILS_H)
#define CPCAPI2_SIPDTMFRELAYUTILS_H

namespace resip
{
   class Contents;
}

namespace CPCAPI2
{
namespace SipConversation
{

class SipDTMFRelayUtils
{
public:

   enum
   {
      EDurationMinimumMs = 100,
      EDurationDefaultMs = 250,
      EDurationMaximumMs = 5000
   };
   
   /**
    * Allocates a new resip::Contents structure (this must be deallocated
    * by the caller somehow).
    */
   static resip::Contents *MakeDTMFRelayContents(
      const unsigned int   inToneId, 
      unsigned long        ulInDuration = EDurationDefaultMs
   );
   
   static bool ParseDTMFInfo(
      const resip::Contents&  cInContents,
      unsigned int&           outToneId,
      unsigned long&          ulOutDuration
   );

private:
   SipDTMFRelayUtils();

};

}
}

#endif
