#include "brand_branded.h"

#if (CPCAPI2_BRAND_CONFERENCE_BRIDGE_MODULE == 1)
#include "cpcapi2utils.h"
#include "confbridge/ConferenceBridgeHandler.h"
#include "media/MediaManager.h"
#include "media/video/Video.h"
#include "ConferenceBridgeManagerInterface.h"
#include "ConferenceBridgeManagerImpl.h"
#include "peerconnection/PeerConnectionManagerInterface.h"
#include "jsonapi/JsonApiServerSendTransportInterface.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "jsonapi/JsonApiServer_HTTP.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"

#include <rutil/ParseBuffer.hxx>
#include <rutil/Random.hxx>

#include "websocketpp/uri.hpp"

#include <curlpp/cURLpp.hpp>

// rapidjson
#include <writer.h>
#include <prettywriter.h>
#include <stringbuffer.h>
#include <document.h>

#include <time.h>

using namespace resip;

using resip::ReadCallbackBase;

#define RANDOM_PATH_COMPONENT_LENGTH 8
#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER

namespace CPCAPI2
{
namespace ConferenceBridge
{
ConferenceHandle ConferenceBridgeHandleFactory::sNextHandle = 0;
ConferenceParticipantHandle ConferenceParticipantHandleFactory::sNextHandle = 1;
resip::Mutex ConferenceIdGenerator::sAllocatedIdsMtx;
std::set<resip::Data> ConferenceIdGenerator::sAllocatedIds;

ConferenceBridgeManagerInterface::ConferenceBridgeManagerInterface(Phone* phone)
   : mShutdown(false),
     mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mCustomVideoSourceUsage(new RefCountedCameraUsage(phone)),
     mConfRegistrar(NULL)
{
   mConfRegistrar = ConferenceRegistrar::getInterface(phone);
}

ConferenceBridgeManagerInterface::~ConferenceBridgeManagerInterface()
{
   mShutdown = true;
}

void ConferenceBridgeManagerInterface::Release()
{
   StackLog(<< "ConferenceBridgeManagerInterface::Release(): " << this << " phone: " << mPhone);
   reactorSafeReleaseAfter(&mPhone->getSdkModuleThread());
}

void ConferenceBridgeManagerInterface::release()
{
   StackLog(<< "ConferenceBridgeManagerInterface::release(): " << this << " phone: " << mPhone);
   delete this;
}

int ConferenceBridgeManagerInterface::process(unsigned int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return kConferenceBridgeModuleDisabled;
   }
   ReadCallbackBase* fp = mCallbackFifo.getNext(timeout);
   while(fp)
   {
      (*fp)();
      delete fp;
      if (mShutdown)
      {
         return kConferenceBridgeModuleDisabled;
      }
      fp = mCallbackFifo.getNext(kBlockingModeNonBlocking);
   }
   return kSuccess;
}

ConferenceBridgeManagerInternal* ConferenceBridgeManagerInternal::getInternalInterface(Phone* cpcPhone)
{
   return static_cast<ConferenceBridgeManagerInternal*>(ConferenceBridgeManager::getInterface(cpcPhone));
}

void ConferenceBridgeManagerInterface::setCallbackHook(void(*cbHook)(void*), void* context)
{
   mCbHook = std::bind(cbHook, context);
}

#ifdef CPCAPI2_AUTO_TEST
AutoTestReadCallback* ConferenceBridgeManagerInterface::process_test(int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return NULL;
   }
   resip::ReadCallbackBase* rcb = mCallbackFifo.getNext(timeout);
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
   {
      return fpCmd;
   }
   if (rcb != NULL)
   {
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   }
   return NULL;
}
#endif

void ConferenceBridgeManagerInterface::post(ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void ConferenceBridgeManagerInterface::interruptProcess()
{
   mPhone->getSdkModuleThread().getAsyncProcessHandler()->handleProcessNotification();
}
   
void ConferenceBridgeManagerInterface::postCallback(ReadCallbackBase* command)
{
   mCallbackFifo.add(command);
   if (mCbHook) { mCbHook(); }
}

void ConferenceBridgeManagerInterface::addSdkObserver(ConferenceBridgeHandler* observer)
{
   mSdkObservers.insert(observer);
}

void ConferenceBridgeManagerInterface::removeSdkObserver(ConferenceBridgeHandler* observer)
{
   mSdkObservers.erase(observer);
}

void ConferenceBridgeManagerInterface::addParticipantMapping(ConferenceParticipantHandle participant, ConferenceHandle conference)
{
   mMapWebParticipantToConference[participant] = conference;
}

void ConferenceBridgeManagerInterface::removeParticipantMapping(ConferenceParticipantHandle participant)
{
   mMapWebParticipantToConference.erase(participant);
}

ConferenceHandle ConferenceBridgeManagerInterface::createConference(const ConferenceSettings& conferenceSettings)
{
   ConferenceHandle h = ConferenceBridgeHandleFactory::getNext();
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::createConferenceImpl, this, h, conferenceSettings, (ConferenceBridgeHandler*)NULL));
   return h;
}

ConferenceHandle ConferenceBridgeManagerInterface::createConference(const ConferenceSettings& conferenceSettings, ConferenceBridgeHandler* handler)
{
   ConferenceHandle h = ConferenceBridgeHandleFactory::getNext();
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::createConferenceImpl, this, h, conferenceSettings, handler));
   return h;
}

int ConferenceBridgeManagerInterface::createConference(ConferenceHandle conference, const ConferenceSettings& conferenceSettings, ConferenceBridgeHandler* handler)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::createConferenceImpl, this, conference, conferenceSettings, handler));
   return kSuccess;
}

class RegisterConferenceResultHandlerFuncObj : public RegisterConferenceResultHandler
{
public:
   RegisterConferenceResultHandlerFuncObj(const std::function<void(ConferenceRegistrarHandle, const RegisterConferenceResult&)>& func) : mFunc(func) {}
   virtual ~RegisterConferenceResultHandlerFuncObj() {}

   virtual int onRegisterConferenceComplete(ConferenceRegistrarHandle registrar, const RegisterConferenceResult& args) override {
      mFunc(registrar, args);
      delete this;
      return kSuccess;
   }
   virtual bool synchronous() const override {
      return true;
   }

private:
   std::function<void(ConferenceRegistrarHandle, const RegisterConferenceResult&)> mFunc;
};

int ConferenceBridgeManagerInterface::createConferenceImpl(ConferenceHandle conference, const ConferenceSettings& conferenceSettings, ConferenceBridgeHandler* handler)
{
   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*> conferencesLocalCpy = mConferences;
   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = conferencesLocalCpy.begin();
   for (; it != conferencesLocalCpy.end(); ++it)
   {
      if (it->second->getConferenceSettings().conferenceToken == conferenceSettings.conferenceToken)
      {
         break;
      }
   }

   ConferenceBridgeManagerImpl* impl = NULL;
   if (it != conferencesLocalCpy.end())
   {
      impl = it->second;
   }
   else
   {
      impl = new ConferenceBridgeManagerImpl(conference, mPhone, this);
      impl->setSdkObservers(&mSdkObservers);
      impl->setConferenceSettings(conferenceSettings);
      InfoLog(<< "createConferenceImpl creating conference " << conference << ", conferenceToken: " << impl->getConferenceToken());
      mConferences[conference] = impl;
      if (handler != NULL)
      {
         setHandlerImpl(conference, handler);
      }
      updateConferenceSummaryTs();
   }
   impl->queryConferenceDetails();
   return 0;
}

int ConferenceBridgeManagerInterface::setHandler(ConferenceHandle conference, ConferenceBridgeHandler* handler)
{
   ReadCallbackBase* rcb = resip::resip_safe_bind(&ConferenceBridgeManagerInterface::setHandlerImpl, this, conference, handler);
   post(rcb);
   return kSuccess;
}

int ConferenceBridgeManagerInterface::setHandlerImpl(ConferenceHandle conference, ConferenceBridgeHandler* handler)
{
   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(conference);
   if (it != mConferences.end())
   {
      it->second->setHandler(handler);
      return kSuccess;
   }
   return kError;
}

int ConferenceBridgeManagerInterface::setHandler(ConferenceBridgeHandler* handler)
{
   ReadCallbackBase* rcb = resip::resip_safe_bind(&ConferenceBridgeManagerInterface::addSdkObserver, this, handler);
   post(rcb);
   return kSuccess;
}

int ConferenceBridgeManagerInterface::start(const ConferenceBridgeConfig& bridgeConfig)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::startImpl, this, bridgeConfig));
   return kSuccess;
}

int ConferenceBridgeManagerInterface::startImpl(const ConferenceBridgeConfig& bridgeConfig)
{
   InfoLog(<< "Starting conference bridge " << bridgeConfig.userContext);

   mBridgeConfig = bridgeConfig;

   mConferenceUrlRandomness = bridgeConfig.userContext;
   updateJoinUrlTs();

   CPCAPI2::PeerConnection::PeerConnectionManagerInterface* peerConnMgrIf = dynamic_cast<CPCAPI2::PeerConnection::PeerConnectionManagerInterface*>(CPCAPI2::PeerConnection::PeerConnectionManager::getInterface(mPhone));
   peerConnMgrIf->preinitDns(bridgeConfig.natTraversalServerInfo.natTraversalServerHostname, bridgeConfig.natTraversalServerInfo.natTraversalServerPort);

   return kSuccess;
}

template <typename T>
void ConferenceBridgeManagerInterface::handleUsersRequest(std::shared_ptr<typename SimpleWeb::Server<T>::Response> response, std::shared_ptr<typename SimpleWeb::Server<T>::Request> request)
{
   static std::string STR_SCREENSHARE_PATH = "/screenshare/";
   std::string::size_type cidPos = request->path.rfind(STR_SCREENSHARE_PATH);
   if (cidPos == std::string::npos)
   {
      *response << "HTTP/1.1 400 Missing cid\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   std::string randomBit = request->path.substr(cidPos + STR_SCREENSHARE_PATH.size() + 1);
   if (randomBit.empty())
   {
      *response << "HTTP/1.1 400 Missing Random Path Component\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   if (randomBit.at(randomBit.size()-1) == '/')
   {
      randomBit = randomBit.substr(0, randomBit.size() - 1);
   }

   if (randomBit.compare(mConferenceUrlRandomness.c_str()) != 0)
   {
      *response << "HTTP/1.1 400 Random Path Mismatch\r\nContent-Length: 0\r\n\r\n";
      return;
   }

   rapidjson::Document respJson;
   respJson.SetObject();

   std::stringstream ssUrl;
   ssUrl << mBridgeConfig.wsUrlBase;
   if (!(mBridgeConfig.wsUrlBase.substr(mBridgeConfig.wsUrlBase.size() - 1, 1) == "/"))
   {
      ssUrl << "/";
   }
   ssUrl << mBridgeConfig.userContext;

   rapidjson::Value wsUrlVal(ssUrl.str().c_str(), respJson.GetAllocator());
   respJson.AddMember("websocketUrl", wsUrlVal, respJson.GetAllocator());

   rapidjson::StringBuffer buffer(0, 1024);
   rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
   respJson.Accept(writer);

   SimpleWeb::CaseInsensitiveMultimap respHeaders;
   respHeaders.emplace("Access-Control-Allow-Origin", "*");
   respHeaders.emplace("Access-Control-Allow-Headers", "Authorization");
   respHeaders.emplace("Cach e-Control", "no-cache, no-store, must-revalidate");
   response->write(std::string(buffer.GetString(), buffer.GetSize()), respHeaders);
}

template <typename T>
void ConferenceBridgeManagerInterface::handleStatusRequest(std::shared_ptr<typename SimpleWeb::Server<T>::Response> response, std::shared_ptr<typename SimpleWeb::Server<T>::Request> request)
{
   cpc::string summary;
   getConferenceSummary(summary);

   SimpleWeb::CaseInsensitiveMultimap allowOriginHeader;
   allowOriginHeader.emplace("Access-Control-Allow-Origin", "*");
   response->write(std::string(summary.c_str(), summary.size()), allowOriginHeader);
}

int ConferenceBridgeManagerInterface::getConferenceSummary(cpc::string& summary)
{
   resip::Lock l(mThreadSafeVarsMtx);
   summary = mThreadSafeConferenceSummary.c_str();
   return kSuccess;
}

std::string mixModeToString(CPCAPI2::ConferenceBridge::ConferenceMixMode mixMode)
{
   if (mixMode == CPCAPI2::ConferenceBridge::ConferenceMixMode_MCU)
      return "MCU";
   if (mixMode == CPCAPI2::ConferenceBridge::ConferenceMixMode_SFU)
      return "SFU";
   if (mixMode == CPCAPI2::ConferenceBridge::ConferenceMixMode_NoMixing)
      return "NoMixing";
   if (mixMode == CPCAPI2::ConferenceBridge::ConferenceMixMode_Legacy)
      return "Legacy";
   if (mixMode == CPCAPI2::ConferenceBridge::ConferenceMixMode_SFU_BiDi)
      return "SFU_BiDi";

   return "Unknown";
}

cpc::string ConferenceBridgeManagerInterface::getConferenceSummaryImpl()
{
   // format: {"numConferences":0,"conferences":[]}
   rapidjson::Document respJson;
   respJson.SetObject();

   //CPCAPI2::JsonApi::JsonApiServerInterface* jsonServerIf = dynamic_cast<CPCAPI2::JsonApi::JsonApiServerInterface*>(CPCAPI2::JsonApi::JsonApiServer::getInterface(mPhone));

   unsigned int numConferencesInt = mConferences.size();
   respJson.AddMember("numConferences", numConferencesInt, respJson.GetAllocator());
   rapidjson::Value confUrlRandomnessVal(mConferenceUrlRandomness.c_str(), respJson.GetAllocator());
   respJson.AddMember("conferenceBridgeUid", confUrlRandomnessVal, respJson.GetAllocator());

   rapidjson::Value confsArray(rapidjson::kArrayType);

   if (!mConferences.empty())
   {
      auto iconf = mConferences.begin();

      for (; iconf != mConferences.end(); ++iconf)
      {
         rapidjson::Value confItemObj(rapidjson::kObjectType);
         ConferenceDetailsResult confDetails;
         iconf->second->getConferenceDetails(confDetails);
         rapidjson::Value conferenceJoinUrlVal(confDetails.conferenceJoinUrl.c_str(), respJson.GetAllocator());
         confItemObj.AddMember("conferenceJoinUrl", conferenceJoinUrlVal, respJson.GetAllocator());

         rapidjson::Value confLabelVal(iconf->second->getConferenceInfo().label.c_str(), respJson.GetAllocator());
         confItemObj.AddMember("label", confLabelVal, respJson.GetAllocator());
         std::string mixModeStr = mixModeToString(iconf->second->getConferenceSettings().mixMode);
         rapidjson::Value mixModeVal(mixModeStr.c_str(), respJson.GetAllocator());
         confItemObj.AddMember("mixMode", mixModeVal, respJson.GetAllocator());
         confItemObj.AddMember("numParticipants", iconf->second->getConferenceInfo().numParticipants, respJson.GetAllocator());

         rapidjson::Value partsArray(rapidjson::kArrayType);
         const std::vector<cpc::string> parts = iconf->second->getParticipantInfoSummary();
         for (auto itParts = parts.begin(); itParts != parts.end(); ++itParts)
         {
            rapidjson::Value partAddrVal(itParts->c_str(), respJson.GetAllocator());
            partsArray.PushBack(partAddrVal, respJson.GetAllocator());
         }
         confItemObj.AddMember("participants", partsArray, respJson.GetAllocator());
         confItemObj.AddMember("websocketVideoStreams", iconf->second->getNumVideoStreams(), respJson.GetAllocator());
         confsArray.PushBack(confItemObj, respJson.GetAllocator());
      }
   }
   respJson.AddMember("conferences", confsArray, respJson.GetAllocator());

   rapidjson::StringBuffer buffer(0, 1024);
   rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
   respJson.Accept(writer);

   return cpc::string(buffer.GetString(), buffer.GetSize());
}

int ConferenceBridgeManagerInterface::queryMediaStatistics(ConferenceHandle conference)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::queryMediaStatisticsImpl, this, conference));
   return kSuccess;
}

int ConferenceBridgeManagerInterface::queryMediaStatisticsImpl(ConferenceHandle conference)
{
   DebugLog(<< "ConferenceBridgeManagerInterface::queryMediaStatisticsImpl(): conference: " << conference << " from conference-list count: " << mConferences.size());

   for (std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator i = mConferences.begin(); i != mConferences.end(); ++i)
   {
      // StackLog(<< "ConferenceBridgeManagerInterface::queryMediaStatisticsImpl(): conference: " << i->first << " impl: " << i->second << " participants: " << i->second->getNumParticipants());
      if ((i->first != 0) && (i->second->getNumParticipants() > 0))
      {
         DebugLog(<< "ConferenceBridgeManagerInterface::queryMediaStatisticsImpl(): sending query for conference: " << i->first << " impl: " << i->second << " with " << i->second->getNumParticipants() << " participants");
         ConferenceBridgeManagerImpl* impl = i->second;
         impl->queryMediaStatistics();
      }
   }

   /*
   // TODO: Support conference specific stats, rather than all confrerences
   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(conference);
   if (it != mConferences.end())
   {
      ConferenceBridgeManagerImpl* impl = it->second;
      impl->queryMediaStatistics();
      return kSuccess;
   }
   */
   return kSuccess;
}

int ConferenceBridgeManagerInterface::shutdown()
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::shutdownImpl, this));
   return kSuccess;
}

int ConferenceBridgeManagerInterface::shutdownImpl()
{
   InfoLog(<< "ConferenceBridgeManager shutdown()");

   return kSuccess;
}

int ConferenceBridgeManagerInterface::destroyConference(ConferenceHandle conference)
{
   ReadCallbackBase* rcb = resip::resip_safe_bind(&ConferenceBridgeManagerInterface::destroyConferenceImpl, this, conference);
   post(rcb);
   return kSuccess;
}

int ConferenceBridgeManagerInterface::destroyConferenceImpl(ConferenceHandle conference)
{
   InfoLog(<< "ConferenceBridgeManagerInterface::destroyConferenceImpl(" << conference << ")");
   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(conference);
   if (it != mConferences.end())
   {
      std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator itPar = mConferences.find(it->second->getParentConference());
      if (itPar != mConferences.end())
      {
         itPar->second->removeAssociatedConference(conference);
      }
      ConferenceBridgeManagerImpl* impl = it->second;
      InfoLog(<< "destroyConferenceImpl destroying conference " << conference);
      mConferences.erase(it);
      impl->destroyConference();
      delete impl;
      updateConferenceSummaryTs();
      return kSuccess;
   }
   return kError;
}


int ConferenceBridgeManagerInterface::addSipEndpoint(ConferenceHandle conference, CPCAPI2::SipAccount::SipAccountHandle sipAccount)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::addSipEndpointImpl, this, conference, sipAccount));
   return 0;
}

int ConferenceBridgeManagerInterface::addSipEndpointImpl(ConferenceHandle conference, CPCAPI2::SipAccount::SipAccountHandle sipAccount)
{
   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(conference);
   if (it != mConferences.end())
   {
      it->second->addSipEndpoint(sipAccount);
   }
   return 0;
}

int ConferenceBridgeManagerInterface::queryConferenceDetails(ConferenceHandle conference)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::queryConferenceDetailsImpl, this, conference, (ConferenceDetailsHandler*)NULL));
   return 0;
}

int ConferenceBridgeManagerInterface::queryConferenceDetailsImpl(ConferenceHandle conference, ConferenceDetailsHandler* handler)
{
   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(conference);
   if (it != mConferences.end())
   {
      it->second->queryConferenceDetails(handler);
   }
   return 0;
}

int ConferenceBridgeManagerInterface::queryConferenceDetails(ConferenceHandle conference, ConferenceDetailsHandler* handler)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::queryConferenceDetailsImpl, this, conference, handler));
   return 0;
}

int ConferenceBridgeManagerInterface::queryConferenceDetails(const cpc::string& conferenceToken, ConferenceDetailsHandler* handler)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::queryConferenceDetailsImpl2, this, conferenceToken, handler));
   return 0;
}

bool ConferenceBridgeManagerInterface::extractConfToken(cpc::string& confToken, const cpc::string& conferenceUrl)
{
   resip::Data url(conferenceUrl.c_str(), conferenceUrl.size());
   const char* scratch = NULL;
   resip::ParseBuffer pb(url);
   pb.skipToChars("confToken=");
   pb.skipN(10);
   scratch = pb.position();
   pb.skipToEnd();
   confToken = pb.data(scratch).c_str();
   return true;
}

void ConferenceBridgeManagerInterface::fireConfNotFoundEvent(ConferenceDetailsHandler* handler, ConferenceHandle confHandle, const ConferenceNotFoundResult& args)
{
   resip::ReadCallbackBase* cb = makeFpCommandNew("ConferenceDetailsHandler::onConferenceNotFound", &ConferenceDetailsHandler::onConferenceNotFound, handler, confHandle, args);
   if (dynamic_cast<ConferenceBridgeSyncHandler*>(handler) != NULL)
   {
      (*cb)();
      delete cb;
   }
   else
   {
      postCallback(cb);
   }
}

void ConferenceBridgeManagerInterface::fireConferenceListEvent(ConferenceListHandler* handler, const ConferenceListResult& args)
{
   resip::ReadCallbackBase* cb = makeFpCommand1(ConferenceListHandler::onConferenceList, handler, args);
   if (dynamic_cast<ConferenceBridgeSyncHandler*>(handler) != NULL)
   {
      (*cb)();
      delete cb;
   }
   else
   {
      postCallback(cb);
   }
}

void ConferenceBridgeManagerInterface::fireWebParticipantCreatedEvent(CreateWebParticipantHandler* handler, ConferenceHandle confHandle, const WebParticipantCreatedEvent& args)
{
   resip::ReadCallbackBase* cb = makeFpCommand(CreateWebParticipantHandler::onWebParticipantCreated, handler, confHandle, args);
   if (dynamic_cast<ConferenceBridgeSyncHandler*>(handler) != NULL)
   {
      (*cb)();
      delete cb;
   }
   else
   {
      postCallback(cb);
   }
}

int ConferenceBridgeManagerInterface::queryConferenceDetailsImpl2(const cpc::string& conferenceToken, ConferenceDetailsHandler* handler)
{
   cpc::string extractedToken;
   if (conferenceToken.substr(0, 4) == "http")
   {
      extractConfToken(extractedToken, conferenceToken);
   }
   else
   {
      extractedToken = conferenceToken;
   }
   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.begin();
   ConferenceBridgeManagerImpl* implPtr = NULL;
   for (; it != mConferences.end(); ++it)
   {
      if (it->second->getConferenceToken() == extractedToken || std::strncmp(extractedToken.c_str(), "default", 7) == 0)
      {
         implPtr = it->second;
         break;
      }
   }
   if (implPtr != NULL)
   {
      implPtr->queryConferenceDetails(handler);
   }
   else
   {
      // no conferences exist, but we still need to call the handler
      fireConfNotFoundEvent(handler, (ConferenceHandle)-1, ConferenceNotFoundResult());
   }
   return 0;
}

int ConferenceBridgeManagerInterface::queryConferenceList(ConferenceListHandler* handler)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::queryConferenceListImpl, this, "", handler));
   return 0;
}

int ConferenceBridgeManagerInterface::queryConferenceList(const cpc::string& conferenceToken, ConferenceListHandler* handler)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::queryConferenceListImpl, this, conferenceToken, handler));
   return 0;
}

int ConferenceBridgeManagerInterface::queryConferenceListImpl(const cpc::string& conferenceToken, ConferenceListHandler* handler)
{
   cpc::string tokenMatch;
   if (!conferenceToken.empty())
   {
      // this is in the format https://ss-staging.softphone.com/screenshare/CUFVWIHH/conf/ABCD?displayName=XYZ
      // note that the displayName querystring param is meaningless (it's a Glance thing)
      std::string joinUrlStr = conferenceToken.c_str();

      // if there's a query string, get rid of it
      size_t qsPos = joinUrlStr.rfind("?");
      if (qsPos != std::string::npos)
      {
         joinUrlStr = joinUrlStr.substr(0, qsPos);
      }
      size_t ssPos = joinUrlStr.rfind("/screenshare/");
      if (ssPos != std::string::npos)
      {
         ssPos += 13;
         joinUrlStr = joinUrlStr.substr(ssPos); // now we point to (CUFVWIHH/conf/ABCD)
         size_t dashPos = joinUrlStr.find("/conf/");
         if (dashPos != std::string::npos)
         {
            joinUrlStr = joinUrlStr.substr(dashPos + 6); // now we point to ABCD
            size_t slashPos = joinUrlStr.find("/");
            if (slashPos != std::string::npos)
            {
               joinUrlStr = joinUrlStr.substr(0, slashPos);
            }
            tokenMatch = curlpp::unescape(joinUrlStr).c_str();
         }
      }
   }

   InfoLog(<< "queryConferenceListImpl(conferenceToken=" << conferenceToken << ", tokenMatch=" << tokenMatch << ")");

   ConferenceListResult res;
   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.begin();
   for (; it != mConferences.end(); ++it)
   {
      ConferenceDetailsResult confDetails;
      it->second->getConferenceDetails(confDetails);
      if (!tokenMatch.empty() && confDetails.conferenceToken != tokenMatch)
      {
         DebugLog(<< "queryConferenceListImpl - existing conference token " << confDetails.conferenceToken << " is not a match for " << tokenMatch);
         continue;
      }
      DebugLog(<< "queryConferenceList - adding to return results: " << confDetails.conference << " (" << confDetails.conferenceToken << ")");
      res.conferences.push_back(confDetails);
   }
   if (res.conferences.size() == 0)
   {
      DebugLog(<< "queryConferenceListImpl -- no conferences found");
   }
   fireConferenceListEvent(handler, res);

   return 0;
}

int ConferenceBridgeManagerInterface::destroyEmptyConferences()
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::destroyEmptyConferencesImpl, this));
   return 0;
}

int ConferenceBridgeManagerInterface::destroyEmptyConferencesImpl()
{
   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*> confMapCpy = mConferences;
   auto it = confMapCpy.begin();
   for (; it != confMapCpy.end(); ++it)
   {
      if (it->second->getNumParticipants() == 0 &&
         !it->second->getConferenceSettings().persistent)
      {
         if (it->second->getCreateTime() + std::chrono::seconds(60) < std::chrono::system_clock::now())
         {
            destroyConferenceImpl(it->first);
         }
      }
   }

   return 0;
}

int ConferenceBridgeManagerInterface::queryParticipantList(ConferenceHandle conference, ParticipantListHandler* handler)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::queryParticipantListImpl, this, conference, handler));
   return 0;
}

int ConferenceBridgeManagerInterface::queryParticipantListImpl(ConferenceHandle conference, ParticipantListHandler* handler)
{
   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(conference);
   if (it != mConferences.end())
   {
      it->second->fireParticipantListState(cpc::vector<ParticipantInfo>(), cpc::vector<ParticipantInfo>(), cpc::vector<ParticipantInfo>());
   }
   return 0;
}

ConferenceParticipantHandle ConferenceBridgeManagerInterface::createWebParticipant(ConferenceHandle conference)
{
   ConferenceParticipantHandle h = ConferenceParticipantHandleFactory::getNext();
   WebParticipantIdentity identityInfo;
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::createWebParticipantImpl, this, h, conference, false, identityInfo, false, (CreateWebParticipantHandler*)NULL));
   return h;
}

ConferenceParticipantHandle ConferenceBridgeManagerInterface::createWebParticipant(ConferenceHandle conference, CreateWebParticipantHandler* handler)
{
   ConferenceParticipantHandle h = ConferenceParticipantHandleFactory::getNext();
   WebParticipantIdentity identityInfo;
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::createWebParticipantImpl, this, h, conference, false, identityInfo, false, handler));
   return h;
}

ConferenceParticipantHandle ConferenceBridgeManagerInterface::createWebParticipant(ConferenceHandle conference, const WebParticipantIdentity& identityInfo, bool addToFloor, CreateWebParticipantHandler* handler)
{
   ConferenceParticipantHandle h = ConferenceParticipantHandleFactory::getNext();
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::createWebParticipantImpl, this, h, conference, false, identityInfo, addToFloor, handler));
   return h;
}

int ConferenceBridgeManagerInterface::createWebParticipant(ConferenceParticipantHandle participant, ConferenceHandle conference, bool isContextOwner)
{
   WebParticipantIdentity identityInfo;
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::createWebParticipantImpl, this, participant, conference, isContextOwner, identityInfo, false, (CreateWebParticipantHandler*)NULL));
   return kSuccess;
}

int ConferenceBridgeManagerInterface::createWebParticipant(ConferenceParticipantHandle participant, ConferenceHandle conference, bool isContextOwner, const WebParticipantIdentity& identityInfo, bool addToFloor, CreateWebParticipantHandler* handler)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::createWebParticipantImpl, this, participant, conference, isContextOwner, identityInfo, addToFloor, handler));
   return kSuccess;
}

int ConferenceBridgeManagerInterface::createWebParticipantImpl(ConferenceParticipantHandle webParticipant, ConferenceHandle conference, bool isContextOwner, const WebParticipantIdentity& identityInfo, bool addToFloor, CreateWebParticipantHandler* handler)
{
   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(conference);
   if (it != mConferences.end())
   {
      mMapWebParticipantToConference[webParticipant] = conference;
      it->second->createWebParticipant(webParticipant, isContextOwner, identityInfo, addToFloor);
      WebParticipantCreatedEvent evt;
      evt.success = true;
      evt.conference = conference;
      evt.participant = webParticipant;
      evt.hasFloor = it->second->isFloorOwner(webParticipant);
      fireWebParticipantCreatedEvent(handler, conference, evt);
   }
   else
   {
      WebParticipantCreatedEvent evt;
      evt.success = false;
      evt.conference = conference;
      evt.participant = -1;
      fireWebParticipantCreatedEvent(handler, conference, evt);
   }
   return 0;
}

int ConferenceBridgeManagerInterface::destroyWebParticipant(ConferenceParticipantHandle participant)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::destroyWebParticipantImpl, this, participant));
   return kSuccess;
}

int ConferenceBridgeManagerInterface::destroyWebParticipantImpl(ConferenceParticipantHandle webParticipant)
{
   DebugLog(<< "ConferenceBridgeManagerInterface::destroyWebParticipantImpl(): mMapWebParticipantToConference.size(): " << mMapWebParticipantToConference.size());
   std::map<ConferenceParticipantHandle, ConferenceHandle>::iterator itConf = mMapWebParticipantToConference.find(webParticipant);
   if (itConf != mMapWebParticipantToConference.end())
   {
      std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(itConf->second);
      if (it != mConferences.end())
      {
         it->second->destroyWebParticipant(webParticipant);
      }
   }
   return 0;
}

int ConferenceBridgeManagerInterface::setParticipantPermissions(ConferenceParticipantHandle participant, const ParticipantPermissions& permissionsInfo)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::setParticipantPermissionsImpl, this, participant, permissionsInfo));
   return kSuccess;
}

int ConferenceBridgeManagerInterface::setParticipantPermissionsImpl(ConferenceParticipantHandle participant, const ParticipantPermissions& permissionsInfo)
{
   std::map<ConferenceParticipantHandle, ConferenceHandle>::iterator itConf = mMapWebParticipantToConference.find(participant);
   if (itConf != mMapWebParticipantToConference.end())
   {
      std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(itConf->second);
      if (it != mConferences.end())
      {
         it->second->setParticipantPermissions(participant, permissionsInfo);
      }
   }
   return 0;
}

int ConferenceBridgeManagerInterface::setWebParticipantIdentity(ConferenceParticipantHandle webParticipant, const WebParticipantIdentity& identityInfo)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::setWebParticipantIdentityImpl, this, webParticipant, identityInfo, false));
   return 0;
}

int ConferenceBridgeManagerInterface::setWebParticipantIdentity(ConferenceParticipantHandle webParticipant, const WebParticipantIdentity& identityInfo, bool isContextOwner)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::setWebParticipantIdentityImpl, this, webParticipant, identityInfo, isContextOwner));
   return 0;
}

int ConferenceBridgeManagerInterface::setWebParticipantIdentityImpl(ConferenceParticipantHandle webParticipant, const WebParticipantIdentity& identityInfo, bool isContextOwner)
{
   std::map<ConferenceParticipantHandle, ConferenceHandle>::iterator itConf = mMapWebParticipantToConference.find(webParticipant);
   if (itConf != mMapWebParticipantToConference.end())
   {
      std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(itConf->second);
      if (it != mConferences.end())
      {
         it->second->setWebParticipantIdentity(webParticipant, identityInfo, isContextOwner);
      }
   }
   return 0;
}

int ConferenceBridgeManagerInterface::takeParticipantSnapshot(ConferenceParticipantHandle participant)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::takeParticipantSnapshotImpl, this, participant));
   return 0;
}

int ConferenceBridgeManagerInterface::takeParticipantSnapshotImpl(ConferenceParticipantHandle participant)
{
   std::map<ConferenceParticipantHandle, ConferenceHandle>::iterator itConf = mMapWebParticipantToConference.find(participant);
   if (itConf != mMapWebParticipantToConference.end())
   {
      std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(itConf->second);
      if (it != mConferences.end())
      {
         it->second->takeParticipantSnapshot(participant);
      }
   }
   return 0;
}

int ConferenceBridgeManagerInterface::setParticipantPhoto(ConferenceParticipantHandle participant, const cpc::string& photoFileNameUtf8)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::setParticipantPhotoImpl, this, participant, photoFileNameUtf8));
   return 0;
}

int ConferenceBridgeManagerInterface::setParticipantPhotoImpl(ConferenceParticipantHandle participant, const cpc::string& photoFileNameUtf8)
{
   std::map<ConferenceParticipantHandle, ConferenceHandle>::iterator itConf = mMapWebParticipantToConference.find(participant);
   if (itConf != mMapWebParticipantToConference.end())
   {
      std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(itConf->second);
      if (it != mConferences.end())
      {
         it->second->setParticipantPhoto(participant, photoFileNameUtf8);
      }
   }
   return 0;
}

int ConferenceBridgeManagerInterface::sendPeerConnectionOffer(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpOffer, PeerConnectionAnswerHandler* handler)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::sendPeerConnectionOfferImpl, this, webParticipant, sdpOffer, handler));
   return 0;
}

int ConferenceBridgeManagerInterface::sendPeerConnectionOfferImpl(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpOffer, PeerConnectionAnswerHandler* handler)
{
   std::map<ConferenceParticipantHandle, ConferenceHandle>::iterator itConf = mMapWebParticipantToConference.find(webParticipant);
   if (itConf != mMapWebParticipantToConference.end())
   {
      std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(itConf->second);
      if (it != mConferences.end())
      {
         it->second->sendPeerConnectionOffer(webParticipant, sdpOffer, handler);
      }
   }
   return 0;
}

int ConferenceBridgeManagerInterface::sendPeerConnectionAnswer(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpAnswer)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::sendPeerConnectionAnswerImpl, this, webParticipant, sdpAnswer));
   return 0;
}

int ConferenceBridgeManagerInterface::sendPeerConnectionAnswerImpl(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpAnswer)
{
   std::map<ConferenceParticipantHandle, ConferenceHandle>::iterator itConf = mMapWebParticipantToConference.find(webParticipant);
   if (itConf != mMapWebParticipantToConference.end())
   {
      std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(itConf->second);
      if (it != mConferences.end())
      {
         it->second->sendPeerConnectionAnswer(webParticipant, sdpAnswer);
      }
   }
   return 0;
}

int ConferenceBridgeManagerInterface::generateLocalOffer(ConferenceParticipantHandle webParticipant, PeerConnectionAnswerHandler* handler)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::generateLocalOfferImpl, this, webParticipant, handler));
   return 0;
}

int ConferenceBridgeManagerInterface::generateLocalOfferImpl(ConferenceParticipantHandle webParticipant, PeerConnectionAnswerHandler* handler)
{
   std::map<ConferenceParticipantHandle, ConferenceHandle>::iterator itConf = mMapWebParticipantToConference.find(webParticipant);
   if (itConf != mMapWebParticipantToConference.end())
   {
      std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(itConf->second);
      if (it != mConferences.end())
      {
         it->second->generateLocalOffer(webParticipant, handler);
      }
   }
   return 0;
}

int ConferenceBridgeManagerInterface::addAssociatedConference(ConferenceHandle conference, ConferenceHandle associatedConference)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::addAssociatedConferenceImpl, this, conference, associatedConference));
   return 0;
}

int ConferenceBridgeManagerInterface::addAssociatedConferenceImpl(ConferenceHandle conference, ConferenceHandle associatedConference)
{
   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(conference);
   if (it != mConferences.end())
   {
      it->second->addAssociatedConference(associatedConference);
      it = mConferences.find(associatedConference);
      if (it != mConferences.end())
      {
         it->second->setParentConference(conference);
      }
   }
   return 0;
}

int ConferenceBridgeManagerInterface::requestFloor(ConferenceParticipantHandle requestor)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::requestFloorImpl, this, requestor));
   return 0;
}

int ConferenceBridgeManagerInterface::requestFloorImpl(ConferenceParticipantHandle requestor)
{
   return 0;
}

int ConferenceBridgeManagerInterface::addToFloor(ConferenceParticipantHandle participant)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::addToFloorImpl, this, participant));
   return 0;
}

int ConferenceBridgeManagerInterface::addToFloorImpl(ConferenceParticipantHandle participant)
{
   std::map<ConferenceParticipantHandle, ConferenceHandle>::iterator itConf = mMapWebParticipantToConference.find(participant);
   if (itConf != mMapWebParticipantToConference.end())
   {
      std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(itConf->second);
      if (it != mConferences.end())
      {
         it->second->addToFloor(participant);
      }
   }
   return 0;
}

int ConferenceBridgeManagerInterface::removeFromFloor(ConferenceParticipantHandle participant)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::removeFromFloorImpl, this, participant));
   return 0;
}

int ConferenceBridgeManagerInterface::removeFromFloorImpl(ConferenceParticipantHandle participant)
{
   std::map<ConferenceParticipantHandle, ConferenceHandle>::iterator itConf = mMapWebParticipantToConference.find(participant);
   if (itConf != mMapWebParticipantToConference.end())
   {
      std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(itConf->second);
      if (it != mConferences.end())
      {
         it->second->removeFromFloor(participant);
      }
   }
   return 0;
}

int ConferenceBridgeManagerInterface::setVideoLayout(ConferenceHandle conference, VideoLayout layout)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::setVideoLayoutImpl, this, conference, layout));
   return 0;
}

int ConferenceBridgeManagerInterface::setVideoLayoutImpl(ConferenceHandle conference, VideoLayout layout)
{
   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(conference);
   if (it != mConferences.end())
   {
      it->second->setVideoLayout(layout);
   }
   return 0;
}

int ConferenceBridgeManagerInterface::setStreamingEnabled(ConferenceHandle conference, bool enabled)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::setStreamingEnabledImpl, this, conference, enabled));
   return 0;
}

int ConferenceBridgeManagerInterface::setStreamingEnabledImpl(ConferenceHandle conference, bool enabled)
{
   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(conference);
   if (it != mConferences.end())
   {
      it->second->setStreamingEnabled(enabled);
   }
   return 0;
}

int ConferenceBridgeManagerInterface::setRecordingEnabled(ConferenceHandle conference, bool enabled)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::setRecordingEnabledImpl, this, conference, enabled));
   return 0;
}

int ConferenceBridgeManagerInterface::setRecordingEnabledImpl(ConferenceHandle conference, bool enabled)
{
   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(conference);
   if (it != mConferences.end())
   {
      it->second->setRecordingEnabled(enabled);
   }
   return 0;
}

int ConferenceBridgeManagerInterface::setTranscriptionEnabled(ConferenceHandle conference, bool enabled)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::setTranscriptionEnabledImpl, this, conference, enabled));
   return 0;
}

int ConferenceBridgeManagerInterface::setTranscriptionEnabledImpl(ConferenceHandle conference, bool enabled)
{
   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::iterator it = mConferences.find(conference);
   if (it != mConferences.end())
   {
      it->second->setTranscriptionEnabled(enabled);
   }
   return 0;
}

bool ConferenceBridgeManagerInterface::isValidJoinUrl(const cpc::string& joinUrl)
{
   std::string joinUrlNorm(joinUrl.c_str(), joinUrl.size());
   std::string::size_type qpos = joinUrlNorm.rfind('?');
   if (qpos != std::string::npos)
   {
      joinUrlNorm = joinUrlNorm.substr(0, qpos);
   }
   if (joinUrlNorm.at(joinUrlNorm.size() - 1) == '/')
   {
      joinUrlNorm = joinUrlNorm.substr(0, joinUrlNorm.size() - 1);
   }
   size_t ssPos = joinUrlNorm.find("/screenshare/");
   if (ssPos != std::string::npos)
   {
      size_t dashPos = joinUrlNorm.find("/conf/", ssPos);
      if (dashPos != std::string::npos)
      {
         joinUrlNorm = joinUrlNorm.substr(0, dashPos);
      }
   }
   websocketpp::uri joinUrlIn(joinUrlNorm);
   resip::Lock l(mThreadSafeVarsMtx);

   bool isMatch = false;
   for (const std::string& validUrl : mThreadSafeValidJoinUrls)
   {
      websocketpp::uri joinUrlTest(validUrl);
      InfoLog(<< "isValidJoinUrl: checking input " << joinUrlIn.get_resource() << " against " << joinUrlTest.get_resource());
      isMatch |= (joinUrlIn.get_resource() == joinUrlTest.get_resource());
   }
   return isMatch;
}

int ConferenceBridgeManagerInterface::updateJoinUrlBase(const cpc::string& joinUrl)
{
   post(resip::resip_safe_bind(&ConferenceBridgeManagerInterface::updateJoinUrlBaseImpl, this, joinUrl));
   return 0;
}

int ConferenceBridgeManagerInterface::updateJoinUrlBaseImpl(const cpc::string& joinUrl)
{
   mBridgeConfig.httpJoinUrlBase = joinUrl;
   return 0;
}

int ConferenceBridgeManagerInterface::addResourceHandlerMapping(const std::string& userContext)
{
   if (mConfRegistrar != NULL)
      return kSuccess;

   InfoLog(<< "addResourceHandlerMapping: " << userContext);

   std::stringstream usersRegex; // ^\/confbridge\/jgeras[\/]?$
   usersRegex << "^/screenshare/";
   usersRegex << userContext;
   usersRegex << "[/]?$";
   usersRegex.flush();

   std::stringstream statusRegex;
   statusRegex << "^/screenshare/";
   statusRegex << userContext;
   statusRegex << "/status$";
   statusRegex.flush();

   CPCAPI2::JsonApi::JsonApiServerSendTransportInterface* jsonApiServerSendIf = dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInterface*>(CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(mPhone));
   CPCAPI2::JsonApi::JsonApiServerInterface* jsonApiServerIf = jsonApiServerSendIf->getJsonApiServer();
   std::shared_ptr<CPCAPI2::JsonApi::JsonApiServerHTTP> jsonApiServerHttp = jsonApiServerIf->getJsonApiHttpTransport();
   if (jsonApiServerHttp->useHttps())
   {
      jsonApiServerHttp->addHttpsResourceHandler("POST", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>& request) {
         try {
            handleUsersRequest<SimpleWeb::HTTPS>(response, request);
         }
         catch (const std::exception &e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n"
               << e.what();
         }
      });
      jsonApiServerHttp->addHttpsResourceHandler("OPTIONS", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>& request) {
         try {
            *response << "HTTP/1.1 200 OK\r\n"
               << "Access-Control-Allow-Origin: *\r\n"
               << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
         }
         catch (const std::exception &e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n"
               << e.what();
         }
      });
      jsonApiServerHttp->addHttpsResourceHandler("GET", statusRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTPS>::Request>& request) {
         try {
            handleStatusRequest<SimpleWeb::HTTPS>(response, request);
         }
         catch (const std::exception &e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n"
               << e.what();
         }
      });
   }
   else
   {
      jsonApiServerHttp->addHttpResourceHandler("POST", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Request>& request) {
         try {
            InfoLog(<< "Handling conference join request...");
            handleUsersRequest<SimpleWeb::HTTP>(response, request);
         }
         catch (const std::exception &e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n"
               << e.what();
         }
      });
      jsonApiServerHttp->addHttpResourceHandler("OPTIONS", usersRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Request>& request) {
         try {
            *response << "HTTP/1.1 200 OK\r\n"
               << "Access-Control-Allow-Origin: *\r\n"
               << "Access-Control-Allow-Headers: Authorization\r\n\r\n";
         }
         catch (const std::exception &e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n"
               << e.what();
         }
      });
      jsonApiServerHttp->addHttpResourceHandler("GET", statusRegex.str(), [&](const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Response>& response, const std::shared_ptr<SimpleWeb::Server<SimpleWeb::HTTP>::Request>& request) {
         try {
            handleStatusRequest<SimpleWeb::HTTP>(response, request);
         }
         catch (const std::exception &e) {
            *response << "HTTP/1.1 400 Bad Request\r\nContent-Length: " << strlen(e.what()) << "\r\n\r\n"
               << e.what();
         }
      });
   }

   return kSuccess;
}

int ConferenceBridgeManagerInterface::removeResourceHandlerMapping(const std::string& userContext)
{
   if (mConfRegistrar != NULL)
      return kSuccess;

   InfoLog(<< "removeResourceHandlerMapping: " << userContext);

   std::stringstream usersRegex; // ^\/confbridge\/jgeras[\/]?$
   usersRegex << "^/screenshare/";
   usersRegex << userContext;
   usersRegex << "[/]?$";
   usersRegex.flush();

   std::stringstream statusRegex;
   statusRegex << "^/screenshare/";
   statusRegex << userContext;
   statusRegex << "/status$";
   statusRegex.flush();

   CPCAPI2::JsonApi::JsonApiServerSendTransportInterface* jsonApiServerSendIf = dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInterface*>(CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(mPhone));
   CPCAPI2::JsonApi::JsonApiServerInterface* jsonApiServerIf = jsonApiServerSendIf->getJsonApiServer();
   std::shared_ptr<CPCAPI2::JsonApi::JsonApiServerHTTP> jsonApiServerHttp = jsonApiServerIf->getJsonApiHttpTransport();
   if (jsonApiServerHttp->useHttps())
   {
      jsonApiServerHttp->removeHttpsResourceHandler("POST", usersRegex.str());
      jsonApiServerHttp->removeHttpsResourceHandler("OPTIONS", usersRegex.str());
      jsonApiServerHttp->removeHttpsResourceHandler("GET", statusRegex.str());
   }
   else
   {
      jsonApiServerHttp->removeHttpResourceHandler("POST", usersRegex.str());
      jsonApiServerHttp->removeHttpResourceHandler("OPTIONS", usersRegex.str());
      jsonApiServerHttp->removeHttpResourceHandler("GET", statusRegex.str());
   }

   return kSuccess;
}


ConferenceBridgeManagerImpl* ConferenceBridgeManagerInterface::getImpl(ConferenceHandle conference) const
{
   std::map<ConferenceHandle, ConferenceBridgeManagerImpl*>::const_iterator it = mConferences.find(conference);
   if (it != mConferences.end())
   {
      return it->second;
   }
   return NULL;
}

void ConferenceBridgeManagerInterface::updateJoinUrlTs()
{
   resip::Data confJoinUrl;
   resip::Data confJoinUrlOld;
   {
      resip::DataStream ds(confJoinUrl);
      resip::Data configuredJoinUrl = getConferenceBridgeConfig().httpJoinUrlBase.c_str();
      if (configuredJoinUrl.postfix("/"))
      {
         configuredJoinUrl = configuredJoinUrl.substr(0, configuredJoinUrl.size() - 1);
      }
      ds << configuredJoinUrl;
      ds << "/screenshare/";
      ds << getConferenceUrlRandomness();

      resip::DataStream dsOld(confJoinUrlOld);
      dsOld << configuredJoinUrl;
      dsOld << "/screenshare/";
      dsOld << getConferenceBridgeConfig().serverUid;
      dsOld << "/";
      dsOld << getConferenceUrlRandomness();
   }

   resip::Lock l(mThreadSafeVarsMtx);
   mThreadSafeValidJoinUrls.insert(confJoinUrl.c_str());
   mThreadSafeValidJoinUrls.insert(confJoinUrlOld.c_str());
}

void ConferenceBridgeManagerInterface::updateConferenceSummaryTs()
{
   cpc::string confSummary = getConferenceSummaryImpl();
   resip::Lock l(mThreadSafeVarsMtx);
   mThreadSafeConferenceSummary = confSummary;
}

void ConferenceBridgeManagerInterface::addCameraUsage()
{
   mCustomVideoSourceUsage->AddRef();
}

void ConferenceBridgeManagerInterface::removeCameraUsage()
{
   mCustomVideoSourceUsage->Release();
}

ConferenceBridgeManagerInterface::RefCountedCameraUsage::RefCountedCameraUsage(CPCAPI2::Phone* phone)
   : mPhone(phone),
   mSharedInstanceRefCnt(0)
{
}

ConferenceBridgeManagerInterface::RefCountedCameraUsage::~RefCountedCameraUsage()
{
}

void ConferenceBridgeManagerInterface::RefCountedCameraUsage::AddRef()
{
   resip::Lock l(mSafeDeleteMutex);
   mSharedInstanceRefCnt++;

   CPCAPI2::Media::MediaManager* mm = CPCAPI2::Media::MediaManager::getInterface(mPhone);
   CPCAPI2::Media::Video* video = CPCAPI2::Media::Video::getInterface(mm);
   video->startCapture();
}

void ConferenceBridgeManagerInterface::RefCountedCameraUsage::Release()
{
   bool shouldDestroy = false;
   {
      resip::Lock l(mSafeDeleteMutex);
      mSharedInstanceRefCnt--;
      shouldDestroy = (mSharedInstanceRefCnt == 0);
   }
   if (shouldDestroy)
   {
      CPCAPI2::Media::MediaManager* mm = CPCAPI2::Media::MediaManager::getInterface(mPhone);
      CPCAPI2::Media::Video* video = CPCAPI2::Media::Video::getInterface(mm);
      video->stopCapture();
   }

}

resip::Data ConferenceIdGenerator::getNew()
{
   const int max_num_iterations = 40;
   for (int i=0; i < max_num_iterations; i++)
   {
      resip::Data retVal;
      {
         resip::DataStream ds(retVal);
         for (int j = 0; j < RANDOM_PATH_COMPONENT_LENGTH; j++)
         {
            char c1 = (char)((std::abs(resip::Random::getCryptoRandom()) % 26) + 65);
            ds << c1;
         }
      }
      resip::Lock lck(ConferenceIdGenerator::sAllocatedIdsMtx);
      if (ConferenceIdGenerator::sAllocatedIds.find(retVal) == ConferenceIdGenerator::sAllocatedIds.end())
      {
         ConferenceIdGenerator::sAllocatedIds.insert(retVal);
         return retVal;
      }
   }
   return resip::Data::Empty;
}

void ConferenceIdGenerator::free(const resip::Data& id)
{
   resip::Lock lck(ConferenceIdGenerator::sAllocatedIdsMtx);
   ConferenceIdGenerator::sAllocatedIds.erase(id);
}

cpc::string get_debug_string(const CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode& mode)
{
   switch (mode)
   {
      case ConferenceMediaEncryptionMode_Unencrypted: return "unencrypted";
      case ConferenceMediaEncryptionMode_SRTP_SDES: return "srtp_sdes";
      case ConferenceMediaEncryptionMode_SRTP_DTLS: return "srtp_dtls";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const CPCAPI2::ConferenceBridge::ConferenceBitrateConfig& config)
{
   std::stringstream ss;
   ss << "videoMaxBitrateKbps: " << config.videoMaxBitrateKbps << " videoTargetBitrateKbps: " << config.videoTargetBitrateKbps << " videoStartBitrateKbps: " << config.videoStartBitrateKbps << " videoMinBitrateKbps: " << config.videoMinBitrateKbps;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::ConferenceBridge::ConferenceNatTraversalServerInfo::NatTraversalServerType& serverType)
{
   switch (serverType)
   {
      case ConferenceNatTraversalServerInfo::NatTraversalServerType_StunAndTurn: return "stun-trun";
      case ConferenceNatTraversalServerInfo::NatTraversalServerType_StunOnly: return "stun-only";
      case ConferenceNatTraversalServerInfo::NatTraversalServerType_TurnOnly: return "turn-only";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const CPCAPI2::ConferenceBridge::ConferenceNatTraversalServerInfo& info)
{
   std::stringstream ss;
   ss << "natTraversalServerHostname: " << info.natTraversalServerHostname << " natTraversalServerUsername: " << info.natTraversalServerUsername << " natTraversalServerPort: " << info.natTraversalServerPort
      << " natTraversalServerType: " << get_debug_string(info.natTraversalServerType) << " natTraversalServerType2: " << get_debug_string(info.natTraversalServerType2) << " serverPublicIpAddress: " << info.serverPublicIpAddress;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::ConferenceBridge::ConferenceInfo& info)
{
   std::stringstream ss;
   ss << "label: " << info.label << " owner: " << info.owner << " conferenceToken: " << info.conferenceToken << " conference: " << info.conference << " description: " << info.description << " creatorDisplayName: " << info.creatorDisplayName
      << " streamId: " << info.streamId << " transcriptionEnabled: " << info.transcriptionEnabled << " numParticipants: " << info.numParticipants << " natTraversalServerInfo: {" << get_debug_string(info.natTraversalServerInfo)
      << "} bitrateConfig: {" << get_debug_string(info.bitrateConfig) << "} mediaEncryptionMode: " << info.mediaEncryptionMode;
   ss << " tag-count: " << info.tags.size() << " { ";
   for (cpc::vector<int>::const_iterator i = info.tags.begin(); i != info.tags.end(); i++)
   {
      ss  << (*i) << " ";
   }
   ss << "}";
   /*
   ss << " associated-conferences-count: " << info.associatedConferences.size() << " { ";
   for (cpc::vector<ConferenceInfo>::const_iterator j = info.associatedConferences.begin(); j != info.associatedConferences.end(); j++)
   {
      ss  << (*j) << " ";
   }
   ss << "}";
   */
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::ConferenceBridge::ConferenceDetailsResult& result)
{
   std::stringstream ss;
   ss << "conference: " << result.conference << " conferenceToken: " << result.conferenceToken << " conferenceJoinUrl: " << result.conferenceJoinUrl << " conferenceInfo: {" << get_debug_string(result.conferenceInfo) << "}";
   return ss.str().c_str();
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode& mode)
{
   os << CPCAPI2::ConferenceBridge::get_debug_string(mode);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceBridge::ConferenceBitrateConfig& config)
{
   os << CPCAPI2::ConferenceBridge::get_debug_string(config);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceBridge::ConferenceNatTraversalServerInfo::NatTraversalServerType& serverType)
{
   os << CPCAPI2::ConferenceBridge::get_debug_string(serverType);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceBridge::ConferenceNatTraversalServerInfo& info)
{
   os << CPCAPI2::ConferenceBridge::get_debug_string(info);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceBridge::ConferenceInfo& info)
{
   os << CPCAPI2::ConferenceBridge::get_debug_string(info);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceBridge::ConferenceDetailsResult& result)
{
   os << CPCAPI2::ConferenceBridge::get_debug_string(result);
   return os;
}

}

}

#endif
