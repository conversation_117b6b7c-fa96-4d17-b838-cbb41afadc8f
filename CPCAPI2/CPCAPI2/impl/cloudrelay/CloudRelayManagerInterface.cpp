#include "brand_branded.h"

#if (CPCAPI2_BRAND_CLOUD_RELAY_MODULE == 1)
#include "cpcapi2utils.h"
#include "cloudrelay/CloudRelayHandler.h"
#include "CloudRelayManagerInterface.h"
#include "CloudRelayManagerImpl.h"
#include "jsonapi/JsonApiServerSendTransportInterface.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "jsonapi/JsonApiServer_HTTP.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"

#include <rutil/ParseBuffer.hxx>
#include <rutil/Random.hxx>

// rapidjson
#include <writer.h>
#include <prettywriter.h>
#include <stringbuffer.h>
#include <document.h>

#include <time.h>

using namespace resip;

using resip::ReadCallbackBase;

#define RANDOM_PATH_COMPONENT_LENGTH 8
#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER

namespace CPCAPI2
{
namespace CloudRelay
{
CloudRelayHandle CloudRelayHandleFactory::sNextHandle = 1000; // handle does not change (single instance model)

CloudRelayManagerInterface::CloudRelayManagerInterface(Phone* phone)
   : mShutdown(false),
     mPhone(dynamic_cast<PhoneInterface*>(phone))
{
}

CloudRelayManagerInterface::~CloudRelayManagerInterface()
{
   mShutdown = true;
   interruptProcess();
}

void CloudRelayManagerInterface::Release()
{
   delete this;
}

int CloudRelayManagerInterface::process(unsigned int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return kCloudRelayModuleDisabled;
   }
   ReadCallbackBase* fp = mCallbackFifo.getNext(timeout);
   while(fp)
   {
      (*fp)();
      delete fp;
      if (mShutdown)
      {
         return kCloudRelayModuleDisabled;
      }
      fp = mCallbackFifo.getNext(kBlockingModeNonBlocking);
   }
   return kSuccess;
}

CloudRelayManagerInternal* CloudRelayManagerInternal::getInternalInterface(Phone* cpcPhone)
{
   return static_cast<CloudRelayManagerInternal*>(CloudRelayManager::getInterface(cpcPhone));
}

void CloudRelayManagerInterface::setCallbackHook(void(*cbHook)(void*), void* context)
{
   mCbHook = std::bind(cbHook, context);
}

#ifdef CPCAPI2_AUTO_TEST
AutoTestReadCallback* CloudRelayManagerInterface::process_test(int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return NULL;
   }
   resip::ReadCallbackBase* rcb = mCallbackFifo.getNext(timeout);
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
   {
      return fpCmd;
   }
   if (rcb != NULL)
   {
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   }
   return NULL;
}
#endif

void CloudRelayManagerInterface::post(ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void CloudRelayManagerInterface::interruptProcess()
{
   mPhone->getSdkModuleThread().getAsyncProcessHandler()->handleProcessNotification();
}
   
void CloudRelayManagerInterface::postCallback(ReadCallbackBase* command)
{
   mCallbackFifo.add(command);
   if (mCbHook) { mCbHook(); }
}

void CloudRelayManagerInterface::addSdkObserver(CloudRelayHandler* observer)
{
   mSdkObservers.insert(observer);
}

void CloudRelayManagerInterface::removeSdkObserver(CloudRelayHandler* observer)
{
   mSdkObservers.erase(observer);
}

CloudRelayHandle CloudRelayManagerInterface::getRelay()
{
   CloudRelayHandle h = CloudRelayHandleFactory::getNext();
   post(resip::resip_bind(&CloudRelayManagerInterface::getRelayImpl, this, h, (CloudRelayHandler*)NULL));
   return h;
}

int CloudRelayManagerInterface::getRelayImpl(CloudRelayHandle h, CloudRelayHandler* handler)
{
   auto it = mRelays.begin();
   if (it == mRelays.end())
   {
      CloudRelayManagerImpl* impl = new CloudRelayManagerImpl(h, mPhone, this);
      impl->setSdkObservers(&mSdkObservers);
      mRelays[h] = impl;
      if (handler != NULL)
      {
         setHandlerImpl(h, handler);
      }
   }
   return 0;
}

int CloudRelayManagerInterface::setHandler(CloudRelayHandle conference, CloudRelayHandler* handler)
{
   ReadCallbackBase* rcb = resip::resip_bind(&CloudRelayManagerInterface::setHandlerImpl, this, conference, handler);
   post(rcb);
   return kSuccess;
}

int CloudRelayManagerInterface::setHandlerImpl(CloudRelayHandle conference, CloudRelayHandler* handler)
{
   std::map<CloudRelayHandle, CloudRelayManagerImpl*>::iterator it = mRelays.find(conference);
   if (it != mRelays.end())
   {
      it->second->setHandler(handler);
      return kSuccess;
   }
   return kError;
}

int CloudRelayManagerInterface::setHandler(CloudRelayHandler* handler)
{
   ReadCallbackBase* rcb = resip::resip_bind(&CloudRelayManagerInterface::addSdkObserver, this, handler);
   post(rcb);
   return kSuccess;
}

int CloudRelayManagerInterface::start()
{
   post(resip::resip_bind(&CloudRelayManagerInterface::startImpl, this));
   return kSuccess;
}

int CloudRelayManagerInterface::startImpl()
{
   return kSuccess;
}



int CloudRelayManagerInterface::shutdown()
{
   post(resip::resip_bind(&CloudRelayManagerInterface::shutdownImpl, this));
   return kSuccess;
}

int CloudRelayManagerInterface::shutdownImpl()
{
   InfoLog(<< "CloudRelayManager shutdown()");

   return kSuccess;
}


int CloudRelayManagerInterface::broadcast(CloudRelayHandle relay, const cpc::string& msg)
{
   post(resip::resip_bind(&CloudRelayManagerInterface::broadcastImpl, this, (CPCAPI2::JsonApi::JsonApiUserHandle)(-1), relay, msg));
   return kSuccess;
}

int CloudRelayManagerInterface::broadcast(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, CloudRelayHandle relay, const cpc::string& msg)
{
   post(resip::resip_bind(&CloudRelayManagerInterface::broadcastImpl, this, jsonApiUser, relay, msg));
   return kSuccess;
}

int CloudRelayManagerInterface::broadcastImpl(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, CloudRelayHandle relay, const cpc::string& msg)
{
   std::map<CloudRelayHandle, CloudRelayManagerImpl*>::iterator it = mRelays.find(relay);
   if (it != mRelays.end())
   {
      it->second->broadcast(jsonApiUser, msg);
      return kSuccess;
   }
   return kError;
}

int CloudRelayManagerInterface::sendTo(CloudRelayHandle relay, CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const cpc::string& msg)
{
   post(resip::resip_bind(&CloudRelayManagerInterface::sendToImpl, this, relay, jsonApiUser, msg));
   return kSuccess;
}

int CloudRelayManagerInterface::sendToImpl(CloudRelayHandle relay, CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const cpc::string& msg)
{
   std::map<CloudRelayHandle, CloudRelayManagerImpl*>::iterator it = mRelays.find(relay);
   if (it != mRelays.end())
   {
      it->second->sendTo(jsonApiUser, msg);
      return kSuccess;
   }
   return kError;
}

CloudRelayManagerImpl* CloudRelayManagerInterface::getImpl(CloudRelayHandle conference) const
{
   std::map<CloudRelayHandle, CloudRelayManagerImpl*>::const_iterator it = mRelays.find(conference);
   if (it != mRelays.end())
   {
      return it->second;
   }
   return NULL;
}

}
}

#endif
