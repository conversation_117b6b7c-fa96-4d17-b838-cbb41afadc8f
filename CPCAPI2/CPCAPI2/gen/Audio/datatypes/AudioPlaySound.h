#include "jsonrpc/StringSerialization.h"
#include <nlohmann/json.hpp>
#include "AudioAudioDeviceRole.h"

#ifndef JSONRPC_CPCAPI2_AUDIO_AUDIOPLAYSOUND_H
#define JSONRPC_CPCAPI2_AUDIO_AUDIOPLAYSOUND_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Audio {
  class AudioPlaySound
  {
  public:
    int32_t phoneHandle;
    AudioAudioDeviceRole role;
    cpc::string resourceUri;
    bool repeat;

    AudioPlaySound() = default;

    AudioPlaySound(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const AudioPlaySound& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["role"] = o.role;
     j["resourceUri"] = o.resourceUri;
     j["repeat"] = o.repeat;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, AudioPlaySound& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.role = j.at("role");
     o.resourceUri = j.at("resourceUri");
     o.repeat = j.at("repeat");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Audio
#endif // JSONRPC_CPCAPI2_AUDIO_AUDIOPLAYSOUND_H
