#pragma once

#if !defined(CPCAPI2_JSON_API_CLIENT_INTERFACE_H)
#define CPCAPI2_JSON_API_CLIENT_INTERFACE_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiTypes.h"
#include "jsonapi/JsonApiClient.h"
#include "jsonapi/JsonApiClientModule.h"
#include "jsonapi/JsonApiClient_WebSocketHandler.h"
#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"
#include "../util/AutoTestProcessor.h"
#include "phone/Cpcapi2EventSource.h"

#include <rutil/Fifo.hxx>

#include <map>

namespace CPCAPI2
{

class PhoneInterface;

namespace JsonApi
{

class JsonApiClientWebSocket;
class JsonApiClientSyncHandler {};

class JsonApiClientInterface : public EventSource<int, JsonApi<PERSON><PERSON><PERSON><PERSON><PERSON>, JsonApiClientSyncHandler>,
                               public JsonApiClient,
                               public PhoneModule,
                               public JsonApiClientModule,
                               public JsonApiClientWebSocketHandler,
                               public resip::ReactorBinded
{

public:

   JsonApiClientInterface(Phone* phone);
   virtual ~JsonApiClientInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // ReactorBinded
   virtual void release() OVERRIDE;

   // JsonApiClientModule
   virtual void setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport) OVERRIDE;
   virtual int processIncoming(const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   virtual int process(unsigned int timeout) OVERRIDE {
      return EventSource<int, JsonApiClientHandler, JsonApiClientSyncHandler>::process(timeout);
   }
   virtual void interruptProcess() OVERRIDE {
      EventSource<int, JsonApiClientHandler, JsonApiClientSyncHandler>::interruptProcess();
   }
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE {
      EventSource<int, JsonApiClientHandler, JsonApiClientSyncHandler>::setCallbackHook(cbHook, context);
   }

   // JsonApiClient
   virtual int setHandler(JsonApiClientHandler* handler) OVERRIDE {
      return setAppHandler(0, handler);
   }
   virtual JsonApiConnectionHandle connect(const cpc::string& serverUri) OVERRIDE;
   virtual int shutdown() OVERRIDE;
   virtual JsonApiLoginHandle login(const cpc::string& authToken, const cpc::string& realm = "") OVERRIDE;
   virtual int logout() OVERRIDE;
   virtual int configureDefaultSettings(const JsonApiClientSettings& settings) OVERRIDE;
   virtual int enable() OVERRIDE;
   virtual int disable() OVERRIDE;
   virtual int generateHandle() OVERRIDE;
   virtual int dropIncomingJsonMessages(bool enable);
   CPCAPI2::JsonApi::JsonApiTransport* getTransport();

   // JsonApiClientWebsocketHandler
   virtual int onStateChanged(JsonApiClientWebSocketHandler::State newState) OVERRIDE;
   virtual int onLogin() OVERRIDE { return kSuccess; }
   virtual int onLogout() { return kSuccess; }
   virtual int onLoginResponse(const std::shared_ptr<rapidjson::Document>& response) OVERRIDE { return kSuccess; }
   virtual int onLogoutResponse(const std::shared_ptr<rapidjson::Document>& response) { return kSuccess; }

   void setCallOnDestructFn(void (*func)(void*), void* context);

private:

   void processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request);
   int connectImpl(const cpc::string& serverUri);
   int shutdownImpl();
   int loginImpl(const cpc::string& authToken, const cpc::string& realm);
   int logoutImpl();

   int configureDefaultSettingsImpl(const JsonApiClientSettings& settings);
   int enableImpl();
   int disableImpl();
   int dropIncomingJsonMessagesImpl(bool enable);

   int handleLoginResult(const rapidjson::Value& functionObjectVal);
   int handleLogoutResult(const rapidjson::Value& functionObjectVal);

   int onStateChangedImpl(JsonApiClientWebSocketHandler::State newState);

private:

   PhoneInterface* mPhone;
   std::shared_ptr<JsonApiClientWebSocket> mImpl;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   std::vector<std::function<void(void)>> mCallOnDestructFn;
   FunctionMap mFunctionMap;
   JsonApiConnectionHandle mConnHandle;
   JsonApiLoginHandle mLoginHandle;
   JsonApiClientSettings mSettings;
   JsonApiUserHandle mJsonApiUserHandle;
   int16_t mServerGeneratedHandle;
   int16_t mClientGeneratedHandle;

};

}

}

#endif // CPCAPI2_JSON_API_CLIENT_INTERFACE_H
