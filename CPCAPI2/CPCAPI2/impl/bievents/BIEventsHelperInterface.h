#pragma once
#ifndef __CPCAPI2_BIEVENTS_HELPER_INTERFACE_H__
#define __CPCAPI2_BIEVENTS_HELPER_INTERFACE_H__

#include <bievents/BIEventsManagerInterface.h>
#include <bievents/BIEventsHelper.h>

namespace CPCAPI2
{
   namespace BIEvents
   {
      class BIEventsHelperInterface : 
         public BIEventsHelper
      {
      public:
         BIEventsHelperInterface( BIEventsManagerInterface *pManager );

         int createEventGroupID( void ) OVERRIDE;

         int postUIEvent(
            const BIEventsHandle& hEvents,
            const cpc::string& eventSource,
            const cpc::string& parentID,
            const cpc::string& ID,
            const char *uiVerb,
            const cpc::string& text,
            const BIEventBody& extraInfo,
            EventID& outEventID,
            const int eventGroup = 0,
            const int eventContext = 0,
            const cpc::string& eventSummary = "" ) OVERRIDE;

         int postModelEvent(
            const BIEventsHandle& hEvents,
            const cpc::string& eventSource,
            const char *modelID,
            const char *modelVerb,
            const int& hResource,
            const BIEventBody& extraInfo,
            EventID& outEventID,
            const int eventGroup = 0,
            const int eventContext = 0,
            const cpc::string& eventSummary = "" ) OVERRIDE;

         int postSystemEvent(
            const BIEventsHandle& hEvents,
            const cpc::string& eventSource,
            const char *systemVerb,
            const BIEventBody& extraInfo,
            EventID& outEventID,
            const int eventGroup = 0,
            const int eventContext = 0,
            const cpc::string& eventSummary = "" ) OVERRIDE;

         int postCommerceEvent(
            const BIEventsHandle& hEvents,
            const cpc::string& eventSource,
            const char *commerceVerb,
            const int& hResource,
            const BIEventBody& extraInfo,
            EventID& outEventID,
            const int eventGroup = 0,
            const int eventContext = 0,
            const cpc::string& eventSummary = "" ) OVERRIDE;

         virtual int postSDKEvent(
            const BIEventsHandle& hEvents,
            const cpc::string& eventSource,
            const cpc::vector< cpc::string >& labels,
            const BIEventBody& extraInfo,
            EventID& outEventID,
            const int eventGroup = 0,
            const int eventContext = 0,
            const cpc::string& eventSummary = "" ) OVERRIDE;

         int postDebugEvent(
            const BIEventsHandle& hEvents,
            const cpc::string& eventSource,
            const char *debugVerb,
            int threadID,
            const cpc::string& message,
            const cpc::string& stackTrace,
            const BIEventBody& extraInfo,
            EventID& outEventID,
            const int eventGroup = 0,
            const int eventContext = 0,
            const cpc::string& eventSummary = "" ) OVERRIDE;

      private:
         BIEventsManagerInterface *m_Interface; // not owned
         int m_CurrentGroupID;
      };
   }
}

#endif // __CPCAPI2_BIEVENTS_HELPER_INTERFACE_H__