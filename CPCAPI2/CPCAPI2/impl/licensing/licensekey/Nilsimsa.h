//
//  nilsimsa.h
//  nilsimsa
//
//  Created by <PERSON><PERSON> on 10/28/2013.
//  Copyright (c) 2013 CounterPath. All rights reserved.
//

#ifndef __CPCAPI2_NILSIMSA_H__
#define __CPCAPI2_NILSIMSA_H__

namespace CPCAPI2
{
namespace Licensing
{
extern "C" {

typedef struct {
   unsigned short acc[256];
   unsigned long count;
   char signature[65];
} ns_obj;

//ns_obj ns_create_object();
void ns_add_data(ns_obj*, const char*);
void ns_create_signature(ns_obj*);
//unsigned char ns_compare(const char*, const char*);

}

}
}

#endif // __CPCAPI2_NILSIMSA_H__
