#include "../datatypes/SipAccountSettings.h"
#include "../datatypes/SipAccountNetworkTransport.h"

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
class SipAccount
{
public:
    SipAccount(ITransport& transport)
    {
        this.transport = transport;
    }

    int64_t create(int64_t phoneHandle);

	void destroy(int64_t phoneHandle, int64_t sipAccountHandle);

	void configureDefaultAccountSettings(int64_t phoneHandle, int64_t sipAccountHandle, SipAccountSettings sipAccountSettings);

	void configureTransportAccountSettings(int64_t phoneHandle, int64_t sipAccountHandle, SipAccountSettings sipAccountSettings, SipAccountNetworkTransport transport);

	void applySettings(int64_t phoneHandle, int64_t sipAccountHandle);

	void enable(int64_t phoneHandle, int64_t sipAccountHandle);

	void disable(int64_t phoneHandle, int64_t sipAccountHandle);

private:
    ITransport* transport;
}; // class SipAccount
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
