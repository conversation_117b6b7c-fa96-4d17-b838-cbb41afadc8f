#include "brand_branded.h"
#if (CPCAPI2_BRAND_SIP_CHAT_MODULE == 1)

#include "SipSimpleMessageProcessor.h"

#include "../cpm/CpmHelper.h"

#include <resip/dum/ServerInviteSession.hxx>
#include <resip/dum/ServerPagerMessage.hxx>

namespace CPCAPI2
{
namespace SipChat
{

SipSimpleMessageProcessor::SipSimpleMessageProcessor()
{
}

SipSimpleMessageProcessor::~SipSimpleMessageProcessor()
{
}

int SipSimpleMessageProcessor::registerSdkDialogSetFactory(CPCAPI2::SipAccount::AppDialogSetFactory& factory)
{   
class MyAppDialogFactoryDelegate : public CPCAPI2::SipAccount::AppDialogFactoryDelegate
   {
   public:
      MyAppDialogFactoryDelegate(SipSimpleMessageProcessor *parent)
         : parent(parent) {}

      virtual bool isMyMessage(const resip::SipMessage& msg)
      {
         return parent->isMyMessage(msg);
      }

      virtual resip::AppDialogSet* createAppDialogSet(resip::DialogUsageManager& dum, const resip::SipMessage& msg)
      {
         return new CPCAPI2::SipAccount::CPInviteHandlerSelectorDialogSet(dum, parent);
      }

   private:
      SipSimpleMessageProcessor *parent;
   };

   std::shared_ptr<MyAppDialogFactoryDelegate> appDialogFactoryDelegate(new MyAppDialogFactoryDelegate(this)); // Shared pointer
   factory.addDelegate(appDialogFactoryDelegate);

   return kSuccess;}

int SipSimpleMessageProcessor::registerSdkPagerMessageHandler(CPCAPI2::SipAccount::CPPagerMessageHandler& pagerMessageHandler)
{
   pagerMessageHandler.addDelegate(this);

   return kSuccess;
}

int SipSimpleMessageProcessor::adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile)
{
   if (!profile->isMethodSupported(resip::INVITE)) profile->addSupportedMethod(resip::INVITE);
   if (!profile->isMethodSupported(resip::ACK)) profile->addSupportedMethod(resip::ACK);
   if (!profile->isMethodSupported(resip::CANCEL)) profile->addSupportedMethod(resip::CANCEL);
   if (!profile->isMethodSupported(resip::BYE)) profile->addSupportedMethod(resip::BYE);
   if (!profile->isMethodSupported(resip::MESSAGE)) profile->addSupportedMethod(resip::MESSAGE);

   return kSuccess;
}

int SipSimpleMessageProcessor::onDumBeingDestroyed()
{
   dum.reset();

   return kSuccess;
}

int SipSimpleMessageProcessor::addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig)
{
   // Keep a reference to the DUM
   this->dum = dum;

   return kSuccess;
}

bool SipSimpleMessageProcessor::isMyMessage(const resip::SipMessage& msg)
{
   // Make sure the message has the expected feature tag and MIME type
   return CPM::CpmHelper::containsFeatureTag(msg, "+g.oma.sip-im");
}

void SipSimpleMessageProcessor::onNewSession(ServerInviteSessionHandle sish, InviteSession::OfferAnswerType oat, const SipMessage& msg)
{
   // Reply to SIP/SIMPLE SIP INVITEs so that they are not left unreplied
   // NOTE: This is needed until we have a separate service for this type of message. NewPace server sends this type of message occasionally
   sish->reject(486);
}

void SipSimpleMessageProcessor::onMessageArrived(resip::ServerPagerMessageHandle h, const resip::SipMessage& sipMessage)
{
   // Reply to SIP/SIMPLE SIP MESSAGEs so that they are not left unreplied
   // NOTE: This is needed until we have a separate service for this type of message. NewPace server sends this type of message occasionally
   resip::SharedPtr<SipMessage> msg = h->accept();
   h->send(msg);
}

}
}

#endif