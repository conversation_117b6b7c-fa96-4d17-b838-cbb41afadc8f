#pragma once

#if !defined(__CPCAPI2_LICENSING_H__)
#define __CPCAPI2_LICENSING_H__

#include "LicenseDocParser.h"
#include "LicenseAuxiliaryDocParser.h"
#include "LicenseAuxiliaryDocCreater.h"
#include "LicensingTypes.h"
#include "LicensingHandler.h"
#include "licensing/LicensingClientSettings.h"
#include "../../util/HttpClient.h"

#include <libxml/xmlwriter.h>
#include <string>
#include <ctime>
#include <rutil/DeadlineTimer.hxx>
#include <rutil/MultiReactor.hxx>

namespace CPCAPI2
{
class PhoneInterface;

namespace SipAccount
{
class SipAccountInterface;
}

namespace XmppAccount
{
class XmppAccountInterface;
}

namespace Licensing
{
class XmlSignatureVerify;


class Licensing : public resip::DeadlineTimerHandler,
                  public std::enable_shared_from_this<Licensing>
{
public:
   Licensing(CPCAPI2::PhoneInterface* cpcPhone, LicensingHandler* licensingSink);
   virtual ~Licensing();

   void Initialize(const LicensingClientSettings& licenseSettings, void *context = NULL);
   void Shutdown();

   static bool PerformKeySanityCheck(const std::string&);

private:

   enum LicensingFileType
   {
      LFT_License,
      LFT_Auxiliary
   };

private:
   void initializeImpl(const LicensingClientSettings& licenseSettings, void* context);
   void InternalSetStatus(LicenseStatus, bool notify = false);
   LicenseStatus InternalDoLocalCheck();
   LicenseStatus InternalDoRemoteCheck();

   void SendLicenseRequest(bool useSecondaryUrl = false);
   const std::string CreateRequestXML();

   void processServerResponse(int, int, std::string, std::string, bool);
   void handleHTTPError(LicenseStatus);
   bool trySecondaryServer(bool fromSecondaryUrl, std::string message);

   void SetHardwareHash();
   void SetHardwareID();
   void SetMachineName();
   void SetMacAddressHash();
   void SetHarddiskHash();

   const cpc::string GetStorageFileName(LicensingFileType);
   void LicDeleteFile(LicensingFileType);
   void WriteToFile(const std::string&, LicensingFileType whichType);
   const std::string ReadFromFile(LicensingFileType);

   void StartExpiryTimer(const time_t, bool=false);
   void CancelExpiryTimer();
   time_t ReadCheckTime();
   void WriteCheckTime(const time_t);
   unsigned long CalcDaysLeft() const;
   unsigned long CalcMinutesLeft() const;
   time_t GetCurrentTimeUTC() const;
   bool IsLicenseExpired();

   void StartRunTimeTimer();
   void CancelRunTimeTimer();
   time_t ReadAccumulatedRunTime();
   void WriteAccumulatedRunTime(const time_t);
   void ResetAccumulatedRunTime();
   void handleExpiryTimer();

   void DoAccumulatedRunTimeCheck();

   bool PerformKeySanityCheck();

   void SetAllValuesFromParser(const LicenseDocParser&);
   void SetErrorValuesFromParser(const LicenseDocParser&);

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   bool SameSetKeysAsInput(const LicenseList&) const;
   bool SameBrand(const std::string&) const;
   cpc::vector<CPCAPI2::HTTPClient::StringPair> CustomHeaders(const std::string&) const;
   CPCAPI2::HTTPClient::StringPair TimestampHeader(const std::string& timestamp) const;
   CPCAPI2::HTTPClient::StringPair SignatureHeader(const std::string& timestamp, const std::string& content) const;
   std::string GenerateSignature(const std::string&, const std::string&) const;
   void sendLicenseEvent() const;
   static void fireLicenseStateEvent(const std::weak_ptr<const Licensing>& weakThis, LicensingHandler* licensingHandler, const LicenseStateEvent& evt);

private:
   LicenseList mLicenses;
   InvalidLicenseList mInvalidLicenses;
   LicenseStatus mLicenseStatus;

   bool mInitialized;
   bool mFirstCheck;

   LicensingHandler* mLicensingHandler;
   std::string mUserLicenseMessage;
   std::string mUserUrlDescription;
   std::string mUserLicenseURL;
   unsigned long mErrorCode;

   CPCAPI2::HTTPClient* mHttpClient;
   resip::DeadlineTimer<resip::MultiReactor>* mExpiryTimer;
   resip::DeadlineTimer<resip::MultiReactor>* mRTTimer;

   time_t mExpiry;
   time_t mStartTime;
   time_t mDuration;
   unsigned int mCheckDelay;
   unsigned int mMinInterval;
   unsigned int mEndPercent;

   time_t mLastRunTimeCheckTime;

   std::string mHardwareID;
   std::string mMacAddressHash;
   std::string mMachineName;
   std::vector<std::string> mAllMacs;
   std::vector<std::string> mAllHdds;
   std::string mHardwareHash;
   std::string mHardDiskHash;
   XmlSignatureVerify* mXmlSigVerify;
   resip::MultiReactor* mReactor;
   bool mOwnReactor;
   CPCAPI2::PhoneInterface* mPhone;
   CPCAPI2::SipAccount::SipAccountInterface* mSipAccountInterface;
   CPCAPI2::XmppAccount::XmppAccountInterface* mXmppAccountInterface;
   void* mContext;
   LicensingClientSettings mSettings;
}; // class Licensing

}
}

#endif // __CPCAPI2_LICENSING_H__
