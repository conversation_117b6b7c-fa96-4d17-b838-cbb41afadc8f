#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_PHONE_PHONEGETEVENT_H
#define JSONRPC_CPCAPI2_PHONE_PHONEGETEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace Phone {
  class PhoneGetEvent
  {
  public:
    int32_t handle;
    int32_t eventQueueHandle;
    int32_t timeout;

    PhoneGetEvent() = default;

    PhoneGetEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const PhoneGetEvent& o)
    {
      j = nlohmann::json::object();
       j["handle"] = o.handle;
     j["eventQueueHandle"] = o.eventQueueHandle;
     j["timeout"] = o.timeout;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, PhoneGetEvent& o)
    {
     o.handle = j.at("handle");
     o.eventQueueHandle = j.at("eventQueueHandle");
     o.timeout = j.at("timeout");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace Phone
#endif // JSONRPC_CPCAPI2_PHONE_PHONEGETEVENT_H
