#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE == 1)

#include "JsonRpcServerImpl.h"

namespace CPCAPI2
{
namespace JsonRpc
{
JsonRpcServer* JsonRpcServer::create()
{
    return new JsonRpcServerImpl();
}

JsonRpcServerImpl::JsonRpcServerImpl() : mJsonRpc(std::make_shared<jsonrpccxx::JsonRpc2Server>()),
                                         mPhoneInstances(std::make_shared<CPCAPI2::PhoneInstances>()),
                                         mPhoneCompatibility(mPhoneInstances),
                                         mPhoneServer(*this, mPhoneCompatibility),
                                         mNetworkChangeManagerCompatibility(mPhoneInstances),
                                         mNetworkChangeManagerServer(*this, mNetworkChangeManagerCompatibility),
                                         mSipAccountCompatibility(mPhoneInstances),
                                         mSipAccountServer(*this, mSipAccountCompatibility),
                                         mSipConversationCompatibility(mPhoneInstances),
                                         mSipConversationServer(*this, mSipConversationCompatibility),
                                         mMediaManagerCompatibility(mPhoneInstances),
                                         mMediaManagerServer(*this, mMediaManagerCompatibility),
                                         mSipMwiCompatibility(mPhoneInstances),
                                         mSipMwiServer(*this, mSipMwiCompatibility),
                                         mAudioCompatibility(mPhoneInstances),
                                         mAudioServer(*this, mAudioCompatibility)
{
}

bool JsonRpcServerImpl::registerFunction(const std::string &functionName, jsonrpccxx::MethodHandle callback, const jsonrpccxx::NamedParamMapping &mapping)
{
    return mJsonRpc->Add(functionName, callback, mapping);
}

std::string JsonRpcServerImpl::processIncoming(const std::string &incoming)
{
    return mJsonRpc->HandleRequest(incoming);
}
} // namespace JsonRpc
} // namespace CPCAPI2
#endif // CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE
