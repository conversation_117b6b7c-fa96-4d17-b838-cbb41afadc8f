#pragma once

#ifndef JSON_DESERIALIZE_HELPER_H
#define JSON_DESERIALIZE_HELPER_H

#include "JsonDataTypeHelpers.h"

namespace CPCAPI2
{
namespace Json
{
   template<typename T>
   typename std::enable_if<std::is_enum<T>::value, void>::type DeserializeEnum(const rapidjson::Value& json, T& v)
   {
      if (json.IsInt()) v = (T)json.GetInt();
      if (json.IsUint()) v = (T)json.GetUint();
      if (json.IsInt64()) v = (T)json.GetInt64();
      if (json.IsUint64()) v = (T)json.GetUint64();
   }

   template<typename T>
   typename std::enable_if< !std::is_enum<T>::value, void>::type DeserializeEnum(const rapidjson::Value& json, T& v)
   {
      // Not an enum so do nothing
      Deserialize(json, v);
   }

   template<size_t size>
   void Deserialize(const rapidjson::Value& json, char (&v)[size])
   {
      if (json.IsString())
      {
         size_t array_size = json.GetStringLength();
         if (size < array_size)
         {
            array_size = size;
         }
         strncpy(v, json.GetString(), array_size);
      }
   }

   template<typename T, size_t size>
   void Deserialize(const rapidjson::Value& json, T (&v)[size])
   {
      if (json.IsArray())
      {
         auto array = json.GetArray();

         size_t array_size = json.Size();
         if (size < array_size)
         {
            array_size = size;
         }

         for (int i = 0; i < array_size; i++)
         {
            DeserializeEnum(array[i], v[i]);
         }
      }
   }

   template<typename T>
   void Deserialize(const rapidjson::Value& json, cpc::vector<T>& array)
   {
      if (json.IsArray())
      {
         array.clear();
         for (auto& jsonItem : json.GetArray())
         {
            T value;
            DeserializeEnum(jsonItem, value);
            array.push_back(value);
         }
      }
   }

   template<typename T>
   void Deserialize(const rapidjson::Value& json, std::vector<T>& array)
   {
      if (json.IsArray())
      {
         array.clear();
         for (auto& jsonItem : json.GetArray())
         {
            T value;
            DeserializeEnum(jsonItem, value);
            array.push_back(value);
         }
      }
   }

   template<typename T, typename U>
   void Deserialize(const rapidjson::Value& json, std::map<T, U>& map)
   {
      if (json.IsArray())
      {
         map.clear();
         for (auto& mapElement : json.GetArray())
         {
            T key;
            U value;

            Read(mapElement, "key", key);
            Read(mapElement, "value", value);

            map[key] = value;
         }
      }
   }

   template<typename T>
   void Read(const rapidjson::Value& parent, const char* name, T& v)
   {
      if (parent.HasMember(name))
      {
         DeserializeEnum(parent[name], v);
      }
   }

   template<typename T>
   void Read(const rapidjson::Value& parent, const char* name, T& v, T d)
   {
      if (parent.HasMember(name))
      {
         DeserializeEnum(parent[name], v);
      }
      else
      {
         v = d;
      }
   }
} // namespace Json

// Add new function if you need more parameters
class JsonDeserialize
{
public:

  template<class T0>
  JsonDeserialize(const rapidjson::Value& json,
    const char* name0, T0& arg0
  )
  {
     Json::Read(json, name0, arg0);
  }

  template<class T0, class T1>
  JsonDeserialize(const rapidjson::Value& json,
    const char* name0, T0& arg0,
    const char* name1, T1& arg1
  )
  {
     Json::Read(json, name0, arg0);
     Json::Read(json, name1, arg1);
  }

  template<class T0, class T1, class T2>
  JsonDeserialize(const rapidjson::Value& json,
    const char* name0, T0& arg0,
    const char* name1, T1& arg1,
    const char* name2, T2& arg2
  )
  {
     Json::Read(json, name0, arg0);
     Json::Read(json, name1, arg1);
     Json::Read(json, name2, arg2);
  }

  template<class T0, class T1, class T2, class T3>
  JsonDeserialize(const rapidjson::Value& json,
    const char* name0, T0& arg0,
    const char* name1, T1& arg1,
    const char* name2, T2& arg2,
    const char* name3, T3& arg3
  )
  {
     Json::Read(json, name0, arg0);
     Json::Read(json, name1, arg1);
     Json::Read(json, name2, arg2);
     Json::Read(json, name3, arg3);
  }

  template<class T0, class T1, class T2, class T3, class T4>
  JsonDeserialize(const rapidjson::Value& json,
    const char* name0, T0& arg0,
    const char* name1, T1& arg1,
    const char* name2, T2& arg2,
    const char* name3, T3& arg3,
    const char* name4, T4& arg4
  )
  {
     Json::Read(json, name0, arg0);
     Json::Read(json, name1, arg1);
     Json::Read(json, name2, arg2);
     Json::Read(json, name3, arg3);
     Json::Read(json, name4, arg4);
  }

  template<class T0, class T1, class T2, class T3, class T4,
           class T5>
  JsonDeserialize(const rapidjson::Value& json,
    const char* name0, T0& arg0,
    const char* name1, T1& arg1,
    const char* name2, T2& arg2,
    const char* name3, T3& arg3,
    const char* name4, T4& arg4,
    const char* name5, T5& arg5
  )
  {
     Json::Read(json, name0, arg0);
     Json::Read(json, name1, arg1);
     Json::Read(json, name2, arg2);
     Json::Read(json, name3, arg3);
     Json::Read(json, name4, arg4);
     Json::Read(json, name5, arg5);
  }

  template<class T0, class T1, class T2, class T3, class T4,
           class T5, class T6>
  JsonDeserialize(const rapidjson::Value& json,
    const char* name0, T0& arg0,
    const char* name1, T1& arg1,
    const char* name2, T2& arg2,
    const char* name3, T3& arg3,
    const char* name4, T4& arg4,
    const char* name5, T5& arg5,
    const char* name6, T6& arg6
  )
  {
     Json::Read(json, name0, arg0);
     Json::Read(json, name1, arg1);
     Json::Read(json, name2, arg2);
     Json::Read(json, name3, arg3);
     Json::Read(json, name4, arg4);
     Json::Read(json, name5, arg5);
     Json::Read(json, name6, arg6);
  }

  template<class T0, class T1, class T2, class T3, class T4,
           class T5, class T6, class T7>
  JsonDeserialize(const rapidjson::Value& json,
    const char* name0, T0& arg0,
    const char* name1, T1& arg1,
    const char* name2, T2& arg2,
    const char* name3, T3& arg3,
    const char* name4, T4& arg4,
    const char* name5, T5& arg5,
    const char* name6, T6& arg6,
    const char* name7, T7& arg7
  )
  {
     Json::Read(json, name0, arg0);
     Json::Read(json, name1, arg1);
     Json::Read(json, name2, arg2);
     Json::Read(json, name3, arg3);
     Json::Read(json, name4, arg4);
     Json::Read(json, name5, arg5);
     Json::Read(json, name6, arg6);
     Json::Read(json, name7, arg7);
  }

  template<class T0, class T1, class T2, class T3, class T4,
           class T5, class T6, class T7, class T8>
  JsonDeserialize(const rapidjson::Value& json,
    const char* name0, T0& arg0,
    const char* name1, T1& arg1,
    const char* name2, T2& arg2,
    const char* name3, T3& arg3,
    const char* name4, T4& arg4,
    const char* name5, T5& arg5,
    const char* name6, T6& arg6,
    const char* name7, T7& arg7,
    const char* name8, T8& arg8
  )
  {
     Json::Read(json, name0, arg0);
     Json::Read(json, name1, arg1);
     Json::Read(json, name2, arg2);
     Json::Read(json, name3, arg3);
     Json::Read(json, name4, arg4);
     Json::Read(json, name5, arg5);
     Json::Read(json, name6, arg6);
     Json::Read(json, name7, arg7);
     Json::Read(json, name8, arg8);
  }

  template<class T0, class T1, class T2, class T3, class T4,
           class T5, class T6, class T7, class T8, class T9>
  JsonDeserialize(const rapidjson::Value& json,
    const char* name0, T0& arg0,
    const char* name1, T1& arg1,
    const char* name2, T2& arg2,
    const char* name3, T3& arg3,
    const char* name4, T4& arg4,
    const char* name5, T5& arg5,
    const char* name6, T6& arg6,
    const char* name7, T7& arg7,
    const char* name8, T8& arg8,
    const char* name9, T9& arg9
  )
  {
     Json::Read(json, name0, arg0);
     Json::Read(json, name1, arg1);
     Json::Read(json, name2, arg2);
     Json::Read(json, name3, arg3);
     Json::Read(json, name4, arg4);
     Json::Read(json, name5, arg5);
     Json::Read(json, name6, arg6);
     Json::Read(json, name7, arg7);
     Json::Read(json, name8, arg8);
     Json::Read(json, name9, arg9);
  }

  template<class T0, class T1, class T2, class T3, class T4,
           class T5, class T6, class T7, class T8, class T9,
           class T10>
  JsonDeserialize(const rapidjson::Value& json,
    const char* name0, T0& arg0,
    const char* name1, T1& arg1,
    const char* name2, T2& arg2,
    const char* name3, T3& arg3,
    const char* name4, T4& arg4,
    const char* name5, T5& arg5,
    const char* name6, T6& arg6,
    const char* name7, T7& arg7,
    const char* name8, T8& arg8,
    const char* name9, T9& arg9,
    const char* name10, T10& arg10
  )
  {
     Json::Read(json, name0, arg0);
     Json::Read(json, name1, arg1);
     Json::Read(json, name2, arg2);
     Json::Read(json, name3, arg3);
     Json::Read(json, name4, arg4);
     Json::Read(json, name5, arg5);
     Json::Read(json, name6, arg6);
     Json::Read(json, name7, arg7);
     Json::Read(json, name8, arg8);
     Json::Read(json, name9, arg9);
     Json::Read(json, name10, arg10);
  }

  template<class T0, class T1, class T2, class T3, class T4,
           class T5, class T6, class T7, class T8, class T9,
           class T10, class T11>
  JsonDeserialize(const rapidjson::Value& json,
    const char* name0, T0& arg0,
    const char* name1, T1& arg1,
    const char* name2, T2& arg2,
    const char* name3, T3& arg3,
    const char* name4, T4& arg4,
    const char* name5, T5& arg5,
    const char* name6, T6& arg6,
    const char* name7, T7& arg7,
    const char* name8, T8& arg8,
    const char* name9, T9& arg9,
    const char* name10, T10& arg10,
    const char* name11, T11& arg11
  )
  {
     Json::Read(json, name0, arg0);
     Json::Read(json, name1, arg1);
     Json::Read(json, name2, arg2);
     Json::Read(json, name3, arg3);
     Json::Read(json, name4, arg4);
     Json::Read(json, name5, arg5);
     Json::Read(json, name6, arg6);
     Json::Read(json, name7, arg7);
     Json::Read(json, name8, arg8);
     Json::Read(json, name9, arg9);
     Json::Read(json, name10, arg10);
     Json::Read(json, name11, arg11);
  }

  template<class T0, class T1, class T2, class T3, class T4,
           class T5, class T6, class T7, class T8, class T9,
           class T10, class T11, class T12>
  JsonDeserialize(const rapidjson::Value& json,
    const char* name0, T0& arg0,
    const char* name1, T1& arg1,
    const char* name2, T2& arg2,
    const char* name3, T3& arg3,
    const char* name4, T4& arg4,
    const char* name5, T5& arg5,
    const char* name6, T6& arg6,
    const char* name7, T7& arg7,
    const char* name8, T8& arg8,
    const char* name9, T9& arg9,
    const char* name10, T10& arg10,
    const char* name11, T11& arg11,
    const char* name12, T12& arg12
  )
  {
     Json::Read(json, name0, arg0);
     Json::Read(json, name1, arg1);
     Json::Read(json, name2, arg2);
     Json::Read(json, name3, arg3);
     Json::Read(json, name4, arg4);
     Json::Read(json, name5, arg5);
     Json::Read(json, name6, arg6);
     Json::Read(json, name7, arg7);
     Json::Read(json, name8, arg8);
     Json::Read(json, name9, arg9);
     Json::Read(json, name10, arg10);
     Json::Read(json, name11, arg11);
     Json::Read(json, name12, arg12);
  }

  template<class T0, class T1, class T2, class T3, class T4,
           class T5, class T6, class T7, class T8, class T9,
           class T10, class T11, class T12, class T13>
  JsonDeserialize(const rapidjson::Value& json,
    const char* name0, T0& arg0,
    const char* name1, T1& arg1,
    const char* name2, T2& arg2,
    const char* name3, T3& arg3,
    const char* name4, T4& arg4,
    const char* name5, T5& arg5,
    const char* name6, T6& arg6,
    const char* name7, T7& arg7,
    const char* name8, T8& arg8,
    const char* name9, T9& arg9,
    const char* name10, T10& arg10,
    const char* name11, T11& arg11,
    const char* name12, T12& arg12,
    const char* name13, T13& arg13
  )
  {
     Json::Read(json, name0, arg0);
     Json::Read(json, name1, arg1);
     Json::Read(json, name2, arg2);
     Json::Read(json, name3, arg3);
     Json::Read(json, name4, arg4);
     Json::Read(json, name5, arg5);
     Json::Read(json, name6, arg6);
     Json::Read(json, name7, arg7);
     Json::Read(json, name8, arg8);
     Json::Read(json, name9, arg9);
     Json::Read(json, name10, arg10);
     Json::Read(json, name11, arg11);
     Json::Read(json, name12, arg12);
     Json::Read(json, name13, arg13);
  }

  template<class T0, class T1, class T2, class T3, class T4,
           class T5, class T6, class T7, class T8, class T9,
           class T10, class T11, class T12, class T13, class T14>
  JsonDeserialize(const rapidjson::Value& json,
    const char* name0, T0& arg0,
    const char* name1, T1& arg1,
    const char* name2, T2& arg2,
    const char* name3, T3& arg3,
    const char* name4, T4& arg4,
    const char* name5, T5& arg5,
    const char* name6, T6& arg6,
    const char* name7, T7& arg7,
    const char* name8, T8& arg8,
    const char* name9, T9& arg9,
    const char* name10, T10& arg10,
    const char* name11, T11& arg11,
    const char* name12, T12& arg12,
    const char* name13, T13& arg13,
    const char* name14, T14& arg14
  )
  {
     Json::Read(json, name0, arg0);
     Json::Read(json, name1, arg1);
     Json::Read(json, name2, arg2);
     Json::Read(json, name3, arg3);
     Json::Read(json, name4, arg4);
     Json::Read(json, name5, arg5);
     Json::Read(json, name6, arg6);
     Json::Read(json, name7, arg7);
     Json::Read(json, name8, arg8);
     Json::Read(json, name9, arg9);
     Json::Read(json, name10, arg10);
     Json::Read(json, name11, arg11);
     Json::Read(json, name12, arg12);
     Json::Read(json, name13, arg13);
     Json::Read(json, name14, arg14);
  }

};

} // namespace CPCAPI2

#endif
