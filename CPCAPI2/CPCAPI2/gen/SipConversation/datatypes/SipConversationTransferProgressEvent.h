#include "jsonrpc/OptionalSerialization.h"
#include <nlohmann/json.hpp>
#include "TransferProgressEventType.h"

#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONTRANSFERPROGRESSEVENT_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONTRANSFERPROGRESSEVENT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
  class SipConversationTransferProgressEvent
  {
  public:
    std::optional<int32_t> conversationHandle;
    std::optional<TransferProgressEventType> progressEventType;
    std::optional<int32_t> sipResponseCode;

    SipConversationTransferProgressEvent() = default;

    SipConversationTransferProgressEvent(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipConversationTransferProgressEvent& o)
    {
      j = nlohmann::json::object();
       if (o.conversationHandle) { j["conversationHandle"] = *(o.conversationHandle); }
     if (o.progressEventType) { j["progressEventType"] = *(o.progressEventType); }
     if (o.sipResponseCode) { j["sipResponseCode"] = *(o.sipResponseCode); }
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipConversationTransferProgressEvent& o)
    {
     if (j.contains("conversationHandle")) { o.conversationHandle = j.at("conversationHandle"); }
     if (j.contains("progressEventType")) { o.progressEventType = j.at("progressEventType"); }
     if (j.contains("sipResponseCode")) { o.sipResponseCode = j.at("sipResponseCode"); }
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONTRANSFERPROGRESSEVENT_H
