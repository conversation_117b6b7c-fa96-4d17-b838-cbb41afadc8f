#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_CHAT_MODULE==1 || CPCAPI2_BRAND_IM_MODULE==1 || CPCAPI2_BRAND_XMPP_CHAT_MODULE==1 || CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
#include "ReceiverActiveState.h"
#include "ReceiverIdleState.h"
#include "IsComposingManager.h"
#include "IsComposingInfo.h"

namespace CPCAPI2
{
namespace IsComposing
{

ReceiverActiveState ReceiverActiveState::instance;

void ReceiverActiveState::onEnterState(IsComposingManager* manager, IsComposingInfo* info)
{
   // Start the timer
   manager->startReceiverRefreshTimer(info);
}

void ReceiverActiveState::onIdleStateReceived(IsComposingManager* manager, IsComposingInfo* info)
{
   moveToIdleState(manager, info);
}

void ReceiverActiveState::onActiveStateReceived(IsComposingManager* manager, IsComposingInfo* info)
{
   // Reset the timer
   manager->stopReceiverRefreshTimer(info);
   manager->startReceiverRefreshTimer(info);

   // Don't send any notification
   // bliu: should notify about the latest active status
   manager->onIsComposingMessage(info, IsComposingMessageState_Active, info->receiverContentType, info->receiverLastActive);

   // Stay in this state
}

void ReceiverActiveState::onMessageReceived(IsComposingManager* manager, IsComposingInfo* info)
{
   moveToIdleState(manager, info);
}

void ReceiverActiveState::onRefreshTimeout(IsComposingManager* manager, IsComposingInfo* info)
{
   moveToIdleState(manager, info);
}

void ReceiverActiveState::moveToIdleState(IsComposingManager* manager, IsComposingInfo* info)
{
   // Invoke the callback with Idle state
   manager->onIsComposingMessage(info, IsComposingMessageState_Idle, info->receiverContentType, info->receiverLastActive);

   // Transition to the Idle State
   manager->transitionTo(info, &ReceiverIdleState::instance);
   manager->backToReceiverIdleState(info);
}

void ReceiverActiveState::onLeaveState(IsComposingManager* manager, IsComposingInfo* info)
{
   // Stop the timer
   manager->stopReceiverRefreshTimer(info);
}

}
}
#endif
