#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_NETWORKCHANGEMANAGER_NETWORKCHANGEMANAGERNETWORKTRANSPORT_H
#define JSONRPC_CPCAPI2_NETWORKCHANGEMANAGER_NETWORKCHANGEMANAGERNETWORKTRANSPORT_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace NetworkChangeManager {
  class NetworkChangeManagerNetworkTransport
  {
  public:
    int32_t handle;

    NetworkChangeManagerNetworkTransport() = default;

    NetworkChangeManagerNetworkTransport(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const NetworkChangeManagerNetworkTransport& o)
    {
      j = nlohmann::json::object();
       j["handle"] = o.handle;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, NetworkChangeManagerNetworkTransport& o)
    {
     o.handle = j.at("handle");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace NetworkChangeManager
#endif // JSONRPC_CPCAPI2_NETWORKCHANGEMANAGER_NETWORKCHANGEMANAGERNETWORKTRANSPORT_H
