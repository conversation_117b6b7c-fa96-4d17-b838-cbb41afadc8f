#include "brand_branded.h"

#if 1 //(CPCAPI2_BRAND_AUTH_SERVER_MODULE == 1)

#include "AuthServerJwtUtils.h"

#include <rutil/DataStream.hxx>
#include <rutil/ParseBuffer.hxx>

#include <fstream>
#include <sstream>
#include <openssl/ec.h>
#include <openssl/pem.h>
#include <openssl/hmac.h>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

#include <memory>
#include <mutex>

#include "util/cpc_logger.h"
#include "util/OpenSSLHelper.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL


/* .jjg. note we use a 256 bit curve, unlike the 521 bit one used in the sample this was derived from
       because we are using 256 bit keys as input (which is really just because we have to use ES 256
       in order to support Apple push) */
#define ECIES_CURVE NID_X9_62_prime256v1
#define ECIES_CIPHER EVP_aes_256_cbc()
#define ECIES_HASHER EVP_sha512()

namespace CPCAPI2
{

namespace AuthServer
{

// adapted from http://eclipsesource.com/blogs/2016/09/07/tutorial-code-signing-and-verification-with-openssl/
openssl_shared_ptr<EC_KEY> createPrivateECkey(const resip::Data& key)
{
   openssl_unique_ptr<BIO> keybio(BIO_new_mem_buf(key.c_str(), key.size()));
   if (!keybio) {
      return NULL;
   }

   return openssl_shared_ptr<EC_KEY>(PEM_read_bio_ECPrivateKey(keybio.get(), NULL, NULL, NULL));
}

openssl_shared_ptr<EVP_PKEY> createPublicECkey(const resip::Data& key)
{
   openssl_unique_ptr<BIO> keybio(BIO_new_mem_buf(key.c_str(), key.size()));
   if (!keybio)
   {
      DebugLog(<< "JwtUtils::createPublicECkey: key bio is NULL, key: " << key.c_str() << " size: " << key.size());
      return NULL;
   }

   return openssl_shared_ptr<EVP_PKEY>(PEM_read_bio_PUBKEY(keybio.get(), NULL, NULL, NULL));
}

// adapted from http://eclipsesource.com/blogs/2016/09/07/tutorial-code-signing-and-verification-with-openssl/
bool ECSign(openssl_shared_ptr<EC_KEY> eckey,
   const resip::Data& msg,
   resip::Data& signedMsg)
{
   openssl_unique_ptr<EVP_MD_CTX> m_SignCtx(EVP_MD_CTX_create());
   openssl_unique_ptr<EVP_PKEY> priKey(EVP_PKEY_new());
   EVP_PKEY_assign_EC_KEY(priKey.get(), eckey.get());
   if (EVP_DigestSignInit(m_SignCtx.get(), NULL, EVP_sha256(), NULL, priKey.get()) <= 0) {
      return false;
   }
   if (EVP_DigestSignUpdate(m_SignCtx.get(), msg.data(), msg.size()) <= 0) {
      return false;
   }
   size_t MsgLenEnc;
   if (EVP_DigestSignFinal(m_SignCtx.get(), NULL, &MsgLenEnc) <= 0) {
      return false;
   }
   if (EVP_DigestSignFinal(m_SignCtx.get(), (unsigned char*)signedMsg.getBuf(MsgLenEnc), &MsgLenEnc) <= 0) {
      return false;
   }

   // ----
   unsigned int degree, bn_len, r_len, s_len, buf_len;

   /* For EC we need to convert to a raw format of R/S. */

   /* Get the actual ec_key */
   EC_KEY* ec_key(EVP_PKEY_get1_EC_KEY(priKey.get()));
   if (nullptr == ec_key)
      return false;

   degree = EC_GROUP_get_degree(EC_KEY_get0_group(ec_key));

   /* Get the sig from the DER encoded version. */
   char* dataBuf = signedMsg.getBuf(signedMsg.size());
   openssl_unique_ptr<ECDSA_SIG> ec_sig(d2i_ECDSA_SIG(NULL, (const unsigned char **)&dataBuf, MsgLenEnc));
   if (!ec_sig)
      return false;

   const BIGNUM* ec_sig_r;
   const BIGNUM* ec_sig_s;
   ECDSA_SIG_get0(ec_sig.get(), &ec_sig_r, &ec_sig_s);
   r_len = BN_num_bytes(ec_sig_r);
   s_len = BN_num_bytes(ec_sig_s);
   bn_len = (degree + 7) / 8;
   if ((r_len > bn_len) || (s_len > bn_len))
      return false;

   buf_len = 2 * bn_len;
   unsigned char* buf = (unsigned char*)signedMsg.getBuf(buf_len);

   /* Pad the bignums with leading zeroes. */
   memset(buf, 0, buf_len);
   BN_bn2bin(ec_sig_r, buf + bn_len - r_len);
   BN_bn2bin(ec_sig_s, buf + buf_len - s_len);
   // ----

   return true;
}

// adapted from http://doctrina.org/Base64-With-OpenSSL-C-API.html
// encodes a binary safe base 64 string
resip::Data Base64Encode(const resip::Data& toencode)
{
   BIO *bio, *b64;
   BUF_MEM *bufferPtr;

   b64 = BIO_new(BIO_f_base64());
   bio = BIO_new(BIO_s_mem());
   bio = BIO_push(b64, bio);

   BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL); //Ignore newlines - write everything in one line
   BIO_write(bio, toencode.data(), toencode.size());
   BIO_flush(bio);
   BIO_get_mem_ptr(bio, &bufferPtr);
   BIO_set_close(bio, BIO_NOCLOSE);
   BIO_free_all(bio);

   resip::Data ret((*bufferPtr).data, (*bufferPtr).length);
   //ret.replace("+", "-");
   //ret.replace("/", "_");

   BUF_MEM_free(bufferPtr);

   return ret;
}

resip::Data JwtUtils::loadCertFromFile(const resip::Data& fileName)
{
   std::ifstream in(fileName.c_str());
   if (in.is_open())
   {
      std::ostringstream oss;
      oss << in.rdbuf() << std::flush;

      resip::Data doc(oss.str().c_str(), oss.str().size());
      return doc;
   }
   InfoLog(<< "JwtUtils::loadCertFromFile: could not open file: " << fileName.c_str());
   return resip::Data::Empty;
}

int JwtUtils::GenerateJWT(const resip::Data& p8file, const resip::Data& issuer, const std::map<resip::Data, resip::Data>& publicClaims, int validSeconds, resip::Data& outJwt)
{
   time_t genTime = time(NULL);

   resip::Data header;
   {
      resip::DataStream headerDs(header);
      headerDs << "{ \"alg\": \"ES256\" }";
   }
   resip::Data claims;
   {
      resip::DataStream claimsDs(claims);
      claimsDs << "{"
         << " \"iss\": \"" << issuer << "\","
         << " \"iat\": " << resip::Data::from(genTime);

      if (validSeconds > 0)
      {
         time_t expTime = genTime + validSeconds;
         claimsDs << "," << " \"exp\": " << resip::Data::from(expTime);
      }

      for (auto claim : publicClaims)
      {
         claimsDs << ",";
         claimsDs << " \"" << claim.first << "\": ";
         if (!claim.second.prefix("["))
         {
            claimsDs << "\"";
         }
         claimsDs << claim.second;
         if (!claim.second.prefix("["))
         {
            claimsDs << "\"";
         }
      }

      claimsDs << " }";
   }
   resip::Data headerAndClaimsBase64;
   {
      resip::DataStream headerAndClaimsDs(headerAndClaimsBase64);
      headerAndClaimsDs << Base64Encode(header) << "." << Base64Encode(claims);
   }

   resip::Data cert = loadCertFromFile(p8file);
   openssl_shared_ptr<EC_KEY> eckeyobj = createPrivateECkey(cert);
   if (!eckeyobj)
   {
      return -1;
   }

   resip::Data signedHeaderAndClaims;
   if (!ECSign(eckeyobj, headerAndClaimsBase64, signedHeaderAndClaims))
   {
      return -1;
   }

   outJwt.clear();
   {
      resip::DataStream jwtDs(outJwt);
      jwtDs << headerAndClaimsBase64 << "." << Base64Encode(signedHeaderAndClaims);
   }
   return 0;
}

size_t calcDecodeLength(const char* b64input) {
   size_t len = strlen(b64input), padding = 0;

   if (b64input[len - 1] == '=' && b64input[len - 2] == '=') //last two chars are =
      padding = 2;
   else if (b64input[len - 1] == '=') //last char is =
      padding = 1;
   return (len * 3) / 4 - padding;
}

resip::Data Base64Decode(const resip::Data& b64message) {
   BIO *bio, *b64;

   resip::Data b64message_esc(b64message);
   b64message_esc.replace("-", "+");
   b64message_esc.replace("_", "/");

   int decodeLen = calcDecodeLength(b64message_esc.c_str());
   resip::Data ret(decodeLen + 1, resip::Data::Preallocate);

   bio = BIO_new_mem_buf(b64message_esc.c_str(), -1);
   b64 = BIO_new(BIO_f_base64());
   bio = BIO_push(b64, bio);

   BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL); //Do not use newlines to flush buffer
   int length = BIO_read(bio, ret.getBuf(decodeLen+1), b64message_esc.size());
   ret.truncate2(length);
   BIO_free_all(bio);
   return ret;
}

bool ECVerifySignature(openssl_shared_ptr<EVP_PKEY> pkey,
   const resip::Data& signedHeaderDotClaims,
   const resip::Data& headerDotClaims,
   bool* authentic)
{
   *authentic = false;

   openssl_unique_ptr<EVP_MD_CTX> mdVerifyCtx(EVP_MD_CTX_create());

   if (EVP_DigestVerifyInit(mdVerifyCtx.get(), NULL, EVP_sha256(), NULL, pkey.get()) <= 0)
   {
      return false;
   }
   if (EVP_DigestVerifyUpdate(mdVerifyCtx.get(), headerDotClaims.c_str(), headerDotClaims.size()) <= 0) {
      return false;
   }
   int AuthStatus = EVP_DigestVerifyFinal(mdVerifyCtx.get(), (const unsigned char*)signedHeaderDotClaims.c_str(), signedHeaderDotClaims.size());
   if (AuthStatus == 1) {
      *authentic = true;
      return true;
   }
   else if (AuthStatus == 0) {
      *authentic = false;
      return true;
   }
   else {
      *authentic = false;
      return false;
   }
}

int JwtUtils::VerifyJWT(const resip::Data& p8file, const resip::Data& jwt, bool& outValid, std::map<resip::Data, resip::Data>* outPublicClaims, std::vector<resip::Data>* requestedResources)
{
   outValid = false;

   resip::Data cert = loadCertFromFile(p8file);
   openssl_shared_ptr<EVP_PKEY> pkey = createPublicECkey(cert);
   if (!pkey)
   {
      DebugLog(<< "JwtUtils::VerifyJWT: public key is NULL");
      return -1;
   }

   if (jwt.size() == 0)
   {
      DebugLog(<< "JwtUtils::VerifyJWT: JWT size is 0");
      return -1;
   }

   try
   {
   resip::ParseBuffer pb(jwt);
   const char* anchor = pb.position();
   pb.skipToChar('.');
   //resip::Data header_b64 = pb.data(anchor);
   pb.skipChar();
   //anchor = pb.position();
   pb.skipToChar('.');
   resip::Data headerAndClaims_b64 = pb.data(anchor);
   pb.skipChar();
   anchor = pb.position();
   pb.skipToEnd();
   resip::Data signedHeaderAndClaims_b64 = pb.data(anchor);
   resip::Data signedHeaderAndClaims = Base64Decode(signedHeaderAndClaims_b64);

   unsigned int degree, bn_len;
   unsigned char *sig = (unsigned char*)signedHeaderAndClaims.c_str();
   int slen = signedHeaderAndClaims.size();

   /* Convert EC sigs back to ASN1. */
   openssl_unique_ptr<ECDSA_SIG> ec_sig(ECDSA_SIG_new());
   if (!ec_sig)
   {
      DebugLog(<< "JwtUtils::VerifyJWT: EC sig is NULL");
      return -1;
   }

   /* Get the actual ec_key */
   openssl_unique_ptr<EC_KEY> ec_key = EVP_PKEY_get1_EC_KEY(pkey.get());
   if (nullptr == ec_key)
   {
      DebugLog(<< "JwtUtils::VerifyJWT: EC key is NULL");
      return -1;
   }

   degree = EC_GROUP_get_degree(EC_KEY_get0_group(ec_key.get()));

   bn_len = (degree + 7) / 8;
   if ((bn_len * 2) != slen)
   {
      DebugLog(<< "JwtUtils::VerifyJWT: length: " << bn_len << " does not match: " << slen);
      return -1;
   }

   BIGNUM* ec_sig_r = BN_bin2bn(sig, bn_len, NULL);
   BIGNUM* ec_sig_s = BN_bin2bn(sig + bn_len, bn_len, NULL);
   if (nullptr == ec_sig_r || nullptr == ec_sig_s)
   {
      DebugLog(<< "JwtUtils::VerifyJWT: EC sig bin2bn is NULL, ec_sig_r: " << ec_sig_r << " ec_sig_s: " << ec_sig_s);
      return -1;
   }

   ECDSA_SIG_set0(ec_sig.get(), ec_sig_r, ec_sig_s);

   slen = i2d_ECDSA_SIG(ec_sig.get(), NULL);
   resip::Data signedHeaderAndClaims_converted;
   sig = (unsigned char*)signedHeaderAndClaims_converted.getBuf(slen);
   if (sig == NULL)
   {
      DebugLog(<< "JwtUtils::VerifyJWT: converted sig is NULL");
      return -1;
   }

   unsigned char* p = sig;
   slen = i2d_ECDSA_SIG(ec_sig.get(), &p);

   if (slen == 0)
   {
      DebugLog(<< "JwtUtils::VerifyJWT: ECDSA sig length: " << slen);
      return -1;
   }

   signedHeaderAndClaims_converted.truncate2(slen);

   bool authentic;
   bool result = ECVerifySignature(pkey, signedHeaderAndClaims_converted, headerAndClaims_b64, &authentic);
   if (result)
   {
      outValid = authentic;

      if (authentic)
      {
         resip::ParseBuffer pbHC(headerAndClaims_b64);
         //const char* anchor = pbHC.position();
         pbHC.skipToChar('.');
         //resip::Data header_b64 = pb.data(anchor);
         pbHC.skipChar();
         const char* claimsStart = pbHC.position();
         pbHC.skipToEnd();
         resip::Data claims_b64 = pbHC.data(claimsStart);
         resip::Data claims = Base64Decode(claims_b64);

         std::shared_ptr<rapidjson::Document> claimsJson(new rapidjson::Document);
         claimsJson->Parse<0>(claims.c_str());

         if (claimsJson->HasParseError())
         {
            // well, not really authentic then are we?
            authentic = false;
            DebugLog(<< "JwtUtils::VerifyJWT: json parse error");
            return -1;
         }

         if (outPublicClaims != NULL)
         {
            for (rapidjson::Value::ConstMemberIterator itr = claimsJson->MemberBegin(); itr != claimsJson->MemberEnd(); ++itr)
            {
               if (itr->value.GetType() == rapidjson::kStringType)
               {
                  (*outPublicClaims)[itr->name.GetString()] = itr->value.GetString();
                  // StackLog(<< "JwtUtils::VerifyJWT: data type: " << itr->value.GetType() << " name: " << itr->name.GetString() << " string: " << itr->value.GetString());
               }
               else if (itr->value.GetType() == rapidjson::kNumberType)
               {
                  resip::Data numData;
                  {
                     resip::DataStream ds(numData);
                     ds << itr->value.GetInt(); // !jjg! what if it isn't an int?
                     // StackLog(<< "JwtUtils::VerifyJWT: data type: " << itr->value.GetType() << " name: " << itr->name.GetString() << " data: " << itr->value.GetInt());
                  }
                  (*outPublicClaims)[itr->name.GetString()] = numData;

                  if (strcmp(itr->name.GetString(), "exp") == 0)
                  {
                     time_t expiry = (time_t)numData.convertUInt64();
                     if (difftime(expiry, time(NULL)) < 0)
                     {
                        ErrLog(<< "JwtUtils::VerifyJWT: Token has expired.");
                        return -1;
                     }
                  }
               }
               else if (itr->value.GetType() == rapidjson::kArrayType && resip::isEqualNoCase("requested_resources", itr->name.GetString()))
               {
                  if (requestedResources != NULL)
                  {
                     const rapidjson::Value& arrVal = itr->value;
                     for (rapidjson::Value::ConstValueIterator itRR = arrVal.Begin(); itRR != arrVal.End(); ++itRR)
                     {
                        if (itRR->IsString())
                        {
                           requestedResources->push_back(resip::Data(itRR->GetString(), itRR->GetStringLength()));
                        }
                     }
                  }
                  (*outPublicClaims)[itr->name.GetString()] = "";
               }
               else
               {
                  StackLog(<< "JwtUtils::VerifyJWT: name: " << itr->name.GetString() << " unsupported type: " << itr->value.GetType());
               }
            }
         }
      }

      return 0;
   }
   else
   {
      DebugLog(<< "JwtUtils::VerifyJWT: failure in EC signature verification");
   }
   }
   catch (const resip::ParseException& rpe)
   {
      InfoLog(<< "JwtUtils::VerifyJWT: exception: " << rpe.getContext() << "\r\n" << rpe.getMessage());
   }

   return -1;
}

openssl_shared_ptr<EC_GROUP> eliptic(nullptr);

void ecies_group_init(void) {
   static std::mutex sLock;
   std::lock_guard<std::mutex> guard(sLock);

   if (eliptic)
   {
      return;
   }

   openssl_shared_ptr<EC_GROUP> group(EC_GROUP_new_by_curve_name(ECIES_CURVE));

   if (!group) {
      // std::cout << "EC_GROUP_new_by_curve_name failed. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
   }

   else if (EC_GROUP_precompute_mult(group.get(), NULL) != 1) {
      //std::cout << "EC_GROUP_precompute_mult failed. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      group = NULL;
   }

   EC_GROUP_set_point_conversion_form(group.get(), POINT_CONVERSION_COMPRESSED);
   eliptic = group;

   return;
}

// But its worth noting that duplicating the group instead of allocating it on each pass reduced the execution time by 50 % !
openssl_shared_ptr<EC_GROUP> ecies_group(void) {
   if (!eliptic) {
      ecies_group_init();
   }

   return openssl_shared_ptr<EC_GROUP>(EC_GROUP_dup(eliptic.get()));
}

openssl_shared_ptr<EC_KEY> ecies_key_create(void) {
   openssl_shared_ptr<EC_KEY> key(EC_KEY_new());
   if (!key) {
      //std::cout << "EC_KEY_new failed. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      return key;
   }

   openssl_shared_ptr<EC_GROUP> group(ecies_group());
   if (!group) {
      key = NULL;
      return key;
   }

   if (EC_KEY_set_group(key.get(), group.get()) != 1) {
      //std::cout << "EC_KEY_set_group failed. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      group = NULL;
      key = NULL;
      return key;
   }

   if (EC_KEY_generate_key(key.get()) != 1) {
      //std::cout << "EC_KEY_generate_key failed. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      key = NULL;
      return key;
   }

   return key;
}

void* ecies_key_derivation(const void *input, size_t ilen, void *output, size_t *olen) {

   if (*olen < SHA512_DIGEST_LENGTH) {
      return NULL;
   }

   *olen = SHA512_DIGEST_LENGTH;
   return SHA512((const unsigned char*)input, ilen, (unsigned char*)output);
}

typedef struct {

   struct {
      uint64_t key;
      uint64_t mac;
      uint64_t orig;
      uint64_t body;
   } length;

} secure_head_t;

typedef char * secure_t;

uint64_t secure_key_length(secure_t *cryptex) {
   secure_head_t *head = (secure_head_t *)cryptex;
   return head->length.key;
}

uint64_t secure_mac_length(secure_t *cryptex) {
   secure_head_t *head = (secure_head_t *)cryptex;
   return head->length.mac;
}

uint64_t secure_body_length(secure_t *cryptex) {
   secure_head_t *head = (secure_head_t *)cryptex;
   return head->length.body;
}

uint64_t secure_orig_length(secure_t *cryptex) {
   secure_head_t *head = (secure_head_t *)cryptex;
   return head->length.orig;
}

uint64_t secure_total_length(secure_t *cryptex) {
   secure_head_t *head = (secure_head_t *)cryptex;
   return sizeof(secure_head_t) + (head->length.key + head->length.mac +
      head->length.body);
}

void * secure_key_data(secure_t *cryptex) {
   return (char *)cryptex + sizeof(secure_head_t);
}

void * secure_mac_data(secure_t *cryptex) {
   secure_head_t *head = (secure_head_t *)cryptex;
   return (char *)cryptex + (sizeof(secure_head_t) + head->length.key);
}

void * secure_body_data(secure_t *cryptex) {
   secure_head_t *head = (secure_head_t *)cryptex;
   return (char *)cryptex + (sizeof(secure_head_t) + head->length.key +
      head->length.mac);
}

void * secure_alloc(uint64_t key, uint64_t mac, uint64_t orig, uint64_t body) {
   secure_t *cryptex = (secure_t*)malloc(sizeof(secure_head_t) + key + mac + body);
   secure_head_t *head = (secure_head_t *)cryptex;
   head->length.key = key;
   head->length.mac = mac;
   head->length.orig = orig;
   head->length.body = body;
   return cryptex;
}

void secure_free(secure_t *cryptex) {
   free(cryptex);
   return;
}

/**
 * adapted from https://www.mail-archive.com/<EMAIL>/msg28042.html
 */
int JwtUtils::Encrypt(const resip::Data& p8file, const resip::Data& inData, resip::Data& encrypted, bool base64encode)
{
   unsigned char *body;
   int body_length;
   secure_t *cryptex;
   unsigned int mac_length;
   size_t envelope_length, block_length, key_length;
   unsigned char envelope_key[SHA512_DIGEST_LENGTH],
      iv[EVP_MAX_IV_LENGTH], block[EVP_MAX_BLOCK_LENGTH];

   // Make sure we are generating enough key material for the symmetric ciphers.
   if ((key_length = EVP_CIPHER_key_length(ECIES_CIPHER)) * 2 > SHA512_DIGEST_LENGTH) {
      //std::cout << "The key derivation method will not produce enough
      //   envelope key material for the chosen ciphers. {envelope = " << SHA512_DIGEST_LENGTH / 8 << " / required =
      //   "<< (key_length * 2) / 8) << "}" << std::endl;
      return -1;
   }

   resip::Data cert = loadCertFromFile(p8file);
   if (cert.empty())
   {
      // the param can either be a path to a file, or the contents of the file (i.e. the key itself)
      cert = p8file;
   }

   openssl_shared_ptr<EVP_PKEY> pkey = createPublicECkey(cert);
   if (!pkey)
   {
      return -1;
   }

   /* Get the actual ec_key */
   openssl_unique_ptr<EC_KEY> user(EVP_PKEY_get1_EC_KEY(pkey.get()));
   openssl_shared_ptr<EC_KEY> ephemeral(nullptr);
   if (!user)
   {
      return -1;
   }

   // Create the ephemeral key used specifically for this block of data.
   else if (!(ephemeral = ecies_key_create()) || !ephemeral) {
      //std::cout << "An error occurred while trying to generate the ephemeral key." << std::endl;
      return -1;
   }

   // Use the intersection of the provided keys to generate the envelope data used by the ciphers below.The ecies_key_derivation() function uses
   // SHA 512 to ensure we have a sufficient amount of envelope key material and that the material created is sufficiently secure.
   else if (ECDH_compute_key(envelope_key, SHA512_DIGEST_LENGTH,
      EC_KEY_get0_public_key(user.get()), ephemeral.get(), ecies_key_derivation) != SHA512_DIGEST_LENGTH) {
      //std::cout << "An error occurred while trying to compute the envelope key. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      return -1;
   }

   // Determine the envelope and block lengths so we can allocate a buffer for the result.
   else if ((block_length = EVP_CIPHER_block_size(ECIES_CIPHER)) == 0 ||
             block_length > EVP_MAX_BLOCK_LENGTH ||
             (envelope_length = EC_POINT_point2oct(EC_KEY_get0_group(ephemeral.get()), EC_KEY_get0_public_key(ephemeral.get()),
              POINT_CONVERSION_COMPRESSED, NULL, 0, NULL)) == 0) {
      //std::cout << "Invalid block or envelope length. {block = " << block_length << " / envelope = " << envelope_length << "}" << std::endl;
      return -1;
   }

   // We use a conditional to pad the length if the input buffer is not evenly divisible by the block size.
   else if (!(cryptex = (secure_t*)secure_alloc(envelope_length,
      EVP_MD_size(ECIES_HASHER), inData.size(), inData.size() + (inData.size() % block_length ?
      (block_length - (inData.size() % block_length)) : 0)))) {
      //std::cout << "Unable to allocate a secure_t buffer to hold the  encrypted result." << std::endl;
      return -1;
   }

   // Store the public key portion of the ephemeral key.
   else if (EC_POINT_point2oct(EC_KEY_get0_group(ephemeral.get()),
      EC_KEY_get0_public_key(ephemeral.get()), POINT_CONVERSION_COMPRESSED,
      (unsigned char*)secure_key_data(cryptex), envelope_length,
      NULL) != envelope_length) {
      //std::cout << "An error occurred while trying to record the public portion of the envelope key. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      secure_free(cryptex);
      return -1;
   }

   // For now we use an empty initialization vector.
   memset(iv, 0, EVP_MAX_IV_LENGTH);

   // Setup the cipher context, the body length, and store a pointer to the body buffer location.
   openssl_unique_ptr<EVP_CIPHER_CTX> cipher(EVP_CIPHER_CTX_new());
   EVP_CIPHER_CTX_init(cipher.get());
   body = (unsigned char*)secure_body_data(cryptex);
   body_length = secure_body_length(cryptex);

   // Initialize the cipher with the envelope key.
   if (EVP_EncryptInit_ex(cipher.get(), ECIES_CIPHER, NULL, envelope_key, iv) != 1 ||
       EVP_CIPHER_CTX_set_padding(cipher.get(), 0) != 1 ||
       EVP_EncryptUpdate(cipher.get(), (unsigned char*)body, &body_length, (const unsigned char*)inData.data(), inData.size() - (inData.size() % block_length)) != 1) {
      //std::cout << "An error occurred while trying to secure the data using the chosen symmetric cipher. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      secure_free(cryptex);
      return -1;
   }

   // Check whether all of the data was encrypted. If they don't match up, we either have a partial block remaining, or an error occurred.
   else if (body_length != inData.size()) {

      // Make sure all that remains is a partial block, and their wasn't an error.
      if (inData.size() - body_length >= block_length) {
         //std::cout << "Unable to secure the data using the chosen symmetric cipher. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
         secure_free(cryptex);
         return -1;
      }

      // Copy the remaining data into our partial block buffer. The memset() call ensures any extra bytes will be zero'ed out.
      memset(block, 0, EVP_MAX_BLOCK_LENGTH);
      memcpy(block, inData.data() + body_length, inData.size() - body_length);

      // Advance the body pointer to the location of the remaining space, and calculate just how much room is still available.
      body += body_length;
      if ((body_length = secure_body_length(cryptex) - body_length) < 0) {
         //std::cout << "The symmetric cipher overflowed!" << std::endl;
         secure_free(cryptex);
         return -1;
      }

      // Pass the final partially filled data block into the cipher as a complete block.The padding will be removed during the decryption process.
      else if (EVP_EncryptUpdate(cipher.get(), body, &body_length, block, block_length) != 1) {
         //std::cout << "Unable to secure the data using the chosen symmetric cipher. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
         secure_free(cryptex);
         return -1;
      }
   }

   // Advance the pointer, then use pointer arithmetic to calculate how  much of the body buffer has been used.The complex logic is needed so that we
   // get the correct status regardless of whether there was a partial data block.
   body += body_length;
   if ((body_length = secure_body_length(cryptex) - (body - (unsigned char*)secure_body_data(cryptex))) < 0) {
      //std::cout << "The symmetric cipher overflowed!" << std::endl;
      secure_free(cryptex);
      return -1;
   }

   else if (EVP_EncryptFinal_ex(cipher.get(), body, &body_length) != 1) {
      //std::cout << "Unable to secure the data using the chosen symmetric cipher. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      secure_free(cryptex);
      return -1;
   }

   // Generate an authenticated hash which can be used to validate the data during decryption.
   openssl_unique_ptr<HMAC_CTX> hmac(HMAC_CTX_new());
   mac_length = secure_mac_length(cryptex);

   // At the moment we are generating the hash using encrypted data. At some point we may want to validate the original text instead.
   if (HMAC_Init_ex(hmac.get(), envelope_key + key_length, key_length, ECIES_HASHER, NULL) != 1) {
      //std::cout << "Unable to generate a data authentication code. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      secure_free(cryptex);
      return -1;
   }
   if (HMAC_Update(hmac.get(), (const unsigned char*)secure_body_data(cryptex), secure_body_length(cryptex)) != 1) {
      //std::cout << "Unable to generate a data authentication code. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      secure_free(cryptex);
      return -1;
   }
   if (HMAC_Final(hmac.get(), (unsigned char*)secure_mac_data(cryptex), &mac_length) != 1) {
      //std::cout << "Unable to generate a data authentication code. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      secure_free(cryptex);
      return -1;
   }

   resip::Data outData(resip::Data::Share, (const char*)cryptex, secure_total_length(cryptex));

   if (base64encode)
   {
      encrypted = Base64Encode(outData);
   }
   else
   {
      encrypted = outData;
   }

   secure_free(cryptex);

   return 0;
}

openssl_shared_ptr<EC_KEY> ecies_key_create_public_octets(unsigned char *octets, size_t length) {
   openssl_shared_ptr<EC_KEY> key(EC_KEY_new());
   if (!key) {
      //std::cout << "EC_KEY_new failed. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      return key;
   }

   openssl_shared_ptr<EC_GROUP> group(ecies_group());
   if (!group) {
      key = NULL;
      return key;
   }

   if (EC_KEY_set_group(key.get(), group.get()) != 1) {
      //std::cout << "EC_KEY_set_group failed. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      key = NULL;
      return key;
   }

   openssl_unique_ptr<EC_POINT> point(EC_POINT_new(group.get()));
   if (!point) {
      //std::cout << "EC_POINT_new failed. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      key = NULL;
      return key;
   }

   if (EC_POINT_oct2point(group.get(), point.get(), octets, length, NULL) != 1) {
      //std::cout << "EC_POINT_oct2point failed. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      key = NULL;
      return key;
   }

   if (EC_KEY_set_public_key(key.get(), point.get()) != 1) {
      //std::cout << "EC_KEY_set_public_key failed. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      key = NULL;
      return key;
   }

   if (EC_KEY_check_key(key.get()) != 1) {
      //std::cout << "EC_KEY_check_key failed. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      key = NULL;
      return key;
   }

   return key;
}

/**
 * adapted from https://www.mail-archive.com/<EMAIL>/msg28042.html
 */
int JwtUtils::Decrypt(const resip::Data& p8file, const resip::Data& encrypted, resip::Data& outData)
{
   size_t key_length;
   int output_length;
   unsigned int mac_length = EVP_MAX_MD_SIZE;
   unsigned char envelope_key[SHA512_DIGEST_LENGTH],
      iv[EVP_MAX_IV_LENGTH], md[EVP_MAX_MD_SIZE], *block, *output;

   secure_t* cryptex = (secure_t*)encrypted.data();
   resip::Data b64decoded;
   if (secure_mac_length(cryptex) != EVP_MD_size(ECIES_HASHER))
   {
      b64decoded = Base64Decode(encrypted);
      cryptex = (secure_t*)b64decoded.data();

      if (secure_total_length(cryptex) > b64decoded.size())
      {
         return -1;
      }
   }

   resip::Data cert = loadCertFromFile(p8file);
   openssl_shared_ptr<EC_KEY> user = createPrivateECkey(cert);
   if (!user)
   {
      return -1;
   }

   openssl_shared_ptr<EC_KEY> ephemeral(nullptr);

   // Make sure we are generating enough key material for the symmetric ciphers.
   if ((key_length = EVP_CIPHER_key_length(ECIES_CIPHER)) * 2 >
      SHA512_DIGEST_LENGTH) {
      //std::cout << "The key derivation method will not produce enough
      //   envelope key material for the chosen ciphers. {envelope = " << SHA512_DIGEST_LENGTH / 8 << " / required =
      //   " << (key_length * 2) / 8 << "}" << std::endl;
      return -1;
   }

   // Create the ephemeral key used specifically for this block of data.
   else if (!(ephemeral =
      ecies_key_create_public_octets((unsigned char*)secure_key_data(cryptex), secure_key_length(cryptex))) || !ephemeral) {
      //std::cout << "An error occurred while trying to recreate the ephemeral key." << std::endl;
      return -1;
   }

   // Use the intersection of the provided keys to generate the envelope
   // data used by the ciphers below.The ecies_key_derivation() function uses
   // SHA 512 to ensure we have a sufficient amount of envelope key
   // material and that the material created is sufficiently secure.
   else if (ECDH_compute_key(envelope_key, SHA512_DIGEST_LENGTH,
      EC_KEY_get0_public_key(ephemeral.get()), user.get(), ecies_key_derivation) !=
      SHA512_DIGEST_LENGTH) {
      //std::cout << "An error occurred while trying to compute the envelope key. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      return -1;
   }

   // Use the authenticated hash of the ciphered data to ensure it was not
   // modified after being encrypted.
   openssl_unique_ptr<HMAC_CTX> hmac(HMAC_CTX_new());

   // At the moment we are generating the hash using encrypted data. At
   // some point we may want to validate the original text instead.
   if (HMAC_Init_ex(hmac.get(), envelope_key + key_length, key_length, ECIES_HASHER, NULL) != 1) {
         //std::cout << "Unable to generate the authentication code needed for validation. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
         return -1;
   }
   if (HMAC_Update(hmac.get(), (const unsigned char*)secure_body_data(cryptex), secure_body_length(cryptex)) != 1) {
      //std::cout << "Unable to generate the authentication code needed for
      //   validation. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      return -1;
   }
   if (HMAC_Final(hmac.get(), md, &mac_length) != 1) {
      //std::cout << "Unable to generate the authentication code needed for
      //   validation. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      return -1;
   }

   // We can use the generated hash to ensure the encrypted data was not
   // altered after being encrypted.
   if (mac_length != secure_mac_length(cryptex) || memcmp(md,
         secure_mac_data(cryptex), mac_length)) {
         //std::cout << "The authentication code was invalid! The ciphered data has been corrupted!" << std::endl;
         return -1;
   }

   // Create a buffer to hold the result.
   output_length = secure_body_length(cryptex);
   if (!(block = output = (unsigned char*)malloc(output_length + 1))) {
      //std::cout << "An error occurred while trying to allocate memory for the decrypted data." << std::endl;
      return -1;
   }

   // For now we use an empty initialization vector. We also clear out the
   // result buffer just to be on the safe side.
   memset(iv, 0, EVP_MAX_IV_LENGTH);
   memset(output, 0, output_length + 1);

   openssl_unique_ptr<EVP_CIPHER_CTX> cipher(EVP_CIPHER_CTX_new());
   EVP_CIPHER_CTX_init(cipher.get());

   // Decrypt the data using the chosen symmetric cipher.
   if (EVP_DecryptInit_ex(cipher.get(), ECIES_CIPHER, NULL, envelope_key, iv) != 1 ||
       EVP_CIPHER_CTX_set_padding(cipher.get(), 0) != 1 ||
       EVP_DecryptUpdate(cipher.get(), block, &output_length, (const unsigned char*)secure_body_data(cryptex), secure_body_length(cryptex)) != 1)
   {
      //std::cout << "Unable to decrypt the data using the chosen symmetric cipher. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      free(output);
      return -1;
   }

   block += output_length;
   if ((output_length = secure_body_length(cryptex) - output_length) != 0)
   {
      //std::cout << "The symmetric cipher failed to properly decrypt the correct amount of data!" << std::endl;
      free(output);
      return -1;
   }

   if (EVP_DecryptFinal_ex(cipher.get(), block, &output_length) != 1) {
      //std::cout << "Unable to decrypt the data using the chosen symmetric cipher. {error = " << ERR_error_string(ERR_get_error(), NULL)) << "}" << std::endl;
      free(output);
      return -1;
   }

   outData.append((const char*)output, secure_orig_length(cryptex));

   free(output);

   return 0;
}

}

}

#endif
