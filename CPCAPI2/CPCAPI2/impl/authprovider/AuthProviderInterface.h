#pragma once

#if !defined(CPCAPI2_AUTH_PROVIDER_INTERFACE_H)
#define CPCAPI2_AUTH_PROVIDER_INTERFACE_H

#include "cpcapi2defs.h"
#include "authprovider/AuthProvider.h"
#include "phone/PhoneModule.h"

namespace resip
{
struct ReadCallbackBase;
}

namespace CPCAPI2
{
class PhoneInterface;

namespace Auth
{


class AuthProviderInterface : public AuthProvider,
                              public PhoneModule
{
public:
   AuthProviderInterface(Phone* phone);
   virtual ~AuthProviderInterface();
   virtual void Release() OVERRIDE;

   PhoneInterface* phoneInterface() const;
   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

private:
   PhoneInterface* mPhone;
};
}
}

#endif // CPCAPI2_AUTH_PROVIDER_INTERFACE_H
