#include "interface/experimental/jsonrpc/StringSerialization.h"
#include "interface/experimental/jsonrpc/OptionalSerialization.h"
#include "interface/experimental/jsonrpc/VectorSerialization.h"
#include "../datatypes/NetworkTransport.h"

#ifndef JSONRPC_CPCAPI2_NETWORKCHANGEMANAGER_INETWORKCHANGEMANAGER_H
#define JSONRPC_CPCAPI2_NETWORKCHANGEMANAGER_INETWORKCHANGEMANAGER_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace NetworkChangeManager {
class INetworkChangeManager
{
public:
    virtual NetworkTransport networkTransport(const int64_t handle) = 0;
}; // class NetworkChangeManager
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace NetworkChangeManager
#endif // JSONRPC_CPCAPI2_NETWORKCHANGEMANAGER_INETWORKCHANGEMANAGER_H
