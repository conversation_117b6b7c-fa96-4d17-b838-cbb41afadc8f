
// bliu: there is already another OPTIONS handler registered
// bliu: http://www.resiprocate.org/Handling_Options_Requests
// bliu: mClientHandleMap is never freed up?
// bliu: tag concatenation
// bliu: handle special "video" tag
// bliu: handle special "Video Share" tag
// bliu: handle duplicate tag when sending both "IP Voice Call" and "IP Video Call" at the same time

#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACCOUNT_MODULE == 1)
#if (CPCAPI2_BRAND_CAPABILITY_DISCOVERY_MODULE == 1)

#include "RcsCapabilityDiscoveryInternal.h"
#include "RcsCapabilityDiscoveryInternalHandler.h"
#include "../util/buffer.h"

#include "cpcapi2utils.h"
#include "../util/ResipConv.h"

#include <resip/dum/ServerOutOfDialogReq.hxx>
#include <resip/dum/ClientOutOfDialogReq.hxx>
#include <resip/stack/OctetContents.hxx>
#include <resip/stack/GenericContents.hxx>
#include <resip/stack/ExtensionParameter.hxx>

#include <utility>
#include <sstream>

#include <boost/tokenizer.hpp>

using namespace resip;

using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::RcsCapabilityDiscovery;

namespace CPCAPI2
{

// Changes made here must be reflected in RcsCapability.java in the Java SDK wrapper
enum RcsCapabilityId
{
   RcsCapabilityId_Unknown = 0,
   RcsCapabilityId_ImageShare = 1,
   RcsCapabilityId_VideoShare = 2,
   RcsCapabilityId_Chat = 3,
   RcsCapabilityId_FullStoreAndForwardGroupChat = 4,
   RcsCapabilityId_FileTransfer = 5,
   RcsCapabilityId_FileTransferThumbnail = 6,
   RcsCapabilityId_FileTransferStoreAndForward = 7,
   RcsCapabilityId_FileTransferViaHTTP = 8,
   RcsCapabilityId_IPBasedStandaloneMessaging = 9,
   RcsCapabilityId_VideoShareOutsideOfAVoiceCall = 10,
   RcsCapabilityId_SocialPresenceInformation = 11,
   RcsCapabilityId_IPVoiceCall = 12,
   RcsCapabilityId_IPVideoCall = 13,
   RcsCapabilityId_RCSIPVoiceCall = 14,
   RcsCapabilityId_RCSIPVideoCall = 15,
   RcsCapabilityId_RCSIPVideoCallOnly = 16,
   RcsCapabilityId_GeolocationPUSH = 17,
   RcsCapabilityId_GeolocationPULL = 18,
   RcsCapabilityId_GeolocationPULLUsingFileTransfer = 19,
   RcsCapabilityId_TotalNumber = 20,
};

static std::map<RcsCapability, cpc::string> _AllTags;
typedef std::map<RcsCapability, cpc::string>::iterator AllTagsIterator;

const cpc::string& RcsCapability::getDescription() const
{
  AllTagsIterator it = _AllTags.find(*this);

   assert(it != _AllTags.end());

   return it->second;
}

inline static const RcsCapability& addCapability(const RcsCapabilityId id, const cpc::string& tag, const cpc::string& description)
{
   RcsCapability cap;
   cap.tag = tag;
   cap.id = id;
   std::pair<AllTagsIterator,bool> insertor = _AllTags.insert(std::make_pair(cap, description));
   assert(insertor.second);
   return insertor.first->first;
}

const RcsCapability RcsCapability::ImageShare                       = addCapability(RcsCapabilityId_ImageShare,"+g.3gpp.iari-ref=\"urn%3Aurn-7%3A3gpp-application.ims.iari.gsma-is\"","Image Share");
const RcsCapability RcsCapability::VideoShare                       = addCapability(RcsCapabilityId_VideoShare,"+g.3gpp.cs-voice","Video Share");

const RcsCapability RcsCapability::Chat                             = addCapability(RcsCapabilityId_Chat,"+g.3gpp.iari-ref=\"urn%3Aurn-7%3A3gpp-application.ims.iari.rcse.im\"","Chat");
const RcsCapability RcsCapability::FullStoreAndForwardGroupChat     = addCapability(RcsCapabilityId_FullStoreAndForwardGroupChat,"+g.3gpp.iari-ref=\"urn%3Aurn-7%3A3gpp-application.ims.iari.rcs.fullsfgroupchat\"","Full Store and Forward Group Chat");
const RcsCapability RcsCapability::FileTransfer                     = addCapability(RcsCapabilityId_FileTransfer,"+g.3gpp.iari-ref=\"urn%3Aurn-7%3A3gpp-application.ims.iari.rcse.ft\"","File Transfer");
const RcsCapability RcsCapability::FileTransferThumbnail            = addCapability(RcsCapabilityId_FileTransferThumbnail,"+g.3gpp.iari-ref=\"urn%3Aurn-7%3A3gpp-application.ims.iari.rcs.ftthumb\"","File Transfer Thumbnail");
const RcsCapability RcsCapability::FileTransferStoreAndForward      = addCapability(RcsCapabilityId_FileTransferStoreAndForward,"+g.3gpp.iari-ref=\"urn%3Aurn-7%3A3gpp-application.ims.iari.rcs.ftsandfw\"","File Transfer Store and Forward");
const RcsCapability RcsCapability::FileTransferViaHTTP              = addCapability(RcsCapabilityId_FileTransferViaHTTP,"+g.3gpp.iari-ref=\"urn%3Aurn-7%3A3gpp-application.ims.iari.rcs.fthttp\"","File Transfer via HTTP");

const RcsCapability RcsCapability::IPBasedStandaloneMessaging       = addCapability(RcsCapabilityId_IPBasedStandaloneMessaging,"+g.3gpp.icsi-ref=\"urn%3Aurn-7%3A3gpp-service.ims.icsi.oma.cpm.msg;urn%3Aurn-7%3A3gpp-service.ims.icsi.oma.cpm.largemsg\"","IP Based Standalone messaging");

const RcsCapability RcsCapability::VideoShareOutsideOfAVoiceCall    = addCapability(RcsCapabilityId_VideoShareOutsideOfAVoiceCall,"+g.3gpp.iari-ref=\"urn%3Aurn-7%3A3gpp-application.ims.iari.gsma-vs\"","Video Share outside of a voice call");

const RcsCapability RcsCapability::SocialPresenceInformation        = addCapability(RcsCapabilityId_SocialPresenceInformation,"+g.3gpp.iari-ref=\"urn%3Aurn-7%3A3gpp-application.ims.iari.rcse.sp\"","Social presence information");

const RcsCapability RcsCapability::IPVoiceCall                      = addCapability(RcsCapabilityId_IPVoiceCall,"+g.3gpp.icsi-ref=\"urn%3Aurn-7%3A3gpp-service.ims.icsi.mmtel\"","IP Voice Call (as per MMTEL)");
const RcsCapability RcsCapability::IPVideoCall                      = addCapability(RcsCapabilityId_IPVideoCall,"+g.3gpp.icsi-ref=\"urn%3Aurn-7%3A3gpp-service.ims.icsi.mmtel\";video","IP Video Call (as per MMTEL)");
const RcsCapability RcsCapability::RCSIPVoiceCall                   = addCapability(RcsCapabilityId_RCSIPVoiceCall,"+g.gsma.rcs.ipcall","RCS IP Voice Call");
const RcsCapability RcsCapability::RCSIPVideoCall                   = addCapability(RcsCapabilityId_RCSIPVideoCall,"+g.gsma.rcs.ipcall;video","RCS IP Video Call");
const RcsCapability RcsCapability::RCSIPVideoCallOnly               = addCapability(RcsCapabilityId_RCSIPVideoCallOnly,"+g.gsma.rcs.ipvideocallonly","RCS IP Video Call where video media cannot be removed");

const RcsCapability RcsCapability::GeolocationPUSH                  = addCapability(RcsCapabilityId_GeolocationPUSH,"+g.3gpp.iari-ref=\"urn%3Aurn-7%3A3gpp-application.ims.iari.rcs.geopush\"","Geolocation PUSH");
const RcsCapability RcsCapability::GeolocationPULL                  = addCapability(RcsCapabilityId_GeolocationPULL,"+g.3gpp.iari-ref=\"urn%3Aurn-7%3A3gpp-application.ims.iari.rcs.geopull\"","Geolocation PULL");
const RcsCapability RcsCapability::GeolocationPULLUsingFileTransfer = addCapability(RcsCapabilityId_GeolocationPULLUsingFileTransfer,"+g.3gpp.iari-ref=\"urn%3Aurn-7%3A3gpp-application.ims.iari.rcs.geopullft\"","Geolocation PULL using File Transfer");

const int RcsCapabilityDiscoveryManager::kCompletedSynchronously    = 0;
const int RcsCapabilityDiscoveryManager::kCompletedAsynchronously   = 1;

namespace RcsCapabilityDiscovery
{

int RcsCapability::getById(unsigned int id, RcsCapability& capability)
{
   for (AllTagsIterator it = _AllTags.begin(); it != _AllTags.end(); ++it)
   {
      const AllTagsIterator::value_type& item = *it;

      if (item.first.id == id)
      {
         capability = item.first;
         return kSuccess;
      }
   }
   return kError;
}

static bool _MatchTag(const std::string& tag, std::string* key = NULL, std::string* value = NULL, std::string* trail = NULL)
{
   size_t equalSign = tag.find('=');

   if (equalSign == tag.npos)
   {
      if (key)
      {
         *key = tag;
      }
      return true;
   }

   if (key)
   {
      *key = tag.substr(0, equalSign - 0);
   }

   if (equalSign == tag.length() - 1) return true; // nothing after =

   size_t openQuote = tag.find('"', equalSign + 1);

   if (openQuote != equalSign + 1) return false; // not an immediate " or not found at all

   if (openQuote == tag.length() - 1) return false; // only open " but no close "

   size_t closeQuote = tag.find('"', openQuote + 1);

   if (closeQuote == tag.npos) return false; // only open " but no close "

   if (value)
   {
      *value = tag.substr(openQuote + 1, closeQuote - openQuote - 1);
   }

   if (closeQuote == tag.length() - 1) return true; // no trail at all

   size_t comma = tag.find(';', closeQuote + 1);

   if (comma != closeQuote + 1) return false; // not an immediate ; or not found at all

   if (trail)
   {
      *trail = tag.substr(comma + 1);
   }

   return true;
}

static const std::string _caps2string(
   const RcsCapabilitySet& caps)
{
   std::map<std::string, std::string> namedValues;

   for (RcsCapabilitySet::const_iterator it_caps = caps.begin(); it_caps != caps.end(); ++it_caps)
   {
      const RcsCapability& cap = *it_caps;
      AllTagsIterator it = _AllTags.find(cap);
      assert(it != _AllTags.end());

      std::string tag = std::string(cap.tag.c_str());
      std::string key, value, trail;

      if (!_MatchTag(tag, &key, &value, &trail))
      {
         std::cerr << "Invalid capability tag '" + cap.tag + "'" << std::endl;
         assert(false);
         continue;
      }

      // if the named value doesn't exist, just do insertion; otherwise, do appending
      // handle the special case for "Video Share"
      std::pair<std::map<std::string, std::string>::iterator, bool> insertor = namedValues.insert(std::make_pair(key, value));
      if (!insertor.second)
      {
         std::string& old_value = insertor.first->second;
         if (old_value.find(value) == std::string::npos) // if the value already exists, omit appending
         {
            old_value += ("," + value);
         }
      }

      // handle the trail value like "video"
      if (!trail.empty())
      {
         namedValues.insert(std::make_pair(trail, ""));
      }
   }

   std::ostringstream oss;

   for (std::map<std::string, std::string>::iterator it = namedValues.begin(); it != namedValues.end(); ++it)
   {
      const std::map<std::string, std::string>::value_type& item = *it;

      // handle the special case for "video"
      if (item.second.empty())
      {
         oss << ";" << item.first;
         continue;
      }

      oss << ";" << item.first << "=\"" + item.second + "\"";
   }

   return oss.str();
}

static const RcsCapabilitySet _NameAddr2caps(
   const resip::NameAddrs& params)
{

#define addCap(cap)\
{\
   if (std::find(caps.begin(), caps.end(), cap) == caps.end()) caps.push_back(cap);\
}

   RcsCapabilitySet caps;

   for (resip::NameAddrs::const_iterator it_params = params.begin(); it_params != params.end(); ++it_params)
   {
      const resip::NameAddr& param = *it_params;

      for (AllTagsIterator it = _AllTags.begin(); it != _AllTags.end(); ++it)
      {
         AllTagsIterator::value_type& item = *it;
         std::string tag = std::string(item.first.tag.c_str());
         std::string key;

         if (!_MatchTag(tag, &key))
         {
            std::cerr << "Invalid capability mapping '" + item.first.tag + "'" << std::endl;
            assert(false);
            continue;
         }

         resip::ExtensionParameter ext (key.c_str());

         if (!param.exists(ext)) continue;
         std::string value = param.param(ext).c_str();

         boost::tokenizer<boost::char_separator<char> > t (value, boost::char_separator<char>(","));

         for (boost::tokenizer<boost::char_separator<char> >::iterator it_t = t.begin(); it_t != t.end(); ++it_t)
         {
            std::string token = *it_t;

            for (AllTagsIterator it = _AllTags.begin(); it != _AllTags.end(); ++it)
            {
               AllTagsIterator::value_type& item = *it;

               if ((item.first.tag) == cpc::string(key.c_str()) + "=\"" + token.c_str() + "\"")
               {
                  addCap(item.first);
               }
            }
         }

         if (value.empty())
         {
            for (AllTagsIterator it = _AllTags.begin(); it != _AllTags.end(); ++it)
            {
               AllTagsIterator::value_type& item = *it;

               if ((item.first.tag) == key.c_str())
               {
                  addCap(item.first);
               }
            }
         }

         if (param.exists(p_video))
         {
            if (std::find(caps.begin(), caps.end(), RcsCapability::IPVoiceCall) != caps.end()) addCap(RcsCapability::IPVideoCall);
            if (std::find(caps.begin(), caps.end(), RcsCapability::RCSIPVoiceCall) != caps.end()) addCap(RcsCapability::RCSIPVideoCall);
         }
      }
   }

   return caps;
}

RcsCapabilityDiscoveryInternal::RcsCapabilityDiscoveryInternal(SipAccountImpl& account) :
   mAccount(account),
   mCapabilityDiscoveryHandler(NULL),
   mNextHandle(account.getHandle() + 1)
{
   mAccount.registerAccountAwareFeature(this);
}

RcsCapabilityDiscoveryInternal::~RcsCapabilityDiscoveryInternal()
{
   mAccount.unregisterAccountAwareFeature(this);
}

int RcsCapabilityDiscoveryInternal::adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile)
{
   if (!profile->isMethodSupported(resip::OPTIONS)) profile->addSupportedMethod(resip::OPTIONS);

   return kSuccess;
}

int RcsCapabilityDiscoveryInternal::addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig)
{
   dum->addOutOfDialogHandler(OPTIONS, this, false);

   return kSuccess;
}

int RcsCapabilityDiscoveryInternal::onDumBeingDestroyed()
{
   return kSuccess;
}

void RcsCapabilityDiscoveryInternal::release()
{
   mCapabilityDiscoveryHandler->onReleased();
}

int RcsCapabilityDiscoveryInternal::initialize()
{
   return kSuccess;
}

int RcsCapabilityDiscoveryInternal::setHandler(RcsCapabilityDiscoveryInternalHandler* handler)
{
   mCapabilityDiscoveryHandler = handler;

   return kSuccess;
}

void RcsCapabilityDiscoveryInternal::fireError(RcsCapabilityDiscoveryInternalHandle capability, const cpc::string& errorText)
{
   ErrorEvent event;
   event.errorText = errorText;

   mCapabilityDiscoveryHandler->onError(mAccount.getHandle(), event);
}

int RcsCapabilityDiscoveryInternal::sendCapabilityRequest(
      RcsCapabilityDiscoveryInternalHandle capability,
      cpc::string targetAddress,
      const RcsCapabilitySet& caps)
{
   NameAddr naTarget;

   if (!ResipConv::stringToAddr(targetAddress, naTarget))
   {
      fireError(capability, "Invalid targetAddress for outgoing message '" + targetAddress + "'");
      return kError;
   }

   if (!mAccount.isEnabled()) return kError;

   SharedPtr<SipMessage> options = mAccount.getDUM()->makeOutOfDialogRequest(naTarget, OPTIONS);

   resip::Data dataRcsCapsParams(_caps2string(caps));

   // modify both Contacts and Accept-Contacts headers for request
   if (options->exists(h_Contacts))
   {
      resip::Data dummyWithParams;
      {
         resip::DataStream ds(dummyWithParams);
         ds << resip::Data::from(options->header(h_Contacts).front());
         ds << dataRcsCapsParams;
      }
      resip::NameAddr dummyNA(dummyWithParams);      
      options->header(h_Contacts).front() = dummyNA;
   }

   if (options->exists(h_AcceptContacts))
   {
      resip::Data dummyWithParams;
      {
         resip::DataStream ds(dummyWithParams);
         ds << resip::Data::from(options->header(h_AcceptContacts).front());
         ds << dataRcsCapsParams;
      }
      resip::NameAddr dummyNA(dummyWithParams);      
      options->header(h_AcceptContacts).front() = dummyNA;
   }

   mAccount.getDUM()->send(options);

   mClientHandleMap[capability] = options->getTransactionId();

   return kSuccess;
}

int RcsCapabilityDiscoveryInternal::acceptIncoming(
      RcsCapabilityDiscoveryInternalHandle capability,
      unsigned int statusCode,
      const RcsCapabilitySet& caps)
{
   std::map<RcsCapabilityDiscoveryInternalHandle, resip::ServerOutOfDialogReqHandle>::iterator it = mServerHandleMap.find(capability);

   if (it == mServerHandleMap.end()) return kSuccess;

   if (!mAccount.isEnabled()) return kError;

   SharedPtr<SipMessage> options = it->second->answerOptions();

   resip::Data dataRcsCapsParams(_caps2string(caps));

   // only modify Contacts header for response
   if (options->exists(h_Contacts))
   {
      resip::Data dummyWithParams;
      {
         resip::DataStream ds(dummyWithParams);
         ds << resip::Data::from(options->header(h_Contacts).front());
         ds << dataRcsCapsParams;
      }
      resip::NameAddr dummyNA(dummyWithParams);
      options->header(h_Contacts).front() = dummyNA;
   }

   mAccount.getDUM()->send(options);

   mServerHandleMap.erase(it);

   return kSuccess;
}

int RcsCapabilityDiscoveryInternal::rejectIncoming(
   RcsCapabilityDiscoveryInternalHandle capability,
   unsigned int statusCode)
{
   std::map<RcsCapabilityDiscoveryInternalHandle, resip::ServerOutOfDialogReqHandle>::iterator it = mServerHandleMap.find(capability);

   if (it == mServerHandleMap.end()) return kSuccess;

   if (!mAccount.isEnabled()) return kError;

   SharedPtr<SipMessage> options = it->second->reject(statusCode);
   mAccount.getDUM()->send(options);

   mServerHandleMap.erase(it);

   return kSuccess;
}

bool RcsCapabilityDiscoveryInternal::onReceivedRequest(resip::ServerOutOfDialogReqHandle h, const resip::SipMessage& request)
{
   RcsIncomingCapabilityDiscoveryEvent args;
   args.account = mAccount.getHandle();
   args.capability = mNextHandle++;

   args.to.address = (Data::from(request.header(h_To).uri()).c_str());
   args.to.displayName = (Data::from(request.header(h_To).displayName()).c_str());
   args.from.address = (Data::from(request.header(h_From).uri()).c_str());
   args.from.displayName = (Data::from(request.header(h_From).displayName()).c_str());

   // check both Contacts and Accept-Contacts headers for request
   if (request.exists(h_Contacts))
   {
      const resip::NameAddrs&  contacts = request.header(h_Contacts);
      args.caps = _NameAddr2caps(contacts);
   }

   if (args.caps.empty() && request.exists(h_AcceptContacts))
   {
      const resip::NameAddrs&  contacts = request.header(h_AcceptContacts);
      args.caps = _NameAddr2caps(contacts);
   }

   mServerHandleMap[args.capability] = h;

   mCapabilityDiscoveryHandler->onIncomingCapabilityDiscovery(mAccount.getHandle(), args);

   return false;
}

bool RcsCapabilityDiscoveryInternal::onSuccess(ClientOutOfDialogReqHandle h, const SipMessage& successResponse)
{
   RcsOutgoingCapabilityDiscoveryEvent args;
   args.capability = getSdkHandleFrom(successResponse.getTransactionId());
   args.to.address = (Data::from(successResponse.header(h_To).uri()).c_str());
   args.to.displayName = (Data::from(successResponse.header(h_To).displayName()).c_str());
   args.from.address = (Data::from(successResponse.header(h_From).uri()).c_str());
   args.from.displayName = (Data::from(successResponse.header(h_From).displayName()).c_str());

   args.signalingResponseText = (successResponse.header(h_StatusLine).reason().c_str());
   args.signalingStatusCode = successResponse.header(h_StatusLine).responseCode();

   // only check Contacts header for response
   if (successResponse.exists(h_Contacts))
   {
      const resip::NameAddrs& contacts = successResponse.header(h_Contacts);

      args.caps = _NameAddr2caps(contacts);
   }

   mCapabilityDiscoveryHandler->onOutgoingCapabilityDiscoverySuccess(mAccount.getHandle(), args);

   return false;
}

bool RcsCapabilityDiscoveryInternal::onFailure(ClientOutOfDialogReqHandle h, const SipMessage& errorResponse)
{
   RcsOutgoingCapabilityDiscoveryEvent args;
   args.capability = getSdkHandleFrom(errorResponse.getTransactionId());
   args.to.address = (Data::from(errorResponse.header(h_To).uri()).c_str());
   args.to.displayName = (Data::from(errorResponse.header(h_To).displayName()).c_str());
   args.from.address = (Data::from(errorResponse.header(h_From).uri()).c_str());
   args.from.displayName = (Data::from(errorResponse.header(h_From).displayName()).c_str());

   args.signalingResponseText = (errorResponse.header(h_StatusLine).reason().c_str());
   args.signalingStatusCode = errorResponse.header(h_StatusLine).responseCode();

   mCapabilityDiscoveryHandler->onOutgoingCapabilityDiscoveryFailure(mAccount.getHandle(), args);

   return false;
}

RcsCapabilityDiscoveryInternalHandle RcsCapabilityDiscoveryInternal::getSdkHandleFrom(resip::Data transactionId)
{
   for (std::map<RcsCapabilityDiscoveryInternalHandle, resip::Data>::iterator it = mClientHandleMap.begin(); it != mClientHandleMap.end(); ++it)
   {
      std::map<RcsCapabilityDiscoveryInternalHandle, resip::Data>::value_type& item = *it;

      if (item.second == transactionId)
         return item.first;
   }

   return 0xffffffff;
}

}
}
// CPCAPI2_CAPABILITY_DISCOVERY_MODULE

#endif
#endif
