#include "ISipConversation.h"
#include "impl/jsonrpc/IJsonRpcServerModuleProcessing.h"

#ifndef JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSERVER_H
#define JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSERVER_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipConversation {
class SipConversationServer
{
public:
    SipConversationServer(::CPCAPI2::IJsonRpcServerModuleProcessing& transport, ISipConversation& impl) :
        mTransport(transport),
        mImpl(impl)
    {
        mTransport.registerFunction("SipConversation::setDefaultSettings", jsonrpccxx::methodHandle(&ISipConversation::setDefaultSettings, mImpl), {{ "phoneHandle" }, { "accountHandle" }, { "settings" }});
	mTransport.registerFunction("SipConversation::createConversation", jsonrpccxx::methodHandle(&ISipConversation::createConversation, mImpl), {{ "phoneHandle" }, { "accountHandle" }});
	mTransport.registerFunction("SipConversation::addParticipant", jsonrpccxx::methodHandle(&ISipConversation::addParticipant, mImpl), {{ "phoneHandle" }, { "conversationHandle" }, { "targetAddress" }});
	mTransport.registerFunction("SipConversation::configureMedia", jsonrpccxx::methodHandle(&ISipConversation::configureMedia, mImpl), {{ "phoneHandle" }, { "conversationHandle" }, { "mediaDescriptor" }});
	mTransport.registerFunction("SipConversation::setMediaEnabled", jsonrpccxx::methodHandle(&ISipConversation::setMediaEnabled, mImpl), {{ "phoneHandle" }, { "conversationHandle" }, { "mediaType" }, { "enabled" }});
	mTransport.registerFunction("SipConversation::setAnonymousMode", jsonrpccxx::methodHandle(&ISipConversation::setAnonymousMode, mImpl), {{ "phoneHandle" }, { "conversationHandle" }, { "anonymousMode" }});
	mTransport.registerFunction("SipConversation::start", jsonrpccxx::methodHandle(&ISipConversation::start, mImpl), {{ "phoneHandle" }, { "conversationHandle" }});
	mTransport.registerFunction("SipConversation::hold", jsonrpccxx::methodHandle(&ISipConversation::hold, mImpl), {{ "phoneHandle" }, { "conversationHandle" }});
	mTransport.registerFunction("SipConversation::unhold", jsonrpccxx::methodHandle(&ISipConversation::unhold, mImpl), {{ "phoneHandle" }, { "conversationHandle" }});
	mTransport.registerFunction("SipConversation::sendMediaChangeRequest", jsonrpccxx::methodHandle(&ISipConversation::sendMediaChangeRequest, mImpl), {{ "phoneHandle" }, { "conversationHandle" }});
	mTransport.registerFunction("SipConversation::end", jsonrpccxx::methodHandle(&ISipConversation::end, mImpl), {{ "phoneHandle" }, { "conversationHandle" }});
	mTransport.registerFunction("SipConversation::redirect", jsonrpccxx::methodHandle(&ISipConversation::redirect, mImpl), {{ "phoneHandle" }, { "conversationHandle" }, { "targetAddress" }, { "reason" }});
	mTransport.registerFunction("SipConversation::sendRingingResponse", jsonrpccxx::methodHandle(&ISipConversation::sendRingingResponse, mImpl), {{ "phoneHandle" }, { "conversationHandle" }});
	mTransport.registerFunction("SipConversation::reject", jsonrpccxx::methodHandle(&ISipConversation::reject, mImpl), {{ "phoneHandle" }, { "conversationHandle" }, { "rejectReason" }});
	mTransport.registerFunction("SipConversation::accept", jsonrpccxx::methodHandle(&ISipConversation::accept, mImpl), {{ "phoneHandle" }, { "conversationHandle" }});
	mTransport.registerFunction("SipConversation::acceptIncomingTransferRequest", jsonrpccxx::methodHandle(&ISipConversation::acceptIncomingTransferRequest, mImpl), {{ "phoneHandle" }, { "conversationHandle" }});
	mTransport.registerFunction("SipConversation::rejectIncomingTransferRequest", jsonrpccxx::methodHandle(&ISipConversation::rejectIncomingTransferRequest, mImpl), {{ "phoneHandle" }, { "conversationHandle" }});
	mTransport.registerFunction("SipConversation::transfer", jsonrpccxx::methodHandle(&ISipConversation::transfer, mImpl), {{ "phoneHandle" }, { "transferTargetConversation" }, { "transfereeConversation" }});
	mTransport.registerFunction("SipConversation::transferWithFlag", jsonrpccxx::methodHandle(&ISipConversation::transferWithFlag, mImpl), {{ "phoneHandle" }, { "transferTargetConversation" }, { "transfereeConversation" }, { "endTargetConversationOnSuccess" }});
	mTransport.registerFunction("SipConversation::transferToAddress", jsonrpccxx::methodHandle(&ISipConversation::transferToAddress, mImpl), {{ "phoneHandle" }, { "transfereeConversation" }, { "targetAddress" }});
	mTransport.registerFunction("SipConversation::setDtmfMode", jsonrpccxx::methodHandle(&ISipConversation::setDtmfMode, mImpl), {{ "phoneHandle" }, { "account" }, { "ordinal" }, { "dtmfMode" }});
	mTransport.registerFunction("SipConversation::startDtmfTone", jsonrpccxx::methodHandle(&ISipConversation::startDtmfTone, mImpl), {{ "phoneHandle" }, { "conversationHandle" }, { "toneId" }, { "playLocally" }});
	mTransport.registerFunction("SipConversation::stopDtmfTone", jsonrpccxx::methodHandle(&ISipConversation::stopDtmfTone, mImpl), {{ "phoneHandle" }});
	mTransport.registerFunction("SipConversation::playSound", jsonrpccxx::methodHandle(&ISipConversation::playSound, mImpl), {{ "phoneHandle" }, { "conversationHandle" }, { "resourceUri" }, { "repeat" }});
	mTransport.registerFunction("SipConversation::stopPlaySound", jsonrpccxx::methodHandle(&ISipConversation::stopPlaySound, mImpl), {{ "phoneHandle" }, { "conversationHandle" }});
	mTransport.registerFunction("SipConversation::getCallCount", jsonrpccxx::methodHandle(&ISipConversation::getCallCount, mImpl), {{ "phoneHandle" }});
	mTransport.registerFunction("SipConversation::setCallKitMode", jsonrpccxx::methodHandle(&ISipConversation::setCallKitMode, mImpl), {{ "phoneHandle" }, { "conversationHandle" }});
	mTransport.registerFunction("SipConversation::setTelecomFrameworkMode", jsonrpccxx::methodHandle(&ISipConversation::setTelecomFrameworkMode, mImpl), {{ "phoneHandle" }, { "conversationHandle" }});
    }

private:
    ::CPCAPI2::IJsonRpcServerModuleProcessing& mTransport;
    ISipConversation& mImpl;
}; // class SipConversationServer
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipConversation
#endif // JSONRPC_CPCAPI2_SIPCONVERSATION_SIPCONVERSATIONSERVER_H
