#include "SopiHelper.h"

#if (CPCAPI2_BRAND_GENBAND_SOPI_MODULE == 1)

#if defined(WinRT)
#include <resip/stack/ssl/counterpath/XWinRTSecurity.hxx>
#elif _WIN32
#include <resip/stack/ssl/counterpath/XWinSecurity.hxx>
#elif ANDROID
#include <resip/stack/ssl/counterpath/AndroidSecurity.hxx>
#elif BB10
#include <resip/stack/ssl/counterpath/BlackberrySecurity.hxx>
#elif __APPLE__
#include "resip/stack/ssl/IOSSecurity.hxx"
#elif __THREADX
#include <resip/stack/ssl/counterpath/ThreadXSecurity.hxx>
#endif

#ifdef ANDROID
#include <android/log.h>
#endif

namespace CPCAPI2
{
namespace GenbandSopi
{

void
SopiHelper::makePABEntry(const AddressBookEntry& entryIn, ns3__AddressBookEntryDO& pabEntryOut)
{
   pabEntryOut.buddy = entryIn.buddy;
   pabEntryOut.name = toStringPointer(entryIn.nickName);
   pabEntryOut.firstName = toStringPointer(entryIn.firstName);
   pabEntryOut.lastName = toStringPointer(entryIn.lastName);
   pabEntryOut.emailAddress = toStringPointer(entryIn.email);
   pabEntryOut.homePhoneNumber = toStringPointer(entryIn.home);
   pabEntryOut.mobile =toStringPointer(entryIn.mobile);
   pabEntryOut.businessPhoneNumber = toStringPointer(entryIn.business);
   pabEntryOut.pager = toStringPointer(entryIn.pager);
   pabEntryOut.fax = toStringPointer(entryIn.fax);
   pabEntryOut.primaryContact = toStringPointer(entryIn.primaryContact);
   pabEntryOut.photoURL = toStringPointer(entryIn.photoURL);
   pabEntryOut.group->name = toStringPointer(entryIn.group);
}

void
SopiHelper::freePABEntry(ns3__AddressBookEntryDO& pabEntry)
{
   delete pabEntry.name;
   delete pabEntry.firstName;
   delete pabEntry.lastName;
   delete pabEntry.emailAddress;
   delete pabEntry.homePhoneNumber;
   delete pabEntry.mobile;
   delete pabEntry.businessPhoneNumber;
   delete pabEntry.pager;
   delete pabEntry.fax;
   delete pabEntry.primaryContact;
   delete pabEntry.photoURL;
   delete pabEntry.group->name;
}

void
SopiHelper::copyPABEntry(ns3__AddressBookEntryDO pabEntryIn, AddressBookEntry& entryOut)
{
   entryOut.buddy = pabEntryIn.buddy;
   entryOut.nickName = fromStringPointer(pabEntryIn.name);
   entryOut.firstName = fromStringPointer(pabEntryIn.firstName);
   entryOut.lastName = fromStringPointer(pabEntryIn.lastName);
   entryOut.email = fromStringPointer(pabEntryIn.emailAddress);
   entryOut.home = fromStringPointer(pabEntryIn.homePhoneNumber);
   entryOut.mobile = fromStringPointer(pabEntryIn.mobile);
   entryOut.business = fromStringPointer(pabEntryIn.businessPhoneNumber);
   entryOut.pager = fromStringPointer(pabEntryIn.pager);
   entryOut.fax = fromStringPointer(pabEntryIn.fax);
   entryOut.primaryContact = fromStringPointer(pabEntryIn.primaryContact);
   entryOut.photoURL = fromStringPointer(pabEntryIn.photoURL);
   entryOut.group = pabEntryIn.group ? fromStringPointer(pabEntryIn.group->name)  : "";
}

void
SopiHelper::copyPABEntryArr(ArrayOfAddressBookEntryDO pabEntryArr, cpc::vector<AddressBookEntry>& entryVecOut)
{
   for (int i = 0; i < pabEntryArr.__size; i++)
   {
      AddressBookEntry entry;
      copyPABEntry(*pabEntryArr.__ptr[i], entry);
      entryVecOut.push_back(entry);
   }
}
/*
void
SopiHelper::copyPABGroupsArr(ArrayOfAddrBookGroupDO pabGroupArr, cpc::vector<cpc::string>& grpsVecOut)
{
   for (int i = 0; i < pabGroupArr.__size; i++)
   {
      if (pabGroupArr.__ptr[i]->name)
      {
         grpsVecOut.push_back(pabGroupArr.__ptr[i]->name->c_str());
      }
   }
}
*/
void
SopiHelper::copyGABEntry(ns4__GABEntryDO gabEntryIn, AddressBookEntry& entryOut)
{
   entryOut.buddy = gabEntryIn.buddy;
   entryOut.nickName = fromStringPointer(gabEntryIn.name);
   entryOut.firstName = fromStringPointer(gabEntryIn.firstName);
   entryOut.lastName = fromStringPointer(gabEntryIn.lastName);
   entryOut.email = fromStringPointer(gabEntryIn.emailAddress);
   entryOut.home = fromStringPointer(gabEntryIn.homePhoneNumber);
   entryOut.mobile = fromStringPointer(gabEntryIn.mobile);
   entryOut.business = fromStringPointer(gabEntryIn.businessPhoneNumber);
   entryOut.pager = fromStringPointer(gabEntryIn.pager);
   entryOut.fax = fromStringPointer(gabEntryIn.fax);
   entryOut.primaryContact = fromStringPointer(gabEntryIn.primaryContact);
   entryOut.photoURL = fromStringPointer(gabEntryIn.photoURL);
}

void
SopiHelper::copyGABEntryArr(ArrayOfGABEntryDO gabEntryArrIn, cpc::vector<AddressBookEntry>& addrBookVecOut)
{
   for (int i = 0; i < gabEntryArrIn.__size; i++)
   {
      AddressBookEntry entry;
      copyGABEntry(*gabEntryArrIn.__ptr[i], entry);
      addrBookVecOut.push_back(entry);
   }
}

void
SopiHelper::copyPAUEntryArr(ArrayOfPresenceAuthorizationUserDO pauEntryArrIn, cpc::vector<PresenceAuthorizationEntry>& presenceAuthVecOut)
{
   for (int i = 0; i < pauEntryArrIn.__size; i++)
   {
      PresenceAuthorizationEntry entry;
	  entry.userName = fromStringPointer(pauEntryArrIn.__ptr[i]->name);
      presenceAuthVecOut.push_back(entry);
   }
}

void
SopiHelper::loadRootSSLCerts(SSL_CTX* ctx)
{
   X509_STORE* rootSSLCerts = 0;
   resip::Data pathToCerts = "";
   unsigned short usCertStorageType = resip::CERT_OS_SPECIFIC_STORAGE;
#if defined(WinRT)
   resip::Security* sec = new resip::XWinRTSecurity(pathToCerts, usCertStorageType);
#elif _WIN32
   resip::Security* sec = new resip::XWinSecurity(pathToCerts, usCertStorageType);
#elif __APPLE__
   resip::Security* sec = new resip::IOSSecurity(pathToCerts, usCertStorageType);
#elif __linux__
#if ANDROID
   resip::Security* sec = new resip::AndroidSecurity(pathToCerts);
#else
   resip::Security* sec = new resip::LinuxSecurity(pathToCerts);
#endif
#elif BB10
   resip::Security* sec = new resip::BlackberrySecurity(pathToCerts);
#else
   assert(0);
#endif

   //load all the certificates
   sec->preload();
   SSL_CTX *sslCtx = sec->getSslCtx();
   if(sslCtx)
   {
      rootSSLCerts = SSL_CTX_get_cert_store(sslCtx);
#if defined OPENSSL_VERSION_NUMBER && ( OPENSSL_VERSION_NUMBER < 0x10100000 )
      // hack, we only need the certifcates from the cert store, so set the reference to null, to avoid destruction
      sslCtx->cert_store = 0;
#else
      X509_STORE_up_ref(rootSSLCerts);
#endif
   }
   else
   {   //TODO: log error
      return;
   }

   delete sec;

   if(rootSSLCerts)
   {
      SSL_CTX_set_cert_store(ctx, rootSSLCerts);
   }
   else
   {  //TODO: log error
   }       
}

cpc::string
SopiHelper::fromStringPointer(std::string* pStr)
{
   return pStr ? pStr->c_str() : "";
}

std::string*
SopiHelper::toStringPointer(const cpc::string& input)
{
   return new std::string(input);
}

}
}
#endif
