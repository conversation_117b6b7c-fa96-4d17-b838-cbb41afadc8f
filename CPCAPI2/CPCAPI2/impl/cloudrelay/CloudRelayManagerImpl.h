#pragma once

#if !defined(CPCAPI2_CloudRelay_MANAGER_IMPL_H)
#define CPCAPI2_CloudRelay_MANAGER_IMPL_H

#include "cpcapi2defs.h"
#include "CloudRelayManagerInterface.h"
#include "CloudRelaySyncHandler.h"
#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"
#include "../util/AutoTestProcessor.h"

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>

#include <map>

namespace CPCAPI2
{
namespace CloudRelay
{
class CloudRelayManagerImpl
{
public:
   CloudRelayManagerImpl(CloudRelayHandle h, CPCAPI2::Phone* phone, CloudRelayManagerInterface* confBridgeMgrIf);
   virtual ~CloudRelayManagerImpl();

   void setSdkObservers(const std::set<CloudRelayHandler*>* observers);

   void broadcast(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const cpc::string& msg);
   void sendTo(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const cpc::string& msg);

   CloudRelayHandle getHandle() const {
      return mHandle;
   }

   // CloudRelayManager
   int setHandler(CloudRelayHandler* handler);

   template<typename TFn, typename TEvt> void fireEvent(const char* funcName, TFn func, const TEvt& args, bool internalOnly = false, bool logging = true)
   {
      for (std::set<CloudRelayHandler*>::iterator itHandler = mSdkObservers->begin(); itHandler != mSdkObservers->end(); ++itHandler)
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, *itHandler, mHandle, args);
         if (dynamic_cast<CloudRelaySyncHandler*>(*itHandler) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }

      //if (logging)
      //   logEvent(funcName, h);

      if (!internalOnly)
      {
         resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, mAppHandler, mHandle, args);
         if (mAppHandler != (void*)0xDEADBEEF && dynamic_cast<CloudRelaySyncHandler*>(mAppHandler) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }
   }

private:
   void postCallback(resip::ReadCallbackBase* rcb);

private:
   CloudRelayHandle mHandle;
   CPCAPI2::Phone* mPhone;
   CloudRelayManagerInterface* mConfBridgeMgrIf;
   bool mShutdown;
   const std::set<CloudRelayHandler*>* mSdkObservers;
   CloudRelayHandler* mAppHandler;

};



}
}

#endif // CPCAPI2_CloudRelay_MANAGER_IMPL_H
