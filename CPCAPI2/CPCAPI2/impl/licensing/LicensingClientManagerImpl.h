#pragma once

#if !defined(__CPCAPI2_LICENSING_CLIENT_MANAGER_IMPL_H__)
#define __CPCAPI2_LICENSING_CLIENT_MANAGER_IMPL_H__

#include "cpcapi2defs.h"
#include "LicensingClientInfo.h"
#include "licensing/LicensingClientHandler.h"
#include "LicensingClientManagerInterface.h"

namespace CPCAPI2
{
namespace Licensing
{

class LicensingClientManagerImpl : public LicensingHandler
{
public:
   LicensingClientManagerImpl(LicensingClientHandle handle, LicensingClientManagerInterface &licensingClientManagerInterface);
   virtual ~LicensingClientManagerImpl();

   LicensingClientInfo* createLicensingClientInfo(LicensingClientHandle client);
   LicensingClientInfo* getLicensingClientInfo(LicensingClientHandle client) const;
   LicensingClientInfo* removeLicensingClientInfo(LicensingClientHandle client);
   void setHandler(LicensingClientHandler* handler);
   void validateLicenses(LicensingClientInfo* clientInfo);
   void destroy(LicensingClientInfo *clientInfo);
   void addSdkObserver(LicensingClientHandler* sdkObserver);
   void removeSdkObserver(LicensingClientHandler* sdkObserver);
   void fireError(LicensingClientHandle client, const cpc::string& msg);
   static bool ensureValidateAttempted();
   static void resetValidateAttempted();
   static bool appResourceSignatureCheckEnabled();

private:
   LicensingClientHandle mHandle;
   LicensingClientManagerInterface& mLicensingClientManagerInterface;
   LicensingClientHandler* mHandler;
   LicensingClientInfoMap mLicensingClientInfoMap;
   std::set<LicensingClientHandler*> mSdkObservers;
   static bool sValidateAttempted;

   // LicensingHandler
   void OnLicenseState(const CPCAPI2::Licensing::LicenseStateEvent &);

   void fireValidateLicensesSuccess(LicensingClientHandle client, const ValidateLicensesSuccessEvent& args);
   void fireValidateLicensesFailure(LicensingClientHandle client, const ValidateLicensesFailureEvent& args);
   void fireError(LicensingClientHandle client, const ErrorEvent& event);

   static ValidateLicensesSuccessEvent licenseStateEventToValidateLicensesSuccessEvent(const LicenseStateEvent& event);
   static ValidateLicensesFailureEvent licenseStateEventToValidateLicensesFailureEvent(const LicenseStateEvent& event);
};

}
}

#endif // __CPCAPI2_LICENSING_CLIENT_MANAGER_IMPL_H__
