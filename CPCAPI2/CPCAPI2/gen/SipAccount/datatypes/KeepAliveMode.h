#include <iostream>
#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPACCOUNT_KEEPALIVEMODE_H
#define JSONRPC_CPCAPI2_SIPACCOUNT_KEEPALIVEMODE_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipAccount {
  enum class KeepAliveMode
  {
    Default,
    NoKeepAlives,
    Crlfcrlf
  };

  inline std::string to_string(KeepAliveMode value)
  {
    switch (value)
    {
      case KeepAliveMode::Default:
        return "Default";
      case KeepAliveMode::NoKeepAlives:
        return "NoKeepAlives";
      case KeepAliveMode::Crlfcrlf:
        return "CRLFCRLF";
      default:
        throw std::invalid_argument("Invalid enum value");
    }
  }

  inline std::ostream& operator<<(std::ostream& os, KeepAliveMode value)
  {
    switch (value)
    {
      case KeepAliveMode::Default:
        os << "Default";
        break;
      case KeepAliveMode::NoKeepAlives:
        os << "NoKeepAlives";
        break;
      case KeepAliveMode::Crlfcrlf:
        os << "CRLFCRLF";
        break;
      default:
        os.setstate(std::ios_base::failbit);
    }
    return os;
  }

  inline void to_json(nlohmann::json& j, const KeepAliveMode& e)
  {
    switch (e)
    {
      case KeepAliveMode::Default:
        j = "Default";
        break;
      case KeepAliveMode::NoKeepAlives:
        j = "NoKeepAlives";
        break;
      case KeepAliveMode::Crlfcrlf:
        j = "CRLFCRLF";
        break;
      default:
        throw std::invalid_argument("Invalid enum value");
    };
  }

  inline void from_json(const nlohmann::json& j, KeepAliveMode& e)
  {
      if (0 == j.get<std::string>().compare("Default"))
    {
      e = KeepAliveMode::Default;
    }
    else if (0 == j.get<std::string>().compare("NoKeepAlives"))
    {
      e = KeepAliveMode::NoKeepAlives;
    }
    else if (0 == j.get<std::string>().compare("CRLFCRLF"))
    {
      e = KeepAliveMode::Crlfcrlf;
    }
    else
    {
      throw std::invalid_argument(std::string("Invalid string ").append(j.get<std::string>()).append(" for enum KeepAliveMode"));
    }
  }
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipAccount
#endif // JSONRPC_CPCAPI2_SIPACCOUNT_KEEPALIVEMODE_H
