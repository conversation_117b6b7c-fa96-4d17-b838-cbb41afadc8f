#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_EVENT_MODULE == 1)
#include "SipEventPublicationManagerImpl.h"
#include "event/SipEventPublicationHandler.h"
#include "cpcapi2utils.h"
#include "../util/buffer.h"
#include <resip/dum/ClientPublication.hxx>
#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_EVENT

using namespace CPCAPI2::SipAccount;
using namespace resip;

namespace CPCAPI2
{
namespace SipEvent
{

SipEventPublicationManagerImpl::SipEventPublicationManagerImpl(std::shared_ptr<AccountMap> parentMap, SipAccountImpl& account, CPCAPI2::SipEvent::SipEventPublicationManagerInterface* sipEventIf) :
mSipEventIf(sipEventIf),
mAccount(account),
mParentMap(parentMap)
{
   mAccount.registerAccountAwareFeature(this);
}

SipEventPublicationManagerImpl::~SipEventPublicationManagerImpl()
{
   mAccount.unregisterAccountAwareFeature(this);
}

void SipEventPublicationManagerImpl::addEventType(const std::string& eventType)
{
   if (mEventTypes.end() == mEventTypes.find(eventType.c_str()))
   {
      mEventTypes.insert(eventType.c_str());
      if (NULL != mDum.get())
      {
         mDum->addClientPublicationHandler(eventType.c_str(), this);

         PublicationManagerReadyEvent args;
         mSipEventIf->fireEvent(cpcEvent(SipEventPublicationHandler, onReady), mAccount.getHandle(), eventType, mAccount.getHandle(), args);
      }
   }
}

///////////////////////////////////////////////////////////////////////////////
//
// SipAccountAwareFeature
//
///////////////////////////////////////////////////////////////////////////////
   
int SipEventPublicationManagerImpl::addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig)
{
   mDum = dum;

   for (auto it = mEventTypes.begin(); mEventTypes.end() != it; ++it)
   {
      dum->addClientPublicationHandler(*it, this);

      PublicationManagerReadyEvent args;
      mSipEventIf->fireEvent(cpcEvent(SipEventPublicationHandler, onReady), mAccount.getHandle(), it->c_str(), mAccount.getHandle(), args);
   }

   return kSuccess;
}

int SipEventPublicationManagerImpl::onDumBeingDestroyed()
{
   InfoLog(<< "SipEventPublicationManagerImpl::onDumBeingDestroyed()");
   if (mDum.get() != NULL) // if account was created but never enabled
   {
      SipEventPublicationCreationInfoMap copy = mCreationInfo;
      SipEventPublicationCreationInfoMap::const_iterator it = copy.begin();
      for (; it != copy.end(); ++it)
      {
         if (it->second != NULL)
         {
            it->second->cleanup();
         }
      }
   }
   mDum.reset();
   mCreationInfo.clear();
   return kSuccess;
}

void SipEventPublicationManagerImpl::release()
{
   if (std::shared_ptr<AccountMap> parentMap = mParentMap.lock())
   {
      parentMap->erase(mAccount.getHandle());
   }

   delete this;
}

int SipEventPublicationManagerImpl::onRegistrationSuccess(const SipRegistrationSuccessEvent& args)
{
   DebugLog(<< "SipEventPublicationManagerImpl::onRegistrationSuccess(): publication list size: " << mCreationInfo.size()
      << " server: " << args.server << " localContact: " << args.localContact << " overrideSourceIpForNAT64: " << args.overrideSourceIpSignalling);

   std::vector<SipEventPublicationHandle> publicationList;
   for (SipEventPublicationCreationInfoMap::iterator it = mCreationInfo.begin(); it != mCreationInfo.end(); it++)
   {
      publicationList.push_back(it->first);
   }

   for (std::vector<SipEventPublicationHandle>::iterator it = publicationList.begin(); it != publicationList.end(); it++)
   {
      SipEventPublicationCreationInfo* ci = getCreationInfo(*it);
      if (!ci)
      {
         DebugLog(<< "SipEventPublicationManagerImpl::onRegistrationSuccess(): error getting creation info for publication handle: " << (*it));
         continue;
      }

      if (!ci->dumClientPublicationHandle.isValid())
      {
         DebugLog(<< "SipEventPublicationManagerImpl::onRegistrationSuccess(): creation info for publication handle: " << (*it) << " has invalid dumClientPublicationHandle");
         continue;
      }

      // Note that call to recreatePublication will change the publication creation info list
      DebugLog(<< "SipEventPublicationManagerImpl::onRegistrationSuccess(): recreate publication handle: " << (*it)
         << " as the contact header may have to be updated " << args.localContact);
      if (!mSipEventIf->recreatePublication(ci->account, ci->sdkh))
      {
         DebugLog(<< "SipEventPublicationManagerImpl::onRegistrationSuccess(): error recreating publication for publication handle: " << (*it));
      }
   }

   return kSuccess;
}

void SipEventPublicationManagerImpl::fireError(const SipEventPublicationHandle& h, const cpc::string& errorText)
{
   PublicationErrorEvent evt;
   evt.errorText = errorText;
   SipEventPublicationCreationInfo* ci = getCreationInfo(h);

   mSipEventIf->fireEvent(cpcEvent(SipEventPublicationHandler, onError), mAccount.getHandle(), ci->eventType.c_str(), h, evt);
}

SipEventPublicationCreationInfo* SipEventPublicationManagerImpl::getCreationInfo(const SipEventPublicationHandle& h) const
{
   SipEventPublicationCreationInfoMap::const_iterator it = mCreationInfo.find(h);
   if (it != mCreationInfo.end())
   {
      return it->second;
   }

   return NULL;
}

void SipEventPublicationManagerImpl::addCreationInfo(const SipEventPublicationHandle& h, SipEventPublicationCreationInfo* ci)
{
   mCreationInfo[h] = ci;
}

void SipEventPublicationManagerImpl::removeCreationInfo(const SipEventPublicationHandle& h)
{
   SipEventPublicationCreationInfoMap::iterator it = mCreationInfo.find(h);
   if (it != mCreationInfo.end())
   {
      DebugLog(<< "SipEventPublicationManagerImpl::removeCreationInfo(): " << this << " removing creation info with publication handle: " << h);
      mCreationInfo.erase(h);
   }
}

void SipEventPublicationManagerImpl::setPublicationSettings( const SipEventPublicationSettings & settings)
{
   mPublicationSettings = settings;
}

SipEventPublicationHandle SipEventPublicationManagerImpl::findSipEventPublicationHandleByResipHandle(resip::ClientPublicationHandle h)
{
   SipEventPublicationCreationInfoMap::iterator it = mCreationInfo.begin();
   for (; it != mCreationInfo.end(); ++it)
   {
      if (it->second->dumClientPublicationHandle == h)
      {
         return it->first;
      }
   }
   return 0xffffffff;
}
   
///////////////////////////////////////////////////////////////////////////////
//
// ClientPublicationHandler
//
///////////////////////////////////////////////////////////////////////////////
void SipEventPublicationManagerImpl::onSuccess(resip::ClientPublicationHandle h, const resip::SipMessage& status)
{
/*
   ClientPublication::AppDialogSet* ads = dynamic_cast<ClientPublication::AppDialogSet*>(h->getAppDialogSet().get());
   if (ads)
   {
      ClientPublicationPtr sess = ads->getSub();
      IClientPublicationSinkPtr sink = sess->GetSink();
      if (sink)
      {
         sink->OnPublicationSuccess(sess, SipMessageAutoPtr(new SipMessage(msg)));
      }
   }
*/
   PublicationSuccessEvent args;

   SipEventPublicationCreationInfo* ci = dynamic_cast<SipEventPublicationCreationInfo*>(h->getAppDialogSet().get());
   if (ci != NULL)
   {
      ci->dumClientPublicationHandle = h;
      mSipEventIf->fireEvent(cpcEvent(SipEventPublicationHandler, onPublicationSuccess), mAccount.getHandle(), ci->eventType.c_str(), ci->sdkh, args);

      if (ci->queuedContents.get() != NULL)
      {
         ci->dumClientPublicationHandle->update(ci->queuedContents.release());
      }
   }
}

void SipEventPublicationManagerImpl::onRemove(resip::ClientPublicationHandle h, const resip::SipMessage& status)
{
/*
   ClientPublication::AppDialogSet* ads = dynamic_cast<ClientPublication::AppDialogSet*>(h->getAppDialogSet().get());
   if (ads)
   {
      ClientPublicationPtr sess = ads->getSub();
      IClientPublicationSinkPtr sink = sess->GetSink();
      if (sink)
      {
         sink->OnPublicationRemoved(sess, SipMessageAutoPtr(new SipMessage(msg)));
      }
   }
*/
   PublicationRemoveEvent args;

   SipEventPublicationCreationInfo* ci = dynamic_cast<SipEventPublicationCreationInfo*>(h->getAppDialogSet().get());
   if (ci != NULL)
   {
      ci->dumClientPublicationHandle = h;
      mSipEventIf->fireEvent(cpcEvent(SipEventPublicationHandler, onPublicationRemove), mAccount.getHandle(), ci->eventType.c_str(), ci->sdkh, args);
   }
   
}
void SipEventPublicationManagerImpl::onFailure(resip::ClientPublicationHandle h, const resip::SipMessage& status)
{
/*
   ClientPublication::AppDialogSet* ads = dynamic_cast<ClientPublication::AppDialogSet*>(h->getAppDialogSet().get());
   if (ads)
   {
      ClientPublicationPtr sess = ads->getSub();
      IClientPublicationSinkPtr sink = sess->GetSink();
      if (sink)
      {
         sink->OnPublicationFailure(sess, SipMessageAutoPtr(new SipMessage(msg)));
      }
   }
*/
   PublicationFailureEvent args;
   int code = status.header(resip::h_StatusLine).statusCode()/100;

   if ( code == 4)   
   {
      // Client error
      args.reason = SipPublicationFailureReason_ConditionalRequestFailed;
   }
   else if( code == 5 ) 
   {
      // Server error
      args.reason = SipPublicationFailureReason_ServerError;
   }
   else
   {
      // unkown/not specified  error
      args.reason = SipPublicationFailureReason_Unknown;
   }

   SipEventPublicationCreationInfo* ci = dynamic_cast<SipEventPublicationCreationInfo*>(h->getAppDialogSet().get());
   if (ci != NULL)
   {
      ci->dumClientPublicationHandle = h;
      mSipEventIf->fireEvent(cpcEvent(SipEventPublicationHandler, onPublicationFailure), mAccount.getHandle(), ci->eventType.c_str(), ci->sdkh, args);
   }
}
int SipEventPublicationManagerImpl::onRequestRetry(resip::ClientPublicationHandle h, int retrySeconds, const resip::SipMessage& status)
{
   // ?jjg? default retry after 1800 seconds???
   return 1800;
}

//////////////////////////////////////////////////////////////////////////////
//
// ServerPublicationHandler 
//
//////////////////////////////////////////////////////////////////////////////
/*
void SipEventPublicationManagerImpl::onInitial(resip::ServerPublicationHandle h,
                           const resip::Data& etag,
                           const resip::SipMessage& pub,
                           const resip::Contents* contents,
                           const resip::SecurityAttributes* attrs,
                           UInt32 expires)
{

}

void SipEventPublicationManagerImpl::onExpired(resip::ServerPublicationHandle h, const resip::Data& etag)
{

}
void SipEventPublicationManagerImpl::onRefresh(resip::ServerPublicationHandle h, const resip::Data& etag,
                           const resip::SipMessage& pub,
                           const resip::Contents* contents,
                           const resip::SecurityAttributes* attrs,
                           UInt32 expires)
{

}
void SipEventPublicationManagerImpl::onUpdate(resip::ServerPublicationHandle h,
                           const resip::Data& etag,
                           const resip::SipMessage& pub,
                           const resip::Contents* contents,
                           const resip::SecurityAttributes* attrs,
                           UInt32 expires)
{

}
void SipEventPublicationManagerImpl::onRemoved(resip::ServerPublicationHandle h,
                           const resip::Data& etag,
                           const resip::SipMessage& pub,
                           UInt32 expires)
{

}
*/

}
}


#endif // 
