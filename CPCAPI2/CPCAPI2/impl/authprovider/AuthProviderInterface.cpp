#include "brand_branded.h"

#if (CPCAPI2_BRAND_AUTH_PROVIDER_MODULE == 1)
#include "cpcapi2utils.h"
#include "AuthProviderInterface.h"
#include "../phone/PhoneModule.h"
#include "../phone/PhoneInterface.h"

#include <MediaStackImpl.hxx>

#include "../util/cpc_logger.h"
#include <rutil/Log.hxx>
#include <rutil/Reactor.hxx>


namespace CPCAPI2
{
namespace Auth
{
   
void AuthProviderInterface::Release()
{
   delete this;
}

AuthProviderInterface::AuthProviderInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone))
{
}

AuthProviderInterface::~AuthProviderInterface()
{
}

PhoneInterface* AuthProviderInterface::phoneInterface() const
{
   return mPhone;
}

void AuthProviderInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void AuthProviderInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

}
}
#endif
