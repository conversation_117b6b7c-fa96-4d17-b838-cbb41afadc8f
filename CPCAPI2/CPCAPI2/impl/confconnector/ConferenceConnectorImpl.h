#pragma once

#if !defined(CPCAPI2_CONFERENCE_CONNECTOR_IMPL_H)
#define CPCAPI2_CONFERENCE_CONNECTOR_IMPL_H

#include "cpcapi2defs.h"
#include "confconnector/ConferenceConnector.h"
#include "confconnector/ConferenceConnectorHandler.h"
#include "jsonapi/JsonApiClient.h"
#include "cloudconnector/CloudServerConnectionObserver.h"
#include "confbridge/ConferenceBridgeManager.h"
#include "confbridge/ConferenceBridgeHandler.h"
#include "confbridge/ConferenceBridgeSyncHandler.h"
#include "confbridge/ConferenceBridgeJsonProxy.h"
#include "peerconnection/PeerConnectionManager.h"
#include "peerconnection/PeerConnectionHandler.h"
#include "peerconnection/PeerConnectionHandlerInternal.h"
#include "peerconnection/PeerConnectionSyncHandler.h"
#include "videostreaming/VideoStreaming.h"
#include "media/video/Video.h"
#include "../experimental/video_ext/VideoExt.h"

#include <rutil/Data.hxx>
#include <resip/stack/Tuple.hxx>

#include <map>
#include <memory>

#if !defined(_WIN32) && !defined(ANDROID)
#include <mm_malloc.h>
#endif

namespace CPCAPI2
{
class PhoneInterface;
namespace CloudConnector
{
class CloudServerConnection;
}
namespace ConferenceConnector
{
class ConferenceConnectorInterface;
struct CloudServerConnStatusEvent;
struct ConferenceSessionStatusChangedEvent;

class ConferenceConnectorImpl : public CPCAPI2::CloudConnector::CloudServerConnectionObserver,
                                public CPCAPI2::ConferenceBridge::ConferenceBridgeHandler,
                                public CPCAPI2::ConferenceBridge::ConferenceListHandler,
                                public CPCAPI2::ConferenceBridge::ParticipantListHandler,
                                public CPCAPI2::ConferenceBridge::PeerConnectionAnswerHandler,
                                public CPCAPI2::ConferenceBridge::ConferenceBridgeSyncHandler,
                                public CPCAPI2::PeerConnection::PeerConnectionHandlerInternal,
                                public CPCAPI2::PeerConnection::PeerConnectionSyncHandler,
                                public CPCAPI2::Media::VideoWebsocketServerHandler,
                                public CPCAPI2::Media::ScreenShareHandler,
                                public std::enable_shared_from_this<ConferenceConnectorImpl>
{
public:
   virtual ~ConferenceConnectorImpl();
   
   static std::shared_ptr<ConferenceConnectorImpl> Create(ConferenceConnectorInterface* ccif, ConferenceConnectorHandle h);

   void* operator new(size_t i)
   {
#if defined(ANDROID)
      return memalign(folly::hardware_destructive_interference_size, i);
#else
      return _mm_malloc(i, folly::hardware_destructive_interference_size);
#endif
   }

   void operator delete(void* p)
   {
#if defined(ANDROID)
      free(p);
#else
      _mm_free(p);
#endif
   }

   int destroy();
   int setConnectionSettings(const ConferenceConnectorSettings& settings);
   int requestService();
   int connectToServices();
   int disconnectService();
   int queryConferenceList();
   CloudConferenceHandle createNewConference(const CloudConferenceSettings& settings);
   int destroyConference(CloudConferenceHandle conference);
   int queryParticipantList(CloudConferenceHandle conference);
   int createConferenceSession(CloudConferenceHandle conference, CloudConferenceSessionHandle session, const CloudConferenceSessionSettings& settings);
   int setSessionSettings(CloudConferenceSessionHandle session, const CloudConferenceSessionSettings& settings);
   int setSessionMediaSettings(CloudConferenceSessionHandle session, const CloudConferenceSessionMediaSettings& settings);
   int startSession(CloudConferenceSessionHandle session);
   int endSession(CloudConferenceSessionHandle session, bool isReconnecting);
   int updateSessionMedia(CloudConferenceSessionHandle session);
   int setParticipantPermissions(CloudConferenceSessionHandle session, CloudConferenceParticipantHandle participant, const ConferenceSessionPermissions& permissions);
   int pauseSessionPlayout(CloudConferenceSessionHandle session);
   int resumeSessionPlayout(CloudConferenceSessionHandle session);
   int dropIncomingJsonMessages(bool enable);
   int queryMediaStatistics(CloudConferenceSessionHandle session);
   int setMediaInactivityMonitor(CloudConferenceSessionHandle session, bool enableMediaInactivityMonitor, int mediaInactivityIntervalMsecs);

   void release();

   // CloudServerConnectionObserver
   virtual void handleCloudServerConnectionStatusChanged(CPCAPI2::CloudConnector::CloudServerConnection* conn, const CPCAPI2::CloudConnector::CloudServerConnStatusEvent& args) OVERRIDE;

   // ConferenceBridgeHandler
   virtual int onConferenceDetails(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceDetailsResult& args) OVERRIDE;
   virtual int onConferenceEnded(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceEndedEvent& args) OVERRIDE;
   virtual int onParticipantListState(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ParticipantListState& args) OVERRIDE;
   virtual int onConferenceTranscriptionResult(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceTranscriptionEvent& args) OVERRIDE;

   // PeerConnectionAnswerHandler
   virtual int onPeerConnectionAnswer(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::PeerConnectionAnswerEvent& args) OVERRIDE;

   // ConferenceListHandler
   virtual int onConferenceList(const CPCAPI2::ConferenceBridge::ConferenceListResult& args) OVERRIDE;

   // PeerConnectionHandler
   virtual int onSignalingStateChange(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SignalingStateChangeEvent& args) OVERRIDE;
   virtual int onCreateOfferResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateOfferResult& args) OVERRIDE;
   virtual int onCreateAnswerResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args) OVERRIDE;
   virtual int onSetLocalSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetLocalSessionDescriptionResult& args) OVERRIDE;
   virtual int onSetRemoteSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetRemoteSessionDescriptionResult& args) OVERRIDE;
   virtual int onMediaInactivity(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::MediaInactivityEvent& args) OVERRIDE;
   virtual int onError(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::ErrorEvent& args) OVERRIDE;

   // PeerConnectionHandlerInternal
   virtual int onPeerConnectionMediaStatistics(CPCAPI2::PeerConnection::PeerConnectionHandle peerConnection, const PeerConnection::PeerConnectionMediaStatisticsEvent& args) OVERRIDE;

   // VideoWebsocketServerHandler
   virtual void onVideoWebsocketServerStarted(int mediaStreamId, const CPCAPI2::Media::VideoWebsocketServerStartedEvent& args) OVERRIDE;

   virtual void onScreenShareError() override;

private:
   ConferenceConnectorImpl(ConferenceConnectorInterface* ccif, ConferenceConnectorHandle h);
   void init();

   CloudConferenceHandle createConferenceHandle(const CPCAPI2::ConferenceBridge::ConferenceSettings& settings);
   int doFirstTimeConnect(const resip::Data& authToken, const resip::Data& realm, const CPCAPI2::CloudConnector::ServiceDesc& confbridgeServiceDesc);
   static void setIncomingVideoRenderTargetWrap(CPCAPI2::Media::Video* videoIf, int mediaStreamId, void* surface, Media::VideoSurfaceType type = Media::VideoSurfaceType_Default);
   void resolveNatTraversalServerHostname(const cpc::string& natTraversalServerHostname, int natTraversalServerPort);

   void handleScreenShareError();

   size_t getNumSessionsCapturingLocalVideo() const;

private:

   ConferenceConnectorInterface* mInterface;
   ConferenceConnectorHandle mHandle;
   ConferenceConnectorSettings mSettings;

   enum ConnectionDisconnectState
   {
      ConnectionDisconnectState_None,
      ConnectionDisconnectState_DisconnectSent,
      ConnectionDisconnectState_DisconnectNotSent
   };
   std::atomic<bool> mConferenceConnectorDisconnecting;
   std::atomic<bool> mConferenceConnectorDestroyed;
   std::atomic<int> mConferenceConnectorDisconnectState;

   resip::Data mConfServiceUri;
   std::shared_ptr<CPCAPI2::CloudConnector::CloudServerConnection> mConfServiceConn;
   CPCAPI2::CloudConnector::CloudServerConnStatus mConfServiceConnStatus;
   resip::MultiReactor mWebRequestThread;

   CPCAPI2::Phone* mConfPhone;
   CPCAPI2::Media::Video* mVideoIf;
   CPCAPI2::Media::VideoExt* mVideoExtIf;

   // JSON proxy for ConferenceBridgeManager
   CPCAPI2::ConferenceBridge::ConferenceBridgeManager* mConfBridgeMgr;

   // local PeerConnectionManager
   CPCAPI2::PeerConnection::PeerConnectionManager* mPeerConnMgr;

   CPCAPI2::VideoStreaming::VideoStreamingManager* mVideoStreamingMgr;

   struct ConferenceState
   {
      CPCAPI2::ConferenceBridge::ConferenceHandle confHandle = (CPCAPI2::ConferenceBridge::ConferenceHandle)-1;
      CloudConferenceHandle cloudConfHandle = (CloudConferenceHandle)-1;
      CloudConferenceSettings settings;
      CPCAPI2::ConferenceBridge::ConferenceParticipantHandle part = (CPCAPI2::ConferenceBridge::ConferenceParticipantHandle)-1;
      bool hasFloor = false;
      bool owner = false;
      bool waitingForCreatedEvent = false;
      CPCAPI2::ConferenceBridge::ParticipantPermissions participantPermissions;
      std::shared_ptr<ConferenceSessionStatusChangedEvent> lastConnectedEvent;
      CPCAPI2::ConferenceBridge::ConferenceInfo confInfo;
   };
   std::vector<ConferenceState> mConfStateMap;
   std::vector<ConferenceState>::iterator getConferenceState(CloudConferenceHandle h);
   std::vector<ConferenceState>::iterator getConferenceState_ConfHandle(CPCAPI2::ConferenceBridge::ConferenceHandle h);

   struct SessionState
   {
      CPCAPI2::ConferenceBridge::ConferenceParticipantHandle part = (CPCAPI2::ConferenceBridge::ConferenceParticipantHandle )-1;
      CPCAPI2::PeerConnection::PeerConnectionHandle pc = (CPCAPI2::PeerConnection::PeerConnectionHandle )-1;
      CloudConferenceSessionSettings sessionSettings;
      bool sessionSettingsUpdated = false;
      std::unique_ptr<CloudConferenceSessionMediaSettings> pendingMediaSettings;
      CloudConferenceSessionMediaSettings activeMediaSettings;
      CloudConferenceHandle cloudConference;
      bool isCapturingLocalVideo = false;
      bool isCapturingLocalScreen = false;
      int videoRecvMediaStreamId = -1;
      CPCAPI2::PeerConnection::MediaStreamHandle audioMediaStream;
      CPCAPI2::PeerConnection::MediaStreamHandle videoMediaStream;
      CPCAPI2::VideoStreaming::VideoStreamHandle vidStream = (CPCAPI2::VideoStreaming::VideoStreamHandle)-1;
      bool enableMediaInactivityMonitor = false;
      int mediaInactivityIntervalMsecs = -1;
   };
   std::map<CloudConferenceSessionHandle, SessionState> mSessStateMap;

   bool mQueryConferenceListPending = false;
   std::map<CloudConferenceHandle, CPCAPI2::ConferenceConnector::ConferenceParticipantListUpdatedEvent> mPendingParticipantListUpdateEvent;

   static cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::ConferenceConnectorImpl::ConferenceState& state);
   static cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::ConferenceConnectorImpl::SessionState& state);
   static cpc::string get_debug_string(const CPCAPI2::ConferenceConnector::ConferenceConnectorImpl& impl);
   friend std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceConnectorImpl::ConferenceState& state);
   friend std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceConnectorImpl::SessionState& state);
   friend std::ostream& operator<<(std::ostream& os, const CPCAPI2::ConferenceConnector::ConferenceConnectorImpl& impl);

};

}

}

#endif // CPCAPI2_CONFERENCE_CONNECTOR_IMPL_H

