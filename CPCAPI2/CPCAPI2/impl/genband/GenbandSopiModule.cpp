#include "brand_branded.h"

#include "interface/experimental/genband/GenbandSopiManager.h"

#if (CPCAPI2_BRAND_GENBAND_SOPI_MODULE == 1)
#include "GenbandSopiManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace GenbandSopi
   {

      GenbandSopiManager* GenbandSopiManager::getInterface(CPCAPI2::Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_GENBAND_SOPI_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<GenbandSopiManagerInterface>(phone, "GenbandSopiClientManagerInterface");
#else
         return NULL;
#endif
      }

   }
}
