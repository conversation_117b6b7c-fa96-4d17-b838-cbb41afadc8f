#include <nlohmann/json.hpp>


#ifndef JSONRPC_CPCAPI2_SIPMWI_SIPMWISTART_H
#define JSONRPC_CPCAPI2_SIPMWI_SIPMWISTART_H

namespace jsonrpc {
namespace CPCAPI2 {
namespace SipMwi {
  class SipMwiStart
  {
  public:
    int32_t phoneHandle;
    int32_t mwiSubscriptionHandle;

    SipMwiStart() = default;

    SipMwiStart(const std::string& json)
    {
      this->unmarshal(json);
    }

    std::string marshal()
    {
      nlohmann::json j;
      to_json(j, *this);
      return j.dump(-1, ' ', false, nlohmann::json::error_handler_t::replace);
    }

    friend void to_json(nlohmann::json& j, const SipMwiStart& o)
    {
      j = nlohmann::json::object();
       j["phoneHandle"] = o.phoneHandle;
     j["mwiSubscriptionHandle"] = o.mwiSubscriptionHandle;
    }


    void unmarshal(const std::string& json)
    {
      nlohmann::json j = nlohmann::json::parse(json);
      from_json(j, *this);
    }

    friend void from_json(const nlohmann::json& j, SipMwiStart& o)
    {
     o.phoneHandle = j.at("phoneHandle");
     o.mwiSubscriptionHandle = j.at("mwiSubscriptionHandle");
    }
  };
} // namepace jsonrpc
} // namepace CPCAPI2
} // namepace SipMwi
#endif // JSONRPC_CPCAPI2_SIPMWI_SIPMWISTART_H
