#pragma once

#if !defined(CPCAPI2_CONFERENCE_WEB_PARTICIPANT_IMPL_H)
#define CPCAPI2_CONFERENCE_WEB_PARTICIPANT_IMPL_H

#include "cpcapi2defs.h"
#include "confbridge/ConferenceBridgeTypes.h"
#include "peerconnection/PeerConnectionManager.h"
#include "peerconnection/PeerConnectionHandlerInternal.h"
#include "peerconnection/PeerConnectionSyncHandler.h"
#include "audiotranscription/AudioTrans.h"
#include "audiotranscription/AudioTransInterface.h"

#include <memory>

namespace CPCAPI2
{
namespace ConferenceBridge
{
class ConferenceBridgeManagerImpl;
class PeerConnectionAnswerHandler;

class ConferenceWebParticipantImpl : public CPCAPI2::PeerConnection::PeerConnectionHandlerInternal,
                                     public CPCAPI2::PeerConnection::PeerConnectionSyncHand<PERSON>,
                                     public CPCAPI2::AudioTrans::AudioTransH<PERSON><PERSON>,
                                     public CPCAPI2::AudioTrans::AudioTransSyncHand<PERSON>,
                                     public std::enable_shared_from_this<ConferenceWebParticipantImpl>
{
public:
   ConferenceWebParticipantImpl(ConferenceParticipantHandle confPartHandle, CPCAPI2::Phone* phone, ConferenceBridgeManagerImpl* confBridgeMgr);
   virtual ~ConferenceWebParticipantImpl();

   int sendPeerConnectionOffer(const CPCAPI2::PeerConnection::SessionDescription& sdpOffer, PeerConnectionAnswerHandler* handler);
   int sendPeerConnectionAnswer(const CPCAPI2::PeerConnection::SessionDescription& sdpAnswer);
   int generateLocalOffer(PeerConnectionAnswerHandler* handler);
   int destroyWebParticipant();
   int takeParticipantSnapshot();
   int setParticipantPhoto(const cpc::string& photoFileNameUtf8);
   int addToFloor(bool suppressEvent = false);
   int removeFromFloor();
   int setTranscriptionEnabled(bool enabled);
   int queryMediaStatistics();
   int connectAllStreams();
   int connectPresenterStream();
   int connectParticipantStreams();

   const ParticipantInfo& participantInfo() const {
      return mPartInfo;
   }
   ParticipantInfo& participantInfo() {
      return mPartInfo;
   }

   // Inherited via PeerConnectionHandler
   virtual int onSignalingStateChange(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SignalingStateChangeEvent & args) OVERRIDE;
   virtual int onCreateOfferResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateOfferResult & args) OVERRIDE;
   virtual int onCreateAnswerResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult & args) OVERRIDE;
   virtual int onSetLocalSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetLocalSessionDescriptionResult & args) OVERRIDE;
   virtual int onSetRemoteSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetRemoteSessionDescriptionResult & args) OVERRIDE;
   virtual int onWebVideoServerReady(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::WebVideoServerReadyEvent& args) OVERRIDE {
      return kSuccess;
   }
   virtual int onError(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::ErrorEvent & args) OVERRIDE;
   virtual int onMediaInactivity(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::MediaInactivityEvent& args) OVERRIDE;
   virtual int onPeerConnectionMediaStatistics(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const PeerConnection::PeerConnectionMediaStatisticsEvent& args) OVERRIDE;

   // AudioTransHandler
   virtual int onTranscriptionResult(CPCAPI2::AudioTrans::AudioTransHandle audioTransSession, const CPCAPI2::AudioTrans::AudioTranscriptionResult& evt) OVERRIDE;

private:
   void onSnapshotComplete(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const cpc::string& filenameUtf8);

private:
   CPCAPI2::Phone* mPhone;
   CPCAPI2::PeerConnection::PeerConnectionManager* mPeerConnMgr;
   CPCAPI2::AudioTrans::AudioTransManager* mAudioTransMgr;
   ConferenceBridgeManagerImpl* mConfBridgeMgr;
   ConferenceParticipantHandle mConfPartHandle;
   CPCAPI2::PeerConnection::PeerConnectionHandle mHandle;
   ParticipantInfo mPartInfo;
   cpc::vector<CPCAPI2::PeerConnection::MediaInfo> mMediaInfo;
   CPCAPI2::AudioTrans::AudioTransHandle mAudioTransHandle = (CPCAPI2::AudioTrans::AudioTransHandle)-1;
   bool mTranscriptionEnabled = false;
   cpc::string mPartPhoto;
   PeerConnectionAnswerHandler* mPeerConnectionAnswerHandler;

   bool mMediaQueryInProgress = false;
   std::set<CPCAPI2::PeerConnection::MediaStreamHandle> mMediaQueries;
   
   static uint64_t mLastJoinTimestampMsecs;

   int mVideoSFUParticipantChannel = -1;

};

}

}

#endif // CPCAPI2_CONFERENCE_WEB_PARTICIPANT_IMPL_H
