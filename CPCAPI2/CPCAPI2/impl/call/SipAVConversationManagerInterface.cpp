#include "brand_branded.h"

#if (CPCAPI2_BRAND_CALL_MODULE == 1)
#include "cpcapi2utils.h"
#include "SipAVConversationManagerInterface.h"
#include "SipDTMFRelayUtils.h"
#include "../phone/PhoneInterface.h"
#include "../account/SipAccountInterface.h"
#include "SipAVConversationManagerImpl.h"
#include "SipConversationStateImpl.h"
#include "ReconConversationManagerImpl.h"
#include "../util/dtmf_tone_helper.h"
#include "../util/ResipConv.h"
#include "../util/cpc_logger.h"
#include "../util/CharEncodingHelper.h"
#include "../analytics1/AnalyticsManagerInterface.h"
#include "watchdog/WatchdogManager.h"
#include "../util/cpc_thread.h"

#include "../media/MediaManagerInterface.h"
#include "media/audio/Audio.h"
#include "../media/PlaySoundHelper.h"
#include <MediaStackImpl.hxx>
#include <CodecFactoryImpl.hxx>
#include <RtpStreamImpl.hxx>
#include <MixerImpl.hxx>

// video support
#include "media/video/Video.h"
#include "../media/VideoInterface.h"

#ifdef ANDROID
#include "JniHelper.h"
#include "webrtc/base/checks.h"
#include "webrtc/modules/utility/interface/helpers_android.h"
#endif

#include <resip/stack/ExtensionParameter.hxx>
#include <resip/stack/PlainContents.hxx>
#include <rutil/Time.hxx>

#include <voe_base.h>
#include <voe_dtmf.h>
#include <webrtc/modules/audio_device/include/audio_device.h>
#include <webrtc/common_types.h>

#include <sstream>

#include "json/JsonHelper.h"

using namespace CPCAPI2::Media;
using namespace CPCAPI2::SipAccount;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_CALL

namespace CPCAPI2
{
namespace SipConversation
{

SipAVConversationManagerInterface::SipAVConversationManagerInterface(Phone* phone)
   : EventSource2< EventHandler<SipConversationHandler, SipAccount::SipAccountHandle> >(dynamic_cast<PhoneInterface*>(phone),
      /* enable logging */ true, RESIPROCATE_SUBSYSTEM),
     mAccountIf(NULL),
     mAccountMap(new AccountMap),
     mMediaIf(NULL),
     mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mLastStatsRefreshTime_Secs(0)
{
   mAccountIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(phone));
   mMediaIf = dynamic_cast<MediaManagerInterface*>(MediaManager::getInterface(phone));
}

SipAVConversationManagerInterface::~SipAVConversationManagerInterface()
{
}

void SipAVConversationManagerInterface::Release()
{
   mAccountMap->clear();

   delete this;
}

PhoneInterface* SipAVConversationManagerInterface::phoneInterface()
{
   return mPhone;
}

int SipAVConversationManagerInterface::setHandler(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipConversationHandler* handler)
{
   resip::ReadCallbackBase* configureCmd = resip::resip_bind(&SipAVConversationManagerInterface::setHandlerImpl, this, account, handler);
   if (handler == NULL)
   {
      // removing the handler involves two steps:
      // 1. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);

      // 2. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      executeOnSdkThread(configureCmd);
   }
   else
   {
      postToSdkThread(configureCmd);
   }

   return kSuccess;
}

int SipAVConversationManagerInterface::setHandlerImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipConversationHandler* handler)
{
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (NULL == acct)
   {
      mAccountIf->fireError("Invalid account handle for ConversationManager::setHandler");
   }
   else if (handler == NULL)
   {
      auto it = mHandlers.find(account);
      if (mHandlers.end() != it)
      {
        removeAppHandler(it->second, account);
      }
   }
   else if(acct->isEnabled())
   {
      mAccountIf->fireError("ConversationManager::setHandler was called after account enabled: " + cpc::to_string(account));
   }
   else
   {
      initImpl(account);

      auto it = mHandlers.find(account);
      if (mHandlers.end() != it)
      {
        removeAppHandler(it->second, account);
      }

      mHandlers[account] = handler;
      if (nullptr != handler)
      {
         addAppHandler(handler, account);
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::setDefaultSettings(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipConversationSettings& settings)
{
   resip::ReadCallbackBase* cmd = resip::resip_bind(&SipAVConversationManagerInterface::setDefaultSettingsImpl, this, account, settings);
   postToSdkThread(cmd);
   return kSuccess;
}

int SipAVConversationManagerInterface::setDefaultSettingsImpl(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipConversationSettings& settings)
{
   SipAVConversationManagerImpl* convMan = initImpl(account);
   if (convMan)
   {
      convMan->setDefaultSettings(settings, TransportNone);
      return kSuccess;
   }
   return kError;
}

int SipAVConversationManagerInterface::setDefaultSettings(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipConversationSettings& settings, CPCAPI2::NetworkTransport transport)
{
   resip::ReadCallbackBase* cmd = resip::resip_bind(&SipAVConversationManagerInterface::setDefaultSettingsImpl2, this, account, settings, transport);
   postToSdkThread(cmd);
   return kSuccess;
}

int SipAVConversationManagerInterface::setDefaultSettingsImpl2(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipConversationSettings& settings, CPCAPI2::NetworkTransport transport)
{
   SipAVConversationManagerImpl* convMan = initImpl(account);
   if (convMan)
   {
      convMan->setDefaultSettings(settings, transport);
      return kSuccess;
   }
   return kError;
}

bool SipAVConversationManagerInterface::getConversationSettings(CPCAPI2::SipConversationSettings& settings, CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::NetworkTransport transport)
{
   AccountMap::iterator it = mAccountMap->find(account);
   if (it != mAccountMap->end())
   {
      SipAVConversationManagerImpl* impl = it->second;
      return (impl->getDefaultSettings(transport, settings));
   }

   return false;
}

SipAVConversationManagerImpl* SipAVConversationManagerInterface::initImpl(CPCAPI2::SipAccount::SipAccountHandle account)
{
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (!acct)
   {
      mAccountIf->fireError("Invalid account handle for SipAVConversationManager::init");
      return NULL;
   }

   std::shared_ptr<webrtc_recon::MediaStackImpl> ms = mMediaIf->media_stack_ptr();
   AccountMap::iterator it = mAccountMap->find(account);
   SipAVConversationManagerImpl* convMan = (it == mAccountMap->end() ? NULL : it->second);
   if (convMan == NULL)
   {
      convMan = new SipAVConversationManagerImpl(this, mAccountMap, *acct, ms, mPhone->localLogger());
      (*mAccountMap)[account] = convMan;
   }
   return convMan;
}

int SipAVConversationManagerInterface::setCallInitiatedFromPush(SipConversationHandle conversation)
{
   WarningLog(<< "setCallInitiatedFromPush is deprecated; it is no longer necessary to call this method.");
   return kSuccess;
}

SipConversationHandle SipAVConversationManagerInterface::createConversation(CPCAPI2::SipAccount::SipAccountHandle account, SipConversationHandle conversation)
{
   resip::ReadCallbackBase* cmd = resip::resip_bind(&SipAVConversationManagerInterface::createConversationImpl, this, account, conversation);
   postToSdkThread(cmd);
   return conversation;
}

SipConversationHandle SipAVConversationManagerInterface::createConversation(CPCAPI2::SipAccount::SipAccountHandle account)
{
   SipConversationHandle h = SipConversationHandleFactory::getNext();
   resip::ReadCallbackBase* cmd = resip::resip_bind(&SipAVConversationManagerInterface::createConversationImpl, this, account, h);
   postToSdkThread(cmd);
   return h;
}

int SipAVConversationManagerInterface::createConversationImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipConversationHandle h)
{
   DebugLog(<< "SipAVConversationManagerInterface::createConversationImpl(), account=" << account << " handle=" << h);
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (!acct)
   {
      cpc::string msg = cpc::string("SipAVConversationManagerInterface::createConversation called with invalid account handle: ") + cpc::to_string(account) +
                        cpc::string(" SipConversationHandle invalid: " + cpc::to_string(h));
      mAccountIf->fireError(msg);
      return kSuccess;
   }
   if(!acct->isEnabled())
   {
      cpc::string msg = cpc::string("SipAVConversationManagerInterface::createConversation called before account enabled: ") + cpc::to_string(account) +
                           cpc::string(" SipConversationHandle invalid: " + cpc::to_string(h));
      mAccountIf->fireError(msg);
      return kSuccess;
   }
   AccountMap::iterator it = mAccountMap->find(account);
   if (it != mAccountMap->end())
   {
      SipCallCreationInfo* ci = new SipCallCreationInfo();
      ci->account = account;
      it->second->addCreationInfo(h, ci);
      return kSuccess;
   }
   return kError;
}

SipCallCreationInfo* SipAVConversationManagerInterface::getCreationInfo(SipConversationHandle h) const
{
   AccountMap::iterator itAcct = mAccountMap->begin();
   for (; itAcct != mAccountMap->end(); ++itAcct)
   {
      SipCallCreationInfo* ci = itAcct->second->getCreationInfo(h);
      if (ci != NULL)
      {
         return ci;
      }
   }
   return NULL;
}

SipAVConversationManagerImpl* SipAVConversationManagerInterface::getConvImpl(SipConversationHandle h)
{
   AccountMap::iterator itAcct = mAccountMap->begin();
   for (; itAcct != mAccountMap->end(); ++itAcct)
   {
      SipCallCreationInfo* ci = itAcct->second->getCreationInfo(h);
      if (ci != NULL)
      {
         return itAcct->second;
      }
   }
   return NULL;
}

SipAccountHandle SipAVConversationManagerInterface::getSipAccountHandle(SipConversationHandle conversation) const
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      return ci->account;
   }
   return (SipAccountHandle)-1;
}

CPCAPI2::SipAccount::SipAccountHandle SipAVConversationManagerInterface::getSipAccountHandleForCallId(const cpc::string& callId) const
{
   AccountMap::iterator itAcct = mAccountMap->begin();
   for (; itAcct != mAccountMap->end(); ++itAcct)
   {
      if (itAcct->second->hasCallWithCallId(callId))
      {
         return itAcct->first;
      }
   }
   return (SipAccountHandle)-1;
}

int SipAVConversationManagerInterface::internalStarCodeHandling(const cpc::string& targetAddress, SipConversationHandle conversation,
                                                           SipCallCreationInfo& creationInfo)
{
   const resip::Data t(targetAddress);

   if (t.prefix("sip:***crashcpcapi2"))
   {
#ifndef __clang_analyzer__
#ifdef __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wnull-dereference"
#endif
      char* f = 0;
      *f = 16;
#ifdef __GNUC__
#pragma GCC diagnostic pop
#endif
#endif // #ifndef __clang_analyzer__
   }
   else if (t.prefix("sip:***fatalmessage"))
   {
#ifdef ANDROID
      CPCAPI2::Jni::JniThread env;
      jclass Exception = env->FindClass("java/lang/Exception");
      env->ThrowNew(Exception, "Test exception message");
      // use CPCAPI2 check
      JNI_CHECK(false) << "Test fatal message";
#endif
   }
   else if (t.prefix("sip:***watchdogcpcapi2"))
   {
      if (Watchdog::WatchdogManager* w = Watchdog::WatchdogManager::getInterface(mPhone))
      {
         w->startThreadWatchdog();
      }
      else
      {
         WarningLog(<< "Unable to start watchdog; not able to get watchdog interface");
      }
   }
   else if (t.prefix("sip:***hangcpcapi2"))
   {
      this_thread::sleep_for(chrono::milliseconds(6500));
   }
   else if (t.prefix("sip:***nonack"))
   {
      AccountMap::iterator itAcct = mAccountMap->begin();
      for (; itAcct != mAccountMap->end(); ++itAcct)
      {
         itAcct->second->recon()->setNackPliSupported(false);
      }
   }
   else if (t.prefix("sip:***nack"))
   {
      AccountMap::iterator itAcct = mAccountMap->begin();
      for (; itAcct != mAccountMap->end(); ++itAcct)
      {
         itAcct->second->recon()->setNackPliSupported(true);
      }
   }
   else if (t.prefix("sip:***nosendaudiofilters"))
   {
      CPCAPI2::Media::MediaManager* mediaIf = CPCAPI2::Media::MediaManager::getInterface(mPhone);
      CPCAPI2::Media::Audio* audioIf = CPCAPI2::Media::Audio::getInterface(mediaIf);
      audioIf->setEchoCancellationMode(CPCAPI2::Media::AudioDeviceRole_Headset, CPCAPI2::Media::EchoCancellationMode_None);
      audioIf->setNoiseSuppressionMode(CPCAPI2::Media::AudioDeviceRole_Headset, CPCAPI2::Media::NoiseSuppressionMode_None);
      CPCAPI2::Media::GainSettings gainSettings;
      gainSettings.rxConfig.mode = CPCAPI2::Media::GainMode_None;
      gainSettings.txConfig.mode = CPCAPI2::Media::GainMode_None;
      gainSettings.spkConfig.enabled = false;
      audioIf->setGainSettings(gainSettings);
   }
   else if (t.prefix("sip:***lowersendres"))
   {
      CPCAPI2::Media::MediaManager* mediaIf = CPCAPI2::Media::MediaManager::getInterface(mPhone);
      CPCAPI2::Media::Video* videoIf = CPCAPI2::Media::Video::getInterface(mediaIf);
      videoIf->setLocalVideoPreviewResolution(CPCAPI2::Media::VideoCaptureResolution_Standard);
   }
   else if (t.prefix("sip:***1080p"))
   {
#if (CPCAPI2_BRAND_VIDEO_MODULE == 1)
      CPCAPI2::Media::MediaManager* mediaIf = CPCAPI2::Media::MediaManager::getInterface(mPhone);
      CPCAPI2::Media::Video* videoIf = CPCAPI2::Media::Video::getInterface(mediaIf);
      VideoInterface* videoIfImpl = dynamic_cast<VideoInterface*>(videoIf);
      videoIfImpl->set1080pEnabled(true);
#endif // #if (CPCAPI2_BRAND_VIDEO_MODULE == 1)
   }
   else if (t.prefix("sip:***videoredundancyon"))
   {
      mVideoPacketRedundancyFactor = 2;
   }
   else if (t.prefix("sip:***videoredundancyoff"))
   {
      mVideoPacketRedundancyFactor = 1;
   }
   else if (t.prefix("sip:***receivetmmbron"))
   {
      mReceiveTmmbrEnabled = true;
   }
   else if (t.prefix("sip:***receivetmmbroff"))
   {
      mReceiveTmmbrEnabled = false;
   }
   else if (t.prefix("sip:***periodickeyframeson"))
   {
#if (CPCAPI2_BRAND_VIDEO_MODULE == 1)
      CPCAPI2::Media::MediaManager* mediaIf = CPCAPI2::Media::MediaManager::getInterface(mPhone);
      CPCAPI2::Media::VideoExt* videoIf = CPCAPI2::Media::VideoExt::getInterface(mediaIf);
      videoIf->setKeyFramePeriod(30*2);
#endif // #if (CPCAPI2_BRAND_VIDEO_MODULE == 1)
   }
   else if (t.prefix("sip:***periodickeyframesoff"))
   {
#if (CPCAPI2_BRAND_VIDEO_MODULE == 1)
      CPCAPI2::Media::MediaManager* mediaIf = CPCAPI2::Media::MediaManager::getInterface(mPhone);
      CPCAPI2::Media::VideoExt* videoIf = CPCAPI2::Media::VideoExt::getInterface(mediaIf);
      videoIf->setKeyFramePeriod(-1);
#endif // #if (CPCAPI2_BRAND_VIDEO_MODULE == 1)
   }
   else if (t.prefix("sip:***aecdumpon"))
   {
      webrtc_recon::MediaStackImpl::setApmDump(true);
   }
   else if (t.prefix("sip:***aecdumpoff"))
   {
      webrtc_recon::MediaStackImpl::setApmDump(false);
   }
   else if (t.prefix("sip:***transferresponsetest"))
   {
      AccountMap::iterator itAcct = mAccountMap->find( creationInfo.account );
      if( itAcct == mAccountMap->end() )
         return kError;

      SipAVConversationManagerImpl* acct = itAcct->second;
      if( acct == NULL )
         return kError;
         
      TransferResponseEvent args;
      args.sipResponseCode = 202;
      args.warningHeader = "Test Warning Header";
      fireEvent(cpcEvent(SipConversationHandler, onTransferResponse), conversation, args);
   }
   else if (t.prefix("sip:***setssfps"))
   {
#if (CPCAPI2_BRAND_VIDEO_MODULE == 1)
      resip::Data fpsStr = t.substr(15, t.find("@"));
      int fps = fpsStr.convertInt();
      if (fps > 0)
      {
         CPCAPI2::Media::MediaManager* mediaIf = CPCAPI2::Media::MediaManager::getInterface(mPhone);
         CPCAPI2::Media::VideoExt* videoIf = CPCAPI2::Media::VideoExt::getInterface(mediaIf);
         videoIf->setScreenshareCaptureMaxFramerate(fps, true);
      }
#endif // #if (CPCAPI2_BRAND_VIDEO_MODULE == 1)      
   }
   else if (t.prefix("sip:***accounterror"))
   {
      mAccountIf->getAccountImpl(creationInfo.account)->fireAccountError("Can't provide an offer");
   }

   return kSuccess;
}

int SipAVConversationManagerInterface::addParticipant(SipConversationHandle conversation, const cpc::string& targetAddress)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::addParticipantImpl, this, conversation, targetAddress));
   return kSuccess;
}

int SipAVConversationManagerInterface::addParticipantImpl(SipConversationHandle conversation, const cpc::string& targetAddress)
{
   DebugLog(<< "SipAVConversationManagerInterface::addParticipantImpl(" << conversation << ", target: " << targetAddress << ")");

   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      internalStarCodeHandling(targetAddress, conversation, *ci);
   
      resip::NameAddr targetNameAddr;
      if (!ResipConv::stringToAddr(targetAddress, targetNameAddr))
      {
         AccountMap::iterator it = mAccountMap->find(ci->account);
         SipAVConversationManagerImpl* evtMan = (it == mAccountMap->end() ? NULL : it->second);
         if (evtMan)
         {
            evtMan->fireError(conversation, "Failed to parse participant URI '" + targetAddress + "'");
            return kSuccess;
         }
         return kError;
      }
      ci->targetAddresses.push_back(targetNameAddr);
      return kSuccess;
   }
   else
   {
      cpc::string msg = "SipAVConversationManagerInterface::addParticipant called with invalid conversation handle: " + cpc::to_string(conversation);
      mAccountIf->fireError(msg);
      return kSuccess;
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::setMediaEnabled(SipConversationHandle conversation, MediaType mediaType, bool enabled)
{
   DebugLog(<< "SipAVConversationManagerInterface::setMediaEnabled()");

   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setMediaEnabledImpl, this, conversation, mediaType, enabled));
   return kSuccess;
}

int SipAVConversationManagerInterface::setMediaEnabledImpl(SipConversationHandle conversation, MediaType mediaType, bool enabled)
{
   DebugLog(<< "SipAVConversationManagerInterface::setMediaEnabledImpl(media " << conversation << "," << (mediaType == MediaType_Video ? "video" : "audio") << ", enabled " << (enabled ? "true" : "false") << ")");

   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      cpc::vector<MediaInfo>::iterator itMi = ci->localMediaInfo.begin();
      bool found = false;
      MediaEncryptionOptions existingMediaEncryptionOptions;
      existingMediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_Unencrypted;
      existingMediaEncryptionOptions.secureMediaRequired = false;

      // Crypto suite gets initialized from configuration, don't override
      DebugLog(<< "SipAVConversationManagerInterface::setMediaEnabledImpl: CRYPTO Suites size: " << existingMediaEncryptionOptions.mediaCryptoSuites.size());

      for (; itMi != ci->localMediaInfo.end(); ++itMi)
      {
         existingMediaEncryptionOptions = itMi->mediaEncryptionOptions;
         if (itMi->mediaType == mediaType)
         {
            found = true;
            if (enabled)
            {
               itMi->mediaDirection = MediaDirection_SendReceive;
            }
            else
            {
               itMi->mediaDirection = MediaDirection_None;
            }
         }
      }
      if (!found && enabled)
      {
         MediaInfo mi;
         mi.mediaType = mediaType;
         mi.mediaDirection = MediaDirection_SendReceive;
         mi.mediaEncryptionOptions = existingMediaEncryptionOptions;
         ci->localMediaInfo.push_back(mi);
      }
   }
   else
      ErrLog(<< "CreationInfo not found in SipAVConversationManagerInterface::setMediaEnabledImpl() for conversation " << conversation << "!");
   return kSuccess;
}

int SipAVConversationManagerInterface::setMediaEnabledByDirection(SipConversationHandle conversation, MediaType mediaType, bool enabled)
{
   DebugLog(<< "SipAVConversationManagerInterface::setMediaEnabledByDirection(conversation " << conversation << ", media " << (mediaType == MediaType_Video ? "video" : "audio") << ", enabled " << (enabled ? "true" : "false") << ")");

   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setMediaEnabledByDirectionImpl, this, conversation, mediaType, enabled));
   return kSuccess;
}

int SipAVConversationManagerInterface::setMediaEnabledByDirectionImpl(SipConversationHandle conversation, MediaType mediaType, bool enabled)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      cpc::vector<MediaInfo>::iterator itMi = ci->localMediaInfo.begin();
      bool found = false;
      MediaEncryptionOptions existingMediaEncryptionOptions;
      existingMediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_Unencrypted;
      existingMediaEncryptionOptions.secureMediaRequired = false;

      // Crypto suite gets initialized from configuration, don't override
      DebugLog(<< "SipAVConversationManagerInterface::setMediaEnabledImpl: CRYPTO Suites size: " << existingMediaEncryptionOptions.mediaCryptoSuites.size());

      for (; itMi != ci->localMediaInfo.end(); ++itMi)
      {
         existingMediaEncryptionOptions = itMi->mediaEncryptionOptions;
         if (itMi->mediaType == mediaType)
         {
            found = true;
            if (enabled)
            {
               itMi->mediaDirection = MediaDirection_SendReceive;
               itMi->isLocallyDisabled = false;
            }
            else
            {
               itMi->mediaDirection = MediaDirection_Inactive;
               itMi->isLocallyDisabled = true;
            }
         }
      }
      if (!found && enabled)
      {
         MediaInfo mi;
         mi.mediaType = mediaType;
         mi.mediaDirection = MediaDirection_SendReceive;
         mi.mediaEncryptionOptions = existingMediaEncryptionOptions;
         mi.isLocallyDisabled = false;
         ci->localMediaInfo.push_back(mi);
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::configureMedia(SipConversationHandle conversation, const MediaInfo& mediaDescriptor)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::configureMediaImpl, this, conversation, mediaDescriptor));
   return kSuccess;
}

int SipAVConversationManagerInterface::configureMediaImpl(SipConversationHandle conversation, const MediaInfo& mediaDescriptor)
{
   DebugLog(<< "SipAVConversationManagerInterface::configureMedia(" << conversation << ", " << (mediaDescriptor.mediaType == MediaType_Audio ? "audio" : "video") << ", direction " << mediaDescriptor.mediaDirection << ")");
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      configureMedia(mediaDescriptor, ci->localMediaInfo);
      ci->localEncryptionOptionsConfigured = true;
      configureMedia(mediaDescriptor, ci->configuredLocalMediaInfo);
   }
   else
   {
      ErrLog(<< "CreationInfo not found in SipAVConversationManagerInterface::configureMediaImpl() for conversation " << conversation << "!");
   }
   return kSuccess;
}

void SipAVConversationManagerInterface::configureMedia(const MediaInfo& media, cpc::vector<MediaInfo>& mediaList)
{
   bool found = false;
   for (cpc::vector<MediaInfo>::iterator it = mediaList.begin(); it != mediaList.end(); ++it)
   {
      if (it->mediaType == media.mediaType)
      {
         found = true;
         (*it) = media;
      }
   }
   if (!found)
   {
      mediaList.push_back(media);
   }
}

int SipAVConversationManagerInterface::setAnonymousMode(SipConversationHandle conversation, unsigned int anonymousMode)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setAnonymousModeImpl, this, conversation, anonymousMode));
   return kSuccess;
}

int SipAVConversationManagerInterface::setAnonymousModeImpl(SipConversationHandle conversation, unsigned int anonymousMode)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      ci->anonymousMode = anonymousMode;
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::setBestEffortMediaEncryption(SipConversationHandle conversation, bool enabled)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setBestEffortMediaEncryptionImpl, this, conversation, enabled));
   return kSuccess;
}

int SipAVConversationManagerInterface::setBestEffortMediaEncryptionImpl(SipConversationHandle conversation, bool enabled)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      ci->bestEffortMediaEncryption = enabled;
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::setMediaCryptoSuites(SipConversationHandle conversation, cpc::vector<MediaCryptoSuite> cryptoSuites)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setMediaCryptoSuitesImpl, this, conversation, cryptoSuites));
   return kSuccess;
}

int SipAVConversationManagerInterface::setMediaCryptoSuitesImpl(SipConversationHandle conversation, cpc::vector<MediaCryptoSuite> cryptoSuites)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      for (cpc::vector<MediaInfo>::iterator it = ci->localMediaInfo.begin(); it != ci->localMediaInfo.end(); ++it)
      {
         DebugLog(<< "SipAVConversationManagerInterface::setMediaCryptoSuitesImpl(): CRYPTO Suites size: " << cryptoSuites.size());
         it->mediaEncryptionOptions.mediaCryptoSuites = cryptoSuites;
      }
      for (cpc::vector<MediaInfo>::iterator it = ci->configuredLocalMediaInfo.begin(); it != ci->configuredLocalMediaInfo.end(); ++it)
      {
         it->mediaEncryptionOptions.mediaCryptoSuites = cryptoSuites;
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::setCryptoSuitesForMedia(SipConversationHandle conversation, CPCAPI2::SipConversation::MediaType mediaType, cpc::vector<MediaCryptoSuite> cryptoSuites)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setCryptoSuitesForMediaImpl, this, conversation, mediaType, cryptoSuites));
   return kSuccess;
}

int SipAVConversationManagerInterface::setCryptoSuitesForMediaImpl(SipConversationHandle conversation, CPCAPI2::SipConversation::MediaType mediaType, cpc::vector<MediaCryptoSuite> cryptoSuites)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      for (cpc::vector<MediaInfo>::iterator it = ci->localMediaInfo.begin(); it != ci->localMediaInfo.end(); ++it)
      {
         if (it->mediaType == mediaType)
         {
            DebugLog(<< "SipAVConversationManagerInterface::setCryptoSuitesForMediaImpl(): CRYPTO Suites size: " << cryptoSuites.size() << " for media-type: " << mediaType);
            it->mediaEncryptionOptions.mediaCryptoSuites = cryptoSuites;
         }
      }
      for (cpc::vector<MediaInfo>::iterator it = ci->configuredLocalMediaInfo.begin(); it != ci->configuredLocalMediaInfo.end(); ++it)
      {
         if (it->mediaType == mediaType)
         {
            it->mediaEncryptionOptions.mediaCryptoSuites = cryptoSuites;
         }
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::setFromAddress(SipConversationHandle conversation, const cpc::string& fromAddress)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setFromAddressImpl, this, conversation, fromAddress));
   return kSuccess;
}

int SipAVConversationManagerInterface::setFromAddressImpl(SipConversationHandle conversation, const cpc::string& fromAddress)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      ci->fromAddress = fromAddress;
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::addHeader(SipConversationHandle conversation, const SipHeader& header)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::addHeaderImpl, this, conversation, header));
   return kSuccess;
}

int SipAVConversationManagerInterface::addHeaderImpl(SipConversationHandle conversation, const SipHeader& header)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      ci->customHeaders.push_back(header);
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::start(SipConversationHandle conversation)
{
   DebugLog(<< "SipAVConversationManagerInterface::start(" << conversation << ")");
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::startImpl, this, conversation));
   return kSuccess;
}

void SipAVConversationManagerInterface::logDtmfPrefs(const std::string& msg, SipAVConversationManagerImpl& acct)
{
   std::ostringstream ss;

   std::vector<DtmfMode> dtmfPrefs = acct.dtmfPreferences();
   for (std::vector<DtmfMode>::iterator it = dtmfPrefs.begin(); it != dtmfPrefs.end(); ++it)
   {
      ss << *it;
   }

   InfoLog(<< "DTMF preference at " << msg << ": " << ss.str());
}

struct CodecSpec
{
   CodecSpec(const resip::Data& plname_, int plrate_, unsigned int priority_) : plname(plname_), plrate(plrate_), priority(priority_) {}
   resip::Data plname;
   int plrate = -1;
   unsigned int priority = 0;
};

int SipAVConversationManagerInterface::startImpl(SipConversationHandle conversation)
{
   InfoLog(<< "SipAVConversationManagerInterface::startImpl(" << conversation << ")");
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         SipAVConversationManagerImpl* conversationManager = itAcct->second;

         if (ci->bestEffortMediaEncryption)
         {
            // Best effort media encryption has been selected

            // Check the state of the feature
            if (ci->bestEffortMediaEncryptionState == BestEffortMediaEncryptionState_Initial)
            {
               // No call has been attempted yet

               // Set the encryption to SAVP for all specified medias
               for (cpc::vector<MediaInfo>::iterator it = ci->localMediaInfo.begin(); it != ci->localMediaInfo.end(); ++it)
               {
                  it->mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_SDES_Encrypted;
                  it->mediaEncryptionOptions.secureMediaRequired = true;

                  // Crypto suite gets initialized from configuration, don't override
                  DebugLog(<< "SipAVConversationManagerInterface::startImpl: CRYPTO Suites size: " << it->mediaEncryptionOptions.mediaCryptoSuites.size() << " for media-type: " << it->mediaType);
               }

               // Get ready to make a SAVP call
               ci->bestEffortMediaEncryptionState = BestEffortMediaEncryptionState_SAVP_Call;
            }
            else if (ci->bestEffortMediaEncryptionState == BestEffortMediaEncryptionState_SAVP_Call_Rejected)
            {
               // SAVP call failed

               // Now set the encryption to AVP for all specified medias
               for (cpc::vector<MediaInfo>::iterator it = ci->localMediaInfo.begin(); it != ci->localMediaInfo.end(); ++it)
               {
                  it->mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_Unencrypted;
                  it->mediaEncryptionOptions.secureMediaRequired = false;
               }

               // Get ready to make a AVP call
               ci->bestEffortMediaEncryptionState = BestEffortMediaEncryptionState_AVP_Call;
            }
         }

         SipAccount::SipAccountImpl* acct = mAccountIf->getAccountImpl(ci->account);
         if (!acct->isDisabling())
         {

            recon::ConversationManager::MediaAttributes mediaAttribs(ReconConversationManagerImpl::toReconMediaAttribs(ci->localMediaInfo, ci->account));

            mediaAttribs.max_nack_list_size = ci->videoNackSettings.maxNackListSize;
            mediaAttribs.max_packet_age_to_nack = ci->videoNackSettings.maxPacketAgeToNack;
            mediaAttribs.max_incomplete_time_ms = ci->videoNackSettings.maxIncompleteTimeMs;
            mediaAttribs.nack_history_size_sender = ci->videoNackSettings.nackHistorySizeSender;
            mediaAttribs.videoPacketRedundancyFactor = mVideoPacketRedundancyFactor;
            mediaAttribs.receiveTmmbrEnabled = mReceiveTmmbrEnabled;

            const CPCAPI2::SipConversation::DtmfMode& mode(conversationManager->dtmfPreferences()[ 0 ] );
            if( mode == DtmfMode_RFC2833          || mode == DtmfMode_RFC2833_InBand ||
                mode == DtmfMode_RFC2833_SIP_INFO || mode == DtmfMode_Everything )
               conversationManager->recon()->setOfferTelephoneEvent( true );
            else
               conversationManager->recon()->setOfferTelephoneEvent( false );

            logDtmfPrefs("startImpl", *conversationManager);

            recon::ConversationProfileHandle convProfile = conversationManager->recon()->getDefaultConversationProfile();
            resip::NameAddr fromNameAddr;
            if (!ci->fromAddress.empty())
            {
               // Override the default From: address to use in outgoing INVITEs
               if (ResipConv::stringToAddr(ci->fromAddress, fromNameAddr))
               {
                  recon::ConversationProfileHandle clone = conversationManager->recon()->cloneConversationProfile(convProfile);
                  resip::SharedPtr<recon::ConversationProfile> clonedProfile = conversationManager->recon()->getConversationProfile(clone);
                  clonedProfile->setDefaultFrom(fromNameAddr);
                  convProfile = clone;
               }
            }

            std::vector<CodecSpec> forcedAudioCodecs;
            std::vector<CodecSpec> forcedVideoCodecs;
            const cpc::vector<MediaInfo>& localMi = ci->localMediaInfo;
            cpc::vector<MediaInfo>::const_iterator itLocalMi = localMi.begin();
            for (; itLocalMi != localMi.end(); ++itLocalMi)
            {
               if (itLocalMi->mediaType == MediaType_Audio)
               {
                  resip::Data plnameData(itLocalMi->audioCodec.plname);
                  if (resip::Data(itLocalMi->audioCodec.plname).size() > 0)
                  {
                     CodecSpec c(plnameData, itLocalMi->audioCodec.plfreq, itLocalMi->audioCodec.priority);
                     forcedAudioCodecs.push_back(c);
                  }
               }
               else if (itLocalMi->mediaType == MediaType_Video)
               {
                  resip::Data plnameData(itLocalMi->videoCodec.plName);
                  if (resip::Data(itLocalMi->videoCodec.plName).size() > 0)
                  {
                     CodecSpec c(plnameData, 90000, itLocalMi->videoCodec.priority);
                     forcedVideoCodecs.push_back(c);
                  }
               }
            }

            std::shared_ptr<webrtc_recon::CpsiCodec> forcedAudioCodec;
            bool forcedAudioCodecOrigEnabled = false;
            unsigned int forcedAudioCodecOrigPriority = 0;
            std::shared_ptr<webrtc_recon::CpsiCodec> forcedVideoCodec;
            bool forcedVideoCodecOrigEnabled = false;
            unsigned int forcedVideoCodecOrigPriority = 0;
            if (forcedAudioCodecs.size() > 0 || forcedVideoCodecs.size() > 0)
            {
               std::shared_ptr<webrtc_recon::CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<webrtc_recon::CodecFactoryImpl>(mMediaIf->media_stack_ptr()->codecFactory());
               std::vector<CodecSpec>::const_iterator itFc = forcedAudioCodecs.begin();
               for (; itFc != forcedAudioCodecs.end(); ++itFc)
               {
                  forcedAudioCodec = codecFactory->getAudioCodec(itFc->plname, itFc->plrate);
                  if (forcedAudioCodec)
                  {
                     forcedAudioCodecOrigEnabled = forcedAudioCodec->enabled;
                     forcedAudioCodecOrigPriority = forcedAudioCodec->priority;
                     forcedAudioCodec->enabled = true;
                     forcedAudioCodec->priority = itFc->priority;
                  }
               }
               itFc = forcedVideoCodecs.begin();
               for (; itFc != forcedVideoCodecs.end(); ++itFc)
               {
                  forcedVideoCodec = codecFactory->getVideoCodec(itFc->plname);
                  if (forcedVideoCodec)
                  {
                     forcedVideoCodecOrigEnabled = forcedVideoCodec->enabled;
                     forcedVideoCodecOrigPriority = forcedVideoCodec->priority;
                     forcedVideoCodec->enabled = true;
                     forcedVideoCodec->priority = itFc->priority;
                  }
               }
            }

            // Make sure the c= line in our SDP offer is correct
            resip::SharedPtr<recon::ConversationProfile> convProfileInst = conversationManager->recon()->getConversationProfile(convProfile);
            conversationManager->recon()->updateConversationProfile(convProfileInst);

            recon::ConversationHandle reconConv = conversationManager->recon()->createConversation(convProfile);
            conversationManager->recon()->addParticipant(reconConv, conversationManager->recon()->getLocalParticipant());

            ci->answerMode.mode = (convProfileInst->answerModeEnabled() ? (convProfileInst->answerModeManual() ? SipConversation::AnswerMode_Manual : SipConversation::AnswerMode_Auto) : SipConversation::AnswerMode_Disabled);
            ci->answerMode.privileged = convProfileInst->answerModePrivileged();
            ci->answerMode.required = convProfileInst->answerModeRequired();

            recon::ConversationManager::CallAttributes callAttribs;
            callAttribs.answerModeAttributes = ReconConversationManagerImpl::toReconAnswerMode(ci->answerMode);
            callAttribs.isAnonymous = (ci->anonymousMode > 0);
            callAttribs.forceTarget = ci->forceTarget.c_str();
            callAttribs.replacesDialogId = ci->replacesDialogId;
            callAttribs.joinDialogId = ci->joinDialogId;
            for (cpc::vector<Parameter>::const_iterator it = ci->joinParams.begin(); it != ci->joinParams.end(); it++)
            {
               callAttribs.joinParams[it->name.c_str()] = it->value.c_str();
            }

            if (ci->targetAddresses.empty())
            {
               conversationManager->fireError(conversation, "Cannot start conversation. No participants have been added");
               return kSuccess;
            }

            resip::NameAddr targetNameAddr = ci->targetAddresses[0];
            // transform address if transformer defined
            AddressTransformer* transformer = mPhone->getAddressTransformer();
            if (transformer != NULL)
            {
               cpc::string transformedAddress = "";
               AddressTransformationContext context;
               context.addressUsageType = AddressUsageType_SipConversation;

               if (!acct)
               {
                  mAccountIf->fireError("Invalid account handle for SipAVConversationManager::start");
                  return kError;
               }

               context.registrationDomain = acct->getSettings().domain;

               if (transformer->applyTransformation(targetNameAddr.uri().getAORKeepCase(true).data(), context, transformedAddress) == kSuccess)
               {
                  ResipConv::stringToAddr(transformedAddress, targetNameAddr);
               }
            }

            // Android and iOS specific
            if(ci->callKitMode)
            {
               webrtc::AudioDeviceModule* adm = mMediaIf->media_stack()->voe_base()->audio_device();
               adm->ActivatePlayAndRecordMode(true);
            }

            // Fire an internal event here
            ConversationInitiatedEvent arg;
            arg.account = ci->account;
            arg.conversationType = ConversationType_Outgoing;
            arg.remoteAddress = targetNameAddr.uri().getAOR(false).data();
            conversationManager->fireCallInitiated(conversation, arg);

            ci->reconConversation = reconConv;

            conversationManager->countNewCall();

            recon::ParticipantHandle remoteParticipant = conversationManager->recon()->createRemoteParticipant(
               reconConv,
               targetNameAddr,
               mediaAttribs,
               callAttribs);

            conversationManager->recon()->addParticipant(reconConv, remoteParticipant);

            ci->reconRemoteParticipant = remoteParticipant;
            ci->reconOriginalRemoteParticipant = remoteParticipant;

            // reset the state of any forced codecs to original
            if (forcedAudioCodec)
            {
               forcedAudioCodec->enabled = forcedAudioCodecOrigEnabled;
               forcedAudioCodec->priority = forcedAudioCodecOrigPriority;
            }
            if (forcedVideoCodec)
            {
               forcedVideoCodec->enabled = forcedVideoCodecOrigEnabled;
               forcedVideoCodec->priority = forcedVideoCodecOrigPriority;
            }

            // now fire the event
            const resip::NameAddr target = (!ci->redirectedAddrs.empty() ? ci->redirectedAddrs.front() : targetNameAddr);

            // we only fire onNewConversation for the original call, not for new forks
            NewConversationEvent args;
            args.conversationState = ConversationState_LocalOriginated;
            args.account = ci->account;
            args.conversationType = ConversationType_Outgoing;
            args.relatedConversation = 0;
            args.conversationToJoin = 0;
            args.conversationToReplace = 0;
            args.localMediaInfo = ci->localMediaInfo;
            args.answerMode = ci->answerMode;

            if (!fromNameAddr.uri().getAor().empty())
            {
               // from header was overriden, see above
               args.localAddress = fromNameAddr.uri().getAorNoReally().c_str();
               args.localDisplayName = fromNameAddr.displayName().c_str();
            }

            args.remoteAddress = CharEncodingHelper::unEscape(target.uri().getAorNoReally()).c_str();
            args.remoteDisplayName = target.displayName().c_str();

            conversationManager->fireNewConvEvent(conversation, args);
            ci->lastRemoteAddr = { args.remoteAddress, args.remoteDisplayName };
         }
         else
         {
            ErrorEvent errArgs;
            errArgs.errorText = "Call creation attempted while account was disabling.";
            conversationManager->fireEvent(cpcEvent(SipConversationHandler, onError), conversation, errArgs);
            ErrLog(<< "SipAVConversationManagerInterface::startImpl: Call creation attempted while account was disabling." );
            return kError;
         }
      }
      return kSuccess;
   }
   else
   {
      return kError;
   }

   return kSuccess;
}

int SipAVConversationManagerInterface::hold(SipConversationHandle conversation)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::holdImpl, this, conversation));
   return kSuccess;
}

int SipAVConversationManagerInterface::holdImpl(SipConversationHandle conversation)
{
   InfoLog(<< "SipConversationManager::hold(" << conversation << ")");
   /* Future note: for Music On Hold
    * ReconConversationManagerImpl::onParticipantMediaChanged depends on numLocal and numRemote participants = 0 to detect
    * localHold. Make sure this works when MediaResourceParticipant is added
    */
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         SipAVConversationManagerImpl* acct = itAcct->second;
         DebugLog(<< "SipConversationManager::hold(): conversation: " << conversation << " recon conversation: " << ci->reconConversation << " recon local-participant: " << acct->recon()->getLocalParticipant() << " recon remote-participant: " << ci->reconRemoteParticipant);
         acct->recon()->removeParticipant(ci->reconConversation, acct->recon()->getLocalParticipant());
         acct->recon()->removeParticipant(ci->reconConversation, ci->reconRemoteParticipant);
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::unhold(SipConversationHandle conversation)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::unholdImpl, this, conversation));
   return kSuccess;
}

int SipAVConversationManagerInterface::unholdImpl(SipConversationHandle conversation)
{
   InfoLog(<< "SipConversationManager::unhold(" << conversation << ")");
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         SipAVConversationManagerImpl* acct = itAcct->second;

         acct->recon()->addParticipant(ci->reconConversation, ci->reconRemoteParticipant);
         acct->recon()->addParticipant(ci->reconConversation, acct->recon()->getLocalParticipant());
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::sendMediaChangeRequest(SipConversationHandle conversation)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::sendMediaChangeRequestImpl, this, conversation));
   return kSuccess;
}

int SipAVConversationManagerInterface::sendMediaChangeRequestImpl(SipConversationHandle conversation)
{
   InfoLog(<< "sendMediaChangeRequest(" << conversation << ")");
// !jjg! TODO: disallow this if we are on hold
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         SipAVConversationManagerImpl* acct = itAcct->second;
         recon::ConversationManager::MediaAttributes mediaAttribs(ReconConversationManagerImpl::toReconMediaAttribs(ci->localMediaInfo, ci->account));
         acct->recon()->updateMedia(ci->reconRemoteParticipant, mediaAttribs, true);
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::end(SipConversationHandle conversation)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::endImpl, this, conversation));
   return kSuccess;
}

int SipAVConversationManagerInterface::endImpl(SipConversationHandle conversation)
{
   InfoLog(<< "SipConversationManager::end(" << conversation << ")");
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      // make sure any currently recording files are properly closed, the app may want to rename those files
      std::set<Recording::RecorderHandle> temp = ci->recordings;  // use a copy since the original will be changed by the call below
      for (std::set<Recording::RecorderHandle>::iterator it = temp.begin(); it != temp.end(); ++it)
      {
         this->removeFromRecorderImpl(conversation, *it);
      }

      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         SipAVConversationManagerImpl* acct = itAcct->second;
         acct->recon()->destroyRelatedConversations(conversation);
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::getActiveCallCountForStarcodeHandling()
{
   SipConversationHandle activeCall = 0;
   SipConversationStateImpl* callStateMgr = dynamic_cast<SipConversationStateImpl*>(SipConversationStateManager::getInterface(this));
   cpc::vector<SipConversationState> conversationStates;
   callStateMgr->getStateAllActiveConversations(conversationStates);

   int activeCalls = 0;
   for (cpc::vector<SipConversationState>::iterator i = conversationStates.begin(); i != conversationStates.end(); ++i)
   {
      // sometimes the hold has been requested but not processed by the remote end, but we can catch this scenario by checking 
      // the number of participants since we remove them in order to initiate a local hold
      SipAVConversationManagerImpl* acct = SipAVConversationManagerInterface::getConvImpl(i->conversation);
      bool localHoldPendingOrHeld = acct && acct->recon()->getRemoteParticipantCount(i->conversation) == 0;

      if ((i->conversationState == ConversationState_Connected) && (i->localHold == false) && (localHoldPendingOrHeld == false))
      {
         activeCalls++;
      }
   }

   DebugLog(<< "SipConversationManager::getActiveCallCountForStarcodeHandling(): total call-states: " << conversationStates.size() << " active-calls: " << activeCalls);
   return activeCalls;
}

bool SipAVConversationManagerInterface::isStarcodeHandlingRequired()
{
   // A starcode session is only required if there is only one active call
   int activeCalls = getActiveCallCountForStarcodeHandling();
   bool starcodeHandlingRequired = false;
   if (activeCalls == 1)
   {
      starcodeHandlingRequired = true;
   }

   DebugLog(<< "SipConversationManager::isStarcodeHandlingRequired(): active-calls: " << activeCalls << " starcodeHandlingRequired: " << starcodeHandlingRequired);
   return starcodeHandlingRequired;
}

SipConversationHandle SipAVConversationManagerInterface::endCallsNotApplicableForStarcodeNetworkChange(cpc::vector<SipConversationHandle>& endedCalls)
{
   // Destroy all calls except for the active call and also destroy all calls if there is
   // more than one active call, as would be the case with a local conference.
   SipConversationHandle activeCall = 0;
   SipConversationStateImpl* callStateMgr = dynamic_cast<SipConversationStateImpl*>(SipConversationStateManager::getInterface(this));
   cpc::vector<SipConversationState> conversationStates;
   callStateMgr->getStateAllActiveConversations(conversationStates);

   int activeCalls = getActiveCallCountForStarcodeHandling();

   for (cpc::vector<SipConversationState>::iterator i = conversationStates.begin(); i != conversationStates.end(); ++i)
   {
      if (i->conversationState == ConversationState_Ended)
      {
         StackLog(<< "SipConversationManager::endCallsNotApplicableForStarcodeNetworkChange(): ignoring call: " << i->conversation << " as it has already been ended");
         continue;
      }

      // sometimes the hold has been requested but not processed by the remote end, but we can catch this scenario by checking 
      // the number of participants since we remove them in order to initiate a local hold
      SipAVConversationManagerImpl* acct = SipAVConversationManagerInterface::getConvImpl(i->conversation);
      bool localHoldPendingOrHeld = acct && acct->recon()->getRemoteParticipantCount(i->conversation) == 0;

      if (activeCalls > 1)
      {
         // Ending the calls as starcode handling not supported in conference mode.
         DebugLog(<< "SipConversationManager::endCallsNotApplicableForStarcodeNetworkChange(): ending call: " << i->conversation << " as more than one call is active");
         endImpl(i->conversation);
         endedCalls.push_back(i->conversation);
      }
      else if ((i->conversationState == ConversationState_Connected) && (i->localHold == false) && (localHoldPendingOrHeld == false))
      {
         DebugLog(<< "SipConversationManager::endCallsNotApplicableForStarcodeNetworkChange(): active call: " << i->conversation);
         activeCall = i->conversation;
      }
      else
      {
         DebugLog(<< "SipConversationManager::endCallsNotApplicableForStarcodeNetworkChange(): ending inactive call: " << i->conversation << " in state: " << i->conversationState);
         endImpl(i->conversation);
         endedCalls.push_back(i->conversation);
      }
   }

   InfoLog(<< "SipConversationManager::endCallsNotApplicableForStarcodeNetworkChange(): total call-states: " << conversationStates.size() << " active-calls: " << activeCalls << " ended-calls: " << endedCalls.size() << " surviving call (not-ended): " << activeCall);
   return activeCall;
}

int SipAVConversationManagerInterface::redirect(SipConversationHandle conversation, const cpc::string& targetAddress, const cpc::string& reason)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::redirectImpl, this, conversation, targetAddress, reason));
   return kSuccess;
}

int SipAVConversationManagerInterface::redirectImpl(SipConversationHandle conversation, const cpc::string& targetAddress, const cpc::string& reason)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         SipAVConversationManagerImpl* acct = itAcct->second;
         resip::NameAddr targetNameAddr;
         if (ResipConv::stringToAddr(targetAddress, targetNameAddr))
         {
            acct->recon()->redirectParticipant(ci->reconRemoteParticipant, targetNameAddr, reason.c_str());
         }
         else
         {
            return kError;
         }
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::sendRingingResponse(SipConversationHandle conversation)
{
   DebugLog(<< "SipConversationManager::" << __FUNCTION__ << "(" << conversation << ")");

   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::sendRingingResponseImpl, this, conversation));
   return kSuccess;
}

int SipAVConversationManagerInterface::sendRingingResponseImpl(SipConversationHandle conversation)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         SipAVConversationManagerImpl* acct = itAcct->second;
         acct->recon()->alertParticipant(ci->reconRemoteParticipant, false);

         if (ci->callKitMode)
         {
            webrtc::AudioDeviceModule* adm = mMediaIf->media_stack()->voe_base()->audio_device();
            adm->ActivatePlayAndRecordMode(true);
         }
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::sendInfo(SipConversationHandle conversation, const cpc::string& body)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::sendInfoImpl, this, conversation, body));
   return kSuccess;
}

int SipAVConversationManagerInterface::sendInfoImpl(SipConversationHandle conversation, const cpc::string& body)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         SipAVConversationManagerImpl* acct = itAcct->second;
         resip::PlainContents content(body.c_str());
         acct->recon()->sendInfo(ci->reconRemoteParticipant, content);
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::reject(SipConversationHandle conversation, unsigned int rejectReason)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::rejectImpl, this, conversation, rejectReason));
   return kSuccess;
}

int SipAVConversationManagerInterface::rejectImpl(SipConversationHandle conversation, unsigned int rejectReason)
{
   InfoLog(<< "SipConversationManager::reject(" << conversation << ")");
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         SipAVConversationManagerImpl* acct = itAcct->second;
         if (ci->reconRemoteParticipant != 0)
         {
            unsigned int reason = rejectReason;
            if (reason == 0)
            {
               // default reason for RE-INVITE or INVITE respectively
               reason = ci->hasPendingRequest ? 488 : 486;
            }
            acct->recon()->rejectParticipant(ci->reconRemoteParticipant, reason);
            ci->hasPendingRequest = false;
         }
         else
         {
            mAccountIf->fireError("Tried to reject conversation, but no remote participant is available to reject " + cpc::to_string(conversation));
         }
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::provideSdpOffer(SipConversationHandle conversation, const CPCAPI2::SipConversation::SessionDescription& sdpOffer)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::provideSdpOfferImpl, this, conversation, sdpOffer));
   return kSuccess;
}

int SipAVConversationManagerInterface::provideSdpOfferImpl(SipConversationHandle conversation, const CPCAPI2::SipConversation::SessionDescription& sdpOffer)
{
   InfoLog(<< "SipConversationManager::provideSdpOffer(" << conversation << ")");
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         SipAVConversationManagerImpl* acct = itAcct->second;
         resip::ParseBuffer pbSdp(sdpOffer.sdpString, sdpOffer.sdpLen);
         resip::SdpContents resipSdp;
         resipSdp.parse(pbSdp);
         acct->recon()->provideAppSpecifiedOffer(ci->reconRemoteParticipant, resipSdp);
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::provideSdpAnswer(SipConversationHandle conversation, const CPCAPI2::SipConversation::SessionDescription& sdpOffer, const CPCAPI2::SipConversation::SessionDescription& sdpAnswer)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::provideSdpAnswerImpl, this, conversation, sdpOffer, sdpAnswer));
   return kSuccess;
}

int SipAVConversationManagerInterface::provideSdpAnswerImpl(SipConversationHandle conversation, const CPCAPI2::SipConversation::SessionDescription& sdpOffer, const CPCAPI2::SipConversation::SessionDescription& sdpAnswer)
{
   InfoLog(<< "SipConversationManager::provideSdpAnswer(" << conversation << ")");
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         SipAVConversationManagerImpl* acct = itAcct->second;
         resip::ParseBuffer pbSdpOffer(sdpOffer.sdpString, sdpOffer.sdpLen);
         resip::SdpContents resipSdpOffer;
         resipSdpOffer.parse(pbSdpOffer);
         resip::ParseBuffer pbSdpAnswer(sdpAnswer.sdpString, sdpAnswer.sdpLen);
         resip::SdpContents resipSdpAnswer;
         resipSdpAnswer.parse(pbSdpAnswer);
         acct->recon()->provideAppSpecifiedAnswer(ci->reconRemoteParticipant, resipSdpOffer, resipSdpAnswer);
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::setExternalSdpHandlingEnabled(SipConversationHandle conversation, bool enabled)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setExternalSdpHandlingEnabledImpl, this, conversation, enabled));
   return kSuccess;
}

int SipAVConversationManagerInterface::setExternalSdpHandlingEnabledImpl(SipConversationHandle conversation, bool enabled)
{
   InfoLog(<< "SipConversationManager::setExternalSdpHandlingEnabled(" << conversation << ", " << enabled << ")");
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      ci->mediaBypassMode = true;
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::setDtlsSupported(CPCAPI2::SipAccount::SipAccountHandle account, bool dtlsSupported)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setDtlsSupportedImpl, this, account, dtlsSupported));
   return kSuccess;
}


int SipAVConversationManagerInterface::setDtlsSupportedImpl(CPCAPI2::SipAccount::SipAccountHandle account, bool dtlsSupported)
{
   AccountMap::iterator itAcct = mAccountMap->find(account);
   if (itAcct != mAccountMap->end())
   {
      itAcct->second->setDtlsSupported(dtlsSupported);
   }

   return kSuccess;
}

int SipAVConversationManagerInterface::setNackPliEnabled(bool enabled)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setNackPliEnabledImpl, this, enabled));
   return kSuccess;
}

int SipAVConversationManagerInterface::setNackPliEnabledImpl(bool enabled)
{
   AccountMap::iterator itAcct = mAccountMap->begin();
   for (; itAcct != mAccountMap->end(); ++itAcct)
   {
      itAcct->second->recon()->setNackPliSupported(enabled);
   }

   return kSuccess;
}

int SipAVConversationManagerInterface::accept(SipConversationHandle conversation)
{
   DebugLog(<< "SipConversationManager::" << __FUNCTION__ << "(" << conversation << ")");

   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::acceptImpl, this, conversation));
   return kSuccess;
}

int SipAVConversationManagerInterface::acceptImpl(SipConversationHandle conversation)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         if (!ci->remoteMediaInfo.empty())
         {
            if (ci->bestEffortMediaEncryption || !ci->localEncryptionOptionsConfigured)
            {
               // Set the same media encyption options as the remote endpoint
               MediaEncryptionOptions remoteMediaEncyptionOptions = ci->remoteMediaInfo.begin()->mediaEncryptionOptions;
               for (cpc::vector<MediaInfo>::iterator it = ci->localMediaInfo.begin(); it != ci->localMediaInfo.end(); ++it)
               {
                  it->mediaEncryptionOptions = remoteMediaEncyptionOptions;
               }
               ci->localEncryptionOptionsConfigured = true;
            }
         }

         SipAVConversationManagerImpl* acct = itAcct->second;
         if (ci->hasPendingRequest && ci->reconConversation != 0 && ci->reconRemoteParticipant != 0)
         {
            // this is a re-INVITE that we are responding to
            recon::ConversationManager::MediaAttributes mediaAttribs(ReconConversationManagerImpl::toReconMediaAttribs(ci->localMediaInfo, ci->account));
            acct->recon()->answerParticipant(ci->reconRemoteParticipant, mediaAttribs);
            ci->hasPendingRequest = false;
         }
         else
         {
            if (ci->reconConversation == 0)
            {
               // ensure the default profile has the most up-to-date list of codecs, etc.
               acct->recon()->updateDefaultConversationProfile();

               // this is a new incoming call that we are answering
               recon::ConversationProfileHandle convProfile = acct->recon()->getDefaultConversationProfile();
               recon::ConversationHandle reconConv = acct->recon()->createConversation(convProfile);
               acct->recon()->addParticipant(reconConv, acct->recon()->getLocalParticipant());
               acct->recon()->addParticipant(reconConv, ci->reconRemoteParticipant);

               recon::ConversationManager::MediaAttributes mediaAttribs(ReconConversationManagerImpl::toReconMediaAttribs(ci->localMediaInfo, ci->account));
               mediaAttribs.max_nack_list_size = ci->videoNackSettings.maxNackListSize;
               mediaAttribs.max_packet_age_to_nack = ci->videoNackSettings.maxPacketAgeToNack;
               mediaAttribs.max_incomplete_time_ms = ci->videoNackSettings.maxIncompleteTimeMs;
               mediaAttribs.nack_history_size_sender = ci->videoNackSettings.nackHistorySizeSender;

               acct->recon()->answerParticipant(ci->reconRemoteParticipant, mediaAttribs);

               ci->reconConversation = reconConv;

               if (ci->conversationToReplace != 0)
               {
                  endImpl(ci->conversationToReplace);
               }

               // Fire an internal event here
               ConversationInitiatedEvent arg;
               arg.account          = ci->account;
               arg.conversationType = ConversationType_Incoming;
               arg.remoteAddress    = ci->fromAddress;
               acct->fireCallInitiated(conversation, arg );
            }
            else
            {
               // accept wasn't for an invite or re-invite, probably an SDK mis-use (obelisk-105)
               acct->fireError(conversation, "Accept() called without a pending invite or re-invite");
            }
         }
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::transfer(SipConversationHandle transferTargetConversation, SipConversationHandle transfereeConversation)
{
   DebugLog(<< "SipConversationManager::" << __FUNCTION__ << "(" << transferTargetConversation << ", " << transfereeConversation << ")");
   postToSdkThread(resip::resip_bind(static_cast<int (SipAVConversationManagerInterface::*)(SipConversationHandle,SipConversationHandle,bool)>(&SipAVConversationManagerInterface::transferImpl), this, transferTargetConversation, transfereeConversation, true));
   return kSuccess;
}

int SipAVConversationManagerInterface::transfer(SipConversationHandle transferTargetConversation, SipConversationHandle transfereeConversation, bool endTargetConversationOnSuccess)
{
   DebugLog(<< "SipConversationManager::" << __FUNCTION__ << "(" << transferTargetConversation << ", " << transfereeConversation << ", " << endTargetConversationOnSuccess << ")");
   postToSdkThread(resip::resip_bind(static_cast<int (SipAVConversationManagerInterface::*)(SipConversationHandle,SipConversationHandle,bool)>(&SipAVConversationManagerInterface::transferImpl), this, transferTargetConversation, transfereeConversation, endTargetConversationOnSuccess));
   return kSuccess;
}

int SipAVConversationManagerInterface::transferImpl(SipConversationHandle transferTargetConversation, SipConversationHandle transfereeConversation, bool endTargetConversationOnSuccess)
{
   SipCallCreationInfo* ciTransferTarget = getCreationInfo(transferTargetConversation);
   SipCallCreationInfo* ciTransferee = getCreationInfo(transfereeConversation);

   /*
   StackLog(<< "SipConversationManager::transferImpl(): transfer-target-account: " << ciTransferTarget->account << " conversation: " << ciTransferTarget->reconConversation << " remote-participant: " << ciTransferTarget->reconRemoteParticipant << " original-remote-participant: " << ciTransferTarget->reconOriginalRemoteParticipant << " media-participant: " << ciTransferTarget->reconMediaParticipant << " conversation-to-replace: " << ciTransferTarget->conversationToReplace << " original-conversation: " << ciTransferTarget->originalConversation << " from-address: " << ciTransferTarget->fromAddress << " targetAddress: " << ((ciTransferTarget->targetAddresses.size() > 0) ? ciTransferTarget->targetAddresses[0].uri().getAor().c_str() : ""));

   StackLog(<< "SipConversationManager::transferImpl(): transferee-account: " << ciTransferee->account << " conversation: " << ciTransferee->reconConversation << " remote-participant: " << ciTransferee->reconRemoteParticipant << " original-remote-participant: " << ciTransferee->reconOriginalRemoteParticipant << " media-participant: " << ciTransferee->reconMediaParticipant << " conversation-to-replace: " << ciTransferee->conversationToReplace << " original-conversation: " << ciTransferee->originalConversation << " from-address: " << ciTransferee->fromAddress << " targetAddress: " << ((ciTransferee->targetAddresses.size() > 0) ? ciTransferee->targetAddresses[0].uri().getAor().c_str() : ""));
   */

   if (ciTransferTarget && ciTransferee)
   {
      SipAVConversationManagerImpl* transferTargetConversationImpl = NULL;
      SipAVConversationManagerImpl* transfereeConversationImpl = NULL;

      AccountMap::iterator i = mAccountMap->find(ciTransferTarget->account);
      if (i != mAccountMap->end())
      {
         transferTargetConversationImpl = i->second;

         AccountMap::iterator j = mAccountMap->find(ciTransferee->account);
         if (j != mAccountMap->end())
         {
            transfereeConversationImpl = j->second;
         }

         if (transferTargetConversationImpl && transfereeConversationImpl)
         {
            DebugLog(<< "SipConversationManager::transferImpl(): transfer-target participant handle: " << ciTransferTarget->reconRemoteParticipant
               << " transfer-target account: " << ciTransferTarget->account <<  " transfer-target conversation impl: " <<  transferTargetConversationImpl
               << " transferee participant handle: " << ciTransferee->reconRemoteParticipant << " transferee-account: " << ciTransferee->account
               << " transferee conversation impl: " << transfereeConversationImpl);
            ciTransferee->shouldEndOriginalCallAfterAttendedTransferSuccess = endTargetConversationOnSuccess;
            transferTargetConversationImpl->recon()->redirectToParticipant(ciTransferee->reconRemoteParticipant, ciTransferTarget->reconRemoteParticipant, transfereeConversationImpl->recon());
         }
         else
         {
            InfoLog(<< "SipConversationManager::transferImpl(): transfer failed as could not retrieve transfer target or transferee conversation manager, transfer-target participant handle: "
               << ciTransferTarget->reconRemoteParticipant << " transfer-target account: " << ciTransferTarget->account <<  " transfer-target conversation impl: "
               <<  transferTargetConversationImpl << " transfereeConversation: " << transfereeConversation << " transferee participant handle: "
               << ciTransferee->reconRemoteParticipant << " transferee-account: " << ciTransferee->account << " transferee conversation impl: " << transfereeConversationImpl);
         }
      }
      else
      {
         InfoLog(<< "SipConversationManager::transferImpl(): transfer failed as could not retrieve transfer-target conversation for account handle: "
            << ciTransferTarget->account << " transferTargetConversation: " << transferTargetConversation << " transfereeConversation: " << transfereeConversation);
      }
   }
   else
   {
      InfoLog(<< "SipConversationManager::transferImpl(): transfer failed as could not retrieve sip call creation info, transferTargetConversation: "
         << transferTargetConversation << " transfer-target: " << ciTransferTarget << " transfereeConversation: " << transfereeConversation << " transferee: " << ciTransferee);
   }

   return kSuccess;
}

int SipAVConversationManagerInterface::transfer(SipConversationHandle conversation, const cpc::string& targetAddress)
{
   DebugLog(<< "SipConversationManager::" << __FUNCTION__ << "(" << conversation << ", " << targetAddress << ")");
   postToSdkThread(resip::resip_bind(static_cast<int (SipAVConversationManagerInterface::*)(SipConversationHandle,const cpc::string&)>(&SipAVConversationManagerInterface::transferImpl), this, conversation, targetAddress));
   return kSuccess;
}

int SipAVConversationManagerInterface::transferImpl(SipConversationHandle conversation, const cpc::string& targetAddress)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         SipAVConversationManagerImpl* acct = itAcct->second;
         resip::NameAddr targetNameAddr;
         if (!ResipConv::stringToAddr(targetAddress, targetNameAddr))
         {
            acct->fireError(conversation, "Failed to parse transfer target URI '" + targetAddress + "'");
            return kSuccess;
         }
         acct->recon()->redirectParticipant(ci->reconRemoteParticipant, targetNameAddr, "");
      }
      else
      {
         WarningLog(<< "Didn't find account for transfer");
      }
   }
   else
   {
      WarningLog(<< "Didn't find conversation for handle " << conversation);
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::acceptIncomingTransferRequest(SipConversationHandle transferTargetConversation)
{
   DebugLog(<< "SipConversationManager::" << __FUNCTION__ << "(" << transferTargetConversation << ")");
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::acceptIncomingTransferRequestImpl, this, transferTargetConversation));
   return kSuccess;
}

int SipAVConversationManagerInterface::acceptIncomingTransferRequestImpl(SipConversationHandle transferTargetConversation)
{
   SipCallCreationInfo* ci = getCreationInfo(transferTargetConversation);
   if (ci != NULL)
   {
      AccountMap::iterator itAcct = mAccountMap->find(ci->account);
      if (itAcct != mAccountMap->end())
      {
         SipAVConversationManagerImpl* acct = itAcct->second;

         // Although the method name is called
         // "acceptINCOMINGTransferRequest..." it actually results in an
         // OUTBOUND call. So, we have to go through the same procedure here,
         // with respect to the bestEffortMediaEncryption flag.
         if (ci->bestEffortMediaEncryption)
         {
            // Best effort media encryption has been selected

            // Check the state of the feature
            if (ci->bestEffortMediaEncryptionState == BestEffortMediaEncryptionState_Initial)
            {
               // No call has been attempted yet

               // Set the encryption to SAVP for all specified medias
               for (cpc::vector<MediaInfo>::iterator it = ci->localMediaInfo.begin(); it != ci->localMediaInfo.end(); ++it)
               {
                  it->mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_SDES_Encrypted;
                  it->mediaEncryptionOptions.secureMediaRequired = true;

                  // Crypto suite gets initialized from configuration, don't override
                  DebugLog(<< "SipAVConversationManagerInterface::acceptIncomingTransferRequestImpl: CRYPTO Suites size: " << it->mediaEncryptionOptions.mediaCryptoSuites.size() << " for media-type: " << it->mediaType);
               }

               // Get ready to make a SAVP call
               ci->bestEffortMediaEncryptionState = BestEffortMediaEncryptionState_SAVP_Call;
            }
            else if (ci->bestEffortMediaEncryptionState == BestEffortMediaEncryptionState_SAVP_Call_Rejected)
            {
               // SAVP call failed

               // Now set the encryption to AVP for all specified medias
               for (cpc::vector<MediaInfo>::iterator it = ci->localMediaInfo.begin(); it != ci->localMediaInfo.end(); ++it)
               {
                  it->mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_Unencrypted;
                  it->mediaEncryptionOptions.secureMediaRequired = false;

                  // Crypto suite gets initialized from configuration, don't override
               }

               // Get ready to make a AVP call
               ci->bestEffortMediaEncryptionState = BestEffortMediaEncryptionState_AVP_Call;
            }
         }

         if (ci->callKitMode)
         {
            webrtc::AudioDeviceModule* adm = mMediaIf->media_stack()->voe_base()->audio_device();
            adm->ActivatePlayAndRecordMode(true);
         }

         // ensure the default profile has the most up-to-date list of codecs, etc.
         acct->recon()->updateDefaultConversationProfile();

         recon::ConversationProfileHandle convProfile = acct->recon()->getDefaultConversationProfile();
         recon::ConversationHandle reconConv = acct->recon()->createConversation(convProfile);
         ci->reconConversation = reconConv;
         acct->recon()->addParticipant(reconConv, acct->recon()->getLocalParticipant());
         acct->recon()->addParticipant(reconConv, ci->reconRemoteParticipant);

         // the media spec'd by setMedia for the transfer target conv
         recon::ConversationManager::MediaAttributes mediaAttribs(ReconConversationManagerImpl::toReconMediaAttribs(ci->localMediaInfo, ci->account));
         mediaAttribs.max_nack_list_size = ci->videoNackSettings.maxNackListSize;
         mediaAttribs.max_packet_age_to_nack = ci->videoNackSettings.maxPacketAgeToNack;
         mediaAttribs.max_incomplete_time_ms = ci->videoNackSettings.maxIncompleteTimeMs;
         mediaAttribs.nack_history_size_sender = ci->videoNackSettings.nackHistorySizeSender;

         acct->recon()->answerParticipant(ci->reconRemoteParticipant, mediaAttribs);

         // now fire the event
         const resip::NameAddr target = (!ci->targetAddresses.empty() ? ci->targetAddresses[0] : resip::NameAddr("sip:<EMAIL>"));

         // fire onNewConversation
         NewConversationEvent args;
         args.conversationState = ConversationState_LocalOriginated;
         args.account = ci->account;
         args.conversationType = ConversationType_Outgoing;
         args.relatedConversation = 0;
         args.conversationToJoin = 0;
         args.conversationToReplace = 0;
         args.localMediaInfo = ci->localMediaInfo;
         args.answerMode = ci->answerMode;

         args.remoteAddress = CharEncodingHelper::unEscape(target.uri().getAorNoReally()).c_str();
         args.remoteDisplayName = target.displayName().c_str();

         acct->fireNewConvEvent(transferTargetConversation, args);

         ci->lastRemoteAddr = { args.remoteAddress, args.remoteDisplayName };
         acct->countNewCall();
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::rejectIncomingTransferRequest(SipConversationHandle transferTargetConversation)
{
   return kSuccess;
}

int SipAVConversationManagerInterface::setDtmfMode(CPCAPI2::SipAccount::SipAccountHandle account, unsigned int ordinal, CPCAPI2::SipConversation::DtmfMode dtmfMode)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setDtmfModeImpl, this, account, ordinal, dtmfMode));
   return kSuccess;
}

int SipAVConversationManagerInterface::setDtmfModeImpl(CPCAPI2::SipAccount::SipAccountHandle account, unsigned int ordinal, CPCAPI2::SipConversation::DtmfMode dtmfMode)
{
   AccountMap::iterator itAcct = mAccountMap->find(account);
   if (itAcct != mAccountMap->end())
   {
      SipAVConversationManagerImpl* acct = itAcct->second;
      if (ordinal == 0xffffffff || ordinal >= DtmfMode_Everything)
      {
         acct->dtmfPreferences().clear();
         acct->dtmfPreferences().resize(( int ) DtmfMode_Everything );
      }
      else
      {
         acct->dtmfPreferences()[ ordinal ] = dtmfMode;
      }

      logDtmfPrefs("setDtmfModeImpl", *acct);
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::startDtmfTone(SipConversationHandle conversation, unsigned int toneId, bool playLocally)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::startDtmfToneImpl, this, conversation, toneId, playLocally));
   return kSuccess;
}

int SipAVConversationManagerInterface::startDtmfToneImpl(SipConversationHandle conversation, unsigned int toneId, bool playLocally)
{
   // Get the Account, we need to fetch the DTMF settings.
   SipCallCreationInfo* ci = getCreationInfo( conversation );
   if( ci == NULL )
      return kError;

   AccountMap::iterator itAcct = mAccountMap->find( ci->account );
   if( itAcct == mAccountMap->end() )
      return kError;

   SipAVConversationManagerImpl* acct = itAcct->second;
   if( acct == NULL )
      return kError;

   // After getting the account, try to determine whether RFC2833 negotiation
   // was successful for this conversation (this will be done by examining the
   // streams).
   int telephoneEventPlType = -1;
   std::vector< std::weak_ptr< webrtc_recon::RtpStreamImpl > >::const_iterator iter;
   for( iter = ci->rtpStreams.begin() ; iter != ci->rtpStreams.end() ; ++iter )
   {
      std::shared_ptr< webrtc_recon::RtpStreamImpl > pStream( iter->lock() );
      if( pStream != NULL )
      {
         int temp( pStream->getRemoteTelephoneEventPlType() );
         if( temp != -1 )
         {
            telephoneEventPlType = temp;
            break;
         }
      }
   }


   int i = 0; // Choose the first preference as our starting point
   CPCAPI2::SipConversation::DtmfMode mode = acct->dtmfPreferences()[ i++ ];

   // If 2833 was only specified, and that was not negotiated, search until
   // we find the first non-2833 mode.
   if( telephoneEventPlType < 0 && mode == DtmfMode_RFC2833 )
   {
      while( mode == DtmfMode_RFC2833 && i < DtmfMode_Everything )
         mode = acct->dtmfPreferences()[ i++ ];

      // in the event that nothing else was found, go to inband
      if( mode == 0 || mode == DtmfMode_RFC2833 )
         mode = DtmfMode_InBand;
   }

   // If SIP INFO only was specified, and this call has evidence of a
   // SIP INFO failure, go to the next mode, or inband if nothing else
   // was specified
   if( ci->sipDTMFFailure && mode == DtmfMode_SIP_INFO )
   {
      while( mode == DtmfMode_SIP_INFO && i < DtmfMode_Everything )
         mode = acct->dtmfPreferences()[ i++ ];

      // in the event that nothing else was found, go to inband
      if( mode == 0 || mode == DtmfMode_SIP_INFO )
         mode = DtmfMode_InBand;
   }

   bool playedLocally = false;
   int durationMS = 200;   // should be configurable?

   if( mode == DtmfMode_RFC2833         || mode == DtmfMode_InBand ||
       mode == DtmfMode_RFC2833_InBand  || mode == DtmfMode_RFC2833_SIP_INFO ||
       mode == DtmfMode_InBand_SIP_INFO || mode == DtmfMode_Everything )
   {
      // Both 2833 and in-band are sent using basically the same technique.
      // we create a media participant of the "tone" variety and add it to
      // the conversation. The only difference between the two are the parameters
      // which are used.
      //
      resip::Uri toneUri("tone:");
      toneUri.host() = resip::Data::from(DtmfToneHelper::dtmfCharFromToneId( toneId ));
      if( toneUri.host().size() == 0 )
         return kError;

      // Identify whether the tone is 2833 or inband

      if( mode == DtmfMode_RFC2833          || mode == DtmfMode_RFC2833_InBand ||
          mode == DtmfMode_RFC2833_SIP_INFO || mode == DtmfMode_Everything )
         toneUri.param(resip::ExtensionParameter( "oob" ));

      if( mode == DtmfMode_InBand          || mode == DtmfMode_RFC2833_InBand ||
          mode == DtmfMode_InBand_SIP_INFO || mode == DtmfMode_Everything )
      {
         toneUri.param(resip::ExtensionParameter( "remote" )); // used to be called "inband"
         toneUri.param(resip::ExtensionParameter( "inband" )); // used to be called "inband"
      }

      if( playLocally )
         toneUri.param(resip::ExtensionParameter( "local" ));

      toneUri.param(resip::p_duration) = durationMS;

      if( ci->reconConversation != 0 )
      {
         ci->reconMediaParticipant = acct->recon()->createMediaResourceParticipant(ci->reconConversation, toneUri);
         playedLocally = playLocally;
      }
   }

   if( mode == DtmfMode_SIP_INFO        || mode == DtmfMode_RFC2833_SIP_INFO ||
       mode == DtmfMode_InBand_SIP_INFO || mode == DtmfMode_Everything )
   {
      resip::Contents *pInfoDtmfContents = SipDTMFRelayUtils::MakeDTMFRelayContents( toneId );
      if( pInfoDtmfContents )
         acct->recon()->sendInfo( ci->reconRemoteParticipant, *pInfoDtmfContents );

      delete pInfoDtmfContents; // ?
   }

   if( playLocally && !playedLocally )
   {
      int channel_for_tone = mMediaIf->media_stack()->voe_base()->CreateChannel();
      if (channel_for_tone >= 0 && mMediaIf->media_stack()->voe_base()->StartPlayout(channel_for_tone) == 0)
      {
         mMediaIf->media_stack()->dtmf()->PlayDtmfTone(toneId, durationMS);
         mPhone->getSdkModuleThread().postMS(resip::resip_bind(&SipAVConversationManagerInterface::dtmfToneChannelCleanup, this, channel_for_tone), durationMS+50);
      }
   }

   return kSuccess;
}

int SipAVConversationManagerInterface::dtmfToneChannelCleanup(int channel_for_tone)
{
   mMediaIf->media_stack()->voe_base()->StopPlayout(channel_for_tone);
   mMediaIf->media_stack()->voe_base()->DeleteChannel(channel_for_tone);
   return kSuccess;
}

int SipAVConversationManagerInterface::stopDtmfTone()
{
   return kSuccess;
}

int SipAVConversationManagerInterface::setAnswerMode(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipConversation::AnswerModeSettings answerMode, CPCAPI2::NetworkTransport transport)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setAnswerModeImpl, this, account, answerMode, transport));
   return kSuccess;
}


int SipAVConversationManagerInterface::setAnswerModeImpl(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipConversation::AnswerModeSettings answerMode, CPCAPI2::NetworkTransport transport)
{
   AccountMap::iterator itAcct = mAccountMap->find(account);
   if (itAcct != mAccountMap->end())
   {
      itAcct->second->setAnswerMode(answerMode, transport);
   }

   return kSuccess;
}

SipConversationHandle SipAVConversationManagerInterface::networkChangeStarcodeHandover(SipConversationHandle originalConversation)
{
   SipConversationHandle h = SipConversationHandleFactory::getNext();
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::networkChangeStarcodeHandoverImpl, this, h, originalConversation));
   return h;
}

int SipAVConversationManagerInterface::networkChangeStarcodeHandoverImpl(SipConversationHandle conversation, SipConversationHandle originalConversation)
{
   InfoLog(<< "SipConversationManager::networkChangeStarcodeHandoverImpl(): original conversation handle: " << originalConversation);
   SipCallCreationInfo* ci = getCreationInfo(originalConversation);
   if (!ci)
   {
      InfoLog(<< "SipConversationManager::networkChangeStarcodeHandoverImpl(): invalid conversation handle: " << originalConversation);
      return kError;
   }

   AccountMap::iterator itAcct = mAccountMap->find(ci->account);
   if (itAcct == mAccountMap->end())
   {
      InfoLog(<< "SipConversationManager::networkChangeStarcodeHandoverImpl(): invalid account handle: " << ci->account << " for conversation handle: " << originalConversation);
      return kError;
   }

   SipAccount::SipAccountImpl* acctImpl = mAccountIf->getAccountImpl(ci->account);
   if (acctImpl->isDisabling())
   {
      InfoLog(<< "SipConversationManager::networkChangeStarcodeHandoverImpl(): network change handover attempted while account " << ci->account << " was disabling for conversation handle: " << originalConversation);
      return kError;
   }

   SipAVConversationManagerImpl* convImpl = itAcct->second;

   // As the starcode handover triggers a new outbound sip dialog, we go through the same procedure to handle the bestEffortMediaEncryption flag.
   if (ci->bestEffortMediaEncryption)
   {
      // Best effort media encryption has been selected

      // Check the state of the feature
      if (ci->bestEffortMediaEncryptionState == BestEffortMediaEncryptionState_Initial)
      {
         // No call has been attempted yet

         // Set the encryption to SAVP for all specified medias
         for (cpc::vector<MediaInfo>::iterator it = ci->localMediaInfo.begin(); it != ci->localMediaInfo.end(); ++it)
         {
            it->mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_SDES_Encrypted;
            it->mediaEncryptionOptions.secureMediaRequired = true;

            // Crypto suite gets initialized from configuration, don't override
            DebugLog(<< "SipAVConversationManagerInterface::networkChangeStarcodeHandoverImpl: CRYPTO Suites size: " << it->mediaEncryptionOptions.mediaCryptoSuites.size() << " for media-type: " << it->mediaType);
         }

         // Get ready to make a SAVP call
         ci->bestEffortMediaEncryptionState = BestEffortMediaEncryptionState_SAVP_Call;
      }
      else if (ci->bestEffortMediaEncryptionState == BestEffortMediaEncryptionState_SAVP_Call_Rejected)
      {
         // SAVP call failed

         // Now set the encryption to AVP for all specified medias
         for (cpc::vector<MediaInfo>::iterator it = ci->localMediaInfo.begin(); it != ci->localMediaInfo.end(); ++it)
         {
            it->mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_Unencrypted;
            it->mediaEncryptionOptions.secureMediaRequired = false;

            // Crypto suite gets initialized from configuration, don't override
         }

         // Get ready to make a AVP call
         ci->bestEffortMediaEncryptionState = BestEffortMediaEncryptionState_AVP_Call;
      }
   }

   if (ci->callKitMode)
   {
      webrtc::AudioDeviceModule* adm = mMediaIf->media_stack()->voe_base()->audio_device();
      adm->ActivatePlayAndRecordMode(true);
   }

   // Ensure the default profile has the most up-to-date list of codecs, etc.
   convImpl->recon()->updateDefaultConversationProfile();

   convImpl->countNewCall();

   recon::ConversationProfileHandle convProfile = convImpl->recon()->getDefaultConversationProfile();
   recon::ConversationHandle reconConv = convImpl->recon()->createConversation(convProfile);
   convImpl->recon()->addParticipant(reconConv, convImpl->recon()->getLocalParticipant());
   convImpl->recon()->addParticipant(reconConv, ci->reconRemoteParticipant);

   DebugLog(<< "SipAVConversationManagerInterface::networkChangeStarcodeHandoverImpl(): new recon conversation: " << reconConv << " local participant: " << convImpl->recon()->getLocalParticipant() << " remote participant: " << ci->reconRemoteParticipant);
   DebugLog(<< "SipAVConversationManagerInterface::networkChangeStarcodeHandoverImpl(): old recon conversation: " << ci->reconConversation << " local participant: " << convImpl->recon()->getLocalParticipant() << " remote participant: " << ci->reconRemoteParticipant << " local-media-size: " << ci->localMediaInfo.size());

   // Media attributes
   recon::ConversationManager::MediaAttributes mediaAttribs(ReconConversationManagerImpl::toReconMediaAttribs(ci->configuredLocalMediaInfo, ci->account));
   mediaAttribs.max_nack_list_size = ci->videoNackSettings.maxNackListSize;
   mediaAttribs.max_packet_age_to_nack = ci->videoNackSettings.maxPacketAgeToNack;
   mediaAttribs.max_incomplete_time_ms = ci->videoNackSettings.maxIncompleteTimeMs;
   mediaAttribs.nack_history_size_sender = ci->videoNackSettings.nackHistorySizeSender;

   DebugLog(<< "SipAVConversationManagerInterface::networkChangeStarcodeHandoverImpl(): local-media list size: " << ci->localMediaInfo.size());
   convImpl->recon()->answerParticipant(ci->reconRemoteParticipant, mediaAttribs);

   // Call attributes
   recon::ConversationManager::CallAttributes callAttribs;
   callAttribs.answerModeAttributes = ReconConversationManagerImpl::toReconAnswerMode(ci->answerMode);
   callAttribs.isAnonymous = (ci->anonymousMode > 0);
   callAttribs.replacesDialogId = ci->replacesDialogId;
   callAttribs.joinDialogId = ci->joinDialogId;
   for (cpc::vector<Parameter>::const_iterator it = ci->joinParams.begin(); it != ci->joinParams.end(); it++)
   {
      callAttribs.joinParams[it->name.c_str()] = it->value.c_str();
   }

   // Handling of the starcode dialog similar to a transfer event (i.e. onRequestOutgoingParticipant)
   std::string starcode = convImpl->recon()->getConvSettings().networkChangeHandoverStarcode.c_str();
   std::stringstream ss;
   ss << "sip:" << starcode << "@" << acctImpl->getSettings().domain;
   resip::NameAddr transferTarget(ss.str().c_str());

   recon::ParticipantHandle transferTargetParticipant = convImpl->recon()->createRemoteParticipant(reconConv, transferTarget, mediaAttribs, callAttribs);

   NewConversationEvent args;
   args.conversationState = ConversationState_LocalOriginated;
   args.account = ci->account;
   args.conversationType = ConversationType_OutgoingNetworkChangeHandover;
   args.relatedConversation = 0;
   args.conversationToJoin = 0;
   args.conversationToReplace = originalConversation;
   args.localMediaInfo = ci->configuredLocalMediaInfo; // retain configured local media preferences
   args.answerMode = ci->answerMode;
   args.remoteAddress = ci->lastRemoteAddr.address;
   args.remoteDisplayName = ci->lastRemoteAddr.displayName;

   SipCallCreationInfo* ciStar = new SipCallCreationInfo();
   ciStar->reconRemoteParticipant = transferTargetParticipant;
   ciStar->account = ci->account;
   ciStar->targetAddresses.push_back(transferTarget);
   ciStar->localMediaInfo = ci->configuredLocalMediaInfo; // retain configured local media preferences
   ciStar->configuredLocalMediaInfo = ci->configuredLocalMediaInfo;
   ciStar->bestEffortMediaEncryption = ci->bestEffortMediaEncryption; // retain media encyprtion options
   ciStar->callKitMode = ci->callKitMode; // retain callKitMode
   ciStar->conversationToReplaceWithStarcode = originalConversation;
   ciStar->reconConversation = reconConv;
   ciStar->lastRemoteAddr = { args.remoteAddress, args.remoteDisplayName };
   DebugLog(<< "ReconConversationManagerImpl::networkChangeStarcodeHandoverImpl(): original conversation: " << originalConversation << " added starcode conversation: " << conversation << " recon starcode conversation: " << reconConv);
   convImpl->recon()->addCreationInfo(conversation, ciStar);

   convImpl->fireNewConvEvent(conversation, args);
   return kSuccess;
}

int SipAVConversationManagerInterface::ignoreBindingFilterForStarcodeHandover(SipConversationHandle conversation)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::ignoreBindingFilterForStarcodeHandoverImpl, this, conversation));
   return kSuccess;
}

int SipAVConversationManagerInterface::ignoreBindingFilterForStarcodeHandoverImpl(SipConversationHandle conversation)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci == NULL)
   {
      return kError;
   }

   AccountMap::iterator itAcct = mAccountMap->find(ci->account);
   if (itAcct == mAccountMap->end())
   {
      return kError;
   }

   SipAVConversationManagerImpl* acct = itAcct->second;
   if (acct == NULL)
   {
      return kError;
   }

   acct->ignoreBindingFilterForStarcodeHandover();
   return kSuccess;
}

int SipAVConversationManagerInterface::refreshConversationStatistics(SipConversationHandle conversation)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::refreshConversationStatisticsImpl, this, conversation, true, false, false, false));
   return kSuccess;
}

int SipAVConversationManagerInterface::refreshConversationStatistics(SipConversationHandle conversation, bool includeNetworkStatistics, bool includeJitterStatistics, bool includeRemoteStatistics)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::refreshConversationStatisticsImpl, this, conversation, includeNetworkStatistics, includeJitterStatistics, includeRemoteStatistics, false));
   return kSuccess;
}

int SipAVConversationManagerInterface::refreshConversationStatistics(SipConversationHandle conversation, bool includeNetworkStatistics, bool includeJitterStatistics, bool includeRemoteStatistics, bool force)
{
   // intended for internal use only so we can use the unit tests to validate conversation statistics
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::refreshConversationStatisticsImpl, this, conversation, includeNetworkStatistics, includeJitterStatistics, includeRemoteStatistics, force));
   return kSuccess;
}

int SipAVConversationManagerInterface::refreshConversationStatisticsImpl(SipConversationHandle conversation, bool includeNetworkStatistics, bool includeJitterStatistics, bool includeRemoteStatistics, bool force)
{
   UInt64 now = resip::ResipClock::getTimeSecs();
   if (force || (now - mLastStatsRefreshTime_Secs) > 2) // guard against the app requesting stats too often; it is expensive because of locking in WebRTC
   {
      mLastStatsRefreshTime_Secs = now;
      SipCallCreationInfo* ci = getCreationInfo(conversation);
      if (ci != NULL)
      {
         AccountMap::iterator itAcct = mAccountMap->find(ci->account);
         if (itAcct != mAccountMap->end())
         {
            SipAVConversationManagerImpl* acct = itAcct->second;
            return acct->recon()->refreshConversationStatisticsImpl(conversation, includeNetworkStatistics, includeJitterStatistics, includeRemoteStatistics, false);
         }
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::startMonitoringAudioDeviceLevels(SipConversationHandle conversation)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::startMonitoringAudioDeviceLevelsImpl, this, conversation));
   return kSuccess;
}

int SipAVConversationManagerInterface::startMonitoringAudioDeviceLevelsImpl(SipConversationHandle conversation)
{
   // Get the Account, we need to fetch the DTMF settings.
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci == NULL)
   {
      return kError;
   }

   AccountMap::iterator itAcct = mAccountMap->find(ci->account);
   if (itAcct == mAccountMap->end())
   {
      return kError;
   }

   SipAVConversationManagerImpl* acct = itAcct->second;
   if (acct == NULL)
   {
      return kError;
   }

   return acct->recon()->startMonitoringAudioLevels(conversation);
}

int SipAVConversationManagerInterface::stopMonitoringAudioDeviceLevels(SipConversationHandle conversation)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::stopMonitoringAudioDeviceLevelsImpl, this, conversation));
   return kSuccess;
}

int SipAVConversationManagerInterface::stopMonitoringAudioDeviceLevelsImpl(SipConversationHandle conversation)
{
   // Get the Account, we need to fetch the DTMF settings.
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci == NULL)
   {
      return kError;
   }

   AccountMap::iterator itAcct = mAccountMap->find(ci->account);
   if (itAcct == mAccountMap->end())
   {
      return kError;
   }

   SipAVConversationManagerImpl* acct = itAcct->second;
   if (acct == NULL)
   {
      return kError;
   }

   return acct->recon()->stopMonitoringAudioLevels(conversation);
}

int SipAVConversationManagerInterface::setVideoNackSettings(SipConversationHandle conversation, const NackSettings& nackSettings)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setVideoNackSettingsImpl, this, conversation, nackSettings));
   return kSuccess;
}

int SipAVConversationManagerInterface::setVideoNackSettingsImpl(SipConversationHandle conversation, const NackSettings& nackSettings)
{
   // Get the Account, we need to fetch the DTMF settings.
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci == NULL)
   {
      return kError;
   }
   ci->videoNackSettings = nackSettings;
   return kSuccess;
}

int SipAVConversationManagerInterface::setForceTarget(SipConversationHandle conversation, const cpc::string& target)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setForceTargetImpl, this, conversation, target));
   return kSuccess;
}

int SipAVConversationManagerInterface::setForceTargetImpl(SipConversationHandle conversation, const cpc::string& target)
{
   // Get the Account, we need to fetch the DTMF settings.
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci == NULL)
   {
      return kError;
   }
   ci->forceTarget = target;
   return kSuccess;
}

int SipAVConversationManagerInterface::onConversationAdornment(SipAccountHandle account, ConversationAdornmentInternalEvent& args)
{
   AdornmentHandlerMap::iterator it = mAdornmentHandlerMap.find(account);
   if (it == mAdornmentHandlerMap.end()) return kSuccess;
   if (!it->second) return kSuccess;

   mAdornmentEventMap[args.adornmentMessageId] = &args;

   it->second->onConversationAdornment(args.conversation, args);

   mAdornmentEventMap.erase(args.adornmentMessageId);
   assert(mAdornmentEventMap.empty());

   return kSuccess;
}

int SipAVConversationManagerInterface::setAdornmentHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipConversationAdornmentHandler* handler)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&SipAVConversationManagerInterface::setAdornmentHandlerImpl, this, account, handler);

   if (handler == NULL)
   {
      // removing the handler involves two steps:

      // 1. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      executeOnSdkThread(f);

      // 2. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);
   }
   else
   {
      postToSdkThread(f);
   }

   return kSuccess;
}

int SipAVConversationManagerInterface::setAdornmentHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipConversationAdornmentHandler* handler)
{
   if(handler == NULL)
      mAdornmentHandlerMap.erase(account);
   else
      mAdornmentHandlerMap.insert(std::make_pair(account, handler));

   AccountMap::iterator it = mAccountMap->find(account);
   SipAVConversationManagerImpl* convMan = (it == mAccountMap->end() ? NULL : it->second);

   if (convMan)
   {
      if(handler != NULL)
         convMan->setAdornmentHandler(this);
      else
         convMan->setAdornmentHandler(NULL);
   }
   return kSuccess;
}


int SipAVConversationManagerInterface::adornMessage(SipConversationHandle conversation, unsigned int adornmentMessageId, const cpc::vector<SipHeader>& customHeaders)
{
   AdornmentEventMap::iterator it = mAdornmentEventMap.find(adornmentMessageId);

   if (it != mAdornmentEventMap.end())
   {
	   it->second->customHeaders = customHeaders;

	   return kSuccess;
   }

   return kError;

}

int SipAVConversationManagerInterface::setCallToReplace(SipConversationHandle conversation, const cpc::string& callId, const cpc::string& localTag, const cpc::string& remoteTag)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setCallToReplaceImpl, this, conversation, callId, localTag, remoteTag));
   return kSuccess;
}

int SipAVConversationManagerInterface::setCallToReplaceImpl(SipConversationHandle conversation, const cpc::string& callId, const cpc::string& localTag, const cpc::string& remoteTag)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci)
   {
      ci->replacesDialogId = resip::DialogId(callId.c_str(), localTag.c_str(), remoteTag.c_str());
   }

   return kSuccess;
}

int SipAVConversationManagerInterface::setCallToJoin(SipConversationHandle conversation, const cpc::string& callId, const cpc::string& fromTag, const cpc::string& toTag, const cpc::vector<Parameter>& joinParams)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setCallToJoinImpl, this, conversation, callId, fromTag, toTag, joinParams));
   return kSuccess;
}

int SipAVConversationManagerInterface::setCallToJoinImpl(SipConversationHandle conversation, const cpc::string& callId, const cpc::string& fromTag, const cpc::string& toTag, const cpc::vector<Parameter>& joinParams)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci)
   {
      ci->joinDialogId = resip::DialogId(callId.c_str(), fromTag.c_str(), toTag.c_str());
      ci->joinParams = joinParams;
   }

   return kSuccess;
}

int SipAVConversationManagerInterface::addToRecorder(SipConversationHandle conversation, Recording::RecorderHandle recorder)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::addToRecorderImpl, this, conversation, recorder));
   return kSuccess;
}

int SipAVConversationManagerInterface::addToRecorderImpl(SipConversationHandle conversation, Recording::RecorderHandle recorder)
{
#if (CPCAPI2_BRAND_RECORDING_MODULE == 1)
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci)
   {
      ci->recordings.insert(recorder);

      bool foundStream = false;
      std::vector< std::weak_ptr< webrtc_recon::RtpStreamImpl > >::const_iterator iter;
      for( iter = ci->rtpStreams.begin() ; iter != ci->rtpStreams.end() ; ++iter )
      {
         std::shared_ptr< webrtc_recon::RtpStreamImpl > pStream( iter->lock() );
         if( pStream != NULL && pStream->mediaType() == recon::MediaStack::MediaType_Audio)
         {
            int channel = pStream->channel();

            if(channel < 0)
            {
               ErrLog(<< "didn't add conversation " << conversation << " to recorder " << recorder << " due to no channel");
               return kError;
            }

            webrtc::VoEFile* voeFile = mMediaIf->media_stack()->file();
            voeFile->FileRecorderAddChannel(recorder, channel);

            foundStream = true;

            #if (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)
                //Send data to Analytics (UEM) module as well
            static_cast<Analytics::AnalyticsManagerInt*>(Analytics::AnalyticsManager::getInterface(mPhone))->ConvRecordingStarted(conversation);
            #endif
         }
      }

      if (!foundStream)
      {
         ErrLog(<< "didn't add conversation " << conversation << " to recorder " << recorder << " since couldn't find a stream");
      }
   }
   else
   {
      ErrLog(<< "didn't add conversation " << conversation << " to recorder " << recorder << " since creation info not found");
   }
#endif

   return kSuccess;
}

int SipAVConversationManagerInterface::removeFromRecorder(SipConversationHandle conversation, Recording::RecorderHandle recorder)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::removeFromRecorderImpl, this, conversation, recorder));
   return kSuccess;
}

int SipAVConversationManagerInterface::removeFromRecorderImpl(SipConversationHandle conversation, Recording::RecorderHandle recorder)
{
#if (CPCAPI2_BRAND_RECORDING_MODULE == 1)
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci)
   {
      ci->recordings.erase(recorder);

      std::vector< std::weak_ptr< webrtc_recon::RtpStreamImpl > >::const_iterator iter;
      for( iter = ci->rtpStreams.begin() ; iter != ci->rtpStreams.end() ; ++iter )
      {
         std::shared_ptr< webrtc_recon::RtpStreamImpl > pStream( iter->lock() );
         if( pStream != NULL && pStream->mediaType() == recon::MediaStack::MediaType_Audio)
         {
            int channel = pStream->channel();

            if(channel < 0)
               return kError;

            webrtc::VoEFile* voeFile = mMediaIf->media_stack()->file();
            voeFile->FileRecorderRemoveChannel(recorder, channel);
         }
      }
   }
#endif

   return kSuccess;
}

int SipAVConversationManagerInterface::playSound(SipConversationHandle conversation, const cpc::string& resourceUri, bool repeat)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::playSoundImpl, this, conversation, resourceUri, repeat));
   return kSuccess;
}

int SipAVConversationManagerInterface::playSoundImpl(SipConversationHandle conversation, const cpc::string& resourceUri, bool repeat)
{
   InfoLog(<< "playSound (conversation=" << conversation << ")");

   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci)
   {
      std::vector< std::weak_ptr< webrtc_recon::RtpStreamImpl > >::const_iterator iter;
      for( iter = ci->rtpStreams.begin() ; iter != ci->rtpStreams.end() ; ++iter )
      {
         std::shared_ptr< webrtc_recon::RtpStreamImpl > pStream( iter->lock() );
         if( pStream != NULL && pStream->mediaType() == recon::MediaStack::MediaType_Audio)
         {
            int channel = pStream->channel();

            if(channel < 0)
               return kError;

            InfoLog(<< "playSound invoke media stack api");

            if (resourceUri.find("tone:") == 0)
            {
               InfoLog(<< "play test tone");
               pStream->playTone(1000,1000);
            }
            else if(resourceUri.find("silence:") == 0)
            {
               InfoLog(<< "play silence");
               pStream->playTone(0,0);
            }
            else if(resourceUri.find("file:") == 0)
            {
               cpc::string filename(resourceUri.substr(5,resourceUri.size()));
               InfoLog(<< "playFile path: " << filename.c_str());

               if (!mPhone->hasFilePermission(Permission_ReadFiles, filename))
               {
                  InfoLog(<< "playFile - Cannot read file due to missing permission");
                  mPhone->requestPermission(0, Permission_ReadFiles);
                  return kError;
               }

               resip::Data file_name(filename);
               pStream->playFile(file_name,repeat);
            }
            else if(resourceUri.find("android.resource:") == 0)
            {
               pStream->playFile(resourceUri.c_str(), repeat);
            }
            else if(resourceUri.find("seq:") == 0)
            {
               cpc::string seq(resourceUri.substr(4,resourceUri.size()));
               InfoLog(<< "seq is: " << seq.c_str());
               resip::Data sequence(seq);
               pStream->playSeq(sequence, repeat);
             }
         }
      }
   }

   return kSuccess;
}

int SipAVConversationManagerInterface::playSound(SipConversationHandle conversation, PlaySoundStream* playSoundStream, bool repeat)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::playSoundStreamImpl, this, conversation, playSoundStream, repeat));
   return kSuccess;
}

class PlaySoundStreamToInStream : public webrtc::InStream
{
public:
   PlaySoundStreamToInStream(PlaySoundStream* playSoundStream) : mPlaySoundStream(playSoundStream) {}
   virtual ~PlaySoundStreamToInStream() {}

   virtual int Read(void *buf, size_t len)
   {
      return mPlaySoundStream->Read(buf, len);
   }
   virtual int Rewind()
   {
      return mPlaySoundStream->Rewind();
   }
private:
   PlaySoundStream* mPlaySoundStream;
};

int SipAVConversationManagerInterface::playSoundStreamImpl(SipConversationHandle conversation, PlaySoundStream* playSoundStream, bool repeat)
{
   InfoLog(<< "playSoundStream (conversation=" << conversation << ")");

   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci)
   {
      std::vector< std::weak_ptr< webrtc_recon::RtpStreamImpl > >::const_iterator iter;
      for (iter = ci->rtpStreams.begin(); iter != ci->rtpStreams.end(); ++iter)
      {
         std::shared_ptr< webrtc_recon::RtpStreamImpl > pStream(iter->lock());
         if (pStream != NULL && pStream->mediaType() == recon::MediaStack::MediaType_Audio)
         {
            int channel = pStream->channel();

            if (channel < 0)
               return kError;

            InfoLog(<< "playSoundStream invoke media stack api");

            PlaySoundStreamToInStream* converter = new PlaySoundStreamToInStream(playSoundStream);
            pStream->playFileStream(converter, repeat);
         }
      }
   }

   return kSuccess;
}

int SipAVConversationManagerInterface::stopPlaySound(SipConversationHandle conversation)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::stopPlaySoundImpl, this, conversation));
   return kSuccess;
}

int SipAVConversationManagerInterface::stopPlaySoundImpl(SipConversationHandle conversation)
{
   InfoLog(<< "stopPlaySound (conversation=" << conversation << ")");

   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci)
   {
      std::vector< std::weak_ptr< webrtc_recon::RtpStreamImpl > >::const_iterator iter;
      for( iter = ci->rtpStreams.begin() ; iter != ci->rtpStreams.end() ; ++iter )
      {
         std::shared_ptr< webrtc_recon::RtpStreamImpl > pStream( iter->lock() );
         if( pStream != NULL && pStream->mediaType() == recon::MediaStack::MediaType_Audio)
         {
            int channel = pStream->channel();

            if(channel < 0)
               return kError;

            InfoLog(<< "stopPlaySound invoke media stack api");

            pStream->stopFile();
            pStream->stopTone();
            pStream->stopSeq();
         }
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::enableMusicOnHold(SipConversationHandle conversation, bool on)
{
   InfoLog(<< "enableMusicOnHold (" << on << ")");

   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci)
   {
      std::vector< std::weak_ptr< webrtc_recon::RtpStreamImpl > >::const_iterator iter;
      for( iter = ci->rtpStreams.begin() ; iter != ci->rtpStreams.end() ; ++iter )
      {
         std::shared_ptr< webrtc_recon::RtpStreamImpl > pStream( iter->lock() );
         if( pStream != NULL && pStream->mediaType() == recon::MediaStack::MediaType_Audio)
         {
            int channel = pStream->channel();

            if(channel < 0)
               return kError;
            InfoLog(<< "setMoHEnabled invoke media stack api");
            pStream->setMoHEnabled(on);
         }
      }
   }

   return kSuccess;
}

int SipAVConversationManagerInterface::decodeProvisioningResponse(const cpc::string& provisioningResponse, cpc::vector<SipConversationSettings>& outConversationSettings)
{
   rapidjson::Document provisionedJSON;
   provisionedJSON.Parse<0>(provisioningResponse.c_str());

   if (provisionedJSON.HasParseError())
   {
      WarningLog(<< "Invalid provisioning format, parse error occured:" << provisionedJSON.GetParseError() << "Aborting decode.");
      return kError;
   }

   if (!provisionedJSON.HasMember("conversation"))
   {
      WarningLog(<< "Invalid provisioning format, conversation node missing. Aborting decode.");
      return kError;
   }

   const rapidjson::Value& conversation = provisionedJSON["conversation"];
   if (!conversation.IsArray())
   {
      WarningLog(<< "Invalid provisioning format, conversation node not an array. Aborting decode.");
      return kError;
   }

   for (rapidjson::Value::ConstValueIterator itr = conversation.Begin(); itr != conversation.End(); ++itr)
   {
      if (!itr->HasMember("conversationSettings"))
      {
         WarningLog(<< "Invalid provisioning format, conversationSettings node missing.");
         continue;
      }

      const rapidjson::Value& conversationSettings = (*itr)["conversationSettings"];
      if (!conversationSettings.IsObject())
      {
         WarningLog(<< "Invalid provisioning format, conversationSettings not an object.");
         continue;
      }

      SipConversationSettings settings;
      JsonDeserialize(*itr, "conversationSettings", settings);
      outConversationSettings.push_back(settings);
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::getCallCount()
{
   int callCount = 0;
   for (AccountMap::iterator it = mAccountMap->begin(); it != mAccountMap->end(); it++)
   {
      SipAVConversationManagerImpl* convMan = it->second;
      if (convMan != NULL)
      {
         callCount += convMan->getCallCount();
      }
   }

   return callCount;
}

int SipAVConversationManagerInterface::getCallCount(CPCAPI2::SipAccount::SipAccountHandle account)
{
   int callCount = 0;
   AccountMap::iterator it = mAccountMap->find(account);
   if (it != mAccountMap->end())
   {
      SipAVConversationManagerImpl* convMan = it->second;
      if (convMan != NULL)
      {
         callCount = convMan->getCallCount();
      }
   }

   return callCount;
}

int SipAVConversationManagerInterface::setCallKitMode(SipConversationHandle conversation)
{
   DebugLog(<< "SipConversationManager::" << __FUNCTION__ << "(" << conversation << ")");

#if defined(TARGET_OS_IPHONE) && TARGET_OS_IPHONE
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setCallKitModeImpl, this, conversation));
   return kSuccess;
#else
   return kError;
#endif
}

int SipAVConversationManagerInterface::setCallKitModeImpl(SipConversationHandle conversation)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      if (ci->callKitMode)
      {
         WarningLog(<< "setCallKitMode already called for conversation " << conversation << "; ignoring");
      }
      else
      {
         if(ci->currentConversationState == ConversationState_None)
         {
            DebugLog(<< "setCallKitModeImpl ConversationState_None");
            ci->callKitMode = true;
         }
         else if(ci->currentConversationState == ConversationState_LocalRinging)
         {
            DebugLog(<< "setCallKitModeImpl ConversationState_LocalRinging");

            ci->callKitMode = true;

            webrtc::AudioDeviceModule* adm = mMediaIf->media_stack()->voe_base()->audio_device();
            adm->ActivatePlayAndRecordMode(true);
         }
         else
         {
            WarningLog(<< "setCallKitModeImpl other conversation state (" << ConversationState_None << "), ignoring");
         }
      }
   }
   else
   {
      ErrLog(<< "Couldn't set CallKit mode for conversation " << conversation << "; couldn't find conversation");
   }

   return kSuccess;
}

int SipAVConversationManagerInterface::setTelecomFrameworkMode(SipConversationHandle conversation)
{
#ifdef ANDROID
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setTelecomFrameworkModeImpl, this, conversation));
   return kSuccess;
#else
   ErrLog(<< "setTelecomFrameworkMode called on a non Android platform");
   return kError;
#endif
}

int SipAVConversationManagerInterface::setTelecomFrameworkModeImpl(SipConversationHandle conversation)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      if(ci->currentConversationState == ConversationState_None)
      {
         ci->callKitMode = true;
         DebugLog(<< "Set Telecom framework mode for conversation " << conversation);
      }
      else if(ci->currentConversationState == ConversationState_LocalRinging)
      {
         ci->callKitMode = true;

         webrtc::AudioDeviceModule* adm = mMediaIf->media_stack()->voe_base()->audio_device();
         adm->ActivatePlayAndRecordMode(true);
         DebugLog(<< "Set Telecom framework mode for conversation " << conversation << " local ringing");
      }
      else
      {
        ErrLog(<< "Set Telecom framework mode for conversation " << conversation << "; setTelecomFrameworkMode called too late");
      }
   }
   else
   {
      ErrLog(<< "Couldn't set Telecom framework mode for conversation " << conversation << "; couldn't find conversation");
   }

   return kSuccess;
}

int SipAVConversationManagerInterface::setIncomingVideoRenderTarget(SipConversationHandle conversation, void* surface, VideoSurfaceType type)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setIncomingVideoRenderTargetImpl, this, conversation, surface, type));
   return kSuccess;
}

int SipAVConversationManagerInterface::setIncomingVideoRenderTargetImpl(SipConversationHandle conversation, void* surface, VideoSurfaceType type)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL && surface)
   {
      webrtc::VideoRenderSurfaceType remoteVideoSurfaceType = webrtc::kRenderSurfaceDefault;
#ifdef _WIN32
      if (VideoSurfaceType_WindowsHWND == type)
      {
         InfoLog(<< "SipAVConversationManagerInterface::setIncomingVideoRenderTargetImpl(" << conversation << ", " << surface << ") - surface is a window");
         remoteVideoSurfaceType = webrtc::kRenderSurfaceWindows;
      }
      else if (VideoSurfaceType_WindowsWpfControl == type)
      {
         InfoLog(<< "SipAVConversationManagerInterface::setIncomingVideoRenderTargetImpl(" << conversation << ", " << surface << ") - surface is a WPF control");
         remoteVideoSurfaceType = webrtc::kRenderSurfaceWindowsDirectX;
      }

      if (webrtc::kRenderSurfaceDefault == remoteVideoSurfaceType)
      {
         if (::IsWindow((HWND)surface))
         {
            InfoLog(<< "SipAVConversationManagerInterface::setIncomingVideoRenderTargetImpl(" << conversation << ", " << surface << ") - detected that surface is a window");
            remoteVideoSurfaceType = webrtc::kRenderSurfaceWindows;
         }
         else
         {
            InfoLog(<< "SipAVConversationManagerInterface::setIncomingVideoRenderTargetImpl(" << conversation << ", " << surface << ") - detected that surface is WPF control");
            remoteVideoSurfaceType = webrtc::kRenderSurfaceWindowsDirectX;
         }
      }
#endif
      if (ci->rtpStreams.empty())
      {
         cpc::vector<MediaInfo>::iterator itMi = ci->localMediaInfo.begin();
         for (; itMi != ci->localMediaInfo.end(); ++itMi)
         {
            if (itMi->mediaType == MediaType_Video)
            {
               ci->incomingVideoRenderSurface = surface;
               ci->incomingVideoRenderSurfaceType = remoteVideoSurfaceType;
            }
         }
      }
      else
      {
         std::vector< std::weak_ptr< webrtc_recon::RtpStreamImpl > >::const_iterator iter;
         for (iter = ci->rtpStreams.begin(); iter != ci->rtpStreams.end(); ++iter)
         {
            std::shared_ptr< webrtc_recon::RtpStreamImpl > pStream(iter->lock());
            if (pStream != NULL && pStream->mediaType() == recon::MediaStack::MediaType_Video)
            {
               std::shared_ptr<webrtc_recon::MixerImpl> mixerImpl = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(pStream->getMediaStack()->mixer());
               mixerImpl->removeVideoRenderer(pStream->channel(), surface);
               mixerImpl->addVideoRenderer(pStream->channel(), surface, remoteVideoSurfaceType);
            }
         }
      }
   }
   return kSuccess;
}

int SipAVConversationManagerInterface::setAudioDeviceCloseDelay(SipConversationHandle conversation, int audioDeviceCloseDelay)
{
   postToSdkThread(resip::resip_bind(&SipAVConversationManagerInterface::setAudioDeviceCloseDelayImpl, this, conversation, audioDeviceCloseDelay));
   return kSuccess;
}

int SipAVConversationManagerInterface::setAudioDeviceCloseDelayImpl(SipConversationHandle conversation, int audioDeviceCloseDelay)
{
   SipCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci == NULL)
   {
      return kError;
   }

   ci->audioDeviceCloseDelay = audioDeviceCloseDelay;

   std::vector< std::weak_ptr< webrtc_recon::RtpStreamImpl > >::const_iterator iter;
   for (iter = ci->rtpStreams.begin(); iter != ci->rtpStreams.end(); ++iter)
   {
      std::shared_ptr< webrtc_recon::RtpStreamImpl > pStream(iter->lock());
      if (pStream != NULL && pStream->mediaType() == recon::MediaStack::MediaType_Audio)
      {
         pStream->setAudioDeviceCloseDelay(audioDeviceCloseDelay);
      }
   }

   return kSuccess;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::SipConversation::MediaCryptoSuite crypto)
{
   switch (crypto)
   {
      case MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32: os << "AES_CM_128_HMAC_SHA1_32"; break;
      case MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80: os << "AES_CM_128_HMAC_SHA1_80"; break;
      case MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32: os << "AES_256_CM_HMAC_SHA1_32"; break;
      case MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80: os << "AES_256_CM_HMAC_SHA1_80"; break;
      case MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32: os << "AES_192_CM_HMAC_SHA1_32"; break;
      case MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80: os << "AES_192_CM_HMAC_SHA1_80"; break;
      case MediaCryptoSuite_AEAD_AES_128_GCM:        os << "AEAD_AES_128_GCM"; break;
      case MediaCryptoSuite_AEAD_AES_256_GCM:        os << "AEAD_AES_256_GCM"; break;
      default: os << "invalid"; break;
   }

   return os;
}

std::ostream& operator<<(std::ostream& os, const std::set<CPCAPI2::SipConversation::MediaCryptoSuite>& cryptos)
{
   for (std::set<CPCAPI2::SipConversation::MediaCryptoSuite>::const_iterator i = cryptos.begin(); i != cryptos.end(); i++)
   {
      switch (*i)
      {
         case MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32: os << "AES_CM_128_HMAC_SHA1_32 "; break;
         case MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80: os << "AES_CM_128_HMAC_SHA1_80 "; break;
         case MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32: os << "AES_256_CM_HMAC_SHA1_32 "; break;
         case MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80: os << "AES_256_CM_HMAC_SHA1_80 "; break;
         case MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32: os << "AES_192_CM_HMAC_SHA1_32 "; break;
         case MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80: os << "AES_192_CM_HMAC_SHA1_80 "; break;
         case MediaCryptoSuite_AEAD_AES_128_GCM:        os << "AEAD_AES_128_GCM "; break;
         case MediaCryptoSuite_AEAD_AES_256_GCM:        os << "AEAD_AES_256_GCM "; break;
         default: os << "invalid "; break;
      }
   }

   return os;
}

std::ostream& operator<<(std::ostream& os, const SipConversation::NewConversationEvent& evt)
{
   os << "localAddress: " << evt.localAddress << ", localDisplayName: " << evt.localDisplayName
      << ", remoteAddress: " << evt.remoteAddress << ", remoteDisplayName: " << evt.remoteDisplayName
      << ", type: " << evt.conversationType << ", convToReplace: " << evt.conversationToReplace;

   return os;
}

std::ostream& operator<<(std::ostream& os, const SipConversation::ErrorEvent& evt)
{
   return os;
}

std::ostream& operator<<(std::ostream& os, const ConversationEndedEvent & evt)
{
   return os;
}

std::ostream& operator<<(std::ostream& os, const TransferRequestEvent& evt)
{
   return os;
}

std::ostream& operator<<(std::ostream& os, const RedirectRequestEvent& evt)
{
   return os;
}

std::ostream& operator<<(std::ostream& os, const TargetChangeRequestEvent& evt)
{
   return os;
}

std::ostream& operator<<(std::ostream& os, const HangupRequestEvent& evt)
{
   return os;
}

std::ostream& operator<<(std::ostream& os, const BroadsoftTalkEvent& evt)
{
   return os;
}

std::ostream& operator<<(std::ostream& os, const BroadsoftHoldEvent& evt)
{
   return os;
}

std::ostream& operator<<(std::ostream& os, const TransferProgressEvent& evt)
{
   os << "progressEventType: " << evt.progressEventType << ", sipResponseCode: " << evt.sipResponseCode;

   return os;
}

std::ostream& operator<<(std::ostream& os, const TransferResponseEvent& evt)
{
   os << "sipResponseCode: " << evt.sipResponseCode << ", warningHeader: " << evt.warningHeader;
   
   return os;
}

std::ostream& operator<<(std::ostream& os, const ConversationStateChangeRequestEvent& evt)
{
   return os;
}

std::ostream& operator<<(std::ostream& os, const ConversationStateChangedEvent& evt)
{
   os << "conversationState: " << evt.conversationState << ", contactHeaderField: " << evt.contactHeaderField << ", "
      << "remoteAddress: " << evt.remoteAddress << ", remoteDisplayName: " << evt.remoteDisplayName << ", alertInfoHeader: " << evt.alertInfoHeader;
   return os;
}

std::ostream& operator<<(std::ostream& os, const ConversationMediaChangeRequestEvent& evt)
{
   for (cpc::vector<MediaInfo>::const_iterator it = evt.remoteMediaInfo.begin(); it != evt.remoteMediaInfo.end(); ++it)
   {
      os << "MediaInfo item; mediaType=" << it->mediaType << ", mediaDirection=" << it->mediaDirection;
   }

   return os;
}

std::ostream& operator<<(std::ostream& os, const ConversationMediaChangedEvent& evt)
{
   os << "localHold: " << evt.localHold << ", remoteHold: " << evt.remoteHold;

   return os;
}

std::ostream& operator<<(std::ostream& os, const ConversationStatisticsUpdatedEvent& evt)
{
   return os << "ConversationStatisticsUpdatedEvent";
}

std::ostream& operator<<(std::ostream& os, const ConversationAudioDeviceLevelChangeEvent& evt)
{
   return os << "ConversationAudioDeviceLevelChangeEvent";
}

std::ostream& operator<<(std::ostream& os, const SdpOfferAnswerEvent& evt)
{
   return os << "SdpOfferAnswerEvent";
}

std::ostream& operator<<(std::ostream& os, const LocalSdpOfferEvent& evt)
{
   return os << "LocalSdpOfferEvent";
}

std::ostream& operator<<(std::ostream& os, const ConversationInitiatedEvent& evt)
{
   return os << "ConversationInitiatedEvent";
}

std::ostream& operator<<(std::ostream& os, const LocalSdpAnswerEvent& evt)
{
   return os << "LocalSdpAnswerEvent";
}

std::ostream& operator<<(std::ostream& os, const ConversationEndedEventFromStarcodeNetworkChange& evt)
{
   return os << "ConversationEndedEventFromStarcodeNetworkChange";
}

std::ostream& operator<<(std::ostream& os, const SipCallCreationInfo& info)
{
   os << "\n";
   os << "\nSIP Account Handle:                          " << info.account;
   os << "\nTarget Addresses:                            " << "{";
   for (cpc::vector<resip::NameAddr>::const_iterator i = info.targetAddresses.begin(); i != info.targetAddresses.end(); i++)
   {
      os << i->uri().getAOR(true).c_str() << ", ";
   }
   os << "}";
   os << "\nLocal Media Info:                            " << "{";
   for (cpc::vector<MediaInfo>::const_iterator i = info.localMediaInfo.begin(); i != info.localMediaInfo.end(); i++)
   {
      os << "{type=" << i->mediaType << " direction=" << i->mediaDirection << " stream=" << i->mediaStreamId << "}, ";
   }
   os << "}";
   os << "\nRemote Media Info:                           " << "{";
   for (cpc::vector<MediaInfo>::const_iterator i = info.remoteMediaInfo.begin(); i != info.remoteMediaInfo.end(); i++)
   {
      os << "{type=" << i->mediaType << " direction=" << i->mediaDirection << " stream=" << i->mediaStreamId << "}, ";
   }
   os << "}";
   os << "\nConfigured Media Info:                       " << "{";
   for (cpc::vector<MediaInfo>::const_iterator i = info.configuredLocalMediaInfo.begin(); i != info.configuredLocalMediaInfo.end(); i++)
   {
      os << "{type=" << i->mediaType << " direction=" << i->mediaDirection << " stream=" << i->mediaStreamId << "}, ";
   }
   os << "}";
   os << "\nAnonymous Mode:                              " << info.anonymousMode;
   os << "\nBest Effort Media Encryption:                " << info.bestEffortMediaEncryption;
   os << "\nBest Effort Media Encryption State:          " << info.bestEffortMediaEncryptionState;
   os << "\nStack Conversation Handle:                   " << info.reconConversation;
   os << "\nStack Remote Participant:                    " << info.reconRemoteParticipant;
   os << "\nStack Original Remote Participant:           " << info.reconOriginalRemoteParticipant;
   os << "\nHas Pending Request:                         " << info.hasPendingRequest;
   os << "\nStack Media Participant Handle:              " << info.reconMediaParticipant;
   os << "\nSip Conversation To Replace:                 " << info.conversationToReplace;
   os << "\nSip Original Conversation:                   " << info.originalConversation;
   os << "\nSip Conversation To Replace With Starcode:   " << info.conversationToReplaceWithStarcode;
   os << "\nRTP Streams:                                 " << "{";
   for (std::vector<std::weak_ptr<webrtc_recon::RtpStreamImpl>>::const_iterator i = info.rtpStreams.begin(); i != info.rtpStreams.end(); ++i)
   {
      if (std::shared_ptr<webrtc_recon::RtpStreamImpl> sp = i->lock())
      {
         os << "{session=" << sp->getSessionId() << " channel=" << sp->channel() << " local=" << sp->getLocalTuple() << " remote=" << sp->getRemoteTuple() << "}, ";
      }
   }
   os << "}";
   // resip::NameAddrs redirectedAddrs;
   os << "\nWas Redirected:                              " << info.wasRedirected;
   os << "\nWas Forked:                                  " << info.wasForked;
   os << "\nReplaces Dialog Id:                          " << info.replacesDialogId.getDialogSetId().getCallId().c_str();
   os << "\nJoin Dialog Id:                              " << info.joinDialogId.getDialogSetId().getCallId().c_str();
   // cpc::vector<Parameter> joinParams;
   os << "\nSIP DTMF Failure:                            " << info.sipDTMFFailure;
   os << "\nFrom Address:                                " << info.fromAddress.c_str();
   // cpc::vector<CPCAPI2::SipHeader> customHeaders;
   os << "\nEnd Original Call After Transfer Success:    " << info.shouldEndOriginalCallAfterAttendedTransferSuccess;
   // std::shared_ptr<resip::NameAddr> potentialTargetAddress;
   os << "\nConversation State:                          " << info.currentConversationState;
   os << "\nLocal Encryption Configurecd:                " << info.localEncryptionOptionsConfigured;
   os << "\nCall Id:                                     " << info.callId.c_str();
   os << "\nNon Zero Audio Input Levels Sampled:         " << info.numNonZeroAudioInputLevelsSampled;
   os << "\nCall Kit Mode:                               " << info.callKitMode;
   // void* incomingVideoRenderSurface;
   // webrtc::VideoRenderSurfaceType incomingVideoRenderSurfaceType;
   os << "\nCall Initiated From Push:                    " << info.callInitiatedFromPush;
   os << "\nMedia Bypass Mode:                           " << info.mediaBypassMode;
   // SipConversation::NackSettings videoNackSettings;
   os << "\nSent Remote Ringing:                         " << info.sentRemoteRinging;
   // ConversationStateChangedEvent lastRemoteRingingEvent;
   // CPCAPI2::SipConversation::AnswerModeSettings answerMode;
   os << "\nPrevious Packets Received:                   " << info.previousPacketsReceived;
   os << "\nPrevious Audio RTCP Sender Report Timestamp: " << info.previousAudioRTCPSenderReportTimestamp;
   os << "\nIntervals Without Update:                    " << info.numIntervalsWithoutUpdate;
   // std::set<Recording::RecorderHandle> recordings;
   os << "\nAudio Device Close Delay:                    " << info.audioDeviceCloseDelay;
   os << "\n";
   return os;
}

}

}

#endif
